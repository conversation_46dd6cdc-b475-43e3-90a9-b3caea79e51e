# DeepMu 生长速率模型训练报告

## 训练信息

- **开始时间**: 2025年 06月 29日 星期日 20:23:28 CST
- **模型类型**: 生长速率预测
- **交叉验证**: 5折
- **训练轮数**: 300
- **批次大小**: 128
- **学习率**: 0.001
- **激活函数**: gelu

## 模型架构

- **隐藏层**: [512, 384, 256, 128]
- **Dropout率**: [0.3, 0.4, 0.4, 0.5]
- **批量归一化**: 是
- **梯度裁剪**: 1.0

## 数据处理

- **特征标准化**: RobustScaler
- **目标标准化**: true
- **异常值检测**: IsolationForest

## 使用方法

```python
import torch
from train_growth_rate_model import GrowthRateModel

# 加载模型
model = GrowthRateModel(...)
model.load_state_dict(torch.load('growth_rate_model.pt'))
model.eval()

# 加载标准化器
scalers = torch.load('scalers.pt')
feature_scaler = scalers['feature_scaler']
target_scaler = scalers['target_scaler']

# 进行预测
features_scaled = feature_scaler.transform(features)
with torch.no_grad():
    predictions = model(torch.FloatTensor(features_scaled))
    predictions_orig = target_scaler.inverse_transform(predictions.numpy())
```

