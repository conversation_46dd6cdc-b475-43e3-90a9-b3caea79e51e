# DeepMu 温度预测模型训练报告

## 训练信息

- **开始时间**: 2025年 06月 29日 星期日 20:12:43 CST
- **模型类型**: 最适温度预测
- **交叉验证**: 5折
- **训练轮数**: 300
- **批次大小**: 128
- **学习率**: 0.001
- **激活函数**: gelu
- **温度范围**: [0.0, 100.0]°C

## 模型架构

- **隐藏层**: [512, 256, 128, 64]
- **Dropout率**: [0.3, 0.4, 0.4, 0.5]
- **批量归一化**: 是
- **温度约束**: 是
- **损失函数**: MAE

## 数据处理

- **特征标准化**: RobustScaler
- **目标标准化**: MinMaxScaler
- **异常值检测**: IsolationForest
- **温度范围过滤**: 是

## 评估指标

- **R²**: 决定系数
- **MAE**: 平均绝对误差 (°C)
- **RMSE**: 均方根误差 (°C)
- **1°C内准确率**: 预测误差在1°C内的样本比例
- **2°C内准确率**: 预测误差在2°C内的样本比例
- **5°C内准确率**: 预测误差在5°C内的样本比例

## 使用方法

```python
import torch
from train_temperature_model import TemperatureModel

# 加载模型
model = TemperatureModel(...)
model.load_state_dict(torch.load('temperature_model.pt'))
model.eval()

# 加载标准化器
scalers = torch.load('scalers.pt')
feature_scaler = scalers['feature_scaler']
target_scaler = scalers['target_scaler']

# 进行预测
features_scaled = feature_scaler.transform(features)
with torch.no_grad():
    predictions = model(torch.FloatTensor(features_scaled))
    predictions_orig = target_scaler.inverse_transform(predictions.numpy())
```

## 温度约束

模型使用sigmoid函数将输出约束到[0,1]范围，然后线性变换到指定的温度范围，
确保预测的温度值在物理合理的范围内。

