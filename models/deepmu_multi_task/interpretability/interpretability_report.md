# DeepMu 可解释性分析报告

**生成时间**: 2025-06-29 21:06:23
**模型类型**: multi_task
**特征数量**: 1516
**样本数量**: 6052

## 分析方法

本报告采用以下方法进行可解释性分析：

1. **随机森林特征重要性**: 使用随机森林模型计算特征重要性
2. **特征方差分析**: 分析特征的方差作为重要性指标
3. **特征相关性分析**: 识别高相关性特征对
4. **特征分布分析**: 分析代表性特征的分布特征

## 主要发现

- **高相关性特征对**: 发现 10858 对高相关性特征 (|r| > 0.8)
- **最高相关性**: ko00902 与 pathway_completeness_ko00902 (r = 1.0000)
- **特征维度**: 1516 维特征空间
- **数据完整性**: 99.20% 数据完整

## 建议

基于可解释性分析结果，建议：

1. **特征选择**: 考虑移除高相关性特征以减少冗余
2. **特征工程**: 关注高重要性特征进行进一步工程化
3. **模型简化**: 基于重要性分析简化模型结构
4. **领域知识**: 结合生物学领域知识解释重要特征

## 生成的分析图表

### temperature_rf_feature_importance

![temperature_rf_feature_importance](temperature_rf_feature_importance.png)

### feature_correlation_heatmap

![feature_correlation_heatmap](feature_correlation_heatmap.png)

### growth_rate_rf_feature_importance

![growth_rate_rf_feature_importance](growth_rate_rf_feature_importance.png)

### feature_distributions

![feature_distributions](feature_distributions.png)

