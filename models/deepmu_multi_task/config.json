{"data": {"feature_file": "training_data/combined_features.tsv", "metadata_file": "training_data/metadata.tsv", "test_size": 0.2, "validation_size": 0.2, "random_state": 42}, "model": {"shared_dims": [512, 256], "growth_dims": [128, 64], "temp_dims": [128, 64], "dropout_rate": 0.3, "use_batch_norm": true}, "training": {"batch_size": 128, "learning_rate": 0.001, "epochs": 300, "patience": 30, "weight_decay": 0.0001, "gradient_clip": 1.0}, "task_weights": {"growth": 1.0, "temperature": 1.0}}