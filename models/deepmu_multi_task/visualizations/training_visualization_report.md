# DeepMu 训练可视化报告

**生成时间**: 2025-06-29 20:36:47
**模型类型**: multi_task
**模型目录**: models/deepmu_multi_task

## 训练配置

```json
{
  "data": {
    "feature_file": "training_data/combined_features.tsv",
    "metadata_file": "training_data/metadata.tsv",
    "test_size": 0.2,
    "validation_size": 0.2,
    "random_state": 42
  },
  "model": {
    "shared_dims": [
      512,
      256
    ],
    "growth_dims": [
      128,
      64
    ],
    "temp_dims": [
      128,
      64
    ],
    "dropout_rate": 0.3,
    "use_batch_norm": true
  },
  "training": {
    "batch_size": 128,
    "learning_rate": 0.001,
    "epochs": 300,
    "patience": 30,
    "weight_decay": 0.0001,
    "gradient_clip": 1.0
  },
  "task_weights": {
    "growth": 1.0,
    "temperature": 1.0
  }
}
```

## 性能总结

### 多任务测试结果

- **生长速率 R²**: -2297.1427
- **生长速率 RMSE**: 165.350386
- **温度预测 R²**: 0.8273
- **温度预测 MAE**: 2.97°C

## 生成的可视化图表

### multi_task_performance

![multi_task_performance](multi_task_performance.png)

### multi_task_training_curves

![multi_task_training_curves](multi_task_training_curves.png)

