#!/usr/bin/env python3
"""
Training script for a simple Random Forest model for both growth rate and optimal temperature.

This script trains a simple Random Forest model for both growth rate and optimal temperature
prediction, without any complex architecture or feature engineering.
"""

import os
import argparse
import logging
import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union, Any
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, r2_score

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('train_simple_rf')

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Train a simple Random Forest model for both growth rate and optimal temperature')

    # Data arguments
    parser.add_argument('--metadata', required=True, help='Path to metadata TSV file')
    parser.add_argument('--feature-file', required=True, help='Path to combined features TSV file')
    parser.add_argument('--output-dir', required=True, help='Directory to save model and results')
    parser.add_argument('--metrics-dir', default=None, help='Directory to save metrics')

    # Random Forest arguments
    parser.add_argument('--n-estimators', type=int, default=200, help='Number of trees in the Random Forest')
    parser.add_argument('--max-depth', type=int, default=15, help='Maximum depth of trees in the Random Forest')
    parser.add_argument('--min-samples-split', type=int, default=5, help='Minimum samples required to split a node in Random Forest')
    parser.add_argument('--min-samples-leaf', type=int, default=2, help='Minimum samples required in a leaf node in Random Forest')

    # Training arguments
    parser.add_argument('--seed', type=int, default=42, help='Random seed')

    return parser.parse_args()

def save_prediction_plots(
    val_metrics: Dict,
    output_dir: str
):
    """
    Save plots of predictions vs actual values.

    Args:
        val_metrics: Dictionary of validation metrics
        output_dir: Directory to save the plots
    """
    # Create directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Growth rate predictions
    if 'growth_rate_preds' in val_metrics and 'growth_rate_targets' in val_metrics:
        plt.figure(figsize=(10, 8))
        
        # Create DataFrame for easier plotting
        df = pd.DataFrame({
            'Actual': val_metrics['growth_rate_targets'],
            'Predicted': val_metrics['growth_rate_preds']
        })
        
        # Create scatter plot with regression line
        sns.regplot(x='Actual', y='Predicted', data=df, scatter_kws={'alpha': 0.5})
        
        # Add perfect prediction line
        min_val = min(df['Actual'].min(), df['Predicted'].min())
        max_val = max(df['Actual'].max(), df['Predicted'].max())
        plt.plot([min_val, max_val], [min_val, max_val], 'r--')
        
        # Add R² value
        if 'growth_rate_r2' in val_metrics:
            plt.title(f'Growth Rate Prediction (R² = {val_metrics["growth_rate_r2"]:.4f})')
        else:
            plt.title('Growth Rate Prediction')
        
        plt.xlabel('Actual Growth Rate')
        plt.ylabel('Predicted Growth Rate')
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'growth_rate_prediction.png'))
    
    # Optimal temperature predictions
    if 'optimal_temperature_preds' in val_metrics and 'optimal_temperature_targets' in val_metrics:
        plt.figure(figsize=(10, 8))
        
        # Create DataFrame for easier plotting
        df = pd.DataFrame({
            'Actual': val_metrics['optimal_temperature_targets'],
            'Predicted': val_metrics['optimal_temperature_preds']
        })
        
        # Create scatter plot with regression line
        sns.regplot(x='Actual', y='Predicted', data=df, scatter_kws={'alpha': 0.5})
        
        # Add perfect prediction line
        min_val = min(df['Actual'].min(), df['Predicted'].min())
        max_val = max(df['Actual'].max(), df['Predicted'].max())
        plt.plot([min_val, max_val], [min_val, max_val], 'r--')
        
        # Add R² value
        if 'optimal_temperature_r2' in val_metrics:
            plt.title(f'Optimal Temperature Prediction (R² = {val_metrics["optimal_temperature_r2"]:.4f})')
        else:
            plt.title('Optimal Temperature Prediction')
        
        plt.xlabel('Actual Optimal Temperature (°C)')
        plt.ylabel('Predicted Optimal Temperature (°C)')
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'optimal_temperature_prediction.png'))

def main():
    """Main function."""
    args = parse_args()
    
    # Set random seed
    np.random.seed(args.seed)
    
    # Create output directories
    os.makedirs(args.output_dir, exist_ok=True)
    
    if args.metrics_dir is None:
        args.metrics_dir = os.path.join(args.output_dir, 'metrics')
    os.makedirs(args.metrics_dir, exist_ok=True)
    
    # Load metadata
    logger.info(f"Loading metadata from {args.metadata}...")
    metadata = pd.read_csv(args.metadata, sep='\t')
    
    # Load features
    logger.info(f"Loading features from {args.feature_file}...")
    features = pd.read_csv(args.feature_file, sep='\t')
    
    # Merge metadata and features
    logger.info("Merging metadata and features...")
    data = pd.merge(metadata, features, on='genome_id')
    
    # Extract targets
    y_growth_rate = data['growth_rate'].values
    y_optimal_temperature = data['optimal_temperature'].values
    
    # Extract features
    X = data.drop(['genome_id', 'growth_rate', 'optimal_temperature', 'taxid', 'genetic_code', 'kingdom'], axis=1)
    feature_names = X.columns.tolist()
    X = X.values
    
    # Split data into train and validation sets
    X_train, X_val, y_growth_train, y_growth_val, y_temp_train, y_temp_val = train_test_split(
        X, y_growth_rate, y_optimal_temperature,
        test_size=0.2,
        random_state=args.seed
    )
    
    logger.info(f"Train set size: {len(X_train)}, Validation set size: {len(X_val)}")
    
    # Scale features
    logger.info("Scaling features...")
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_val_scaled = scaler.transform(X_val)
    
    # Train Random Forest for growth rate
    logger.info("Training Random Forest for growth rate...")
    growth_rf = RandomForestRegressor(
        n_estimators=args.n_estimators,
        max_depth=args.max_depth,
        min_samples_split=args.min_samples_split,
        min_samples_leaf=args.min_samples_leaf,
        random_state=args.seed,
        n_jobs=-1
    )
    growth_rf.fit(X_train_scaled, y_growth_train)
    
    # Train Random Forest for optimal temperature
    logger.info("Training Random Forest for optimal temperature...")
    temp_rf = RandomForestRegressor(
        n_estimators=args.n_estimators,
        max_depth=args.max_depth,
        min_samples_split=args.min_samples_split,
        min_samples_leaf=args.min_samples_leaf,
        random_state=args.seed,
        n_jobs=-1
    )
    temp_rf.fit(X_train_scaled, y_temp_train)
    
    # Evaluate models
    logger.info("Evaluating models...")
    
    # Growth rate predictions
    growth_train_preds = growth_rf.predict(X_train_scaled)
    growth_val_preds = growth_rf.predict(X_val_scaled)
    
    # Optimal temperature predictions
    temp_train_preds = temp_rf.predict(X_train_scaled)
    temp_val_preds = temp_rf.predict(X_val_scaled)
    
    # Calculate metrics
    growth_train_mse = mean_squared_error(y_growth_train, growth_train_preds)
    growth_val_mse = mean_squared_error(y_growth_val, growth_val_preds)
    growth_train_r2 = r2_score(y_growth_train, growth_train_preds)
    growth_val_r2 = r2_score(y_growth_val, growth_val_preds)
    
    temp_train_mse = mean_squared_error(y_temp_train, temp_train_preds)
    temp_val_mse = mean_squared_error(y_temp_val, temp_val_preds)
    temp_train_r2 = r2_score(y_temp_train, temp_train_preds)
    temp_val_r2 = r2_score(y_temp_val, temp_val_preds)
    
    # Log metrics
    logger.info(f"Growth rate train MSE: {growth_train_mse:.4f}, R²: {growth_train_r2:.4f}")
    logger.info(f"Growth rate validation MSE: {growth_val_mse:.4f}, R²: {growth_val_r2:.4f}")
    logger.info(f"Optimal temperature train MSE: {temp_train_mse:.4f}, R²: {temp_train_r2:.4f}")
    logger.info(f"Optimal temperature validation MSE: {temp_val_mse:.4f}, R²: {temp_val_r2:.4f}")
    
    # Save models
    import joblib
    
    logger.info("Saving models...")
    joblib.dump(growth_rf, os.path.join(args.output_dir, 'growth_rate_rf.joblib'))
    joblib.dump(temp_rf, os.path.join(args.output_dir, 'optimal_temperature_rf.joblib'))
    joblib.dump(scaler, os.path.join(args.output_dir, 'feature_scaler.joblib'))
    
    # Save feature importance
    growth_importance = growth_rf.feature_importances_
    temp_importance = temp_rf.feature_importances_
    
    # Sort feature importance
    growth_indices = np.argsort(growth_importance)[::-1]
    temp_indices = np.argsort(temp_importance)[::-1]
    
    # Save feature importance report
    with open(os.path.join(args.metrics_dir, 'feature_importance_report.md'), 'w') as f:
        f.write('# Feature Importance Report\n\n')
        
        f.write('## Growth Rate Features\n\n')
        for i, idx in enumerate(growth_indices[:50]):
            f.write(f'{i+1}. {feature_names[idx]} ({growth_importance[idx]:.6f})\n')
        
        f.write('\n## Optimal Temperature Features\n\n')
        for i, idx in enumerate(temp_indices[:50]):
            f.write(f'{i+1}. {feature_names[idx]} ({temp_importance[idx]:.6f})\n')
        
        # Find common important features
        growth_top_indices = set(growth_indices[:50])
        temp_top_indices = set(temp_indices[:50])
        common_indices = growth_top_indices.intersection(temp_top_indices)
        
        f.write('\n## Common Important Features\n\n')
        for idx in common_indices:
            f.write(f'- {feature_names[idx]} (Growth: {growth_importance[idx]:.6f}, Temperature: {temp_importance[idx]:.6f})\n')
        
        f.write(f'\nTotal common important features: {len(common_indices)}\n')
        f.write(f'Percentage of growth rate features: {len(common_indices) / 50 * 100:.2f}%\n')
        f.write(f'Percentage of optimal temperature features: {len(common_indices) / 50 * 100:.2f}%\n')
    
    # Save metrics
    metrics = {
        'train_metrics': [{
            'growth_rate_mse': float(growth_train_mse),
            'growth_rate_r2': float(growth_train_r2),
            'optimal_temperature_mse': float(temp_train_mse),
            'optimal_temperature_r2': float(temp_train_r2)
        }],
        'val_metrics': [{
            'growth_rate_mse': float(growth_val_mse),
            'growth_rate_r2': float(growth_val_r2),
            'growth_rate_preds': growth_val_preds.tolist(),
            'growth_rate_targets': y_growth_val.tolist(),
            'optimal_temperature_mse': float(temp_val_mse),
            'optimal_temperature_r2': float(temp_val_r2),
            'optimal_temperature_preds': temp_val_preds.tolist(),
            'optimal_temperature_targets': y_temp_val.tolist()
        }]
    }
    
    with open(os.path.join(args.metrics_dir, 'training_metrics.json'), 'w') as f:
        json.dump(metrics, f, indent=2)
    
    # Save prediction plots
    save_prediction_plots(metrics['val_metrics'][0], args.metrics_dir)
    
    logger.info(f"Training complete. Models saved to {args.output_dir}")
    logger.info(f"Metrics saved to {os.path.join(args.metrics_dir, 'training_metrics.json')}")
    logger.info(f"Feature importance report saved to {os.path.join(args.metrics_dir, 'feature_importance_report.md')}")

if __name__ == '__main__':
    main()
