#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Improved LightGBM Component for Hybrid Multi-Task Model.
This module provides an enhanced LightGBM component with optimized parameters
to address the "No further splits with positive gain" warnings.
"""

import os
import numpy as np
import pandas as pd
import logging
from typing import Dict, Tuple, Optional
import joblib
import lightgbm as lgb
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ImprovedLightGBMComponent:
    """
    Improved LightGBM component with optimized parameters to address warnings.
    """
    def __init__(
        self,
        n_estimators: int = 200,
        learning_rate: float = 0.05,
        max_depth: int = 10,
        min_data_in_leaf: int = 20,  # Increased from default
        min_gain_to_split: float = 0.1,  # Added to prevent splits with minimal gain
        reg_alpha: float = 0.1,  # L1 regularization
        reg_lambda: float = 1.0,  # L2 regularization
        subsample: float = 0.8,
        colsample_bytree: float = 0.8,
        random_state: int = 42,
        verbose: int = -1  # Set to -1 to suppress warnings in console output
    ):
        """
        Initialize improved LightGBM component.

        Args:
            n_estimators: Number of boosting iterations
            learning_rate: Boosting learning rate
            max_depth: Maximum tree depth
            min_data_in_leaf: Minimum number of data in a leaf
            min_gain_to_split: Minimum gain to perform split
            reg_alpha: L1 regularization
            reg_lambda: L2 regularization
            subsample: Subsample ratio of the training data
            colsample_bytree: Subsample ratio of columns when constructing each tree
            random_state: Random seed
            verbose: Verbosity of output
        """
        self.params = {
            'n_estimators': n_estimators,
            'learning_rate': learning_rate,
            'max_depth': max_depth,
            'min_data_in_leaf': min_data_in_leaf,
            'min_gain_to_split': min_gain_to_split,
            'reg_alpha': reg_alpha,
            'reg_lambda': reg_lambda,
            'subsample': subsample,
            'colsample_bytree': colsample_bytree,
            'random_state': random_state,
            'verbose': verbose,
            'n_jobs': -1
        }

        # Initialize models for growth rate and temperature
        self.growth_model = None
        self.temp_model = None

    def fit(
        self,
        X: pd.DataFrame,
        y_growth: pd.Series,
        y_temp: pd.Series,
        X_val: Optional[pd.DataFrame] = None,
        y_val_growth: Optional[pd.Series] = None,
        y_val_temp: Optional[pd.Series] = None
    ) -> None:
        """
        Fit LightGBM models to data.

        Args:
            X: Training features
            y_growth: Training growth rate targets
            y_temp: Training temperature targets
            X_val: Validation features (optional)
            y_val_growth: Validation growth rate targets (optional)
            y_val_temp: Validation temperature targets (optional)
        """
        logger.info("Training improved LightGBM models...")

        # Prepare validation data if provided
        if X_val is not None and y_val_growth is not None and y_val_temp is not None:
            # Use DataFrames directly to preserve feature names
            eval_set_growth = [(X, y_growth), (X_val, y_val_growth)]
            eval_set_temp = [(X, y_temp), (X_val, y_val_temp)]
        else:
            eval_set_growth = None
            eval_set_temp = None

        # Train growth rate model
        logger.info("Training LightGBM model for growth rate...")
        self.growth_model = lgb.LGBMRegressor(**self.params)

        if eval_set_growth is not None:
            # For scikit-learn API, we need to use eval_metric and early_stopping_rounds differently
            # Use DataFrames directly to preserve feature names
            self.growth_model.fit(
                X, y_growth,
                eval_set=eval_set_growth,
                eval_metric='rmse',
                callbacks=[
                    lgb.early_stopping(stopping_rounds=20),
                    lgb.log_evaluation(period=20)
                ]
            )
        else:
            self.growth_model.fit(X, y_growth)

        # Train temperature model
        logger.info("Training LightGBM model for temperature...")
        self.temp_model = lgb.LGBMRegressor(**self.params)

        if eval_set_temp is not None:
            # Use DataFrames directly to preserve feature names
            self.temp_model.fit(
                X, y_temp,
                eval_set=eval_set_temp,
                eval_metric='rmse',
                callbacks=[
                    lgb.early_stopping(stopping_rounds=20),
                    lgb.log_evaluation(period=20)
                ]
            )
        else:
            self.temp_model.fit(X, y_temp)

        # Get feature importances
        growth_importances = self.growth_model.feature_importances_
        temp_importances = self.temp_model.feature_importances_

        # Log top 10 features for each model
        feature_names = X.columns.tolist()
        growth_importance_dict = dict(zip(feature_names, growth_importances))
        temp_importance_dict = dict(zip(feature_names, temp_importances))

        growth_top_features = sorted(growth_importance_dict.items(), key=lambda x: x[1], reverse=True)[:10]
        temp_top_features = sorted(temp_importance_dict.items(), key=lambda x: x[1], reverse=True)[:10]

        logger.info("Top 10 features for growth rate prediction:")
        for feature, importance in growth_top_features:
            logger.info(f"  {feature}: {importance}")

        logger.info("Top 10 features for temperature prediction:")
        for feature, importance in temp_top_features:
            logger.info(f"  {feature}: {importance}")

    def predict(self, X: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """
        Generate predictions for both growth rate and temperature.

        Args:
            X: Features

        Returns:
            Tuple of (growth_rate_predictions, temperature_predictions)
        """
        if self.growth_model is None or self.temp_model is None:
            raise ValueError("Models have not been trained yet")

        # Use DataFrame directly to preserve feature names
        # Generate predictions
        growth_pred = self.growth_model.predict(X)
        temp_pred = self.temp_model.predict(X)

        return growth_pred, temp_pred

    def predict_growth(self, X: pd.DataFrame) -> np.ndarray:
        """
        Generate growth rate predictions only.

        Args:
            X: Features

        Returns:
            Growth rate predictions
        """
        growth_pred, _ = self.predict(X)
        return growth_pred

    def predict_temperature(self, X: pd.DataFrame) -> np.ndarray:
        """
        Generate temperature predictions only.

        Args:
            X: Features

        Returns:
            Temperature predictions
        """
        _, temp_pred = self.predict(X)
        return temp_pred

    def evaluate(
        self,
        X: pd.DataFrame,
        y_growth: pd.Series,
        y_temp: pd.Series
    ) -> Dict[str, Dict[str, float]]:
        """
        Evaluate models on data.

        Args:
            X: Features
            y_growth: Growth rate targets
            y_temp: Temperature targets

        Returns:
            Dictionary of evaluation metrics for each task
        """
        # Generate predictions (using DataFrame to preserve feature names)
        y_growth_pred, y_temp_pred = self.predict(X)

        # Calculate metrics for growth rate
        growth_metrics = {
            'R2': r2_score(y_growth, y_growth_pred),
            'RMSE': np.sqrt(mean_squared_error(y_growth, y_growth_pred)),
            'MAE': mean_absolute_error(y_growth, y_growth_pred)
        }

        # Calculate metrics for temperature
        temp_metrics = {
            'R2': r2_score(y_temp, y_temp_pred),
            'RMSE': np.sqrt(mean_squared_error(y_temp, y_temp_pred)),
            'MAE': mean_absolute_error(y_temp, y_temp_pred)
        }

        # Calculate overall metrics (average R2)
        overall_metrics = {
            'R2': (growth_metrics['R2'] + temp_metrics['R2']) / 2,
            'RMSE': (growth_metrics['RMSE'] + temp_metrics['RMSE']) / 2,
            'MAE': (growth_metrics['MAE'] + temp_metrics['MAE']) / 2
        }

        # Log metrics
        logger.info(f"LightGBM evaluation metrics - Growth Rate: R²={growth_metrics['R2']:.4f}, RMSE={growth_metrics['RMSE']:.4f}, MAE={growth_metrics['MAE']:.4f}")
        logger.info(f"LightGBM evaluation metrics - Temperature: R²={temp_metrics['R2']:.4f}, RMSE={temp_metrics['RMSE']:.4f}, MAE={temp_metrics['MAE']:.4f}")
        logger.info(f"LightGBM evaluation metrics - Overall: R²={overall_metrics['R2']:.4f}, RMSE={overall_metrics['RMSE']:.4f}, MAE={overall_metrics['MAE']:.4f}")

        return {
            'growth_rate': growth_metrics,
            'temperature': temp_metrics,
            'overall': overall_metrics
        }

    def save(self, output_dir: str) -> None:
        """
        Save models to disk.

        Args:
            output_dir: Directory to save models
        """
        if self.growth_model is None or self.temp_model is None:
            raise ValueError("Models have not been trained yet")

        os.makedirs(output_dir, exist_ok=True)

        # Save models
        joblib.dump(self.growth_model, os.path.join(output_dir, 'lgb_growth_model.joblib'))
        joblib.dump(self.temp_model, os.path.join(output_dir, 'lgb_temp_model.joblib'))

        # Save parameters
        joblib.dump(self.params, os.path.join(output_dir, 'lgb_params.joblib'))

        logger.info(f"LightGBM models saved to {output_dir}")

    def load(self, input_dir: str) -> None:
        """
        Load models from disk.

        Args:
            input_dir: Directory to load models from
        """
        # Load models
        self.growth_model = joblib.load(os.path.join(input_dir, 'lgb_growth_model.joblib'))
        self.temp_model = joblib.load(os.path.join(input_dir, 'lgb_temp_model.joblib'))

        # Load parameters
        self.params = joblib.load(os.path.join(input_dir, 'lgb_params.joblib'))

        logger.info(f"LightGBM models loaded from {input_dir}")
