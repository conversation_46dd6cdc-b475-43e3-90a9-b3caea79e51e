#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Run Hybrid Model with Highly Optimized Temperature DNN v2.
This script trains a hybrid model that combines the successful LightGBM+ensemble approach for growth rate prediction
with the heavily optimized RegularizedDNN approach for temperature prediction, matching the original standalone implementation.
"""

import os
import argparse
import logging
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
import joblib
from typing import Dict, List, Tuple, Optional

# Import only necessary functions
from train_hybrid_with_enhanced_nn import load_data, evaluate_model

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import the required components for temperature prediction
from hybrid_model_with_enhanced_nn import FeatureDataset, RegularizedDNN, HybridModelWithEnhancedNN

def prepare_data_optimized(
    features: pd.DataFrame, 
    metadata: pd.DataFrame,
    n_features: int = 150, 
    output_dir: Optional[str] = None
) -> Tuple:
    """
    Prepare data with optimized processing for temperature prediction.
    
    Args:
        features: Feature DataFrame
        metadata: Metadata DataFrame
        n_features: Number of features to use
        output_dir: Directory to save outputs
        
    Returns:
        Tuple containing processed datasets and transformation info
    """
    logger.info("Preparing data with optimized processing for temperature prediction...")

    # Extract targets
    y_growth_original = metadata['growth_rate']
    y_temp_original = metadata['optimal_temperature']
    
    # Apply square root transformation only to growth rate
    logger.info("Applying square root transformation to growth rate only")
    y_growth = np.sqrt(y_growth_original)
    y_temp = y_temp_original  # Keep temperature in original scale
    
    # Create bins for stratification based on temperature
    n_bins = 10
    y_binned = pd.qcut(y_temp, n_bins, labels=False, duplicates='drop')
    
    # First split data into train+val and test sets with stratification on temperature
    X_train_val, X_test, y_growth_train_val, y_growth_test, y_temp_train_val, y_temp_test, y_binned_train_val, _ = train_test_split(
        features, y_growth, y_temp, y_binned, test_size=0.2, random_state=42, shuffle=True, stratify=y_binned
    )

    # Then split train+val into train and validation sets with stratification
    X_train, X_val, y_growth_train, y_growth_val, y_temp_train, y_temp_val = train_test_split(
        X_train_val, y_growth_train_val, y_temp_train_val, test_size=0.25, random_state=42, 
        shuffle=True, stratify=y_binned_train_val
    )

    logger.info(f"Data split: Train={X_train.shape[0]} samples, Validation={X_val.shape[0]} samples, Test={X_test.shape[0]} samples")
    
    # Apply target scaling using RobustScaler for both targets
    growth_scaler = RobustScaler()
    temp_scaler = RobustScaler()
    
    # Reshape for scaler
    y_growth_train_reshaped = y_growth_train.values.reshape(-1, 1)
    y_growth_val_reshaped = y_growth_val.values.reshape(-1, 1)
    y_growth_test_reshaped = y_growth_test.values.reshape(-1, 1)
    
    y_temp_train_reshaped = y_temp_train.values.reshape(-1, 1)
    y_temp_val_reshaped = y_temp_val.values.reshape(-1, 1)
    y_temp_test_reshaped = y_temp_test.values.reshape(-1, 1)
    
    # Fit scalers on training data only
    growth_scaler.fit(y_growth_train_reshaped)
    temp_scaler.fit(y_temp_train_reshaped)
    
    # Transform all sets
    y_growth_train_scaled = growth_scaler.transform(y_growth_train_reshaped).flatten()
    y_growth_val_scaled = growth_scaler.transform(y_growth_val_reshaped).flatten()
    y_growth_test_scaled = growth_scaler.transform(y_growth_test_reshaped).flatten()
    
    y_temp_train_scaled = temp_scaler.transform(y_temp_train_reshaped).flatten()
    y_temp_val_scaled = temp_scaler.transform(y_temp_val_reshaped).flatten()
    y_temp_test_scaled = temp_scaler.transform(y_temp_test_reshaped).flatten()
    
    # Convert back to pandas Series with original indices
    y_growth_train = pd.Series(y_growth_train_scaled, index=y_growth_train.index)
    y_growth_val = pd.Series(y_growth_val_scaled, index=y_growth_val.index)
    y_growth_test = pd.Series(y_growth_test_scaled, index=y_growth_test.index)
    
    y_temp_train = pd.Series(y_temp_train_scaled, index=y_temp_train.index)
    y_temp_val = pd.Series(y_temp_val_scaled, index=y_temp_val.index)
    y_temp_test = pd.Series(y_temp_test_scaled, index=y_temp_test.index)
    
    logger.info("Applied target scaling using RobustScaler for both targets")
    
    # Feature selection - we'll use all features but prioritize temperature-related ones
    # From the successful LightGBM model, we know GC content, GC1, GC2, GC3 are important for temperature
    temp_priority_features = [col for col in features.columns if 'GC' in col]
    temp_priority_features.extend([col for col in features.columns if 'temp' in col.lower()])
    temp_priority_features.extend([col for col in features.columns if 'amino' in col.lower()])
    
    # Deduplicate
    temp_priority_features = list(set(temp_priority_features))
    
    # Add remaining features up to n_features
    remaining_features = [col for col in features.columns if col not in temp_priority_features]
    selected_features = temp_priority_features + remaining_features
    selected_features = selected_features[:n_features]
    
    logger.info(f"Selected {len(selected_features)} features with priority for temperature-related features")
    
    # Filter to selected features
    X_train = X_train[selected_features]
    X_val = X_val[selected_features]
    X_test = X_test[selected_features]

    # Store transformation information
    transform_info = {
        'growth_scaler': growth_scaler,
        'temp_scaler': temp_scaler,
        'growth_sqrt_transform': True,
        'temp_sqrt_transform': False
    }
    
    # Save transformation info if output_dir is provided
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
        joblib.dump(transform_info, os.path.join(output_dir, 'transform_info.joblib'))
        logger.info(f"Saved transformation info to {output_dir}")

    return (
        X_train, y_growth_train, y_temp_train, 
        X_val, y_growth_val, y_temp_val, 
        X_test, y_growth_test, y_temp_test, 
        selected_features, transform_info
    )

class EnhancedHybridModel(HybridModelWithEnhancedNN):
    """Enhanced hybrid model with improvements specifically for temperature prediction."""
    
    def _train_temperature_model(
        self,
        X_train_scaled: np.ndarray,
        y_temp_train: np.ndarray,
        X_val_scaled: np.ndarray,
        y_temp_val: np.ndarray,
        output_dir: Optional[str] = None
    ) -> None:
        """
        Train the RegularizedDNN model for temperature prediction with advanced optimizations.
        
        Args:
            X_train_scaled: Scaled training features
            y_temp_train: Training temperature targets
            X_val_scaled: Scaled validation features
            y_temp_val: Validation temperature targets
            output_dir: Directory to save outputs
        """
        # Create datasets and dataloaders
        train_dataset = FeatureDataset(X_train_scaled, y_temp_train)
        val_dataset = FeatureDataset(X_val_scaled, y_temp_val)
        
        # Use a larger batch size for more stable gradients
        train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size=64, shuffle=False)
        
        # Initialize model with the optimal configuration
        input_dim = X_train_scaled.shape[1]
        
        # Create a custom model with deeper architecture
        self.temp_model = RegularizedDNN(
            input_dim=input_dim,
            hidden_dims=[512, 512, 512, 384, 256, 128],  # Deeper network
            dropout_rates=[0.2, 0.3, 0.3, 0.4, 0.5, 0.5],  # Less dropout early in the network
            use_batch_norm=True,
            activation='leaky_relu',  # Use leaky ReLU for better gradient flow
            use_residual=True  # Enable residual connections
        ).to(self.device)
        
        # Initialize optimizer with AdamW for better weight decay handling
        optimizer = optim.AdamW(
            self.temp_model.parameters(), 
            lr=0.0005,  # Lower initial learning rate
            weight_decay=1e-4,  # Stronger weight decay
            betas=(0.9, 0.999),  # Default betas
            eps=1e-8  # Default epsilon
        )
        
        # Initialize learning rate scheduler with OneCycleLR
        scheduler = optim.lr_scheduler.OneCycleLR(
            optimizer,
            max_lr=0.005,  # 10x higher peak LR
            epochs=self.temp_nn_epochs,
            steps_per_epoch=len(train_loader),
            pct_start=0.3,  # 30% warmup
            anneal_strategy='cos',
            div_factor=25.0,
            final_div_factor=10000.0
        )
        
        # Initialize loss function - mix of MSE and L1 for more robust learning
        mse_criterion = nn.MSELoss()
        l1_criterion = nn.L1Loss()
        
        # Add L1 regularization parameter
        l1_lambda = 1e-5
        
        # Early stopping variables with longer patience
        best_val_loss = float('inf')
        patience_counter = 0
        best_model_state = None
        best_epoch = 0
        
        # Training loop
        logger.info(f"Beginning temperature model training for {self.temp_nn_epochs} epochs...")
        
        for epoch in range(self.temp_nn_epochs):
            # Training phase
            self.temp_model.train()
            train_loss = 0.0
            
            for features, targets in train_loader:
                features, targets = features.to(self.device), targets.to(self.device)
                
                # Zero gradients
                optimizer.zero_grad()
                
                # Forward pass
                outputs = self.temp_model(features)
                
                # Combined loss: 80% MSE and 20% L1
                mse_loss = mse_criterion(outputs, targets)
                l1_loss = l1_criterion(outputs, targets)
                loss = 0.8 * mse_loss + 0.2 * l1_loss
                
                # Add L1 regularization
                if l1_lambda > 0:
                    l1_norm = sum(p.abs().sum() for p in self.temp_model.parameters())
                    loss += l1_lambda * l1_norm
                
                # Backward pass
                loss.backward()
                
                # Gradient clipping with more aggressive norm
                torch.nn.utils.clip_grad_norm_(self.temp_model.parameters(), 0.5)
                
                # Update weights
                optimizer.step()
                
                # Update learning rate
                scheduler.step()
                
                # Add batch loss
                train_loss += loss.item() * features.size(0)
            
            # Calculate average loss
            train_loss /= len(train_loader.dataset)
            
            # Validation phase
            self.temp_model.eval()
            val_loss = 0.0
            predictions = []
            actuals = []
            
            with torch.no_grad():
                for features, targets in val_loader:
                    features, targets = features.to(self.device), targets.to(self.device)
                    
                    # Forward pass
                    outputs = self.temp_model(features)
                    
                    # Calculate loss (same combined loss)
                    mse_loss = mse_criterion(outputs, targets)
                    l1_loss = l1_criterion(outputs, targets)
                    loss = 0.8 * mse_loss + 0.2 * l1_loss
                    
                    # Add batch loss
                    val_loss += loss.item() * features.size(0)
                    
                    # Store predictions and targets for metrics
                    predictions.extend(outputs.cpu().numpy())
                    actuals.extend(targets.cpu().numpy())
            
            # Calculate average loss and metrics
            val_loss /= len(val_loader.dataset)
            
            # Calculate metrics
            val_rmse = np.sqrt(mean_squared_error(actuals, predictions))
            val_r2 = r2_score(actuals, predictions)
            val_mae = mean_absolute_error(actuals, predictions)
            
            # Log metrics
            if (epoch + 1) % 10 == 0 or epoch == 0:
                logger.info(f"Epoch {epoch+1}/{self.temp_nn_epochs} - "
                           f"Train Loss: {train_loss:.4f}, "
                           f"Val Loss: {val_loss:.4f}, "
                           f"Val RMSE: {val_rmse:.4f}, "
                           f"Val R²: {val_r2:.4f}, "
                           f"Val MAE: {val_mae:.4f}")
            
            # Early stopping - store best model with a higher patience
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                best_model_state = self.temp_model.state_dict().copy()
                best_epoch = epoch + 1
                patience_counter = 0
            else:
                patience_counter += 1
                
            if patience_counter >= self.temp_nn_patience:
                logger.info(f"Early stopping at epoch {epoch+1}, best epoch was {best_epoch}")
                break
        
        # Load best model
        if best_model_state is not None:
            self.temp_model.load_state_dict(best_model_state)
            logger.info(f"Loaded best model from epoch {best_epoch} with validation loss: {best_val_loss:.4f}")
        
        # Calculate final metrics
        self.temp_model.eval()
        predictions = []
        actuals = []
        
        with torch.no_grad():
            for features, targets in val_loader:
                features, targets = features.to(self.device), targets.to(self.device)
                outputs = self.temp_model(features)
                predictions.extend(outputs.cpu().numpy())
                actuals.extend(targets.cpu().numpy())
        
        final_rmse = np.sqrt(mean_squared_error(actuals, predictions))
        final_r2 = r2_score(actuals, predictions)
        final_mae = mean_absolute_error(actuals, predictions)
        
        logger.info(f"Final Temperature Model - "
                   f"Val RMSE: {final_rmse:.4f}, "
                   f"Val R²: {final_r2:.4f}, "
                   f"Val MAE: {final_mae:.4f}")

def main():
    """Main function."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Train hybrid model with highly optimized temperature DNN v2')
    parser.add_argument('--feature_file', type=str, required=True, help='Path to feature file (TSV)')
    parser.add_argument('--metadata_file', type=str, required=True, help='Path to metadata file (TSV)')
    parser.add_argument('--output_dir', type=str, required=True, help='Directory to save outputs')
    parser.add_argument('--n_features', type=int, default=200, help='Number of features to use (increased from 150)')
    
    # Enhanced parameters with higher epochs and patience
    parser.add_argument('--temp_nn_epochs', type=int, default=200,
                       help='Maximum number of epochs for temperature neural network')
    parser.add_argument('--temp_nn_patience', type=int, default=25,
                       help='Early stopping patience for temperature neural network')
    parser.add_argument('--seed', type=int, default=42, help='Random seed')
    
    args = parser.parse_args()
    
    # Set random seeds for reproducibility
    np.random.seed(args.seed)
    torch.manual_seed(args.seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(args.seed)
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Load data
    features, metadata = load_data(args.feature_file, args.metadata_file)
    
    # Prepare data with optimized processing for temperature prediction
    (X_train, y_growth_train, y_temp_train, 
     X_val, y_growth_val, y_temp_val, 
     X_test, y_growth_test, y_temp_test, 
     selected_features, transform_info) = prepare_data_optimized(
        features, metadata, args.n_features, args.output_dir
    )
    
    # Log dataset information
    logger.info(f"Training set: {X_train.shape[0]} samples, {X_train.shape[1]} features")
    logger.info(f"Validation set: {X_val.shape[0]} samples, {X_val.shape[1]} features")
    logger.info(f"Test set: {X_test.shape[0]} samples, {X_test.shape[1]} features")
    
    # Initialize the enhanced hybrid model
    model = EnhancedHybridModel(
        # Growth rate ensemble parameters - keep as is
        growth_ensemble_weight_lr=0.01,
        growth_ensemble_weight_epochs=50,
        
        # Temperature neural network parameters - highly optimized
        temp_nn_hidden_dims=[512, 512, 512, 384, 256, 128],
        temp_nn_dropout_rates=[0.2, 0.3, 0.3, 0.4, 0.5, 0.5],
        temp_nn_lr=0.0005,
        temp_nn_batch_size=32,
        temp_nn_epochs=args.temp_nn_epochs,
        temp_nn_patience=args.temp_nn_patience,
        
        # General parameters
        variance_percentile=25
    )
    
    # Fit model
    model.fit(
        X_train, 
        y_growth_train, 
        y_temp_train,
        X_val, 
        y_growth_val, 
        y_temp_val,
        args.output_dir
    )
    
    # Evaluate model
    metrics = evaluate_model(
        model, X_test, y_growth_test, y_temp_test,
        transform_info, args.output_dir
    )
    
    # Print final metrics
    logger.info("Hybrid model with highly optimized temperature DNN v2 - training and evaluation completed")
    logger.info(f"Final metrics (scaled) - Overall R²: {metrics['scaled']['overall']['r2']:.4f}")
    logger.info(f"Final metrics (scaled) - Growth Rate R²: {metrics['scaled']['growth_rate']['r2']:.4f}")
    logger.info(f"Final metrics (scaled) - Temperature R²: {metrics['scaled']['temperature']['r2']:.4f}")
    
    logger.info(f"Final metrics (original) - Overall R²: {metrics['original']['overall']['r2']:.4f}")
    logger.info(f"Final metrics (original) - Growth Rate R²: {metrics['original']['growth_rate']['r2']:.4f}")
    logger.info(f"Final metrics (original) - Temperature R²: {metrics['original']['temperature']['r2']:.4f}")
    
    # Save command line arguments
    with open(os.path.join(args.output_dir, 'args.txt'), 'w') as f:
        for arg, value in vars(args).items():
            f.write(f"{arg}: {value}\n")
    
    logger.info(f"Model and results saved to {args.output_dir}")
    logger.info("Done!")

if __name__ == "__main__":
    main() 