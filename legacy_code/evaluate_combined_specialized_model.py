#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Evaluate Combined Specialized Model.
This script combines our specialized models:
1. Hybrid model with enhanced neural network for growth rate prediction
2. Enhanced DNN model for temperature prediction
And evaluates the combined model on the test set.
"""

import os
import argparse
import logging
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
import joblib
from pathlib import Path

# Import our enhanced DNN model for temperature prediction
from train_enhanced_dnn_temperature import RegularizedDNN

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_data(feature_file, metadata_file):
    """
    Load and prepare feature and metadata files.
    
    Args:
        feature_file: Path to the combined feature file (TSV)
        metadata_file: Path to the metadata file (TSV)
        
    Returns:
        Tuple of (features_df, metadata_df)
    """
    logger.info(f"Loading features from {feature_file}")
    features_df = pd.read_csv(feature_file, sep='\t', index_col='genome_id')
    
    # Check for NaN values in features
    nan_count = features_df.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in features. Filling with 0.")
        features_df = features_df.fillna(0)
    
    # Check for infinite values
    inf_count = np.isinf(features_df.values).sum()
    if inf_count > 0:
        logger.warning(f"Found {inf_count} infinite values in features. Replacing with max/min values.")
        features_df = features_df.replace([np.inf, -np.inf], np.nan)
        features_df = features_df.fillna(features_df.max())

    logger.info(f"Loading metadata from {metadata_file}")
    metadata_df = pd.read_csv(metadata_file, sep='\t', index_col='genome_id')
    
    # Check for NaN values in metadata
    nan_count = metadata_df.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in metadata. Filling with 0.")
        metadata_df = metadata_df.fillna(0)

    # Get common genomes
    common_genomes = list(set(features_df.index) & set(metadata_df.index))
    logger.info(f"Found {len(common_genomes)} common genomes")

    # Filter to common genomes
    features_df = features_df.loc[common_genomes]
    metadata_df = metadata_df.loc[common_genomes]
    
    return features_df, metadata_df

class CombinedSpecializedModel:
    """
    Combined Specialized Model that uses:
    1. Hybrid model with enhanced neural network for growth rate prediction
    2. Enhanced DNN model for temperature prediction
    """
    def __init__(self, growth_model_dir, temp_model_dir):
        """
        Initialize combined specialized model.
        
        Args:
            growth_model_dir: Directory containing the growth rate model
            temp_model_dir: Directory containing the temperature model
        """
        self.growth_model_dir = growth_model_dir
        self.temp_model_dir = temp_model_dir
        
        # Load growth rate model components
        self.load_growth_model()
        
        # Load temperature model
        self.load_temp_model()
        
        logger.info("Combined specialized model initialized successfully")
    
    def load_growth_model(self):
        """Load growth rate model components."""
        logger.info(f"Loading growth rate model from {self.growth_model_dir}")
        
        # Load feature scaler
        self.growth_scaler = joblib.load(os.path.join(self.growth_model_dir, 'feature_scaler.joblib'))
        
        # Load Random Forest model
        rf_dir = os.path.join(self.growth_model_dir, 'rf_models')
        self.growth_rf = joblib.load(os.path.join(rf_dir, 'rf_growth_model.joblib'))
        
        # Load XGBoost model
        xgb_dir = os.path.join(self.growth_model_dir, 'xgb_models')
        self.growth_xgb = joblib.load(os.path.join(xgb_dir, 'xgb_growth_model.joblib'))
        
        # Load LightGBM model
        lgb_dir = os.path.join(self.growth_model_dir, 'lgb_models')
        try:
            self.growth_lgb = joblib.load(os.path.join(lgb_dir, 'lgb_growth_model.joblib'))
        except:
            # Try alternative filename
            self.growth_lgb = joblib.load(os.path.join(lgb_dir, 'growth_model.joblib'))
        
        # Load ensemble weights
        self.growth_weights = torch.load(
            os.path.join(self.growth_model_dir, 'growth_weights.pth'),
            map_location=torch.device('cpu')
        )
        
        logger.info("Growth rate model components loaded successfully")
    
    def load_temp_model(self):
        """Load temperature model."""
        logger.info(f"Loading temperature model from {self.temp_model_dir}")
        
        # Load feature scaler
        self.temp_scaler = joblib.load(os.path.join(self.temp_model_dir, 'temperature_scaler.joblib'))
        
        # Load model architecture
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # Get input dimension from scaler
        input_dim = len(self.temp_scaler.mean_)
        
        # Create model with the same architecture
        self.temp_model = RegularizedDNN(
            input_dim=input_dim, 
            hidden_dims=[512, 512, 384, 256, 128], 
            dropout_rates=[0.3, 0.4, 0.4, 0.5, 0.5],
            use_batch_norm=True,
            activation='relu'
        )
        
        # Load weights
        self.temp_model.load_state_dict(torch.load(
            os.path.join(self.temp_model_dir, 'temperature_model.pt'),
            map_location=device
        ))
        
        # Set model to evaluation mode
        self.temp_model.eval()
        
        # Set device
        self.device = device
        self.temp_model.to(self.device)
        
        logger.info("Temperature model loaded successfully")
    
    def predict_growth(self, X):
        """
        Generate growth rate predictions.
        
        Args:
            X: Features DataFrame
            
        Returns:
            Growth rate predictions
        """
        # Handle older scikit-learn versions that don't have feature_names_in_
        if hasattr(self.growth_scaler, 'feature_names_in_'):
            # Filter features to match training data
            common_cols = [col for col in X.columns if col in self.growth_scaler.feature_names_in_]
            X_filtered = X[common_cols]
        else:
            # Use all features (assuming they're in the right order)
            X_filtered = X
        
        # Scale features
        X_scaled = self.growth_scaler.transform(X_filtered)
        
        # Get predictions from Random Forest
        rf_growth_pred = self.growth_rf.predict(X_scaled)
        
        # Get predictions from XGBoost
        xgb_growth_pred = self.growth_xgb.predict(X_scaled)
        
        # Get predictions from LightGBM
        lgb_growth_pred = self.growth_lgb.predict(X_scaled)
        
        # Combine predictions using ensemble weights
        growth_weights = self.growth_weights.cpu().numpy()
        
        combined_growth_pred = (
            growth_weights[0] * rf_growth_pred + 
            growth_weights[1] * xgb_growth_pred +
            growth_weights[2] * lgb_growth_pred
        )
        
        return combined_growth_pred
    
    def predict_temperature(self, X):
        """
        Generate temperature predictions.
        
        Args:
            X: Features DataFrame
            
        Returns:
            Temperature predictions
        """
        # Handle older scikit-learn versions that don't have feature_names_in_
        if hasattr(self.temp_scaler, 'feature_names_in_'):
            # Filter features to match training data
            common_cols = [col for col in X.columns if col in self.temp_scaler.feature_names_in_]
            X_filtered = X[common_cols]
        else:
            # Use all features (assuming they're in the right order)
            X_filtered = X
        
        # Scale features
        X_scaled = self.temp_scaler.transform(X_filtered)
        
        # Convert to tensor
        X_tensor = torch.tensor(X_scaled, dtype=torch.float32).to(self.device)
        
        # Generate predictions
        with torch.no_grad():
            temp_pred = self.temp_model(X_tensor)
            temp_pred = temp_pred.cpu().numpy()
        
        return temp_pred
    
    def predict(self, X):
        """
        Generate predictions for both growth rate and temperature.
        
        Args:
            X: Features DataFrame
            
        Returns:
            Tuple of (growth_rate_predictions, temperature_predictions)
        """
        growth_pred = self.predict_growth(X)
        temp_pred = self.predict_temperature(X)
        
        return growth_pred, temp_pred

def evaluate_model(growth_model_dir, temp_model_dir, feature_file, metadata_file, output_dir=None):
    """
    Evaluate the combined specialized model on the test set.
    
    Args:
        growth_model_dir: Directory containing the growth rate model
        temp_model_dir: Directory containing the temperature model
        feature_file: Path to combined feature file (TSV)
        metadata_file: Path to metadata file (TSV)
        output_dir: Directory to save evaluation results
        
    Returns:
        Dictionary with evaluation results
    """
    logger.info(f"Evaluating combined specialized model")
    
    # Create output directory if provided
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
    
    # Load and prepare data
    features_df, metadata_df = load_data(feature_file, metadata_file)
    
    # Get target variables
    y_growth = metadata_df['growth_rate'].values
    y_temp = metadata_df['optimal_temperature'].values
    
    logger.info(f"Growth rate statistics - Min: {y_growth.min():.4f}, Max: {y_growth.max():.4f}, Mean: {y_growth.mean():.4f}")
    logger.info(f"Temperature statistics - Min: {y_temp.min():.4f}, Max: {y_temp.max():.4f}, Mean: {y_temp.mean():.4f}")
    
    # Create stratification bins for splitting
    # For growth rate
    growth_bins = pd.qcut(y_growth, 5, labels=False, duplicates='drop')
    
    # Split data into train, validation, and test sets with stratification
    X_train_val, X_test, y_growth_train_val, y_growth_test, y_temp_train_val, y_temp_test, bins_train_val, _ = train_test_split(
        features_df, y_growth, y_temp, growth_bins, test_size=0.2, random_state=42, stratify=growth_bins
    )
    
    # Then split train+val into train and validation
    X_train, X_val, y_growth_train, y_growth_val, y_temp_train, y_temp_val = train_test_split(
        X_train_val, y_growth_train_val, y_temp_train_val, test_size=0.25, random_state=42, 
        stratify=bins_train_val
    )
    
    # Log data split statistics
    logger.info(f"Training set: {X_train.shape[0]} samples")
    logger.info(f"Validation set: {X_val.shape[0]} samples")
    logger.info(f"Test set: {X_test.shape[0]} samples")
    
    # Initialize combined model
    model = CombinedSpecializedModel(growth_model_dir, temp_model_dir)
    
    # Evaluate on train, validation, and test sets
    results = {}
    
    # Function to evaluate on a dataset
    def evaluate_dataset(X, y_growth, y_temp, dataset_name):
        # Generate predictions
        growth_pred, temp_pred = model.predict(X)
        
        # Calculate metrics for growth rate
        growth_rmse = np.sqrt(mean_squared_error(y_growth, growth_pred))
        growth_r2 = r2_score(y_growth, growth_pred)
        growth_mae = mean_absolute_error(y_growth, growth_pred)
        
        # Calculate metrics for temperature
        temp_rmse = np.sqrt(mean_squared_error(y_temp, temp_pred))
        temp_r2 = r2_score(y_temp, temp_pred)
        temp_mae = mean_absolute_error(y_temp, temp_pred)
        
        # Calculate overall metrics (average R2)
        overall_r2 = (growth_r2 + temp_r2) / 2
        overall_rmse = (growth_rmse + temp_rmse) / 2
        overall_mae = (growth_mae + temp_mae) / 2
        
        logger.info(f"{dataset_name} - Overall R²: {overall_r2:.4f}")
        logger.info(f"{dataset_name} - Growth Rate: R²={growth_r2:.4f}, RMSE={growth_rmse:.4f}, MAE={growth_mae:.4f}")
        logger.info(f"{dataset_name} - Temperature: R²={temp_r2:.4f}, RMSE={temp_rmse:.4f}, MAE={temp_mae:.4f}")
        
        return {
            'overall': {
                'r2': overall_r2,
                'rmse': overall_rmse,
                'mae': overall_mae
            },
            'growth_rate': {
                'r2': growth_r2,
                'rmse': growth_rmse,
                'mae': growth_mae,
                'targets': y_growth,
                'predictions': growth_pred
            },
            'temperature': {
                'r2': temp_r2,
                'rmse': temp_rmse,
                'mae': temp_mae,
                'targets': y_temp,
                'predictions': temp_pred
            }
        }
    
    # Evaluate on each dataset
    train_results = evaluate_dataset(X_train, y_growth_train, y_temp_train, "Train")
    val_results = evaluate_dataset(X_val, y_growth_val, y_temp_val, "Validation")
    test_results = evaluate_dataset(X_test, y_growth_test, y_temp_test, "Test")
    
    results['train'] = train_results
    results['val'] = val_results
    results['test'] = test_results
    
    # Save results if output_dir is provided
    if output_dir:
        # Save metrics
        metrics_df = pd.DataFrame({
            'dataset': ['train', 'val', 'test'],
            'overall_r2': [train_results['overall']['r2'], val_results['overall']['r2'], test_results['overall']['r2']],
            'overall_rmse': [train_results['overall']['rmse'], val_results['overall']['rmse'], test_results['overall']['rmse']],
            'overall_mae': [train_results['overall']['mae'], val_results['overall']['mae'], test_results['overall']['mae']],
            'growth_r2': [train_results['growth_rate']['r2'], val_results['growth_rate']['r2'], test_results['growth_rate']['r2']],
            'growth_rmse': [train_results['growth_rate']['rmse'], val_results['growth_rate']['rmse'], test_results['growth_rate']['rmse']],
            'growth_mae': [train_results['growth_rate']['mae'], val_results['growth_rate']['mae'], test_results['growth_rate']['mae']],
            'temp_r2': [train_results['temperature']['r2'], val_results['temperature']['r2'], test_results['temperature']['r2']],
            'temp_rmse': [train_results['temperature']['rmse'], val_results['temperature']['rmse'], test_results['temperature']['rmse']],
            'temp_mae': [train_results['temperature']['mae'], val_results['temperature']['mae'], test_results['temperature']['mae']]
        })
        metrics_df.to_csv(os.path.join(output_dir, 'combined_specialized_metrics.tsv'), sep='\t', index=False)
        
        # Plot actual vs predicted for test set - Growth Rate
        plt.figure(figsize=(12, 5))
        
        plt.subplot(1, 2, 1)
        plt.scatter(test_results['growth_rate']['targets'], test_results['growth_rate']['predictions'], alpha=0.5)
        plt.plot([min(test_results['growth_rate']['targets']), max(test_results['growth_rate']['targets'])], 
                 [min(test_results['growth_rate']['targets']), max(test_results['growth_rate']['targets'])], 'r--')
        plt.xlabel('Actual Growth Rate')
        plt.ylabel('Predicted Growth Rate')
        plt.title(f'Growth Rate (Test Set, R² = {test_results["growth_rate"]["r2"]:.4f})')
        
        # Plot actual vs predicted for test set - Temperature
        plt.subplot(1, 2, 2)
        plt.scatter(test_results['temperature']['targets'], test_results['temperature']['predictions'], alpha=0.5)
        plt.plot([min(test_results['temperature']['targets']), max(test_results['temperature']['targets'])], 
                 [min(test_results['temperature']['targets']), max(test_results['temperature']['targets'])], 'r--')
        plt.xlabel('Actual Temperature')
        plt.ylabel('Predicted Temperature')
        plt.title(f'Temperature (Test Set, R² = {test_results["temperature"]["r2"]:.4f})')
        
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'combined_specialized_test_predictions.png'))
        
        # Save test predictions
        pd.DataFrame({
            'actual_growth': test_results['growth_rate']['targets'],
            'predicted_growth': test_results['growth_rate']['predictions'],
            'actual_temp': test_results['temperature']['targets'],
            'predicted_temp': test_results['temperature']['predictions']
        }).to_csv(os.path.join(output_dir, 'combined_specialized_test_predictions.tsv'), sep='\t', index=False)
        
        logger.info(f"Saved evaluation results to {output_dir}")
    
    return results

def main():
    parser = argparse.ArgumentParser(description='Evaluate Combined Specialized Model')
    parser.add_argument('--growth-model-dir', type=str, required=True, 
                        help='Directory containing the growth rate model')
    parser.add_argument('--temp-model-dir', type=str, required=True, 
                        help='Directory containing the temperature model')
    parser.add_argument('--feature-file', type=str, default='./training_data/combined_features.tsv', 
                        help='Path to the combined feature file (TSV)')
    parser.add_argument('--metadata', type=str, default='./training_data/metadata.tsv', 
                        help='Path to metadata file')
    parser.add_argument('--output-dir', type=str, default=None, 
                        help='Directory to save evaluation results')
    
    args = parser.parse_args()
    
    try:
        # Create output directory if provided
        if args.output_dir:
            os.makedirs(args.output_dir, exist_ok=True)
        
        # Evaluate model
        results = evaluate_model(
            args.growth_model_dir,
            args.temp_model_dir,
            args.feature_file,
            args.metadata,
            args.output_dir
        )
        
        logger.info("Combined specialized model evaluation completed successfully")
        return 0
    
    except Exception as e:
        logger.error(f"Error evaluating combined specialized model: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == '__main__':
    main()
