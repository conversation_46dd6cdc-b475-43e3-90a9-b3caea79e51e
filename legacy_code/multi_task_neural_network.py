#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Multi-Task Neural Network Component for Growth Rate and Temperature Prediction.
This module provides a PyTorch neural network model with batch normalization
that can predict both growth rate and optimal temperature simultaneously.
"""

import os
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import logging
from typing import Dict, List, Tuple, Optional
from sklearn.preprocessing import StandardScaler

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MultiTaskDataset(Dataset):
    """Dataset for multi-task tabular data."""
    def __init__(self, features: np.ndarray, growth_rate: np.ndarray, temperature: np.ndarray):
        """
        Initialize dataset.
        
        Args:
            features: Feature matrix
            growth_rate: Growth rate target vector
            temperature: Temperature target vector
        """
        self.features = torch.tensor(features, dtype=torch.float32)
        self.growth_rate = torch.tensor(growth_rate, dtype=torch.float32).reshape(-1, 1)
        self.temperature = torch.tensor(temperature, dtype=torch.float32).reshape(-1, 1)
        
    def __len__(self):
        return len(self.features)
    
    def __getitem__(self, idx):
        return self.features[idx], (self.growth_rate[idx], self.temperature[idx])

class MultiTaskTabularNN(nn.Module):
    """
    A multi-task neural network for tabular data with batch normalization.
    Predicts both growth rate and optimal temperature.
    """
    def __init__(
        self,
        input_dim: int,
        hidden_dim: int = 128,
        dropout: float = 0.3
    ):
        """
        Initialize neural network.
        
        Args:
            input_dim: Number of input features
            hidden_dim: Size of hidden layers
            dropout: Dropout rate
        """
        super().__init__()
        
        # Shared layers
        self.shared_layer1 = nn.Linear(input_dim, hidden_dim)
        self.shared_bn1 = nn.BatchNorm1d(hidden_dim)
        
        self.shared_layer2 = nn.Linear(hidden_dim, hidden_dim)
        self.shared_bn2 = nn.BatchNorm1d(hidden_dim)
        
        # Growth rate specific layers
        self.growth_layer1 = nn.Linear(hidden_dim, hidden_dim // 2)
        self.growth_bn1 = nn.BatchNorm1d(hidden_dim // 2)
        self.growth_output = nn.Linear(hidden_dim // 2, 1)
        
        # Temperature specific layers
        self.temp_layer1 = nn.Linear(hidden_dim, hidden_dim // 2)
        self.temp_bn1 = nn.BatchNorm1d(hidden_dim // 2)
        self.temp_output = nn.Linear(hidden_dim // 2, 1)
        
        self.dropout = nn.Dropout(dropout)
        self.activation = nn.GELU()  # Using GELU activation as in modern architectures
    
    def forward(self, x):
        """
        Forward pass.
        
        Args:
            x: Input tensor
            
        Returns:
            Tuple of (growth_rate_prediction, temperature_prediction)
        """
        # Shared layers
        x = self.shared_layer1(x)
        x = self.shared_bn1(x)
        x = self.activation(x)
        x = self.dropout(x)
        
        x = self.shared_layer2(x)
        x = self.shared_bn2(x)
        x = self.activation(x)
        x = self.dropout(x)
        
        # Growth rate specific path
        growth = self.growth_layer1(x)
        growth = self.growth_bn1(growth)
        growth = self.activation(growth)
        growth = self.dropout(growth)
        growth = self.growth_output(growth)
        
        # Temperature specific path
        temp = self.temp_layer1(x)
        temp = self.temp_bn1(temp)
        temp = self.activation(temp)
        temp = self.dropout(temp)
        temp = self.temp_output(temp)
        
        return growth, temp

class MultiTaskNeuralNetwork:
    """
    Multi-Task Neural Network component for growth rate and temperature prediction.
    """
    def __init__(
        self,
        hidden_dim: int = 128,
        dropout: float = 0.3,
        lr: float = 0.001,
        batch_size: int = 64,
        epochs: int = 100,
        patience: int = 10,
        growth_weight: float = 0.5,  # Weight for growth rate loss
        device: str = None
    ):
        """
        Initialize neural network component.
        
        Args:
            hidden_dim: Size of hidden layers
            dropout: Dropout rate
            lr: Learning rate
            batch_size: Batch size for training
            epochs: Maximum number of epochs
            patience: Patience for early stopping
            growth_weight: Weight for growth rate loss (1-growth_weight for temperature)
            device: Device to use (cpu or cuda)
        """
        self.hidden_dim = hidden_dim
        self.dropout = dropout
        self.lr = lr
        self.batch_size = batch_size
        self.epochs = epochs
        self.patience = patience
        self.growth_weight = growth_weight
        
        # Determine device
        if device is None:
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = torch.device(device)
            
        logger.info(f"Using device: {self.device}")
        
        # Initialize model, optimizer, and loss function
        self.model = None
        self.optimizer = None
        self.criterion = nn.MSELoss()
        
        # Initialize scalers
        self.feature_scaler = StandardScaler()
        self.growth_scaler = None
        self.temp_scaler = None
        
    def fit(
        self, 
        X: pd.DataFrame, 
        y_growth: pd.Series, 
        y_temp: pd.Series,
        X_val: pd.DataFrame = None, 
        y_val_growth: pd.Series = None,
        y_val_temp: pd.Series = None
    ) -> Dict[str, List[float]]:
        """
        Fit neural network to data.
        
        Args:
            X: Training features
            y_growth: Training growth rate targets
            y_temp: Training temperature targets
            X_val: Validation features (optional)
            y_val_growth: Validation growth rate targets (optional)
            y_val_temp: Validation temperature targets (optional)
            
        Returns:
            Dictionary of training history
        """
        # Scale features
        X_scaled = self.feature_scaler.fit_transform(X)
        
        # Store scalers for targets if provided
        if self.growth_scaler is not None:
            y_growth_scaled = y_growth.values
        else:
            y_growth_scaled = y_growth.values
            
        if self.temp_scaler is not None:
            y_temp_scaled = y_temp.values
        else:
            y_temp_scaled = y_temp.values
        
        # Convert validation data if provided
        if X_val is not None and y_val_growth is not None and y_val_temp is not None:
            X_val_scaled = self.feature_scaler.transform(X_val)
            
            if self.growth_scaler is not None:
                y_val_growth_scaled = y_val_growth.values
            else:
                y_val_growth_scaled = y_val_growth.values
                
            if self.temp_scaler is not None:
                y_val_temp_scaled = y_val_temp.values
            else:
                y_val_temp_scaled = y_val_temp.values
                
            val_dataset = MultiTaskDataset(X_val_scaled, y_val_growth_scaled, y_val_temp_scaled)
            val_loader = DataLoader(val_dataset, batch_size=self.batch_size, shuffle=False)
        else:
            val_loader = None
        
        # Create dataset and dataloader
        train_dataset = MultiTaskDataset(X_scaled, y_growth_scaled, y_temp_scaled)
        train_loader = DataLoader(train_dataset, batch_size=self.batch_size, shuffle=True)
        
        # Initialize model
        input_dim = X.shape[1]
        self.model = MultiTaskTabularNN(
            input_dim=input_dim,
            hidden_dim=self.hidden_dim,
            dropout=self.dropout
        ).to(self.device)
        
        # Initialize optimizer with weight decay
        self.optimizer = optim.Adam(self.model.parameters(), lr=self.lr, weight_decay=1e-5)
        
        # Learning rate scheduler
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, mode='min', factor=0.5, patience=5
        )
        
        # Training loop
        best_val_loss = float('inf')
        epochs_no_improve = 0
        history = {
            'train_loss': [], 
            'train_growth_loss': [], 
            'train_temp_loss': [],
            'val_loss': [], 
            'val_growth_loss': [], 
            'val_temp_loss': []
        }
        
        logger.info(f"Starting multi-task neural network training for {self.epochs} epochs...")
        for epoch in range(self.epochs):
            # Train
            self.model.train()
            train_loss = 0.0
            train_growth_loss = 0.0
            train_temp_loss = 0.0
            
            for features, targets in train_loader:
                growth_targets, temp_targets = targets
                features = features.to(self.device)
                growth_targets = growth_targets.to(self.device)
                temp_targets = temp_targets.to(self.device)
                
                self.optimizer.zero_grad()
                growth_outputs, temp_outputs = self.model(features)
                
                # Calculate losses
                growth_loss = self.criterion(growth_outputs, growth_targets)
                temp_loss = self.criterion(temp_outputs, temp_targets)
                
                # Weighted combined loss
                loss = self.growth_weight * growth_loss + (1 - self.growth_weight) * temp_loss
                
                loss.backward()
                self.optimizer.step()
                
                # Track losses
                train_loss += loss.item() * features.size(0)
                train_growth_loss += growth_loss.item() * features.size(0)
                train_temp_loss += temp_loss.item() * features.size(0)
            
            # Calculate average losses
            train_loss /= len(train_loader.dataset)
            train_growth_loss /= len(train_loader.dataset)
            train_temp_loss /= len(train_loader.dataset)
            
            # Store in history
            history['train_loss'].append(train_loss)
            history['train_growth_loss'].append(train_growth_loss)
            history['train_temp_loss'].append(train_temp_loss)
            
            # Validate
            if val_loader is not None:
                val_loss, val_growth_loss, val_temp_loss = self._validate(val_loader)
                
                # Store in history
                history['val_loss'].append(val_loss)
                history['val_growth_loss'].append(val_growth_loss)
                history['val_temp_loss'].append(val_temp_loss)
                
                # Update learning rate
                scheduler.step(val_loss)
                
                # Early stopping
                if val_loss < best_val_loss:
                    best_val_loss = val_loss
                    epochs_no_improve = 0
                    # Save best model
                    self.best_model_state = self.model.state_dict().copy()
                else:
                    epochs_no_improve += 1
                
                if epochs_no_improve >= self.patience:
                    logger.info(f"Early stopping after {epoch+1} epochs without improvement")
                    break
                
                logger.info(
                    f"Epoch {epoch+1}/{self.epochs}: "
                    f"Train Loss: {train_loss:.4f} (Growth: {train_growth_loss:.4f}, Temp: {train_temp_loss:.4f}), "
                    f"Val Loss: {val_loss:.4f} (Growth: {val_growth_loss:.4f}, Temp: {val_temp_loss:.4f})"
                )
            else:
                logger.info(
                    f"Epoch {epoch+1}/{self.epochs}: "
                    f"Train Loss: {train_loss:.4f} (Growth: {train_growth_loss:.4f}, Temp: {train_temp_loss:.4f})"
                )
        
        # Load best model if validation was used
        if val_loader is not None and hasattr(self, 'best_model_state'):
            self.model.load_state_dict(self.best_model_state)
            logger.info(f"Loaded best model with validation loss: {best_val_loss:.4f}")
        
        return history
    
    def _validate(self, val_loader: DataLoader) -> Tuple[float, float, float]:
        """
        Validate model on validation data.
        
        Args:
            val_loader: Validation data loader
            
        Returns:
            Tuple of (combined_loss, growth_loss, temp_loss)
        """
        self.model.eval()
        val_loss = 0.0
        val_growth_loss = 0.0
        val_temp_loss = 0.0
        
        with torch.no_grad():
            for features, targets in val_loader:
                growth_targets, temp_targets = targets
                features = features.to(self.device)
                growth_targets = growth_targets.to(self.device)
                temp_targets = temp_targets.to(self.device)
                
                growth_outputs, temp_outputs = self.model(features)
                
                # Calculate losses
                growth_loss = self.criterion(growth_outputs, growth_targets)
                temp_loss = self.criterion(temp_outputs, temp_targets)
                
                # Weighted combined loss
                loss = self.growth_weight * growth_loss + (1 - self.growth_weight) * temp_loss
                
                # Track losses
                val_loss += loss.item() * features.size(0)
                val_growth_loss += growth_loss.item() * features.size(0)
                val_temp_loss += temp_loss.item() * features.size(0)
        
        # Calculate average losses
        val_loss /= len(val_loader.dataset)
        val_growth_loss /= len(val_loader.dataset)
        val_temp_loss /= len(val_loader.dataset)
        
        return val_loss, val_growth_loss, val_temp_loss
    
    def predict(self, X: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """
        Generate predictions.
        
        Args:
            X: Features
            
        Returns:
            Tuple of (growth_rate_predictions, temperature_predictions)
        """
        if self.model is None:
            raise ValueError("Model has not been trained yet")
        
        # Scale features
        X_scaled = self.feature_scaler.transform(X)
        
        # Convert to tensor
        X_tensor = torch.tensor(X_scaled, dtype=torch.float32).to(self.device)
        
        # Generate predictions
        self.model.eval()
        with torch.no_grad():
            growth_pred, temp_pred = self.model(X_tensor)
            growth_pred = growth_pred.cpu().numpy()
            temp_pred = temp_pred.cpu().numpy()
        
        # Inverse transform if scalers are available
        if self.growth_scaler is not None:
            growth_pred = self.growth_scaler.inverse_transform(growth_pred)
            
        if self.temp_scaler is not None:
            temp_pred = self.temp_scaler.inverse_transform(temp_pred)
        
        return growth_pred.flatten(), temp_pred.flatten()
    
    def predict_growth(self, X: pd.DataFrame) -> np.ndarray:
        """
        Generate growth rate predictions only.
        
        Args:
            X: Features
            
        Returns:
            Growth rate predictions
        """
        growth_pred, _ = self.predict(X)
        return growth_pred
    
    def predict_temperature(self, X: pd.DataFrame) -> np.ndarray:
        """
        Generate temperature predictions only.
        
        Args:
            X: Features
            
        Returns:
            Temperature predictions
        """
        _, temp_pred = self.predict(X)
        return temp_pred
    
    def save(self, output_dir: str) -> None:
        """
        Save model to disk.
        
        Args:
            output_dir: Directory to save model
        """
        if self.model is None:
            raise ValueError("Model has not been trained yet")
        
        os.makedirs(output_dir, exist_ok=True)
        
        # Save model
        torch.save(self.model.state_dict(), os.path.join(output_dir, 'multi_task_nn_model.pth'))
        
        # Save scalers
        import joblib
        joblib.dump(self.feature_scaler, os.path.join(output_dir, 'feature_scaler.joblib'))
        
        if self.growth_scaler is not None:
            joblib.dump(self.growth_scaler, os.path.join(output_dir, 'growth_scaler.joblib'))
            
        if self.temp_scaler is not None:
            joblib.dump(self.temp_scaler, os.path.join(output_dir, 'temp_scaler.joblib'))
        
        # Save hyperparameters
        hyperparams = {
            'hidden_dim': self.hidden_dim,
            'dropout': self.dropout,
            'lr': self.lr,
            'batch_size': self.batch_size,
            'epochs': self.epochs,
            'patience': self.patience,
            'growth_weight': self.growth_weight
        }
        joblib.dump(hyperparams, os.path.join(output_dir, 'hyperparams.joblib'))
        
        logger.info(f"Multi-task neural network model saved to {output_dir}")
    
    def load(self, input_dir: str) -> None:
        """
        Load model from disk.
        
        Args:
            input_dir: Directory to load model from
        """
        import joblib
        
        # Load scalers
        self.feature_scaler = joblib.load(os.path.join(input_dir, 'feature_scaler.joblib'))
        
        # Try to load target scalers if they exist
        try:
            self.growth_scaler = joblib.load(os.path.join(input_dir, 'growth_scaler.joblib'))
        except:
            self.growth_scaler = None
            
        try:
            self.temp_scaler = joblib.load(os.path.join(input_dir, 'temp_scaler.joblib'))
        except:
            self.temp_scaler = None
        
        # Load hyperparameters
        hyperparams = joblib.load(os.path.join(input_dir, 'hyperparams.joblib'))
        self.hidden_dim = hyperparams['hidden_dim']
        self.dropout = hyperparams['dropout']
        self.lr = hyperparams['lr']
        self.batch_size = hyperparams['batch_size']
        self.epochs = hyperparams['epochs']
        self.patience = hyperparams['patience']
        self.growth_weight = hyperparams['growth_weight']
        
        # Determine input dimension from scaler
        input_dim = len(self.feature_scaler.mean_)
        
        # Initialize model
        self.model = MultiTaskTabularNN(
            input_dim=input_dim,
            hidden_dim=self.hidden_dim,
            dropout=self.dropout
        ).to(self.device)
        
        # Load model weights
        self.model.load_state_dict(torch.load(
            os.path.join(input_dir, 'multi_task_nn_model.pth'),
            map_location=self.device
        ))
        
        logger.info(f"Multi-task neural network model loaded from {input_dir}")
