import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt
from pathlib import Path
from tqdm import tqdm
import seaborn as sns
import os

from cnn_dnn_temp_model_boxcox import CNNDNNTemperatureModelBoxCox

def load_and_preprocess_data(metadata_path, features_path, test_size=0.2, random_state=42):
    print("Loading metadata...")
    metadata = pd.read_csv(metadata_path, sep='\t')
    
    print("Loading features...")
    features = pd.read_csv(features_path, sep='\t')
    
    # Merge metadata and features
    data = pd.merge(metadata, features, on='genome_id', how='inner')
    
    # Extract features and target
    X = data.drop(['genome_id', 'growth_rate', 'optimal_temperature', 'taxid', 'codon_table', 'kingdom'], axis=1)
    y = data['optimal_temperature']
    
    # Split the data
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=test_size, random_state=random_state)
    
    # Scale features
    feature_scaler = StandardScaler()
    X_train_scaled = feature_scaler.fit_transform(X_train)
    X_test_scaled = feature_scaler.transform(X_test)
    
    return X_train_scaled, X_test_scaled, y_train.values, y_test.values, feature_scaler

def create_data_loaders(X_train, X_test, y_train, y_test, batch_size=32):
    # Convert to PyTorch tensors
    X_train_tensor = torch.FloatTensor(X_train)
    y_train_tensor = torch.FloatTensor(y_train)
    X_test_tensor = torch.FloatTensor(X_test)
    y_test_tensor = torch.FloatTensor(y_test)
    
    # Create datasets
    train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
    test_dataset = TensorDataset(X_test_tensor, y_test_tensor)
    
    # Create data loaders
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=batch_size)
    
    return train_loader, test_loader

def train_model(model, train_loader, test_loader, optimizer, criterion, scheduler, num_epochs, device, 
                l2_reg=0.0005, patience=20, max_grad_norm=0.5):
    train_losses = []
    test_losses = []
    best_test_loss = float('inf')
    patience_counter = 0
    
    for epoch in range(num_epochs):
        model.train()
        train_loss = 0.0
        for X_batch, y_batch in train_loader:
            X_batch, y_batch = X_batch.to(device), y_batch.to(device)
            y_batch = y_batch.view(-1, 1)
            
            optimizer.zero_grad()
            outputs = model(X_batch)
            loss = criterion(outputs, y_batch)
            
            # Add L2 regularization
            l2_loss = 0.0
            for param in model.parameters():
                l2_loss += torch.norm(param, 2)
            loss += l2_reg * l2_loss
            
            loss.backward()
            
            # Gradient clipping
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_grad_norm)
            
            optimizer.step()
            train_loss += loss.item()
        
        # Update learning rate
        scheduler.step()
        
        # Evaluate on test set
        model.eval()
        test_loss = 0.0
        with torch.no_grad():
            for X_batch, y_batch in test_loader:
                X_batch, y_batch = X_batch.to(device), y_batch.to(device)
                y_batch = y_batch.view(-1, 1)
                outputs = model(X_batch)
                loss = criterion(outputs, y_batch)
                test_loss += loss.item()
        
        train_loss /= len(train_loader)
        test_loss /= len(test_loader)
        train_losses.append(train_loss)
        test_losses.append(test_loss)
        
        # Early stopping
        if test_loss < best_test_loss:
            best_test_loss = test_loss
            patience_counter = 0
            # Save best model
            torch.save(model.state_dict(), 'models/best_model.pth')
        else:
            patience_counter += 1
            if patience_counter >= patience:
                print(f"\nEarly stopping triggered after {epoch + 1} epochs")
                # Load best model
                model.load_state_dict(torch.load('models/best_model.pth'))
                break
        
        if (epoch + 1) % 10 == 0:
            print(f'Epoch [{epoch+1}/{num_epochs}], Train Loss: {train_loss:.4f}, Test Loss: {test_loss:.4f}')
    
    return train_losses, test_losses

def evaluate_model(model, X_test, y_test, device):
    model.eval()
    with torch.no_grad():
        X_test_tensor = torch.FloatTensor(X_test).to(device)
        outputs = model(X_test_tensor, return_transformed=False)
        predictions = outputs.cpu().numpy().flatten()
    # y_test should be the original, untransformed values
    actuals = y_test
    mse = np.mean((predictions - actuals) ** 2)
    rmse = np.sqrt(mse)
    mae = np.mean(np.abs(predictions - actuals))
    r2 = 1 - np.sum((actuals - predictions) ** 2) / np.sum((actuals - np.mean(actuals)) ** 2)
    return {
        'MSE': mse,
        'RMSE': rmse,
        'MAE': mae,
        'R2': r2
    }

def plot_results(train_losses, test_losses, metrics, save_dir='plots'):
    # Create plots directory if it doesn't exist
    os.makedirs(save_dir, exist_ok=True)
    
    # Plot training and test losses
    plt.figure(figsize=(10, 6))
    plt.plot(train_losses, label='Training Loss')
    plt.plot(test_losses, label='Test Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('Training and Test Losses')
    plt.legend()
    plt.savefig(os.path.join(save_dir, 'loss_plot.png'))
    plt.close()
    
    # Plot metrics
    plt.figure(figsize=(10, 6))
    metrics_values = list(metrics.values())
    metrics_names = list(metrics.keys())
    plt.bar(metrics_names, metrics_values)
    plt.title('Model Evaluation Metrics')
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'metrics_plot.png'))
    plt.close()

def main():
    # Set device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Load and preprocess data
    print("Loading and preprocessing data...")
    X_train, X_test, y_train, y_test, feature_scaler = load_and_preprocess_data(
        'training_data/metadata.tsv',
        'training_data/combined_features.tsv'
    )
    
    # Initialize model with simpler architecture
    input_dim = X_train.shape[1]
    model = CNNDNNTemperatureModelBoxCox(
        input_dim=input_dim,
        hidden_dims=[128, 64, 32],  # Reduced dimensions
        cnn_filters=[32, 64, 128],  # Reduced filters
        kernel_sizes=(3, 5, 7),     # Reduced kernel sizes
        dropout_rate=0.4,           # Increased dropout
        l2_reg=0.0005              # Reduced L2 regularization
    ).to(device)
    
    # Fit Box-Cox transformer and transform targets
    y_train_trans = model.fit_transform(y_train)
    y_test_trans = model.transform(y_test)
    
    # Create data loaders using transformed targets
    train_loader, test_loader = create_data_loaders(X_train, X_test, y_train_trans, y_test_trans, batch_size=64)
    
    # Initialize optimizer with smaller learning rate
    optimizer = torch.optim.AdamW(  # Using AdamW optimizer
        model.parameters(),
        lr=0.0005,                 # Smaller initial learning rate
        weight_decay=0.0005,       # Reduced weight decay
        betas=(0.9, 0.999)
    )
    
    # Learning rate scheduler with warmup
    num_warmup_steps = 5
    num_training_steps = 100
    
    def lr_lambda(step):
        if step < num_warmup_steps:
            return float(step) / float(max(1, num_warmup_steps))
        return 1.0
    
    scheduler = optim.lr_scheduler.LambdaLR(optimizer, lr_lambda)
    
    criterion = nn.MSELoss()
    
    # Train model with increased patience
    print("Training model...")
    train_losses, test_losses = train_model(
        model, train_loader, test_loader, optimizer, criterion, scheduler,
        num_epochs=100, device=device,
        l2_reg=0.0005,    # Reduced L2 regularization
        patience=20,      # Increased patience for early stopping
        max_grad_norm=0.5 # Reduced gradient clipping
    )
    
    # Evaluate model using original (untransformed) targets
    print("Evaluating model...")
    metrics = evaluate_model(model, X_test, y_test, device)
    print("\nEvaluation Metrics:")
    for metric_name, value in metrics.items():
        print(f"{metric_name}: {value:.4f}")
    
    # Plot results
    print("Plotting results...")
    plot_results(train_losses, test_losses, metrics)
    
    # Save model
    print("Saving model...")
    torch.save({
        'model_state_dict': model.state_dict(),
        'feature_scaler': feature_scaler,
        'boxcox_lambda': model.boxcox.lambda_param,
        'optimizer_state_dict': optimizer.state_dict(),
        'scheduler_state_dict': scheduler.state_dict()
    }, 'models/temperature_model_boxcox.pth')
    
    print("Training completed. Check the 'models' directory for the saved model and 'plots' directory for visualizations.")

if __name__ == "__main__":
    main() 