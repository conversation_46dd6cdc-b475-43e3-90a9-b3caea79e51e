#!/bin/bash

# Run improved growth rate model for microbial growth rate prediction

# Set paths
FEATURE_FILE="training_data/combined_features.tsv"
METADATA_FILE="training_data/metadata.tsv"
OUTPUT_DIR="models/improved_growth_rate"

# Set parameters
N_FEATURES=150

# Create output directory
mkdir -p $OUTPUT_DIR

# Run improved growth rate model
python train_improved_growth_rate_model.py \
  --feature_file $FEATURE_FILE \
  --metadata_file $METADATA_FILE \
  --output_dir $OUTPUT_DIR \
  --n_features $N_FEATURES

echo "Improved growth rate model training and evaluation completed"
