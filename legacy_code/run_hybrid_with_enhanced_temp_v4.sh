#!/bin/bash

# Run the new hybrid model with optimally integrated components

# Set output directory
OUTPUT_DIR="models/hybrid_with_optimally_integrated_components_v4"

# Create output directory
mkdir -p "$OUTPUT_DIR"

# Run the script
python run_hybrid_with_enhanced_temp_v4.py \
    --feature_file "./training_data/combined_features.tsv" \
    --metadata_file "./training_data/metadata.tsv" \
    --output_dir "$OUTPUT_DIR" \
    --n_features 250 \
    --temp_nn_epochs 250 \
    --temp_nn_patience 25

# Compare all model versions
python simple_compare_models.py --output_dir "model_comparison"

echo "Training and comparison completed. Results are in $OUTPUT_DIR and model_comparison directories." 