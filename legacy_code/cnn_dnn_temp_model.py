import torch
import torch.nn as nn
import torch.nn.functional as F

class CNNDNNTemperatureModel(nn.Module):
    """Enhanced CNN+DNN model for temperature prediction with dual branch architecture.
    
    This dual-branch architecture combines:
    1. A primary branch with CNN+DNN for capturing general patterns
    2. A specialized branch with wider networks focusing on extreme values
    3. A range-aware output layer that learns to predict full temperature range
    """
    
    def __init__(self, input_dim, hidden_dims=(256, 128, 64, 32), cnn_filters=(128, 64, 32, 16), 
                 kernel_sizes=(3, 5, 7, 9), dropout_rate=0.3, l2_reg=0.001, use_residual=True):
        """Initialize the enhanced CNN+DNN temperature model.
        
        Args:
            input_dim: Dimension of input features
            hidden_dims: Tuple of hidden dimensions for DNN layers
            cnn_filters: Tuple of CNN filter numbers
            kernel_sizes: Tuple of kernel sizes for CNN layers
            dropout_rate: Dropout rate for regularization
            l2_reg: L2 regularization factor
            use_residual: Whether to use residual connections
        """
        super(CNNDNNTemperatureModel, self).__init__()
        
        # Store parameters
        self.input_dim = input_dim
        self.use_residual = use_residual
        self.l2_reg = l2_reg
        
        # Input normalization
        self.input_norm = nn.LayerNorm(input_dim)
        
        # ======================= PRIMARY BRANCH (CNN) ===========================
        # CNN layers
        self.cnn_layers = nn.ModuleList()
        self.bn_cnn_layers = nn.ModuleList()
        self.residual_layers = nn.ModuleList() if use_residual else None
        
        # Create 1D CNN layers
        in_channels = 1
        
        for i, (filters, kernel_size) in enumerate(zip(cnn_filters, kernel_sizes)):
            padding = kernel_size // 2  # Same padding
            self.cnn_layers.append(
                nn.Conv1d(in_channels, filters, kernel_size, padding=padding)
            )
            self.bn_cnn_layers.append(nn.BatchNorm1d(filters))
            
            # Add residual connections where filter sizes match or can be projected
            if use_residual and i > 0:
                if in_channels == filters:
                    # Identity residual connection
                    self.residual_layers.append(nn.Identity())
                else:
                    # Projection residual connection
                    self.residual_layers.append(
                        nn.Conv1d(in_channels, filters, kernel_size=1)
                    )
            
            in_channels = filters
        
        # Spatial dropout for regularization
        self.spatial_dropout = nn.Dropout2d(dropout_rate)
        
        # Average pooling and max pooling
        self.avg_pool = nn.AdaptiveAvgPool1d(1)
        self.max_pool = nn.AdaptiveMaxPool1d(1)
        
        # Calculate CNN output dimension
        cnn_output_dim = cnn_filters[-1] * 2  # Combined avg and max pooling
        
        # DNN layers after CNN
        self.cnn_dnn_layers = nn.ModuleList()
        self.cnn_bn_layers = nn.ModuleList()
        
        # First DNN layer after CNN
        self.cnn_dnn_layers.append(nn.Linear(cnn_output_dim, hidden_dims[0]))
        self.cnn_bn_layers.append(nn.BatchNorm1d(hidden_dims[0]))
        
        # Hidden DNN layers
        for i in range(len(hidden_dims) - 1):
            self.cnn_dnn_layers.append(nn.Linear(hidden_dims[i], hidden_dims[i+1]))
            self.cnn_bn_layers.append(nn.BatchNorm1d(hidden_dims[i+1]))
        
        # Primary branch output
        self.cnn_output = nn.Linear(hidden_dims[-1], 1)
        
        # ====================== SPECIALIZED BRANCH (DNN) ======================
        # Direct DNN branch optimized for extreme values
        wider_dims = [int(dim * 1.5) for dim in hidden_dims]  # 50% wider network
        
        self.extreme_layers = nn.ModuleList()
        self.extreme_bn_layers = nn.ModuleList()
        
        # First layer with direct input
        self.extreme_layers.append(nn.Linear(input_dim, wider_dims[0]))
        self.extreme_bn_layers.append(nn.BatchNorm1d(wider_dims[0]))
        
        # Hidden layers
        for i in range(len(wider_dims) - 1):
            self.extreme_layers.append(nn.Linear(wider_dims[i], wider_dims[i+1]))
            self.extreme_bn_layers.append(nn.BatchNorm1d(wider_dims[i+1]))
            
        # Extreme specialized output
        self.extreme_output = nn.Linear(wider_dims[-1], 1)
        
        # ====================== RANGE EXTENSION LAYER ======================
        # Final layer that combines both branches and applies range extension
        self.combine_layer = nn.Linear(2, 1)  # Combines the two branch outputs
        
        # Temperature range extension factors (learnable parameters)
        self.range_min = nn.Parameter(torch.tensor([-0.2]))  # Init to potentially go below 0
        self.range_max = nn.Parameter(torch.tensor([1.2]))   # Init to potentially go above 1
        
        # ====================== SHARED COMPONENTS ======================
        # Dropout for regularization
        self.dropout = nn.Dropout(dropout_rate)
            
    def forward(self, x):
        """Forward pass through the model.
        
        Args:
            x: Input tensor of shape (batch_size, input_dim)
            
        Returns:
            Output tensor of shape (batch_size, 1) in eval mode
            Output tensor and L2 regularization loss in train mode
        """
        batch_size = x.size(0)
        
        # Check for NaN values and replace with zeros
        if torch.isnan(x).any():
            x = torch.nan_to_num(x, nan=0.0)
        
        # Apply input normalization
        x_norm = self.input_norm(x)
        
        # ====================== PRIMARY BRANCH (CNN) ======================
        # Reshape for CNN (batch_size, channels, features)
        x_cnn = x_norm.view(batch_size, 1, self.input_dim)
        
        # Forward through CNN layers with residual connections
        for i, (cnn_layer, bn_layer) in enumerate(zip(self.cnn_layers, self.bn_cnn_layers)):
            identity = x_cnn
            x_cnn = cnn_layer(x_cnn)
            
            # Check for NaN values after convolution
            if torch.isnan(x_cnn).any():
                x_cnn = torch.nan_to_num(x_cnn, nan=0.0)
                
            x_cnn = bn_layer(x_cnn)
            x_cnn = F.relu(x_cnn)
            
            # Apply residual connection if not the first layer
            if self.use_residual and i > 0:
                res_layer = self.residual_layers[i-1]
                x_cnn = x_cnn + res_layer(identity)
        
        # Apply spatial dropout
        x_cnn = x_cnn.unsqueeze(3)  # [batch, channels, width, 1]
        x_cnn = self.spatial_dropout(x_cnn)
        x_cnn = x_cnn.squeeze(3)    # [batch, channels, width]
        
        # Apply pooling
        avg_pooled = self.avg_pool(x_cnn).view(batch_size, -1)
        max_pooled = self.max_pool(x_cnn).view(batch_size, -1)
        
        # Concatenate pooled features
        x_cnn = torch.cat([avg_pooled, max_pooled], dim=1)
        
        # Forward through DNN layers
        for i, (dnn_layer, bn_layer) in enumerate(zip(self.cnn_dnn_layers, self.cnn_bn_layers)):
            x_cnn = dnn_layer(x_cnn)
            
            # Check for NaN values after linear layer
            if torch.isnan(x_cnn).any():
                x_cnn = torch.nan_to_num(x_cnn, nan=0.0)
                
            x_cnn = bn_layer(x_cnn)
            x_cnn = F.relu(x_cnn)
            x_cnn = self.dropout(x_cnn)
        
        # Primary branch output
        primary_output = self.cnn_output(x_cnn)
        
        # ====================== SPECIALIZED BRANCH (DNN) ======================
        # Process through specialized extreme value branch
        x_extreme = x_norm
        
        for i, (extreme_layer, bn_layer) in enumerate(zip(self.extreme_layers, self.extreme_bn_layers)):
            x_extreme = extreme_layer(x_extreme)
            
            if torch.isnan(x_extreme).any():
                x_extreme = torch.nan_to_num(x_extreme, nan=0.0)
                
            x_extreme = bn_layer(x_extreme)
            x_extreme = F.relu(x_extreme)
            x_extreme = self.dropout(x_extreme)
            
        # Extreme branch output
        extreme_output = self.extreme_output(x_extreme)
        
        # ====================== COMBINE OUTPUTS ======================
        # Concatenate the two branch outputs
        combined = torch.cat([primary_output, extreme_output], dim=1)
        
        # Get raw combined prediction
        raw_pred = self.combine_layer(combined)
        
        # Range extension in training mode - this helps the model learn to predict extreme values
        if self.training:
            # Apply sigmoid to normalize to [0, 1] range
            normalized_pred = torch.sigmoid(raw_pred)
            
            # Apply range extension to go beyond [0, 1]
            # This lets the model learn to predict beyond observed ranges
            extended_pred = normalized_pred * (self.range_max - self.range_min) + self.range_min
            
            # Apply tanh to keep the output bounded but with extended range
            x = torch.tanh(extended_pred)
        else:
            # In eval mode, use direct output
            x = raw_pred
            
        # Final NaN check
        if torch.isnan(x).any():
            x = torch.nan_to_num(x, nan=0.0)
        
        # Apply L2 regularization (will be added to loss function)
        l2_reg_loss = torch.tensor(0., device=x.device)
        if self.training and self.l2_reg > 0:
            for param in self.parameters():
                if not torch.isnan(param).any():  # Skip parameters with NaN values
                    l2_reg_loss += torch.norm(param, 2)
            l2_reg_loss *= self.l2_reg
            return x, l2_reg_loss
        
        return x 