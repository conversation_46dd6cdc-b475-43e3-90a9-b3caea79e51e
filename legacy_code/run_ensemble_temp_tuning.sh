#!/bin/bash

# DeepMu: Hyperparameter Tuning for Ensemble Temperature Model
# This script uses Optuna to optimize the hyperparameters of the ensemble temperature model.

# Set a clean output directory
OUTPUT_DIR="models/deepmu_tuned_ensemble_temp"

# Create output directory
mkdir -p "$OUTPUT_DIR"

# Define color codes for output formatting
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Display initial message
echo -e "${BLUE}${BOLD}========================================================${NC}"
echo -e "${BLUE}${BOLD}   DeepMu - Ensemble Temperature Model Tuning   ${NC}"
echo -e "${BLUE}${BOLD}========================================================${NC}"
echo -e "${GREEN}This script uses Optuna to optimize the hyperparameters of:${NC}"
echo -e "  1. ${YELLOW}The neural network component${NC}"
echo -e "  2. ${YELLOW}The random forest component${NC}"
echo -e "  3. ${YELLOW}The ensemble weights${NC}"
echo -e ""
echo -e "${YELLOW}Output will be saved to:${NC} ${OUTPUT_DIR}"
echo ""

# Check if prerequisites are available
echo -e "${YELLOW}Checking prerequisites...${NC}"
if [ ! -f "./training_data/combined_features.tsv" ]; then
    echo -e "${RED}Error: Feature file not found. Please ensure './training_data/combined_features.tsv' exists.${NC}"
    exit 1
fi

if [ ! -f "./training_data/metadata.tsv" ]; then
    echo -e "${RED}Error: Metadata file not found. Please ensure './training_data/metadata.tsv' exists.${NC}"
    exit 1
fi

# Check if Optuna is installed
if ! python -c "import optuna" &> /dev/null; then
    echo -e "${YELLOW}Optuna not found. Installing...${NC}"
    pip install optuna
    
    if [ $? -ne 0 ]; then
        echo -e "${RED}Error: Failed to install Optuna. Please install it manually.${NC}"
        exit 1
    fi
fi

echo -e "${GREEN}All prerequisites found.${NC}"
echo ""

# Step 1: Filter the dataset to remove unreliable temperature data
echo -e "${YELLOW}Step 1: Filtering dataset to remove unreliable temperature data...${NC}"
python improved_temp_data_filter.py \
    --feature-file "./training_data/combined_features.tsv" \
    --metadata-file "./training_data/metadata.tsv" \
    --output-dir "$OUTPUT_DIR/filtered_data" \
    --reliability-threshold 0.6 \
    --prediction-error-threshold 2.0

# Check if filtering was successful
if [ $? -eq 0 ]; then
    echo -e "${GREEN}Dataset filtering completed. Using filtered datasets for training.${NC}"
    FEATURES_FILE="${OUTPUT_DIR}/filtered_data/filtered_features.tsv"
    METADATA_FILE="${OUTPUT_DIR}/filtered_data/filtered_metadata.tsv"
else
    echo -e "${RED}Error: Dataset filtering failed. Exiting.${NC}"
    exit 1
fi

# Step 2: Run hyperparameter tuning
echo -e "${YELLOW}Step 2: Running hyperparameter tuning with Optuna...${NC}"

# Set parameters for hyperparameter tuning
N_FEATURES=1000  # Number of features to use
N_TRIALS=50      # Number of optimization trials

# Run the hyperparameter tuning script
python tune_ensemble_temp_model.py \
    --feature_file "$FEATURES_FILE" \
    --metadata_file "$METADATA_FILE" \
    --output_dir "$OUTPUT_DIR/tuning_results" \
    --n_features $N_FEATURES \
    --n_trials $N_TRIALS

# Check if tuning was successful
if [ $? -eq 0 ]; then
    echo ""
    echo -e "${GREEN}${BOLD}========================================================${NC}"
    echo -e "${GREEN}${BOLD}   Hyperparameter tuning completed successfully!   ${NC}"
    echo -e "${GREEN}${BOLD}========================================================${NC}"
    echo ""
    echo -e "${GREEN}Results saved to:${NC} ${OUTPUT_DIR}/tuning_results"
    
    # Display best parameters and metrics
    if [ -f "${OUTPUT_DIR}/tuning_results/metrics.csv" ]; then
        echo -e "${YELLOW}Best Model Metrics:${NC}"
        cat "${OUTPUT_DIR}/tuning_results/metrics.csv" | column -t -s,
    fi
    
else
    echo ""
    echo -e "${RED}${BOLD}========================================================${NC}"
    echo -e "${RED}${BOLD}   Hyperparameter tuning failed!   ${NC}"
    echo -e "${RED}${BOLD}========================================================${NC}"
    echo ""
    echo -e "${RED}Please check the error messages above for details.${NC}"
fi

echo ""
echo -e "${GREEN}Done.${NC}"
