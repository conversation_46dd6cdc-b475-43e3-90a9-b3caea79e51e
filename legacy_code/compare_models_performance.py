#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Compare the performance of the original hybrid model and the improved hybrid model.
"""

import os
import argparse
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns

def load_metrics(metrics_file):
    """Load metrics from a CSV file."""
    if os.path.exists(metrics_file):
        return pd.read_csv(metrics_file)
    return None

def main():
    parser = argparse.ArgumentParser(description="Compare model performance")
    parser.add_argument("--original_metrics", default="models/hybrid/metrics.csv", help="Path to original model metrics")
    parser.add_argument("--improved_metrics", default="models/improved_hybrid/evaluation_metrics.csv", help="Path to improved model metrics")
    parser.add_argument("--output_dir", default="models/comparison", help="Directory to save comparison")
    args = parser.parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Load metrics
    original_metrics = load_metrics(args.original_metrics)
    improved_metrics = load_metrics(args.improved_metrics)
    
    if original_metrics is None or improved_metrics is None:
        print("Error: Could not load metrics files.")
        return
    
    # Create comparison DataFrame
    comparison = pd.DataFrame({
        'Metric': ['R2', 'RMSE', 'MAE'],
        'Original Growth Rate': [original_metrics['Growth Rate R2'].values[0], 
                                original_metrics['Growth Rate RMSE'].values[0], 
                                original_metrics['Growth Rate MAE'].values[0]],
        'Improved Growth Rate': [improved_metrics.loc[improved_metrics['Metric'] == 'R2', 'Growth Rate'].values[0],
                                improved_metrics.loc[improved_metrics['Metric'] == 'RMSE', 'Growth Rate'].values[0],
                                improved_metrics.loc[improved_metrics['Metric'] == 'MAE', 'Growth Rate'].values[0]],
        'Original Temperature': [original_metrics['Temperature R2'].values[0], 
                                original_metrics['Temperature RMSE'].values[0], 
                                original_metrics['Temperature MAE'].values[0]],
        'Improved Temperature': [improved_metrics.loc[improved_metrics['Metric'] == 'R2', 'Optimal Temperature'].values[0],
                                improved_metrics.loc[improved_metrics['Metric'] == 'RMSE', 'Optimal Temperature'].values[0],
                                improved_metrics.loc[improved_metrics['Metric'] == 'MAE', 'Optimal Temperature'].values[0]]
    })
    
    # Save comparison to CSV
    comparison.to_csv(os.path.join(args.output_dir, 'model_comparison.csv'), index=False)
    print(f"Comparison saved to {os.path.join(args.output_dir, 'model_comparison.csv')}")
    
    # Create bar plots for R2
    plt.figure(figsize=(12, 6))
    r2_data = comparison[comparison['Metric'] == 'R2']
    r2_data_melted = pd.melt(r2_data, id_vars=['Metric'], var_name='Model', value_name='R2')
    sns.barplot(x='Model', y='R2', data=r2_data_melted)
    plt.title('R² Comparison')
    plt.ylabel('R² Score')
    plt.ylim(-0.2, 1.0)  # Set y-axis limits to include negative values
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig(os.path.join(args.output_dir, 'r2_comparison.png'))
    
    # Create bar plots for RMSE
    plt.figure(figsize=(12, 6))
    rmse_data = comparison[comparison['Metric'] == 'RMSE']
    rmse_data_melted = pd.melt(rmse_data, id_vars=['Metric'], var_name='Model', value_name='RMSE')
    sns.barplot(x='Model', y='RMSE', data=rmse_data_melted)
    plt.title('RMSE Comparison')
    plt.ylabel('RMSE')
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig(os.path.join(args.output_dir, 'rmse_comparison.png'))
    
    print("Comparison plots saved to:")
    print(f"  {os.path.join(args.output_dir, 'r2_comparison.png')}")
    print(f"  {os.path.join(args.output_dir, 'rmse_comparison.png')}")

if __name__ == "__main__":
    main()
