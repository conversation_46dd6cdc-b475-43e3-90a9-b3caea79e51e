#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Train Hybrid Multi-Task Model for Growth Rate and Temperature Prediction.
This script trains a hybrid model that combines ensemble methods with a neural network
to predict both growth rate and optimal temperature simultaneously.
"""

import os
import argparse
import logging
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from sklearn.preprocessing import RobustScaler

from hybrid_multi_task_model import HybridMultiTaskModel

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_data(feature_file: str, metadata_file: str) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    Load feature and metadata files.
    
    Args:
        feature_file: Path to feature file
        metadata_file: Path to metadata file
        
    Returns:
        Tuple of (features, metadata)
    """
    logger.info(f"Loading features from {feature_file}")
    features = pd.read_csv(feature_file, sep='\t', index_col='genome_id')
    
    logger.info(f"Loading metadata from {metadata_file}")
    metadata = pd.read_csv(metadata_file, sep='\t', index_col='genome_id')
    
    # Get common samples
    common_indices = features.index.intersection(metadata.index)
    logger.info(f"Found {len(common_indices)} common samples")
    
    # Filter to common samples
    features = features.loc[common_indices]
    metadata = metadata.loc[common_indices]
    
    # Check for NaN values in features
    nan_count = features.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in features. Filling with 0.")
        features = features.fillna(0)
    
    # Check for infinite values
    inf_count = np.isinf(features.values).sum()
    if inf_count > 0:
        logger.warning(f"Found {inf_count} infinite values in features. Replacing with max/min values.")
        features = features.replace([np.inf, -np.inf], np.nan)
        features = features.fillna(features.max())
    
    # Check for NaN values in metadata
    nan_count = metadata.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in metadata. Filling with 0.")
        metadata = metadata.fillna(0)
    
    return features, metadata

def prepare_data(
    features: pd.DataFrame, 
    metadata: pd.DataFrame,
    n_features: int = 150, 
    output_dir: Optional[str] = None
) -> Tuple[pd.DataFrame, pd.Series, pd.Series, pd.DataFrame, pd.Series, pd.Series, pd.DataFrame, pd.Series, pd.Series, List[str], RobustScaler, RobustScaler]:
    """
    Prepare data for multi-task learning with proper train/validation/test split and target scaling.

    Args:
        features: Feature DataFrame
        metadata: Metadata DataFrame
        n_features: Number of features to select
        output_dir: Directory to save outputs

    Returns:
        Tuple of (X_train, y_train_growth, y_train_temp, X_val, y_val_growth, y_val_temp, 
                 X_test, y_test_growth, y_test_temp, selected_features, growth_scaler, temp_scaler)
    """
    logger.info("Preparing data for multi-task learning...")

    # Extract targets
    y_growth = metadata['growth_rate']
    y_temp = metadata['optimal_temperature']
    
    # Create bins for stratification based on both targets
    # We'll use growth rate for stratification as it's typically more challenging
    n_bins = 10
    y_binned = pd.qcut(y_growth, n_bins, labels=False, duplicates='drop')
    
    # Check if we have enough samples in each bin
    bin_counts = pd.Series(y_binned).value_counts()
    min_bin_count = bin_counts.min()
    
    if min_bin_count < 3:
        logger.warning(f"Some bins have very few samples (min: {min_bin_count}). Using fewer bins.")
        n_bins = max(2, min(5, min_bin_count))
        y_binned = pd.qcut(y_growth, n_bins, labels=False, duplicates='drop')
    
    # First split data into train+val and test sets with stratification
    X_train_val, X_test, y_growth_train_val, y_growth_test, y_temp_train_val, y_temp_test, y_binned_train_val, _ = train_test_split(
        features, y_growth, y_temp, y_binned, test_size=0.2, random_state=42, shuffle=True, stratify=y_binned
    )

    # Then split train+val into train and validation sets with stratification
    X_train, X_val, y_growth_train, y_growth_val, y_temp_train, y_temp_val = train_test_split(
        X_train_val, y_growth_train_val, y_temp_train_val, test_size=0.25, random_state=42, 
        shuffle=True, stratify=y_binned_train_val
    )

    logger.info(f"Data split: Train={X_train.shape[0]} samples, Validation={X_val.shape[0]} samples, Test={X_test.shape[0]} samples")
    
    # Check distribution of target variables in each split
    logger.info(f"Growth Rate - Train: mean={y_growth_train.mean():.4f}, std={y_growth_train.std():.4f}")
    logger.info(f"Growth Rate - Validation: mean={y_growth_val.mean():.4f}, std={y_growth_val.std():.4f}")
    logger.info(f"Growth Rate - Test: mean={y_growth_test.mean():.4f}, std={y_growth_test.std():.4f}")
    
    logger.info(f"Temperature - Train: mean={y_temp_train.mean():.4f}, std={y_temp_train.std():.4f}")
    logger.info(f"Temperature - Validation: mean={y_temp_val.mean():.4f}, std={y_temp_val.std():.4f}")
    logger.info(f"Temperature - Test: mean={y_temp_test.mean():.4f}, std={y_temp_test.std():.4f}")
    
    # Apply target scaling using RobustScaler for both targets
    growth_scaler = RobustScaler()
    temp_scaler = RobustScaler()
    
    # Reshape for scaler
    y_growth_train_reshaped = y_growth_train.values.reshape(-1, 1)
    y_growth_val_reshaped = y_growth_val.values.reshape(-1, 1)
    y_growth_test_reshaped = y_growth_test.values.reshape(-1, 1)
    
    y_temp_train_reshaped = y_temp_train.values.reshape(-1, 1)
    y_temp_val_reshaped = y_temp_val.values.reshape(-1, 1)
    y_temp_test_reshaped = y_temp_test.values.reshape(-1, 1)
    
    # Fit scalers on training data only
    growth_scaler.fit(y_growth_train_reshaped)
    temp_scaler.fit(y_temp_train_reshaped)
    
    # Transform all sets
    y_growth_train_scaled = growth_scaler.transform(y_growth_train_reshaped).flatten()
    y_growth_val_scaled = growth_scaler.transform(y_growth_val_reshaped).flatten()
    y_growth_test_scaled = growth_scaler.transform(y_growth_test_reshaped).flatten()
    
    y_temp_train_scaled = temp_scaler.transform(y_temp_train_reshaped).flatten()
    y_temp_val_scaled = temp_scaler.transform(y_temp_val_reshaped).flatten()
    y_temp_test_scaled = temp_scaler.transform(y_temp_test_reshaped).flatten()
    
    # Convert back to pandas Series with original indices
    y_growth_train = pd.Series(y_growth_train_scaled, index=y_growth_train.index)
    y_growth_val = pd.Series(y_growth_val_scaled, index=y_growth_val.index)
    y_growth_test = pd.Series(y_growth_test_scaled, index=y_growth_test.index)
    
    y_temp_train = pd.Series(y_temp_train_scaled, index=y_temp_train.index)
    y_temp_val = pd.Series(y_temp_val_scaled, index=y_temp_val.index)
    y_temp_test = pd.Series(y_temp_test_scaled, index=y_temp_test.index)
    
    logger.info("Applied target scaling using RobustScaler for both targets")
    
    # For simplicity, we'll use all features for now
    # In a real implementation, you would want to use feature selection here
    selected_features = features.columns.tolist()[:n_features]
    
    # Filter to selected features
    X_train = X_train[selected_features]
    X_val = X_val[selected_features]
    X_test = X_test[selected_features]

    logger.info(f"Prepared data for multi-task learning with {len(selected_features)} features")

    return (
        X_train, y_growth_train, y_temp_train, 
        X_val, y_growth_val, y_temp_val, 
        X_test, y_growth_test, y_temp_test, 
        selected_features, growth_scaler, temp_scaler
    )

def train_model(
    X_train: pd.DataFrame, 
    y_growth_train: pd.Series,
    y_temp_train: pd.Series,
    X_val: pd.DataFrame, 
    y_growth_val: pd.Series,
    y_temp_val: pd.Series,
    nn_hidden_dim: int = 128,
    nn_dropout: float = 0.3,
    nn_growth_weight: float = 0.5,
    output_dir: Optional[str] = None
) -> HybridMultiTaskModel:
    """
    Train hybrid multi-task model.
    
    Args:
        X_train: Training features
        y_growth_train: Training growth rate targets
        y_temp_train: Training temperature targets
        X_val: Validation features
        y_growth_val: Validation growth rate targets
        y_temp_val: Validation temperature targets
        nn_hidden_dim: Size of hidden layers in neural network
        nn_dropout: Dropout rate for neural network
        nn_growth_weight: Weight for growth rate loss in neural network
        output_dir: Directory to save outputs
        
    Returns:
        Trained model
    """
    logger.info("Training hybrid multi-task model...")
    
    # Initialize model
    model = HybridMultiTaskModel(
        nn_hidden_dim=nn_hidden_dim,
        nn_dropout=nn_dropout,
        nn_lr=0.001,
        nn_batch_size=64,
        nn_epochs=100,
        nn_patience=10,
        nn_growth_weight=nn_growth_weight,
        ensemble_weight_lr=0.01,
        ensemble_weight_epochs=50,
        variance_percentile=25
    )
    
    # Train model
    model.fit(
        X_train, 
        y_growth_train, 
        y_temp_train,
        X_val, 
        y_growth_val, 
        y_temp_val,
        output_dir=output_dir
    )
    
    return model

def evaluate_model(
    model: HybridMultiTaskModel, 
    X_test: pd.DataFrame, 
    y_growth_test: pd.Series,
    y_temp_test: pd.Series,
    growth_scaler: RobustScaler,
    temp_scaler: RobustScaler,
    output_dir: Optional[str] = None
) -> Dict[str, Dict[str, float]]:
    """
    Evaluate model on test data.
    
    Args:
        model: Trained model
        X_test: Test features
        y_growth_test: Test growth rate targets
        y_temp_test: Test temperature targets
        growth_scaler: Scaler for growth rate
        temp_scaler: Scaler for temperature
        output_dir: Directory to save outputs
        
    Returns:
        Dictionary of evaluation metrics for each task
    """
    logger.info("Evaluating model on test data...")
    
    # Evaluate model
    metrics = model.evaluate(X_test, y_growth_test, y_temp_test)
    
    # Convert metrics to original scale
    growth_rmse_orig = metrics['growth_rate']['RMSE'] * growth_scaler.scale_[0]
    growth_mae_orig = metrics['growth_rate']['MAE'] * growth_scaler.scale_[0]
    
    temp_rmse_orig = metrics['temperature']['RMSE'] * temp_scaler.scale_[0]
    temp_mae_orig = metrics['temperature']['MAE'] * temp_scaler.scale_[0]
    
    logger.info(f"Growth Rate (original scale) - RMSE: {growth_rmse_orig:.4f}, MAE: {growth_mae_orig:.4f}")
    logger.info(f"Temperature (original scale) - RMSE: {temp_rmse_orig:.4f}, MAE: {temp_mae_orig:.4f}")
    
    # Plot predictions vs actual values
    if output_dir:
        # Generate predictions
        y_growth_pred, y_temp_pred = model.predict(X_test)
        
        # Growth rate plot
        plt.figure(figsize=(10, 6))
        plt.scatter(y_growth_test, y_growth_pred, alpha=0.5)
        plt.plot([y_growth_test.min(), y_growth_test.max()], [y_growth_test.min(), y_growth_test.max()], 'r--')
        plt.xlabel('Actual Growth Rate (Scaled)')
        plt.ylabel('Predicted Growth Rate (Scaled)')
        plt.title(f'Growth Rate Predictions (R² = {metrics["growth_rate"]["R2"]:.4f}, RMSE = {metrics["growth_rate"]["RMSE"]:.4f})')
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'growth_rate_predictions.png'))
        
        # Temperature plot
        plt.figure(figsize=(10, 6))
        plt.scatter(y_temp_test, y_temp_pred, alpha=0.5)
        plt.plot([y_temp_test.min(), y_temp_test.max()], [y_temp_test.min(), y_temp_test.max()], 'r--')
        plt.xlabel('Actual Temperature (Scaled)')
        plt.ylabel('Predicted Temperature (Scaled)')
        plt.title(f'Temperature Predictions (R² = {metrics["temperature"]["R2"]:.4f}, RMSE = {metrics["temperature"]["RMSE"]:.4f})')
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'temperature_predictions.png'))
    
    return metrics

def main():
    parser = argparse.ArgumentParser(description="Train and evaluate hybrid multi-task model")
    parser.add_argument("--feature_file", required=True, help="Path to feature file")
    parser.add_argument("--metadata_file", required=True, help="Path to metadata file")
    parser.add_argument("--output_dir", default="models/hybrid_multi_task", help="Directory to save outputs")
    parser.add_argument("--n_features", type=int, default=150, help="Number of features to select")
    parser.add_argument("--nn_hidden_dim", type=int, default=128, help="Size of hidden layers in neural network")
    parser.add_argument("--nn_dropout", type=float, default=0.3, help="Dropout rate for neural network")
    parser.add_argument("--nn_growth_weight", type=float, default=0.5, help="Weight for growth rate loss in neural network")
    args = parser.parse_args()

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Load data
    features, metadata = load_data(args.feature_file, args.metadata_file)

    # Prepare data with proper train/validation/test split and target scaling
    (
        X_train, y_growth_train, y_temp_train, 
        X_val, y_growth_val, y_temp_val, 
        X_test, y_growth_test, y_temp_test, 
        selected_features, growth_scaler, temp_scaler
    ) = prepare_data(
        features, metadata, args.n_features, args.output_dir
    )

    # Train model
    model = train_model(
        X_train, y_growth_train, y_temp_train,
        X_val, y_growth_val, y_temp_val,
        nn_hidden_dim=args.nn_hidden_dim,
        nn_dropout=args.nn_dropout,
        nn_growth_weight=args.nn_growth_weight,
        output_dir=args.output_dir
    )

    # Evaluate model on the test set
    metrics = evaluate_model(
        model, 
        X_test, 
        y_growth_test, 
        y_temp_test,
        growth_scaler,
        temp_scaler,
        args.output_dir
    )

    # Save the scalers for later use
    if args.output_dir:
        import joblib
        joblib.dump(growth_scaler, os.path.join(args.output_dir, 'growth_scaler.joblib'))
        joblib.dump(temp_scaler, os.path.join(args.output_dir, 'temp_scaler.joblib'))
        logger.info(f"Saved scalers to {args.output_dir}")

    # Print final metrics
    logger.info("Hybrid multi-task model training and evaluation completed")
    logger.info(f"Final metrics - Overall R²: {metrics['overall']['R2']:.4f}")
    logger.info(f"Final metrics - Growth Rate R²: {metrics['growth_rate']['R2']:.4f}")
    logger.info(f"Final metrics - Temperature R²: {metrics['temperature']['R2']:.4f}")

if __name__ == "__main__":
    main()
