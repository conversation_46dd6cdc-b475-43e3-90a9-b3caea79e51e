#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Compare specialized hybrid models with joint hybrid tabular model.

This script loads both model types and compares their performance on the test set.
"""

import os
import argparse
import logging
import numpy as np
import pandas as pd
import torch
import joblib
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.model_selection import train_test_split
import matplotlib.pyplot as plt

from hybrid_tabular_model import load_data, SimpleTabularModel, HybridRFTabular
from specialized_hybrid_models import GrowthRateModel, TemperatureModel, SpecializedHybridModel

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_joint_model(model_dir, device):
    """Load the joint hybrid tabular model"""
    logger.info(f"Loading joint model from {model_dir}...")
    
    # Load components
    components = joblib.load(os.path.join(model_dir, 'final_model.joblib'))
    
    # Extract components
    feature_scaler = components['feature_scaler']
    target_scaler = components['target_scaler']
    rf_growth = components['rf_growth']
    rf_temp = components['rf_temp']
    ensemble_weights = components['ensemble_weights']
    categorical_cols = components['categorical_cols']
    continuous_cols = components['continuous_cols']
    
    # Get categories from metadata file if not found in features
    combined_features_df = pd.read_csv('./training_data/combined_features.tsv', sep='\t')
    metadata_df = pd.read_csv('./training_data/metadata.tsv', sep='\t')
    
    # Create a combined dataframe for categorical features
    logger.info(f"Preparing categorical features...")
    for col in categorical_cols:
        if col not in combined_features_df.columns and col in metadata_df.columns:
            logger.info(f"Adding {col} from metadata to features")
            combined_features_df[col] = metadata_df[col]
            
    # Now get categories
    categories = []
    for col in categorical_cols:
        try:
            unique_vals = len(np.unique(combined_features_df[col]))
            categories.append(unique_vals)
        except KeyError:
            logger.warning(f"Column {col} not found in features or metadata. Using default value.")
            categories.append(2)  # Default fallback
    
    tabular_model = SimpleTabularModel(
        categories=categories,
        num_continuous=len(continuous_cols),
        dim=128,
        dim_out=2
    ).to(device)
    
    # Create hybrid model
    model = HybridRFTabular(
        tabular_model=tabular_model,
        categorical_cols=categorical_cols,
        continuous_cols=continuous_cols
    ).to(device)
    
    # Load model weights
    model.tabular_model.load_state_dict(components['nn_state_dict'])
    model.rf_growth = rf_growth
    model.rf_temp = rf_temp
    
    # Set ensemble weights
    with torch.no_grad():
        model.ensemble_weights.copy_(torch.tensor(ensemble_weights, dtype=torch.float32).to(device))
    
    return model, feature_scaler, target_scaler, categorical_cols, continuous_cols

def load_specialized_models(growth_dir, temp_dir, device):
    """Load specialized models for growth rate and temperature"""
    logger.info(f"Loading specialized models from {growth_dir} and {temp_dir}...")
    
    # Load growth rate model components
    growth_components = joblib.load(os.path.join(growth_dir, 'model_components.joblib'))
    growth_feature_scaler = growth_components['feature_scaler']
    growth_target_scaler = growth_components['target_scaler']
    growth_rf_model = growth_components['rf_model']
    growth_ensemble_weight = growth_components['ensemble_weight']
    categorical_cols = growth_components['categorical_cols']
    growth_continuous_cols = growth_components['continuous_cols']
    growth_selected_features = growth_components['selected_features']
    
    # Load temperature model components
    temp_components = joblib.load(os.path.join(temp_dir, 'model_components.joblib'))
    temp_feature_scaler = temp_components['feature_scaler']
    temp_target_scaler = temp_components['target_scaler']
    temp_rf_model = temp_components['rf_model']
    temp_ensemble_weight = temp_components['ensemble_weight']
    temp_continuous_cols = temp_components['continuous_cols']
    temp_selected_features = temp_components['selected_features']
    
    # Get categories from metadata file if not found in features
    combined_features_df = pd.read_csv('./training_data/combined_features.tsv', sep='\t')
    metadata_df = pd.read_csv('./training_data/metadata.tsv', sep='\t')
    
    # Create a combined dataframe for categorical features
    logger.info(f"Preparing categorical features for specialized models...")
    for col in categorical_cols:
        if col not in combined_features_df.columns and col in metadata_df.columns:
            logger.info(f"Adding {col} from metadata to features")
            combined_features_df[col] = metadata_df[col]
            
    # Now get categories
    growth_categories = []
    for col in categorical_cols:
        try:
            unique_vals = len(np.unique(combined_features_df[col]))
            growth_categories.append(unique_vals)
        except KeyError:
            logger.warning(f"Column {col} not found in features or metadata. Using default value.")
            growth_categories.append(2)  # Default fallback
            
    temp_categories = growth_categories  # Same categories for both models
    
    # Create neural network models
    growth_nn = GrowthRateModel(
        categories=growth_categories,
        num_continuous=len(growth_continuous_cols),
        dim=128,
        dropout=0.3
    ).to(device)
    
    temp_nn = TemperatureModel(
        categories=temp_categories,
        num_continuous=len(temp_continuous_cols),
        dim=128,
        dropout=0.3
    ).to(device)
    
    # Load neural network weights
    growth_nn.load_state_dict(torch.load(os.path.join(growth_dir, 'nn_model.pth'), map_location=device))
    temp_nn.load_state_dict(torch.load(os.path.join(temp_dir, 'nn_model.pth'), map_location=device))
    
    # Create specialized hybrid models
    growth_model = SpecializedHybridModel(
        nn_model=growth_nn,
        categorical_cols=categorical_cols,
        continuous_cols=growth_continuous_cols,
        target_name="Growth Rate"
    )
    
    temp_model = SpecializedHybridModel(
        nn_model=temp_nn,
        categorical_cols=categorical_cols,
        continuous_cols=temp_continuous_cols,
        target_name="Optimal Temperature"
    )
    
    # Set RF models and ensemble weights
    growth_model.rf_model = growth_rf_model
    growth_model.ensemble_weight = growth_ensemble_weight
    
    temp_model.rf_model = temp_rf_model
    temp_model.ensemble_weight = temp_ensemble_weight
    
    return (
        growth_model, temp_model, 
        growth_feature_scaler, temp_feature_scaler,
        growth_target_scaler, temp_target_scaler,
        growth_selected_features, temp_selected_features,
        categorical_cols
    )

def prepare_test_data(features_df, metadata_df):
    """Prepare test data for model evaluation"""
    logger.info("Preparing test data...")
    
    # Ensure categorical columns are available
    categorical_cols = ['kingdom', 'codon_table']
    for col in categorical_cols:
        if col not in features_df.columns:
            if col in metadata_df.columns:
                logger.info(f"Adding missing categorical column {col} from metadata")
                features_df[col] = metadata_df[col]
            else:
                logger.warning(f"Categorical column {col} not found in features or metadata. Using placeholder.")
                features_df[col] = 0  # Default placeholder
    
    # Split data with fallback for stratification
    try:
        X_train, X_temp, y_train, y_temp = train_test_split(
            features_df, metadata_df[['growth_rate', 'optimal_temperature']], 
            test_size=0.3, random_state=42, 
            stratify=features_df['kingdom'] if 'kingdom' in features_df.columns and len(features_df['kingdom'].unique()) > 1 else None
        )
    except ValueError as e:
        logger.warning(f"Error in stratified splitting: {e}. Falling back to random split.")
        X_train, X_temp, y_train, y_temp = train_test_split(
            features_df, metadata_df[['growth_rate', 'optimal_temperature']], 
            test_size=0.3, random_state=42
        )
    
    X_val, X_test, y_val, y_test = train_test_split(
        X_temp, y_temp, test_size=0.5, random_state=42
    )
    
    # Convert categorical columns to integer codes
    for col in categorical_cols:
        # Get all unique values
        all_categories = pd.concat([X_train[col], X_val[col], X_test[col]]).unique()
        # Create mapping
        category_map = {cat: i for i, cat in enumerate(all_categories)}
        # Apply mapping
        X_train[col] = X_train[col].map(category_map)
        X_val[col] = X_val[col].map(category_map)
        X_test[col] = X_test[col].map(category_map)
    
    return X_test, y_test, categorical_cols

def main():
    parser = argparse.ArgumentParser(description='Compare Specialized vs Joint Models')
    parser.add_argument('--feature_file', type=str, default='./training_data/combined_features.tsv',
                        help='Path to feature file')
    parser.add_argument('--metadata_file', type=str, default='./training_data/metadata.tsv',
                        help='Path to metadata file')
    parser.add_argument('--joint_model_dir', type=str, default='./models/optimized_tabular',
                        help='Directory containing joint model')
    parser.add_argument('--specialized_model_dir', type=str, default='./models/specialized_hybrid',
                        help='Directory containing specialized models')
    parser.add_argument('--output_dir', type=str, default='./comparison_results',
                        help='Directory to save comparison results')
    args = parser.parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Initialize device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"Using device: {device}")
    
    # Load data
    features_df, metadata_df = load_data(args.feature_file, args.metadata_file)
    
    # Prepare test data
    X_test, y_test, categorical_cols = prepare_test_data(features_df, metadata_df)
    
    # Load joint model
    joint_model, joint_feature_scaler, joint_target_scaler, joint_categorical_cols, joint_continuous_cols = load_joint_model(
        args.joint_model_dir, device
    )
    
    # Load specialized models
    growth_model, temp_model, growth_feature_scaler, temp_feature_scaler, growth_target_scaler, temp_target_scaler, \
    growth_selected_features, temp_selected_features, specialized_categorical_cols = load_specialized_models(
        os.path.join(args.specialized_model_dir, 'growth_rate'),
        os.path.join(args.specialized_model_dir, 'optimal_temperature'),
        device
    )
    
    # Process test data for joint model
    X_test_joint = X_test.copy()
    X_test_joint[joint_continuous_cols] = joint_feature_scaler.transform(X_test_joint[joint_continuous_cols])
    
    # Process test data for specialized models
    X_test_growth = X_test.copy()
    X_test_growth[growth_selected_features] = growth_feature_scaler.transform(X_test_growth[growth_selected_features])
    
    X_test_temp = X_test.copy()
    X_test_temp[temp_selected_features] = temp_feature_scaler.transform(X_test_temp[temp_selected_features])
    
    # Make predictions with joint model
    joint_predictions = joint_model.predict(X_test_joint, joint_target_scaler)
    
    # Make predictions with specialized models
    growth_predictions = growth_model.predict(
        X_test_growth, growth_target_scaler, device
    )
    
    temp_predictions = temp_model.predict(
        X_test_temp, temp_target_scaler, device
    )
    
    # Combine specialized predictions
    specialized_predictions = np.column_stack([growth_predictions, temp_predictions])
    
    # Calculate metrics for joint model
    joint_rmse = np.sqrt(mean_squared_error(y_test, joint_predictions))
    joint_r2 = r2_score(y_test, joint_predictions)
    
    joint_growth_rmse = np.sqrt(mean_squared_error(y_test['growth_rate'], joint_predictions[:, 0]))
    joint_growth_r2 = r2_score(y_test['growth_rate'], joint_predictions[:, 0])
    
    joint_temp_rmse = np.sqrt(mean_squared_error(y_test['optimal_temperature'], joint_predictions[:, 1]))
    joint_temp_r2 = r2_score(y_test['optimal_temperature'], joint_predictions[:, 1])
    
    # Calculate metrics for specialized models
    specialized_rmse = np.sqrt(mean_squared_error(y_test, specialized_predictions))
    specialized_r2 = r2_score(y_test, specialized_predictions)
    
    specialized_growth_rmse = np.sqrt(mean_squared_error(y_test['growth_rate'], specialized_predictions[:, 0]))
    specialized_growth_r2 = r2_score(y_test['growth_rate'], specialized_predictions[:, 0])
    
    specialized_temp_rmse = np.sqrt(mean_squared_error(y_test['optimal_temperature'], specialized_predictions[:, 1]))
    specialized_temp_r2 = r2_score(y_test['optimal_temperature'], specialized_predictions[:, 1])
    
    # Log results
    logger.info("\n======== MODEL COMPARISON RESULTS ========")
    logger.info(f"Joint Model Overall - RMSE: {joint_rmse:.4f}, R²: {joint_r2:.4f}")
    logger.info(f"Joint Model Growth Rate - RMSE: {joint_growth_rmse:.4f}, R²: {joint_growth_r2:.4f}")
    logger.info(f"Joint Model Optimal Temperature - RMSE: {joint_temp_rmse:.4f}, R²: {joint_temp_r2:.4f}")
    
    logger.info(f"Specialized Models Overall - RMSE: {specialized_rmse:.4f}, R²: {specialized_r2:.4f}")
    logger.info(f"Specialized Model Growth Rate - RMSE: {specialized_growth_rmse:.4f}, R²: {specialized_growth_r2:.4f}")
    logger.info(f"Specialized Model Optimal Temperature - RMSE: {specialized_temp_rmse:.4f}, R²: {specialized_temp_r2:.4f}")
    
    # Calculate improvement percentages
    growth_improvement = (specialized_growth_r2 - joint_growth_r2) / joint_growth_r2 * 100
    temp_improvement = (specialized_temp_r2 - joint_temp_r2) / joint_temp_r2 * 100
    overall_improvement = (specialized_r2 - joint_r2) / joint_r2 * 100
    
    logger.info(f"Growth Rate Improvement: {growth_improvement:.2f}%")
    logger.info(f"Optimal Temperature Improvement: {temp_improvement:.2f}%")
    logger.info(f"Overall Improvement: {overall_improvement:.2f}%")
    
    # Plot comparison
    plt.figure(figsize=(15, 12))
    
    # Growth rate predictions
    plt.subplot(2, 2, 1)
    plt.scatter(y_test['growth_rate'], joint_predictions[:, 0], alpha=0.5, label='Joint Model')
    plt.scatter(y_test['growth_rate'], specialized_predictions[:, 0], alpha=0.5, label='Specialized Model')
    plt.plot([min(y_test['growth_rate']), max(y_test['growth_rate'])], 
             [min(y_test['growth_rate']), max(y_test['growth_rate'])], 'r--')
    plt.xlabel('Actual Growth Rate')
    plt.ylabel('Predicted Growth Rate')
    plt.title('Growth Rate Predictions Comparison')
    plt.legend()
    
    plt.subplot(2, 2, 2)
    plt.scatter(y_test['optimal_temperature'], joint_predictions[:, 1], alpha=0.5, label='Joint Model')
    plt.scatter(y_test['optimal_temperature'], specialized_predictions[:, 1], alpha=0.5, label='Specialized Model')
    plt.plot([min(y_test['optimal_temperature']), max(y_test['optimal_temperature'])], 
             [min(y_test['optimal_temperature']), max(y_test['optimal_temperature'])], 'r--')
    plt.xlabel('Actual Optimal Temperature')
    plt.ylabel('Predicted Optimal Temperature')
    plt.title('Optimal Temperature Predictions Comparison')
    plt.legend()
    
    # R² comparison
    plt.subplot(2, 2, 3)
    models = ['Joint', 'Specialized']
    growth_r2 = [joint_growth_r2, specialized_growth_r2]
    temp_r2 = [joint_temp_r2, specialized_temp_r2]
    overall_r2 = [joint_r2, specialized_r2]
    
    x = np.arange(len(models))
    width = 0.25
    
    plt.bar(x - width, growth_r2, width, label='Growth Rate')
    plt.bar(x, temp_r2, width, label='Optimal Temperature')
    plt.bar(x + width, overall_r2, width, label='Overall')
    
    plt.ylabel('R² Score')
    plt.title('R² Comparison')
    plt.xticks(x, models)
    plt.legend()
    
    # RMSE comparison
    plt.subplot(2, 2, 4)
    growth_rmse = [joint_growth_rmse, specialized_growth_rmse]
    temp_rmse = [joint_temp_rmse, specialized_temp_rmse]
    overall_rmse = [joint_rmse, specialized_rmse]
    
    plt.bar(x - width, growth_rmse, width, label='Growth Rate')
    plt.bar(x, temp_rmse, width, label='Optimal Temperature')
    plt.bar(x + width, overall_rmse, width, label='Overall')
    
    plt.ylabel('RMSE')
    plt.title('RMSE Comparison')
    plt.xticks(x, models)
    plt.legend()
    
    plt.tight_layout()
    plt.savefig(os.path.join(args.output_dir, 'model_comparison.png'))
    plt.close()
    
    # Save metrics to file
    with open(os.path.join(args.output_dir, 'comparison_metrics.txt'), 'w') as f:
        f.write("Joint Model Metrics:\n")
        f.write(f"Overall RMSE: {joint_rmse:.4f}\n")
        f.write(f"Overall R²: {joint_r2:.4f}\n")
        f.write(f"Growth Rate RMSE: {joint_growth_rmse:.4f}\n")
        f.write(f"Growth Rate R²: {joint_growth_r2:.4f}\n")
        f.write(f"Optimal Temperature RMSE: {joint_temp_rmse:.4f}\n")
        f.write(f"Optimal Temperature R²: {joint_temp_r2:.4f}\n\n")
        
        f.write("Specialized Models Metrics:\n")
        f.write(f"Overall RMSE: {specialized_rmse:.4f}\n")
        f.write(f"Overall R²: {specialized_r2:.4f}\n")
        f.write(f"Growth Rate RMSE: {specialized_growth_rmse:.4f}\n")
        f.write(f"Growth Rate R²: {specialized_growth_r2:.4f}\n")
        f.write(f"Optimal Temperature RMSE: {specialized_temp_rmse:.4f}\n")
        f.write(f"Optimal Temperature R²: {specialized_temp_r2:.4f}\n\n")
        
        f.write("Improvement Analysis:\n")
        f.write(f"Growth Rate R² Improvement: {growth_improvement:.2f}%\n")
        f.write(f"Optimal Temperature R² Improvement: {temp_improvement:.2f}%\n")
        f.write(f"Overall R² Improvement: {overall_improvement:.2f}%\n")
    
    logger.info(f"Comparison results saved to {args.output_dir}")

if __name__ == '__main__':
    main() 