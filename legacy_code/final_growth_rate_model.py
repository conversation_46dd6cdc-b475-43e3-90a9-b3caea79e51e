#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Final growth rate prediction model with advanced regularization and feature engineering.
This model combines multiple techniques to achieve high R² values while preventing overfitting.
"""

import os
import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Tuple, Union, Optional
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor, VotingRegressor, StackingRegressor
from sklearn.linear_model import Ridge, Lasso, ElasticNet, HuberRegressor
from sklearn.svm import SVR
from sklearn.model_selection import KFold, cross_val_score, train_test_split, GridSearchCV
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from sklearn.preprocessing import StandardScaler, RobustScaler, PolynomialFeatures
from sklearn.pipeline import Pipeline
from sklearn.feature_selection import SelectFromModel, RFE, RFECV
import matplotlib.pyplot as plt
import seaborn as sns
import joblib
import xgboost as xgb
import lightgbm as lgb
import shap
from skopt import BayesSearchCV
from skopt.space import Real, Integer, Categorical

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FinalGrowthRateModel:
    """
    Final growth rate prediction model with advanced regularization and feature engineering.
    """

    def __init__(self):
        """
        Initialize the model with improved components for feature interactions and enhanced regularization.
        """
        # Initialize scaler with robust scaling to handle outliers better
        self.scaler = RobustScaler()

        # Initialize feature engineering components with reduced complexity
        # Only use first-order interactions to reduce overfitting
        self.poly_features = PolynomialFeatures(degree=2, interaction_only=True, include_bias=False)

        # Initialize interaction-specific components
        self.interaction_scaler = None
        self.interaction_features = None
        self.original_feature_indices = None
        self.final_feature_list = None
        self.y_train = None  # For correlation-based feature selection

        # For early stopping and model selection
        self.best_val_score = -np.inf
        self.patience = 10
        self.patience_counter = 0
        self.best_models = {}

        # Initialize base models with balanced regularization
        # Random Forest with moderate regularization
        self.rf_model = RandomForestRegressor(
            n_estimators=500,
            max_depth=10,
            min_samples_split=10,
            min_samples_leaf=5,
            max_features='sqrt',
            bootstrap=True,
            oob_score=True,
            n_jobs=-1,
            random_state=42
        )

        # XGBoost with stronger regularization
        self.xgb_model = xgb.XGBRegressor(
            n_estimators=500,
            learning_rate=0.01,
            max_depth=5,
            subsample=0.7,
            colsample_bytree=0.7,
            reg_alpha=1.0,
            reg_lambda=2.0,
            gamma=0.1,  # Minimum loss reduction for split
            min_child_weight=3,  # Minimum sum of instance weight in child
            random_state=42
        )

        # LightGBM with balanced regularization
        self.lgb_model = lgb.LGBMRegressor(
            n_estimators=500,
            learning_rate=0.01,
            num_leaves=31,
            max_depth=5,
            subsample=0.7,
            colsample_bytree=0.7,
            reg_alpha=1.0,
            reg_lambda=2.0,
            min_child_samples=20,
            min_child_weight=0.001,
            random_state=42,
            n_jobs=-1
        )

        # Gradient Boosting with moderate regularization
        self.gb_model = GradientBoostingRegressor(
            n_estimators=300,
            learning_rate=0.01,
            max_depth=5,
            subsample=0.7,
            min_samples_split=10,
            min_samples_leaf=5,
            random_state=42
        )

        # Initialize linear models with different regularization strengths
        # Ridge regression with moderate regularization
        self.ridge_model = Ridge(alpha=1.0, random_state=42)

        # Lasso regression with stronger feature selection
        self.lasso_model = Lasso(alpha=0.1, random_state=42)

        # ElasticNet with balanced L1/L2 regularization
        self.elastic_model = ElasticNet(alpha=0.1, l1_ratio=0.5, random_state=42)

        # Huber regression for robustness to outliers
        self.huber_model = HuberRegressor(epsilon=1.35, alpha=0.0001, max_iter=3000)

        # SVR for non-linear patterns
        self.svr_model = SVR(C=1.0, epsilon=0.1, kernel='rbf')

        # Initialize ensemble model
        self.ensemble_model = None

        # Initialize stacking model
        self.stacking_model = None

        # For storing feature importances
        self.feature_importances = {}

        # For storing evaluation metrics
        self.metrics = {}

        # For storing feature names
        self.feature_names = None

        # For storing interaction feature names
        self.interaction_feature_names = None

        # For storing SHAP values
        self.shap_values = None

        # For storing feature selector
        self.feature_selector = None

        # For cross-validation
        self.cv = KFold(n_splits=5, shuffle=True, random_state=42)

    def _create_ensemble_model(self) -> Union[VotingRegressor, StackingRegressor]:
        """
        Create an ensemble model with base models and meta-models.
        Uses stacking with cross-validation for better generalization.

        Returns:
            Union[VotingRegressor, StackingRegressor]
        """
        # Define base models
        base_models = [
            ('rf', self.rf_model),
            ('xgb', self.xgb_model),
            ('lgb', self.lgb_model),
            ('gb', self.gb_model),
            ('ridge', self.ridge_model),
            ('lasso', self.lasso_model),
            ('elastic', self.elastic_model),
            ('huber', self.huber_model),
            ('svr', self.svr_model)
        ]

        # Create a stacking regressor with cross-validation
        # Use a meta-model that can handle the predictions from base models
        meta_model = Ridge(alpha=1.0, random_state=42)

        # Create stacking regressor with cross-validation
        stacking_model = StackingRegressor(
            estimators=base_models,
            final_estimator=meta_model,
            cv=self.cv,  # Use cross-validation for better generalization
            n_jobs=-1
        )

        # Also create a voting regressor as a backup
        voting_model = VotingRegressor(
            estimators=base_models,
            weights=[0.15, 0.15, 0.15, 0.1, 0.1, 0.1, 0.1, 0.05, 0.1]  # Adjusted weights
        )

        # Store both models for potential ensemble of ensembles
        self.stacking_model = stacking_model

        # Return the stacking model as the primary ensemble
        return stacking_model

    def _select_features(self, X: pd.DataFrame, y: pd.Series, n_features: int = 100) -> List[str]:
        """
        Select features using multiple methods and ensemble.

        Args:
            X: Features
            y: Target
            n_features: Number of features to select

        Returns:
            List of selected feature names
        """
        logger.info(f"Selecting top {n_features} features...")

        # Train a Random Forest model for feature importance
        rf = RandomForestRegressor(
            n_estimators=200,
            max_depth=15,
            min_samples_split=5,
            min_samples_leaf=2,
            max_features='sqrt',
            n_jobs=-1,
            random_state=42
        )
        rf.fit(X, y)

        # Get feature importance scores
        importance_scores = pd.Series(
            rf.feature_importances_,
            index=X.columns
        ).sort_values(ascending=False)

        # Select top n_features
        selected_features = importance_scores.head(n_features).index.tolist()

        # Create feature selector
        self.feature_selector = SelectFromModel(
            rf,
            threshold=-np.inf,  # Keep all features
            max_features=n_features,
            prefit=True
        )

        logger.info(f"Selected {len(selected_features)} features")

        return selected_features

    def _create_interaction_features(self, X: pd.DataFrame, is_training: bool = False) -> pd.DataFrame:
        """
        Create interaction features with improved stability and feature selection.
        Simplified to reduce overfitting and ensure consistent preprocessing.

        Args:
            X: Features
            is_training: Whether this is being called during training (to fit the transformer)

        Returns:
            DataFrame with original and interaction features
        """
        logger.info("Creating interaction features...")

        # Ensure X has the same columns in the same order as during training
        if not is_training and hasattr(self, 'feature_names') and self.feature_names is not None:
            # Reindex to match the order of features during training
            # Fill missing columns with 0
            X = X.reindex(columns=self.feature_names, fill_value=0)

        # Step 1: Select a smaller number of important features for interactions to reduce overfitting
        if is_training:
            # If we're in training mode, select features based on importance
            if hasattr(self, 'feature_importances') and 'rf' in self.feature_importances:
                # Use top features from Random Forest importance
                importance_scores = self.feature_importances['rf']
                # Select top 10 features to limit complexity (reduced from 20)
                top_features = importance_scores.head(10).index.tolist()
                logger.info(f"Using top 10 features from RF importance for interactions")
            else:
                # If no feature importances available yet, use correlation with target
                if hasattr(self, 'y_train') and self.y_train is not None:
                    # Calculate correlation with target
                    correlations = X.corrwith(self.y_train).abs().sort_values(ascending=False)
                    top_features = correlations.head(10).index.tolist()
                    logger.info(f"Using top 10 features by correlation for interactions")
                else:
                    # If no target available, use variance
                    variances = X.var().sort_values(ascending=False)
                    top_features = variances.head(10).index.tolist()
                    logger.info(f"Using top 10 features by variance for interactions")

            # Store selected features for prediction time
            self.interaction_features = top_features
            X_for_interactions = X[top_features]
        else:
            # In prediction mode, use the features selected during training
            if hasattr(self, 'interaction_features') and self.interaction_features is not None:
                # Use the features selected during training
                interaction_features = [f for f in self.interaction_features if f in X.columns]
                if len(interaction_features) < len(self.interaction_features):
                    logger.warning(f"Some interaction features are missing: expected {len(self.interaction_features)}, got {len(interaction_features)}")
                X_for_interactions = X[interaction_features]
            else:
                logger.warning("No interaction features defined. Using original features.")
                X_for_interactions = X

        # Step 2: Standardize features before creating interactions to prevent numerical issues
        if is_training:
            # Fit a scaler specifically for interaction features
            self.interaction_scaler = RobustScaler()  # Changed to RobustScaler for better handling of outliers
            X_for_interactions_scaled = self.interaction_scaler.fit_transform(X_for_interactions)
        else:
            # Use the scaler fitted during training
            if hasattr(self, 'interaction_scaler') and self.interaction_scaler is not None:
                X_for_interactions_scaled = self.interaction_scaler.transform(X_for_interactions)
            else:
                logger.warning("Interaction scaler not found. Using unscaled features.")
                X_for_interactions_scaled = X_for_interactions.values

        # Step 3: Create polynomial features with more conservative settings
        if is_training:
            # Initialize polynomial features transformer with conservative settings
            self.poly_features = PolynomialFeatures(
                degree=2,                # Only use 2nd degree interactions
                interaction_only=True,   # Only use interaction terms, no squared terms
                include_bias=False       # Don't include bias term
            )

            # Fit and transform
            X_poly = self.poly_features.fit_transform(X_for_interactions_scaled)

            # Store feature names for later use
            try:
                self.interaction_feature_names = self.poly_features.get_feature_names_out(X_for_interactions.columns)
            except AttributeError:
                # For older scikit-learn versions
                self.interaction_feature_names = self.poly_features.get_feature_names(X_for_interactions.columns)

            # Store original feature indices for filtering
            self.original_feature_indices = list(range(len(X_for_interactions.columns)))
        else:
            # During prediction, only transform
            if not hasattr(self, 'poly_features') or not hasattr(self.poly_features, 'n_output_features_'):
                logger.warning("Polynomial features transformer not fitted. Skipping interaction features.")
                return X

            # Transform features
            X_poly = self.poly_features.transform(X_for_interactions_scaled)

        # Step 4: Create DataFrame with interaction features only (exclude original features)
        if hasattr(self, 'original_feature_indices') and self.original_feature_indices is not None:
            # Get indices of interaction terms (exclude original features)
            interaction_indices = [i for i in range(X_poly.shape[1]) if i not in self.original_feature_indices]

            # Extract only interaction terms
            X_interactions = X_poly[:, interaction_indices]

            # Create consistent interaction feature names
            interaction_names = [f"interaction_{i}" for i in range(len(interaction_indices))]
        else:
            # If original feature indices not available, use all features
            X_interactions = X_poly
            interaction_names = [f"interaction_{i}" for i in range(X_poly.shape[1])]

        # Step 5: Create DataFrame with interaction features
        X_interactions_df = pd.DataFrame(
            X_interactions,
            index=X.index,
            columns=interaction_names
        )

        # Step 6: Apply clipping to handle outliers
        # Clip extreme values to prevent numerical issues
        X_interactions_df = X_interactions_df.clip(-5, 5)  # More conservative clipping

        # Step 7: Combine original features with interaction features
        X_combined = pd.concat([X, X_interactions_df], axis=1)

        logger.info(f"Created {X_interactions_df.shape[1]} interaction features")

        return X_combined

    def optimize_hyperparameters(self, X: pd.DataFrame, y: pd.Series, cv: int = 5) -> Dict:
        """
        Optimize hyperparameters using Bayesian optimization.

        Args:
            X: Features
            y: Target
            cv: Number of cross-validation folds

        Returns:
            Dictionary of best parameters
        """
        logger.info("Optimizing hyperparameters for growth rate prediction...")

        # Scale features
        X_scaled = pd.DataFrame(
            self.scaler.fit_transform(X),
            columns=X.columns,
            index=X.index
        )

        # Define parameter space for Random Forest with focus on regularization
        rf_param_space = {
            'n_estimators': Integer(100, 500),
            'max_depth': Integer(3, 15),
            'min_samples_split': Integer(5, 25),
            'min_samples_leaf': Integer(5, 20),
            'max_features': Categorical(['sqrt', 'log2'])
        }

        # Initialize Bayesian search for Random Forest
        rf_bayes = BayesSearchCV(
            RandomForestRegressor(random_state=42, n_jobs=-1),
            rf_param_space,
            n_iter=50,
            cv=cv,
            scoring='r2',
            n_jobs=-1,
            verbose=1,
            random_state=42
        )

        # Fit Bayesian search for Random Forest
        rf_bayes.fit(X_scaled, y)

        # Get best parameters for Random Forest
        rf_best_params = rf_bayes.best_params_
        rf_best_score = rf_bayes.best_score_

        logger.info(f"Best Random Forest parameters: {rf_best_params}")
        logger.info(f"Best Random Forest R²: {rf_best_score:.4f}")

        # Define parameter space for XGBoost with stronger regularization
        xgb_param_space = {
            'n_estimators': Integer(100, 500),
            'learning_rate': Real(0.001, 0.05, prior='log-uniform'),
            'max_depth': Integer(2, 8),
            'subsample': Real(0.5, 0.8),
            'colsample_bytree': Real(0.5, 0.8),
            'reg_alpha': Real(0.1, 20.0, prior='log-uniform'),
            'reg_lambda': Real(0.1, 20.0, prior='log-uniform')
        }

        # Initialize Bayesian search for XGBoost
        xgb_bayes = BayesSearchCV(
            xgb.XGBRegressor(random_state=42),
            xgb_param_space,
            n_iter=50,
            cv=cv,
            scoring='r2',
            n_jobs=-1,
            verbose=1,
            random_state=42
        )

        # Fit Bayesian search for XGBoost
        xgb_bayes.fit(X_scaled, y)

        # Get best parameters for XGBoost
        xgb_best_params = xgb_bayes.best_params_
        xgb_best_score = xgb_bayes.best_score_

        logger.info(f"Best XGBoost parameters: {xgb_best_params}")
        logger.info(f"Best XGBoost R²: {xgb_best_score:.4f}")

        # Define parameter space for LightGBM with stronger regularization
        lgb_param_space = {
            'n_estimators': Integer(100, 500),
            'learning_rate': Real(0.001, 0.05, prior='log-uniform'),
            'num_leaves': Integer(10, 50),
            'max_depth': Integer(2, 8),
            'subsample': Real(0.5, 0.8),
            'colsample_bytree': Real(0.5, 0.8),
            'reg_alpha': Real(0.1, 20.0, prior='log-uniform'),
            'reg_lambda': Real(0.1, 20.0, prior='log-uniform')
        }

        # Initialize Bayesian search for LightGBM
        lgb_bayes = BayesSearchCV(
            lgb.LGBMRegressor(random_state=42),
            lgb_param_space,
            n_iter=50,
            cv=cv,
            scoring='r2',
            n_jobs=-1,
            verbose=1,
            random_state=42
        )

        # Fit Bayesian search for LightGBM
        lgb_bayes.fit(X_scaled, y)

        # Get best parameters for LightGBM
        lgb_best_params = lgb_bayes.best_params_
        lgb_best_score = lgb_bayes.best_score_

        logger.info(f"Best LightGBM parameters: {lgb_best_params}")
        logger.info(f"Best LightGBM R²: {lgb_best_score:.4f}")

        # Update model parameters
        self.rf_model = RandomForestRegressor(**rf_best_params, random_state=42, n_jobs=-1)
        self.xgb_model = xgb.XGBRegressor(**xgb_best_params, random_state=42)
        self.lgb_model = lgb.LGBMRegressor(**lgb_best_params, random_state=42)

        # Return best parameters
        return {
            'rf': rf_best_params,
            'xgb': xgb_best_params,
            'lgb': lgb_best_params
        }

    def fit(self, X_train: pd.DataFrame, y_train: pd.Series,
            X_val: pd.DataFrame = None, y_val: pd.Series = None,
            n_features: int = 100, use_interactions: bool = True) -> 'FinalGrowthRateModel':
        """
        Train the model with improved handling of interaction features, early stopping,
        and better validation handling.

        Args:
            X_train: Training features
            y_train: Training target
            X_val: Validation features (optional)
            y_val: Validation target (optional)
            n_features: Number of features to select
            use_interactions: Whether to use interaction features

        Returns:
            Self
        """
        # Store training target for correlation-based feature selection
        self.y_train = y_train

        # Store feature names
        self.feature_names = X_train.columns.tolist()

        # Reset early stopping variables
        self.best_val_score = -np.inf
        self.patience_counter = 0
        self.best_models = {}

        # Step 1: Select features using multiple methods
        logger.info(f"Selecting top {n_features} features...")
        selected_features = self._select_features(X_train, y_train, n_features)
        X_train_selected = X_train[selected_features]
        if X_val is not None:
            X_val_selected = X_val[selected_features]

        # Step 2: Create interaction features if requested
        if use_interactions:
            logger.info("Adding interaction features...")
            # First train a simple RF model to get feature importances for interaction selection
            if not hasattr(self, 'feature_importances') or 'rf' not in self.feature_importances:
                temp_rf = RandomForestRegressor(
                    n_estimators=100,
                    max_depth=10,
                    random_state=42,
                    n_jobs=-1
                )
                temp_rf.fit(X_train_selected, y_train)
                self.feature_importances = {
                    'rf': pd.Series(
                        temp_rf.feature_importances_,
                        index=X_train_selected.columns
                    ).sort_values(ascending=False)
                }
                logger.info("Created preliminary feature importance ranking for interaction selection")

            # Create interaction features
            X_train_with_interactions = self._create_interaction_features(X_train_selected, is_training=True)
            if X_val is not None:
                X_val_with_interactions = self._create_interaction_features(X_val_selected, is_training=False)
        else:
            # Use selected features without interactions
            X_train_with_interactions = X_train_selected
            if X_val is not None:
                X_val_with_interactions = X_val_selected

        # Step 3: Scale features with robust scaling for better handling of outliers
        logger.info("Scaling features...")
        # Store the column order for consistent scaling
        self.final_feature_list = X_train_with_interactions.columns.tolist()

        X_train_scaled = pd.DataFrame(
            self.scaler.fit_transform(X_train_with_interactions),
            columns=self.final_feature_list,
            index=X_train_with_interactions.index
        )

        # Step 4: Create and train ensemble model
        logger.info("Creating ensemble model...")
        self.ensemble_model = self._create_ensemble_model()

        # Step 5: Train individual models first for better initialization
        logger.info("Pre-training individual models...")
        # Train Random Forest
        self.rf_model.fit(X_train_scaled, y_train)

        # Train XGBoost with early stopping if validation data is available
        if X_val is not None and y_val is not None:
            # Prepare validation data
            X_val_prepared = self._prepare_validation_data(X_val_with_interactions)

            # Train XGBoost with early stopping
            self.xgb_model.fit(
                X_train_scaled, y_train,
                eval_set=[(X_val_prepared, y_val)],
                verbose=False
            )

            # Train LightGBM with early stopping
            self.lgb_model.fit(
                X_train_scaled, y_train,
                eval_set=[(X_val_prepared, y_val)],
                eval_metric='rmse',
                callbacks=[lgb.early_stopping(stopping_rounds=50)]
            )
        else:
            # Train without early stopping
            self.xgb_model.fit(X_train_scaled, y_train)
            self.lgb_model.fit(X_train_scaled, y_train)

        # Train other models
        self.gb_model.fit(X_train_scaled, y_train)
        self.ridge_model.fit(X_train_scaled, y_train)
        self.lasso_model.fit(X_train_scaled, y_train)
        self.elastic_model.fit(X_train_scaled, y_train)
        self.huber_model.fit(X_train_scaled, y_train)

        # SVR can be slow, so only train it on a subset if the dataset is large
        if X_train_scaled.shape[0] > 1000:
            sample_indices = np.random.choice(X_train_scaled.shape[0], 1000, replace=False)
            self.svr_model.fit(X_train_scaled.iloc[sample_indices], y_train.iloc[sample_indices])
        else:
            self.svr_model.fit(X_train_scaled, y_train)

        # Step 6: Train the ensemble model
        logger.info("Training ensemble model...")
        self.ensemble_model.fit(X_train_scaled, y_train)

        # Step 7: Calculate training metrics
        train_preds = self.ensemble_model.predict(X_train_scaled)
        train_r2 = r2_score(y_train, train_preds)
        train_rmse = np.sqrt(mean_squared_error(y_train, train_preds))
        train_mae = mean_absolute_error(y_train, train_preds)

        self.metrics['train_r2'] = train_r2
        self.metrics['train_rmse'] = train_rmse
        self.metrics['train_mae'] = train_mae

        logger.info(f"Training metrics - R²: {train_r2:.4f}, RMSE: {train_rmse:.4f}, MAE: {train_mae:.4f}")

        # Step 8: Calculate validation metrics if validation data is provided
        if X_val is not None and y_val is not None:
            logger.info("Evaluating on validation data...")

            # Use the prepared validation data
            X_val_scaled = self._prepare_validation_data(X_val_with_interactions)

            # Generate predictions
            val_preds = self.ensemble_model.predict(X_val_scaled)
            val_r2 = r2_score(y_val, val_preds)
            val_rmse = np.sqrt(mean_squared_error(y_val, val_preds))
            val_mae = mean_absolute_error(y_val, val_preds)

            self.metrics['val_r2'] = val_r2
            self.metrics['val_rmse'] = val_rmse
            self.metrics['val_mae'] = val_mae

            logger.info(f"Validation metrics - R²: {val_r2:.4f}, RMSE: {val_rmse:.4f}, MAE: {val_mae:.4f}")

            # Check if this is the best model so far
            if val_r2 > self.best_val_score:
                logger.info(f"New best model found! R² improved from {self.best_val_score:.4f} to {val_r2:.4f}")
                self.best_val_score = val_r2
                self.patience_counter = 0

                # Save the best models
                self.best_models = {
                    'ensemble': self.ensemble_model,
                    'rf': self.rf_model,
                    'xgb': self.xgb_model,
                    'lgb': self.lgb_model
                }
            else:
                self.patience_counter += 1
                logger.info(f"No improvement in validation R². Patience: {self.patience_counter}/{self.patience}")

        # Step 9: Extract feature importances from base models
        logger.info("Extracting feature importances...")

        # Get feature importances from Random Forest
        rf_model = self.ensemble_model.named_estimators_['rf']
        self.feature_importances['rf'] = pd.Series(
            rf_model.feature_importances_,
            index=X_train_with_interactions.columns
        ).sort_values(ascending=False)

        # Get feature importances from XGBoost
        xgb_model = self.ensemble_model.named_estimators_['xgb']
        self.feature_importances['xgb'] = pd.Series(
            xgb_model.feature_importances_,
            index=X_train_with_interactions.columns
        ).sort_values(ascending=False)

        # Get feature importances from LightGBM
        lgb_model = self.ensemble_model.named_estimators_['lgb']
        self.feature_importances['lgb'] = pd.Series(
            lgb_model.feature_importances_,
            index=X_train_with_interactions.columns
        ).sort_values(ascending=False)

        # Step 10: Calculate SHAP values for feature importance
        logger.info("Calculating SHAP values for feature importance...")
        try:
            explainer = shap.TreeExplainer(rf_model)
            self.shap_values = explainer.shap_values(X_train_scaled)
        except Exception as e:
            logger.warning(f"Error calculating SHAP values: {e}")
            self.shap_values = None

        # Save the final feature list for prediction
        self.final_feature_list = X_train_with_interactions.columns.tolist()

        return self

    def _prepare_validation_data(self, X_val: pd.DataFrame) -> pd.DataFrame:
        """
        Prepare validation data by ensuring it has the same columns as training data
        and applying the same scaling.

        Args:
            X_val: Validation features

        Returns:
            Scaled validation features
        """
        # Ensure validation data has the same columns in the same order as training data
        for col in self.final_feature_list:
            if col not in X_val.columns:
                logger.warning(f"Adding missing column {col} to validation data")
                X_val[col] = 0

        # Check for extra columns not in training data
        extra_cols = set(X_val.columns) - set(self.final_feature_list)
        if extra_cols:
            logger.warning(f"Found {len(extra_cols)} extra columns in validation data. These will be ignored.")

        # Reorder columns to match training data
        X_val = X_val[self.final_feature_list]

        # Scale validation data
        X_val_scaled = pd.DataFrame(
            self.scaler.transform(X_val),
            columns=self.final_feature_list,
            index=X_val.index
        )

        return X_val_scaled

    def predict(self, X: pd.DataFrame) -> np.ndarray:
        """
        Generate predictions for new data with improved handling of interaction features.

        Args:
            X: Features

        Returns:
            Predictions
        """
        logger.info(f"Generating predictions for {X.shape[0]} samples...")

        # Step 1: Select features if feature selector is available
        if hasattr(self, 'feature_names') and self.feature_names is not None:
            # Get the intersection of available features and required features
            available_features = [f for f in self.feature_names if f in X.columns]
            if len(available_features) < len(self.feature_names):
                missing_features = set(self.feature_names) - set(available_features)
                logger.warning(f"Missing {len(missing_features)} features: {', '.join(list(missing_features)[:5])}...")

            # Select available features
            X_selected = X[available_features]

            # Fill missing features with zeros
            if len(available_features) < len(self.feature_names):
                missing_df = pd.DataFrame(0, index=X.index, columns=list(set(self.feature_names) - set(available_features)))
                X_selected = pd.concat([X_selected, missing_df], axis=1)
                X_selected = X_selected[self.feature_names]  # Reorder columns
        else:
            logger.warning("No feature names available. Using all features.")
            X_selected = X

        # Step 2: Create interaction features if poly_features is fitted
        if hasattr(self.poly_features, 'n_output_features_') and self.poly_features.n_output_features_ > 0:
            logger.info("Adding interaction features for prediction...")
            X_with_interactions = self._create_interaction_features(X_selected, is_training=False)
        else:
            X_with_interactions = X_selected

        # Step 3: Ensure all expected columns are present
        if hasattr(self, 'final_feature_list') and self.final_feature_list is not None:
            # Check if we have all the expected features
            missing_cols = set(self.final_feature_list) - set(X_with_interactions.columns)
            if missing_cols:
                logger.warning(f"Missing {len(missing_cols)} columns in prediction data. Adding with zero values.")
                for col in missing_cols:
                    X_with_interactions[col] = 0

            # Check for extra columns not in training data
            extra_cols = set(X_with_interactions.columns) - set(self.final_feature_list)
            if extra_cols:
                logger.warning(f"Found {len(extra_cols)} extra columns in prediction data. These will be ignored.")

            # Reorder columns to match training data
            X_with_interactions = X_with_interactions[self.final_feature_list]

        # Step 4: Scale features
        try:
            X_scaled = pd.DataFrame(
                self.scaler.transform(X_with_interactions),
                columns=X_with_interactions.columns,
                index=X_with_interactions.index
            )
        except ValueError as e:
            logger.error(f"Error scaling features: {e}")
            logger.error(f"Expected {len(self.scaler.mean_)} features, got {X_with_interactions.shape[1]}")
            # Try to recover by using only the features that were used during training
            if hasattr(self, 'final_feature_list') and self.final_feature_list is not None:
                common_features = [f for f in self.final_feature_list if f in X_with_interactions.columns]
                logger.info(f"Attempting recovery with {len(common_features)} common features")
                X_with_interactions = X_with_interactions[common_features]
                # Add missing features with zeros
                missing_features = set(self.final_feature_list) - set(common_features)
                for f in missing_features:
                    X_with_interactions[f] = 0
                X_with_interactions = X_with_interactions[self.final_feature_list]
                X_scaled = pd.DataFrame(
                    self.scaler.transform(X_with_interactions),
                    columns=X_with_interactions.columns,
                    index=X_with_interactions.index
                )
            else:
                raise

        # Step 5: Generate predictions
        logger.info("Generating final predictions...")
        predictions = self.ensemble_model.predict(X_scaled)

        return predictions

    def evaluate(self, X: pd.DataFrame, y: pd.Series) -> Dict[str, float]:
        """
        Evaluate the model on new data.

        Args:
            X: Features
            y: Target (assumed to be already scaled if scaling was applied)

        Returns:
            Dictionary of evaluation metrics
        """
        # Generate predictions
        y_pred = self.predict(X)

        # Calculate metrics
        # Note: When using scaled targets, R² remains the same but RMSE and MAE are in scaled units
        metrics = {
            'R2': r2_score(y, y_pred),
            'RMSE': np.sqrt(mean_squared_error(y, y_pred)),
            'MAE': mean_absolute_error(y, y_pred)
        }

        # Log metrics
        logger.info(f"Evaluation metrics - R²: {metrics['R2']:.4f}, RMSE: {metrics['RMSE']:.4f}, MAE: {metrics['MAE']:.4f}")
        logger.info("Note: RMSE and MAE are in scaled units if target scaling was applied")

        return metrics

    def get_feature_importance(self) -> pd.DataFrame:
        """
        Get feature importance from Random Forest.

        Returns:
            DataFrame with feature importance
        """
        if not self.feature_importances:
            logger.warning("Feature importances not available. Train the model first.")
            return pd.DataFrame()

        return pd.DataFrame(self.feature_importances)

    def plot_feature_importance(self, output_dir: str = None, n_features: int = 30) -> None:
        """
        Plot feature importance.

        Args:
            output_dir: Directory to save plots (optional)
            n_features: Number of top features to plot
        """
        if not self.feature_importances:
            logger.warning("Feature importances not available. Train the model first.")
            return

        # Create output directory if provided
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)

        # Plot Random Forest feature importance
        plt.figure(figsize=(12, 8))
        top_features = self.feature_importances['rf'].head(n_features)
        sns.barplot(x=top_features.values, y=top_features.index)
        plt.title(f'Top {n_features} Feature Importance (Random Forest)')
        plt.tight_layout()

        if output_dir:
            plt.savefig(os.path.join(output_dir, 'rf_feature_importance.png'))
        else:
            plt.show()

        # Plot SHAP values if available
        if self.shap_values is not None:
            try:
                plt.figure(figsize=(12, 8))
                # Use plot_type='bar' without specifying features to avoid dimension mismatch
                shap.summary_plot(self.shap_values, plot_type='bar', show=False)
                plt.title('SHAP Feature Importance')
                plt.tight_layout()

                if output_dir:
                    plt.savefig(os.path.join(output_dir, 'shap_feature_importance.png'))
                else:
                    plt.show()
            except Exception as e:
                logger.error(f"Error plotting SHAP values: {e}")
                plt.close()  # Close the figure in case of error

    def plot_predictions(self, X: pd.DataFrame, y: pd.Series, output_dir: str = None) -> None:
        """
        Plot predictions vs actual values.

        Args:
            X: Features
            y: Target
            output_dir: Directory to save plots (optional)
        """
        # Generate predictions
        y_pred = self.predict(X)

        # Ensure predictions are 1-dimensional
        if len(y_pred.shape) > 1:
            y_pred = y_pred.flatten()

        # Convert predictions to Series with the same index as y for proper alignment
        y_pred_series = pd.Series(y_pred, index=y.index)

        # Calculate metrics
        r2 = r2_score(y, y_pred_series)
        rmse = np.sqrt(mean_squared_error(y, y_pred_series))

        # Create scatter plot
        plt.figure(figsize=(10, 6))
        plt.scatter(y, y_pred_series, alpha=0.5)
        plt.plot([y.min(), y.max()], [y.min(), y.max()], 'r--')
        plt.xlabel('Actual Growth Rate')
        plt.ylabel('Predicted Growth Rate')
        plt.title(f'Growth Rate Predictions (R² = {r2:.4f}, RMSE = {rmse:.4f})')
        plt.tight_layout()

        if output_dir:
            os.makedirs(output_dir, exist_ok=True)
            plt.savefig(os.path.join(output_dir, 'growth_predictions_scatter.png'))
        else:
            plt.show()

        # Create residual plot
        residuals = y - y_pred_series
        plt.figure(figsize=(10, 6))
        plt.scatter(y_pred_series, residuals, alpha=0.5)
        plt.axhline(y=0, color='r', linestyle='--')
        plt.xlabel('Predicted Growth Rate')
        plt.ylabel('Residuals')
        plt.title('Residual Plot for Growth Rate Predictions')
        plt.tight_layout()

        if output_dir:
            plt.savefig(os.path.join(output_dir, 'growth_predictions_residuals.png'))
        else:
            plt.show()

    def save(self, output_dir: str) -> None:
        """
        Save the model to disk with all interaction-related components.

        Args:
            output_dir: Directory to save the model
        """
        os.makedirs(output_dir, exist_ok=True)

        # Save ensemble model
        joblib.dump(self.ensemble_model, os.path.join(output_dir, 'ensemble_model.joblib'))

        # Save scaler
        joblib.dump(self.scaler, os.path.join(output_dir, 'scaler.joblib'))

        # Save interaction scaler if available
        if self.interaction_scaler is not None:
            joblib.dump(self.interaction_scaler, os.path.join(output_dir, 'interaction_scaler.joblib'))

        # Save feature selector if available
        if self.feature_selector is not None:
            joblib.dump(self.feature_selector, os.path.join(output_dir, 'feature_selector.joblib'))

        # Save poly features if fitted
        if hasattr(self.poly_features, 'n_output_features_') and self.poly_features.n_output_features_ > 0:
            joblib.dump(self.poly_features, os.path.join(output_dir, 'poly_features.joblib'))

        # Save feature importances
        if self.feature_importances:
            pd.DataFrame(self.feature_importances).to_csv(os.path.join(output_dir, 'feature_importances.csv'))

        # Save metrics
        if self.metrics:
            pd.DataFrame([self.metrics]).to_csv(os.path.join(output_dir, 'metrics.csv'), index=False)

        # Save feature names
        if self.feature_names:
            with open(os.path.join(output_dir, 'feature_names.txt'), 'w') as f:
                for feature in self.feature_names:
                    f.write(f"{feature}\n")

        # Save interaction feature names
        if self.interaction_feature_names is not None:
            with open(os.path.join(output_dir, 'interaction_feature_names.txt'), 'w') as f:
                for feature in self.interaction_feature_names:
                    f.write(f"{feature}\n")

        # Save interaction features list
        if self.interaction_features is not None:
            with open(os.path.join(output_dir, 'interaction_features.txt'), 'w') as f:
                for feature in self.interaction_features:
                    f.write(f"{feature}\n")

        # Save original feature indices
        if self.original_feature_indices is not None:
            joblib.dump(self.original_feature_indices, os.path.join(output_dir, 'original_feature_indices.joblib'))

        # Save final feature list
        if self.final_feature_list is not None:
            with open(os.path.join(output_dir, 'final_feature_list.txt'), 'w') as f:
                for feature in self.final_feature_list:
                    f.write(f"{feature}\n")

        logger.info(f"Model saved to {output_dir}")

    def load(self, input_dir: str) -> 'FinalGrowthRateModel':
        """
        Load the model from disk with all interaction-related components.

        Args:
            input_dir: Directory to load the model from

        Returns:
            Self
        """
        # Load ensemble model
        self.ensemble_model = joblib.load(os.path.join(input_dir, 'ensemble_model.joblib'))

        # Load scaler
        self.scaler = joblib.load(os.path.join(input_dir, 'scaler.joblib'))

        # Load interaction scaler if available
        interaction_scaler_path = os.path.join(input_dir, 'interaction_scaler.joblib')
        if os.path.exists(interaction_scaler_path):
            self.interaction_scaler = joblib.load(interaction_scaler_path)

        # Load feature selector if available
        feature_selector_path = os.path.join(input_dir, 'feature_selector.joblib')
        if os.path.exists(feature_selector_path):
            self.feature_selector = joblib.load(feature_selector_path)

        # Load poly features if available
        poly_features_path = os.path.join(input_dir, 'poly_features.joblib')
        if os.path.exists(poly_features_path):
            self.poly_features = joblib.load(poly_features_path)

        # Load feature importances if available
        feature_importances_path = os.path.join(input_dir, 'feature_importances.csv')
        if os.path.exists(feature_importances_path):
            self.feature_importances = pd.read_csv(feature_importances_path, index_col=0).to_dict()

        # Load metrics if available
        metrics_path = os.path.join(input_dir, 'metrics.csv')
        if os.path.exists(metrics_path):
            self.metrics = pd.read_csv(metrics_path).iloc[0].to_dict()

        # Load feature names if available
        feature_names_path = os.path.join(input_dir, 'feature_names.txt')
        if os.path.exists(feature_names_path):
            with open(feature_names_path, 'r') as f:
                self.feature_names = [line.strip() for line in f.readlines()]

        # Load interaction feature names if available
        interaction_feature_names_path = os.path.join(input_dir, 'interaction_feature_names.txt')
        if os.path.exists(interaction_feature_names_path):
            with open(interaction_feature_names_path, 'r') as f:
                self.interaction_feature_names = [line.strip() for line in f.readlines()]

        # Load interaction features list if available
        interaction_features_path = os.path.join(input_dir, 'interaction_features.txt')
        if os.path.exists(interaction_features_path):
            with open(interaction_features_path, 'r') as f:
                self.interaction_features = [line.strip() for line in f.readlines()]

        # Load original feature indices if available
        original_feature_indices_path = os.path.join(input_dir, 'original_feature_indices.joblib')
        if os.path.exists(original_feature_indices_path):
            self.original_feature_indices = joblib.load(original_feature_indices_path)

        # Load final feature list if available
        final_feature_list_path = os.path.join(input_dir, 'final_feature_list.txt')
        if os.path.exists(final_feature_list_path):
            with open(final_feature_list_path, 'r') as f:
                self.final_feature_list = [line.strip() for line in f.readlines()]

        logger.info(f"Model loaded from {input_dir}")

        return self

if __name__ == "__main__":
    # This module is not meant to be run directly
    logger.info("This module provides the FinalGrowthRateModel class for growth rate prediction.")
