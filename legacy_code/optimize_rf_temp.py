#!/usr/bin/env python3

import os
import argparse
import numpy as np
import pandas as pd
import optuna
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.preprocessing import RobustScaler
import joblib
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_and_filter_data(feature_file, target_file):
    """
    Load preprocessed data for temperature prediction.
    
    Args:
        feature_file: Path to the preprocessed feature file (.npy)
        target_file: Path to the preprocessed target file (.npy)
        
    Returns:
        X: Preprocessed feature matrix
        y: Preprocessed target vector
    """
    logger.info(f"Loading preprocessed data from {feature_file} and {target_file}")
    
    # Load preprocessed data
    X = np.load(feature_file)
    y = np.load(target_file)
    
    logger.info(f"Loaded data shapes - X: {X.shape}, y: {y.shape}")
    
    return X, y

def objective(trial, X_train, X_val, y_train, y_val):
    """Optuna objective function for Random Forest optimization."""
    # Define hyperparameter search space
    params = {
        'n_estimators': trial.suggest_int('n_estimators', 100, 1000),
        'max_depth': trial.suggest_int('max_depth', 10, 50),
        'min_samples_split': trial.suggest_int('min_samples_split', 2, 20),
        'min_samples_leaf': trial.suggest_int('min_samples_leaf', 1, 10),
        'max_features': trial.suggest_categorical('max_features', ['sqrt', 'log2']),
        'bootstrap': trial.suggest_categorical('bootstrap', [True, False])
    }
    
    # Create and train model
    model = RandomForestRegressor(**params, random_state=42, n_jobs=-1)
    model.fit(X_train, y_train)
    
    # Make predictions
    y_pred = model.predict(X_val)
    
    # Calculate R² score
    r2 = r2_score(y_val, y_pred)
    
    # Return R² directly since we're maximizing
    return r2

def main():
    parser = argparse.ArgumentParser(description='Optimize Random Forest for temperature prediction')
    parser.add_argument('--feature_file', type=str, required=True, help='Path to preprocessed feature file')
    parser.add_argument('--target_file', type=str, required=True, help='Path to preprocessed target file')
    parser.add_argument('--output_dir', type=str, required=True, help='Output directory for models')
    parser.add_argument('--n_trials', type=int, default=100, help='Number of optimization trials')
    parser.add_argument('--study_name', type=str, default='rf_temp_optimization', help='Optuna study name')
    parser.add_argument('--n_jobs', type=int, default=4, help='Number of parallel jobs')
    
    args = parser.parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Load preprocessed data
    X, y = load_and_filter_data(args.feature_file, args.target_file)
    
    # Split data
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    X_train, X_val, y_train, y_val = train_test_split(X_train, y_train, test_size=0.2, random_state=42)
    
    # Create Optuna study
    study = optuna.create_study(direction='maximize', study_name=args.study_name)
    
    # Run optimization
    logger.info(f"Starting optimization with {args.n_trials} trials...")
    study.optimize(lambda trial: objective(trial, X_train, X_val, y_train, y_val),
                  n_trials=args.n_trials,
                  n_jobs=args.n_jobs)
    
    # Get best parameters
    best_params = study.best_params
    logger.info(f"Best parameters: {best_params}")
    logger.info(f"Best validation R²: {study.best_value:.4f}")  # No need to convert back to positive
    
    # Train final model with best parameters
    logger.info("Training final model with best parameters...")
    final_model = RandomForestRegressor(**best_params, random_state=42, n_jobs=-1)
    final_model.fit(X_train, y_train)
    
    # Evaluate on test set
    y_pred = final_model.predict(X_test)
    mse = mean_squared_error(y_test, y_pred)
    r2 = r2_score(y_test, y_pred)
    
    logger.info(f"Test MSE: {mse:.4f}")
    logger.info(f"Test R²: {r2:.4f}")
    
    # Save model
    model_path = os.path.join(args.output_dir, 'temperature_rf_model.pkl')
    joblib.dump(final_model, model_path)
    logger.info(f"Model saved to {model_path}")
    
    # Save study
    study_path = os.path.join(args.output_dir, 'temperature_rf_study.pkl')
    joblib.dump(study, study_path)
    logger.info(f"Study saved to {study_path}")

if __name__ == '__main__':
    main() 