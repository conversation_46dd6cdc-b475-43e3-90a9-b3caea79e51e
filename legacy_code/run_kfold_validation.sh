#!/bin/bash

# DeepMu: K-Fold Cross-Validation for Temperature Prediction Model
# This script runs k-fold cross-validation to evaluate the temperature prediction model's
# performance across different data splits.

# Set a clean output directory
OUTPUT_DIR="models/deepmu_kfold_validation"

# Create output directory
mkdir -p "$OUTPUT_DIR"

# Define color codes for output formatting
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Display initial message
echo -e "${BLUE}${BOLD}========================================================${NC}"
echo -e "${BLUE}${BOLD}   DeepMu - K-Fold Cross-Validation   ${NC}"
echo -e "${BLUE}${BOLD}========================================================${NC}"
echo -e "${GREEN}This script runs k-fold cross-validation to evaluate the temperature prediction model:${NC}"
echo -e "  1. ${YELLOW}5-fold cross-validation${NC}"
echo -e "  2. ${YELLOW}Feature selection for each fold${NC}"
echo -e "  3. ${YELLOW}Balanced model architecture${NC}"
echo -e "  4. ${YELLOW}Early stopping to prevent overfitting${NC}"
echo -e ""
echo -e "${YELLOW}Output will be saved to:${NC} ${OUTPUT_DIR}"
echo ""

# Check if prerequisites are available
echo -e "${YELLOW}Checking prerequisites...${NC}"
if [ ! -f "./training_data/combined_features.tsv" ]; then
    echo -e "${RED}Error: Feature file not found. Please ensure './training_data/combined_features.tsv' exists.${NC}"
    exit 1
fi

if [ ! -f "./training_data/metadata.tsv" ]; then
    echo -e "${RED}Error: Metadata file not found. Please ensure './training_data/metadata.tsv' exists.${NC}"
    exit 1
fi

echo -e "${GREEN}All prerequisites found.${NC}"
echo ""

# Step 1: Filter the dataset to remove unreliable temperature data
echo -e "${YELLOW}Step 1: Filtering dataset to remove unreliable temperature data...${NC}"
python improved_temp_data_filter.py \
    --feature-file "./training_data/combined_features.tsv" \
    --metadata-file "./training_data/metadata.tsv" \
    --output-dir "$OUTPUT_DIR/filtered_data" \
    --reliability-threshold 0.6 \
    --prediction-error-threshold 2.0

# Check if filtering was successful
if [ $? -eq 0 ]; then
    echo -e "${GREEN}Dataset filtering completed. Using filtered datasets for training.${NC}"
    FEATURES_FILE="${OUTPUT_DIR}/filtered_data/filtered_features.tsv"
    METADATA_FILE="${OUTPUT_DIR}/filtered_data/filtered_metadata.tsv"
else
    echo -e "${RED}Error: Dataset filtering failed. Exiting.${NC}"
    exit 1
fi

# Step 2: Run k-fold cross-validation
echo -e "${YELLOW}Step 2: Running k-fold cross-validation...${NC}"

# Set parameters for k-fold cross-validation
N_FEATURES=1000
N_FOLDS=5
HIDDEN_DIMS="512,512,384,256,128"
DROPOUT_RATES="0.2,0.3,0.4,0.5,0.5"
LEARNING_RATE=0.001
WEIGHT_DECAY=1e-5
BATCH_SIZE=32
EPOCHS=300
PATIENCE=50
SEED=42
FEATURE_SELECTION="correlation"  # Use correlation-based feature selection

# Run k-fold cross-validation
python kfold_temp_validation.py \
    --feature-file "$FEATURES_FILE" \
    --metadata-file "$METADATA_FILE" \
    --output-dir "$OUTPUT_DIR/kfold_results" \
    --n-features $N_FEATURES \
    --n-folds $N_FOLDS \
    --hidden-dims "$HIDDEN_DIMS" \
    --dropout-rates "$DROPOUT_RATES" \
    --learning-rate $LEARNING_RATE \
    --weight-decay $WEIGHT_DECAY \
    --batch-size $BATCH_SIZE \
    --epochs $EPOCHS \
    --patience $PATIENCE \
    --seed $SEED \
    --feature-selection "$FEATURE_SELECTION"

# Check if k-fold cross-validation was successful
if [ $? -eq 0 ]; then
    echo -e "${GREEN}K-fold cross-validation completed successfully.${NC}"
else
    echo -e "${RED}Error: K-fold cross-validation failed. Exiting.${NC}"
    exit 1
fi

# Step 3: Analyze results
echo -e "${YELLOW}Step 3: Analyzing results...${NC}"

# Display average metrics
if [ -f "$OUTPUT_DIR/kfold_results/average_metrics.csv" ]; then
    echo -e "${GREEN}Average Metrics:${NC}"
    echo -e "$(cat "$OUTPUT_DIR/kfold_results/average_metrics.csv" | column -t -s,)"
else
    echo -e "${RED}Error: Average metrics file not found.${NC}"
fi

echo ""
echo -e "${GREEN}${BOLD}========================================================${NC}"
echo -e "${GREEN}${BOLD}   K-Fold Cross-Validation Completed!   ${NC}"
echo -e "${GREEN}${BOLD}========================================================${NC}"
echo ""
echo -e "${GREEN}Results saved to:${NC} ${OUTPUT_DIR}/kfold_results"

echo ""
echo -e "${GREEN}Done.${NC}"
