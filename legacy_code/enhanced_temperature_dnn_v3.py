#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Enhanced Temperature DNN V3 for DeepMu.

This module implements a highly advanced deep neural network architecture
specifically designed for temperature prediction, incorporating residual connections,
attention mechanisms, and other modern deep learning techniques.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import List, Optional, Dict, Tuple, Union

class ResidualBlock(nn.Module):
    """
    Residual block with batch normalization and dropout.
    """
    def __init__(self, input_dim: int, output_dim: int, 
                 dropout_rate: float = 0.3, 
                 use_batch_norm: bool = True,
                 activation: str = 'leaky_relu'):
        super().__init__()
        
        # Choose activation function
        if activation == 'relu':
            self.act_fn = nn.ReLU()
        elif activation == 'leaky_relu':
            self.act_fn = nn.LeakyReLU(0.1)
        elif activation == 'elu':
            self.act_fn = nn.ELU()
        elif activation == 'gelu':
            self.act_fn = nn.GELU()
        else:
            self.act_fn = nn.LeakyReLU(0.1)  # Default to leaky ReLU
        
        # Main path
        layers = []
        layers.append(nn.Linear(input_dim, output_dim))
        
        if use_batch_norm:
            layers.append(nn.BatchNorm1d(output_dim))
            
        layers.append(self.act_fn)
        layers.append(nn.Dropout(dropout_rate))
        
        self.main_path = nn.Sequential(*layers)
        
        # Skip connection (if dimensions don't match)
        self.skip_connection = None
        if input_dim != output_dim:
            self.skip_connection = nn.Linear(input_dim, output_dim)
    
    def forward(self, x):
        # Main path
        main = self.main_path(x)
        
        # Skip connection
        if self.skip_connection is not None:
            skip = self.skip_connection(x)
        else:
            skip = x
            
        # Combine
        return main + skip

class SelfAttention(nn.Module):
    """
    Self-attention mechanism for feature refinement.
    """
    def __init__(self, embed_dim: int, num_heads: int = 4, dropout: float = 0.1):
        super().__init__()
        self.attention = nn.MultiheadAttention(
            embed_dim=embed_dim,
            num_heads=num_heads,
            dropout=dropout,
            batch_first=True
        )
        self.layer_norm = nn.LayerNorm(embed_dim)
        
    def forward(self, x):
        # Reshape for attention if needed (batch_size, seq_len, embed_dim)
        if len(x.shape) == 2:
            x_reshaped = x.unsqueeze(1)  # Add sequence dimension
        else:
            x_reshaped = x
            
        # Apply self-attention
        attn_output, _ = self.attention(x_reshaped, x_reshaped, x_reshaped)
        
        # Add residual connection and layer normalization
        output = self.layer_norm(x_reshaped + attn_output)
        
        # Reshape back if needed
        if len(x.shape) == 2:
            output = output.squeeze(1)
            
        return output

class FeatureExtractor(nn.Module):
    """
    Feature extractor with residual connections and optional attention.
    """
    def __init__(self, 
                 input_dim: int,
                 hidden_dims: List[int],
                 dropout_rates: List[float],
                 use_batch_norm: bool = True,
                 activation: str = 'leaky_relu',
                 use_attention: bool = True,
                 attention_heads: int = 4):
        super().__init__()
        
        self.use_attention = use_attention
        
        # Create residual blocks
        self.blocks = nn.ModuleList()
        prev_dim = input_dim
        
        for i, (hdim, drop_rate) in enumerate(zip(hidden_dims, dropout_rates)):
            self.blocks.append(
                ResidualBlock(
                    input_dim=prev_dim,
                    output_dim=hdim,
                    dropout_rate=drop_rate,
                    use_batch_norm=use_batch_norm,
                    activation=activation
                )
            )
            prev_dim = hdim
        
        # Add attention layers after every 2 residual blocks
        if use_attention:
            self.attention_layers = nn.ModuleList()
            for i, hdim in enumerate(hidden_dims[1::2]):
                self.attention_layers.append(
                    SelfAttention(
                        embed_dim=hdim,
                        num_heads=attention_heads,
                        dropout=0.1
                    )
                )
    
    def forward(self, x):
        # Process through residual blocks with attention
        attention_idx = 0
        
        for i, block in enumerate(self.blocks):
            x = block(x)
            
            # Apply attention after every 2 blocks
            if self.use_attention and i > 0 and i % 2 == 1 and attention_idx < len(self.attention_layers):
                x = self.attention_layers[attention_idx](x)
                attention_idx += 1
                
        return x

class EnhancedTemperatureDNNV3(nn.Module):
    """
    Enhanced temperature prediction model with residual connections,
    attention mechanisms, and advanced regularization techniques.
    
    This version is designed to achieve R² values >0.92 for temperature prediction.
    """
    def __init__(self, 
                 input_dim: int, 
                 hidden_dims: List[int] = [768, 768, 512, 512, 384, 256, 128],
                 dropout_rates: List[float] = [0.2, 0.25, 0.3, 0.35, 0.4, 0.45, 0.5],
                 use_batch_norm: bool = True,
                 activation: str = 'leaky_relu',
                 use_attention: bool = True,
                 attention_heads: int = 4,
                 use_layer_norm: bool = True):
        super().__init__()
        
        # Input normalization
        self.input_norm = nn.LayerNorm(input_dim) if use_layer_norm else nn.Identity()
        
        # Feature extractor
        self.feature_extractor = FeatureExtractor(
            input_dim=input_dim,
            hidden_dims=hidden_dims,
            dropout_rates=dropout_rates,
            use_batch_norm=use_batch_norm,
            activation=activation,
            use_attention=use_attention,
            attention_heads=attention_heads
        )
        
        # Output layer
        self.output_layer = nn.Linear(hidden_dims[-1], 1)
        
        # Initialize weights
        self._init_weights()
    
    def _init_weights(self):
        """Initialize weights with improved initialization."""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                # Use Kaiming initialization for better training with deep networks
                nn.init.kaiming_normal_(m.weight, nonlinearity='leaky_relu')
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
    
    def forward(self, x):
        # Normalize input
        x = self.input_norm(x)
        
        # Extract features
        x = self.feature_extractor(x)
        
        # Output layer
        return self.output_layer(x).squeeze()

class TemperatureEnsemble(nn.Module):
    """
    Ensemble of temperature prediction models for improved performance.
    """
    def __init__(self, 
                 input_dim: int,
                 hidden_dims: List[int] = [768, 768, 512, 512, 384, 256, 128],
                 n_models: int = 3,
                 dropout_rates: List[float] = [0.2, 0.25, 0.3, 0.35, 0.4, 0.45, 0.5],
                 use_batch_norm: bool = True,
                 activation: str = 'leaky_relu',
                 use_attention: bool = True):
        super().__init__()
        
        # Create multiple models with different initializations
        self.models = nn.ModuleList()
        for _ in range(n_models):
            self.models.append(
                EnhancedTemperatureDNNV3(
                    input_dim=input_dim,
                    hidden_dims=hidden_dims,
                    dropout_rates=dropout_rates,
                    use_batch_norm=use_batch_norm,
                    activation=activation,
                    use_attention=use_attention
                )
            )
        
        # Learnable weights for ensemble combination
        self.ensemble_weights = nn.Parameter(torch.ones(n_models) / n_models)
    
    def forward(self, x):
        # Get predictions from all models
        predictions = []
        for model in self.models:
            predictions.append(model(x).unsqueeze(1))
        
        # Stack predictions
        stacked_preds = torch.cat(predictions, dim=1)
        
        # Apply softmax to ensure weights sum to 1
        weights = F.softmax(self.ensemble_weights, dim=0)
        
        # Weighted average of predictions
        ensemble_pred = torch.sum(stacked_preds * weights, dim=1)
        
        return ensemble_pred
