#!/bin/bash

# Run improved hybrid model for microbial growth rate and optimal temperature prediction

# Set paths
FEATURE_FILE="training_data/combined_features.tsv"
METADATA_FILE="training_data/metadata.tsv"
OUTPUT_DIR="models/improved_hybrid"

# Set parameters
N_FEATURES_GROWTH=100
N_FEATURES_TEMP=100

# Create output directory
mkdir -p $OUTPUT_DIR

# Run improved hybrid model
python improved_hybrid_model.py \
  --feature_file $FEATURE_FILE \
  --metadata_file $METADATA_FILE \
  --output_dir $OUTPUT_DIR \
  --n_features_growth $N_FEATURES_GROWTH \
  --n_features_temp $N_FEATURES_TEMP

echo "Improved hybrid model training completed"
