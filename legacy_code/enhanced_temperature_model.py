#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Enhanced temperature prediction model using DNN with One-Cycle Learning.
This model implements a deep neural network with advanced regularization techniques.
"""

import os
import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Tuple, Union, Optional
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt
import seaborn as sns
import joblib
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
from torch.optim.lr_scheduler import OneCycleLR

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Check GPU availability
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
logger.info(f"Using device: {device}")

class DNNModel(nn.Module):
    """
    Deep Neural Network model for temperature prediction.
    """

    def __init__(self, input_dim: int, hidden_dims: List[int], dropout_rates: List[float]):
        """
        Initialize the model.

        Args:
            input_dim: Input dimension
            hidden_dims: List of hidden dimensions
            dropout_rates: List of dropout rates
        """
        super(DNNModel, self).__init__()

        layers = []
        prev_dim = input_dim

        for i, (hdim, drop_rate) in enumerate(zip(hidden_dims, dropout_rates)):
            layers.append(nn.Linear(prev_dim, hdim))
            layers.append(nn.BatchNorm1d(hdim))
            layers.append(nn.LeakyReLU())
            layers.append(nn.Dropout(drop_rate))
            prev_dim = hdim

        # Output layer
        layers.append(nn.Linear(prev_dim, 1))

        self.model = nn.Sequential(*layers)

    def forward(self, x):
        """
        Forward pass.

        Args:
            x: Input tensor

        Returns:
            Output tensor
        """
        return self.model(x)

class EnhancedTemperatureModel:
    """
    Enhanced temperature prediction model using DNN with One-Cycle Learning.
    """

    def __init__(self, hidden_dims: List[int] = None, dropout_rates: List[float] = None):
        """
        Initialize the model with parameters.

        Args:
            hidden_dims: List of hidden dimensions
            dropout_rates: List of dropout rates
        """
        # Set default parameters if not provided
        self.hidden_dims = hidden_dims or [512, 256, 128, 64]
        self.dropout_rates = dropout_rates or [0.3, 0.4, 0.4, 0.5]

        # Initialize model
        self.model = None

        # For storing evaluation metrics
        self.metrics = {}

        # For storing feature names
        self.feature_names = None

        # For storing scaler
        self.scaler = StandardScaler()

        # For storing training history
        self.history = {
            'train_loss': [],
            'val_loss': [],
            'val_r2': []
        }

    def fit(self, X_train: pd.DataFrame, y_train: pd.Series,
            X_val: pd.DataFrame = None, y_val: pd.Series = None,
            batch_size: int = 64, epochs: int = 150,
            learning_rate: float = 0.001, weight_decay: float = 1e-5) -> 'EnhancedTemperatureModel':
        """
        Train the model.

        Args:
            X_train: Training features
            y_train: Training target
            X_val: Validation features (optional)
            y_val: Validation target (optional)
            batch_size: Batch size
            epochs: Number of epochs
            learning_rate: Learning rate
            weight_decay: Weight decay

        Returns:
            Self
        """
        # Store feature names
        self.feature_names = X_train.columns.tolist()

        # Scale features
        X_train_scaled = self.scaler.fit_transform(X_train)

        # Convert to PyTorch tensors
        X_train_tensor = torch.FloatTensor(X_train_scaled)
        y_train_tensor = torch.FloatTensor(y_train.values).reshape(-1, 1)

        # Create dataset and dataloader
        train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)

        # Prepare validation data if provided
        val_loader = None
        if X_val is not None and y_val is not None:
            X_val_scaled = self.scaler.transform(X_val)
            X_val_tensor = torch.FloatTensor(X_val_scaled)
            y_val_tensor = torch.FloatTensor(y_val.values).reshape(-1, 1)
            val_dataset = TensorDataset(X_val_tensor, y_val_tensor)
            val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)

        # Initialize model
        self.model = DNNModel(
            input_dim=X_train.shape[1],
            hidden_dims=self.hidden_dims,
            dropout_rates=self.dropout_rates
        ).to(device)

        # Loss function and optimizer
        criterion = nn.MSELoss()
        optimizer = optim.Adam(self.model.parameters(), lr=learning_rate, weight_decay=weight_decay)

        # Learning rate scheduler with one-cycle policy
        scheduler = OneCycleLR(
            optimizer,
            max_lr=learning_rate,
            steps_per_epoch=len(train_loader),
            epochs=epochs,
            pct_start=0.3
        )

        # Training loop
        best_val_loss = float('inf')
        best_model_state = None

        logger.info(f"Starting DNN training for {epochs} epochs")
        for epoch in range(epochs):
            # Training
            self.model.train()
            train_loss = 0.0

            for inputs, targets in train_loader:
                inputs, targets = inputs.to(device), targets.to(device)

                # Zero gradients
                optimizer.zero_grad()

                # Forward pass
                outputs = self.model(inputs)
                loss = criterion(outputs, targets)

                # Backward pass and optimize
                loss.backward()

                # Apply gradient clipping
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)

                optimizer.step()
                scheduler.step()

                train_loss += loss.item() * inputs.size(0)

            # Calculate average loss for the epoch
            train_loss = train_loss / len(train_loader.dataset)
            self.history['train_loss'].append(train_loss)

            # Validation
            if val_loader is not None:
                self.model.eval()
                val_loss = 0.0
                all_preds = []
                all_targets = []

                with torch.no_grad():
                    for inputs, targets in val_loader:
                        inputs, targets = inputs.to(device), targets.to(device)
                        outputs = self.model(inputs)
                        loss = criterion(outputs, targets)
                        val_loss += loss.item() * inputs.size(0)

                        # Collect predictions and targets for R² calculation
                        all_preds.extend(outputs.cpu().numpy())
                        all_targets.extend(targets.cpu().numpy())

                val_loss = val_loss / len(val_loader.dataset)
                self.history['val_loss'].append(val_loss)

                # Calculate R² score
                val_r2 = r2_score(all_targets, all_preds)
                self.history['val_r2'].append(val_r2)

                # Print progress
                if (epoch + 1) % 10 == 0:
                    logger.info(f'Epoch {epoch+1}/{epochs} - Train Loss: {train_loss:.4f} - '
                              f'Val Loss: {val_loss:.4f} - Val R²: {val_r2:.4f}')

                # Save best model
                if val_loss < best_val_loss:
                    best_val_loss = val_loss
                    best_model_state = self.model.state_dict().copy()
            else:
                # Print progress without validation
                if (epoch + 1) % 10 == 0:
                    logger.info(f'Epoch {epoch+1}/{epochs} - Train Loss: {train_loss:.4f}')

        # Load best model if validation was used
        if best_model_state is not None:
            self.model.load_state_dict(best_model_state)

        # Calculate training metrics
        self.model.eval()
        with torch.no_grad():
            train_preds = self.model(X_train_tensor.to(device)).cpu().numpy().flatten()

        train_r2 = r2_score(y_train, train_preds)
        train_rmse = np.sqrt(mean_squared_error(y_train, train_preds))
        train_mae = mean_absolute_error(y_train, train_preds)

        self.metrics['train_r2'] = train_r2
        self.metrics['train_rmse'] = train_rmse
        self.metrics['train_mae'] = train_mae

        logger.info(f"Training metrics - R²: {train_r2:.4f}, RMSE: {train_rmse:.4f}, MAE: {train_mae:.4f}")

        # Calculate validation metrics if validation was used
        if val_loader is not None:
            with torch.no_grad():
                val_preds = self.model(X_val_tensor.to(device)).cpu().numpy().flatten()

            val_r2 = r2_score(y_val, val_preds)
            val_rmse = np.sqrt(mean_squared_error(y_val, val_preds))
            val_mae = mean_absolute_error(y_val, val_preds)

            self.metrics['val_r2'] = val_r2
            self.metrics['val_rmse'] = val_rmse
            self.metrics['val_mae'] = val_mae

            logger.info(f"Validation metrics - R²: {val_r2:.4f}, RMSE: {val_rmse:.4f}, MAE: {val_mae:.4f}")

        return self

    def predict(self, X: pd.DataFrame) -> np.ndarray:
        """
        Generate predictions for new data.

        Args:
            X: Features

        Returns:
            Predictions
        """
        # Scale features
        X_scaled = self.scaler.transform(X)
        X_tensor = torch.FloatTensor(X_scaled).to(device)

        # Generate predictions
        self.model.eval()
        with torch.no_grad():
            predictions = self.model(X_tensor).cpu().numpy().flatten()

        return predictions

    def evaluate(self, X: pd.DataFrame, y: pd.Series) -> Dict[str, float]:
        """
        Evaluate the model on new data.

        Args:
            X: Features
            y: Target

        Returns:
            Dictionary of evaluation metrics
        """
        # Generate predictions
        y_pred = self.predict(X)

        # Calculate metrics
        metrics = {
            'R2': r2_score(y, y_pred),
            'RMSE': np.sqrt(mean_squared_error(y, y_pred)),
            'MAE': mean_absolute_error(y, y_pred)
        }

        # Log metrics
        logger.info(f"Evaluation metrics - R²: {metrics['R2']:.4f}, RMSE: {metrics['RMSE']:.4f}, MAE: {metrics['MAE']:.4f}")

        return metrics

    def plot_training_history(self, output_dir: str = None) -> None:
        """
        Plot training history.

        Args:
            output_dir: Directory to save plots (optional)
        """
        if not self.history['train_loss']:
            logger.warning("Training history not available. Train the model first.")
            return

        # Create output directory if provided
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)

        # Plot training and validation loss
        plt.figure(figsize=(10, 6))
        plt.plot(self.history['train_loss'], label='Training Loss')
        if self.history['val_loss']:
            plt.plot(self.history['val_loss'], label='Validation Loss')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.title('Training and Validation Loss')
        plt.legend()
        plt.tight_layout()

        if output_dir:
            plt.savefig(os.path.join(output_dir, 'temperature_training_loss.png'))
        else:
            plt.show()

        # Plot validation R² if available
        if self.history['val_r2']:
            plt.figure(figsize=(10, 6))
            plt.plot(self.history['val_r2'])
            plt.xlabel('Epoch')
            plt.ylabel('R²')
            plt.title('Validation R²')
            plt.tight_layout()

            if output_dir:
                plt.savefig(os.path.join(output_dir, 'temperature_validation_r2.png'))
            else:
                plt.show()

    def plot_predictions(self, X: pd.DataFrame, y: pd.Series, output_dir: str = None) -> None:
        """
        Plot predictions vs actual values.

        Args:
            X: Features
            y: Target
            output_dir: Directory to save plots (optional)
        """
        # Generate predictions
        y_pred = self.predict(X)

        # Ensure predictions are 1-dimensional
        if len(y_pred.shape) > 1:
            y_pred = y_pred.flatten()

        # Convert predictions to Series with the same index as y for proper alignment
        y_pred_series = pd.Series(y_pred, index=y.index)

        # Calculate metrics
        r2 = r2_score(y, y_pred_series)
        rmse = np.sqrt(mean_squared_error(y, y_pred_series))

        # Create scatter plot
        plt.figure(figsize=(10, 6))
        plt.scatter(y, y_pred_series, alpha=0.5)
        plt.plot([y.min(), y.max()], [y.min(), y.max()], 'r--')
        plt.xlabel('Actual Temperature')
        plt.ylabel('Predicted Temperature')
        plt.title(f'Temperature Predictions (R² = {r2:.4f}, RMSE = {rmse:.4f})')
        plt.tight_layout()

        if output_dir:
            os.makedirs(output_dir, exist_ok=True)
            plt.savefig(os.path.join(output_dir, 'temperature_predictions_scatter.png'))
        else:
            plt.show()

        # Create residual plot
        residuals = y - y_pred_series
        plt.figure(figsize=(10, 6))
        plt.scatter(y_pred_series, residuals, alpha=0.5)
        plt.axhline(y=0, color='r', linestyle='--')
        plt.xlabel('Predicted Temperature')
        plt.ylabel('Residuals')
        plt.title('Residual Plot for Temperature Predictions')
        plt.tight_layout()

        if output_dir:
            plt.savefig(os.path.join(output_dir, 'temperature_predictions_residuals.png'))
        else:
            plt.show()

    def save(self, output_dir: str) -> None:
        """
        Save the model to disk.

        Args:
            output_dir: Directory to save the model
        """
        os.makedirs(output_dir, exist_ok=True)

        # Save model
        torch.save(self.model.state_dict(), os.path.join(output_dir, 'temperature_dnn_model.pt'))

        # Save scaler
        joblib.dump(self.scaler, os.path.join(output_dir, 'temperature_scaler.joblib'))

        # Save metrics
        if self.metrics:
            pd.DataFrame([self.metrics]).to_csv(os.path.join(output_dir, 'temperature_metrics.csv'), index=False)

        # Save feature names
        if self.feature_names:
            with open(os.path.join(output_dir, 'temperature_feature_names.txt'), 'w') as f:
                for feature in self.feature_names:
                    f.write(f"{feature}\n")

        # Save training history
        if self.history['train_loss']:
            pd.DataFrame(self.history).to_csv(os.path.join(output_dir, 'temperature_training_history.csv'), index=False)

        # Save model architecture
        with open(os.path.join(output_dir, 'temperature_model_architecture.txt'), 'w') as f:
            f.write(f"Hidden dimensions: {self.hidden_dims}\n")
            f.write(f"Dropout rates: {self.dropout_rates}\n")

        logger.info(f"Temperature model saved to {output_dir}")

    def load(self, input_dir: str) -> 'EnhancedTemperatureModel':
        """
        Load the model from disk.

        Args:
            input_dir: Directory to load the model from

        Returns:
            Self
        """
        # Load model architecture
        architecture_path = os.path.join(input_dir, 'temperature_model_architecture.txt')
        if os.path.exists(architecture_path):
            with open(architecture_path, 'r') as f:
                lines = f.readlines()
                for line in lines:
                    if line.startswith('Hidden dimensions:'):
                        self.hidden_dims = eval(line.split(':', 1)[1].strip())
                    elif line.startswith('Dropout rates:'):
                        self.dropout_rates = eval(line.split(':', 1)[1].strip())

        # Load feature names
        feature_names_path = os.path.join(input_dir, 'temperature_feature_names.txt')
        if os.path.exists(feature_names_path):
            with open(feature_names_path, 'r') as f:
                self.feature_names = [line.strip() for line in f.readlines()]

        # Initialize model
        input_dim = len(self.feature_names) if self.feature_names else 100  # Default to 100 if feature names not available
        self.model = DNNModel(
            input_dim=input_dim,
            hidden_dims=self.hidden_dims,
            dropout_rates=self.dropout_rates
        ).to(device)

        # Load model weights
        self.model.load_state_dict(torch.load(os.path.join(input_dir, 'temperature_dnn_model.pt'), map_location=device))
        self.model.eval()

        # Load scaler
        self.scaler = joblib.load(os.path.join(input_dir, 'temperature_scaler.joblib'))

        # Load metrics if available
        metrics_path = os.path.join(input_dir, 'temperature_metrics.csv')
        if os.path.exists(metrics_path):
            self.metrics = pd.read_csv(metrics_path).iloc[0].to_dict()

        # Load training history if available
        history_path = os.path.join(input_dir, 'temperature_training_history.csv')
        if os.path.exists(history_path):
            history_df = pd.read_csv(history_path)
            self.history = {col: history_df[col].tolist() for col in history_df.columns}

        logger.info(f"Temperature model loaded from {input_dir}")

        return self

if __name__ == "__main__":
    # This module is not meant to be run directly
    logger.info("This module provides the EnhancedTemperatureModel class for temperature prediction.")
