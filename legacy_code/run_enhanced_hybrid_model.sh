#!/bin/bash

# Run enhanced hybrid model for microbial growth rate and optimal temperature prediction

# Set paths
FEATURE_FILE="training_data/combined_features.tsv"
METADATA_FILE="training_data/metadata.tsv"
OUTPUT_DIR="models/enhanced_hybrid"

# Set parameters
N_FEATURES_GROWTH=150
N_FEATURES_TEMP=150
HIDDEN_DIMS="512 256 128 64"
DROPOUT_RATES="0.3 0.4 0.4 0.5"

# Create output directory
mkdir -p $OUTPUT_DIR

# Run enhanced hybrid model
python enhanced_hybrid_model.py \
  --feature_file $FEATURE_FILE \
  --metadata_file $METADATA_FILE \
  --output_dir $OUTPUT_DIR \
  --n_features_growth $N_FEATURES_GROWTH \
  --n_features_temp $N_FEATURES_TEMP \
  --hidden_dims $HIDDEN_DIMS \
  --dropout_rates $DROPOUT_RATES

echo "Enhanced hybrid model training completed"
