#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Train Enhanced Neural Network for Temperature Prediction.
This script trains an enhanced neural network with advanced architecture and One-Cycle Learning Rate Schedule
specifically for optimal temperature prediction.
"""

import os
import argparse
import logging
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from sklearn.preprocessing import RobustScaler

from enhanced_neural_network import EnhancedNeuralNetworkComponent

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_data(feature_file: str, metadata_file: str) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    Load feature and metadata files.
    
    Args:
        feature_file: Path to feature file
        metadata_file: Path to metadata file
        
    Returns:
        Tuple of (features, metadata)
    """
    logger.info(f"Loading features from {feature_file}")
    features = pd.read_csv(feature_file, sep='\t', index_col='genome_id')
    
    logger.info(f"Loading metadata from {metadata_file}")
    metadata = pd.read_csv(metadata_file, sep='\t', index_col='genome_id')
    
    # Get common samples
    common_indices = features.index.intersection(metadata.index)
    logger.info(f"Found {len(common_indices)} common samples")
    
    # Filter to common samples
    features = features.loc[common_indices]
    metadata = metadata.loc[common_indices]
    
    # Check for NaN values in features
    nan_count = features.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in features. Filling with 0.")
        features = features.fillna(0)
    
    # Check for infinite values
    inf_count = np.isinf(features.values).sum()
    if inf_count > 0:
        logger.warning(f"Found {inf_count} infinite values in features. Replacing with max/min values.")
        features = features.replace([np.inf, -np.inf], np.nan)
        features = features.fillna(features.max())
    
    # Check for NaN values in metadata
    nan_count = metadata.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in metadata. Filling with 0.")
        metadata = metadata.fillna(0)
    
    return features, metadata

def prepare_data(
    features: pd.DataFrame, 
    metadata: pd.DataFrame,
    n_features: int = 150, 
    output_dir: Optional[str] = None
) -> Tuple[pd.DataFrame, pd.Series, pd.DataFrame, pd.Series, pd.DataFrame, pd.Series, List[str]]:
    """
    Prepare data for temperature prediction with proper train/validation/test split.

    Args:
        features: Feature DataFrame
        metadata: Metadata DataFrame
        n_features: Number of features to select
        output_dir: Directory to save outputs

    Returns:
        Tuple of (X_train, y_train, X_val, y_val, X_test, y_test, selected_features)
    """
    logger.info("Preparing data for temperature prediction...")

    # Extract target
    y_temp = metadata['optimal_temperature']
    
    # Create bins for stratification
    n_bins = 10
    y_binned = pd.qcut(y_temp, n_bins, labels=False, duplicates='drop')
    
    # Check if we have enough samples in each bin
    bin_counts = pd.Series(y_binned).value_counts()
    min_bin_count = bin_counts.min()
    
    if min_bin_count < 3:
        logger.warning(f"Some bins have very few samples (min: {min_bin_count}). Using fewer bins.")
        n_bins = max(2, min(5, min_bin_count))
        y_binned = pd.qcut(y_temp, n_bins, labels=False, duplicates='drop')
    
    # First split data into train+val and test sets with stratification
    X_train_val, X_test, y_train_val, y_test, y_binned_train_val, _ = train_test_split(
        features, y_temp, y_binned, test_size=0.2, random_state=42, shuffle=True, stratify=y_binned
    )

    # Then split train+val into train and validation sets with stratification
    X_train, X_val, y_train, y_val = train_test_split(
        X_train_val, y_train_val, test_size=0.25, random_state=42, 
        shuffle=True, stratify=y_binned_train_val
    )

    logger.info(f"Data split: Train={X_train.shape[0]} samples, Validation={X_val.shape[0]} samples, Test={X_test.shape[0]} samples")
    
    # Check distribution of target variables in each split
    logger.info(f"Temperature - Train: mean={y_train.mean():.4f}, std={y_train.std():.4f}")
    logger.info(f"Temperature - Validation: mean={y_val.mean():.4f}, std={y_val.std():.4f}")
    logger.info(f"Temperature - Test: mean={y_test.mean():.4f}, std={y_test.std():.4f}")
    
    # Apply target scaling using RobustScaler
    scaler = RobustScaler()
    
    # Reshape for scaler
    y_train_reshaped = y_train.values.reshape(-1, 1)
    y_val_reshaped = y_val.values.reshape(-1, 1)
    y_test_reshaped = y_test.values.reshape(-1, 1)
    
    # Fit scaler on training data only
    scaler.fit(y_train_reshaped)
    
    # Transform all sets
    y_train_scaled = scaler.transform(y_train_reshaped).flatten()
    y_val_scaled = scaler.transform(y_val_reshaped).flatten()
    y_test_scaled = scaler.transform(y_test_reshaped).flatten()
    
    # Convert back to pandas Series with original indices
    y_train = pd.Series(y_train_scaled, index=y_train.index)
    y_val = pd.Series(y_val_scaled, index=y_val.index)
    y_test = pd.Series(y_test_scaled, index=y_test.index)
    
    logger.info("Applied target scaling using RobustScaler")
    
    # For simplicity, we'll use all features for now
    # In a real implementation, you would want to use feature selection here
    selected_features = features.columns.tolist()[:n_features]
    
    # Filter to selected features
    X_train = X_train[selected_features]
    X_val = X_val[selected_features]
    X_test = X_test[selected_features]

    logger.info(f"Prepared data for temperature prediction with {len(selected_features)} features")
    
    # Save scaler if output_dir is provided
    if output_dir:
        import joblib
        os.makedirs(output_dir, exist_ok=True)
        joblib.dump(scaler, os.path.join(output_dir, 'temperature_scaler.joblib'))
        logger.info(f"Saved temperature scaler to {output_dir}")

    return X_train, y_train, X_val, y_val, X_test, y_test, selected_features

def train_model(
    X_train: pd.DataFrame, 
    y_train: pd.Series,
    X_val: pd.DataFrame, 
    y_val: pd.Series,
    hidden_dims: List[int] = [512, 512, 384, 256, 128],
    dropout_rates: List[float] = [0.3, 0.4, 0.4, 0.5, 0.5],
    lr: float = 0.001,
    batch_size: int = 64,
    epochs: int = 100,
    patience: int = 15,
    output_dir: Optional[str] = None
) -> EnhancedNeuralNetworkComponent:
    """
    Train enhanced neural network for temperature prediction.
    
    Args:
        X_train: Training features
        y_train: Training temperature targets
        X_val: Validation features
        y_val: Validation temperature targets
        hidden_dims: List of hidden layer dimensions
        dropout_rates: List of dropout rates
        lr: Learning rate
        batch_size: Batch size
        epochs: Number of epochs
        patience: Early stopping patience
        output_dir: Directory to save outputs
        
    Returns:
        Trained model
    """
    logger.info("Training enhanced neural network for temperature prediction...")
    
    # Initialize model
    model = EnhancedNeuralNetworkComponent(
        hidden_dims=hidden_dims,
        dropout_rates=dropout_rates,
        lr=lr,
        batch_size=batch_size,
        epochs=epochs,
        patience=patience,
        growth_weight=0.0,  # Only care about temperature
        use_batch_norm=True,
        use_gradient_clipping=True,
        max_grad_norm=1.0,
        lr_scheduler='one_cycle'
    )
    
    # Create dummy growth rate targets (not used, but required by the API)
    dummy_growth = pd.Series(np.zeros(len(y_train)), index=y_train.index)
    dummy_growth_val = pd.Series(np.zeros(len(y_val)), index=y_val.index)
    
    # Train model
    history = model.fit(
        X_train, 
        dummy_growth, 
        y_train,
        X_val, 
        dummy_growth_val, 
        y_val,
        output_dir=output_dir
    )
    
    # Plot training history
    if output_dir:
        plt.figure(figsize=(12, 6))
        
        # Plot losses
        plt.subplot(1, 2, 1)
        plt.plot(history['train_temp_loss'], label='Train Loss')
        plt.plot(history['val_temp_loss'], label='Validation Loss')
        plt.xlabel('Epoch')
        plt.ylabel('Loss (MSE)')
        plt.title('Temperature Loss')
        plt.legend()
        
        # Plot learning rate
        plt.subplot(1, 2, 2)
        plt.plot(history['learning_rates'])
        plt.xlabel('Epoch')
        plt.ylabel('Learning Rate')
        plt.title('Learning Rate Schedule')
        
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'temperature_training_history.png'))
    
    return model

def evaluate_model(
    model: EnhancedNeuralNetworkComponent, 
    X_test: pd.DataFrame, 
    y_test: pd.Series,
    output_dir: Optional[str] = None
) -> Dict[str, float]:
    """
    Evaluate model on test data.
    
    Args:
        model: Trained model
        X_test: Test features
        y_test: Test temperature targets
        output_dir: Directory to save outputs
        
    Returns:
        Dictionary of evaluation metrics
    """
    logger.info("Evaluating model on test data...")
    
    # Generate predictions
    _, y_pred = model.predict(X_test)
    
    # Calculate metrics
    mse = mean_squared_error(y_test, y_pred)
    rmse = np.sqrt(mse)
    mae = mean_absolute_error(y_test, y_pred)
    r2 = r2_score(y_test, y_pred)
    
    # Log metrics
    logger.info(f"Temperature prediction metrics - R²: {r2:.4f}, RMSE: {rmse:.4f}, MAE: {mae:.4f}")
    
    # Plot predictions vs actual
    if output_dir:
        plt.figure(figsize=(10, 8))
        plt.scatter(y_test, y_pred, alpha=0.5)
        plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--')
        plt.xlabel('Actual Temperature (Scaled)')
        plt.ylabel('Predicted Temperature (Scaled)')
        plt.title(f'Temperature Predictions (R² = {r2:.4f})')
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'temperature_predictions.png'))
    
    return {
        'MSE': mse,
        'RMSE': rmse,
        'MAE': mae,
        'R2': r2
    }

def main():
    parser = argparse.ArgumentParser(description="Train and evaluate enhanced neural network for temperature prediction")
    parser.add_argument("--feature_file", required=True, help="Path to feature file")
    parser.add_argument("--metadata_file", required=True, help="Path to metadata file")
    parser.add_argument("--output_dir", default="models/enhanced_nn_temperature", help="Directory to save outputs")
    parser.add_argument("--n_features", type=int, default=150, help="Number of features to select")
    parser.add_argument("--hidden_dims", type=str, default="512,512,384,256,128", help="Hidden layer dimensions (comma-separated)")
    parser.add_argument("--dropout_rates", type=str, default="0.3,0.4,0.4,0.5,0.5", help="Dropout rates (comma-separated)")
    parser.add_argument("--learning_rate", type=float, default=0.001, help="Learning rate")
    parser.add_argument("--batch_size", type=int, default=64, help="Batch size")
    parser.add_argument("--epochs", type=int, default=100, help="Number of epochs")
    parser.add_argument("--patience", type=int, default=15, help="Early stopping patience")
    args = parser.parse_args()

    # Parse hidden dimensions and dropout rates
    hidden_dims = [int(dim) for dim in args.hidden_dims.split(',')]
    dropout_rates = [float(rate) for rate in args.dropout_rates.split(',')]

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Load data
    features, metadata = load_data(args.feature_file, args.metadata_file)

    # Prepare data
    X_train, y_train, X_val, y_val, X_test, y_test, selected_features = prepare_data(
        features, metadata, args.n_features, args.output_dir
    )

    # Train model
    model = train_model(
        X_train, y_train,
        X_val, y_val,
        hidden_dims=hidden_dims,
        dropout_rates=dropout_rates,
        lr=args.learning_rate,
        batch_size=args.batch_size,
        epochs=args.epochs,
        patience=args.patience,
        output_dir=args.output_dir
    )

    # Evaluate model
    metrics = evaluate_model(model, X_test, y_test, args.output_dir)

    # Save metrics
    metrics_df = pd.DataFrame([metrics])
    metrics_df.to_csv(os.path.join(args.output_dir, 'temperature_metrics.tsv'), sep='\t', index=False)

    # Print final metrics
    logger.info("Enhanced neural network for temperature prediction - training and evaluation completed")
    logger.info(f"Final metrics - R²: {metrics['R2']:.4f}, RMSE: {metrics['RMSE']:.4f}, MAE: {metrics['MAE']:.4f}")

if __name__ == "__main__":
    main()
