#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Train Improved Hybrid Multi-Task Model with Square Root Transformation.
This script applies square root transformation to growth rate and optimal temperature
before training the improved hybrid multi-task model.
"""

import os
import argparse
import logging
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from sklearn.preprocessing import RobustScaler

from improved_hybrid_multi_task_model import ImprovedHybridMultiTaskModel

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_data(feature_file: str, metadata_file: str) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    Load feature and metadata files.
    
    Args:
        feature_file: Path to feature file
        metadata_file: Path to metadata file
        
    Returns:
        Tuple of (features, metadata)
    """
    logger.info(f"Loading features from {feature_file}")
    features = pd.read_csv(feature_file, sep='\t', index_col='genome_id')
    
    logger.info(f"Loading metadata from {metadata_file}")
    metadata = pd.read_csv(metadata_file, sep='\t', index_col='genome_id')
    
    # Get common samples
    common_indices = features.index.intersection(metadata.index)
    logger.info(f"Found {len(common_indices)} common samples")
    
    # Filter to common samples
    features = features.loc[common_indices]
    metadata = metadata.loc[common_indices]
    
    # Check for NaN values in features
    nan_count = features.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in features. Filling with 0.")
        features = features.fillna(0)
    
    # Check for infinite values
    inf_count = np.isinf(features.values).sum()
    if inf_count > 0:
        logger.warning(f"Found {inf_count} infinite values in features. Replacing with max/min values.")
        features = features.replace([np.inf, -np.inf], np.nan)
        features = features.fillna(features.max())
    
    # Check for NaN values in metadata
    nan_count = metadata.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in metadata. Filling with 0.")
        metadata = metadata.fillna(0)
    
    return features, metadata

def prepare_data_with_sqrt_transform(
    features: pd.DataFrame, 
    metadata: pd.DataFrame,
    n_features: int = 150, 
    output_dir: Optional[str] = None
) -> Tuple[pd.DataFrame, pd.Series, pd.Series, pd.DataFrame, pd.Series, pd.Series, pd.DataFrame, pd.Series, pd.Series, List[str], Dict[str, Dict]]:
    """
    Prepare data for multi-task learning with square root transformation and proper train/validation/test split.

    Args:
        features: Feature DataFrame
        metadata: Metadata DataFrame
        n_features: Number of features to select
        output_dir: Directory to save outputs

    Returns:
        Tuple of (X_train, y_train_growth, y_train_temp, X_val, y_val_growth, y_val_temp, 
                 X_test, y_test_growth, y_test_temp, selected_features, transform_info)
    """
    logger.info("Preparing data for multi-task learning with square root transformation...")

    # Extract targets
    y_growth_original = metadata['growth_rate']
    y_temp_original = metadata['optimal_temperature']
    
    # Apply square root transformation
    logger.info("Applying square root transformation to growth rate and optimal temperature")
    y_growth = np.sqrt(y_growth_original)
    y_temp = np.sqrt(y_temp_original)
    
    # Create bins for stratification based on transformed growth rate
    n_bins = 10
    y_binned = pd.qcut(y_growth, n_bins, labels=False, duplicates='drop')
    
    # Check if we have enough samples in each bin
    bin_counts = pd.Series(y_binned).value_counts()
    min_bin_count = bin_counts.min()
    
    if min_bin_count < 3:
        logger.warning(f"Some bins have very few samples (min: {min_bin_count}). Using fewer bins.")
        n_bins = max(2, min(5, min_bin_count))
        y_binned = pd.qcut(y_growth, n_bins, labels=False, duplicates='drop')
    
    # First split data into train+val and test sets with stratification
    X_train_val, X_test, y_growth_train_val, y_growth_test, y_temp_train_val, y_temp_test, y_binned_train_val, _ = train_test_split(
        features, y_growth, y_temp, y_binned, test_size=0.2, random_state=42, shuffle=True, stratify=y_binned
    )

    # Then split train+val into train and validation sets with stratification
    X_train, X_val, y_growth_train, y_growth_val, y_temp_train, y_temp_val = train_test_split(
        X_train_val, y_growth_train_val, y_temp_train_val, test_size=0.25, random_state=42, 
        shuffle=True, stratify=y_binned_train_val
    )

    logger.info(f"Data split: Train={X_train.shape[0]} samples, Validation={X_val.shape[0]} samples, Test={X_test.shape[0]} samples")
    
    # Check distribution of target variables in each split
    logger.info(f"Growth Rate (sqrt) - Train: mean={y_growth_train.mean():.4f}, std={y_growth_train.std():.4f}")
    logger.info(f"Growth Rate (sqrt) - Validation: mean={y_growth_val.mean():.4f}, std={y_growth_val.std():.4f}")
    logger.info(f"Growth Rate (sqrt) - Test: mean={y_growth_test.mean():.4f}, std={y_growth_test.std():.4f}")
    
    logger.info(f"Temperature (sqrt) - Train: mean={y_temp_train.mean():.4f}, std={y_temp_train.std():.4f}")
    logger.info(f"Temperature (sqrt) - Validation: mean={y_temp_val.mean():.4f}, std={y_temp_val.std():.4f}")
    logger.info(f"Temperature (sqrt) - Test: mean={y_temp_test.mean():.4f}, std={y_temp_test.std():.4f}")
    
    # Apply target scaling using RobustScaler for both targets
    growth_scaler = RobustScaler()
    temp_scaler = RobustScaler()
    
    # Reshape for scaler
    y_growth_train_reshaped = y_growth_train.values.reshape(-1, 1)
    y_growth_val_reshaped = y_growth_val.values.reshape(-1, 1)
    y_growth_test_reshaped = y_growth_test.values.reshape(-1, 1)
    
    y_temp_train_reshaped = y_temp_train.values.reshape(-1, 1)
    y_temp_val_reshaped = y_temp_val.values.reshape(-1, 1)
    y_temp_test_reshaped = y_temp_test.values.reshape(-1, 1)
    
    # Fit scalers on training data only
    growth_scaler.fit(y_growth_train_reshaped)
    temp_scaler.fit(y_temp_train_reshaped)
    
    # Transform all sets
    y_growth_train_scaled = growth_scaler.transform(y_growth_train_reshaped).flatten()
    y_growth_val_scaled = growth_scaler.transform(y_growth_val_reshaped).flatten()
    y_growth_test_scaled = growth_scaler.transform(y_growth_test_reshaped).flatten()
    
    y_temp_train_scaled = temp_scaler.transform(y_temp_train_reshaped).flatten()
    y_temp_val_scaled = temp_scaler.transform(y_temp_val_reshaped).flatten()
    y_temp_test_scaled = temp_scaler.transform(y_temp_test_reshaped).flatten()
    
    # Convert back to pandas Series with original indices
    y_growth_train = pd.Series(y_growth_train_scaled, index=y_growth_train.index)
    y_growth_val = pd.Series(y_growth_val_scaled, index=y_growth_val.index)
    y_growth_test = pd.Series(y_growth_test_scaled, index=y_growth_test.index)
    
    y_temp_train = pd.Series(y_temp_train_scaled, index=y_temp_train.index)
    y_temp_val = pd.Series(y_temp_val_scaled, index=y_temp_val.index)
    y_temp_test = pd.Series(y_temp_test_scaled, index=y_temp_test.index)
    
    logger.info("Applied target scaling using RobustScaler for both targets")
    
    # For simplicity, we'll use all features for now
    # In a real implementation, you would want to use feature selection here
    selected_features = features.columns.tolist()[:n_features]
    
    # Filter to selected features
    X_train = X_train[selected_features]
    X_val = X_val[selected_features]
    X_test = X_test[selected_features]

    logger.info(f"Prepared data for multi-task learning with {len(selected_features)} features")
    
    # Store transformation information
    transform_info = {
        'growth_scaler': growth_scaler,
        'temp_scaler': temp_scaler,
        'sqrt_transform': True
    }
    
    # Save transformation info if output_dir is provided
    if output_dir:
        import joblib
        os.makedirs(output_dir, exist_ok=True)
        joblib.dump(transform_info, os.path.join(output_dir, 'transform_info.joblib'))
        logger.info(f"Saved transformation info to {output_dir}")

    return (
        X_train, y_growth_train, y_temp_train, 
        X_val, y_growth_val, y_temp_val, 
        X_test, y_growth_test, y_temp_test, 
        selected_features, transform_info
    )

def train_model(
    X_train: pd.DataFrame, 
    y_growth_train: pd.Series,
    y_temp_train: pd.Series,
    X_val: pd.DataFrame, 
    y_growth_val: pd.Series,
    y_temp_val: pd.Series,
    nn_hidden_dim: int = 256,
    nn_num_blocks: int = 3,
    nn_dropout: float = 0.3,
    nn_l2_reg: float = 1e-5,
    nn_growth_weight: float = 0.5,
    nn_use_swa: bool = True,
    output_dir: Optional[str] = None
) -> ImprovedHybridMultiTaskModel:
    """
    Train improved hybrid multi-task model.
    
    Args:
        X_train: Training features
        y_growth_train: Training growth rate targets
        y_temp_train: Training temperature targets
        X_val: Validation features
        y_growth_val: Validation growth rate targets
        y_temp_val: Validation temperature targets
        nn_hidden_dim: Size of hidden layers in neural network
        nn_num_blocks: Number of residual blocks in neural network
        nn_dropout: Dropout rate for neural network
        nn_l2_reg: L2 regularization strength for neural network
        nn_growth_weight: Weight for growth rate loss in neural network
        nn_use_swa: Whether to use Stochastic Weight Averaging for neural network
        output_dir: Directory to save outputs
        
    Returns:
        Trained model
    """
    logger.info("Training improved hybrid multi-task model...")
    
    # Initialize model
    model = ImprovedHybridMultiTaskModel(
        nn_hidden_dim=nn_hidden_dim,
        nn_num_blocks=nn_num_blocks,
        nn_dropout=nn_dropout,
        nn_l2_reg=nn_l2_reg,
        nn_lr=0.001,
        nn_batch_size=64,
        nn_epochs=100,
        nn_patience=15,
        nn_growth_weight=nn_growth_weight,
        nn_use_swa=nn_use_swa,
        ensemble_weight_lr=0.01,
        ensemble_weight_epochs=50,
        variance_percentile=25
    )
    
    # Train model
    model.fit(
        X_train, 
        y_growth_train, 
        y_temp_train,
        X_val, 
        y_growth_val, 
        y_temp_val,
        output_dir=output_dir
    )
    
    return model

def evaluate_model(
    model: ImprovedHybridMultiTaskModel, 
    X_test: pd.DataFrame, 
    y_growth_test: pd.Series,
    y_temp_test: pd.Series,
    transform_info: Dict[str, Dict],
    output_dir: Optional[str] = None
) -> Dict[str, Dict[str, float]]:
    """
    Evaluate model on test data.
    
    Args:
        model: Trained model
        X_test: Test features
        y_growth_test: Test growth rate targets
        y_temp_test: Test temperature targets
        transform_info: Dictionary with transformation information
        output_dir: Directory to save outputs
        
    Returns:
        Dictionary of evaluation metrics for each task
    """
    logger.info("Evaluating model on test data...")
    
    # Evaluate model
    metrics = model.evaluate(X_test, y_growth_test, y_temp_test)
    
    # Extract scalers
    growth_scaler = transform_info['growth_scaler']
    temp_scaler = transform_info['temp_scaler']
    
    # Convert metrics to original scale (accounting for both scaling and sqrt transform)
    # First, undo the scaling
    growth_rmse_scaled = metrics['growth_rate']['RMSE']
    growth_mae_scaled = metrics['growth_rate']['MAE']
    temp_rmse_scaled = metrics['temperature']['RMSE']
    temp_mae_scaled = metrics['temperature']['MAE']
    
    # Generate predictions
    y_growth_pred_scaled, y_temp_pred_scaled = model.predict(X_test)
    
    # Inverse transform scaling
    y_growth_test_sqrt = growth_scaler.inverse_transform(y_growth_test.values.reshape(-1, 1)).flatten()
    y_growth_pred_sqrt = growth_scaler.inverse_transform(y_growth_pred_scaled.reshape(-1, 1)).flatten()
    
    y_temp_test_sqrt = temp_scaler.inverse_transform(y_temp_test.values.reshape(-1, 1)).flatten()
    y_temp_pred_sqrt = temp_scaler.inverse_transform(y_temp_pred_scaled.reshape(-1, 1)).flatten()
    
    # Inverse transform sqrt (square the values)
    y_growth_test_orig = y_growth_test_sqrt ** 2
    y_growth_pred_orig = y_growth_pred_sqrt ** 2
    
    y_temp_test_orig = y_temp_test_sqrt ** 2
    y_temp_pred_orig = y_temp_pred_sqrt ** 2
    
    # Calculate metrics in original scale
    growth_metrics_orig = {
        'R2': r2_score(y_growth_test_orig, y_growth_pred_orig),
        'RMSE': np.sqrt(mean_squared_error(y_growth_test_orig, y_growth_pred_orig)),
        'MAE': mean_absolute_error(y_growth_test_orig, y_growth_pred_orig)
    }
    
    temp_metrics_orig = {
        'R2': r2_score(y_temp_test_orig, y_temp_pred_orig),
        'RMSE': np.sqrt(mean_squared_error(y_temp_test_orig, y_temp_pred_orig)),
        'MAE': mean_absolute_error(y_temp_test_orig, y_temp_pred_orig)
    }
    
    # Calculate overall metrics (average R2)
    overall_metrics_orig = {
        'R2': (growth_metrics_orig['R2'] + temp_metrics_orig['R2']) / 2,
        'RMSE': (growth_metrics_orig['RMSE'] + temp_metrics_orig['RMSE']) / 2,
        'MAE': (growth_metrics_orig['MAE'] + temp_metrics_orig['MAE']) / 2
    }
    
    # Log metrics in both scaled and original space
    logger.info(f"Scaled metrics - Growth Rate: R²={metrics['growth_rate']['R2']:.4f}, RMSE={growth_rmse_scaled:.4f}, MAE={growth_mae_scaled:.4f}")
    logger.info(f"Scaled metrics - Temperature: R²={metrics['temperature']['R2']:.4f}, RMSE={temp_rmse_scaled:.4f}, MAE={temp_mae_scaled:.4f}")
    logger.info(f"Scaled metrics - Overall: R²={metrics['overall']['R2']:.4f}")
    
    logger.info(f"Original scale metrics - Growth Rate: R²={growth_metrics_orig['R2']:.4f}, RMSE={growth_metrics_orig['RMSE']:.4f}, MAE={growth_metrics_orig['MAE']:.4f}")
    logger.info(f"Original scale metrics - Temperature: R²={temp_metrics_orig['R2']:.4f}, RMSE={temp_metrics_orig['RMSE']:.4f}, MAE={temp_metrics_orig['MAE']:.4f}")
    logger.info(f"Original scale metrics - Overall: R²={overall_metrics_orig['R2']:.4f}")
    
    # Plot predictions vs actual values
    if output_dir:
        # Growth rate plot (scaled space)
        plt.figure(figsize=(10, 6))
        plt.scatter(y_growth_test, y_growth_pred_scaled, alpha=0.5)
        plt.plot([y_growth_test.min(), y_growth_test.max()], [y_growth_test.min(), y_growth_test.max()], 'r--')
        plt.xlabel('Actual Growth Rate (Scaled)')
        plt.ylabel('Predicted Growth Rate (Scaled)')
        plt.title(f'Growth Rate Predictions - Scaled (R² = {metrics["growth_rate"]["R2"]:.4f})')
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'growth_rate_predictions_scaled.png'))
        
        # Temperature plot (scaled space)
        plt.figure(figsize=(10, 6))
        plt.scatter(y_temp_test, y_temp_pred_scaled, alpha=0.5)
        plt.plot([y_temp_test.min(), y_temp_test.max()], [y_temp_test.min(), y_temp_test.max()], 'r--')
        plt.xlabel('Actual Temperature (Scaled)')
        plt.ylabel('Predicted Temperature (Scaled)')
        plt.title(f'Temperature Predictions - Scaled (R² = {metrics["temperature"]["R2"]:.4f})')
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'temperature_predictions_scaled.png'))
        
        # Growth rate plot (original space)
        plt.figure(figsize=(10, 6))
        plt.scatter(y_growth_test_orig, y_growth_pred_orig, alpha=0.5)
        plt.plot([min(y_growth_test_orig), max(y_growth_test_orig)], [min(y_growth_test_orig), max(y_growth_test_orig)], 'r--')
        plt.xlabel('Actual Growth Rate (Original)')
        plt.ylabel('Predicted Growth Rate (Original)')
        plt.title(f'Growth Rate Predictions - Original (R² = {growth_metrics_orig["R2"]:.4f})')
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'growth_rate_predictions_original.png'))
        
        # Temperature plot (original space)
        plt.figure(figsize=(10, 6))
        plt.scatter(y_temp_test_orig, y_temp_pred_orig, alpha=0.5)
        plt.plot([min(y_temp_test_orig), max(y_temp_test_orig)], [min(y_temp_test_orig), max(y_temp_test_orig)], 'r--')
        plt.xlabel('Actual Temperature (Original)')
        plt.ylabel('Predicted Temperature (Original)')
        plt.title(f'Temperature Predictions - Original (R² = {temp_metrics_orig["R2"]:.4f})')
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'temperature_predictions_original.png'))
    
    # Combine metrics
    all_metrics = {
        'scaled': {
            'growth_rate': metrics['growth_rate'],
            'temperature': metrics['temperature'],
            'overall': metrics['overall']
        },
        'original': {
            'growth_rate': growth_metrics_orig,
            'temperature': temp_metrics_orig,
            'overall': overall_metrics_orig
        }
    }
    
    return all_metrics

def main():
    parser = argparse.ArgumentParser(description="Train and evaluate improved hybrid multi-task model with square root transformation")
    parser.add_argument("--feature_file", required=True, help="Path to feature file")
    parser.add_argument("--metadata_file", required=True, help="Path to metadata file")
    parser.add_argument("--output_dir", default="models/improved_hybrid_sqrt", help="Directory to save outputs")
    parser.add_argument("--n_features", type=int, default=150, help="Number of features to select")
    parser.add_argument("--nn_hidden_dim", type=int, default=256, help="Size of hidden layers in neural network")
    parser.add_argument("--nn_num_blocks", type=int, default=3, help="Number of residual blocks in neural network")
    parser.add_argument("--nn_dropout", type=float, default=0.3, help="Dropout rate for neural network")
    parser.add_argument("--nn_l2_reg", type=float, default=1e-5, help="L2 regularization strength for neural network")
    parser.add_argument("--nn_growth_weight", type=float, default=0.5, help="Weight for growth rate loss in neural network")
    parser.add_argument("--nn_use_swa", action="store_true", help="Use Stochastic Weight Averaging for neural network")
    args = parser.parse_args()

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Load data
    features, metadata = load_data(args.feature_file, args.metadata_file)

    # Prepare data with square root transformation and proper train/validation/test split
    (
        X_train, y_growth_train, y_temp_train, 
        X_val, y_growth_val, y_temp_val, 
        X_test, y_growth_test, y_temp_test, 
        selected_features, transform_info
    ) = prepare_data_with_sqrt_transform(
        features, metadata, args.n_features, args.output_dir
    )

    # Train model
    model = train_model(
        X_train, y_growth_train, y_temp_train,
        X_val, y_growth_val, y_temp_val,
        nn_hidden_dim=args.nn_hidden_dim,
        nn_num_blocks=args.nn_num_blocks,
        nn_dropout=args.nn_dropout,
        nn_l2_reg=args.nn_l2_reg,
        nn_growth_weight=args.nn_growth_weight,
        nn_use_swa=args.nn_use_swa,
        output_dir=args.output_dir
    )

    # Evaluate model on the test set
    metrics = evaluate_model(
        model, 
        X_test, 
        y_growth_test, 
        y_temp_test,
        transform_info,
        args.output_dir
    )

    # Print final metrics
    logger.info("Improved hybrid multi-task model with sqrt transformation - training and evaluation completed")
    logger.info(f"Final metrics (scaled) - Overall R²: {metrics['scaled']['overall']['R2']:.4f}")
    logger.info(f"Final metrics (scaled) - Growth Rate R²: {metrics['scaled']['growth_rate']['R2']:.4f}")
    logger.info(f"Final metrics (scaled) - Temperature R²: {metrics['scaled']['temperature']['R2']:.4f}")
    
    logger.info(f"Final metrics (original) - Overall R²: {metrics['original']['overall']['R2']:.4f}")
    logger.info(f"Final metrics (original) - Growth Rate R²: {metrics['original']['growth_rate']['R2']:.4f}")
    logger.info(f"Final metrics (original) - Temperature R²: {metrics['original']['temperature']['R2']:.4f}")

if __name__ == "__main__":
    main()
