#!/bin/bash

# DeepMu: Training Script with Improved Temperature Model (Version 2)
# This script uses the existing final_hybrid_model.py but with improved parameters
# for temperature prediction, focusing on reducing overfitting and improving test performance.

# Set a clean output directory
OUTPUT_DIR="models/deepmu_improved_temp_model_v2"

# Create output directory
mkdir -p "$OUTPUT_DIR"

# Define color codes for output formatting
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Display initial message
echo -e "${BLUE}${BOLD}========================================================${NC}"
echo -e "${BLUE}${BOLD}   DeepMu - Improved Temperature Model V2   ${NC}"
echo -e "${BLUE}${BOLD}========================================================${NC}"
echo -e "${GREEN}This script uses improved parameters for temperature prediction:${NC}"
echo -e "  1. ${YELLOW}Enhanced regularization to prevent overfitting${NC}"
echo -e "  2. ${YELLOW}More aggressive early stopping${NC}"
echo -e "  3. ${YELLOW}Balanced architecture with optimal complexity${NC}"
echo -e "  4. ${YELLOW}Ensemble approach with multiple seeds${NC}"
echo -e ""
echo -e "${YELLOW}Output will be saved to:${NC} ${OUTPUT_DIR}"
echo ""

# Check if prerequisites are available
echo -e "${YELLOW}Checking prerequisites...${NC}"
if [ ! -f "./training_data/combined_features.tsv" ]; then
    echo -e "${RED}Error: Feature file not found. Please ensure './training_data/combined_features.tsv' exists.${NC}"
    exit 1
fi

if [ ! -f "./training_data/metadata.tsv" ]; then
    echo -e "${RED}Error: Metadata file not found. Please ensure './training_data/metadata.tsv' exists.${NC}"
    exit 1
fi

echo -e "${GREEN}All prerequisites found.${NC}"
echo ""

# Step 1: Filter the dataset to remove unreliable temperature data
echo -e "${YELLOW}Step 1: Filtering dataset to remove unreliable temperature data...${NC}"
python improved_temp_data_filter.py \
    --feature-file "./training_data/combined_features.tsv" \
    --metadata-file "./training_data/metadata.tsv" \
    --output-dir "$OUTPUT_DIR/filtered_data" \
    --reliability-threshold 0.65 \
    --prediction-error-threshold 1.8

# Check if filtering was successful
if [ $? -eq 0 ]; then
    echo -e "${GREEN}Dataset filtering completed. Using filtered datasets for training.${NC}"
    FEATURES_FILE="${OUTPUT_DIR}/filtered_data/filtered_features.tsv"
    METADATA_FILE="${OUTPUT_DIR}/filtered_data/filtered_metadata.tsv"
else
    echo -e "${RED}Error: Dataset filtering failed. Exiting.${NC}"
    exit 1
fi

# Function to train a model with a specific seed
train_model_with_seed() {
    local SEED=$1
    local MODEL_DIR="${OUTPUT_DIR}/seed_${SEED}"
    
    mkdir -p "$MODEL_DIR"
    
    echo -e "\n${BLUE}${BOLD}========================================================${NC}"
    echo -e "${BLUE}${BOLD}   Training Model with Seed ${SEED}   ${NC}"
    echo -e "${BLUE}${BOLD}========================================================${NC}"
    
    # Set parameters for training with enhanced regularization
    HIDDEN_DIMS="512,512,384,384,256,256,128"  # More balanced architecture
    DROPOUT_RATES="0.3,0.35,0.4,0.45,0.5,0.5,0.5"  # Higher dropout rates
    MAX_EPOCHS=600  # Fewer epochs to prevent overfitting
    PATIENCE=40  # More aggressive early stopping
    BATCH_SIZE=32  # Smaller batch size for better generalization
    N_GROWTH_FEATURES=150  # Fewer features for growth rate
    N_TEMP_FEATURES=800  # Fewer features for temperature to reduce overfitting
    L1_LAMBDA="5e-6"  # Increased L1 regularization
    WEIGHT_DECAY="5e-6"  # Increased weight decay
    LEARNING_RATE="0.0003"  # Lower learning rate for better convergence
    
    # Set environment variables for deterministic behavior
    export PYTHONHASHSEED=$SEED
    export TORCH_CUDNN_DETERMINISTIC=1
    export TORCH_DETERMINISTIC=1
    export CUBLAS_WORKSPACE_CONFIG=:4096:8
    
    # Run the Python script with optimized parameters
    python final_hybrid_model.py \
        --feature_file "$FEATURES_FILE" \
        --metadata_file "$METADATA_FILE" \
        --output_dir "$MODEL_DIR" \
        --n_growth_features $N_GROWTH_FEATURES \
        --n_temp_features $N_TEMP_FEATURES \
        --seed $SEED \
        --lr $LEARNING_RATE \
        --batch_size $BATCH_SIZE \
        --patience $PATIENCE \
        --epochs $MAX_EPOCHS \
        --l1_lambda $L1_LAMBDA \
        --weight_decay $WEIGHT_DECAY \
        --mse_l1_ratio 0.95 \
        --hidden_dims "$HIDDEN_DIMS" \
        --dropout_rates "$DROPOUT_RATES" \
        --activation "leaky_relu" \
        --use_batch_norm \
        --use_residual \
        --use_attention \
        --use_one_cycle_lr \
        --save_checkpoints \
        --missing_threshold 0.5 \
        --temp_transform "none" \
        --growth_transform "log2" \
        --verbose 2>&1 | tee "${MODEL_DIR}/training.log"
    
    # Check if training was successful
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}Training completed successfully for seed ${SEED}.${NC}"
        return 0
    else
        echo -e "${RED}Error: Training failed for seed ${SEED}.${NC}"
        return 1
    fi
}

# Train models with different seeds
SEEDS=("42" "123" "456" "789" "101112")
SUCCESS=true

for SEED in "${SEEDS[@]}"; do
    train_model_with_seed $SEED
    if [ $? -ne 0 ]; then
        SUCCESS=false
    fi
done

# Create ensemble model script
echo -e "${YELLOW}Creating ensemble model script...${NC}"

cat > "$OUTPUT_DIR/ensemble_predict.py" << 'EOF'
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Ensemble Prediction for DeepMu Temperature Models.

This script combines predictions from multiple models trained with different seeds
to create an ensemble prediction with improved generalization.
"""

import os
import sys
import argparse
import logging
import numpy as np
import pandas as pd
import pickle
from typing import List, Dict, Tuple, Optional

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_model(model_path: str):
    """Load a trained model from a pickle file."""
    logger.info(f"Loading model from {model_path}")
    with open(model_path, 'rb') as f:
        model = pickle.load(f)
    return model

def ensemble_predict(
    models_dir: str,
    feature_file: str,
    output_file: str,
    seeds: List[str] = ["42", "123", "456", "789", "101112"],
    weights: Optional[List[float]] = None
):
    """
    Make ensemble predictions using models trained with different seeds.
    
    Args:
        models_dir: Directory containing model subdirectories
        feature_file: Path to feature file
        output_file: Path to output file
        seeds: List of seeds used for training
        weights: Optional weights for each model (if None, equal weights are used)
    """
    # Load features
    logger.info(f"Loading features from {feature_file}")
    features = pd.read_csv(feature_file, sep='\t', index_col='genome_id')
    
    # Initialize arrays for predictions
    temp_predictions = []
    growth_predictions = []
    valid_models = []
    
    # Load models and make predictions
    for seed in seeds:
        model_path = os.path.join(models_dir, f"seed_{seed}", "model.pkl")
        if not os.path.exists(model_path):
            logger.warning(f"Model for seed {seed} not found at {model_path}")
            continue
        
        model = load_model(model_path)
        temp_pred, growth_pred = model.predict(features)
        
        temp_predictions.append(temp_pred)
        growth_predictions.append(growth_pred)
        valid_models.append(seed)
    
    if not valid_models:
        logger.error("No valid models found. Exiting.")
        return 1
    
    logger.info(f"Loaded {len(valid_models)} models for ensemble prediction")
    
    # Set weights
    if weights is None:
        weights = [1.0 / len(valid_models)] * len(valid_models)
    else:
        # Normalize weights
        weights = [w / sum(weights) for w in weights]
    
    # Combine predictions
    temp_predictions = np.array(temp_predictions)
    growth_predictions = np.array(growth_predictions)
    
    ensemble_temp = np.zeros(temp_predictions.shape[1])
    ensemble_growth = np.zeros(growth_predictions.shape[1])
    
    for i, weight in enumerate(weights):
        ensemble_temp += weight * temp_predictions[i]
        ensemble_growth += weight * growth_predictions[i]
    
    # Create output DataFrame
    results = pd.DataFrame(index=features.index)
    results['predicted_temperature'] = ensemble_temp
    results['predicted_growth_rate'] = ensemble_growth
    
    # Save results
    logger.info(f"Saving ensemble predictions to {output_file}")
    results.to_csv(output_file, sep='\t')
    
    logger.info("Ensemble prediction completed successfully")
    return 0

def main():
    parser = argparse.ArgumentParser(description='Ensemble Prediction for DeepMu Temperature Models')
    parser.add_argument('--models-dir', type=str, required=True, help='Directory containing model subdirectories')
    parser.add_argument('--feature-file', type=str, required=True, help='Path to feature file')
    parser.add_argument('--output-file', type=str, required=True, help='Path to output file')
    parser.add_argument('--seeds', type=str, default="42,123,456,789,101112", help='Comma-separated list of seeds')
    
    args = parser.parse_args()
    
    seeds = args.seeds.split(',')
    
    try:
        return ensemble_predict(args.models_dir, args.feature_file, args.output_file, seeds)
    except Exception as e:
        logger.error(f"Error making ensemble predictions: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == '__main__':
    sys.exit(main())
EOF

chmod +x "$OUTPUT_DIR/ensemble_predict.py"
echo -e "${GREEN}Created ensemble prediction script at $OUTPUT_DIR/ensemble_predict.py${NC}"

# Final status
if $SUCCESS; then
    echo ""
    echo -e "${GREEN}${BOLD}========================================================${NC}"
    echo -e "${GREEN}${BOLD}   All training completed successfully!   ${NC}"
    echo -e "${GREEN}${BOLD}========================================================${NC}"
    echo ""
    echo -e "${GREEN}Models saved to:${NC} ${OUTPUT_DIR}"
    echo -e "${YELLOW}To use the ensemble model, run:${NC}"
    echo -e "  python $OUTPUT_DIR/ensemble_predict.py --models-dir $OUTPUT_DIR --feature-file <features.tsv> --output-file <predictions.tsv>"
else
    echo ""
    echo -e "${RED}${BOLD}========================================================${NC}"
    echo -e "${RED}${BOLD}   Some training tasks failed!   ${NC}"
    echo -e "${RED}${BOLD}========================================================${NC}"
    echo ""
    echo -e "${RED}Please check the error messages above for details.${NC}"
fi

echo ""
echo -e "${GREEN}Done.${NC}"
