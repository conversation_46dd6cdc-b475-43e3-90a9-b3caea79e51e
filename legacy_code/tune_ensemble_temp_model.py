#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Hyperparameter Tuning for Ensemble Temperature Model.

This script uses Optuna to optimize the hyperparameters of both the neural network
and random forest components of the ensemble temperature model.
"""

import os
import argparse
import logging
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import TensorDataset, DataLoader
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestRegressor
import optuna
from optuna.pruners import MedianPruner
from optuna.samplers import TPESampler
import joblib
from typing import Dict, List, Tuple, Union, Optional
import time
import matplotlib.pyplot as plt
import seaborn as sns

# Import the enhanced temperature DNN
from enhanced_temperature_dnn import EnhancedTemperatureDNN

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Set device
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

def load_data(feature_file: str, metadata_file: str, n_features: int = 1000) -> Dict:
    """
    Load and prepare data for model training.

    Args:
        feature_file: Path to feature file
        metadata_file: Path to metadata file
        n_features: Number of features to select

    Returns:
        Dictionary containing prepared data
    """
    logger.info(f"Loading features from {feature_file}")
    features = pd.read_csv(feature_file, sep='\t', index_col='genome_id')

    logger.info(f"Loading metadata from {metadata_file}")
    metadata = pd.read_csv(metadata_file, sep='\t', index_col='genome_id')

    # Get common samples
    common_indices = features.index.intersection(metadata.index)
    logger.info(f"Found {len(common_indices)} common samples")

    features = features.loc[common_indices]
    temperatures = metadata.loc[common_indices, 'optimal_temperature']

    # Handle missing values
    features = features.fillna(0)

    # Select top features by variance
    if n_features and features.shape[1] > n_features:
        logger.info(f"Selecting top {n_features} features by variance")
        feature_variance = features.var().sort_values(ascending=False)
        selected_features = feature_variance.index[:n_features].tolist()
        features = features[selected_features]

    # Split data
    X_train, X_val, y_train, y_val = train_test_split(
        features.values, temperatures.values,
        test_size=0.2,
        random_state=42
    )

    logger.info(f"Training set: {X_train.shape[0]} samples, Validation set: {X_val.shape[0]} samples")

    # Scale features
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_val_scaled = scaler.transform(X_val)

    return {
        'X_train': X_train,
        'X_val': X_val,
        'y_train': y_train,
        'y_val': y_val,
        'X_train_scaled': X_train_scaled,
        'X_val_scaled': X_val_scaled,
        'scaler': scaler,
        'features': features,
        'temperatures': temperatures
    }

def train_dnn(trial, X_train_scaled, y_train, X_val_scaled, y_val) -> Tuple[EnhancedTemperatureDNN, Dict[str, float]]:
    """
    Train a neural network model with hyperparameters suggested by Optuna.

    Args:
        trial: Optuna trial object
        X_train_scaled: Scaled training features
        y_train: Training temperatures
        X_val_scaled: Scaled validation features
        y_val: Validation temperatures

    Returns:
        Tuple of (trained_model, metrics_dict)
    """
    # Suggest hyperparameters
    n_layers = trial.suggest_int('n_layers', 3, 6)
    hidden_dims = []
    dropout_rates = []

    # First layer size
    first_layer_size = trial.suggest_categorical('first_layer_size', [512, 640, 768, 896, 1024])
    hidden_dims.append(first_layer_size)
    dropout_rates.append(trial.suggest_float('dropout_1', 0.1, 0.3))

    # Subsequent layers
    for i in range(1, n_layers):
        # Layer size decreases with depth
        layer_size = trial.suggest_int(f'layer_{i}_size',
                                      int(hidden_dims[i-1] * 0.5),
                                      int(hidden_dims[i-1] * 0.8))
        hidden_dims.append(layer_size)

        # Dropout rate increases with depth
        dropout_rate = trial.suggest_float(f'dropout_{i+1}', 0.2, 0.5)
        dropout_rates.append(dropout_rate)

    # Other hyperparameters
    batch_size = trial.suggest_categorical('batch_size', [32, 64, 128])
    learning_rate = trial.suggest_float('learning_rate', 1e-4, 1e-2, log=True)
    weight_decay = trial.suggest_float('weight_decay', 1e-6, 1e-4, log=True)
    activation = trial.suggest_categorical('activation', ['relu', 'leaky_relu', 'gelu'])
    use_batch_norm = trial.suggest_categorical('use_batch_norm', [True, False])
    use_residual = trial.suggest_categorical('use_residual', [True, False])
    use_attention = trial.suggest_categorical('use_attention', [True, False])

    if use_attention:
        # Ensure number of attention heads divides the input dimension evenly
        valid_heads = [h for h in range(2, 9) if X_train_scaled.shape[1] % h == 0]
        if not valid_heads:
            # If no valid heads, default to 4 and adjust the model to make it work
            attention_heads = 4
            logger.warning(f"No valid attention heads found for input dim {X_train_scaled.shape[1]}. Using 4 and will adjust model.")
        else:
            attention_heads = trial.suggest_categorical('attention_heads', valid_heads)
    else:
        attention_heads = 4

    # Convert data to PyTorch tensors
    X_train_tensor = torch.FloatTensor(X_train_scaled).to(device)
    y_train_tensor = torch.FloatTensor(y_train.reshape(-1, 1)).to(device)
    X_val_tensor = torch.FloatTensor(X_val_scaled).to(device)
    y_val_tensor = torch.FloatTensor(y_val.reshape(-1, 1)).to(device)

    # Create datasets and dataloaders
    train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)

    val_dataset = TensorDataset(X_val_tensor, y_val_tensor)
    val_loader = DataLoader(val_dataset, batch_size=batch_size*2)

    # Initialize model
    model = EnhancedTemperatureDNN(
        input_dim=X_train_scaled.shape[1],
        hidden_dims=hidden_dims,
        dropout_rates=dropout_rates,
        use_batch_norm=use_batch_norm,
        activation=activation,
        use_residual=use_residual,
        use_attention=use_attention,
        attention_heads=attention_heads
    ).to(device)

    # Initialize optimizer and scheduler
    optimizer = torch.optim.Adam(model.parameters(), lr=learning_rate, weight_decay=weight_decay)
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=0.5, patience=5
    )

    # Training loop
    n_epochs = 100  # Limit epochs for hyperparameter search
    patience = 10
    best_val_loss = float('inf')
    best_model_state = None
    early_stopping_counter = 0

    for epoch in range(n_epochs):
        # Training phase
        model.train()
        train_loss = 0.0

        for X_batch, y_batch in train_loader:
            # Zero gradients
            optimizer.zero_grad()

            # Forward pass
            y_pred = model(X_batch)
            # Ensure consistent shapes for loss calculation
            if y_pred.ndim == 1:
                y_pred = y_pred.unsqueeze(1)
            loss = F.mse_loss(y_pred, y_batch)

            # Backward pass
            loss.backward()

            # Gradient clipping
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)

            # Update weights
            optimizer.step()

            # Accumulate loss
            train_loss += loss.item() * len(y_batch)

        # Calculate average training loss
        train_loss /= len(train_loader.dataset)

        # Validation phase
        model.eval()
        val_loss = 0.0
        val_preds = []
        val_targets = []

        with torch.no_grad():
            for X_batch, y_batch in val_loader:
                # Forward pass
                y_pred = model(X_batch)
                # Ensure consistent shapes for loss calculation
                if y_pred.ndim == 1:
                    y_pred = y_pred.unsqueeze(1)
                loss = F.mse_loss(y_pred, y_batch)

                # Accumulate loss
                val_loss += loss.item() * len(y_batch)

                # Store predictions and targets
                val_preds.extend(y_pred.cpu().numpy().flatten())
                val_targets.extend(y_batch.cpu().numpy().flatten())

        # Calculate average validation loss
        val_loss /= len(val_loader.dataset)

        # Update scheduler
        scheduler.step(val_loss)

        # Report intermediate metric to Optuna
        if epoch % 10 == 0 or epoch == n_epochs - 1:
            # Calculate R² score
            val_r2 = r2_score(val_targets, val_preds)
            trial.report(val_r2, epoch)

            # Handle pruning
            if trial.should_prune():
                raise optuna.exceptions.TrialPruned()

        # Check for improvement
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            best_model_state = model.state_dict().copy()
            early_stopping_counter = 0
        else:
            early_stopping_counter += 1

            if early_stopping_counter >= patience:
                break

    # Load best model
    model.load_state_dict(best_model_state)

    # Final evaluation
    model.eval()
    with torch.no_grad():
        train_preds = model(X_train_tensor).cpu().numpy().flatten()
        val_preds = model(X_val_tensor).cpu().numpy().flatten()

    # Calculate final metrics
    train_r2 = r2_score(y_train, train_preds)
    train_rmse = np.sqrt(mean_squared_error(y_train, train_preds))

    val_r2 = r2_score(y_val, val_preds)
    val_rmse = np.sqrt(mean_squared_error(y_val, val_preds))

    metrics = {
        'train_r2': train_r2,
        'train_rmse': train_rmse,
        'val_r2': val_r2,
        'val_rmse': val_rmse
    }

    return model, metrics

def train_rf(trial, X_train, y_train, X_val, y_val) -> Tuple[RandomForestRegressor, Dict[str, float]]:
    """
    Train a random forest model with hyperparameters suggested by Optuna.

    Args:
        trial: Optuna trial object
        X_train: Training features
        y_train: Training temperatures
        X_val: Validation features
        y_val: Validation temperatures

    Returns:
        Tuple of (trained_model, metrics_dict)
    """
    # Suggest hyperparameters
    n_estimators = trial.suggest_int('rf_n_estimators', 100, 500)
    max_depth = trial.suggest_int('rf_max_depth', 10, 30)
    min_samples_split = trial.suggest_int('rf_min_samples_split', 2, 10)
    min_samples_leaf = trial.suggest_int('rf_min_samples_leaf', 1, 5)
    max_features = trial.suggest_categorical('rf_max_features', ['sqrt', 'log2', None])
    bootstrap = trial.suggest_categorical('rf_bootstrap', [True, False])

    # Initialize model
    model = RandomForestRegressor(
        n_estimators=n_estimators,
        max_depth=max_depth,
        min_samples_split=min_samples_split,
        min_samples_leaf=min_samples_leaf,
        max_features=max_features,
        bootstrap=bootstrap,
        n_jobs=-1,
        random_state=42
    )

    # Train model
    model.fit(X_train, y_train)

    # Make predictions
    train_preds = model.predict(X_train)
    val_preds = model.predict(X_val)

    # Calculate metrics
    train_r2 = r2_score(y_train, train_preds)
    train_rmse = np.sqrt(mean_squared_error(y_train, train_preds))

    val_r2 = r2_score(y_val, val_preds)
    val_rmse = np.sqrt(mean_squared_error(y_val, val_preds))

    metrics = {
        'train_r2': train_r2,
        'train_rmse': train_rmse,
        'val_r2': val_r2,
        'val_rmse': val_rmse
    }

    return model, metrics

def objective(trial, data: Dict) -> float:
    """
    Objective function for Optuna optimization.

    Args:
        trial: Optuna trial object
        data: Dictionary containing prepared data

    Returns:
        Validation R² score of the ensemble model
    """
    # Train neural network
    dnn_model, dnn_metrics = train_dnn(
        trial,
        data['X_train_scaled'],
        data['y_train'],
        data['X_val_scaled'],
        data['y_val']
    )

    # Train random forest
    rf_model, rf_metrics = train_rf(
        trial,
        data['X_train'],
        data['y_train'],
        data['X_val'],
        data['y_val']
    )

    # Suggest ensemble weights
    dnn_weight = trial.suggest_float('dnn_weight', 0.0, 1.0)
    rf_weight = 1.0 - dnn_weight

    # Make ensemble predictions
    dnn_val_preds = dnn_model(torch.FloatTensor(data['X_val_scaled']).to(device)).detach().cpu().numpy().flatten()
    rf_val_preds = rf_model.predict(data['X_val'])

    ensemble_val_preds = dnn_weight * dnn_val_preds + rf_weight * rf_val_preds

    # Calculate ensemble metrics
    ensemble_val_r2 = r2_score(data['y_val'], ensemble_val_preds)

    return ensemble_val_r2

def optimize_ensemble_model(data: Dict, n_trials: int = 100, output_dir: str = None) -> Dict:
    """
    Optimize the ensemble model using Optuna.

    Args:
        data: Dictionary containing prepared data
        n_trials: Number of optimization trials
        output_dir: Directory to save results

    Returns:
        Dictionary containing the best parameters and models
    """
    # Create output directory if provided
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)

        # Set up logging to file
        file_handler = logging.FileHandler(os.path.join(output_dir, "optimization.log"))
        file_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
        logger.addHandler(file_handler)

    # Create Optuna study
    study = optuna.create_study(
        direction='maximize',
        sampler=TPESampler(seed=42),
        pruner=MedianPruner(n_startup_trials=5, n_warmup_steps=10)
    )

    # Optimize
    logger.info(f"Starting optimization with {n_trials} trials")
    study.optimize(lambda trial: objective(trial, data), n_trials=n_trials)

    # Get best parameters
    best_params = study.best_params
    best_value = study.best_value

    logger.info(f"Best validation R²: {best_value:.6f}")
    logger.info(f"Best parameters: {best_params}")

    # Train final models with best parameters
    logger.info("Training final models with best parameters")

    # Neural network parameters
    dnn_params = {k: v for k, v in best_params.items() if not k.startswith('rf_') and k != 'dnn_weight'}

    # Extract hidden dimensions and dropout rates
    n_layers = dnn_params.pop('n_layers')
    hidden_dims = []
    dropout_rates = []

    # First layer
    hidden_dims.append(dnn_params.pop('first_layer_size'))
    dropout_rates.append(dnn_params.pop('dropout_1'))

    # Subsequent layers
    for i in range(1, n_layers):
        hidden_dims.append(dnn_params.pop(f'layer_{i}_size'))
        dropout_rates.append(dnn_params.pop(f'dropout_{i+1}'))

    # Random forest parameters
    rf_params = {k[3:]: v for k, v in best_params.items() if k.startswith('rf_')}

    # Ensemble weight
    dnn_weight = best_params['dnn_weight']
    rf_weight = 1.0 - dnn_weight

    # Initialize models
    dnn_model = EnhancedTemperatureDNN(
        input_dim=data['X_train_scaled'].shape[1],
        hidden_dims=hidden_dims,
        dropout_rates=dropout_rates,
        use_batch_norm=dnn_params['use_batch_norm'],
        activation=dnn_params['activation'],
        use_residual=dnn_params['use_residual'],
        use_attention=dnn_params['use_attention'],
        attention_heads=dnn_params.get('attention_heads', 4)
    ).to(device)

    rf_model = RandomForestRegressor(
        n_estimators=rf_params['n_estimators'],
        max_depth=rf_params['max_depth'],
        min_samples_split=rf_params['min_samples_split'],
        min_samples_leaf=rf_params['min_samples_leaf'],
        max_features=rf_params['max_features'],
        bootstrap=rf_params['bootstrap'],
        n_jobs=-1,
        random_state=42
    )

    # Train neural network
    logger.info("Training final neural network model")

    # Convert data to PyTorch tensors
    X_train_tensor = torch.FloatTensor(data['X_train_scaled']).to(device)
    y_train_tensor = torch.FloatTensor(data['y_train'].reshape(-1, 1)).to(device)
    X_val_tensor = torch.FloatTensor(data['X_val_scaled']).to(device)
    y_val_tensor = torch.FloatTensor(data['y_val'].reshape(-1, 1)).to(device)

    # Create datasets and dataloaders
    train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
    train_loader = DataLoader(train_dataset, batch_size=dnn_params['batch_size'], shuffle=True)

    val_dataset = TensorDataset(X_val_tensor, y_val_tensor)
    val_loader = DataLoader(val_dataset, batch_size=dnn_params['batch_size']*2)

    # Initialize optimizer and scheduler
    optimizer = torch.optim.Adam(
        dnn_model.parameters(),
        lr=dnn_params['learning_rate'],
        weight_decay=dnn_params['weight_decay']
    )
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=0.5, patience=5
    )

    # Training loop
    n_epochs = 300
    patience = 30
    best_val_loss = float('inf')
    best_model_state = None
    early_stopping_counter = 0

    for epoch in range(n_epochs):
        # Training phase
        dnn_model.train()
        train_loss = 0.0

        for X_batch, y_batch in train_loader:
            # Zero gradients
            optimizer.zero_grad()

            # Forward pass
            y_pred = dnn_model(X_batch)
            # Ensure consistent shapes for loss calculation
            if y_pred.ndim == 1:
                y_pred = y_pred.unsqueeze(1)
            loss = F.mse_loss(y_pred, y_batch)

            # Backward pass
            loss.backward()

            # Gradient clipping
            torch.nn.utils.clip_grad_norm_(dnn_model.parameters(), max_norm=1.0)

            # Update weights
            optimizer.step()

            # Accumulate loss
            train_loss += loss.item() * len(y_batch)

        # Calculate average training loss
        train_loss /= len(train_loader.dataset)

        # Validation phase
        dnn_model.eval()
        val_loss = 0.0

        with torch.no_grad():
            for X_batch, y_batch in val_loader:
                # Forward pass
                y_pred = dnn_model(X_batch)
                # Ensure consistent shapes for loss calculation
                if y_pred.ndim == 1:
                    y_pred = y_pred.unsqueeze(1)
                loss = F.mse_loss(y_pred, y_batch)

                # Accumulate loss
                val_loss += loss.item() * len(y_batch)

        # Calculate average validation loss
        val_loss /= len(val_loader.dataset)

        # Update scheduler
        scheduler.step(val_loss)

        # Log progress
        if epoch % 10 == 0:
            logger.info(f"Epoch {epoch+1}/{n_epochs}")
            logger.info(f"  Train Loss: {train_loss:.6f}")
            logger.info(f"  Val Loss: {val_loss:.6f}")
            logger.info(f"  Learning Rate: {optimizer.param_groups[0]['lr']:.6f}")

        # Check for improvement
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            best_model_state = dnn_model.state_dict().copy()
            early_stopping_counter = 0
            logger.info(f"  Validation loss improved to {val_loss:.6f}")
        else:
            early_stopping_counter += 1

            if early_stopping_counter >= patience:
                logger.info(f"Early stopping triggered after {epoch+1} epochs")
                break

    # Load best model
    dnn_model.load_state_dict(best_model_state)

    # Train random forest
    logger.info("Training final random forest model")
    rf_model.fit(data['X_train'], data['y_train'])

    # Make predictions
    dnn_model.eval()
    with torch.no_grad():
        dnn_train_preds = dnn_model(X_train_tensor).cpu().numpy().flatten()
        dnn_val_preds = dnn_model(X_val_tensor).cpu().numpy().flatten()

    rf_train_preds = rf_model.predict(data['X_train'])
    rf_val_preds = rf_model.predict(data['X_val'])

    # Make ensemble predictions
    ensemble_train_preds = dnn_weight * dnn_train_preds + rf_weight * rf_train_preds
    ensemble_val_preds = dnn_weight * dnn_val_preds + rf_weight * rf_val_preds

    # Calculate metrics
    dnn_train_r2 = r2_score(data['y_train'], dnn_train_preds)
    dnn_val_r2 = r2_score(data['y_val'], dnn_val_preds)

    rf_train_r2 = r2_score(data['y_train'], rf_train_preds)
    rf_val_r2 = r2_score(data['y_val'], rf_val_preds)

    ensemble_train_r2 = r2_score(data['y_train'], ensemble_train_preds)
    ensemble_val_r2 = r2_score(data['y_val'], ensemble_val_preds)

    logger.info("Final Model Metrics:")
    logger.info(f"  DNN - Train R²: {dnn_train_r2:.6f}, Val R²: {dnn_val_r2:.6f}")
    logger.info(f"  RF - Train R²: {rf_train_r2:.6f}, Val R²: {rf_val_r2:.6f}")
    logger.info(f"  Ensemble - Train R²: {ensemble_train_r2:.6f}, Val R²: {ensemble_val_r2:.6f}")

    # Save results if output directory is provided
    if output_dir:
        # Save models
        torch.save(dnn_model.state_dict(), os.path.join(output_dir, "dnn_model.pt"))
        joblib.dump(rf_model, os.path.join(output_dir, "rf_model.pkl"))

        # Save scaler
        joblib.dump(data['scaler'], os.path.join(output_dir, "scaler.pkl"))

        # Save parameters
        joblib.dump(best_params, os.path.join(output_dir, "best_params.pkl"))

        # Save metrics
        metrics = {
            'dnn_train_r2': dnn_train_r2,
            'dnn_val_r2': dnn_val_r2,
            'rf_train_r2': rf_train_r2,
            'rf_val_r2': rf_val_r2,
            'ensemble_train_r2': ensemble_train_r2,
            'ensemble_val_r2': ensemble_val_r2,
            'dnn_weight': dnn_weight,
            'rf_weight': rf_weight
        }

        pd.DataFrame([metrics]).to_csv(os.path.join(output_dir, "metrics.csv"), index=False)

        # Save study
        joblib.dump(study, os.path.join(output_dir, "study.pkl"))

        # Plot optimization history
        plt.figure(figsize=(10, 6))
        optuna.visualization.matplotlib.plot_optimization_history(study)
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, "optimization_history.png"))

        # Plot parameter importances
        plt.figure(figsize=(10, 6))
        optuna.visualization.matplotlib.plot_param_importances(study)
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, "param_importances.png"))

        # Plot model comparison
        plt.figure(figsize=(10, 6))

        plt.scatter(data['y_val'], dnn_val_preds, alpha=0.5, label=f'DNN (R² = {dnn_val_r2:.4f})')
        plt.scatter(data['y_val'], rf_val_preds, alpha=0.5, label=f'RF (R² = {rf_val_r2:.4f})')
        plt.scatter(data['y_val'], ensemble_val_preds, alpha=0.5, label=f'Ensemble (R² = {ensemble_val_r2:.4f})')

        plt.plot([0, 100], [0, 100], 'k--')
        plt.xlabel('True Temperature (°C)')
        plt.ylabel('Predicted Temperature (°C)')
        plt.title('Model Comparison on Validation Set')
        plt.legend()
        plt.grid(alpha=0.3)
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, "model_comparison.png"))

        logger.info(f"Results saved to {output_dir}")

    # Return results
    return {
        'dnn_model': dnn_model,
        'rf_model': rf_model,
        'best_params': best_params,
        'metrics': {
            'dnn_train_r2': dnn_train_r2,
            'dnn_val_r2': dnn_val_r2,
            'rf_train_r2': rf_train_r2,
            'rf_val_r2': rf_val_r2,
            'ensemble_train_r2': ensemble_train_r2,
            'ensemble_val_r2': ensemble_val_r2
        },
        'weights': {
            'dnn_weight': dnn_weight,
            'rf_weight': rf_weight
        },
        'study': study
    }

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Hyperparameter Tuning for Ensemble Temperature Model')
    parser.add_argument('--feature_file', type=str, required=True, help='Path to feature file')
    parser.add_argument('--metadata_file', type=str, required=True, help='Path to metadata file')
    parser.add_argument('--output_dir', type=str, required=True, help='Directory to save results')
    parser.add_argument('--n_features', type=int, default=1000, help='Number of features to select')
    parser.add_argument('--n_trials', type=int, default=100, help='Number of optimization trials')

    args = parser.parse_args()

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Load data
    data = load_data(args.feature_file, args.metadata_file, args.n_features)

    # Optimize ensemble model
    results = optimize_ensemble_model(data, args.n_trials, args.output_dir)

    logger.info("Optimization complete!")

if __name__ == "__main__":
    # Set matplotlib backend to non-interactive
    import matplotlib
    matplotlib.use('Agg')

    main()
