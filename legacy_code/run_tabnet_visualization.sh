#!/bin/bash

# Set environment variables
export PYTHONPATH=.:${PYTHONPATH}

# Set paths
DATA_PATH="data/"
MODEL_PATH="models/tabnet/best_tabnet_model.pt"
OUTPUT_PATH="results/tabnet/"

# Create output directory if it doesn't exist
mkdir -p ${OUTPUT_PATH}

# Run visualization
python -m deepmu.visualize_tabnet \
    --data_path ${DATA_PATH} \
    --model_path ${MODEL_PATH} \
    --output_path ${OUTPUT_PATH} \
    --n_examples 5 \
    --n_d 64 \
    --n_a 64 \
    --n_steps 3 \
    --gamma 1.3 \
    --n_shared 2 \
    --n_independent 2 \
    --virtual_batch_size 128 \
    --mask_type "sparsemax" 