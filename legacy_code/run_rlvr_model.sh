#!/bin/bash
# Run script for training the improved RLVR model with optimized hyperparameters

# Make script executable
chmod +x train_rlvr_model.py

# Create output directories
mkdir -p models/rlvr_model_improved
mkdir -p metrics/rlvr_model_improved

# Run training script with improved hyperparameters
python train_rlvr_model.py \
    --feature-file training_data/combined_features.tsv \
    --metadata training_data/metadata.tsv \
    --output-dir models/rlvr_model_improved \
    --metrics-dir metrics/rlvr_model_improved \
    --hidden-dim 512 \
    --num-heads 8 \
    --num-layers 3 \
    --dropout 0.3 \
    --use-layer-norm \
    --use-residual \
    --use-value-head \
    --activation silu \
    --alpha 0.7 \
    --beta 0.2 \
    --gamma 0.1 \
    --accuracy-scale 0.5 \
    --lr 5e-5 \
    --weight-decay 1e-4 \
    --batch-size 64 \
    --epochs 200 \
    --early-stopping-patience 20 \
    --entropy-coef 0.005 \
    --value-loss-coef 0.3 \
    --max-grad-norm 0.5 \
    --seed 42 \
    --feature-selection \
    --n-features 100


:<<'COMMENT'


To improve the RLVR model performance, we could:

Tune hyperparameters:
Adjust learning rate, batch size, and network architecture
Experiment with different reward function weights
Try different activation functions and regularization techniques
Improve feature engineering:
Experiment with different feature selection methods
Try different numbers of features
Add feature normalization and scaling
Enhance the model architecture:
Add more sophisticated attention mechanisms
Experiment with different network depths and widths
Try different initialization strategies
Improve the training process:
Implement curriculum learning
Add more sophisticated early stopping criteria
Experiment with different optimizer settings

COMMENT
