#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Predict with Square Root Transformation.
This script applies square root transformation to growth rate values for improved prediction.
"""

import os
import argparse
import logging
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import torch
import joblib
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from sklearn.preprocessing import RobustScaler

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class GrowthRatePredictor:
    """
    Predictor that correctly uses the growth rate model from hybrid_enhanced_nn_v1.
    """
    def __init__(self, model_dir: str = 'models/hybrid_enhanced_nn_v1'):
        """
        Initialize growth rate predictor.

        Args:
            model_dir: Directory containing the growth rate model
        """
        self.model_dir = model_dir
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

        # Load model components
        self._load_model()

        logger.info("Growth rate predictor initialized successfully")

    def _load_model(self):
        """Load growth rate model components."""
        logger.info(f"Loading growth rate model from {self.model_dir}")

        # Load feature scaler
        self.feature_scaler = joblib.load(os.path.join(self.model_dir, 'feature_scaler.joblib'))

        # Create a copy of the scaler without feature names to avoid warnings
        if hasattr(self.feature_scaler, 'feature_names_in_'):
            self.feature_scaler_no_names = joblib.load(os.path.join(self.model_dir, 'feature_scaler.joblib'))
            if hasattr(self.feature_scaler_no_names, 'feature_names_in_'):
                delattr(self.feature_scaler_no_names, 'feature_names_in_')
        else:
            self.feature_scaler_no_names = self.feature_scaler

        logger.info(f"Feature scaler loaded successfully")

        # Load Random Forest model
        rf_dir = os.path.join(self.model_dir, 'rf_models')
        self.growth_rf = joblib.load(os.path.join(rf_dir, 'rf_growth_model.joblib'))
        logger.info(f"Random Forest model loaded successfully")

        # Load XGBoost model
        xgb_dir = os.path.join(self.model_dir, 'xgb_models')
        self.growth_xgb = joblib.load(os.path.join(xgb_dir, 'xgb_growth_model.joblib'))
        logger.info(f"XGBoost model loaded successfully")

        # Load ensemble weights
        self.growth_weights = torch.load(
            os.path.join(self.model_dir, 'growth_weights.pth'),
            map_location=self.device
        )
        logger.info(f"Ensemble weights loaded successfully: {self.growth_weights.cpu().numpy()}")

        # Load transform info if available
        transform_info_path = os.path.join(self.model_dir, 'transform_info.joblib')
        if os.path.exists(transform_info_path):
            self.transform_info = joblib.load(transform_info_path)
            logger.info(f"Transform info loaded successfully")
        else:
            # Create default transform info based on the training code
            self.transform_info = {
                'growth_sqrt_transform': True,
                'growth_scaler': RobustScaler()
            }
            logger.info(f"Using default transform info")

            # Fit the scaler on a range of values that might be encountered
            # This is just an approximation
            y_growth_sqrt = np.sqrt(np.linspace(0.01, 10, 1000)).reshape(-1, 1)
            self.transform_info['growth_scaler'].fit(y_growth_sqrt)

    def predict(self, X: pd.DataFrame) -> np.ndarray:
        """
        Generate growth rate predictions.

        Args:
            X: Features DataFrame

        Returns:
            Growth rate predictions
        """
        # Handle feature mismatch
        if hasattr(self.feature_scaler, 'feature_names_in_'):
            # Filter features to match training data
            feature_names = self.feature_scaler.feature_names_in_
            common_cols = [col for col in X.columns if col in feature_names]

            # Check if we have all required features
            if len(common_cols) < len(feature_names):
                logger.warning(f"Missing {len(feature_names) - len(common_cols)} features required by the model")
                # Use only available features
                X_filtered = X[common_cols]
                # Fill missing features with zeros
                missing_cols = [col for col in feature_names if col not in common_cols]
                for col in missing_cols:
                    X_filtered[col] = 0
                # Reorder columns to match the order expected by the model
                X_filtered = X_filtered[feature_names]
            else:
                # Use only the features the model was trained on, in the correct order
                X_filtered = X[feature_names]
        else:
            # If no feature names, check dimensions
            expected_features = self.feature_scaler.mean_.shape[0]
            if X.shape[1] != expected_features:
                logger.warning(f"Feature count mismatch: got {X.shape[1]}, expected {expected_features}")
                # If too many features, truncate
                if X.shape[1] > expected_features:
                    X_filtered = X.iloc[:, :expected_features]
                else:
                    # If too few features, pad with zeros
                    X_filtered = pd.DataFrame(np.zeros((X.shape[0], expected_features)), index=X.index)
                    X_filtered.iloc[:, :X.shape[1]] = X.values
            else:
                X_filtered = X

        # Scale features using the scaler without feature names to avoid warnings
        X_filtered_np = X_filtered.values
        X_scaled = self.feature_scaler_no_names.transform(X_filtered_np)

        # Get predictions from Random Forest
        rf_pred = self.growth_rf.predict(X_scaled)
        logger.info(f"RF predictions - Min: {rf_pred.min():.4f}, Max: {rf_pred.max():.4f}, Mean: {rf_pred.mean():.4f}")

        # Get predictions from XGBoost
        xgb_pred = self.growth_xgb.predict(X_scaled)
        logger.info(f"XGB predictions - Min: {xgb_pred.min():.4f}, Max: {xgb_pred.max():.4f}, Mean: {xgb_pred.mean():.4f}")

        # Use XGBoost predictions as LightGBM predictions (as fallback)
        lgb_pred = xgb_pred
        logger.info(f"LGB predictions (fallback) - Min: {lgb_pred.min():.4f}, Max: {lgb_pred.max():.4f}, Mean: {lgb_pred.mean():.4f}")

        # Combine predictions using ensemble weights
        weights = torch.softmax(self.growth_weights, dim=0).cpu().numpy()
        logger.info(f"Normalized weights: RF={weights[0]:.4f}, XGB={weights[1]:.4f}, LGB={weights[2]:.4f}")

        combined_pred = (
            weights[0] * rf_pred +
            weights[1] * xgb_pred +
            weights[2] * lgb_pred
        )

        logger.info(f"Combined predictions (scaled) - Min: {combined_pred.min():.4f}, Max: {combined_pred.max():.4f}, Mean: {combined_pred.mean():.4f}")

        # The model was trained on sqrt-transformed and RobustScaler-transformed growth rate
        # We need to convert back to the original space

        # First, inverse transform the RobustScaler
        combined_pred_reshaped = combined_pred.reshape(-1, 1)
        combined_pred_unscaled = self.transform_info['growth_scaler'].inverse_transform(combined_pred_reshaped).flatten()

        logger.info(f"Combined predictions (unscaled) - Min: {combined_pred_unscaled.min():.4f}, Max: {combined_pred_unscaled.max():.4f}, Mean: {combined_pred_unscaled.mean():.4f}")

        # Then, inverse transform the sqrt transformation
        if self.transform_info.get('growth_sqrt_transform', False):
            combined_pred_orig = combined_pred_unscaled ** 2
        else:
            combined_pred_orig = combined_pred_unscaled

        # Ensure predictions are non-negative
        combined_pred_orig = np.maximum(combined_pred_orig, 0)

        logger.info(f"Final predictions - Min: {combined_pred_orig.min():.4f}, Max: {combined_pred_orig.max():.4f}, Mean: {combined_pred_orig.mean():.4f}")

        return combined_pred_orig

    def evaluate(self, X: pd.DataFrame, y: pd.Series) -> dict:
        """
        Evaluate model on data.

        Args:
            X: Features DataFrame
            y: Growth rate targets

        Returns:
            Dictionary of evaluation metrics
        """
        # Generate predictions
        y_pred = self.predict(X)

        # Calculate metrics on original scale
        metrics = {
            'R2': r2_score(y, y_pred),
            'RMSE': np.sqrt(mean_squared_error(y, y_pred)),
            'MAE': mean_absolute_error(y, y_pred)
        }

        # Calculate metrics on log scale for better evaluation
        log_r2 = r2_score(np.log1p(y), np.log1p(y_pred))
        log_rmse = np.sqrt(mean_squared_error(np.log1p(y), np.log1p(y_pred)))
        log_mae = mean_absolute_error(np.log1p(y), np.log1p(y_pred))
        
        metrics.update({
            'Log_R2': log_r2,
            'Log_RMSE': log_rmse,
            'Log_MAE': log_mae
        })
        
        # Calculate metrics on sqrt scale
        sqrt_y = np.sqrt(y)
        sqrt_y_pred = np.sqrt(y_pred)
        
        sqrt_r2 = r2_score(sqrt_y, sqrt_y_pred)
        sqrt_rmse = np.sqrt(mean_squared_error(sqrt_y, sqrt_y_pred))
        sqrt_mae = mean_absolute_error(sqrt_y, sqrt_y_pred)
        
        metrics.update({
            'Sqrt_R2': sqrt_r2,
            'Sqrt_RMSE': sqrt_rmse,
            'Sqrt_MAE': sqrt_mae
        })

        logger.info(f"Evaluation metrics - R²={metrics['R2']:.4f}, RMSE={metrics['RMSE']:.4f}, MAE={metrics['MAE']:.4f}")
        logger.info(f"Log-scale metrics - R²={metrics['Log_R2']:.4f}, RMSE={metrics['Log_RMSE']:.4f}, MAE={metrics['Log_MAE']:.4f}")
        logger.info(f"Sqrt-scale metrics - R²={metrics['Sqrt_R2']:.4f}, RMSE={metrics['Sqrt_RMSE']:.4f}, MAE={metrics['Sqrt_MAE']:.4f}")

        return metrics

def load_data(feature_file: str, metadata_file: str, filter_outliers: bool = True, percentile: float = 99.0, apply_sqrt: bool = False) -> tuple:
    """
    Load feature and metadata files.

    Args:
        feature_file: Path to feature file
        metadata_file: Path to metadata file
        filter_outliers: Whether to filter outliers in growth rate
        percentile: Percentile threshold for filtering outliers
        apply_sqrt: Whether to apply square root transformation to growth rate

    Returns:
        Tuple of (features, metadata)
    """
    logger.info(f"Loading features from {feature_file}")
    features = pd.read_csv(feature_file, sep='\t', index_col='genome_id')

    logger.info(f"Loading metadata from {metadata_file}")
    metadata = pd.read_csv(metadata_file, sep='\t', index_col='genome_id')

    # Get common samples
    common_indices = features.index.intersection(metadata.index)
    logger.info(f"Found {len(common_indices)} common samples")

    # Filter to common samples
    features = features.loc[common_indices]
    metadata = metadata.loc[common_indices]

    # Check for NaN values in features
    nan_count = features.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in features. Filling with 0.")
        features = features.fillna(0)

    # Check for infinite values
    inf_count = np.isinf(features.values).sum()
    if inf_count > 0:
        logger.warning(f"Found {inf_count} infinite values in features. Replacing with max/min values.")
        features = features.replace([np.inf, -np.inf], np.nan)
        features = features.fillna(features.max())

    # Check for NaN values in metadata
    nan_count = metadata.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in metadata. Filling with 0.")
        metadata = metadata.fillna(0)

    # Filter outliers in growth rate if requested
    if filter_outliers:
        threshold = np.percentile(metadata['growth_rate'], percentile)
        logger.info(f"Filtering growth rate values above {threshold:.4f} (percentile {percentile})")

        # Count outliers
        outlier_count = (metadata['growth_rate'] > threshold).sum()
        logger.info(f"Found {outlier_count} outliers in growth rate")

        # Log statistics before filtering
        logger.info(f"Growth rate statistics before filtering - Min: {metadata['growth_rate'].min():.4f}, Max: {metadata['growth_rate'].max():.4f}, Mean: {metadata['growth_rate'].mean():.4f}, Median: {metadata['growth_rate'].median():.4f}")

        # Filter out outliers
        outlier_indices = metadata[metadata['growth_rate'] > threshold].index
        metadata = metadata[metadata['growth_rate'] <= threshold]
        features = features.drop(outlier_indices, errors='ignore')

        # Log statistics after filtering
        logger.info(f"Growth rate statistics after filtering - Min: {metadata['growth_rate'].min():.4f}, Max: {metadata['growth_rate'].max():.4f}, Mean: {metadata['growth_rate'].mean():.4f}, Median: {metadata['growth_rate'].median():.4f}")
    
    # Apply square root transformation to growth rate if requested
    if apply_sqrt:
        logger.info("Applying square root transformation to growth rate")
        
        # Store original growth rate
        metadata['original_growth_rate'] = metadata['growth_rate']
        
        # Apply square root transformation
        metadata['growth_rate'] = np.sqrt(metadata['growth_rate'])
        
        # Log statistics after transformation
        logger.info(f"Growth rate statistics after sqrt transformation - Min: {metadata['growth_rate'].min():.4f}, Max: {metadata['growth_rate'].max():.4f}, Mean: {metadata['growth_rate'].mean():.4f}, Median: {metadata['growth_rate'].median():.4f}")

    return features, metadata

def main():
    parser = argparse.ArgumentParser(description="Predict with Square Root Transformation")
    parser.add_argument("--feature_file", required=True, help="Path to feature file")
    parser.add_argument("--metadata_file", required=True, help="Path to metadata file")
    parser.add_argument("--output_dir", default="predictions", help="Directory to save predictions")
    parser.add_argument("--model_dir", default="models/hybrid_enhanced_nn_v1", help="Directory containing the growth rate model")
    parser.add_argument("--filter_outliers", action="store_true", help="Filter outliers in growth rate")
    parser.add_argument("--percentile", type=float, default=99.0, help="Percentile threshold for filtering outliers")
    parser.add_argument("--apply_sqrt", action="store_true", help="Apply square root transformation to growth rate")
    args = parser.parse_args()

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Load data
    features, metadata = load_data(
        args.feature_file,
        args.metadata_file,
        filter_outliers=args.filter_outliers,
        percentile=args.percentile,
        apply_sqrt=args.apply_sqrt
    )

    # Extract growth rate targets
    y_growth = metadata['growth_rate']
    
    # If square root transformation was applied, use original values for plotting
    if args.apply_sqrt:
        y_growth_orig = metadata['original_growth_rate']
    else:
        y_growth_orig = y_growth

    # Initialize predictor
    predictor = GrowthRatePredictor(model_dir=args.model_dir)

    # Generate predictions
    y_growth_pred = predictor.predict(features)
    
    # If square root transformation was applied, transform predictions back
    if args.apply_sqrt:
        y_growth_pred_transformed = y_growth_pred ** 0.5
    else:
        y_growth_pred_transformed = y_growth_pred

    # Save predictions
    pd.DataFrame({
        'genome_id': features.index,
        'actual_growth': y_growth_orig,
        'predicted_growth': y_growth_pred
    }).to_csv(os.path.join(args.output_dir, 'growth_predictions.tsv'), sep='\t', index=False)

    # Plot predictions
    plt.figure(figsize=(12, 10))
    
    # Original scale
    plt.subplot(2, 2, 1)
    plt.scatter(y_growth_orig, y_growth_pred, alpha=0.5)
    plt.plot([min(y_growth_orig), max(y_growth_orig)], [min(y_growth_orig), max(y_growth_orig)], 'r--')
    plt.xlabel('Actual Growth Rate')
    plt.ylabel('Predicted Growth Rate')
    plt.title('Growth Rate Predictions (Original Scale)')
    
    # Log scale
    plt.subplot(2, 2, 2)
    plt.scatter(np.log1p(y_growth_orig), np.log1p(y_growth_pred), alpha=0.5)
    plt.plot([min(np.log1p(y_growth_orig)), max(np.log1p(y_growth_orig))], 
             [min(np.log1p(y_growth_orig)), max(np.log1p(y_growth_orig))], 'r--')
    plt.xlabel('Actual Growth Rate (log scale)')
    plt.ylabel('Predicted Growth Rate (log scale)')
    plt.title('Growth Rate Predictions (Log Scale)')
    
    # Square root scale
    plt.subplot(2, 2, 3)
    plt.scatter(np.sqrt(y_growth_orig), np.sqrt(y_growth_pred), alpha=0.5)
    plt.plot([min(np.sqrt(y_growth_orig)), max(np.sqrt(y_growth_orig))], 
             [min(np.sqrt(y_growth_orig)), max(np.sqrt(y_growth_orig))], 'r--')
    plt.xlabel('Actual Growth Rate (sqrt scale)')
    plt.ylabel('Predicted Growth Rate (sqrt scale)')
    plt.title('Growth Rate Predictions (Sqrt Scale)')
    
    plt.tight_layout()
    plt.savefig(os.path.join(args.output_dir, 'growth_predictions.png'))

    # Evaluate model
    metrics = predictor.evaluate(features, y_growth_orig)

    # Save metrics
    pd.DataFrame({
        'metric': list(metrics.keys()),
        'value': list(metrics.values())
    }).to_csv(os.path.join(args.output_dir, 'growth_metrics.tsv'), sep='\t', index=False)

    logger.info(f"Predictions saved to {args.output_dir}")

if __name__ == "__main__":
    main()
