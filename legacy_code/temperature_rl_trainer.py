#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Reinforcement Learning Trainer for Temperature Prediction.

This module implements the training infrastructure for the RL-based
temperature prediction model.
"""

import os
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from typing import Dict, List, Tuple, Optional, Any
import logging
import random
from collections import deque
import matplotlib.pyplot as plt
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error

from temperature_rl_agent import TemperatureRLAgent, VerifiableRewardFunction

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ExperienceBuffer:
    """
    Buffer for storing and sampling experiences for RL training.
    """
    
    def __init__(self, max_size: int = 10000):
        """
        Initialize the experience buffer.
        
        Args:
            max_size: Maximum buffer size
        """
        self.buffer = deque(maxlen=max_size)
    
    def add(self, experience: Tuple):
        """
        Add an experience to the buffer.
        
        Args:
            experience: Tuple of (features, targets)
        """
        self.buffer.append(experience)
    
    def sample(self, batch_size: int) -> List[Tuple]:
        """
        Sample a batch of experiences.
        
        Args:
            batch_size: Number of experiences to sample
            
        Returns:
            List of experience tuples
        """
        return random.sample(self.buffer, min(batch_size, len(self.buffer)))
    
    def __len__(self) -> int:
        """Get buffer length."""
        return len(self.buffer)

class TemperatureRLTrainer:
    """
    Trainer for the RL-based temperature prediction model.
    
    Implements reinforcement learning training with experience replay,
    adaptive reward scaling, and tracking of model performance.
    """
    
    def __init__(
        self,
        agent: TemperatureRLAgent,
        reward_fn: VerifiableRewardFunction,
        lr: float = 1e-4,
        weight_decay: float = 1e-5,
        gamma: float = 0.99,
        entropy_coef: float = 0.01,
        value_loss_coef: float = 0.5,
        max_grad_norm: float = 1.0,
        use_lr_scheduler: bool = True,
        use_experience_replay: bool = True,
        replay_buffer_size: int = 10000,
        batch_size: int = 32,
        device: Optional[torch.device] = None
    ):
        """
        Initialize the trainer.
        
        Args:
            agent: Temperature RL agent
            reward_fn: Reward function
            lr: Learning rate
            weight_decay: Weight decay for regularization
            gamma: Discount factor
            entropy_coef: Entropy regularization coefficient
            value_loss_coef: Value loss coefficient
            max_grad_norm: Maximum gradient norm for clipping
            use_lr_scheduler: Whether to use learning rate scheduler
            use_experience_replay: Whether to use experience replay
            replay_buffer_size: Size of experience replay buffer
            batch_size: Batch size for training
            device: Device to use for training
        """
        self.agent = agent
        self.reward_fn = reward_fn
        self.gamma = gamma
        self.entropy_coef = entropy_coef
        self.value_loss_coef = value_loss_coef
        self.max_grad_norm = max_grad_norm
        self.use_lr_scheduler = use_lr_scheduler
        self.use_experience_replay = use_experience_replay
        self.batch_size = batch_size
        
        # Set device
        self.device = device if device is not None else torch.device(
            "cuda" if torch.cuda.is_available() else "cpu"
        )
        self.agent.to(self.device)
        
        # Set up optimizer
        self.optimizer = optim.Adam(
            self.agent.parameters(),
            lr=lr,
            weight_decay=weight_decay
        )
        
        # Set up learning rate scheduler
        if use_lr_scheduler:
            self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
                self.optimizer,
                mode='min',
                factor=0.5,
                patience=5,
                min_lr=1e-6
            )
        
        # Set up experience replay
        if use_experience_replay:
            self.replay_buffer = ExperienceBuffer(max_size=replay_buffer_size)
        
        # Training metrics
        self.metrics = {
            'train_losses': [],
            'train_rewards': [],
            'val_losses': [],
            'val_rewards': [],
            'val_r2': [],
            'val_rmse': [],
            'val_mae': [],
            'learning_rates': []
        }
    
    def _prepare_batch(self, features: np.ndarray, targets: np.ndarray) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Prepare a batch for training.
        
        Args:
            features: Feature array
            targets: Target array
            
        Returns:
            Tuple of (features_tensor, targets_tensor)
        """
        # Convert to tensors
        features_tensor = torch.tensor(features, dtype=torch.float32).to(self.device)
        targets_tensor = torch.tensor(targets, dtype=torch.float32).to(self.device)
        
        return features_tensor, targets_tensor
    
    def _calculate_policy_gradient_loss(
        self,
        predictions: torch.Tensor,
        rewards: torch.Tensor,
        targets: Optional[torch.Tensor] = None,
        entropy: Optional[torch.Tensor] = None
    ) -> torch.Tensor:
        """
        Calculate policy gradient loss for temperature prediction.
        
        Args:
            predictions: Predicted temperatures
            rewards: Rewards
            targets: True temperatures
            entropy: Entropy of the policy
            
        Returns:
            Policy gradient loss
        """
        # For temperature prediction, we use a modified policy gradient loss
        # that combines supervised learning and RL signals
        
        # Calculate supervised loss component (MSE)
        if targets is not None:
            supervised_loss = F.mse_loss(predictions, targets)
        else:
            supervised_loss = torch.tensor(0.0).to(self.device)
        
        # Calculate advantage-weighted policy gradient loss
        if rewards is not None:
            # We want to maximize reward, so we use negative loss
            advantage = rewards.detach()
            # Calculate MSE loss weighted by advantages
            rl_loss = -advantage * F.mse_loss(predictions, targets, reduction='none')
            rl_loss = rl_loss.mean()
        else:
            rl_loss = torch.tensor(0.0).to(self.device)
        
        # Add entropy regularization if provided
        entropy_term = torch.tensor(0.0).to(self.device)
        if entropy is not None and self.entropy_coef > 0:
            entropy_term = -self.entropy_coef * entropy
        
        # Combine losses (0.7 for supervised, 0.3 for RL)
        total_loss = 0.7 * supervised_loss + 0.3 * rl_loss + entropy_term
        
        return total_loss
    
    def _calculate_value_loss(self, value_pred: torch.Tensor, rewards: torch.Tensor) -> torch.Tensor:
        """
        Calculate value loss for the critic network.
        
        Args:
            value_pred: Predicted values
            rewards: Actual rewards
            
        Returns:
            Value loss
        """
        # For value prediction, we use MSE loss
        if rewards is not None and value_pred is not None:
            return F.mse_loss(value_pred, rewards)
        else:
            return torch.tensor(0.0).to(self.device)
    
    def add_to_replay_buffer(self, features: np.ndarray, targets: np.ndarray):
        """
        Add experiences to the replay buffer.
        
        Args:
            features: Feature array
            targets: Target array
        """
        if not self.use_experience_replay:
            return
        
        # Add experiences to buffer
        for i in range(len(targets)):
            self.replay_buffer.add((
                features[i:i+1],
                targets[i:i+1]
            ))
    
    def sample_from_replay_buffer(self, batch_size: int) -> List[Tuple[np.ndarray, np.ndarray]]:
        """
        Sample experiences from the replay buffer.
        
        Args:
            batch_size: Number of experiences to sample
            
        Returns:
            List of (features, targets) tuples
        """
        if not self.use_experience_replay or len(self.replay_buffer) < batch_size // 2:
            return []
        
        # Sample experiences
        experiences = self.replay_buffer.sample(batch_size)
        
        # Extract features and targets
        features_list = []
        targets_list = []
        
        for feature, target in experiences:
            features_list.append(feature)
            targets_list.append(target)
        
        if not features_list:
            return []
        
        # Concatenate arrays
        return [(np.concatenate(features_list, axis=0), np.concatenate(targets_list, axis=0))]
    
    def train_step(
        self,
        features: np.ndarray,
        targets: np.ndarray,
        include_replay: bool = True
    ) -> Dict[str, float]:
        """
        Perform a single training step.
        
        Args:
            features: Input features
            targets: Target temperatures
            include_replay: Whether to include replay experiences
            
        Returns:
            Dictionary with training metrics
        """
        self.agent.train()
        
        # Prepare batch
        features_tensor, targets_tensor = self._prepare_batch(features, targets)
        
        # Forward pass
        outputs = self.agent(features_tensor)
        predictions = outputs['temperature']
        
        if 'uncertainty' in outputs and self.agent.use_value_head:
            value_pred = outputs['uncertainty']
        else:
            value_pred = None
        
        # Calculate rewards
        rewards_np = self.reward_fn(targets, predictions.detach().cpu().numpy(), features)
        rewards_tensor = torch.tensor(rewards_np, dtype=torch.float32).to(self.device)
        
        # Sample from replay buffer
        replay_samples = []
        if include_replay and self.use_experience_replay:
            replay_samples = self.sample_from_replay_buffer(self.batch_size)
        
        # Include replay samples in batch
        total_loss = 0.0
        batch_count = 1  # Count batches for averaging loss
        
        # Process regular batch
        policy_loss = self._calculate_policy_gradient_loss(
            predictions, rewards_tensor, targets_tensor
        )
        
        if value_pred is not None:
            value_loss = self._calculate_value_loss(value_pred, rewards_tensor)
            loss = policy_loss + self.value_loss_coef * value_loss
        else:
            value_loss = torch.tensor(0.0).to(self.device)
            loss = policy_loss
        
        total_loss += loss.item()
        
        # Process replay samples
        for replay_features, replay_targets in replay_samples:
            # Prepare replay batch
            replay_features_tensor, replay_targets_tensor = self._prepare_batch(
                replay_features, replay_targets
            )
            
            # Forward pass on replay batch
            replay_outputs = self.agent(replay_features_tensor)
            replay_predictions = replay_outputs['temperature']
            
            if 'uncertainty' in replay_outputs and self.agent.use_value_head:
                replay_value_pred = replay_outputs['uncertainty']
            else:
                replay_value_pred = None
            
            # Calculate rewards for replay batch
            replay_rewards_np = self.reward_fn(
                replay_targets, replay_predictions.detach().cpu().numpy(), replay_features
            )
            replay_rewards_tensor = torch.tensor(replay_rewards_np, dtype=torch.float32).to(self.device)
            
            # Calculate losses for replay batch
            replay_policy_loss = self._calculate_policy_gradient_loss(
                replay_predictions, replay_rewards_tensor, replay_targets_tensor
            )
            
            if replay_value_pred is not None:
                replay_value_loss = self._calculate_value_loss(replay_value_pred, replay_rewards_tensor)
                replay_loss = replay_policy_loss + self.value_loss_coef * replay_value_loss
            else:
                replay_loss = replay_policy_loss
            
            total_loss += replay_loss.item()
            batch_count += 1
        
        # Averaged loss
        avg_loss = total_loss / batch_count
        
        # Optimization step
        self.optimizer.zero_grad()
        loss.backward()
        
        # Gradient clipping
        if self.max_grad_norm > 0:
            torch.nn.utils.clip_grad_norm_(self.agent.parameters(), self.max_grad_norm)
        
        self.optimizer.step()
        
        # Update feature importance
        if hasattr(self.agent, 'feature_encoder') and hasattr(self.agent.feature_encoder[0], 'weight'):
            weight_grad = self.agent.feature_encoder[0].weight.grad
            self.agent.update_feature_importance(weight_grad)
        
        # Add experiences to replay buffer
        self.add_to_replay_buffer(features, targets)
        
        # Calculate metrics
        metrics = {
            'loss': avg_loss,
            'reward': float(np.mean(rewards_np)),
            'policy_loss': float(policy_loss.item()),
            'value_loss': float(value_loss.item()) if value_pred is not None else 0.0
        }
        
        return metrics
    
    def validate(self, val_features: np.ndarray, val_targets: np.ndarray) -> Dict[str, float]:
        """
        Validate the model on validation data.
        
        Args:
            val_features: Validation features
            val_targets: Validation targets
            
        Returns:
            Dictionary with validation metrics
        """
        self.agent.eval()
        
        with torch.no_grad():
            # Prepare batch
            features_tensor, targets_tensor = self._prepare_batch(val_features, val_targets)
            
            # Forward pass
            outputs = self.agent(features_tensor)
            predictions = outputs['temperature']
            
            if 'uncertainty' in outputs and self.agent.use_value_head:
                value_pred = outputs['uncertainty']
            else:
                value_pred = None
            
            # Calculate rewards
            rewards_np = self.reward_fn(val_targets, predictions.cpu().numpy(), val_features)
            rewards_tensor = torch.tensor(rewards_np, dtype=torch.float32).to(self.device)
            
            # Calculate losses
            policy_loss = self._calculate_policy_gradient_loss(
                predictions, rewards_tensor, targets_tensor
            )
            
            if value_pred is not None:
                value_loss = self._calculate_value_loss(value_pred, rewards_tensor)
                loss = policy_loss + self.value_loss_coef * value_loss
            else:
                value_loss = torch.tensor(0.0).to(self.device)
                loss = policy_loss
            
            # Calculate metrics
            predictions_np = predictions.cpu().numpy()
            r2 = r2_score(val_targets, predictions_np)
            rmse = np.sqrt(mean_squared_error(val_targets, predictions_np))
            mae = mean_absolute_error(val_targets, predictions_np)
            
            metrics = {
                'loss': float(loss.item()),
                'reward': float(np.mean(rewards_np)),
                'r2': float(r2),
                'rmse': float(rmse),
                'mae': float(mae),
                'policy_loss': float(policy_loss.item()),
                'value_loss': float(value_loss.item()) if value_pred is not None else 0.0
            }
            
            return metrics
    
    def train(
        self,
        train_features: np.ndarray,
        train_targets: np.ndarray,
        val_features: Optional[np.ndarray] = None,
        val_targets: Optional[np.ndarray] = None,
        num_epochs: int = 100,
        batch_size: int = 32,
        early_stopping_patience: int = 10,
        verbose: bool = True,
        log_interval: int = 10,
        save_best_model: bool = True
    ) -> Dict[str, List[float]]:
        """
        Train the model.
        
        Args:
            train_features: Training features
            train_targets: Training targets
            val_features: Validation features
            val_targets: Validation targets
            num_epochs: Number of training epochs
            batch_size: Batch size
            early_stopping_patience: Patience for early stopping
            verbose: Whether to print progress
            log_interval: Interval for logging
            save_best_model: Whether to save the best model
            
        Returns:
            Dictionary with training metrics
        """
        # Initial setup
        self.batch_size = batch_size
        best_val_r2 = -float('inf')
        best_model_state = None
        patience_counter = 0
        
        # Reset reward function history
        self.reward_fn.reset_history()
        
        # Convert to numpy arrays if they're torch tensors
        if isinstance(train_features, torch.Tensor):
            train_features = train_features.cpu().numpy()
        if isinstance(train_targets, torch.Tensor):
            train_targets = train_targets.cpu().numpy()
        if val_features is not None and isinstance(val_features, torch.Tensor):
            val_features = val_features.cpu().numpy()
        if val_targets is not None and isinstance(val_targets, torch.Tensor):
            val_targets = val_targets.cpu().numpy()
        
        logger.info(f"Starting training for {num_epochs} epochs")
        logger.info(f"Training on {len(train_targets)} samples")
        if val_features is not None:
            logger.info(f"Validating on {len(val_targets)} samples")
        
        # Training loop
        for epoch in range(num_epochs):
            # Create batches
            indices = np.arange(len(train_targets))
            np.random.shuffle(indices)
            
            # Track epoch metrics
            epoch_loss = 0.0
            epoch_reward = 0.0
            num_batches = 0
            
            # Process batches
            for i in range(0, len(indices), batch_size):
                # Get batch indices
                batch_indices = indices[i:i+batch_size]
                
                # Get batch data
                batch_features = train_features[batch_indices]
                batch_targets = train_targets[batch_indices]
                
                # Train on batch
                batch_metrics = self.train_step(
                    batch_features, batch_targets,
                    include_replay=(epoch >= 5)  # Start using replay after a few epochs
                )
                
                # Update epoch metrics
                epoch_loss += batch_metrics['loss']
                epoch_reward += batch_metrics['reward']
                num_batches += 1
            
            # Average epoch metrics
            epoch_loss /= num_batches
            epoch_reward /= num_batches
            
            # Track metrics
            self.metrics['train_losses'].append(epoch_loss)
            self.metrics['train_rewards'].append(epoch_reward)
            self.metrics['learning_rates'].append(self.optimizer.param_groups[0]['lr'])
            
            # Validation
            if val_features is not None and val_targets is not None:
                val_metrics = self.validate(val_features, val_targets)
                
                self.metrics['val_losses'].append(val_metrics['loss'])
                self.metrics['val_rewards'].append(val_metrics['reward'])
                self.metrics['val_r2'].append(val_metrics['r2'])
                self.metrics['val_rmse'].append(val_metrics['rmse'])
                self.metrics['val_mae'].append(val_metrics['mae'])
                
                # Update learning rate scheduler
                if self.use_lr_scheduler:
                    self.scheduler.step(val_metrics['loss'])
                
                # Check for best model
                if val_metrics['r2'] > best_val_r2:
                    best_val_r2 = val_metrics['r2']
                    patience_counter = 0
                    
                    if save_best_model:
                        best_model_state = {
                            key: value.cpu().clone() 
                            for key, value in self.agent.state_dict().items()
                        }
                        
                    if verbose:
                        logger.info(f"New best validation R²: {best_val_r2:.4f}")
                else:
                    patience_counter += 1
                
                # Log progress
                if verbose and ((epoch + 1) % log_interval == 0 or epoch == 0):
                    logger.info(
                        f"Epoch {epoch+1}/{num_epochs}: "
                        f"Train Loss: {epoch_loss:.6f}, "
                        f"Train Reward: {epoch_reward:.4f}, "
                        f"Val R²: {val_metrics['r2']:.4f}, "
                        f"Val RMSE: {val_metrics['rmse']:.4f}, "
                        f"LR: {self.optimizer.param_groups[0]['lr']:.6f}"
                    )
                
                # Check for early stopping
                if patience_counter >= early_stopping_patience:
                    logger.info(f"Early stopping after {epoch+1} epochs")
                    break
            else:
                # Log progress without validation
                if verbose and ((epoch + 1) % log_interval == 0 or epoch == 0):
                    logger.info(
                        f"Epoch {epoch+1}/{num_epochs}: "
                        f"Train Loss: {epoch_loss:.6f}, "
                        f"Train Reward: {epoch_reward:.4f}, "
                        f"LR: {self.optimizer.param_groups[0]['lr']:.6f}"
                    )
        
        # Restore best model
        if best_model_state is not None and save_best_model:
            self.agent.load_state_dict(best_model_state)
            logger.info(f"Restored best model with validation R² of {best_val_r2:.4f}")
        
        return self.metrics
    
    def plot_training_curves(self, output_dir: Optional[str] = None):
        """
        Plot training curves.
        
        Args:
            output_dir: Directory to save plots (optional)
        """
        if not self.metrics['train_losses']:
            logger.warning("No training metrics to plot")
            return
        
        # Create figure
        plt.figure(figsize=(15, 10))
        
        # Plot losses
        plt.subplot(2, 2, 1)
        plt.plot(self.metrics['train_losses'], label='Train Loss')
        if self.metrics['val_losses']:
            plt.plot(self.metrics['val_losses'], label='Val Loss')
        plt.title('Loss Curves')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # Plot rewards
        plt.subplot(2, 2, 2)
        plt.plot(self.metrics['train_rewards'], label='Train Reward')
        if self.metrics['val_rewards']:
            plt.plot(self.metrics['val_rewards'], label='Val Reward')
        plt.title('Reward Curves')
        plt.xlabel('Epoch')
        plt.ylabel('Reward')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # Plot validation metrics
        if self.metrics['val_r2']:
            plt.subplot(2, 2, 3)
            plt.plot(self.metrics['val_r2'], label='R²', color='green')
            plt.title('Validation R²')
            plt.xlabel('Epoch')
            plt.ylabel('R²')
            plt.legend()
            plt.grid(True, alpha=0.3)
            
            plt.subplot(2, 2, 4)
            plt.plot(self.metrics['val_rmse'], label='RMSE', color='red')
            plt.plot(self.metrics['val_mae'], label='MAE', color='orange')
            plt.title('Validation Error Metrics')
            plt.xlabel('Epoch')
            plt.ylabel('Error')
            plt.legend()
            plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # Save or show
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)
            plt.savefig(os.path.join(output_dir, 'training_curves.png'), dpi=300)
            plt.close()
        else:
            plt.show()
    
    def save_model(self, path: str):
        """
        Save model and trainer state.
        
        Args:
            path: Path to save the model
        """
        os.makedirs(os.path.dirname(path), exist_ok=True)
        
        # Save model and optimizer state
        state = {
            'model_state_dict': self.agent.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'metrics': self.metrics
        }
        
        # Add scheduler state if applicable
        if self.use_lr_scheduler:
            state['scheduler_state_dict'] = self.scheduler.state_dict()
        
        # Save state
        torch.save(state, path)
        logger.info(f"Model saved to {path}")
    
    def load_model(self, path: str):
        """
        Load model and trainer state.
        
        Args:
            path: Path to load the model from
        """
        # Load state
        state = torch.load(path, map_location=self.device)
        
        # Load model
        self.agent.load_state_dict(state['model_state_dict'])
        
        # Load optimizer
        self.optimizer.load_state_dict(state['optimizer_state_dict'])
        
        # Load scheduler if available
        if 'scheduler_state_dict' in state and self.use_lr_scheduler:
            self.scheduler.load_state_dict(state['scheduler_state_dict'])
        
        # Load metrics
        if 'metrics' in state:
            self.metrics = state['metrics']
        
        logger.info(f"Model loaded from {path}")
    
    def get_metrics(self) -> Dict[str, List[float]]:
        """Get training metrics."""
        return self.metrics 