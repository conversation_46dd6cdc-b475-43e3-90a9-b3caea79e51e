#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Enhanced Neural Network Component for Growth Rate and Temperature Prediction.
This module provides a PyTorch DNN model with advanced architecture and One-Cycle Learning Rate Schedule
that can predict both growth rate and optimal temperature simultaneously.
"""

import os
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import logging
from typing import Dict, List, Tuple, Optional
from sklearn.preprocessing import StandardScaler

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MultiTaskDataset(Dataset):
    """Dataset for multi-task tabular data."""
    def __init__(self, features: np.ndarray, growth_rate: np.ndarray, temperature: np.ndarray):
        """
        Initialize dataset.
        
        Args:
            features: Feature matrix
            growth_rate: Growth rate target vector
            temperature: Temperature target vector
        """
        self.features = torch.tensor(features, dtype=torch.float32)
        self.growth_rate = torch.tensor(growth_rate, dtype=torch.float32).reshape(-1, 1)
        self.temperature = torch.tensor(temperature, dtype=torch.float32).reshape(-1, 1)
        
    def __len__(self):
        return len(self.features)
    
    def __getitem__(self, idx):
        return self.features[idx], (self.growth_rate[idx], self.temperature[idx])

class EnhancedNeuralNetwork(nn.Module):
    """
    Enhanced Neural Network with batch normalization, gradient clipping, and residual connections.
    """
    def __init__(
        self,
        input_dim: int,
        hidden_dims: List[int] = [512, 512, 384, 256, 128],
        dropout_rates: List[float] = [0.3, 0.4, 0.4, 0.5, 0.5],
        use_batch_norm: bool = True
    ):
        """
        Initialize neural network.
        
        Args:
            input_dim: Number of input features
            hidden_dims: List of hidden layer dimensions
            dropout_rates: List of dropout rates for each layer
            use_batch_norm: Whether to use batch normalization
        """
        super().__init__()
        
        # Ensure dropout_rates has at least as many elements as hidden_dims
        if len(dropout_rates) < len(hidden_dims):
            dropout_rates = dropout_rates + [dropout_rates[-1]] * (len(hidden_dims) - len(dropout_rates))
        
        # Create layers
        layers = []
        prev_dim = input_dim
        
        # Build network with progressively increasing dropout
        for i, hidden_dim in enumerate(hidden_dims):
            # Linear layer
            layers.append(nn.Linear(prev_dim, hidden_dim))
            
            # Batch normalization (before activation)
            if use_batch_norm:
                layers.append(nn.BatchNorm1d(hidden_dim))
            
            # Activation - using LeakyReLU for better gradient flow
            layers.append(nn.LeakyReLU(0.1))
            
            # Dropout with increasing rate for deeper layers
            dropout_rate = dropout_rates[i]
            layers.append(nn.Dropout(dropout_rate))
            
            prev_dim = hidden_dim
        
        self.feature_extractor = nn.Sequential(*layers)
        
        # Output layers for growth rate and temperature
        self.growth_output = nn.Linear(prev_dim, 1)
        self.temp_output = nn.Linear(prev_dim, 1)
        
        # Initialize weights with improved method
        self._init_weights()
    
    def _init_weights(self):
        """Initialize weights with improved initialization."""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                # Use Xavier/Glorot initialization for better stability
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
    
    def forward(self, x):
        """
        Forward pass.
        
        Args:
            x: Input tensor
            
        Returns:
            Tuple of (growth_rate_prediction, temperature_prediction)
        """
        features = self.feature_extractor(x)
        growth = self.growth_output(features)
        temp = self.temp_output(features)
        
        return growth.squeeze(), temp.squeeze()

class EnhancedNeuralNetworkComponent:
    """
    Enhanced Neural Network component for growth rate and temperature prediction.
    """
    def __init__(
        self,
        hidden_dims: List[int] = [512, 512, 384, 256, 128],
        dropout_rates: List[float] = [0.3, 0.4, 0.4, 0.5, 0.5],
        lr: float = 0.001,
        batch_size: int = 64,
        epochs: int = 100,
        patience: int = 10,
        growth_weight: float = 0.5,  # Weight for growth rate loss
        use_batch_norm: bool = True,
        use_gradient_clipping: bool = True,
        max_grad_norm: float = 1.0,
        lr_scheduler: str = 'one_cycle',
        device: str = None
    ):
        """
        Initialize neural network component.
        
        Args:
            hidden_dims: List of hidden layer dimensions
            dropout_rates: List of dropout rates for each layer
            lr: Learning rate
            batch_size: Batch size for training
            epochs: Maximum number of epochs
            patience: Patience for early stopping
            growth_weight: Weight for growth rate loss (1-growth_weight for temperature)
            use_batch_norm: Whether to use batch normalization
            use_gradient_clipping: Whether to use gradient clipping
            max_grad_norm: Maximum gradient norm for clipping
            lr_scheduler: Learning rate scheduler type ('one_cycle', 'plateau', 'none')
            device: Device to use (cpu or cuda)
        """
        self.hidden_dims = hidden_dims
        self.dropout_rates = dropout_rates
        self.lr = lr
        self.batch_size = batch_size
        self.epochs = epochs
        self.patience = patience
        self.growth_weight = growth_weight
        self.use_batch_norm = use_batch_norm
        self.use_gradient_clipping = use_gradient_clipping
        self.max_grad_norm = max_grad_norm
        self.lr_scheduler = lr_scheduler
        
        # Determine device
        if device is None:
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = torch.device(device)
            
        logger.info(f"Using device: {self.device}")
        
        # Initialize model, optimizer, and loss function
        self.model = None
        self.optimizer = None
        self.criterion = nn.MSELoss()
        
        # Initialize scaler
        self.feature_scaler = StandardScaler()
        self.growth_scaler = None
        self.temp_scaler = None
        
    def fit(
        self, 
        X: pd.DataFrame, 
        y_growth: pd.Series, 
        y_temp: pd.Series,
        X_val: pd.DataFrame = None, 
        y_val_growth: pd.Series = None,
        y_val_temp: pd.Series = None,
        output_dir: str = None
    ) -> Dict[str, List[float]]:
        """
        Fit neural network to data.
        
        Args:
            X: Training features
            y_growth: Training growth rate targets
            y_temp: Training temperature targets
            X_val: Validation features (optional)
            y_val_growth: Validation growth rate targets (optional)
            y_val_temp: Validation temperature targets (optional)
            output_dir: Directory to save outputs
            
        Returns:
            Dictionary of training history
        """
        # Scale features
        X_scaled = self.feature_scaler.fit_transform(X)
        
        # Store scalers for targets if provided
        if self.growth_scaler is not None:
            y_growth_scaled = y_growth.values
        else:
            y_growth_scaled = y_growth.values
            
        if self.temp_scaler is not None:
            y_temp_scaled = y_temp.values
        else:
            y_temp_scaled = y_temp.values
        
        # Convert validation data if provided
        if X_val is not None and y_val_growth is not None and y_val_temp is not None:
            X_val_scaled = self.feature_scaler.transform(X_val)
            
            if self.growth_scaler is not None:
                y_val_growth_scaled = y_val_growth.values
            else:
                y_val_growth_scaled = y_val_growth.values
                
            if self.temp_scaler is not None:
                y_val_temp_scaled = y_val_temp.values
            else:
                y_val_temp_scaled = y_val_temp.values
                
            val_dataset = MultiTaskDataset(X_val_scaled, y_val_growth_scaled, y_val_temp_scaled)
            val_loader = DataLoader(val_dataset, batch_size=self.batch_size, shuffle=False)
        else:
            val_loader = None
        
        # Create dataset and dataloader
        train_dataset = MultiTaskDataset(X_scaled, y_growth_scaled, y_temp_scaled)
        train_loader = DataLoader(train_dataset, batch_size=self.batch_size, shuffle=True)
        
        # Initialize model
        input_dim = X.shape[1]
        self.model = EnhancedNeuralNetwork(
            input_dim=input_dim,
            hidden_dims=self.hidden_dims,
            dropout_rates=self.dropout_rates,
            use_batch_norm=self.use_batch_norm
        ).to(self.device)
        
        # Initialize optimizer with weight decay
        self.optimizer = optim.AdamW(self.model.parameters(), lr=self.lr, weight_decay=1e-5)
        
        # Learning rate scheduler
        if self.lr_scheduler == 'one_cycle':
            # Calculate total steps
            steps_per_epoch = len(train_loader)
            total_steps = steps_per_epoch * self.epochs
            
            # Set the max_lr for one cycle
            max_lr = self.lr * 10
            
            logger.info(f"Setting up OneCycleLR with max_lr={max_lr}, total_steps={total_steps}")
            
            scheduler = optim.lr_scheduler.OneCycleLR(
                self.optimizer, 
                max_lr=max_lr, 
                total_steps=total_steps,
                pct_start=0.3,  # 30% of training for warmup
                anneal_strategy='cos'
            )
        elif self.lr_scheduler == 'plateau':
            scheduler = optim.lr_scheduler.ReduceLROnPlateau(
                self.optimizer, mode='min', factor=0.5, patience=5, verbose=True, min_lr=1e-6
            )
        else:  # 'none'
            # Dummy scheduler that keeps the learning rate constant
            scheduler = optim.lr_scheduler.LambdaLR(
                self.optimizer, lambda epoch: 1.0
            )
        
        # Early stopping
        best_val_loss = float('inf')
        epochs_no_improve = 0
        best_model_state = None
        
        # Training history
        history = {
            'train_loss': [], 
            'train_growth_loss': [], 
            'train_temp_loss': [],
            'val_loss': [], 
            'val_growth_loss': [], 
            'val_temp_loss': [],
            'learning_rates': []
        }
        
        logger.info(f"Starting enhanced neural network training for {self.epochs} epochs...")
        for epoch in range(self.epochs):
            # Train
            self.model.train()
            train_loss = 0.0
            train_growth_loss = 0.0
            train_temp_loss = 0.0
            
            for features, targets in train_loader:
                growth_targets, temp_targets = targets
                features = features.to(self.device)
                growth_targets = growth_targets.to(self.device)
                temp_targets = temp_targets.to(self.device)
                
                self.optimizer.zero_grad()
                growth_outputs, temp_outputs = self.model(features)
                
                # Calculate losses
                growth_loss = self.criterion(growth_outputs, growth_targets.squeeze())
                temp_loss = self.criterion(temp_outputs, temp_targets.squeeze())
                
                # Weighted combined loss
                loss = self.growth_weight * growth_loss + (1 - self.growth_weight) * temp_loss
                
                loss.backward()
                
                # Gradient clipping to prevent exploding gradients
                if self.use_gradient_clipping:
                    torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=self.max_grad_norm)
                
                self.optimizer.step()
                
                # Update learning rate for one-cycle scheduler
                if self.lr_scheduler == 'one_cycle':
                    scheduler.step()
                
                # Track losses
                train_loss += loss.item() * features.size(0)
                train_growth_loss += growth_loss.item() * features.size(0)
                train_temp_loss += temp_loss.item() * features.size(0)
            
            # Calculate average losses
            train_loss /= len(train_loader.dataset)
            train_growth_loss /= len(train_loader.dataset)
            train_temp_loss /= len(train_loader.dataset)
            
            # Store in history
            history['train_loss'].append(train_loss)
            history['train_growth_loss'].append(train_growth_loss)
            history['train_temp_loss'].append(train_temp_loss)
            history['learning_rates'].append(self.optimizer.param_groups[0]['lr'])
            
            # Validate
            if val_loader is not None:
                val_loss, val_growth_loss, val_temp_loss = self._validate(val_loader)
                
                # Store in history
                history['val_loss'].append(val_loss)
                history['val_growth_loss'].append(val_growth_loss)
                history['val_temp_loss'].append(val_temp_loss)
                
                # Update learning rate for plateau scheduler
                if self.lr_scheduler == 'plateau':
                    scheduler.step(val_loss)
                
                # Early stopping
                if val_loss < best_val_loss:
                    best_val_loss = val_loss
                    epochs_no_improve = 0
                    # Save best model
                    best_model_state = self.model.state_dict().copy()
                else:
                    epochs_no_improve += 1
                
                if epochs_no_improve >= self.patience:
                    logger.info(f"Early stopping after {epoch+1} epochs without improvement")
                    break
                
                logger.info(
                    f"Epoch {epoch+1}/{self.epochs}: "
                    f"Train Loss: {train_loss:.4f} (Growth: {train_growth_loss:.4f}, Temp: {train_temp_loss:.4f}), "
                    f"Val Loss: {val_loss:.4f} (Growth: {val_growth_loss:.4f}, Temp: {val_temp_loss:.4f}), "
                    f"LR: {self.optimizer.param_groups[0]['lr']:.6f}"
                )
            else:
                logger.info(
                    f"Epoch {epoch+1}/{self.epochs}: "
                    f"Train Loss: {train_loss:.4f} (Growth: {train_growth_loss:.4f}, Temp: {train_temp_loss:.4f}), "
                    f"LR: {self.optimizer.param_groups[0]['lr']:.6f}"
                )
        
        # Load best model if validation was used
        if val_loader is not None and best_model_state is not None:
            self.model.load_state_dict(best_model_state)
            logger.info(f"Loaded best model with validation loss: {best_val_loss:.4f}")
        
        # Save model if output_dir is provided
        if output_dir is not None:
            self.save(output_dir)
        
        return history
    
    def _validate(self, val_loader: DataLoader) -> Tuple[float, float, float]:
        """
        Validate model on validation data.
        
        Args:
            val_loader: Validation data loader
            
        Returns:
            Tuple of (combined_loss, growth_loss, temp_loss)
        """
        self.model.eval()
        val_loss = 0.0
        val_growth_loss = 0.0
        val_temp_loss = 0.0
        
        with torch.no_grad():
            for features, targets in val_loader:
                growth_targets, temp_targets = targets
                features = features.to(self.device)
                growth_targets = growth_targets.to(self.device)
                temp_targets = temp_targets.to(self.device)
                
                growth_outputs, temp_outputs = self.model(features)
                
                # Calculate losses
                growth_loss = self.criterion(growth_outputs, growth_targets.squeeze())
                temp_loss = self.criterion(temp_outputs, temp_targets.squeeze())
                
                # Weighted combined loss
                loss = self.growth_weight * growth_loss + (1 - self.growth_weight) * temp_loss
                
                # Track losses
                val_loss += loss.item() * features.size(0)
                val_growth_loss += growth_loss.item() * features.size(0)
                val_temp_loss += temp_loss.item() * features.size(0)
        
        # Calculate average losses
        val_loss /= len(val_loader.dataset)
        val_growth_loss /= len(val_loader.dataset)
        val_temp_loss /= len(val_loader.dataset)
        
        return val_loss, val_growth_loss, val_temp_loss
    
    def predict(self, X: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """
        Generate predictions.
        
        Args:
            X: Features
            
        Returns:
            Tuple of (growth_rate_predictions, temperature_predictions)
        """
        if self.model is None:
            raise ValueError("Model has not been trained yet")
        
        # Scale features
        X_scaled = self.feature_scaler.transform(X)
        
        # Convert to tensor
        X_tensor = torch.tensor(X_scaled, dtype=torch.float32).to(self.device)
        
        # Generate predictions
        self.model.eval()
        with torch.no_grad():
            growth_pred, temp_pred = self.model(X_tensor)
            growth_pred = growth_pred.cpu().numpy()
            temp_pred = temp_pred.cpu().numpy()
        
        # Inverse transform if scalers are available
        if self.growth_scaler is not None:
            growth_pred = self.growth_scaler.inverse_transform(growth_pred.reshape(-1, 1)).flatten()
            
        if self.temp_scaler is not None:
            temp_pred = self.temp_scaler.inverse_transform(temp_pred.reshape(-1, 1)).flatten()
        
        return growth_pred, temp_pred
    
    def predict_growth(self, X: pd.DataFrame) -> np.ndarray:
        """
        Generate growth rate predictions only.
        
        Args:
            X: Features
            
        Returns:
            Growth rate predictions
        """
        growth_pred, _ = self.predict(X)
        return growth_pred
    
    def predict_temperature(self, X: pd.DataFrame) -> np.ndarray:
        """
        Generate temperature predictions only.
        
        Args:
            X: Features
            
        Returns:
            Temperature predictions
        """
        _, temp_pred = self.predict(X)
        return temp_pred
    
    def save(self, output_dir: str) -> None:
        """
        Save model to disk.
        
        Args:
            output_dir: Directory to save model
        """
        if self.model is None:
            raise ValueError("Model has not been trained yet")
        
        os.makedirs(output_dir, exist_ok=True)
        
        # Save model
        torch.save(self.model.state_dict(), os.path.join(output_dir, 'enhanced_nn_model.pth'))
        
        # Save scalers
        import joblib
        joblib.dump(self.feature_scaler, os.path.join(output_dir, 'feature_scaler.joblib'))
        
        if self.growth_scaler is not None:
            joblib.dump(self.growth_scaler, os.path.join(output_dir, 'growth_scaler.joblib'))
            
        if self.temp_scaler is not None:
            joblib.dump(self.temp_scaler, os.path.join(output_dir, 'temp_scaler.joblib'))
        
        # Save hyperparameters
        hyperparams = {
            'hidden_dims': self.hidden_dims,
            'dropout_rates': self.dropout_rates,
            'lr': self.lr,
            'batch_size': self.batch_size,
            'epochs': self.epochs,
            'patience': self.patience,
            'growth_weight': self.growth_weight,
            'use_batch_norm': self.use_batch_norm,
            'use_gradient_clipping': self.use_gradient_clipping,
            'max_grad_norm': self.max_grad_norm,
            'lr_scheduler': self.lr_scheduler
        }
        joblib.dump(hyperparams, os.path.join(output_dir, 'hyperparams.joblib'))
        
        logger.info(f"Enhanced neural network model saved to {output_dir}")
    
    def load(self, input_dir: str) -> None:
        """
        Load model from disk.
        
        Args:
            input_dir: Directory to load model from
        """
        import joblib
        
        # Load scalers
        self.feature_scaler = joblib.load(os.path.join(input_dir, 'feature_scaler.joblib'))
        
        # Try to load target scalers if they exist
        try:
            self.growth_scaler = joblib.load(os.path.join(input_dir, 'growth_scaler.joblib'))
        except:
            self.growth_scaler = None
            
        try:
            self.temp_scaler = joblib.load(os.path.join(input_dir, 'temp_scaler.joblib'))
        except:
            self.temp_scaler = None
        
        # Load hyperparameters
        hyperparams = joblib.load(os.path.join(input_dir, 'hyperparams.joblib'))
        self.hidden_dims = hyperparams['hidden_dims']
        self.dropout_rates = hyperparams['dropout_rates']
        self.lr = hyperparams['lr']
        self.batch_size = hyperparams['batch_size']
        self.epochs = hyperparams['epochs']
        self.patience = hyperparams['patience']
        self.growth_weight = hyperparams['growth_weight']
        self.use_batch_norm = hyperparams['use_batch_norm']
        self.use_gradient_clipping = hyperparams['use_gradient_clipping']
        self.max_grad_norm = hyperparams['max_grad_norm']
        self.lr_scheduler = hyperparams['lr_scheduler']
        
        # Determine input dimension from scaler
        input_dim = len(self.feature_scaler.mean_)
        
        # Initialize model
        self.model = EnhancedNeuralNetwork(
            input_dim=input_dim,
            hidden_dims=self.hidden_dims,
            dropout_rates=self.dropout_rates,
            use_batch_norm=self.use_batch_norm
        ).to(self.device)
        
        # Load model weights
        self.model.load_state_dict(torch.load(
            os.path.join(input_dir, 'enhanced_nn_model.pth'),
            map_location=self.device
        ))
        
        logger.info(f"Enhanced neural network model loaded from {input_dir}")
