#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
DeepMu Outlier Detection Tool.

This script detects and removes outliers from the dataset before model training.
It uses multiple outlier detection methods and produces cleaned datasets for
improved model training.
"""

import os
import argparse
import logging
import numpy as np
import pandas as pd
from sklearn.ensemble import IsolationForest
from sklearn.preprocessing import RobustScaler
from scipy import stats
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Optional

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class OutlierDetector:
    """
    Robust outlier detection for microbial genome datasets.
    
    Implements outlier detection for target variables with transformations:
    1. Square root transformation for growth rate
    2. Log transformation for optimal temperature
    3. Z-score based threshold for transformed values
    """
    
    def __init__(self, 
                zscore_threshold: float = 7.0,  # More lenient default threshold
                contamination: float = 0.05,
                output_dir: Optional[str] = None,
                random_seed: int = 42):
        """
        Initialize the outlier detector.
        
        Args:
            zscore_threshold: Z-score threshold for outlier detection after transformation
            contamination: Expected proportion of outliers in the dataset
            output_dir: Directory to save outputs
            random_seed: Random seed for reproducibility
        """
        self.zscore_threshold = zscore_threshold
        self.contamination = contamination
        self.output_dir = output_dir
        self.random_seed = random_seed
        
        # Initialize feature scalers
        self.feature_scaler = RobustScaler()
        
    def detect_target_outliers(self, 
                              y_growth: pd.Series, 
                              y_temp: pd.Series) -> Tuple[pd.Series, pd.Series]:
        """
        Detect outliers in target variables using transformations and z-score method.
        
        Args:
            y_growth: Growth rate target
            y_temp: Temperature target
            
        Returns:
            Tuple of boolean Series (growth_outliers, temp_outliers)
        """
        logger.info(f"Detecting outliers in target variables using transformations and z-score method (threshold: {self.zscore_threshold})...")
        
        # Apply transformations to normalize the distributions
        # For growth rate: square root transformation (handling zeros and negatives safely)
        y_growth_valid = y_growth.dropna()
        y_growth_min = y_growth_valid.min()
        
        # If there are non-positive values, shift to make all values positive
        if y_growth_min <= 0:
            offset = abs(y_growth_min) + 1e-6
            y_growth_trans = np.sqrt(y_growth_valid + offset)
        else:
            y_growth_trans = np.sqrt(y_growth_valid)
        
        # For temperature: log transformation (handling zeros and negatives safely)
        y_temp_valid = y_temp.dropna()
        y_temp_min = y_temp_valid.min()
        
        # If there are non-positive values, shift to make all values positive
        if y_temp_min <= 0:
            offset = abs(y_temp_min) + 1
            y_temp_trans = np.log(y_temp_valid + offset)
        else:
            y_temp_trans = np.log(y_temp_valid)
        
        # Calculate z-scores
        growth_z = stats.zscore(y_growth_trans)
        temp_z = stats.zscore(y_temp_trans)
        
        # Identify outliers based on z-score
        growth_outliers_array = abs(growth_z) > self.zscore_threshold
        temp_outliers_array = abs(temp_z) > self.zscore_threshold
        
        # Convert back to Series with original indices
        growth_outliers = pd.Series(False, index=y_growth.index)
        growth_outliers[y_growth_valid.index] = growth_outliers_array
        
        temp_outliers = pd.Series(False, index=y_temp.index)
        temp_outliers[y_temp_valid.index] = temp_outliers_array
        
        # Log results
        logger.info(f"Identified {growth_outliers.sum()} growth rate outliers and {temp_outliers.sum()} temperature outliers")
        logger.info(f"Growth rate Z-score threshold: {self.zscore_threshold}, Temperature Z-score threshold: {self.zscore_threshold}")
        
        return growth_outliers, temp_outliers
    
    def detect_feature_outliers(self, 
                               X: pd.DataFrame) -> pd.Series:
        """
        Detect outliers in feature space using Z-score method with a conservative threshold.
        
        Args:
            X: Feature DataFrame
            
        Returns:
            Boolean Series indicating outliers
        """
        # Use a more conservative Z-score threshold for feature outliers
        z_threshold = 5.0
        logger.info(f"Detecting feature outliers using Z-score method (threshold: {z_threshold})...")
        
        # Select high-variance features for efficiency
        feature_vars = X.var()
        high_var_cols = feature_vars[feature_vars > feature_vars.quantile(0.9)].index.tolist()
        high_var_features = X[high_var_cols]
        
        # Calculate Z-scores
        z_scores = stats.zscore(high_var_features, nan_policy='omit')
        
        # Mark as outlier if any feature has Z-score exceeding threshold
        outliers = pd.Series((np.abs(z_scores) > z_threshold).any(axis=1), index=X.index)
        
        logger.info(f"Identified {outliers.sum()} samples with extreme feature values")
        
        return outliers
    
    def detect_multivariate_outliers(self, 
                                    X: pd.DataFrame) -> pd.Series:
        """
        Detect outliers using Isolation Forest for multivariate analysis.
        
        Args:
            X: Feature DataFrame
            
        Returns:
            Boolean Series indicating outliers
        """
        logger.info(f"Detecting multivariate outliers using Isolation Forest (contamination: {self.contamination})...")
        
        # Select features for Isolation Forest (use PCA to reduce dimensions)
        from sklearn.decomposition import PCA
        
        # Fill NaN values for PCA
        X_filled = X.fillna(X.median())
        
        # Apply PCA for dimension reduction
        n_components = min(50, X.shape[1])
        pca = PCA(n_components=n_components, random_state=self.random_seed)
        
        try:
            X_pca = pca.fit_transform(X_filled)
            
            # Run Isolation Forest on PCA components
            iso_forest = IsolationForest(
                contamination=self.contamination,
                random_state=self.random_seed,
                n_jobs=-1
            )
            
            # Predict outliers (-1 for outliers, 1 for inliers)
            outlier_labels = iso_forest.fit_predict(X_pca)
            outliers = pd.Series(outlier_labels == -1, index=X.index)
            
            logger.info(f"Identified {outliers.sum()} multivariate outliers")
            
        except Exception as e:
            logger.warning(f"Error during multivariate outlier detection: {e}")
            logger.warning("Using simple statistical outlier detection as fallback")
            # Fallback to simple outlier detection
            outliers = self.detect_statistical_outliers(X)
        
        return outliers
    
    def detect_statistical_outliers(self, X: pd.DataFrame) -> pd.Series:
        """
        Simple statistical outlier detection as fallback method.
        
        Args:
            X: Feature DataFrame
            
        Returns:
            Boolean Series indicating outliers
        """
        # Calculate median and MAD for each feature
        medians = X.median()
        MADs = (X - medians).abs().median()
        
        # Mark as outlier if more than 40% of features exceed 5*MAD
        thresholds = 5 * MADs
        exceeds_threshold = ((X - medians).abs() > thresholds).sum(axis=1) / X.shape[1]
        outliers = pd.Series(exceeds_threshold > 0.4, index=X.index)
        
        logger.info(f"Identified {outliers.sum()} statistical outliers")
        
        return outliers
    
    def detect_correlation_outliers(self, 
                                  X: pd.DataFrame, 
                                  y_growth: pd.Series, 
                                  y_temp: pd.Series) -> pd.Series:
        """
        Detect outliers based on feature-target correlations.
        
        Args:
            X: Feature DataFrame
            y_growth: Growth rate target
            y_temp: Temperature target
            
        Returns:
            Boolean Series indicating outliers
        """
        logger.info("Detecting outliers based on feature-target correlations...")
        
        # Handle NaN values in targets
        y_growth_clean = y_growth.copy()
        y_temp_clean = y_temp.copy()
        
        # Drop NaN values from targets
        valid_growth_idx = ~y_growth_clean.isna()
        valid_temp_idx = ~y_temp_clean.isna()
        
        # Get top correlated features with each target, handling NaN values safely
        growth_corrs = pd.Series(dtype=float)
        temp_corrs = pd.Series(dtype=float)
        
        # Filter to non-taxonomic features to avoid NaN correlation issues
        taxonomic_cols = [col for col in X.columns if 'taxid_' in col]
        non_taxonomic_cols = [col for col in X.columns if col not in taxonomic_cols]
        
        # Only use non-taxonomic features for correlation analysis
        X_safe = X[non_taxonomic_cols].copy()
        
        try:
            # Calculate correlations safely with non-taxonomic features only
            # First, filter features to only include those with sufficient variance and no NaN values
            X_filtered = X_safe.copy()
            
            # Remove features with NaN values
            X_filtered = X_filtered.dropna(axis=1, how='any')
            
            # Remove features with zero or near-zero variance
            vars = X_filtered.var()
            X_filtered = X_filtered.loc[:, vars > 1e-8]
            
            if X_filtered.empty or X_filtered.shape[1] < 5:
                logger.warning("Not enough valid features for correlation calculation after filtering.")
                return pd.Series(False, index=X.index)
            
            # Calculate correlation using cleaned data
            growth_corrs = X_filtered.loc[valid_growth_idx].corrwith(
                y_growth_clean[valid_growth_idx], method='pearson', numeric_only=True
            ).dropna().abs().nlargest(10)
            
            temp_corrs = X_filtered.loc[valid_temp_idx].corrwith(
                y_temp_clean[valid_temp_idx], method='pearson', numeric_only=True
            ).dropna().abs().nlargest(10)
            
        except Exception as e:
            logger.warning(f"Error calculating correlations: {e}")
            # Return empty outlier series if correlation fails
            return pd.Series(False, index=X.index)
        
        # Combined correlated features
        top_features = list(set(growth_corrs.index) | set(temp_corrs.index))
        
        if not top_features:
            logger.warning("No top correlated features found. Skipping correlation-based outlier detection.")
            return pd.Series(False, index=X.index)
        
        # For each top feature, check if samples lie outside expected range given target value
        outliers = pd.Series(False, index=X.index)
        
        for feature in top_features:
            feature_vals = X[feature]
            
            # Skip features with NaN values
            if feature_vals.isna().any():
                continue
                
            # For growth rate - skip if no valid growth rate data
            if valid_growth_idx.sum() < 10:
                continue
                
            # Create valid dataframe without NaN values
            valid_indices = valid_growth_idx & ~feature_vals.isna()
            if valid_indices.sum() < 10:
                continue
                
            growth_df = pd.DataFrame({
                'feature': feature_vals[valid_indices],
                'target': y_growth_clean[valid_indices]
            })
            
            # Fit simple robust regression
            from sklearn.linear_model import RANSACRegressor
            try:
                ransac = RANSACRegressor(random_state=self.random_seed)
                ransac.fit(growth_df[['target']], growth_df['feature'])
                
                # Predict for all samples (even those not used in fitting)
                valid_pred_idx = valid_growth_idx & ~feature_vals.isna()
                pred_df = pd.DataFrame({'target': y_growth_clean[valid_pred_idx]})
                predictions = ransac.predict(pred_df)
                
                # Calculate residuals
                residuals = feature_vals[valid_pred_idx].values - predictions
                
                # Mark outliers based on residuals
                Q1 = np.percentile(residuals, 25)
                Q3 = np.percentile(residuals, 75)
                IQR = Q3 - Q1
                
                # Create a temporary Series for this feature's outliers
                temp_outliers = pd.Series(False, index=X.index)
                temp_outliers[valid_pred_idx] = (residuals < Q1 - 2.0 * IQR) | (residuals > Q3 + 2.0 * IQR)
                
                # Update the main outliers Series
                outliers = outliers | temp_outliers
                
            except Exception as e:
                logger.warning(f"Error during correlation outlier detection for feature {feature}: {e}")
        
        logger.info(f"Identified {outliers.sum()} correlation-based outliers")
        
        return outliers
    
    def visualize_outliers(self, 
                          X: pd.DataFrame, 
                          y_growth: pd.Series,
                          y_temp: pd.Series,
                          outliers: pd.Series) -> None:
        """
        Create visualizations for detected outliers.
        
        Args:
            X: Feature DataFrame
            y_growth: Growth rate target
            y_temp: Temperature target
            outliers: Boolean Series indicating outliers
        """
        if self.output_dir is None:
            logger.warning("No output directory specified. Skipping visualization.")
            return
        
        logger.info("Creating outlier visualizations...")
        
        # Create figures directory
        figures_dir = os.path.join(self.output_dir, 'figures')
        os.makedirs(figures_dir, exist_ok=True)
        
        # 1. Target variable distributions
        plt.figure(figsize=(12, 6))
        
        plt.subplot(1, 2, 1)
        sns.histplot(data=pd.DataFrame({
            'Growth Rate': y_growth,
            'Type': ['Outlier' if x else 'Normal' for x in outliers]
        }), x='Growth Rate', hue='Type', bins=30, alpha=0.7)
        plt.title('Growth Rate Distribution with Outliers')
        plt.xlabel('Growth Rate')
        plt.ylabel('Count')
        
        plt.subplot(1, 2, 2)
        sns.histplot(data=pd.DataFrame({
            'Optimal Temperature': y_temp,
            'Type': ['Outlier' if x else 'Normal' for x in outliers]
        }), x='Optimal Temperature', hue='Type', bins=30, alpha=0.7)
        plt.title('Optimal Temperature Distribution with Outliers')
        plt.xlabel('Optimal Temperature (°C)')
        plt.ylabel('Count')
        
        plt.tight_layout()
        plt.savefig(os.path.join(figures_dir, 'target_distributions.png'), dpi=300)
        plt.close()
        
        # 2. Growth vs Temperature scatter with outliers highlighted
        plt.figure(figsize=(10, 8))
        scatter_df = pd.DataFrame({
            'Growth Rate': y_growth,
            'Optimal Temperature': y_temp,
            'Outlier': outliers
        })
        
        sns.scatterplot(
            data=scatter_df, 
            x='Optimal Temperature', 
            y='Growth Rate', 
            hue='Outlier',
            style='Outlier',
            alpha=0.7,
            s=80
        )
        plt.title('Growth Rate vs. Optimal Temperature with Outliers')
        plt.xlabel('Optimal Temperature (°C)')
        plt.ylabel('Growth Rate')
        plt.legend(title='Outlier')
        
        plt.tight_layout()
        plt.savefig(os.path.join(figures_dir, 'growth_temp_scatter.png'), dpi=300)
        plt.close()
        
        # 3. Create PCA plot to visualize outliers in feature space
        try:
            from sklearn.decomposition import PCA
            
            # Select a subset of features for visualization
            if X.shape[1] > 100:
                # Select top variance features
                var_sorted = X.var().sort_values(ascending=False)
                X_viz = X[var_sorted.index[:100]].fillna(0)
            else:
                X_viz = X.fillna(0)
            
            # Scale the data
            X_scaled = self.feature_scaler.fit_transform(X_viz)
            
            # Apply PCA
            pca = PCA(n_components=2, random_state=self.random_seed)
            X_pca = pca.fit_transform(X_scaled)
            
            # Create PCA DataFrame
            pca_df = pd.DataFrame({
                'PC1': X_pca[:, 0],
                'PC2': X_pca[:, 1],
                'Outlier': outliers
            })
            
            # PCA plot
            plt.figure(figsize=(10, 8))
            sns.scatterplot(
                data=pca_df, 
                x='PC1', 
                y='PC2', 
                hue='Outlier',
                style='Outlier',
                alpha=0.7,
                s=80
            )
            plt.title('PCA Visualization of Feature Space with Outliers')
            plt.xlabel(f'PC1 ({pca.explained_variance_ratio_[0]:.2%} variance)')
            plt.ylabel(f'PC2 ({pca.explained_variance_ratio_[1]:.2%} variance)')
            plt.legend(title='Outlier')
            
            plt.tight_layout()
            plt.savefig(os.path.join(figures_dir, 'feature_pca.png'), dpi=300)
            plt.close()
            
        except Exception as e:
            logger.warning(f"Error creating PCA visualization: {e}")
            
        # Save overall summary
        with open(os.path.join(figures_dir, 'outlier_summary.txt'), 'w') as f:
            f.write(f"Total samples: {len(outliers)}\n")
            f.write(f"Detected outliers: {outliers.sum()} ({outliers.sum()/len(outliers):.2%})\n")
            f.write(f"Normal samples: {len(outliers) - outliers.sum()}\n")
            f.write("\n")
            f.write("Detection parameters:\n")
            f.write(f"- Z-score threshold: {self.zscore_threshold}\n")
            f.write(f"- Isolation Forest contamination: {self.contamination}\n")
        
        logger.info(f"Outlier visualizations saved to {figures_dir}")

    def detect_and_remove_outliers(self, 
                                  features: pd.DataFrame, 
                                  metadata: pd.DataFrame,
                                  visualize: bool = True,
                                  max_removal_pct: float = 20.0) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        Detect and remove outliers from both features and metadata.
        
        Args:
            features: Feature DataFrame
            metadata: Metadata DataFrame
            visualize: Whether to create visualizations
            max_removal_pct: Maximum percentage of samples that can be removed
            
        Returns:
            Tuple of cleaned (features, metadata)
        """
        logger.info(f"Starting outlier detection on {len(features)} samples...")
        
        # Extract targets
        y_growth = metadata['growth_rate']
        y_temp = metadata['optimal_temperature']
        
        # Detect outliers using different methods - ONLY for target variables
        growth_outliers, temp_outliers = self.detect_target_outliers(y_growth, y_temp)
        
        # Skip feature-based outlier detection to focus only on target variables
        # Create empty series for feature-based outliers
        feature_outliers = pd.Series(False, index=features.index)
        multivariate_outliers = pd.Series(False, index=features.index)
        correlation_outliers = pd.Series(False, index=features.index)
        
        # Combine target outlier detection results only
        all_outliers = growth_outliers | temp_outliers
        
        # Get outlier counts and percentages
        total_samples = len(features)
        outlier_count = all_outliers.sum()
        outlier_percent = (outlier_count / total_samples) * 100
        
        logger.info(f"Detected {outlier_count} outliers ({outlier_percent:.2f}% of data)")
        logger.info(f"- Growth rate outliers: {growth_outliers.sum()} ({growth_outliers.sum()/total_samples:.2%})")
        logger.info(f"- Temperature outliers: {temp_outliers.sum()} ({temp_outliers.sum()/total_samples:.2%})")
        
        # Check if we're removing too many samples
        if outlier_percent > max_removal_pct:
            logger.warning(f"Outlier detection is removing {outlier_percent:.2f}% of data, which exceeds the maximum of {max_removal_pct}%")
            logger.warning("Removing only the most extreme outliers...")
            
            # Prioritize keeping less extreme outliers
            # Apply a stricter Z-score threshold
            stricter_threshold = self.zscore_threshold * 1.5  # 50% higher threshold
            
            # Recalculate with stricter threshold
            y_growth_valid = y_growth.dropna()
            y_growth_min = y_growth_valid.min()
            
            if y_growth_min <= 0:
                offset = abs(y_growth_min) + 1e-6
                y_growth_trans = np.sqrt(y_growth_valid + offset)
            else:
                y_growth_trans = np.sqrt(y_growth_valid)
            
            y_temp_valid = y_temp.dropna()
            y_temp_min = y_temp_valid.min()
            
            if y_temp_min <= 0:
                offset = abs(y_temp_min) + 1
                y_temp_trans = np.log(y_temp_valid + offset)
            else:
                y_temp_trans = np.log(y_temp_valid)
            
            growth_z = stats.zscore(y_growth_trans)
            temp_z = stats.zscore(y_temp_trans)
            
            growth_outliers_array = abs(growth_z) > stricter_threshold
            temp_outliers_array = abs(temp_z) > stricter_threshold
            
            growth_outliers = pd.Series(False, index=y_growth.index)
            growth_outliers[y_growth_valid.index] = growth_outliers_array
            
            temp_outliers = pd.Series(False, index=y_temp.index)
            temp_outliers[y_temp_valid.index] = temp_outliers_array
            
            all_outliers = growth_outliers | temp_outliers
            outlier_count = all_outliers.sum()
            outlier_percent = (outlier_count / total_samples) * 100
            
            logger.warning(f"Using stricter Z-score threshold of {stricter_threshold:.1f}")
            logger.warning(f"After adjustment: {outlier_count} outliers ({outlier_percent:.2f}%)")
        
        # Create visualizations if requested
        if visualize:
            self.visualize_outliers(features, y_growth, y_temp, all_outliers)
        
        # Remove outliers
        features_cleaned = features[~all_outliers]
        metadata_cleaned = metadata[~all_outliers]
        
        logger.info(f"After outlier removal: {len(features_cleaned)} samples remaining")
        
        # Save outlier indices for reference
        if self.output_dir:
            outlier_indices = pd.DataFrame({
                'genome_id': all_outliers.index[all_outliers],
                'is_growth_outlier': growth_outliers[all_outliers].values,
                'is_temp_outlier': temp_outliers[all_outliers].values
            })
            outlier_indices.to_csv(os.path.join(self.output_dir, 'outlier_indices.tsv'), sep='\t', index=False)
            
            # Save clean datasets
            features_cleaned.to_csv(os.path.join(self.output_dir, 'cleaned_features.tsv'), sep='\t')
            metadata_cleaned.to_csv(os.path.join(self.output_dir, 'cleaned_metadata.tsv'), sep='\t')
        
        return features_cleaned, metadata_cleaned

def main():
    """Command line interface for outlier detection."""
    parser = argparse.ArgumentParser(description='DeepMu Outlier Detection Tool - Using Z-score with transformations')
    parser.add_argument('--feature_file', type=str, required=True, help='Path to feature file')
    parser.add_argument('--metadata_file', type=str, required=True, help='Path to metadata file')
    parser.add_argument('--output_dir', type=str, required=True, help='Output directory')
    parser.add_argument('--zscore_threshold', type=float, default=7.0, help='Z-score threshold after transformations')
    parser.add_argument('--visualize_outliers', action='store_true', help='Create outlier visualizations')
    parser.add_argument('--max_removal_pct', type=float, default=20.0, help='Maximum percentage of samples to remove')
    parser.add_argument('--seed', type=int, default=42, help='Random seed')
    
    args = parser.parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Load data
    logger.info(f"Loading features from {args.feature_file}")
    features = pd.read_csv(args.feature_file, sep='\t', index_col='genome_id')
    
    logger.info(f"Loading metadata from {args.metadata_file}")
    metadata = pd.read_csv(args.metadata_file, sep='\t', index_col='genome_id')
    
    # Find common samples
    common_samples = features.index.intersection(metadata.index)
    features = features.loc[common_samples]
    metadata = metadata.loc[common_samples]
    
    logger.info(f"Data loaded: {len(features)} samples, {features.shape[1]} features")
    logger.info(f"Using Z-score based outlier detection with threshold {args.zscore_threshold} for target variables only")
    
    # Create outlier detector
    detector = OutlierDetector(
        zscore_threshold=args.zscore_threshold,
        contamination=0.05,  # Not used anymore but kept for compatibility
        output_dir=args.output_dir,
        random_seed=args.seed
    )
    
    # Detect and remove outliers
    features_cleaned, metadata_cleaned = detector.detect_and_remove_outliers(
        features, metadata, 
        visualize=args.visualize_outliers,
        max_removal_pct=args.max_removal_pct
    )
    
    # Report results
    removed = len(features) - len(features_cleaned)
    pct_removed = (removed / len(features)) * 100
    
    logger.info(f"Outlier detection complete:")
    logger.info(f"- Original samples: {len(features)}")
    logger.info(f"- Outliers removed: {removed} ({pct_removed:.2f}%)")
    logger.info(f"- Clean samples: {len(features_cleaned)}")
    logger.info(f"Clean datasets saved to {args.output_dir}")

if __name__ == "__main__":
    main() 