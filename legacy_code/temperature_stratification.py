#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Temperature Stratification for DeepMu.

This module implements temperature stratification based on biological categories:
- Psychrophiles: -20°C to 20°C (optimal ~15°C or lower)
- Mesophiles: 20°C to 45°C (optimal ~37°C)
- Thermophiles: 50°C to 80°C (optimal ~60-70°C)
- Hyperthermophiles: 80°C and above (optimal ~80°C or higher)
"""

import os
import argparse
import logging
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from typing import Tuple, List, Dict, Optional, Union

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Define temperature categories
TEMP_CATEGORIES = {
    'psychrophile': (-20, 20),
    'mesophile': (20, 45),
    'thermophile': (45, 80),
    'hyperthermophile': (80, 120)
}

def categorize_temperatures(temperatures: np.ndarray) -> np.ndarray:
    """
    Categorize temperatures into biological categories.
    
    Args:
        temperatures: Array of temperature values
        
    Returns:
        Array of category labels
    """
    categories = np.empty(len(temperatures), dtype=object)
    
    for i, temp in enumerate(temperatures):
        if temp <= TEMP_CATEGORIES['psychrophile'][1]:
            categories[i] = 'psychrophile'
        elif temp <= TEMP_CATEGORIES['mesophile'][1]:
            categories[i] = 'mesophile'
        elif temp <= TEMP_CATEGORIES['thermophile'][1]:
            categories[i] = 'thermophile'
        else:
            categories[i] = 'hyperthermophile'
    
    return categories

def analyze_temperature_distribution(metadata_df: pd.DataFrame) -> Dict[str, int]:
    """
    Analyze the distribution of temperatures across biological categories.
    
    Args:
        metadata_df: DataFrame containing metadata with temperature values
        
    Returns:
        Dictionary with counts for each category
    """
    temperatures = metadata_df['optimal_temperature'].values
    categories = categorize_temperatures(temperatures)
    
    # Count samples in each category
    category_counts = {}
    for category in TEMP_CATEGORIES.keys():
        category_counts[category] = np.sum(categories == category)
    
    return category_counts

def calculate_category_reliability(metadata_df: pd.DataFrame) -> pd.Series:
    """
    Calculate reliability scores for each temperature category.
    
    Args:
        metadata_df: DataFrame containing metadata with temperature values
        
    Returns:
        Series with reliability scores for each genome
    """
    temperatures = metadata_df['optimal_temperature'].values
    categories = categorize_temperatures(temperatures)
    
    # Create reliability scores based on category
    reliability_scores = np.ones(len(temperatures))
    
    # Adjust reliability based on category and temperature value
    for i, (temp, category) in enumerate(zip(temperatures, categories)):
        if category == 'psychrophile':
            # Higher reliability near 15°C (optimal for psychrophiles)
            reliability_scores[i] = 1.0 - 0.03 * abs(temp - 15)
        elif category == 'mesophile':
            # Higher reliability near 37°C (optimal for mesophiles)
            reliability_scores[i] = 1.0 - 0.02 * abs(temp - 37)
        elif category == 'thermophile':
            # Higher reliability near 65°C (optimal for thermophiles)
            reliability_scores[i] = 1.0 - 0.02 * abs(temp - 65)
        elif category == 'hyperthermophile':
            # Higher reliability near 90°C (optimal for hyperthermophiles)
            reliability_scores[i] = 1.0 - 0.02 * abs(temp - 90)
    
    # Ensure all scores are between 0 and 1
    reliability_scores = np.clip(reliability_scores, 0.0, 1.0)
    
    return pd.Series(reliability_scores, index=metadata_df.index)

def filter_by_temperature_category(
    features_df: pd.DataFrame,
    metadata_df: pd.DataFrame,
    reliability_threshold: float = 0.6,
    visualize: bool = True,
    output_dir: Optional[str] = None
) -> Tuple[Dict[str, pd.DataFrame], Dict[str, pd.DataFrame]]:
    """
    Filter the dataset by temperature category and reliability.
    
    Args:
        features_df: DataFrame containing genomic features
        metadata_df: DataFrame containing metadata with temperature values
        reliability_threshold: Minimum reliability score to keep a genome
        visualize: Whether to create visualizations
        output_dir: Directory to save visualizations
        
    Returns:
        Tuple of (filtered_features_dict, filtered_metadata_dict)
    """
    # Get common indices
    common_indices = features_df.index.intersection(metadata_df.index)
    features_df = features_df.loc[common_indices]
    metadata_df = metadata_df.loc[common_indices]
    
    logger.info(f"Found {len(common_indices)} genomes with both features and metadata")
    
    # Categorize temperatures
    temperatures = metadata_df['optimal_temperature'].values
    categories = categorize_temperatures(temperatures)
    metadata_df['temp_category'] = categories
    
    # Calculate reliability scores
    reliability_scores = calculate_category_reliability(metadata_df)
    metadata_df['reliability_score'] = reliability_scores
    
    # Analyze category distribution
    category_counts = analyze_temperature_distribution(metadata_df)
    logger.info(f"Temperature category distribution: {category_counts}")
    
    # Filter by reliability
    reliable_indices = reliability_scores[reliability_scores >= reliability_threshold].index
    logger.info(f"Retained {len(reliable_indices)} genomes with reliability score >= {reliability_threshold}")
    
    # Split by category
    filtered_features_dict = {}
    filtered_metadata_dict = {}
    
    for category in TEMP_CATEGORIES.keys():
        category_indices = metadata_df[metadata_df['temp_category'] == category].index
        reliable_category_indices = category_indices.intersection(reliable_indices)
        
        filtered_features_dict[category] = features_df.loc[reliable_category_indices]
        filtered_metadata_dict[category] = metadata_df.loc[reliable_category_indices]
        
        logger.info(f"{category.capitalize()}: {len(category_indices)} genomes, {len(reliable_category_indices)} reliable")
    
    # Create visualizations
    if visualize and output_dir:
        os.makedirs(output_dir, exist_ok=True)
        
        # Plot temperature distribution
        plt.figure(figsize=(10, 6))
        plt.hist(temperatures, bins=30, alpha=0.7)
        for category, (min_temp, max_temp) in TEMP_CATEGORIES.items():
            plt.axvline(min_temp, color='r', linestyle='--', alpha=0.5)
            plt.axvline(max_temp, color='r', linestyle='--', alpha=0.5)
            plt.text((min_temp + max_temp) / 2, plt.ylim()[1] * 0.9, category, 
                     horizontalalignment='center', fontsize=12)
        plt.xlabel('Optimal Temperature (°C)')
        plt.ylabel('Count')
        plt.title('Temperature Distribution with Biological Categories')
        plt.grid(True, alpha=0.3)
        plt.savefig(os.path.join(output_dir, 'temperature_distribution.png'), dpi=300)
        
        # Plot reliability scores
        plt.figure(figsize=(10, 6))
        plt.scatter(temperatures, reliability_scores, alpha=0.5)
        plt.axhline(reliability_threshold, color='r', linestyle='--', label=f'Threshold ({reliability_threshold})')
        plt.xlabel('Optimal Temperature (°C)')
        plt.ylabel('Reliability Score')
        plt.title('Temperature Reliability Scores')
        plt.grid(True, alpha=0.3)
        plt.legend()
        plt.savefig(os.path.join(output_dir, 'reliability_scores.png'), dpi=300)
        
        # Plot category distribution
        plt.figure(figsize=(10, 6))
        categories_list = list(TEMP_CATEGORIES.keys())
        counts = [category_counts[cat] for cat in categories_list]
        plt.bar(categories_list, counts)
        plt.xlabel('Temperature Category')
        plt.ylabel('Count')
        plt.title('Distribution of Temperature Categories')
        plt.grid(True, alpha=0.3, axis='y')
        plt.savefig(os.path.join(output_dir, 'category_distribution.png'), dpi=300)
        
        logger.info(f"Saved visualizations to {output_dir}")
    
    return filtered_features_dict, filtered_metadata_dict

def create_stratified_datasets(
    features_df: pd.DataFrame,
    metadata_df: pd.DataFrame,
    output_dir: str,
    reliability_threshold: float = 0.6,
    visualize: bool = True
) -> Dict[str, Tuple[str, str]]:
    """
    Create stratified datasets based on temperature categories.
    
    Args:
        features_df: DataFrame containing genomic features
        metadata_df: DataFrame containing metadata with temperature values
        output_dir: Directory to save the filtered datasets
        reliability_threshold: Minimum reliability score to keep a genome
        visualize: Whether to create visualizations
        
    Returns:
        Dictionary with paths to filtered datasets for each category
    """
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Filter by temperature category
    filtered_features_dict, filtered_metadata_dict = filter_by_temperature_category(
        features_df,
        metadata_df,
        reliability_threshold=reliability_threshold,
        visualize=visualize,
        output_dir=os.path.join(output_dir, 'visualizations')
    )
    
    # Save filtered datasets
    dataset_paths = {}
    
    for category in TEMP_CATEGORIES.keys():
        category_dir = os.path.join(output_dir, category)
        os.makedirs(category_dir, exist_ok=True)
        
        features_path = os.path.join(category_dir, 'features.tsv')
        metadata_path = os.path.join(category_dir, 'metadata.tsv')
        
        filtered_features_dict[category].to_csv(features_path, sep='\t')
        filtered_metadata_dict[category].to_csv(metadata_path, sep='\t')
        
        dataset_paths[category] = (features_path, metadata_path)
        
        logger.info(f"Saved {category} datasets to {category_dir}")
    
    # Save combined reliable dataset
    combined_features = pd.concat([df for df in filtered_features_dict.values()])
    combined_metadata = pd.concat([df for df in filtered_metadata_dict.values()])
    
    combined_features_path = os.path.join(output_dir, 'combined_features.tsv')
    combined_metadata_path = os.path.join(output_dir, 'combined_metadata.tsv')
    
    combined_features.to_csv(combined_features_path, sep='\t')
    combined_metadata.to_csv(combined_metadata_path, sep='\t')
    
    dataset_paths['combined'] = (combined_features_path, combined_metadata_path)
    
    logger.info(f"Saved combined reliable dataset with {len(combined_features)} genomes")
    
    return dataset_paths

def main():
    parser = argparse.ArgumentParser(description='Temperature Stratification for DeepMu')
    parser.add_argument('--feature-file', type=str, required=True, help='Path to feature file')
    parser.add_argument('--metadata-file', type=str, required=True, help='Path to metadata file')
    parser.add_argument('--output-dir', type=str, required=True, help='Directory to save stratified datasets')
    parser.add_argument('--reliability-threshold', type=float, default=0.6, help='Minimum reliability score')
    parser.add_argument('--no-visualize', action='store_false', dest='visualize', help='Disable visualization generation')
    
    args = parser.parse_args()
    
    try:
        # Load data
        logger.info(f"Loading features from {args.feature_file}")
        features_df = pd.read_csv(args.feature_file, sep='\t', index_col='genome_id')
        
        logger.info(f"Loading metadata from {args.metadata_file}")
        metadata_df = pd.read_csv(args.metadata_file, sep='\t', index_col='genome_id')
        
        # Create stratified datasets
        dataset_paths = create_stratified_datasets(
            features_df,
            metadata_df,
            args.output_dir,
            reliability_threshold=args.reliability_threshold,
            visualize=args.visualize
        )
        
        logger.info(f"Created stratified datasets in {args.output_dir}")
        return 0
    
    except Exception as e:
        logger.error(f"Error creating stratified datasets: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == '__main__':
    main()
