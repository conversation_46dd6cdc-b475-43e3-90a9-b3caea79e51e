#!/bin/bash

# DeepMu Final Model Prediction Script
# This script runs predictions using the trained final model
# incorporating RL temperature and high-performance hybrid growth rate models

# Default parameters
MODEL_DIR="models/final_deepmu_model"
INPUT_FILE="training_data/combined_features.tsv"
OUTPUT_DIR="predictions/final_model"

# Check if input file is provided
if [ $# -ge 1 ]; then
    INPUT_FILE="$1"
fi

# Check if model directory is provided
if [ $# -ge 2 ]; then
    MODEL_DIR="$2"
fi

# Check if output directory is provided
if [ $# -ge 3 ]; then
    OUTPUT_DIR="$3"
fi

# Create output directory
mkdir -p "$OUTPUT_DIR"

# Display banner
echo "=================================================="
echo "    DeepMu Final Model Predictions                "
echo "=================================================="
echo ""
echo "This prediction system combines:"
echo "1. RL-based temperature prediction with calibration"
echo "2. High-performance hybrid growth model (R²>0.92)"
echo ""
echo "Input data: $INPUT_FILE"
echo "Model directory: $MODEL_DIR"
echo "Output directory: $OUTPUT_DIR"
echo ""

# Run prediction
python final_deepmu_model.py \
    --predict_only \
    --input_data "$INPUT_FILE" \
    --model_dir "$MODEL_DIR" \
    --output_dir "$OUTPUT_DIR"

# Check if prediction was successful
if [ $? -eq 0 ]; then
    echo ""
    echo "=================================================="
    echo "    Prediction completed successfully!            "
    echo "=================================================="
    echo ""
    echo "Predictions saved to: $OUTPUT_DIR/predictions.tsv"
    echo ""
    echo "The final model combines:"
    echo "- RL-optimized temperature prediction (with calibration)"
    echo "- State-of-the-art hybrid growth rate model (FinalHybridModel)"
    echo "- Rank correlation preservation with proper scaling"
    echo ""
else
    echo ""
    echo "=================================================="
    echo "    Prediction failed!                            "
    echo "=================================================="
    echo ""
    echo "Please check the error messages above for details."
    echo ""
    exit 1
fi 