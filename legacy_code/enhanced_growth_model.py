#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Enhanced growth rate prediction model using TabNet.
This model implements TabNet with Bayesian hyperparameter optimization.
"""

import os
import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Tuple, Union, Optional
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt
import seaborn as sns
import joblib
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
from pytorch_tabnet.tab_model import TabNetRegressor
from skopt import BayesSearchCV
from skopt.space import Real, Integer, Categorical

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnhancedGrowthModel:
    """
    Enhanced growth rate prediction model using TabNet with Bayesian optimization.
    """

    def __init__(self, params=None):
        """
        Initialize the model with parameters.

        Args:
            params: Parameters for TabNet
        """
        # Set default parameters if not provided
        self.params = params or {
            'n_d': 64,
            'n_a': 64,
            'n_steps': 5,
            'gamma': 1.5,
            'n_independent': 2,
            'n_shared': 2,
            'lambda_sparse': 1e-4,
            'optimizer_fn': torch.optim.Adam,
            'optimizer_params': {'lr': 2e-2},
            'mask_type': 'entmax',
            'scheduler_params': {
                'mode': 'min',
                'patience': 5,
                'min_lr': 1e-5,
                'factor': 0.5,
            },
            'scheduler_fn': torch.optim.lr_scheduler.ReduceLROnPlateau,
            'verbose': 0,
            'device_name': 'auto'
        }

        # Initialize model
        self.model = TabNetRegressor(**self.params)

        # For storing feature importances
        self.feature_importances = None

        # For storing evaluation metrics
        self.metrics = {}

        # For storing feature names
        self.feature_names = None

        # For storing scaler
        self.scaler = StandardScaler()

    def optimize_hyperparameters(self, X: pd.DataFrame, y: pd.Series, cv: int = 5) -> Dict:
        """
        Optimize hyperparameters using Bayesian optimization.

        Args:
            X: Features
            y: Target
            cv: Number of cross-validation folds

        Returns:
            Dictionary of best parameters
        """
        logger.info("Optimizing hyperparameters for growth rate prediction...")

        # Scale features
        X_scaled = pd.DataFrame(
            self.scaler.fit_transform(X),
            columns=X.columns,
            index=X.index
        )

        # Define parameter space
        param_space = {
            'n_d': Integer(8, 128),
            'n_a': Integer(8, 128),
            'n_steps': Integer(3, 10),
            'gamma': Real(1.0, 2.0),
            'n_independent': Integer(1, 5),
            'n_shared': Integer(1, 5),
            'lambda_sparse': Real(1e-6, 1e-3, prior='log-uniform'),
            'learning_rate': Real(1e-4, 1e-1, prior='log-uniform')
        }

        # Define TabNet model for optimization
        def create_model(**params):
            lr = params.pop('learning_rate')
            model = TabNetRegressor(
                optimizer_params={'lr': lr},
                optimizer_fn=torch.optim.Adam,
                scheduler_fn=torch.optim.lr_scheduler.ReduceLROnPlateau,
                scheduler_params={
                    'mode': 'min',
                    'patience': 5,
                    'min_lr': 1e-5,
                    'factor': 0.5,
                },
                mask_type='entmax',
                verbose=0,
                device_name='auto',
                **params
            )
            return model

        # Define scoring function
        def neg_mean_squared_error(estimator, X, y):
            y_pred = estimator.predict(X)
            return -mean_squared_error(y, y_pred)

        # Initialize Bayesian search
        bayes_search = BayesSearchCV(
            estimator=create_model(),
            search_spaces=param_space,
            n_iter=50,
            cv=cv,
            scoring=neg_mean_squared_error,
            n_jobs=-1,
            verbose=1,
            random_state=42
        )

        # Fit Bayesian search
        bayes_search.fit(X_scaled.values, y.values)

        # Get best parameters
        best_params = bayes_search.best_params_

        logger.info(f"Best parameters: {best_params}")
        logger.info(f"Best score: {-bayes_search.best_score_:.4f} (MSE)")

        # Update model parameters
        lr = best_params.pop('learning_rate')
        self.params.update(best_params)
        self.params['optimizer_params'] = {'lr': lr}

        # Reinitialize model with best parameters
        self.model = TabNetRegressor(**self.params)

        return best_params

    def fit(self, X_train: pd.DataFrame, y_train: pd.Series,
            X_val: pd.DataFrame = None, y_val: pd.Series = None,
            max_epochs: int = 200, patience: int = 15) -> 'EnhancedGrowthModel':
        """
        Train the model.

        Args:
            X_train: Training features
            y_train: Training target
            X_val: Validation features (optional)
            y_val: Validation target (optional)
            max_epochs: Maximum number of epochs
            patience: Patience for early stopping

        Returns:
            Self
        """
        # Store feature names
        self.feature_names = X_train.columns.tolist()

        # Scale features
        X_train_scaled = pd.DataFrame(
            self.scaler.fit_transform(X_train),
            columns=X_train.columns,
            index=X_train.index
        )

        # Prepare validation data if provided
        if X_val is not None and y_val is not None:
            X_val_scaled = pd.DataFrame(
                self.scaler.transform(X_val),
                columns=X_val.columns,
                index=X_val.index
            )

            # Reshape targets to 2D for TabNet
            y_train_2d = y_train.values.reshape(-1, 1)
            y_val_2d = y_val.values.reshape(-1, 1)

            # Train with validation data
            self.model.fit(
                X_train_scaled.values, y_train_2d,
                eval_set=[(X_val_scaled.values, y_val_2d)],
                max_epochs=max_epochs,
                patience=patience
            )

            # Calculate validation metrics
            val_preds = self.model.predict(X_val_scaled.values)
            val_r2 = r2_score(y_val, val_preds)
            val_rmse = np.sqrt(mean_squared_error(y_val, val_preds))
            val_mae = mean_absolute_error(y_val, val_preds)

            self.metrics['val_r2'] = val_r2
            self.metrics['val_rmse'] = val_rmse
            self.metrics['val_mae'] = val_mae

            logger.info(f"Validation metrics - R²: {val_r2:.4f}, RMSE: {val_rmse:.4f}, MAE: {val_mae:.4f}")
        else:
            # Train without validation data
            # Reshape targets to 2D for TabNet
            y_train_2d = y_train.values.reshape(-1, 1)

            # Train without validation data
            self.model.fit(
                X_train_scaled.values, y_train_2d,
                max_epochs=max_epochs,
                patience=patience
            )

        # Calculate training metrics
        train_preds = self.model.predict(X_train_scaled.values)
        train_r2 = r2_score(y_train, train_preds)
        train_rmse = np.sqrt(mean_squared_error(y_train, train_preds))
        train_mae = mean_absolute_error(y_train, train_preds)

        self.metrics['train_r2'] = train_r2
        self.metrics['train_rmse'] = train_rmse
        self.metrics['train_mae'] = train_mae

        logger.info(f"Training metrics - R²: {train_r2:.4f}, RMSE: {train_rmse:.4f}, MAE: {train_mae:.4f}")

        # Get feature importances
        self.feature_importances = pd.Series(
            self.model.feature_importances_,
            index=X_train.columns
        ).sort_values(ascending=False)

        return self

    def predict(self, X: pd.DataFrame) -> np.ndarray:
        """
        Generate predictions for new data.

        Args:
            X: Features

        Returns:
            Predictions
        """
        # Scale features
        X_scaled = pd.DataFrame(
            self.scaler.transform(X),
            columns=X.columns,
            index=X.index
        )

        # Generate predictions
        return self.model.predict(X_scaled.values)

    def evaluate(self, X: pd.DataFrame, y: pd.Series) -> Dict[str, float]:
        """
        Evaluate the model on new data.

        Args:
            X: Features
            y: Target

        Returns:
            Dictionary of evaluation metrics
        """
        # Generate predictions
        y_pred = self.predict(X)

        # Calculate metrics
        metrics = {
            'R2': r2_score(y, y_pred),
            'RMSE': np.sqrt(mean_squared_error(y, y_pred)),
            'MAE': mean_absolute_error(y, y_pred)
        }

        # Log metrics
        logger.info(f"Evaluation metrics - R²: {metrics['R2']:.4f}, RMSE: {metrics['RMSE']:.4f}, MAE: {metrics['MAE']:.4f}")

        return metrics

    def plot_feature_importance(self, output_dir: str = None, n_features: int = 20) -> None:
        """
        Plot feature importance.

        Args:
            output_dir: Directory to save plots (optional)
            n_features: Number of top features to plot
        """
        if self.feature_importances is None or not isinstance(self.feature_importances, pd.Series):
            logger.warning("Feature importances not available. Train the model first.")
            return

        # Create output directory if provided
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)

        # Plot feature importance
        plt.figure(figsize=(12, 8))
        top_features = self.feature_importances.head(n_features)
        sns.barplot(x=top_features.values, y=top_features.index)
        plt.title(f'Top {n_features} Feature Importance for Growth Rate Prediction')
        plt.tight_layout()

        if output_dir:
            plt.savefig(os.path.join(output_dir, 'growth_feature_importance.png'))
        else:
            plt.show()

    def plot_predictions(self, X: pd.DataFrame, y: pd.Series, output_dir: str = None) -> None:
        """
        Plot predictions vs actual values.

        Args:
            X: Features
            y: Target
            output_dir: Directory to save plots (optional)
        """
        # Generate predictions
        y_pred = self.predict(X)

        # Ensure predictions are 1-dimensional
        if len(y_pred.shape) > 1:
            y_pred = y_pred.flatten()

        # Convert predictions to Series with the same index as y for proper alignment
        y_pred_series = pd.Series(y_pred, index=y.index)

        # Calculate metrics
        r2 = r2_score(y, y_pred_series)
        rmse = np.sqrt(mean_squared_error(y, y_pred_series))

        # Create scatter plot
        plt.figure(figsize=(10, 6))
        plt.scatter(y, y_pred_series, alpha=0.5)
        plt.plot([y.min(), y.max()], [y.min(), y.max()], 'r--')
        plt.xlabel('Actual Growth Rate')
        plt.ylabel('Predicted Growth Rate')
        plt.title(f'Growth Rate Predictions (R² = {r2:.4f}, RMSE = {rmse:.4f})')
        plt.tight_layout()

        if output_dir:
            os.makedirs(output_dir, exist_ok=True)
            plt.savefig(os.path.join(output_dir, 'growth_predictions_scatter.png'))
        else:
            plt.show()

        # Create residual plot
        residuals = y - y_pred_series
        plt.figure(figsize=(10, 6))
        plt.scatter(y_pred_series, residuals, alpha=0.5)
        plt.axhline(y=0, color='r', linestyle='--')
        plt.xlabel('Predicted Growth Rate')
        plt.ylabel('Residuals')
        plt.title('Residual Plot for Growth Rate Predictions')
        plt.tight_layout()

        if output_dir:
            plt.savefig(os.path.join(output_dir, 'growth_predictions_residuals.png'))
        else:
            plt.show()

    def save(self, output_dir: str) -> None:
        """
        Save the model to disk.

        Args:
            output_dir: Directory to save the model
        """
        os.makedirs(output_dir, exist_ok=True)

        # Save model
        self.model.save_model(os.path.join(output_dir, 'growth_tabnet_model.zip'))

        # Save scaler
        joblib.dump(self.scaler, os.path.join(output_dir, 'growth_scaler.joblib'))

        # Save feature importances
        if self.feature_importances is not None:
            self.feature_importances.to_csv(os.path.join(output_dir, 'growth_feature_importances.csv'))

        # Save metrics
        if self.metrics:
            pd.DataFrame([self.metrics]).to_csv(os.path.join(output_dir, 'growth_metrics.csv'), index=False)

        # Save feature names
        if self.feature_names:
            with open(os.path.join(output_dir, 'growth_feature_names.txt'), 'w') as f:
                for feature in self.feature_names:
                    f.write(f"{feature}\n")

        logger.info(f"Growth model saved to {output_dir}")

    def load(self, input_dir: str) -> 'EnhancedGrowthModel':
        """
        Load the model from disk.

        Args:
            input_dir: Directory to load the model from

        Returns:
            Self
        """
        # Load model
        self.model.load_model(os.path.join(input_dir, 'growth_tabnet_model.zip'))

        # Load scaler
        self.scaler = joblib.load(os.path.join(input_dir, 'growth_scaler.joblib'))

        # Load feature importances if available
        feature_importances_path = os.path.join(input_dir, 'growth_feature_importances.csv')
        if os.path.exists(feature_importances_path):
            self.feature_importances = pd.read_csv(feature_importances_path, index_col=0, squeeze=True)

        # Load metrics if available
        metrics_path = os.path.join(input_dir, 'growth_metrics.csv')
        if os.path.exists(metrics_path):
            self.metrics = pd.read_csv(metrics_path).iloc[0].to_dict()

        # Load feature names if available
        feature_names_path = os.path.join(input_dir, 'growth_feature_names.txt')
        if os.path.exists(feature_names_path):
            with open(feature_names_path, 'r') as f:
                self.feature_names = [line.strip() for line in f.readlines()]

        logger.info(f"Growth model loaded from {input_dir}")

        return self

if __name__ == "__main__":
    # This module is not meant to be run directly
    logger.info("This module provides the EnhancedGrowthModel class for growth rate prediction.")
