#!/bin/bash
# Run script for training the RLVR model with the best hyperparameters found

# Check if best parameters file exists
BEST_PARAMS_FILE="hyperparam_search/best_params.json"
if [ ! -f "$BEST_PARAMS_FILE" ]; then
    echo "Best parameters file not found. Run hyperparameter search first."
    exit 1
fi

# Extract parameters from the JSON file using jq (if available)
if command -v jq &> /dev/null; then
    HIDDEN_DIM=$(jq -r '.params.hidden_dim' "$BEST_PARAMS_FILE")
    NUM_HEADS=$(jq -r '.params.num_heads' "$BEST_PARAMS_FILE")
    NUM_LAYERS=$(jq -r '.params.num_layers' "$BEST_PARAMS_FILE")
    DROPOUT=$(jq -r '.params.dropout' "$BEST_PARAMS_FILE")
    ACTIVATION=$(jq -r '.params.activation' "$BEST_PARAMS_FILE")
    ALPHA=$(jq -r '.params.alpha' "$BEST_PARAMS_FILE")
    BETA=$(jq -r '.params.beta' "$BEST_PARAMS_FILE")
    GAMMA=$(jq -r '.params.gamma' "$BEST_PARAMS_FILE")
    ACCURACY_SCALE=$(jq -r '.params.accuracy_scale' "$BEST_PARAMS_FILE")
    LR=$(jq -r '.params.lr' "$BEST_PARAMS_FILE")
    WEIGHT_DECAY=$(jq -r '.params.weight_decay' "$BEST_PARAMS_FILE")
    BATCH_SIZE=$(jq -r '.params.batch_size' "$BEST_PARAMS_FILE")
    N_FEATURES=$(jq -r '.params.n_features' "$BEST_PARAMS_FILE")
    
    echo "Using best parameters from hyperparameter search:"
    echo "  Hidden Dim: $HIDDEN_DIM"
    echo "  Num Heads: $NUM_HEADS"
    echo "  Num Layers: $NUM_LAYERS"
    echo "  Dropout: $DROPOUT"
    echo "  Activation: $ACTIVATION"
    echo "  Alpha: $ALPHA"
    echo "  Beta: $BETA"
    echo "  Gamma: $GAMMA"
    echo "  Accuracy Scale: $ACCURACY_SCALE"
    echo "  Learning Rate: $LR"
    echo "  Weight Decay: $WEIGHT_DECAY"
    echo "  Batch Size: $BATCH_SIZE"
    echo "  Num Features: $N_FEATURES"
else
    echo "jq not found. Using default best parameters."
    # Default best parameters (to be updated after hyperparameter search)
    HIDDEN_DIM=512
    NUM_HEADS=8
    NUM_LAYERS=3
    DROPOUT=0.3
    ACTIVATION="silu"
    ALPHA=0.7
    BETA=0.2
    GAMMA=0.1
    ACCURACY_SCALE=0.5
    LR=5e-5
    WEIGHT_DECAY=1e-4
    BATCH_SIZE=64
    N_FEATURES=100
fi

# Make script executable
chmod +x train_rlvr_model.py

# Create output directories
mkdir -p models/rlvr_model_best
mkdir -p metrics/rlvr_model_best

# Run training script with best hyperparameters
python train_rlvr_model.py \
    --feature-file training_data/combined_features.tsv \
    --metadata training_data/metadata.tsv \
    --output-dir models/rlvr_model_best \
    --metrics-dir metrics/rlvr_model_best \
    --hidden-dim $HIDDEN_DIM \
    --num-heads $NUM_HEADS \
    --num-layers $NUM_LAYERS \
    --dropout $DROPOUT \
    --use-layer-norm \
    --use-residual \
    --use-value-head \
    --activation $ACTIVATION \
    --alpha $ALPHA \
    --beta $BETA \
    --gamma $GAMMA \
    --accuracy-scale $ACCURACY_SCALE \
    --lr $LR \
    --weight-decay $WEIGHT_DECAY \
    --batch-size $BATCH_SIZE \
    --epochs 300 \
    --early-stopping-patience 30 \
    --entropy-coef 0.01 \
    --value-loss-coef 0.5 \
    --max-grad-norm 1.0 \
    --seed 42 \
    --feature-selection \
    --n-features $N_FEATURES
