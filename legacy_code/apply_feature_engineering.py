#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Apply Feature Engineering for Temperature Prediction.

This script applies the feature engineering techniques from temperature_feature_engineering.py
to the filtered dataset.
"""

import os
import argparse
import logging
import numpy as np
import pandas as pd
from temperature_feature_engineering import engineer_temperature_features

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def apply_feature_engineering(
    feature_file: str,
    metadata_file: str,
    output_dir: str,
    use_interactions: bool = True,
    use_nonlinear: bool = True,
    use_pca: bool = True,
    use_domain: bool = True,
    n_pca_components: int = 50,
    top_k_interactions: int = 50
) -> str:
    """
    Apply feature engineering to the dataset.
    
    Args:
        feature_file: Path to the feature file (TSV)
        metadata_file: Path to the metadata file (TSV)
        output_dir: Directory to save the engineered dataset
        use_interactions: Whether to create interaction features
        use_nonlinear: Whether to create nonlinear features
        use_pca: Whether to create PCA features
        use_domain: Whether to create domain-specific features
        n_pca_components: Number of PCA components to create
        top_k_interactions: Number of top features to consider for interactions
        
    Returns:
        Path to the engineered features file
    """
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Load data
    logger.info(f"Loading features from {feature_file}")
    features_df = pd.read_csv(feature_file, sep='\t', index_col='genome_id')
    
    logger.info(f"Loading metadata from {metadata_file}")
    metadata_df = pd.read_csv(metadata_file, sep='\t', index_col='genome_id')
    
    # Get common indices
    common_indices = features_df.index.intersection(metadata_df.index)
    features_df = features_df.loc[common_indices]
    metadata_df = metadata_df.loc[common_indices]
    
    logger.info(f"Found {len(common_indices)} genomes with both features and metadata")
    
    # Apply feature engineering
    logger.info("Applying feature engineering...")
    engineered_features = engineer_temperature_features(
        features_df,
        metadata_df,
        use_interactions=use_interactions,
        use_nonlinear=use_nonlinear,
        use_pca=use_pca,
        use_domain=use_domain,
        n_pca_components=n_pca_components,
        top_k_interactions=top_k_interactions
    )
    
    logger.info(f"Original features: {features_df.shape[1]}, Engineered features: {engineered_features.shape[1]}")
    
    # Save engineered features
    engineered_features_path = os.path.join(output_dir, 'engineered_features.tsv')
    engineered_features.to_csv(engineered_features_path, sep='\t')
    
    logger.info(f"Saved engineered features to {engineered_features_path}")
    
    # Save feature names for reference
    feature_names_path = os.path.join(output_dir, 'feature_names.txt')
    with open(feature_names_path, 'w') as f:
        for feature in engineered_features.columns:
            f.write(f"{feature}\n")
    
    logger.info(f"Saved feature names to {feature_names_path}")
    
    return engineered_features_path

def main():
    parser = argparse.ArgumentParser(description='Apply feature engineering for temperature prediction')
    parser.add_argument('--feature-file', type=str, required=True,
                        help='Path to the feature file (TSV)')
    parser.add_argument('--metadata-file', type=str, required=True,
                        help='Path to the metadata file (TSV)')
    parser.add_argument('--output-dir', type=str, required=True,
                        help='Directory to save the engineered dataset')
    parser.add_argument('--use-interactions', action='store_true',
                        help='Create interaction features')
    parser.add_argument('--use-nonlinear', action='store_true',
                        help='Create nonlinear features')
    parser.add_argument('--use-pca', action='store_true',
                        help='Create PCA features')
    parser.add_argument('--use-domain', action='store_true',
                        help='Create domain-specific features')
    parser.add_argument('--n-pca-components', type=int, default=50,
                        help='Number of PCA components to create')
    parser.add_argument('--top-k-interactions', type=int, default=50,
                        help='Number of top features to consider for interactions')
    
    args = parser.parse_args()
    
    try:
        apply_feature_engineering(
            args.feature_file,
            args.metadata_file,
            args.output_dir,
            use_interactions=args.use_interactions,
            use_nonlinear=args.use_nonlinear,
            use_pca=args.use_pca,
            use_domain=args.use_domain,
            n_pca_components=args.n_pca_components,
            top_k_interactions=args.top_k_interactions
        )
        logger.info("Feature engineering completed successfully")
        return 0
    
    except Exception as e:
        logger.error(f"Error applying feature engineering: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == '__main__':
    main()
