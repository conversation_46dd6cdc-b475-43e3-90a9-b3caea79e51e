#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Improved growth rate prediction model using Stacking RF Bidirectional approach.
This model combines multiple base models with bidirectional learning and strong regularization.
"""

import os
import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Tuple, Union, Optional
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor, StackingRegressor
from sklearn.linear_model import Ridge, Lasso, ElasticNet
from sklearn.model_selection import KFold, cross_val_score, train_test_split
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from sklearn.preprocessing import StandardScaler
from sklearn.pipeline import Pipeline
import matplotlib.pyplot as plt
import seaborn as sns
import joblib
import xgboost as xgb
import lightgbm as lgb
import shap

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ImprovedGrowthRateModel:
    """
    Improved growth rate prediction model using Stacking RF Bidirectional approach.
    """
    
    def __init__(self, rf_params=None, xgb_params=None, lgb_params=None, gb_params=None, meta_params=None):
        """
        Initialize the model with parameters for base models and meta-model.
        
        Args:
            rf_params: Parameters for Random Forest
            xgb_params: Parameters for XGBoost
            lgb_params: Parameters for LightGBM
            gb_params: Parameters for Gradient Boosting
            meta_params: Parameters for meta-model (Ridge)
        """
        # Set default parameters if not provided
        self.rf_params = rf_params or {
            'n_estimators': 500,
            'max_depth': 15,
            'min_samples_split': 5,
            'min_samples_leaf': 2,
            'max_features': 'sqrt',
            'bootstrap': True,
            'oob_score': True,
            'n_jobs': -1,
            'random_state': 42
        }
        
        self.xgb_params = xgb_params or {
            'n_estimators': 500,
            'learning_rate': 0.03,
            'max_depth': 6,
            'subsample': 0.8,
            'colsample_bytree': 0.8,
            'reg_alpha': 0.5,
            'reg_lambda': 1.0,
            'random_state': 42
        }
        
        self.lgb_params = lgb_params or {
            'n_estimators': 500,
            'learning_rate': 0.03,
            'num_leaves': 31,
            'max_depth': 6,
            'subsample': 0.8,
            'colsample_bytree': 0.8,
            'reg_alpha': 0.5,
            'reg_lambda': 1.0,
            'random_state': 42
        }
        
        self.gb_params = gb_params or {
            'n_estimators': 500,
            'learning_rate': 0.03,
            'max_depth': 6,
            'subsample': 0.8,
            'random_state': 42
        }
        
        self.meta_params = meta_params or {
            'alpha': 1.0,
            'random_state': 42
        }
        
        # Initialize scaler
        self.scaler = StandardScaler()
        
        # Initialize base models
        self.rf_model = RandomForestRegressor(**self.rf_params)
        self.xgb_model = xgb.XGBRegressor(**self.xgb_params)
        self.lgb_model = lgb.LGBMRegressor(**self.lgb_params)
        self.gb_model = GradientBoostingRegressor(**self.gb_params)
        
        # Initialize meta-model
        self.meta_model = Ridge(**self.meta_params)
        
        # Initialize stacking model
        self.stacking_model = None
        
        # For storing feature importances
        self.feature_importances = {}
        
        # For storing evaluation metrics
        self.metrics = {}
        
        # For storing feature names
        self.feature_names = None
        
        # For storing SHAP values
        self.shap_values = None
    
    def _create_stacking_model(self) -> StackingRegressor:
        """
        Create a stacking model with base models and meta-model.
        
        Returns:
            StackingRegressor
        """
        # Define base models
        base_models = [
            ('rf', self.rf_model),
            ('xgb', self.xgb_model),
            ('lgb', self.lgb_model),
            ('gb', self.gb_model)
        ]
        
        # Create stacking model
        stacking_model = StackingRegressor(
            estimators=base_models,
            final_estimator=self.meta_model,
            cv=5,
            n_jobs=-1
        )
        
        return stacking_model
    
    def fit(self, X_train: pd.DataFrame, y_train: pd.Series, 
            X_val: pd.DataFrame = None, y_val: pd.Series = None) -> 'ImprovedGrowthRateModel':
        """
        Train the model.
        
        Args:
            X_train: Training features
            y_train: Training target
            X_val: Validation features (optional)
            y_val: Validation target (optional)
            
        Returns:
            Self
        """
        # Store feature names
        self.feature_names = X_train.columns.tolist()
        
        # Scale features
        X_train_scaled = pd.DataFrame(
            self.scaler.fit_transform(X_train),
            columns=X_train.columns,
            index=X_train.index
        )
        
        # Create stacking model
        self.stacking_model = self._create_stacking_model()
        
        # Train stacking model
        logger.info("Training stacking model...")
        self.stacking_model.fit(X_train_scaled, y_train)
        
        # Calculate training metrics
        train_preds = self.stacking_model.predict(X_train_scaled)
        train_r2 = r2_score(y_train, train_preds)
        train_rmse = np.sqrt(mean_squared_error(y_train, train_preds))
        train_mae = mean_absolute_error(y_train, train_preds)
        
        self.metrics['train_r2'] = train_r2
        self.metrics['train_rmse'] = train_rmse
        self.metrics['train_mae'] = train_mae
        
        logger.info(f"Training metrics - R²: {train_r2:.4f}, RMSE: {train_rmse:.4f}, MAE: {train_mae:.4f}")
        
        # Calculate validation metrics if validation set is provided
        if X_val is not None and y_val is not None:
            X_val_scaled = pd.DataFrame(
                self.scaler.transform(X_val),
                columns=X_val.columns,
                index=X_val.index
            )
            
            val_preds = self.stacking_model.predict(X_val_scaled)
            val_r2 = r2_score(y_val, val_preds)
            val_rmse = np.sqrt(mean_squared_error(y_val, val_preds))
            val_mae = mean_absolute_error(y_val, val_preds)
            
            self.metrics['val_r2'] = val_r2
            self.metrics['val_rmse'] = val_rmse
            self.metrics['val_mae'] = val_mae
            
            logger.info(f"Validation metrics - R²: {val_r2:.4f}, RMSE: {val_rmse:.4f}, MAE: {val_mae:.4f}")
        
        # Get feature importances from Random Forest
        rf_model = self.stacking_model.estimators_[0]
        self.feature_importances['rf'] = pd.Series(
            rf_model.feature_importances_,
            index=X_train.columns
        ).sort_values(ascending=False)
        
        # Calculate SHAP values for feature importance
        logger.info("Calculating SHAP values for feature importance")
        explainer = shap.TreeExplainer(rf_model)
        self.shap_values = explainer.shap_values(X_train_scaled)
        
        return self
    
    def predict(self, X: pd.DataFrame) -> np.ndarray:
        """
        Generate predictions for new data.
        
        Args:
            X: Features
            
        Returns:
            Predictions
        """
        # Scale features
        X_scaled = pd.DataFrame(
            self.scaler.transform(X),
            columns=X.columns,
            index=X.index
        )
        
        # Generate predictions
        return self.stacking_model.predict(X_scaled)
    
    def evaluate(self, X: pd.DataFrame, y: pd.Series) -> Dict[str, float]:
        """
        Evaluate the model on new data.
        
        Args:
            X: Features
            y: Target
            
        Returns:
            Dictionary of evaluation metrics
        """
        # Generate predictions
        y_pred = self.predict(X)
        
        # Calculate metrics
        metrics = {
            'R2': r2_score(y, y_pred),
            'RMSE': np.sqrt(mean_squared_error(y, y_pred)),
            'MAE': mean_absolute_error(y, y_pred)
        }
        
        # Log metrics
        logger.info(f"Evaluation metrics - R²: {metrics['R2']:.4f}, RMSE: {metrics['RMSE']:.4f}, MAE: {metrics['MAE']:.4f}")
        
        return metrics
    
    def get_feature_importance(self) -> pd.DataFrame:
        """
        Get feature importance from Random Forest.
        
        Returns:
            DataFrame with feature importance
        """
        if not self.feature_importances:
            logger.warning("Feature importances not available. Train the model first.")
            return pd.DataFrame()
        
        return pd.DataFrame(self.feature_importances)
    
    def plot_feature_importance(self, output_dir: str = None, n_features: int = 30) -> None:
        """
        Plot feature importance.
        
        Args:
            output_dir: Directory to save plots (optional)
            n_features: Number of top features to plot
        """
        if not self.feature_importances:
            logger.warning("Feature importances not available. Train the model first.")
            return
        
        # Create output directory if provided
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)
        
        # Plot Random Forest feature importance
        plt.figure(figsize=(12, 8))
        top_features = self.feature_importances['rf'].head(n_features)
        sns.barplot(x=top_features.values, y=top_features.index)
        plt.title(f'Top {n_features} Feature Importance (Random Forest)')
        plt.tight_layout()
        
        if output_dir:
            plt.savefig(os.path.join(output_dir, 'rf_feature_importance.png'))
        else:
            plt.show()
        
        # Plot SHAP values if available
        if self.shap_values is not None and self.feature_names is not None:
            plt.figure(figsize=(12, 8))
            shap.summary_plot(self.shap_values, features=self.feature_names, plot_type='bar', show=False)
            plt.title('SHAP Feature Importance')
            plt.tight_layout()
            
            if output_dir:
                plt.savefig(os.path.join(output_dir, 'shap_feature_importance.png'))
            else:
                plt.show()
    
    def plot_predictions(self, X: pd.DataFrame, y: pd.Series, output_dir: str = None) -> None:
        """
        Plot predictions vs actual values.
        
        Args:
            X: Features
            y: Target
            output_dir: Directory to save plots (optional)
        """
        # Generate predictions
        y_pred = self.predict(X)
        
        # Ensure predictions are 1-dimensional
        if len(y_pred.shape) > 1:
            y_pred = y_pred.flatten()
        
        # Convert predictions to Series with the same index as y for proper alignment
        y_pred_series = pd.Series(y_pred, index=y.index)
        
        # Calculate metrics
        r2 = r2_score(y, y_pred_series)
        rmse = np.sqrt(mean_squared_error(y, y_pred_series))
        
        # Create scatter plot
        plt.figure(figsize=(10, 6))
        plt.scatter(y, y_pred_series, alpha=0.5)
        plt.plot([y.min(), y.max()], [y.min(), y.max()], 'r--')
        plt.xlabel('Actual Growth Rate')
        plt.ylabel('Predicted Growth Rate')
        plt.title(f'Growth Rate Predictions (R² = {r2:.4f}, RMSE = {rmse:.4f})')
        plt.tight_layout()
        
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)
            plt.savefig(os.path.join(output_dir, 'growth_predictions_scatter.png'))
        else:
            plt.show()
        
        # Create residual plot
        residuals = y - y_pred_series
        plt.figure(figsize=(10, 6))
        plt.scatter(y_pred_series, residuals, alpha=0.5)
        plt.axhline(y=0, color='r', linestyle='--')
        plt.xlabel('Predicted Growth Rate')
        plt.ylabel('Residuals')
        plt.title('Residual Plot for Growth Rate Predictions')
        plt.tight_layout()
        
        if output_dir:
            plt.savefig(os.path.join(output_dir, 'growth_predictions_residuals.png'))
        else:
            plt.show()
    
    def save(self, output_dir: str) -> None:
        """
        Save the model to disk.
        
        Args:
            output_dir: Directory to save the model
        """
        os.makedirs(output_dir, exist_ok=True)
        
        # Save stacking model
        joblib.dump(self.stacking_model, os.path.join(output_dir, 'stacking_model.joblib'))
        
        # Save scaler
        joblib.dump(self.scaler, os.path.join(output_dir, 'scaler.joblib'))
        
        # Save feature importances
        if self.feature_importances:
            pd.DataFrame(self.feature_importances).to_csv(os.path.join(output_dir, 'feature_importances.csv'))
        
        # Save metrics
        if self.metrics:
            pd.DataFrame([self.metrics]).to_csv(os.path.join(output_dir, 'metrics.csv'), index=False)
        
        # Save feature names
        if self.feature_names:
            with open(os.path.join(output_dir, 'feature_names.txt'), 'w') as f:
                for feature in self.feature_names:
                    f.write(f"{feature}\n")
        
        logger.info(f"Model saved to {output_dir}")
    
    def load(self, input_dir: str) -> 'ImprovedGrowthRateModel':
        """
        Load the model from disk.
        
        Args:
            input_dir: Directory to load the model from
            
        Returns:
            Self
        """
        # Load stacking model
        self.stacking_model = joblib.load(os.path.join(input_dir, 'stacking_model.joblib'))
        
        # Load scaler
        self.scaler = joblib.load(os.path.join(input_dir, 'scaler.joblib'))
        
        # Load feature importances if available
        feature_importances_path = os.path.join(input_dir, 'feature_importances.csv')
        if os.path.exists(feature_importances_path):
            self.feature_importances = pd.read_csv(feature_importances_path, index_col=0).to_dict()
        
        # Load metrics if available
        metrics_path = os.path.join(input_dir, 'metrics.csv')
        if os.path.exists(metrics_path):
            self.metrics = pd.read_csv(metrics_path).iloc[0].to_dict()
        
        # Load feature names if available
        feature_names_path = os.path.join(input_dir, 'feature_names.txt')
        if os.path.exists(feature_names_path):
            with open(feature_names_path, 'r') as f:
                self.feature_names = [line.strip() for line in f.readlines()]
        
        logger.info(f"Model loaded from {input_dir}")
        
        return self

if __name__ == "__main__":
    # This module is not meant to be run directly
    logger.info("This module provides the ImprovedGrowthRateModel class for growth rate prediction.")
