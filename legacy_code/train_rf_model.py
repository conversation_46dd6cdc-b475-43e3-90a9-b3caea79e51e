#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Random Forest Model Training Script.

This script trains a Random Forest model for either temperature or growth rate prediction.
"""

import os
import argparse
import logging
import numpy as np
import pandas as pd
import joblib
from sklearn.ensemble import RandomForestRegressor
from sklearn.feature_selection import SelectFromModel, mutual_info_regression
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, KBinsDiscretizer
from typing import Dict, List, Tuple, Optional, Union, Any

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def transform_target(y: np.ndarray, transform: str) -> np.ndarray:
    """
    Apply transformation to target values.
    
    Args:
        y: Target values
        transform: Transformation method ('none', 'log2', 'log10', 'sqrt')
        
    Returns:
        Transformed target values
    """
    if transform == 'none':
        return y
    elif transform == 'log2':
        # Ensure all values are positive
        min_val = np.min(y)
        if min_val <= 0:
            y = y - min_val + 1e-6
        return np.log2(y)
    elif transform == 'log10':
        # Ensure all values are positive
        min_val = np.min(y)
        if min_val <= 0:
            y = y - min_val + 1e-6
        return np.log10(y)
    elif transform == 'sqrt':
        # Ensure all values are positive
        min_val = np.min(y)
        if min_val < 0:
            y = y - min_val
        return np.sqrt(y)
    else:
        raise ValueError(f"Unknown transformation: {transform}")

def inverse_transform_target(y: np.ndarray, transform: str) -> np.ndarray:
    """
    Apply inverse transformation to target values.
    
    Args:
        y: Transformed target values
        transform: Transformation method ('none', 'log2', 'log10', 'sqrt')
        
    Returns:
        Original target values
    """
    if transform == 'none':
        return y
    elif transform == 'log2':
        return 2 ** y
    elif transform == 'log10':
        return 10 ** y
    elif transform == 'sqrt':
        return y ** 2
    else:
        raise ValueError(f"Unknown transformation: {transform}")

def stratified_train_val_test_split(
    features: pd.DataFrame,
    target: pd.Series,
    train_ratio: float = 0.7,
    val_ratio: float = 0.2,
    test_ratio: float = 0.1,
    n_bins: int = 10,
    random_state: int = 42
) -> Dict[str, Dict[str, Dict[str, np.ndarray]]]:
    """
    Perform stratified train-validation-test split for regression.
    
    Args:
        features: Feature matrix
        target: Target values
        train_ratio: Ratio of training data
        val_ratio: Ratio of validation data
        test_ratio: Ratio of test data
        n_bins: Number of bins for stratification
        random_state: Random state for reproducibility
        
    Returns:
        Dictionary containing data splits
    """
    # Validate ratios
    if not np.isclose(train_ratio + val_ratio + test_ratio, 1.0):
        logger.warning(f"Ratios don't sum to 1.0: {train_ratio} + {val_ratio} + {test_ratio} = {train_ratio + val_ratio + test_ratio}")
        # Normalize ratios
        total = train_ratio + val_ratio + test_ratio
        train_ratio = train_ratio / total
        val_ratio = val_ratio / total
        test_ratio = test_ratio / total
        logger.info(f"Normalized ratios: train={train_ratio:.2f}, val={val_ratio:.2f}, test={test_ratio:.2f}")
    
    # Create bins for stratification
    discretizer = KBinsDiscretizer(n_bins=n_bins, encode='ordinal', strategy='quantile')
    bins = discretizer.fit_transform(target.values.reshape(-1, 1)).flatten().astype(int)
    
    # First split into train+val and test sets
    test_size = test_ratio / (train_ratio + val_ratio + test_ratio)
    X_trainval, X_test, y_trainval, y_test, bins_trainval, _ = train_test_split(
        features.values, target.values, bins, test_size=test_size, random_state=random_state, stratify=bins
    )
    
    # Now split trainval into train and validation
    val_size = val_ratio / (train_ratio + val_ratio)
    X_train, X_val, y_train, y_val = train_test_split(
        X_trainval, y_trainval, test_size=val_size, random_state=random_state, stratify=bins_trainval
    )
    
    logger.info(f"Data split - Train: {X_train.shape[0]}, Val: {X_val.shape[0]}, Test: {X_test.shape[0]}")
    logger.info(f"Target range - Train: [{np.min(y_train):.4f}, {np.max(y_train):.4f}], "
                f"Val: [{np.min(y_val):.4f}, {np.max(y_val):.4f}], "
                f"Test: [{np.min(y_test):.4f}, {np.max(y_test):.4f}]")
    
    return {
        'train': {'X': X_train, 'y': y_train},
        'val': {'X': X_val, 'y': y_val},
        'test': {'X': X_test, 'y': y_test}
    }

def select_features(
    X_train: np.ndarray,
    y_train: np.ndarray,
    X_val: np.ndarray,
    n_features: int,
    target: str,
    random_state: int = 42
) -> Tuple[np.ndarray, np.ndarray, Any]:
    """
    Select features using Random Forest feature importance or mutual information.
    
    Args:
        X_train: Training features
        y_train: Training targets
        X_val: Validation features
        n_features: Number of features to select
        target: Target variable ('temperature' or 'growth_rate')
        random_state: Random state for reproducibility
        
    Returns:
        Tuple of (selected training features, selected validation features, feature selector)
    """
    logger.info(f"Selecting {n_features} features for {target} prediction")
    
    if target == 'temperature':
        # Use Random Forest feature importance for temperature
        logger.info("Using Random Forest feature importance")
        rf = RandomForestRegressor(
            n_estimators=100,
            max_depth=10,
            random_state=random_state,
            n_jobs=-1
        )
        rf.fit(X_train, y_train)
        
        # Get feature importance
        importances = rf.feature_importances_
        indices = np.argsort(importances)[::-1]
        
        # Select top features
        selector = SelectFromModel(
            rf,
            max_features=n_features,
            threshold=-np.inf,
            prefit=True
        )
    else:
        # Use mutual information for growth rate
        logger.info("Using mutual information")
        mi = mutual_info_regression(X_train, y_train, random_state=random_state)
        indices = np.argsort(mi)[::-1]
        
        # Create a selector that selects the top features
        selector = SelectFromModel(
            RandomForestRegressor(random_state=random_state),
            max_features=n_features,
            threshold=-np.inf
        )
        selector.fit(X_train, y_train)
    
    # Transform the data
    X_train_selected = selector.transform(X_train)
    X_val_selected = selector.transform(X_val)
    
    logger.info(f"Feature selection: {X_train.shape[1]} -> {X_train_selected.shape[1]}")
    
    return X_train_selected, X_val_selected, selector

def train_random_forest(
    X_train: np.ndarray,
    y_train: np.ndarray,
    X_val: np.ndarray,
    y_val: np.ndarray,
    n_estimators: int = 300,
    max_depth: int = 20,
    min_samples_leaf: int = 2,
    random_state: int = 42
) -> Tuple[RandomForestRegressor, Dict[str, float]]:
    """
    Train a Random Forest model.
    
    Args:
        X_train: Training features
        y_train: Training targets
        X_val: Validation features
        y_val: Validation targets
        n_estimators: Number of trees
        max_depth: Maximum depth of trees
        min_samples_leaf: Minimum samples per leaf
        random_state: Random state for reproducibility
        
    Returns:
        Tuple of (trained model, metrics)
    """
    logger.info(f"Training Random Forest with {n_estimators} trees, max_depth={max_depth}, min_samples_leaf={min_samples_leaf}")
    
    # Initialize model
    model = RandomForestRegressor(
        n_estimators=n_estimators,
        max_depth=max_depth,
        min_samples_leaf=min_samples_leaf,
        n_jobs=-1,
        random_state=random_state,
        verbose=1
    )
    
    # Train model
    logger.info("Training Random Forest...")
    model.fit(X_train, y_train)
    
    # Evaluate on training set
    train_preds = model.predict(X_train)
    train_r2 = r2_score(y_train, train_preds)
    train_rmse = np.sqrt(mean_squared_error(y_train, train_preds))
    train_mae = mean_absolute_error(y_train, train_preds)
    
    # Evaluate on validation set
    val_preds = model.predict(X_val)
    val_r2 = r2_score(y_val, val_preds)
    val_rmse = np.sqrt(mean_squared_error(y_val, val_preds))
    val_mae = mean_absolute_error(y_val, val_preds)
    
    # Calculate adjusted R²
    n = len(y_val)
    p = X_val.shape[1]
    val_adj_r2 = 1 - (1 - val_r2) * (n - 1) / (n - p - 1)
    
    # Log metrics
    logger.info(f"Training metrics - R²: {train_r2:.6f}, RMSE: {train_rmse:.6f}, MAE: {train_mae:.6f}")
    logger.info(f"Validation metrics - R²: {val_r2:.6f}, Adj R²: {val_adj_r2:.6f}, RMSE: {val_rmse:.6f}, MAE: {val_mae:.6f}")
    
    # Return model and metrics
    metrics = {
        'train_r2': train_r2,
        'train_rmse': train_rmse,
        'train_mae': train_mae,
        'val_r2': val_r2,
        'val_adj_r2': val_adj_r2,
        'val_rmse': val_rmse,
        'val_mae': val_mae
    }
    
    return model, metrics

def evaluate_model(
    model: RandomForestRegressor,
    X_test: np.ndarray,
    y_test: np.ndarray,
    transform: str
) -> Dict[str, float]:
    """
    Evaluate the model on test data.
    
    Args:
        model: Trained model
        X_test: Test features
        y_test: Test targets
        transform: Transformation method
        
    Returns:
        Dictionary of metrics
    """
    logger.info("Evaluating model on test data")
    
    # Make predictions
    y_pred = model.predict(X_test)
    
    # Calculate metrics
    r2 = r2_score(y_test, y_pred)
    rmse = np.sqrt(mean_squared_error(y_test, y_pred))
    mae = mean_absolute_error(y_test, y_pred)
    
    # Calculate adjusted R²
    n = len(y_test)
    p = X_test.shape[1]
    adj_r2 = 1 - (1 - r2) * (n - 1) / (n - p - 1)
    
    # Log metrics
    logger.info(f"Test metrics - R²: {r2:.6f}, Adj R²: {adj_r2:.6f}, RMSE: {rmse:.6f}, MAE: {mae:.6f}")
    
    # If transformed, also calculate metrics in original scale
    if transform != 'none':
        y_test_orig = inverse_transform_target(y_test, transform)
        y_pred_orig = inverse_transform_target(y_pred, transform)
        
        r2_orig = r2_score(y_test_orig, y_pred_orig)
        rmse_orig = np.sqrt(mean_squared_error(y_test_orig, y_pred_orig))
        mae_orig = mean_absolute_error(y_test_orig, y_pred_orig)
        
        logger.info(f"Test metrics (original scale) - R²: {r2_orig:.6f}, RMSE: {rmse_orig:.6f}, MAE: {mae_orig:.6f}")
        
        # Log prediction stats
        logger.info(f"True values (orig): min={np.min(y_test_orig):.4f}, max={np.max(y_test_orig):.4f}, mean={np.mean(y_test_orig):.4f}")
        logger.info(f"Predicted values (orig): min={np.min(y_pred_orig):.4f}, max={np.max(y_pred_orig):.4f}, mean={np.mean(y_pred_orig):.4f}")
    
    # Log prediction stats
    logger.info(f"True values: min={np.min(y_test):.4f}, max={np.max(y_test):.4f}, mean={np.mean(y_test):.4f}")
    logger.info(f"Predicted values: min={np.min(y_pred):.4f}, max={np.max(y_pred):.4f}, mean={np.mean(y_pred):.4f}")
    
    # Return metrics
    metrics = {
        'test_r2': r2,
        'test_adj_r2': adj_r2,
        'test_rmse': rmse,
        'test_mae': mae
    }
    
    if transform != 'none':
        metrics.update({
            'test_r2_orig': r2_orig,
            'test_rmse_orig': rmse_orig,
            'test_mae_orig': mae_orig
        })
    
    return metrics

def save_model(
    model: RandomForestRegressor,
    feature_selector: Any,
    metrics: Dict[str, float],
    output_dir: str,
    target: str,
    transform: str
) -> None:
    """
    Save the model and related artifacts.
    
    Args:
        model: Trained model
        feature_selector: Feature selector
        metrics: Model metrics
        output_dir: Output directory
        target: Target variable
        transform: Transformation method
    """
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Save model
    model_path = os.path.join(output_dir, f"rf_{target}_model.pkl")
    joblib.dump(model, model_path)
    logger.info(f"Model saved to {model_path}")
    
    # Save feature selector
    selector_path = os.path.join(output_dir, f"{target}_feature_selector.pkl")
    joblib.dump(feature_selector, selector_path)
    logger.info(f"Feature selector saved to {selector_path}")
    
    # Save metrics
    metrics_path = os.path.join(output_dir, f"{target}_metrics.csv")
    pd.DataFrame([metrics]).to_csv(metrics_path, index=False)
    logger.info(f"Metrics saved to {metrics_path}")
    
    # Save model info
    info = {
        'target': target,
        'transform': transform,
        'n_features': feature_selector.get_support().sum(),
        'n_estimators': model.n_estimators,
        'max_depth': model.max_depth,
        'min_samples_leaf': model.min_samples_leaf
    }
    info_path = os.path.join(output_dir, f"{target}_model_info.csv")
    pd.DataFrame([info]).to_csv(info_path, index=False)
    logger.info(f"Model info saved to {info_path}")

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Random Forest Model Training')
    parser.add_argument('--feature_file', type=str, required=True, help='Path to feature file')
    parser.add_argument('--metadata_file', type=str, required=True, help='Path to metadata file')
    parser.add_argument('--output_dir', type=str, required=True, help='Directory to save the model')
    parser.add_argument('--target', type=str, required=True, choices=['temperature', 'growth_rate'], help='Target variable')
    parser.add_argument('--n_features', type=int, default=None, help='Number of features to use')
    parser.add_argument('--n_estimators', type=int, default=300, help='Number of trees in Random Forest')
    parser.add_argument('--max_depth', type=int, default=20, help='Maximum depth of trees in Random Forest')
    parser.add_argument('--min_samples_leaf', type=int, default=2, help='Minimum samples per leaf in Random Forest')
    parser.add_argument('--transform', type=str, default='none', choices=['none', 'log2', 'log10', 'sqrt'], help='Transformation to apply to target')
    parser.add_argument('--train_ratio', type=float, default=0.7, help='Ratio of training data')
    parser.add_argument('--val_ratio', type=float, default=0.2, help='Ratio of validation data')
    parser.add_argument('--test_ratio', type=float, default=0.1, help='Ratio of test data')
    parser.add_argument('--random_state', type=int, default=42, help='Random state for reproducibility')
    parser.add_argument('--verbose', action='store_true', help='Enable verbose output')
    
    args = parser.parse_args()
    
    # Set logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.INFO)
    else:
        logging.getLogger().setLevel(logging.WARNING)
    
    # Set default number of features based on target
    if args.n_features is None:
        args.n_features = 800 if args.target == 'temperature' else 250
    
    # Load data
    logger.info(f"Loading features from {args.feature_file}")
    features = pd.read_csv(args.feature_file, sep='\t', index_col='genome_id')
    
    logger.info(f"Loading metadata from {args.metadata_file}")
    metadata = pd.read_csv(args.metadata_file, sep='\t', index_col='genome_id')
    
    # Get common samples
    common_indices = features.index.intersection(metadata.index)
    logger.info(f"Found {len(common_indices)} common samples")
    
    # Filter to common samples
    features = features.loc[common_indices]
    metadata = metadata.loc[common_indices]
    
    # Fill missing values
    features = features.fillna(0)
    
    # Get target variable
    if args.target == 'temperature':
        target = metadata['optimal_temperature']
        target_name = 'Temperature'
    else:
        target = metadata['growth_rate']
        target_name = 'Growth Rate'
    
    # Log target statistics
    logger.info(f"{target_name} statistics: min={target.min():.4f}, max={target.max():.4f}, mean={target.mean():.4f}, std={target.std():.4f}")
    
    # Apply transformation
    if args.transform != 'none':
        logger.info(f"Applying {args.transform} transformation to {args.target}")
        target_transformed = transform_target(target.values, args.transform)
        logger.info(f"Transformed {target_name} statistics: min={np.min(target_transformed):.4f}, max={np.max(target_transformed):.4f}, mean={np.mean(target_transformed):.4f}, std={np.std(target_transformed):.4f}")
    else:
        target_transformed = target.values
    
    # Split data
    data_splits = stratified_train_val_test_split(
        features=features,
        target=pd.Series(target_transformed),
        train_ratio=args.train_ratio,
        val_ratio=args.val_ratio,
        test_ratio=args.test_ratio,
        random_state=args.random_state
    )
    
    # Extract data
    X_train = data_splits['train']['X']
    y_train = data_splits['train']['y']
    X_val = data_splits['val']['X']
    y_val = data_splits['val']['y']
    X_test = data_splits['test']['X']
    y_test = data_splits['test']['y']
    
    # Select features
    X_train_selected, X_val_selected, feature_selector = select_features(
        X_train=X_train,
        y_train=y_train,
        X_val=X_val,
        n_features=args.n_features,
        target=args.target,
        random_state=args.random_state
    )
    
    # Apply feature selection to test data
    X_test_selected = feature_selector.transform(X_test)
    
    # Train model
    model, train_metrics = train_random_forest(
        X_train=X_train_selected,
        y_train=y_train,
        X_val=X_val_selected,
        y_val=y_val,
        n_estimators=args.n_estimators,
        max_depth=args.max_depth,
        min_samples_leaf=args.min_samples_leaf,
        random_state=args.random_state
    )
    
    # Evaluate model
    test_metrics = evaluate_model(
        model=model,
        X_test=X_test_selected,
        y_test=y_test,
        transform=args.transform
    )
    
    # Combine metrics
    metrics = {**train_metrics, **test_metrics}
    
    # Save model
    save_model(
        model=model,
        feature_selector=feature_selector,
        metrics=metrics,
        output_dir=args.output_dir,
        target=args.target,
        transform=args.transform
    )
    
    # Print final results
    print("\n============================================================")
    print(f"Random Forest {target_name} Model Results:")
    print(f"  Training - R²: {train_metrics['train_r2']:.4f}, RMSE: {train_metrics['train_rmse']:.4f}")
    print(f"  Validation - R²: {train_metrics['val_r2']:.4f}, RMSE: {train_metrics['val_rmse']:.4f}")
    print(f"  Test - R²: {test_metrics['test_r2']:.4f}, RMSE: {test_metrics['test_rmse']:.4f}")
    print("============================================================")

if __name__ == "__main__":
    main()
