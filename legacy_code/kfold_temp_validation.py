#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
K-Fold Cross-Validation for Temperature Prediction Model.

This script implements k-fold cross-validation to evaluate the temperature
prediction model's performance across different data splits.
"""

import os
import argparse
import logging
import numpy as np
import pandas as pd
import pickle
import torch
import torch.nn as nn
from sklearn.model_selection import KFold
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from sklearn.preprocessing import StandardScaler
from sklearn.feature_selection import mutual_info_regression
from typing import Tuple, List, Dict, Optional, Union

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import the EnhancedTemperatureDNN class
try:
    from enhanced_temperature_dnn import EnhancedTemperatureDNN
except ImportError:
    logger.error("Could not import EnhancedTemperatureDNN. Make sure enhanced_temperature_dnn.py is in the current directory.")
    raise

def load_data(feature_file: str, metadata_file: str) -> Tuple[pd.DataFrame, pd.Series]:
    """
    Load feature and metadata files.

    Args:
        feature_file: Path to feature file
        metadata_file: Path to metadata file

    Returns:
        Tuple of (features_df, temperature_series)
    """
    logger.info(f"Loading features from {feature_file}")
    features_df = pd.read_csv(feature_file, sep='\t', index_col='genome_id')

    logger.info(f"Loading metadata from {metadata_file}")
    metadata_df = pd.read_csv(metadata_file, sep='\t', index_col='genome_id')

    # Get common indices
    common_indices = features_df.index.intersection(metadata_df.index)
    features_df = features_df.loc[common_indices]
    temperature_series = metadata_df.loc[common_indices, 'optimal_temperature']

    logger.info(f"Found {len(common_indices)} genomes with both features and metadata")

    # Handle missing values
    missing_count = features_df.isna().sum().sum()
    if missing_count > 0:
        logger.warning(f"Found {missing_count} missing values in features. Filling with 0.")
        features_df = features_df.fillna(0)

    # Remove features with too many missing values
    missing_threshold = 0.5
    missing_ratio = features_df.isna().mean()
    features_to_drop = missing_ratio[missing_ratio > missing_threshold].index.tolist()
    if features_to_drop:
        logger.info(f"Removing {len(features_to_drop)} features with >{missing_threshold*100}% missing values.")
        features_df = features_df.drop(columns=features_to_drop)

    logger.info(f"Final feature shape: {features_df.shape}")

    return features_df, temperature_series

def select_features(
    X: pd.DataFrame,
    y: pd.Series,
    n_features: int = 1000,
    method: str = 'correlation'
) -> List[str]:
    """
    Select the most informative features for temperature prediction.

    Args:
        X: Feature DataFrame
        y: Temperature Series
        n_features: Number of features to select
        method: Feature selection method ('mutual_info', 'correlation', or 'variance')

    Returns:
        List of selected feature names
    """
    logger.info(f"Selecting {n_features} features using {method} method")

    # Ensure no NaN values
    X_clean = X.fillna(0)

    # Limit number of features to available columns
    n_features = min(n_features, X_clean.shape[1])

    try:
        if method == 'mutual_info':
            # Calculate mutual information between features and temperature
            logger.info("Computing mutual information...")
            mi_scores = mutual_info_regression(X_clean, y, random_state=42)
            mi_scores = pd.Series(mi_scores, index=X_clean.columns)
            selected_features = mi_scores.nlargest(n_features).index.tolist()

        elif method == 'correlation':
            # Calculate correlation between features and temperature
            logger.info("Computing correlations...")
            correlations = X_clean.corrwith(y).abs()
            # Handle NaN values in correlations
            correlations = correlations.fillna(0)
            selected_features = correlations.nlargest(n_features).index.tolist()

        elif method == 'variance':
            # Select features with highest variance
            logger.info("Computing variances...")
            variances = X_clean.var()
            selected_features = variances.nlargest(n_features).index.tolist()

        else:
            raise ValueError(f"Unknown feature selection method: {method}")

    except Exception as e:
        logger.error(f"Error in feature selection: {e}")
        logger.warning("Falling back to variance-based feature selection")

        # Fallback to variance-based selection
        variances = X_clean.var()
        selected_features = variances.nlargest(n_features).index.tolist()

    logger.info(f"Selected {len(selected_features)} features")

    return selected_features

def train_and_evaluate_fold(
    X_train: np.ndarray,
    y_train: np.ndarray,
    X_val: np.ndarray,
    y_val: np.ndarray,
    hidden_dims: List[int] = [512, 512, 384, 256, 128],
    dropout_rates: List[float] = [0.2, 0.3, 0.4, 0.5, 0.5],
    learning_rate: float = 0.001,
    weight_decay: float = 1e-5,
    batch_size: int = 32,
    epochs: int = 300,
    patience: int = 50,
    device: str = 'cpu',
    seed: int = 42
) -> Tuple[nn.Module, Dict[str, float]]:
    """
    Train and evaluate a temperature prediction model on a single fold.

    Args:
        X_train: Training features
        y_train: Training temperatures
        X_val: Validation features
        y_val: Validation temperatures
        hidden_dims: List of hidden layer dimensions
        dropout_rates: List of dropout rates
        learning_rate: Learning rate
        weight_decay: Weight decay (L2 regularization)
        batch_size: Batch size
        epochs: Maximum number of epochs
        patience: Patience for early stopping
        device: Device to use ('cpu' or 'cuda')
        seed: Random seed

    Returns:
        Tuple of (trained_model, metrics_dict)
    """
    # Set random seeds for reproducibility
    torch.manual_seed(seed)
    np.random.seed(seed)

    # Convert data to PyTorch tensors
    X_train_tensor = torch.FloatTensor(X_train).to(device)
    y_train_tensor = torch.FloatTensor(y_train).to(device)
    X_val_tensor = torch.FloatTensor(X_val).to(device)
    y_val_tensor = torch.FloatTensor(y_val).to(device)

    # Create dataset and dataloader
    train_dataset = torch.utils.data.TensorDataset(X_train_tensor, y_train_tensor)
    train_loader = torch.utils.data.DataLoader(
        train_dataset, batch_size=batch_size, shuffle=True
    )

    # Initialize model
    input_dim = X_train.shape[1]
    model = EnhancedTemperatureDNN(
        input_dim=input_dim,
        hidden_dims=hidden_dims,
        dropout_rates=dropout_rates,
        use_batch_norm=True,
        activation='leaky_relu',
        use_residual=True,
        use_attention=True,
        attention_heads=4
    ).to(device)

    # Initialize optimizer and loss function
    optimizer = torch.optim.Adam(model.parameters(), lr=learning_rate, weight_decay=weight_decay)
    criterion = nn.MSELoss()

    # Training loop with early stopping
    best_val_loss = float('inf')
    best_model_state = None
    patience_counter = 0

    for epoch in range(epochs):
        # Training
        model.train()
        train_loss = 0.0

        for batch_X, batch_y in train_loader:
            optimizer.zero_grad()
            outputs = model(batch_X)
            loss = criterion(outputs, batch_y)
            loss.backward()
            optimizer.step()
            train_loss += loss.item()

        train_loss /= len(train_loader)

        # Validation
        model.eval()
        with torch.no_grad():
            val_outputs = model(X_val_tensor)
            val_loss = criterion(val_outputs, y_val_tensor).item()

            # Calculate metrics
            val_preds = val_outputs.cpu().numpy()
            val_r2 = r2_score(y_val, val_preds)
            val_rmse = np.sqrt(mean_squared_error(y_val, val_preds))
            val_mae = mean_absolute_error(y_val, val_preds)

        # Early stopping
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            best_model_state = model.state_dict().copy()
            patience_counter = 0

            logger.info(f"Epoch {epoch+1}/{epochs} - Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}, Val R²: {val_r2:.4f}, Val RMSE: {val_rmse:.4f}")
        else:
            patience_counter += 1

            if patience_counter >= patience:
                logger.info(f"Early stopping at epoch {epoch+1}")
                break

    # Load best model
    model.load_state_dict(best_model_state)

    # Final evaluation
    model.eval()
    with torch.no_grad():
        train_preds = model(X_train_tensor).cpu().numpy()
        val_preds = model(X_val_tensor).cpu().numpy()

        # Calculate metrics
        train_r2 = r2_score(y_train, train_preds)
        train_rmse = np.sqrt(mean_squared_error(y_train, train_preds))
        train_mae = mean_absolute_error(y_train, train_preds)

        val_r2 = r2_score(y_val, val_preds)
        val_rmse = np.sqrt(mean_squared_error(y_val, val_preds))
        val_mae = mean_absolute_error(y_val, val_preds)

    # Compile metrics
    metrics = {
        'train_r2': train_r2,
        'train_rmse': train_rmse,
        'train_mae': train_mae,
        'val_r2': val_r2,
        'val_rmse': val_rmse,
        'val_mae': val_mae
    }

    return model, metrics

def run_kfold_cross_validation(
    features_df: pd.DataFrame,
    temperature_series: pd.Series,
    output_dir: str,
    n_features: int = 1000,
    n_folds: int = 5,
    hidden_dims: List[int] = [512, 512, 384, 256, 128],
    dropout_rates: List[float] = [0.2, 0.3, 0.4, 0.5, 0.5],
    learning_rate: float = 0.001,
    weight_decay: float = 1e-5,
    batch_size: int = 32,
    epochs: int = 300,
    patience: int = 50,
    seed: int = 42,
    feature_selection_method: str = 'correlation'
) -> Dict[str, List[float]]:
    """
    Run k-fold cross-validation for temperature prediction.

    Args:
        features_df: Feature DataFrame
        temperature_series: Temperature Series
        output_dir: Directory to save results
        n_features: Number of features to select
        n_folds: Number of folds for cross-validation
        hidden_dims: List of hidden layer dimensions
        dropout_rates: List of dropout rates
        learning_rate: Learning rate
        weight_decay: Weight decay (L2 regularization)
        batch_size: Batch size
        epochs: Maximum number of epochs
        patience: Patience for early stopping
        seed: Random seed

    Returns:
        Dictionary of metrics for each fold
    """
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)

    # Initialize k-fold cross-validation
    kf = KFold(n_splits=n_folds, shuffle=True, random_state=seed)

    # Initialize metrics dictionary
    all_metrics = {
        'train_r2': [],
        'train_rmse': [],
        'train_mae': [],
        'val_r2': [],
        'val_rmse': [],
        'val_mae': []
    }

    # Select device
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    logger.info(f"Using device: {device}")

    # Run k-fold cross-validation
    for fold, (train_idx, val_idx) in enumerate(kf.split(features_df)):
        logger.info(f"Fold {fold+1}/{n_folds}")

        # Split data
        X_train = features_df.iloc[train_idx]
        y_train = temperature_series.iloc[train_idx]
        X_val = features_df.iloc[val_idx]
        y_val = temperature_series.iloc[val_idx]

        # Select features
        selected_features = select_features(X_train, y_train, n_features=n_features, method=feature_selection_method)
        X_train_selected = X_train[selected_features]
        X_val_selected = X_val[selected_features]

        # Scale features
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train_selected)
        X_val_scaled = scaler.transform(X_val_selected)

        # Train and evaluate model
        model, metrics = train_and_evaluate_fold(
            X_train_scaled, y_train.values,
            X_val_scaled, y_val.values,
            hidden_dims=hidden_dims,
            dropout_rates=dropout_rates,
            learning_rate=learning_rate,
            weight_decay=weight_decay,
            batch_size=batch_size,
            epochs=epochs,
            patience=patience,
            device=device,
            seed=seed
        )

        # Save model and metrics
        fold_dir = os.path.join(output_dir, f"fold_{fold+1}")
        os.makedirs(fold_dir, exist_ok=True)

        # Save model
        torch.save(model.state_dict(), os.path.join(fold_dir, "model.pt"))

        # Save feature names
        with open(os.path.join(fold_dir, "selected_features.txt"), "w") as f:
            for feature in selected_features:
                f.write(f"{feature}\n")

        # Save scaler
        with open(os.path.join(fold_dir, "scaler.pkl"), "wb") as f:
            pickle.dump(scaler, f)

        # Save metrics
        metrics_df = pd.DataFrame({k: [v] for k, v in metrics.items()})
        metrics_df.to_csv(os.path.join(fold_dir, "metrics.csv"), index=False)

        # Update all metrics
        for k, v in metrics.items():
            all_metrics[k].append(v)

        logger.info(f"Fold {fold+1} - Train R²: {metrics['train_r2']:.4f}, Val R²: {metrics['val_r2']:.4f}")

    # Calculate average metrics
    avg_metrics = {k: np.mean(v) for k, v in all_metrics.items()}
    std_metrics = {k: np.std(v) for k, v in all_metrics.items()}

    # Save average metrics
    avg_metrics_df = pd.DataFrame({
        'metric': list(avg_metrics.keys()),
        'mean': list(avg_metrics.values()),
        'std': list(std_metrics.values())
    })
    avg_metrics_df.to_csv(os.path.join(output_dir, "average_metrics.csv"), index=False)

    logger.info("K-fold cross-validation completed")
    logger.info(f"Average Train R²: {avg_metrics['train_r2']:.4f} ± {std_metrics['train_r2']:.4f}")
    logger.info(f"Average Val R²: {avg_metrics['val_r2']:.4f} ± {std_metrics['val_r2']:.4f}")
    logger.info(f"Average Val RMSE: {avg_metrics['val_rmse']:.4f} ± {std_metrics['val_rmse']:.4f}")

    return all_metrics

def main():
    parser = argparse.ArgumentParser(description='K-Fold Cross-Validation for Temperature Prediction Model')
    parser.add_argument('--feature-file', type=str, required=True, help='Path to feature file')
    parser.add_argument('--metadata-file', type=str, required=True, help='Path to metadata file')
    parser.add_argument('--output-dir', type=str, required=True, help='Directory to save results')
    parser.add_argument('--n-features', type=int, default=1000, help='Number of features to select')
    parser.add_argument('--n-folds', type=int, default=5, help='Number of folds for cross-validation')
    parser.add_argument('--hidden-dims', type=str, default='512,512,384,256,128', help='Comma-separated list of hidden layer dimensions')
    parser.add_argument('--dropout-rates', type=str, default='0.2,0.3,0.4,0.5,0.5', help='Comma-separated list of dropout rates')
    parser.add_argument('--learning-rate', type=float, default=0.001, help='Learning rate')
    parser.add_argument('--weight-decay', type=float, default=1e-5, help='Weight decay (L2 regularization)')
    parser.add_argument('--batch-size', type=int, default=32, help='Batch size')
    parser.add_argument('--epochs', type=int, default=300, help='Maximum number of epochs')
    parser.add_argument('--patience', type=int, default=50, help='Patience for early stopping')
    parser.add_argument('--seed', type=int, default=42, help='Random seed')
    parser.add_argument('--feature-selection', type=str, default='correlation', help='Feature selection method (correlation, mutual_info, variance)')

    args = parser.parse_args()

    # Parse hidden dimensions and dropout rates
    hidden_dims = [int(x) for x in args.hidden_dims.split(',')]
    dropout_rates = [float(x) for x in args.dropout_rates.split(',')]

    # Load data
    features_df, temperature_series = load_data(args.feature_file, args.metadata_file)

    # Run k-fold cross-validation
    run_kfold_cross_validation(
        features_df,
        temperature_series,
        args.output_dir,
        n_features=args.n_features,
        n_folds=args.n_folds,
        hidden_dims=hidden_dims,
        dropout_rates=dropout_rates,
        learning_rate=args.learning_rate,
        weight_decay=args.weight_decay,
        batch_size=args.batch_size,
        epochs=args.epochs,
        patience=args.patience,
        seed=args.seed,
        feature_selection_method=args.feature_selection
    )

if __name__ == '__main__':
    main()
