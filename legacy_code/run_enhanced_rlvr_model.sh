#!/bin/bash
# Run script for training the enhanced RLVR model with optimized hyperparameters

# First, enhance the RLVR agent with improved architecture
python enhance_rlvr_agent_simple.py

# Make script executable
chmod +x train_rlvr_model.py

# Create output directories
mkdir -p models/enhanced_rlvr_model
mkdir -p metrics/enhanced_rlvr_model

# Run training script with improved hyperparameters and enhanced agent
python train_rlvr_model.py \
    --feature-file training_data/combined_features.tsv \
    --metadata training_data/metadata.tsv \
    --output-dir models/enhanced_rlvr_model \
    --metrics-dir metrics/enhanced_rlvr_model \
    --hidden-dim 512 \
    --num-heads 8 \
    --num-layers 3 \
    --dropout 0.3 \
    --use-layer-norm \
    --use-residual \
    --use-value-head \
    --activation silu \
    --use-enhanced-agent \
    --use-feature-interaction \
    --interaction-layers 2 \
    --alpha 0.7 \
    --beta 0.2 \
    --gamma 0.1 \
    --accuracy-scale 0.5 \
    --lr 5e-5 \
    --weight-decay 1e-4 \
    --batch-size 64 \
    --epochs 200 \
    --early-stopping-patience 20 \
    --entropy-coef 0.005 \
    --value-loss-coef 0.3 \
    --max-grad-norm 0.5 \
    --seed 42 \
    --feature-selection \
    --n-features 100


:<<'COMMENT'

Let's summarize the improvements we made to fix the loss calculation issue:

1. Improved Reward Function:
Changed from exponential decay to inverse scaling for better numerical stability
Added error clipping to prevent extreme values
Added robust error handling for R² calculation
Improved the consistency component calculation
Added non-linear scaling to the improvement component
2. Enhanced Policy Gradient Loss:
Added direct MSE loss between predictions and targets
Incorporated target values in the policy gradient calculation
Added scaled rewards based on prediction error
Combined MSE loss with policy gradient loss for better stability
3. Updated Training Process:
Properly passed target tensors to the loss calculation functions
Ensured both train_step and validate methods use the same loss calculation

COMMENT


