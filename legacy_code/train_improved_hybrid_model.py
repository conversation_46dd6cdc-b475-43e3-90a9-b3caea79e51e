#!/usr/bin/env python3
"""
Training script for the improved hybrid model with Random Forest for growth rate and neural network for temperature.

This script trains an improved hybrid model that uses a Random Forest for growth rate prediction
and a neural network for optimal temperature prediction, with better feature normalization
and hyperparameter tuning.
"""

import os
import argparse
import logging
import json
import numpy as np
import pandas as pd
import torch
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union, Any
from sklearn.model_selection import train_test_split

from deepmu.data.tsv_dataset import DeepMuTsvDataset
from deepmu.models.improved_hybrid_model import create_improved_hybrid_model
from deepmu.utils.correlation_feature_selection import select_features_for_targets

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('train_improved_hybrid')

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Train an improved hybrid model with Random Forest for growth rate and neural network for temperature')

    # Data arguments
    parser.add_argument('--metadata', required=True, help='Path to metadata TSV file')
    parser.add_argument('--feature-file', required=True, help='Path to combined features TSV file')
    parser.add_argument('--output-dir', required=True, help='Directory to save model and results')
    parser.add_argument('--metrics-dir', default=None, help='Directory to save metrics')

    # Feature selection arguments
    parser.add_argument('--growth-rate-features', type=int, default=100, help='Number of features to select for growth rate prediction')
    parser.add_argument('--optimal-temperature-features', type=int, default=50, help='Number of features to select for optimal temperature prediction')
    parser.add_argument('--correlation-method', default='pearson', choices=['pearson', 'spearman', 'f_regression'], help='Method for feature selection')
    parser.add_argument('--max-samples', type=int, default=500, help='Maximum number of samples to use for feature selection')

    # Random Forest arguments
    parser.add_argument('--n-estimators', type=int, default=200, help='Number of trees in the Random Forest')
    parser.add_argument('--max-depth', type=int, default=15, help='Maximum depth of trees in the Random Forest')
    parser.add_argument('--min-samples-split', type=int, default=5, help='Minimum samples required to split a node in Random Forest')
    parser.add_argument('--min-samples-leaf', type=int, default=2, help='Minimum samples required in a leaf node in Random Forest')

    # Neural network arguments
    parser.add_argument('--hidden-dim', type=int, default=128, help='Hidden dimension for neural network')
    parser.add_argument('--num-layers', type=int, default=2, help='Number of hidden layers for neural network')
    parser.add_argument('--dropout', type=float, default=0.3, help='Dropout probability for neural network')
    parser.add_argument('--use-batch-norm', action='store_true', help='Use batch normalization for neural network')
    parser.add_argument('--learning-rate', type=float, default=0.001, help='Learning rate for neural network')
    parser.add_argument('--weight-decay', type=float, default=1e-5, help='Weight decay for neural network')

    # Training arguments
    parser.add_argument('--batch-size', type=int, default=32, help='Batch size for training')
    parser.add_argument('--num-epochs', type=int, default=50, help='Number of epochs to train')
    parser.add_argument('--seed', type=int, default=42, help='Random seed')
    parser.add_argument('--num-workers', type=int, default=4, help='Number of workers for data loading')

    return parser.parse_args()

def save_feature_importance_report(
    growth_rate_features: List[str],
    optimal_temperature_features: List[str],
    output_path: str
):
    """
    Save a report of the selected features and their importance.

    Args:
        growth_rate_features: List of selected features for growth rate prediction
        optimal_temperature_features: List of selected features for optimal temperature prediction
        output_path: Path to save the report
    """
    # Create directory if it doesn't exist
    os.makedirs(os.path.dirname(output_path), exist_ok=True)

    # Find common features
    common_features = set(growth_rate_features) & set(optimal_temperature_features)

    with open(output_path, 'w') as f:
        f.write('# Feature Importance Report\n\n')

        f.write('## Growth Rate Features\n\n')
        for i, feature in enumerate(growth_rate_features):
            f.write(f'{i+1}. {feature}\n')

        f.write('\n## Optimal Temperature Features\n\n')
        for i, feature in enumerate(optimal_temperature_features):
            f.write(f'{i+1}. {feature}\n')

        f.write('\n## Common Features\n\n')
        for feature in common_features:
            f.write(f'- {feature}\n')

        f.write(f'\nTotal common features: {len(common_features)}\n')
        f.write(f'Percentage of growth rate features: {len(common_features) / len(growth_rate_features) * 100:.2f}%\n')
        f.write(f'Percentage of optimal temperature features: {len(common_features) / len(optimal_temperature_features) * 100:.2f}%\n')

def save_prediction_plots(
    val_metrics: Dict,
    output_dir: str
):
    """
    Save plots of predictions vs actual values.

    Args:
        val_metrics: Dictionary of validation metrics
        output_dir: Directory to save the plots
    """
    # Create directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Growth rate predictions
    if 'growth_rate_preds' in val_metrics and 'growth_rate_targets' in val_metrics:
        plt.figure(figsize=(10, 8))
        
        # Create DataFrame for easier plotting
        df = pd.DataFrame({
            'Actual': val_metrics['growth_rate_targets'],
            'Predicted': val_metrics['growth_rate_preds']
        })
        
        # Create scatter plot with regression line
        sns.regplot(x='Actual', y='Predicted', data=df, scatter_kws={'alpha': 0.5})
        
        # Add perfect prediction line
        min_val = min(df['Actual'].min(), df['Predicted'].min())
        max_val = max(df['Actual'].max(), df['Predicted'].max())
        plt.plot([min_val, max_val], [min_val, max_val], 'r--')
        
        # Add R² value
        if 'growth_rate_r2' in val_metrics:
            plt.title(f'Growth Rate Prediction (R² = {val_metrics["growth_rate_r2"]:.4f})')
        else:
            plt.title('Growth Rate Prediction')
        
        plt.xlabel('Actual Growth Rate')
        plt.ylabel('Predicted Growth Rate')
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'growth_rate_prediction.png'))
    
    # Optimal temperature predictions
    if 'optimal_temperature_preds' in val_metrics and 'optimal_temperature_targets' in val_metrics:
        plt.figure(figsize=(10, 8))
        
        # Create DataFrame for easier plotting
        df = pd.DataFrame({
            'Actual': val_metrics['optimal_temperature_targets'],
            'Predicted': val_metrics['optimal_temperature_preds']
        })
        
        # Create scatter plot with regression line
        sns.regplot(x='Actual', y='Predicted', data=df, scatter_kws={'alpha': 0.5})
        
        # Add perfect prediction line
        min_val = min(df['Actual'].min(), df['Predicted'].min())
        max_val = max(df['Actual'].max(), df['Predicted'].max())
        plt.plot([min_val, max_val], [min_val, max_val], 'r--')
        
        # Add R² value
        if 'optimal_temperature_r2' in val_metrics:
            plt.title(f'Optimal Temperature Prediction (R² = {val_metrics["optimal_temperature_r2"]:.4f})')
        else:
            plt.title('Optimal Temperature Prediction')
        
        plt.xlabel('Actual Optimal Temperature (°C)')
        plt.ylabel('Predicted Optimal Temperature (°C)')
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'optimal_temperature_prediction.png'))

def main():
    """Main function."""
    args = parse_args()
    
    # Set random seed
    torch.manual_seed(args.seed)
    np.random.seed(args.seed)
    
    # Create output directories
    os.makedirs(args.output_dir, exist_ok=True)
    
    if args.metrics_dir is None:
        args.metrics_dir = os.path.join(args.output_dir, 'metrics')
    os.makedirs(args.metrics_dir, exist_ok=True)
    
    # Set device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"Using device: {device}")
    
    # Create dataset
    logger.info(f"Loading dataset from {args.feature_file}...")
    dataset = DeepMuTsvDataset(
        metadata_file=args.metadata,
        feature_file=args.feature_file
    )
    
    # Split dataset into train and validation sets
    train_indices, val_indices = train_test_split(
        range(len(dataset)),
        test_size=0.2,
        random_state=args.seed
    )
    
    train_dataset = torch.utils.data.Subset(dataset, train_indices)
    val_dataset = torch.utils.data.Subset(dataset, val_indices)
    
    logger.info(f"Train set size: {len(train_dataset)}, Validation set size: {len(val_dataset)}")
    
    # Create data loaders
    train_loader = torch.utils.data.DataLoader(
        train_dataset,
        batch_size=args.batch_size,
        shuffle=True,
        num_workers=args.num_workers
    )
    
    val_loader = torch.utils.data.DataLoader(
        val_dataset,
        batch_size=args.batch_size,
        shuffle=False,
        num_workers=args.num_workers
    )
    
    # Select features for each target
    logger.info("Selecting features for each target...")
    growth_rate_selector, optimal_temperature_selector = select_features_for_targets(
        dataset,
        growth_rate_k=args.growth_rate_features,
        optimal_temperature_k=args.optimal_temperature_features,
        method=args.correlation_method,
        max_samples=args.max_samples
    )
    
    # Get selected feature names
    growth_rate_features = growth_rate_selector.get_feature_names() if growth_rate_selector else []
    optimal_temperature_features = optimal_temperature_selector.get_feature_names() if optimal_temperature_selector else []
    
    # Save feature importance report
    feature_report_path = os.path.join(args.metrics_dir, 'feature_importance_report.md')
    save_feature_importance_report(
        growth_rate_features=growth_rate_features,
        optimal_temperature_features=optimal_temperature_features,
        output_path=feature_report_path
    )
    
    # Create model
    logger.info("Creating improved hybrid model...")
    model = create_improved_hybrid_model(
        growth_rate_features=growth_rate_features,
        optimal_temperature_features=optimal_temperature_features,
        hidden_dim=args.hidden_dim,
        dropout=args.dropout,
        num_layers=args.num_layers,
        use_batch_norm=args.use_batch_norm,
        n_estimators=args.n_estimators,
        max_depth=args.max_depth,
        min_samples_split=args.min_samples_split,
        min_samples_leaf=args.min_samples_leaf,
        device=device
    )
    
    # Train model
    logger.info(f"Training model for {args.num_epochs} epochs...")
    model.train(train_loader, num_epochs=args.num_epochs)
    
    # Evaluate model
    logger.info("Evaluating model after training...")
    train_metrics = model.evaluate(train_loader)
    val_metrics = model.evaluate(val_loader)
    
    # Log metrics
    logger.info(f"Training metrics: {train_metrics}")
    logger.info(f"Validation metrics: {val_metrics}")
    
    # Save model
    model_path = os.path.join(args.output_dir, 'improved_hybrid_model')
    model.save(model_path)
    
    # Save metrics
    metrics_path = os.path.join(args.metrics_dir, 'training_metrics.json')
    
    # Convert numpy values to Python native types for JSON serialization
    for metric_set in [train_metrics, val_metrics]:
        for key, value in metric_set.items():
            if isinstance(value, (np.float32, np.float64)):
                metric_set[key] = float(value)
            elif isinstance(value, (np.int32, np.int64)):
                metric_set[key] = int(value)
            elif isinstance(value, list):
                metric_set[key] = [float(x) if isinstance(x, (np.float32, np.float64)) else x for x in value]
    
    with open(metrics_path, 'w') as f:
        json.dump({
            'train_metrics': [train_metrics],
            'val_metrics': [val_metrics]
        }, f, indent=2)
    
    # Save prediction plots
    save_prediction_plots(val_metrics, args.metrics_dir)
    
    logger.info(f"Training complete. Model saved to {model_path}")
    logger.info(f"Metrics saved to {metrics_path}")
    logger.info(f"Feature importance report saved to {feature_report_path}")

if __name__ == '__main__':
    main()
