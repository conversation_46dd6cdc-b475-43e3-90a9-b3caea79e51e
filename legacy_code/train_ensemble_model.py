#!/usr/bin/env python
"""
Train an ensemble model that combines Random Forest for growth rate prediction
and Transformer for optimal temperature prediction.

This script trains an ensemble model that leverages the strengths of both approaches:
- Random Forest excels at tabular data and regression tasks like growth rate prediction (R² = 0.773)
- Transformer architecture has shown superior performance for temperature prediction (R² = 0.875)
"""

import os
import sys
import argparse
import logging
import json
import numpy as np
import pandas as pd
import torch
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from typing import Dict, List, Tuple, Optional, Union

from deepmu.models.ensemble_model import EnsembleModel, EnsembleModelTrainer
from deepmu.utils.logging import get_logger

# Set up logging
logger = get_logger()


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Train an ensemble model')

    # Input data
    parser.add_argument('--feature-file', type=str, required=True,
                        help='Path to combined feature file (TSV)')
    parser.add_argument('--metadata', type=str, required=True,
                        help='Path to metadata file (TSV)')
    parser.add_argument('--transformer-model', type=str, required=True,
                        help='Path to pre-trained transformer model')

    # Output
    parser.add_argument('--output-dir', type=str, required=True,
                        help='Directory to save model and results')
    parser.add_argument('--metrics-dir', type=str, default=None,
                        help='Directory to save metrics and plots')

    # Random Forest parameters
    parser.add_argument('--rf-n-estimators', type=int, default=200,
                        help='Number of trees in random forest')
    parser.add_argument('--rf-max-depth', type=int, default=20,
                        help='Maximum depth of trees in random forest')
    parser.add_argument('--rf-min-samples-split', type=int, default=2,
                        help='Minimum samples required to split a node')
    parser.add_argument('--rf-min-samples-leaf', type=int, default=1,
                        help='Minimum samples required at a leaf node')

    # Transformer parameters
    parser.add_argument('--transformer-hidden-dims', type=int, nargs='+', default=[256, 128, 64],
                        help='Hidden layer dimensions for transformer')
    parser.add_argument('--transformer-dropout-rates', type=float, nargs='+', default=[0.2, 0.3, 0.4],
                        help='Dropout rates for transformer')
    parser.add_argument('--transformer-heads', type=int, default=4,
                        help='Number of attention heads in transformer')
    parser.add_argument('--transformer-layers', type=int, default=2,
                        help='Number of transformer layers')
    parser.add_argument('--no-batch-norm', action='store_true',
                        help='Disable batch normalization')
    parser.add_argument('--no-residual', action='store_true',
                        help='Disable residual connections')
    parser.add_argument('--no-positional-encoding', action='store_true',
                        help='Disable positional encoding in transformer')

    # Training parameters
    parser.add_argument('--test-size', type=float, default=0.2,
                        help='Test set size')
    parser.add_argument('--random-state', type=int, default=42,
                        help='Random state for reproducibility')

    return parser.parse_args()


def load_data(feature_file: str, metadata_file: str) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """Load feature and metadata files.

    Args:
        feature_file: Path to feature file (TSV)
        metadata_file: Path to metadata file (TSV)

    Returns:
        Tuple of (features_df, metadata_df)
    """
    logger.info(f"Loading features from {feature_file}")
    features_df = pd.read_csv(feature_file, sep='\t', index_col='genome_id')

    logger.info(f"Loading metadata from {metadata_file}")
    metadata_df = pd.read_csv(metadata_file, sep='\t', index_col='genome_id')

    # Get common genomes
    common_genomes = list(set(features_df.index) & set(metadata_df.index))
    logger.info(f"Found {len(common_genomes)} common genomes")

    # Filter to common genomes
    features_df = features_df.loc[common_genomes]
    metadata_df = metadata_df.loc[common_genomes]

    return features_df, metadata_df


def categorize_features(features_df: pd.DataFrame) -> Dict[str, List[str]]:
    """Categorize features into different types.

    Args:
        features_df: DataFrame with features

    Returns:
        Dictionary mapping feature types to lists of feature names
    """
    feature_categories = {
        'codon': [],
        'aa': [],
        'genomic': [],
        'rna': [],
        'pi': [],
        'taxonomy': [],
        'pathway': []
    }

    # Print a few column names to help with debugging
    logger.info(f"Sample feature names: {list(features_df.columns[:10])}")
    logger.info(f"Total features: {len(features_df.columns)}")

    # Categorize features based on prefixes and keywords
    for feature in features_df.columns:
        # Convert to lowercase for case-insensitive matching
        feature_lower = feature.lower()

        # Codon features - updated to include renamed features
        if any(keyword in feature_lower for keyword in [
            'cub', 'cpb', 'cai', 'enc', 'rscu', 'codon', 'heg', 'bg', 'delta',
            'deviation', 'divergence', 'composite', 'context_bias'
        ]):
            feature_categories['codon'].append(feature)

        # Amino acid features
        elif any(keyword in feature_lower for keyword in [
            'aa_', 'amino', 'protein', 'arsc', 'nitrogen', 'carbon', 'c_arsc', 'n_arsc'
        ]):
            feature_categories['aa'].append(feature)

        # Genomic features
        elif any(keyword in feature_lower for keyword in [
            'genome', 'gc_', 'gc1', 'gc2', 'gc3', 'skew', 'dinuc', 'size', 'length', 'breakpoint'
        ]):
            feature_categories['genomic'].append(feature)

        # RNA features
        elif any(keyword in feature_lower for keyword in [
            'trna', 'rrna', 'tai', 'rna', 'rrna_count', 'trna_count'
        ]):
            feature_categories['rna'].append(feature)

        # Protein pI features
        elif any(keyword in feature_lower for keyword in [
            'pi_', 'isoelectric', 'ph', 'pi_mean', 'pi_median', 'pi_std'
        ]):
            feature_categories['pi'].append(feature)

        # Taxonomy features
        elif any(keyword in feature_lower for keyword in [
            'phylum', 'class', 'order', 'family', 'genus', 'taxid', 'taxonomy'
        ]):
            feature_categories['taxonomy'].append(feature)

        # Pathway features
        elif any(keyword in feature_lower for keyword in [
            'pathway', 'kegg', 'ko_', 'module', 'completeness'
        ]):
            feature_categories['pathway'].append(feature)

        # Default to codon features if not categorized
        else:
            # Check if it's likely a numeric feature (not an ID or metadata)
            if feature != 'genome_id' and not feature.endswith('_id'):
                logger.info(f"Uncategorized feature: {feature}")
                feature_categories['codon'].append(feature)

    # Log feature counts
    for category, features in feature_categories.items():
        logger.info(f"Found {len(features)} {category} features")
        if len(features) > 0:
            logger.info(f"Sample {category} features: {features[:5]}")

    return feature_categories


def prepare_data(
    features_df: pd.DataFrame,
    metadata_df: pd.DataFrame,
    feature_categories: Dict[str, List[str]],
    test_size: float = 0.2,
    random_state: int = 42
) -> Dict[str, Dict[str, np.ndarray]]:
    """Prepare data for training.

    Args:
        features_df: Feature DataFrame
        metadata_df: Metadata DataFrame
        feature_categories: Dictionary mapping feature types to lists of feature names
        test_size: Test set size
        random_state: Random state for reproducibility

    Returns:
        Dictionary with train and test data for each feature type and target
    """
    # Get target variables
    y_growth = metadata_df['growth_rate'].values
    y_temp = metadata_df['optimal_temperature'].values

    # Split data
    indices = np.arange(len(features_df))
    train_indices, test_indices = train_test_split(
        indices, test_size=test_size, random_state=random_state
    )

    # Prepare data dictionary
    data = {
        'train': {
            'features': {},
            'targets': {}
        },
        'test': {
            'features': {},
            'targets': {}
        }
    }

    # Process each feature category
    for category, feature_names in feature_categories.items():
        if not feature_names:
            continue

        # Extract features
        X_category = features_df[feature_names].values

        # Split into train and test
        X_train = X_category[train_indices]
        X_test = X_category[test_indices]

        # Scale features
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)

        # Store in data dictionary
        data['train']['features'][category] = X_train_scaled
        data['test']['features'][category] = X_test_scaled

    # Add targets
    data['train']['targets']['growth_rate'] = y_growth[train_indices]
    data['train']['targets']['temperature'] = y_temp[train_indices]
    data['test']['targets']['growth_rate'] = y_growth[test_indices]
    data['test']['targets']['temperature'] = y_temp[test_indices]

    return data


def plot_feature_importance(importance_df: pd.DataFrame, output_dir: str, top_n: int = 20, by_category: bool = True):
    """Plot feature importance from Random Forest.

    Args:
        importance_df: DataFrame with feature importance
        output_dir: Directory to save plot
        top_n: Number of top features to show
        by_category: Whether to create separate plots for each feature category
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Get top N features overall
    top_features = importance_df.head(top_n)

    # Plot overall feature importance
    plt.figure(figsize=(12, 8))
    plt.barh(top_features['feature'], top_features['importance'])
    plt.xlabel('Importance')
    plt.ylabel('Feature')
    plt.title(f'Top {top_n} Feature Importance for Growth Rate Prediction')
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'feature_importance.png'), dpi=300)
    plt.close()

    # Save top features to TSV
    top_features.to_csv(os.path.join(output_dir, 'top_features.tsv'), sep='\t', index=False)

    # Plot feature importance by category if requested
    if by_category:
        # Add category column based on feature name
        importance_df['category'] = 'other'

        # Categorize features
        for feature in importance_df['feature']:
            feature_lower = feature.lower()

            if any(keyword in feature_lower for keyword in ['cub', 'cpb', 'cai', 'enc', 'rscu', 'codon', 'heg', 'bg', 'delta', 'deviation', 'divergence']):
                importance_df.loc[importance_df['feature'] == feature, 'category'] = 'codon'
            elif any(keyword in feature_lower for keyword in ['aa_', 'amino', 'protein', 'arsc', 'nitrogen', 'carbon']):
                importance_df.loc[importance_df['feature'] == feature, 'category'] = 'amino_acid'
            elif any(keyword in feature_lower for keyword in ['genome', 'gc_', 'gc1', 'gc2', 'gc3', 'skew', 'dinuc', 'size', 'length']):
                importance_df.loc[importance_df['feature'] == feature, 'category'] = 'genomic'
            elif any(keyword in feature_lower for keyword in ['trna', 'rrna', 'tai', 'rna']):
                importance_df.loc[importance_df['feature'] == feature, 'category'] = 'rna'
            elif any(keyword in feature_lower for keyword in ['pi_', 'isoelectric', 'ph']):
                importance_df.loc[importance_df['feature'] == feature, 'category'] = 'protein_pi'
            elif any(keyword in feature_lower for keyword in ['phylum', 'class', 'order', 'family', 'genus', 'taxid', 'taxonomy']):
                importance_df.loc[importance_df['feature'] == feature, 'category'] = 'taxonomy'
            elif any(keyword in feature_lower for keyword in ['pathway', 'kegg', 'ko_', 'module', 'completeness']):
                importance_df.loc[importance_df['feature'] == feature, 'category'] = 'pathway'

        # Plot top features for each category
        categories = importance_df['category'].unique()

        for category in categories:
            category_df = importance_df[importance_df['category'] == category].head(min(top_n, len(importance_df[importance_df['category'] == category])))

            if len(category_df) > 0:
                plt.figure(figsize=(12, 8))
                plt.barh(category_df['feature'], category_df['importance'])
                plt.xlabel('Importance')
                plt.ylabel('Feature')
                plt.title(f'Top {len(category_df)} {category.capitalize()} Feature Importance')
                plt.tight_layout()
                plt.savefig(os.path.join(output_dir, f'feature_importance_{category}.png'), dpi=300)
                plt.close()

                # Save category features to TSV
                category_df.to_csv(os.path.join(output_dir, f'top_features_{category}.tsv'), sep='\t', index=False)


def plot_predictions(y_true: np.ndarray, y_pred: np.ndarray, output_dir: str, title: str, filename: str):
    """Plot true vs predicted values.

    Args:
        y_true: True values
        y_pred: Predicted values
        output_dir: Directory to save plot
        title: Plot title
        filename: Output filename
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Calculate metrics
    r2 = r2_score(y_true, y_pred)
    rmse = np.sqrt(mean_squared_error(y_true, y_pred))

    # Plot true vs predicted
    plt.figure(figsize=(10, 8))
    plt.scatter(y_true, y_pred, alpha=0.5)

    # Add diagonal line
    min_val = min(np.min(y_true), np.min(y_pred))
    max_val = max(np.max(y_true), np.max(y_pred))
    plt.plot([min_val, max_val], [min_val, max_val], 'r--')

    # Add metrics to plot
    plt.text(0.05, 0.95, f'R² = {r2:.3f}\nRMSE = {rmse:.3f}',
             transform=plt.gca().transAxes, fontsize=12,
             verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.5))

    plt.xlabel('True Values')
    plt.ylabel('Predicted Values')
    plt.title(title)
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, filename), dpi=300)
    plt.close()

    return r2, rmse


def save_metrics_to_tsv(metrics: Dict[str, float], output_file: str):
    """Save metrics to a TSV file.

    Args:
        metrics: Dictionary with metrics
        output_file: Path to output file
    """
    metrics_df = pd.DataFrame({
        'metric': list(metrics.keys()),
        'value': list(metrics.values())
    })

    metrics_df.to_csv(output_file, sep='\t', index=False)

    # Also save as JSON for easier parsing
    import json
    json_file = output_file.replace('.tsv', '.json')
    with open(json_file, 'w') as f:
        json.dump(metrics, f, indent=2)


def main():
    """Main function."""
    # Parse arguments
    args = parse_args()

    # Create output directories
    os.makedirs(args.output_dir, exist_ok=True)

    metrics_dir = args.metrics_dir or os.path.join(args.output_dir, 'metrics')
    os.makedirs(metrics_dir, exist_ok=True)

    # Load data
    features_df, metadata_df = load_data(args.feature_file, args.metadata)

    # Categorize features
    feature_categories = categorize_features(features_df)

    # Prepare data
    data = prepare_data(
        features_df,
        metadata_df,
        feature_categories,
        args.test_size,
        args.random_state
    )

    # Get feature dimensions
    feature_dims = {
        category: features.shape[1]
        for category, features in data['train']['features'].items()
    }

    # Create model
    model = EnsembleModel(
        feature_dims=feature_dims,
        transformer_hidden_dims=args.transformer_hidden_dims,
        transformer_dropout_rates=args.transformer_dropout_rates,
        transformer_heads=args.transformer_heads,
        transformer_layers=args.transformer_layers,
        use_batch_norm=not args.no_batch_norm,
        use_residual=not args.no_residual,
        use_positional_encoding=not args.no_positional_encoding,
        rf_n_estimators=args.rf_n_estimators,
        rf_max_depth=args.rf_max_depth,
        rf_min_samples_split=args.rf_min_samples_split,
        rf_min_samples_leaf=args.rf_min_samples_leaf
    )

    # Create trainer
    trainer = EnsembleModelTrainer(model=model)

    # Set up parameters
    rf_params = {}

    # Train model
    training_results = trainer.train(
        data=data,
        transformer_model_path=args.transformer_model,
        rf_params=rf_params,
        output_dir=args.output_dir
    )

    # Get feature importance
    feature_names = []
    for category, names in feature_categories.items():
        feature_names.extend(names)

    importance_df = model.get_feature_importance(feature_names)

    # Plot feature importance
    plot_feature_importance(importance_df, metrics_dir, top_n=30, by_category=True)

    # Generate prediction plots
    # Get test data predictions
    test_features = data['test']['features']
    test_growth_rate = data['test']['targets']['growth_rate']
    test_temperature = data['test']['targets']['temperature']

    # Make predictions
    growth_rate_pred = model.predict_growth_rate(test_features)
    temperature_pred = model.predict_temperature(test_features)

    # Plot predictions
    growth_r2, growth_rmse = plot_predictions(
        test_growth_rate,
        growth_rate_pred,
        metrics_dir,
        'Growth Rate Prediction',
        'growth_rate_predictions.png'
    )

    temp_r2, temp_rmse = plot_predictions(
        test_temperature,
        temperature_pred,
        metrics_dir,
        'Optimal Temperature Prediction',
        'temperature_predictions.png'
    )

    # Calculate additional metrics
    growth_mae = mean_absolute_error(test_growth_rate, growth_rate_pred)
    temp_mae = mean_absolute_error(test_temperature, temperature_pred)

    # Combine all metrics
    all_metrics = {
        'growth_rate_r2': growth_r2,
        'growth_rate_rmse': growth_rmse,
        'growth_rate_mae': growth_mae,
        'temperature_r2': temp_r2,
        'temperature_rmse': temp_rmse,
        'temperature_mae': temp_mae
    }

    # Update model metrics
    model.metrics.update(all_metrics)

    # Save final metrics
    metrics_file = os.path.join(metrics_dir, 'metrics.tsv')
    save_metrics_to_tsv(model.metrics, metrics_file)

    # Save feature importance
    importance_file = os.path.join(metrics_dir, 'feature_importance.tsv')
    importance_df.to_csv(importance_file, sep='\t', index=False)

    # Log final metrics
    logger.info("Final metrics:")
    for metric, value in model.metrics.items():
        logger.info(f"  {metric}: {value:.4f}")

    logger.info(f"Training complete. Models saved to {args.output_dir}")
    logger.info(f"Metrics and plots saved to {metrics_dir}")


if __name__ == '__main__':
    main()
