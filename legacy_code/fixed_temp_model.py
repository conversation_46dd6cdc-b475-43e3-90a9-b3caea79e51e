#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Fixed Temperature Model for DeepMu.

This module implements a fixed version of the temperature prediction component
with proper structure and improved implementation.
"""

import os
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import TensorDataset, DataLoader
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import RobustScaler
import joblib
import logging
import copy
from typing import Dict, Tuple, Union, List, Optional

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Use GPU if available
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

class AdvancedTemperatureModel(nn.Module):
    """Ensemble temperature model with feature engineering and residual connections."""
    
    def __init__(self, input_dim):
        super().__init__()
        
        # Feature preprocessing
        self.feature_norm = nn.LayerNorm(input_dim)
        
        # Regularization parameters
        self.dropout_base = 0.2
        self.spectral_norm = True
        self.weight_decay_factor = 1e-4
        
        # Enhanced polynomial feature combinations
        self.use_poly_features = True
        self.poly_feature_count = min(10, input_dim)
        expanded_dim = input_dim
        if self.use_poly_features:
            # Add both quadratic terms and interaction terms for important features
            expanded_dim += (self.poly_feature_count * (self.poly_feature_count - 1)) // 2 + self.poly_feature_count
        
        # Feature scaling layers
        self.feature_scaling = nn.Sequential(
            nn.BatchNorm1d(expanded_dim),
            nn.Dropout(0.1)
        )
        
        # Multi-model ensemble approach
        self.n_estimators = 7
        self.subsample_ratio = 0.8
        self.subsample_features = True
        self.feature_subset_size = int(0.7 * input_dim)
        
        # Store feature importance scores
        self.feature_importance_scores = torch.ones(input_dim, device=device)
        
        # Create sub-models with different architectures
        self.sub_models = nn.ModuleList()
        
        # Model 1: Deep residual network with skip connections
        self.sub_models.append(nn.Sequential(
            nn.Linear(expanded_dim, 512),
            nn.BatchNorm1d(512),
            nn.GELU(),
            nn.Dropout(self.dropout_base),
            nn.Linear(512, 256),
            nn.BatchNorm1d(256),
            nn.GELU(),
            nn.Dropout(self.dropout_base),
            nn.Linear(256, 128),
            nn.BatchNorm1d(128),
            nn.GELU(),
            nn.Dropout(self.dropout_base),
            nn.Linear(128, 64),
            nn.BatchNorm1d(64),
            nn.GELU(),
            nn.Dropout(self.dropout_base),
            nn.Linear(64, 1)
        ))
        
        # Model 2: Shallow network
        self.sub_models.append(nn.Sequential(
            nn.Linear(input_dim, 128),
            nn.ReLU(),
            nn.Dropout(self.dropout_base),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Dropout(self.dropout_base),
            nn.Linear(64, 1)
        ))
        
        # Model 3: Ridge regression-like model
        self.sub_models.append(nn.Sequential(
            nn.Linear(input_dim, 64),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Linear(64, 1)
        ))
        
        # Model 4: Deep network with alternate activation
        self.sub_models.append(nn.Sequential(
            nn.Linear(expanded_dim, 256),
            nn.BatchNorm1d(256),
            nn.SiLU(),
            nn.Dropout(self.dropout_base + 0.1),
            nn.Linear(256, 128),
            nn.BatchNorm1d(128),
            nn.SiLU(),
            nn.Dropout(self.dropout_base + 0.1),
            nn.Linear(128, 64),
            nn.BatchNorm1d(64),
            nn.SiLU(),
            nn.Dropout(self.dropout_base + 0.1),
            nn.Linear(64, 1)
        ))
        
        # Model 5: Extra wide network
        self.sub_models.append(nn.Sequential(
            nn.Linear(input_dim, 512),
            nn.BatchNorm1d(512),
            nn.GELU(),
            nn.Dropout(self.dropout_base + 0.05),
            nn.Linear(512, 256),
            nn.BatchNorm1d(256),
            nn.GELU(),
            nn.Dropout(self.dropout_base + 0.05),
            nn.Linear(256, 1)
        ))
        
        # Model 6: Specialized model for extreme values
        self.sub_models.append(nn.Sequential(
            nn.Linear(input_dim, 256),
            nn.BatchNorm1d(256),
            nn.LeakyReLU(0.2),
            nn.Dropout(self.dropout_base),
            nn.Linear(256, 128),
            nn.BatchNorm1d(128),
            nn.LeakyReLU(0.2),
            nn.Dropout(self.dropout_base),
            nn.Linear(128, 1)
        ))
        
        # Model 7: Transformer-based model
        transformer_layer = nn.TransformerEncoderLayer(
            d_model=input_dim,
            nhead=4,
            dim_feedforward=256,
            dropout=0.1,
            batch_first=True
        )
        self.transformer = nn.TransformerEncoder(transformer_layer, num_layers=1)
        self.transformer_projection = nn.Linear(input_dim, 1)
        
        # Meta-learner with self-attention
        self.meta_attention = nn.MultiheadAttention(
            embed_dim=len(self.sub_models),
            num_heads=1,
            batch_first=True
        )
        
        self.meta_learner = nn.Sequential(
            nn.Linear(len(self.sub_models), 32),
            nn.LayerNorm(32),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(32, 16),
            nn.ReLU(),
            nn.Linear(16, 1)
        )
        
        # Initialize meta-learner with equal weights
        with torch.no_grad():
            self.meta_learner[0].weight.fill_(1.0 / len(self.sub_models))
        
        # Track feature importance
        self.feature_importances = torch.ones(input_dim, device=device) / input_dim
        
        # Feature selection indices
        self.feature_indices = []
        if self.subsample_features:
            for _ in range(self.n_estimators):
                indices = torch.randperm(input_dim)[:self.feature_subset_size]
                self.feature_indices.append(indices)
        
        # For stochastic weight averaging
        self.swa_count = 0
        self.swa_model = None
    
    def _generate_poly_features(self, x):
        """Generate polynomial features with quadratic terms and interactions"""
        if not self.use_poly_features:
            return x
        
        # Get input shape
        original_shape = x.shape
        
        # Use the first few features for polynomial interactions
        poly_indices = list(range(min(self.poly_feature_count, x.size(1))))
        
        # Extract selected features
        selected = x[:, poly_indices]
        
        # Create squared terms
        squared_terms = []
        for i in range(len(poly_indices)):
            squared_terms.append(selected[:, i] ** 2)
        
        # Create interaction terms
        interactions = []
        for i in range(len(poly_indices)):
            for j in range(i+1, len(poly_indices)):
                interactions.append(selected[:, i] * selected[:, j])
        
        # Concatenate original + squared + interactions
        interactions_tensor = torch.stack(interactions, dim=1) if interactions else torch.tensor([]).to(x.device)
        squared_tensor = torch.stack(squared_terms, dim=1) if squared_terms else torch.tensor([]).to(x.device)
        
        # Combine everything
        result = x
        if interactions_tensor.shape[0] > 0 and interactions_tensor.shape[1] > 0:
            result = torch.cat([result, interactions_tensor], dim=1)
        if squared_tensor.shape[0] > 0 and squared_tensor.shape[1] > 0:
            result = torch.cat([result, squared_tensor], dim=1)
        
        return result
    
    def _subsample_batch(self, x, ratio=0.8):
        """Create bootstrap sample of the batch"""
        batch_size = x.size(0)
        n_samples = int(batch_size * ratio)
        
        # Sample indices with replacement
        indices = torch.randint(0, batch_size, (n_samples,), device=x.device)
        return x[indices]
    
    def _subsample_features(self, x, model_idx):
        """Select subset of features for a specific model"""
        if not self.subsample_features or model_idx >= len(self.feature_indices):
            return x
        
        # Use the pre-generated feature indices for this model
        return x[:, self.feature_indices[model_idx]]
    
    def forward(self, x):
        """Forward pass through the ensemble model."""
        # Normalize features
        x = self.feature_norm(x)
        
        # Generate polynomial features
        x_expanded = self._generate_poly_features(x)
        
        # Ensure expanded features match the expected input dimension
        expected_dim = self.sub_models[0][0].weight.shape[1] if hasattr(self.sub_models[0][0], 'weight') else None
        if expected_dim is not None:
            actual_dim = x_expanded.shape[1]
            if actual_dim != expected_dim:
                if actual_dim < expected_dim:
                    # Pad with zeros
                    padding = torch.zeros(x_expanded.size(0), expected_dim - actual_dim, device=x_expanded.device)
                    x_expanded = torch.cat([x_expanded, padding], dim=1)
                else:
                    # Truncate
                    x_expanded = x_expanded[:, :expected_dim]
        
        # Apply feature scaling to expanded features
        if hasattr(self, 'feature_scaling'):
            try:
                x_expanded = self.feature_scaling(x_expanded)
            except Exception as e:
                # Fallback to no scaling
                pass
        
        # Get predictions from each sub-model
        model_outputs = []
        
        # Process each model
        for i, model in enumerate(self.sub_models):
            try:
                if i == 0 or i == 3:  # Models that use expanded features
                    model_input = x_expanded
                else:  # Models that use original features
                    model_input = x
                
                # Feature subsampling for this model if in training
                if self.training and self.subsample_features:
                    model_input = self._subsample_features(model_input, i)
                
                # Instance subsampling if in training
                if self.training and i > 0:  # Don't subsample the first model
                    model_input = self._subsample_batch(model_input, self.subsample_ratio)
                
                # Get expected input dimension for this model
                model_expected_dim = model[0].weight.shape[1] if hasattr(model[0], 'weight') else None
                if model_expected_dim is not None and model_input.shape[1] != model_expected_dim:
                    if model_input.shape[1] < model_expected_dim:
                        # Pad with zeros
                        padding = torch.zeros(model_input.size(0), model_expected_dim - model_input.shape[1], device=model_input.device)
                        model_input = torch.cat([model_input, padding], dim=1)
                    else:
                        # Truncate
                        model_input = model_input[:, :model_expected_dim]
                
                # Get model output
                model_output = model(model_input)
                model_outputs.append(model_output)
            except Exception as e:
                # Skip this model if there's an error
                continue
        
        # Process transformer model separately (Model 7)
        try:
            if self.training:
                # Add feature dimension for transformer
                x_transformer = x.unsqueeze(1)
                transformer_output = self.transformer(x_transformer)
                transformer_output = transformer_output.squeeze(1)
                transformer_pred = self.transformer_projection(transformer_output)
                model_outputs.append(transformer_pred)
            else:
                # For inference, just use a simplified approach
                transformer_pred = self.transformer_projection(x)
                model_outputs.append(transformer_pred)
        except Exception as e:
            # Skip transformer model if there's an error
            pass
        
        # Ensure we have at least one valid output
        if len(model_outputs) == 0:
            raise RuntimeError("All models failed to produce outputs")
        
        # Stack all outputs for meta-learner
        stacked_outputs = []
        # Ensure all outputs are same batch size for stacking
        min_batch_size = min(output.size(0) for output in model_outputs)
        
        for output in model_outputs:
            # Take up to min_batch_size samples
            stacked_outputs.append(output[:min_batch_size])
        
        # Stack along feature dimension
        meta_input = torch.cat(stacked_outputs, dim=1)
        
        # Apply attention-based weighting
        if hasattr(self, 'meta_attention'):
            try:
                # Reshape for the attention mechanism
                meta_attention_input = meta_input.unsqueeze(1)
                meta_attention_output, _ = self.meta_attention(
                    meta_attention_input, meta_attention_input, meta_attention_input
                )
                # Return to original shape
                meta_input = meta_attention_output.squeeze(1)
            except Exception as e:
                # Skip attention if there's an error
                pass
        
        # Use meta-learner to combine predictions
        try:
            final_output = self.meta_learner(meta_input)
            return final_output
        except Exception as e:
            # If meta-learner fails, return average of model outputs
            return torch.mean(torch.cat(stacked_outputs, dim=1), dim=1, keepdim=True)

class TemperatureModelTrainer:
    """Trainer class for the temperature prediction model."""
    
    def __init__(self, output_dir: str = "models/fixed_temp_model"):
        """Initialize the trainer."""
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        
        # Set up logging to file
        file_handler = logging.FileHandler(os.path.join(output_dir, "training.log"))
        file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
        logger.addHandler(file_handler)
        
        # Initialize model and components to None
        self.model = None
        self.scaler = None
        self.feature_indices = None
        
        # Training history
        self.history = {
            "train_loss": [],
            "val_loss": [],
            "train_r2": [],
            "val_r2": []
        }
    
    def load_data(self, feature_file: str, metadata_file: str, n_features: int = 800) -> Tuple[pd.DataFrame, pd.Series]:
        """Load and prepare data."""
        logger.info(f"Loading features from {feature_file}")
        features = pd.read_csv(feature_file, sep='\t', index_col='genome_id')
        
        logger.info(f"Loading metadata from {metadata_file}")
        metadata = pd.read_csv(metadata_file, sep='\t', index_col='genome_id')
        
        # Get common samples
        common_indices = features.index.intersection(metadata.index)
        logger.info(f"Found {len(common_indices)} common samples")
        
        features = features.loc[common_indices]
        metadata = metadata.loc[common_indices]
        
        # Handle missing values
        features = features.fillna(0)
        
        # Select top n_features with highest variance if needed
        if n_features and features.shape[1] > n_features:
            logger.info(f"Selecting top {n_features} features by variance")
            feature_variance = features.var().sort_values(ascending=False)
            selected_features = feature_variance.index[:n_features].tolist()
            self.feature_indices = selected_features
            return features[selected_features], metadata['optimal_temperature']
        else:
            self.feature_indices = features.columns.tolist()
            return features, metadata['optimal_temperature']
    
    def prepare_data_loaders(self, features: pd.DataFrame, temperatures: pd.Series, 
                            batch_size: int = 64) -> Dict:
        """Split data and create data loaders."""
        # Split data
        X_train, X_val, y_train, y_val = train_test_split(
            features, temperatures, test_size=0.2, random_state=42
        )
        
        # Scale features
        self.scaler = RobustScaler()
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_val_scaled = self.scaler.transform(X_val)
        
        # Create datasets
        train_dataset = TensorDataset(
            torch.FloatTensor(X_train_scaled).to(device),
            torch.FloatTensor(y_train.values.reshape(-1, 1)).to(device)
        )
        
        val_dataset = TensorDataset(
            torch.FloatTensor(X_val_scaled).to(device),
            torch.FloatTensor(y_val.values.reshape(-1, 1)).to(device)
        )
        
        # Create data loaders
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size=batch_size*2)
        
        return {
            "train_loader": train_loader,
            "val_loader": val_loader,
            "X_train": X_train,
            "X_val": X_val,
            "y_train": y_train,
            "y_val": y_val,
            "X_train_scaled": X_train_scaled,
            "X_val_scaled": X_val_scaled
        }
    
    def train(self, features: pd.DataFrame, temperatures: pd.Series, 
              epochs: int = 200, lr: float = 0.001, batch_size: int = 64, 
              patience: int = 30) -> None:
        """Train the model."""
        # Prepare data
        data = self.prepare_data_loaders(features, temperatures, batch_size)
        train_loader = data["train_loader"]
        val_loader = data["val_loader"]
        
        # Initialize model
        input_dim = features.shape[1]
        self.model = AdvancedTemperatureModel(input_dim=input_dim).to(device)
        
        # Create optimizer and scheduler
        optimizer = optim.AdamW(
            self.model.parameters(),
            lr=lr,
            weight_decay=1e-6,
            eps=1e-8
        )
        
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, mode='min', factor=0.5, patience=5, verbose=True
        )
        
        # Set up early stopping
        early_stopping_counter = 0
        best_val_loss = float('inf')
        best_model_state = None
        
        # Training loop
        for epoch in range(epochs):
            # Training phase
            self.model.train()
            train_loss = 0.0
            
            for X_batch, y_batch in train_loader:
                optimizer.zero_grad()
                
                # Forward pass
                y_pred = self.model(X_batch)
                loss = F.mse_loss(y_pred, y_batch)
                
                # Backward pass
                loss.backward()
                
                # Gradient clipping to prevent exploding gradients
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                
                optimizer.step()
                train_loss += loss.item() * len(y_batch)
            
            train_loss /= len(train_loader.dataset)
            
            # Validation phase
            self.model.eval()
            val_loss = 0.0
            val_preds = []
            val_targets = []
            
            with torch.no_grad():
                for X_batch, y_batch in val_loader:
                    # Forward pass
                    y_pred = self.model(X_batch)
                    loss = F.mse_loss(y_pred, y_batch)
                    
                    val_loss += loss.item() * len(y_batch)
                    
                    # Store predictions and targets for metrics calculation
                    val_preds.extend(y_pred.cpu().numpy().flatten())
                    val_targets.extend(y_batch.cpu().numpy().flatten())
            
            val_loss /= len(val_loader.dataset)
            
            # Update scheduler
            scheduler.step(val_loss)
            
            # Calculate R² metrics
            train_preds = []
            train_targets = []
            
            self.model.eval()
            with torch.no_grad():
                for X_batch, y_batch in train_loader:
                    y_pred = self.model(X_batch)
                    train_preds.extend(y_pred.cpu().numpy().flatten())
                    train_targets.extend(y_batch.cpu().numpy().flatten())
            
            train_r2 = r2_score(train_targets, train_preds)
            val_r2 = r2_score(val_targets, val_preds)
            
            # Store metrics in history
            self.history["train_loss"].append(train_loss)
            self.history["val_loss"].append(val_loss)
            self.history["train_r2"].append(train_r2)
            self.history["val_r2"].append(val_r2)
            
            # Log progress every 10 epochs
            if (epoch + 1) % 10 == 0:
                logger.info(f"Epoch {epoch+1}/{epochs}")
                logger.info(f"  Train Loss: {train_loss:.6f}, Train R²: {train_r2:.6f}")
                logger.info(f"  Val Loss: {val_loss:.6f}, Val R²: {val_r2:.6f}")
                logger.info(f"  Current LR: {optimizer.param_groups[0]['lr']:.6f}")
            
            # Check for improvement
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                best_model_state = copy.deepcopy(self.model.state_dict())
                early_stopping_counter = 0
                logger.info(f"  Validation loss improved to {val_loss:.6f}")
            else:
                early_stopping_counter += 1
                
                if early_stopping_counter >= patience:
                    logger.info(f"Early stopping triggered after {epoch+1} epochs")
                    break
        
        # Load best model
        self.model.load_state_dict(best_model_state)
        
        # Final evaluation
        self.evaluate(data["X_train"], data["y_train"], data["X_val"], data["y_val"])
        
        # Save model and artifacts
        self.save()
    
    def evaluate(self, X_train: pd.DataFrame, y_train: pd.Series, 
                X_val: pd.DataFrame, y_val: pd.Series) -> Dict:
        """Evaluate the model on training and validation data."""
        # Ensure model is in evaluation mode
        self.model.eval()
        
        # Scale data
        X_train_scaled = self.scaler.transform(X_train)
        X_val_scaled = self.scaler.transform(X_val)
        
        # Convert to tensors
        X_train_tensor = torch.FloatTensor(X_train_scaled).to(device)
        X_val_tensor = torch.FloatTensor(X_val_scaled).to(device)
        
        # Get predictions
        with torch.no_grad():
            train_preds = self.model(X_train_tensor).cpu().numpy().flatten()
            val_preds = self.model(X_val_tensor).cpu().numpy().flatten()
        
        # Calculate metrics
        train_metrics = self._calculate_metrics(y_train.values, train_preds)
        val_metrics = self._calculate_metrics(y_val.values, val_preds)
        
        # Log results
        logger.info("Final Evaluation Results:")
        logger.info("Training Set:")
        logger.info(f"  R²: {train_metrics['r2']:.6f}")
        logger.info(f"  RMSE: {train_metrics['rmse']:.6f}")
        logger.info(f"  MAE: {train_metrics['mae']:.6f}")
        
        logger.info("Validation Set:")
        logger.info(f"  R²: {val_metrics['r2']:.6f}")
        logger.info(f"  RMSE: {val_metrics['rmse']:.6f}")
        logger.info(f"  MAE: {val_metrics['mae']:.6f}")
        
        return {
            "train": train_metrics,
            "val": val_metrics
        }
    
    def _calculate_metrics(self, y_true: np.ndarray, y_pred: np.ndarray) -> Dict:
        """Calculate evaluation metrics with consistent methodology."""
        # Ensure 1D arrays
        y_true = np.array(y_true).flatten()
        y_pred = np.array(y_pred).flatten()
        
        # Calculate metrics using scikit-learn
        r2 = r2_score(y_true, y_pred)
        rmse = np.sqrt(mean_squared_error(y_true, y_pred))
        mae = mean_absolute_error(y_true, y_pred)
        
        # Calculate R² manually for verification
        y_mean = np.mean(y_true)
        ss_total = np.sum((y_true - y_mean) ** 2)
        ss_residual = np.sum((y_true - y_pred) ** 2)
        manual_r2 = 1 - (ss_residual / ss_total) if ss_total > 0 else 0.0
        
        # Check for discrepancy
        if abs(r2 - manual_r2) > 0.001:
            logger.warning(f"R² calculation discrepancy: sklearn={r2:.6f}, manual={manual_r2:.6f}")
            
        return {
            "r2": r2,
            "rmse": rmse,
            "mae": mae,
            "manual_r2": manual_r2
        }
    
    def predict(self, features: pd.DataFrame) -> np.ndarray:
        """Make predictions with the trained model."""
        if self.model is None:
            raise ValueError("Model not trained yet")
        
        # Apply feature selection if needed
        if self.feature_indices:
            features = features[self.feature_indices]
        
        # Scale features
        X_scaled = self.scaler.transform(features)
        
        # Convert to tensor
        X_tensor = torch.FloatTensor(X_scaled).to(device)
        
        # Get predictions
        self.model.eval()
        with torch.no_grad():
            predictions = self.model(X_tensor).cpu().numpy().flatten()
        
        return predictions
    
    def save(self) -> None:
        """Save model and artifacts."""
        # Save model state
        torch.save(self.model.state_dict(), os.path.join(self.output_dir, "model.pt"))
        
        # Save scaler
        joblib.dump(self.scaler, os.path.join(self.output_dir, "scaler.pkl"))
        
        # Save feature indices
        if self.feature_indices:
            joblib.dump(self.feature_indices, os.path.join(self.output_dir, "feature_indices.pkl"))
        
        # Save model architecture info - use a safer method to get input_dim
        if self.model:
            # Find the first Linear layer to get its input dimension
            for module in self.model.modules():
                if isinstance(module, nn.Linear):
                    input_dim = module.in_features
                    break
            else:
                # Fallback if no Linear layer is found
                input_dim = len(self.feature_indices) if self.feature_indices else None
        else:
            input_dim = None
        
        model_info = {
            "input_dim": input_dim,
            "history": self.history
        }
        joblib.dump(model_info, os.path.join(self.output_dir, "model_info.pkl"))
        
        logger.info(f"Model and artifacts saved to {self.output_dir}")
    
    def load(self, model_dir: str) -> None:
        """Load a trained model."""
        # Load model info
        model_info = joblib.load(os.path.join(model_dir, "model_info.pkl"))
        
        # Initialize model
        self.model = AdvancedTemperatureModel(input_dim=model_info["input_dim"]).to(device)
        
        # Load model weights
        self.model.load_state_dict(torch.load(os.path.join(model_dir, "model.pt")))
        
        # Load scaler
        self.scaler = joblib.load(os.path.join(model_dir, "scaler.pkl"))
        
        # Load feature indices
        try:
            self.feature_indices = joblib.load(os.path.join(model_dir, "feature_indices.pkl"))
        except:
            self.feature_indices = None
        
        logger.info(f"Model loaded from {model_dir}")

def main():
    """Main function to train the model."""
    # Create trainer
    trainer = TemperatureModelTrainer(output_dir="models/fixed_temp_model")
    
    # Load data
    features, temperatures = trainer.load_data(
        "training_data/combined_features.tsv",
        "training_data/metadata.tsv",
        n_features=800
    )
    
    logger.info(f"Training model with {features.shape[1]} features and {len(temperatures)} samples")
    
    # Train model
    trainer.train(
        features,
        temperatures,
        epochs=200,
        lr=0.001,
        batch_size=64,
        patience=30
    )
    
    logger.info("Training complete!")

if __name__ == "__main__":
    main() 