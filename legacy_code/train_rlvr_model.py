#!/usr/bin/env python3
"""
Training script for the RLVR (Reinforcement Learning with Verifiable Rewards) model.

This script trains an RLVR agent for growth rate prediction using a pre-trained
Random Forest model as a baseline for the reward function.
"""

import os
import sys
import argparse
import json
import numpy as np
import pandas as pd
import torch
import matplotlib.pyplot as plt
import seaborn as sns
import logging
from pathlib import Path
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error

# Add parent directory to path to import deepmu modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '.')))

# Import both the original and enhanced RLVR agents
from deepmu.models.rlvr_agent import RLVRAgent, RLVRTrainer, VerifiableRewardFunction
try:
    from deepmu.models.rlvr_agent import EnhancedRLVRAgent
    HAS_ENHANCED_AGENT = True
except ImportError:
    HAS_ENHANCED_AGENT = False
from deepmu.utils.logging import get_logger

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('rlvr_training.log')
    ]
)
logger = get_logger()

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Train RLVR model for growth rate prediction')

    # Input/output arguments
    parser.add_argument('--feature-file', type=str, required=True,
                        help='Path to feature file (TSV format)')
    parser.add_argument('--metadata', type=str, required=True,
                        help='Path to metadata file (TSV format)')
    parser.add_argument('--output-dir', type=str, default='models/rlvr_model',
                        help='Directory to save model and results')
    parser.add_argument('--metrics-dir', type=str, default='metrics/rlvr_model',
                        help='Directory to save metrics and plots')
    parser.add_argument('--baseline-model', type=str, default=None,
                        help='Path to pre-trained baseline model (optional)')

    # Model parameters
    parser.add_argument('--hidden-dim', type=int, default=256,
                        help='Hidden dimension size')
    parser.add_argument('--num-heads', type=int, default=4,
                        help='Number of attention heads')
    parser.add_argument('--num-layers', type=int, default=2,
                        help='Number of transformer layers')
    parser.add_argument('--dropout', type=float, default=0.2,
                        help='Dropout rate')
    parser.add_argument('--use-layer-norm', action='store_true',
                        help='Use layer normalization')
    parser.add_argument('--use-residual', action='store_true',
                        help='Use residual connections')
    parser.add_argument('--use-value-head', action='store_true',
                        help='Use value head for uncertainty estimation')
    parser.add_argument('--activation', type=str, default='relu', choices=['relu', 'gelu', 'silu'],
                        help='Activation function to use')
    parser.add_argument('--use-enhanced-agent', action='store_true',
                        help='Use the enhanced RLVR agent with improved architecture')
    parser.add_argument('--use-feature-interaction', action='store_true',
                        help='Use feature interaction layers (only for enhanced agent)')
    parser.add_argument('--interaction-layers', type=int, default=2,
                        help='Number of feature interaction layers (only for enhanced agent)')
    parser.add_argument('--use-batch-norm', action='store_true',
                        help='Use batch normalization instead of layer normalization (only for enhanced agent)')

    # Reward function parameters
    parser.add_argument('--alpha', type=float, default=0.6,
                        help='Weight for accuracy component in reward function')
    parser.add_argument('--beta', type=float, default=0.3,
                        help='Weight for consistency component in reward function')
    parser.add_argument('--gamma', type=float, default=0.1,
                        help='Weight for improvement component in reward function')
    parser.add_argument('--accuracy-scale', type=float, default=1.0,
                        help='Scaling factor for accuracy component in reward function')
    parser.add_argument('--no-r2-component', action='store_true',
                        help='Disable R² component in reward function')
    parser.add_argument('--no-improvement-component', action='store_true',
                        help='Disable improvement component in reward function')

    # Training parameters
    parser.add_argument('--lr', type=float, default=1e-4,
                        help='Learning rate')
    parser.add_argument('--weight-decay', type=float, default=1e-5,
                        help='Weight decay for regularization')
    parser.add_argument('--batch-size', type=int, default=32,
                        help='Batch size for training')
    parser.add_argument('--epochs', type=int, default=100,
                        help='Number of epochs to train for')
    parser.add_argument('--early-stopping-patience', type=int, default=10,
                        help='Patience for early stopping')
    parser.add_argument('--no-lr-scheduler', action='store_true',
                        help='Disable learning rate scheduler')
    parser.add_argument('--no-experience-replay', action='store_true',
                        help='Disable experience replay')
    parser.add_argument('--replay-buffer-size', type=int, default=10000,
                        help='Size of experience replay buffer')
    parser.add_argument('--entropy-coef', type=float, default=0.01,
                        help='Coefficient for entropy regularization')
    parser.add_argument('--value-loss-coef', type=float, default=0.5,
                        help='Coefficient for value loss')
    parser.add_argument('--max-grad-norm', type=float, default=1.0,
                        help='Maximum gradient norm for clipping')
    parser.add_argument('--seed', type=int, default=42,
                        help='Random seed')
    parser.add_argument('--test-size', type=float, default=0.2,
                        help='Fraction of data to use for testing')
    parser.add_argument('--val-size', type=float, default=0.2,
                        help='Fraction of training data to use for validation')

    # Feature selection
    parser.add_argument('--feature-selection', action='store_true',
                        help='Use feature selection')
    parser.add_argument('--n-features', type=int, default=50,
                        help='Number of features to select')

    return parser.parse_args()

def load_data(feature_file, metadata_file):
    """
    Load feature and metadata files.

    Args:
        feature_file: Path to feature file (TSV format)
        metadata_file: Path to metadata file (TSV format)

    Returns:
        Tuple of (features_df, metadata_df)
    """
    logger.info(f"Loading features from {feature_file}")
    features_df = pd.read_csv(feature_file, sep='\t')

    logger.info(f"Loading metadata from {metadata_file}")
    metadata_df = pd.read_csv(metadata_file, sep='\t')

    # Merge on genome_id
    merged_df = pd.merge(features_df, metadata_df, on='genome_id', how='inner')

    logger.info(f"Loaded {len(merged_df)} samples with {len(features_df.columns)} features")

    return merged_df

def prepare_data(data_df, target='growth_rate', test_size=0.2, val_size=0.2, seed=42):
    """
    Prepare data for training.

    Args:
        data_df: DataFrame with features and targets
        target: Target column name
        test_size: Fraction of data to use for testing
        val_size: Fraction of training data to use for validation
        seed: Random seed

    Returns:
        Dictionary with train, val, and test data
    """
    # Separate features and target
    X = data_df.drop(['genome_id', 'growth_rate', 'optimal_temperature', 'taxid', 'codon_table', 'kingdom'], axis=1)

    # Handle NaN values
    logger.info(f"Handling NaN values in features")
    X = X.fillna(0)  # Replace NaN with 0

    y = data_df[target]

    # Split into train and test
    X_train_val, X_test, y_train_val, y_test = train_test_split(
        X, y, test_size=test_size, random_state=seed
    )

    # Split train into train and validation
    X_train, X_val, y_train, y_val = train_test_split(
        X_train_val, y_train_val, test_size=val_size, random_state=seed
    )

    # Scale features
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_val_scaled = scaler.transform(X_val)
    X_test_scaled = scaler.transform(X_test)

    # Convert to numpy arrays
    y_train = y_train.values
    y_val = y_val.values
    y_test = y_test.values

    # Create data dictionary
    data = {
        'train': {
            'features': {
                'all_features': X_train_scaled
            },
            'targets': y_train
        },
        'val': {
            'features': {
                'all_features': X_val_scaled
            },
            'targets': y_val
        },
        'test': {
            'features': {
                'all_features': X_test_scaled
            },
            'targets': y_test
        },
        'feature_names': X.columns.tolist(),
        'scaler': scaler
    }

    logger.info(f"Prepared data with {len(y_train)} training, {len(y_val)} validation, "
               f"and {len(y_test)} test samples")

    return data

def train_baseline_model(data, output_dir):
    """
    Train a baseline Random Forest model.

    Args:
        data: Dictionary with train, val, and test data
        output_dir: Directory to save the model

    Returns:
        Trained Random Forest model
    """
    logger.info("Training baseline Random Forest model")

    # Determine feature set to use
    feature_set = 'selected' if 'selected' in data['train']['features'] else 'all_features'
    logger.info(f"Using feature set for baseline model: {feature_set}")

    # Create model
    model = RandomForestRegressor(
        n_estimators=200,
        max_depth=8,
        min_samples_split=10,
        min_samples_leaf=5,
        max_features='sqrt',
        random_state=42,
        n_jobs=-1
    )

    # Train model
    model.fit(data['train']['features'][feature_set], data['train']['targets'])

    # Evaluate model
    train_pred = model.predict(data['train']['features'][feature_set])
    val_pred = model.predict(data['val']['features'][feature_set])
    test_pred = model.predict(data['test']['features'][feature_set])

    # Calculate metrics
    train_r2 = r2_score(data['train']['targets'], train_pred)
    val_r2 = r2_score(data['val']['targets'], val_pred)
    test_r2 = r2_score(data['test']['targets'], test_pred)

    train_rmse = np.sqrt(mean_squared_error(data['train']['targets'], train_pred))
    val_rmse = np.sqrt(mean_squared_error(data['val']['targets'], val_pred))
    test_rmse = np.sqrt(mean_squared_error(data['test']['targets'], test_pred))

    logger.info(f"Baseline model metrics:")
    logger.info(f"  Train R²: {train_r2:.4f}, RMSE: {train_rmse:.4f}")
    logger.info(f"  Val R²: {val_r2:.4f}, RMSE: {val_rmse:.4f}")
    logger.info(f"  Test R²: {test_r2:.4f}, RMSE: {test_rmse:.4f}")

    # Save model
    output_path = Path(output_dir) / 'baseline_model.joblib'
    import joblib
    joblib.dump(model, output_path)
    logger.info(f"Saved baseline model to {output_path}")

    # Save metrics
    metrics = {
        'train_r2': train_r2,
        'val_r2': val_r2,
        'test_r2': test_r2,
        'train_rmse': train_rmse,
        'val_rmse': val_rmse,
        'test_rmse': test_rmse
    }

    metrics_path = Path(output_dir) / 'baseline_metrics.json'
    with open(metrics_path, 'w') as f:
        json.dump(metrics, f, indent=2)

    return model

def select_features(data, n_features=50):
    """
    Select top features using Random Forest feature importance.

    Args:
        data: Dictionary with train, val, and test data
        n_features: Number of features to select

    Returns:
        Updated data dictionary with selected features
    """
    logger.info(f"Selecting top {n_features} features")

    # Create model
    model = RandomForestRegressor(
        n_estimators=100,
        max_depth=10,
        random_state=42,
        n_jobs=-1
    )

    # Train model
    model.fit(data['train']['features']['all_features'], data['train']['targets'])

    # Get feature importance
    importances = model.feature_importances_
    indices = np.argsort(importances)[::-1]

    # Select top features
    top_indices = indices[:n_features]
    top_features = [data['feature_names'][i] for i in top_indices]

    # Create new feature arrays
    X_train_selected = data['train']['features']['all_features'][:, top_indices]
    X_val_selected = data['val']['features']['all_features'][:, top_indices]
    X_test_selected = data['test']['features']['all_features'][:, top_indices]

    # Add selected features to data dictionary
    data['train']['features']['selected'] = X_train_selected
    data['val']['features']['selected'] = X_val_selected
    data['test']['features']['selected'] = X_test_selected
    data['selected_features'] = top_features
    data['selected_indices'] = top_indices.tolist()

    logger.info(f"Selected {len(top_features)} features")

    return data

def train_rlvr_model(data, args, baseline_model=None):
    """
    Train an RLVR model for growth rate prediction.

    Args:
        data: Dictionary with train, val, and test data
        args: Command line arguments
        baseline_model: Pre-trained baseline model (optional)

    Returns:
        Trained RLVR agent and trainer
    """
    logger.info("Training RLVR model")

    # Set random seed
    np.random.seed(args.seed)
    torch.manual_seed(args.seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(args.seed)

    # Determine feature set to use
    feature_set = 'selected' if 'selected' in data['train']['features'] else 'all_features'
    logger.info(f"Using feature set: {feature_set}")

    # Get feature dimensions
    feature_dims = {
        'all_features': data['train']['features'][feature_set].shape[1]
    }

    # Create RLVR agent
    if args.use_enhanced_agent and HAS_ENHANCED_AGENT:
        logger.info("Using enhanced RLVR agent with improved architecture")
        agent = EnhancedRLVRAgent(
            feature_dims=feature_dims,
            hidden_dim=args.hidden_dim,
            num_heads=args.num_heads,
            num_layers=args.num_layers,
            dropout=args.dropout,
            use_layer_norm=args.use_layer_norm,
            use_residual=args.use_residual,
            use_value_head=args.use_value_head,
            activation=args.activation,
            use_feature_interaction=args.use_feature_interaction,
            interaction_layers=args.interaction_layers,
            use_batch_norm=args.use_batch_norm
        )
    else:
        if args.use_enhanced_agent and not HAS_ENHANCED_AGENT:
            logger.warning("Enhanced RLVR agent not available. Using standard RLVR agent instead.")
        agent = RLVRAgent(
            feature_dims=feature_dims,
            hidden_dim=args.hidden_dim,
            num_heads=args.num_heads,
            num_layers=args.num_layers,
            dropout=args.dropout,
            use_layer_norm=args.use_layer_norm,
            use_residual=args.use_residual,
            use_value_head=args.use_value_head,
            activation=args.activation
        )

    # Create reward function
    reward_fn = VerifiableRewardFunction(
        baseline_model=baseline_model,
        alpha=args.alpha,
        beta=args.beta,
        gamma=args.gamma,
        accuracy_scale=args.accuracy_scale,
        use_r2_component=not args.no_r2_component,
        use_improvement_component=not args.no_improvement_component
    )

    # Create trainer
    trainer = RLVRTrainer(
        agent=agent,
        reward_fn=reward_fn,
        lr=args.lr,
        weight_decay=args.weight_decay,
        gamma=0.99,
        entropy_coef=args.entropy_coef,
        value_loss_coef=args.value_loss_coef,
        max_grad_norm=args.max_grad_norm,
        use_lr_scheduler=not args.no_lr_scheduler,
        use_experience_replay=not args.no_experience_replay,
        replay_buffer_size=args.replay_buffer_size,
        batch_size=args.batch_size
    )

    # Prepare training data
    train_features = {
        'all_features': data['train']['features'][feature_set]
    }
    train_targets = data['train']['targets']

    # Prepare validation data
    val_features = {
        'all_features': data['val']['features'][feature_set]
    }
    val_targets = data['val']['targets']

    # Train model
    metrics = trainer.train(
        train_features=train_features,
        train_targets=train_targets,
        val_features=val_features,
        val_targets=val_targets,
        num_epochs=args.epochs,
        batch_size=args.batch_size,
        early_stopping_patience=args.early_stopping_patience,
        verbose=True,
        log_interval=5,
        save_best_model=True
    )

    return agent, trainer, metrics

def evaluate_model(agent, data, metrics_dir):
    """
    Evaluate the trained RLVR model.

    Args:
        agent: Trained RLVR agent
        data: Dictionary with train, val, and test data
        metrics_dir: Directory to save metrics and plots

    Returns:
        Dictionary of evaluation metrics
    """
    logger.info("Evaluating RLVR model")

    # Determine feature set to use
    feature_set = 'selected' if 'selected' in data['train']['features'] else 'all_features'

    # Get the device that the model is on
    device = next(agent.parameters()).device

    # Prepare test data and move to the correct device
    test_features = {
        'all_features': torch.tensor(data['test']['features'][feature_set], dtype=torch.float32).to(device)
    }
    test_targets = data['test']['targets']

    # Make predictions
    agent.eval()
    with torch.no_grad():
        predictions = agent.predict(test_features)

    # Move predictions back to CPU for metric calculation
    test_pred = predictions['growth_rate']
    if isinstance(test_pred, torch.Tensor):
        test_pred = test_pred.cpu().numpy()

    # Calculate metrics
    test_r2 = r2_score(test_targets, test_pred)
    test_rmse = np.sqrt(mean_squared_error(test_targets, test_pred))
    test_mae = mean_absolute_error(test_targets, test_pred)

    logger.info(f"Test metrics:")
    logger.info(f"  R²: {test_r2:.4f}")
    logger.info(f"  RMSE: {test_rmse:.4f}")
    logger.info(f"  MAE: {test_mae:.4f}")

    # Create metrics dictionary
    metrics = {
        'test_r2': test_r2,
        'test_rmse': test_rmse,
        'test_mae': test_mae
    }

    # Save metrics
    metrics_path = Path(metrics_dir) / 'test_metrics.json'
    with open(metrics_path, 'w') as f:
        json.dump(metrics, f, indent=2)

    # Plot predictions vs. targets
    plt.figure(figsize=(10, 8))
    plt.scatter(test_targets, test_pred, alpha=0.5)
    plt.plot([min(test_targets), max(test_targets)], [min(test_targets), max(test_targets)], 'r--')
    plt.xlabel('True Growth Rate')
    plt.ylabel('Predicted Growth Rate')
    plt.title(f'RLVR Model Predictions (R² = {test_r2:.4f}, RMSE = {test_rmse:.4f})')
    plt.grid(True, alpha=0.3)
    plt.tight_layout()

    # Save plot
    plot_path = Path(metrics_dir) / 'predictions.png'
    plt.savefig(plot_path, dpi=300)
    plt.close()

    # Plot feature importance if available
    feature_importance = agent.get_feature_importance()
    if feature_importance and len(feature_importance) > 0:
        # Convert to DataFrame
        if 'selected_features' in data:
            # Use selected feature names
            feature_names = data['selected_features']
        else:
            # Use all feature names
            feature_names = data['feature_names']

        # Create DataFrame with proper length matching
        importance_values = [feature_importance.get('all_features', 0.0)] * len(feature_names)
        importance_df = pd.DataFrame({
            'feature': feature_names,
            'importance': importance_values
        })

        # Sort by importance
        importance_df = importance_df.sort_values('importance', ascending=False)

        # Plot top 20 features
        plt.figure(figsize=(12, 8))
        sns.barplot(x='importance', y='feature', data=importance_df.head(20))
        plt.title('Top 20 Feature Importance')
        plt.tight_layout()

        # Save plot
        plot_path = Path(metrics_dir) / 'feature_importance.png'
        plt.savefig(plot_path, dpi=300)
        plt.close()

        # Save feature importance
        importance_path = Path(metrics_dir) / 'feature_importance.tsv'
        importance_df.to_csv(importance_path, sep='\t', index=False)

    return metrics

def plot_training_metrics(metrics, metrics_dir):
    """
    Plot training metrics.

    Args:
        metrics: Dictionary of training metrics
        metrics_dir: Directory to save plots
    """
    logger.info("Plotting training metrics")

    # Plot training and validation loss
    plt.figure(figsize=(12, 8))
    plt.plot(metrics['train_loss'], label='Train Loss')
    if 'val_loss' in metrics and len(metrics['val_loss']) > 0:
        plt.plot(metrics['val_loss'], label='Val Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('Training and Validation Loss')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.tight_layout()

    # Save plot
    plot_path = Path(metrics_dir) / 'loss.png'
    plt.savefig(plot_path, dpi=300)
    plt.close()

    # Plot training and validation reward
    plt.figure(figsize=(12, 8))
    plt.plot(metrics['train_reward'], label='Train Reward')
    if 'val_reward' in metrics and len(metrics['val_reward']) > 0:
        plt.plot(metrics['val_reward'], label='Val Reward')
    plt.xlabel('Epoch')
    plt.ylabel('Reward')
    plt.title('Training and Validation Reward')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.tight_layout()

    # Save plot
    plot_path = Path(metrics_dir) / 'reward.png'
    plt.savefig(plot_path, dpi=300)
    plt.close()

    # Plot validation R² if available
    if 'val_r2' in metrics and len(metrics['val_r2']) > 0:
        plt.figure(figsize=(12, 8))
        plt.plot(metrics['val_r2'])
        plt.xlabel('Epoch')
        plt.ylabel('R²')
        plt.title('Validation R²')
        plt.grid(True, alpha=0.3)
        plt.tight_layout()

        # Save plot
        plot_path = Path(metrics_dir) / 'r2.png'
        plt.savefig(plot_path, dpi=300)
        plt.close()

    # Plot learning rate
    if 'learning_rate' in metrics and len(metrics['learning_rate']) > 0:
        plt.figure(figsize=(12, 8))
        plt.plot(metrics['learning_rate'])
        plt.xlabel('Epoch')
        plt.ylabel('Learning Rate')
        plt.title('Learning Rate')
        plt.grid(True, alpha=0.3)
        plt.tight_layout()

        # Save plot
        plot_path = Path(metrics_dir) / 'learning_rate.png'
        plt.savefig(plot_path, dpi=300)
        plt.close()

def save_model_and_config(_, trainer, args, output_dir):
    """
    Save the trained model and configuration.

    Args:
        _: Trained RLVR agent (not used directly as it's saved via trainer)
        trainer: RLVR trainer
        args: Command line arguments
        output_dir: Directory to save model and configuration
    """
    logger.info("Saving model and configuration")

    # Create output directory
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)

    # Save model
    model_path = output_dir / 'rlvr_model.pt'
    trainer.save_model(str(model_path))

    # Save configuration
    config = vars(args)
    config_path = output_dir / 'config.json'
    with open(config_path, 'w') as f:
        json.dump(config, f, indent=2)

    logger.info(f"Model saved to {model_path}")
    logger.info(f"Configuration saved to {config_path}")

def main():
    """Main function."""
    # Parse command line arguments
    args = parse_args()

    # Create output directories
    output_dir = Path(args.output_dir)
    metrics_dir = Path(args.metrics_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    metrics_dir.mkdir(parents=True, exist_ok=True)

    # Load data
    data_df = load_data(args.feature_file, args.metadata)

    # Prepare data
    data = prepare_data(
        data_df,
        target='growth_rate',
        test_size=args.test_size,
        val_size=args.val_size,
        seed=args.seed
    )

    # Feature selection if enabled
    if args.feature_selection:
        data = select_features(data, n_features=args.n_features)

    # Load or train baseline model
    if args.baseline_model:
        logger.info(f"Loading baseline model from {args.baseline_model}")
        import joblib
        baseline_model = joblib.load(args.baseline_model)
    else:
        # Train baseline model after feature selection
        baseline_model = train_baseline_model(data, args.output_dir)

    # Train RLVR model
    agent, trainer, training_metrics = train_rlvr_model(data, args, baseline_model)

    # Evaluate model
    _ = evaluate_model(agent, data, args.metrics_dir)

    # Plot training metrics
    plot_training_metrics(training_metrics, args.metrics_dir)

    # Save model and configuration
    save_model_and_config(agent, trainer, args, args.output_dir)

    logger.info("Training complete")

if __name__ == '__main__':
    main()
