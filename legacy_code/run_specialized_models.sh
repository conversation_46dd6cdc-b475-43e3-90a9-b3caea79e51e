#!/bin/bash

# This script trains specialized hybrid models for growth rate and optimal temperature prediction

echo "Training specialized hybrid models with advanced tree-based components..."

# Create output directory
mkdir -p models/specialized_hybrid_optimized

# Run training with optimized parameters
python specialized_hybrid_models.py \
    --feature_file ./training_data/combined_features.tsv \
    --metadata_file ./training_data/metadata.tsv \
    --n_features 300 \
    --batch_size 64 \
    --epochs 50 \
    --lr 0.001 \
    --rf_trees 1000 \
    --hidden_dim 192 \
    --dropout 0.3 \
    --weight_decay 1e-5 \
    --cross_target_weight 0.3 \
    --enable_stacking \
    --output_dir ./models/specialized_hybrid_optimized

echo "Training completed."
echo "Running model comparison..."

# Create comparison directory
mkdir -p comparison_results

# Run comparison
python compare_specialized_vs_joint.py \
    --joint_model_dir ./models/optimized_tabular \
    --specialized_model_dir ./models/specialized_hybrid_optimized \
    --output_dir ./comparison_results

echo "Comparison completed. Results saved to comparison_results/"

# Check if we should run with reduced parameters for quick testing
if [ "$1" == "--test" ]; then
    echo "Running quick test with reduced parameters..."
    
    # For test runs, set SWA start higher than epochs to effectively disable it
    python specialized_hybrid_models.py \
        --feature_file ./training_data/combined_features.tsv \
        --metadata_file ./training_data/metadata.tsv \
        --n_features 200 \
        --batch_size 64 \
        --epochs 10 \
        --lr 0.001 \
        --rf_trees 200 \
        --hidden_dim 96 \
        --dropout 0.2 \
        --weight_decay 1e-5 \
        --cross_target_weight 0.3 \
        --enable_stacking \
        --output_dir ./models/specialized_hybrid_test
        
    echo "Test run completed. Results saved to ./models/specialized_hybrid_test"
fi 