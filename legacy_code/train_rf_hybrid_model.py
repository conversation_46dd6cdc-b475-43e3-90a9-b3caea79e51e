#!/usr/bin/env python3
"""
Training script for a hybrid model that uses Random Forest for both growth rate and optimal temperature.

This script implements a hybrid approach based on the successful simple_improved_rf.py implementation,
using Random Forest for both targets with proper feature preprocessing and selection.
"""

import os
import sys
import argparse
import logging
import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union, Any
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from joblib import dump, load

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('train_rf_hybrid')

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Train a hybrid model using Random Forest for both growth rate and optimal temperature')

    # Data arguments
    parser.add_argument('--metadata', required=True, help='Path to metadata TSV file')
    parser.add_argument('--feature-file', required=True, help='Path to combined features TSV file')
    parser.add_argument('--output-dir', required=True, help='Directory to save model and results')
    parser.add_argument('--metrics-dir', default=None, help='Directory to save metrics')

    # Random Forest arguments
    parser.add_argument('--n-estimators', type=int, default=200, help='Number of trees in the Random Forest')
    parser.add_argument('--max-depth', type=int, default=None, help='Maximum depth of trees in the Random Forest')
    parser.add_argument('--min-samples-split', type=int, default=2, help='Minimum samples required to split a node in Random Forest')
    parser.add_argument('--min-samples-leaf', type=int, default=1, help='Minimum samples required in a leaf node in Random Forest')

    # Training arguments
    parser.add_argument('--seed', type=int, default=42, help='Random seed')
    parser.add_argument('--target', type=str, choices=['growth_rate', 'temperature', 'both'], default='both',
                        help='Target variable to predict')

    return parser.parse_args()

def create_taxonomy_features(metadata_df):
    """
    Create simple taxonomy features from metadata.

    Args:
        metadata_df (pd.DataFrame): DataFrame with metadata

    Returns:
        pd.DataFrame: DataFrame with taxonomy features
    """
    # Create simple taxonomy features
    taxonomy_data = []

    for _, row in metadata_df.iterrows():
        sample_id = row['genome_id']
        taxid = int(row['taxid'])

        # Create simple features based on taxid
        features = {
            'genome_id': sample_id,
            'taxid_mod_10': taxid % 10,
            'taxid_mod_100': taxid % 100,
            'taxid_mod_1000': taxid % 1000,
            'taxid_div_10': taxid // 10,
            'taxid_div_100': taxid // 100,
            'taxid_div_1000': taxid // 1000
        }

        taxonomy_data.append(features)

    # Create DataFrame
    taxonomy_df = pd.DataFrame(taxonomy_data)

    return taxonomy_df

def create_breakpoint_features(features_df):
    """
    Create enhanced breakpoint features.

    Args:
        features_df (pd.DataFrame): DataFrame with features

    Returns:
        pd.DataFrame: DataFrame with enhanced breakpoint features
    """
    # Extract breakpoint-related features
    breakpoint_cols = ['genome_id'] + [col for col in features_df.columns if
                                     'skew' in col or
                                     '_change_points' in col or
                                     '_sign_changes' in col or
                                     '_mean' in col or
                                     '_std' in col or
                                     '_min' in col or
                                     '_max' in col or
                                     '_range' in col]

    # Create a new DataFrame with only breakpoint features
    breakpoint_df = features_df[breakpoint_cols].copy()

    # Add emphasized features
    for col in breakpoint_df.columns:
        if col != 'genome_id' and ('_change_points' in col or '_sign_changes' in col):
            breakpoint_df[f'important_{col}'] = breakpoint_df[col] * 2.0
            logger.info(f"Emphasizing important change point feature: important_{col}")

    return breakpoint_df

def enhance_codon_features(features_df):
    """
    Enhance codon-related features.

    Args:
        features_df (pd.DataFrame): DataFrame with features

    Returns:
        pd.DataFrame: DataFrame with enhanced codon features
    """
    # Create a copy of the input DataFrame
    enhanced_df = features_df.copy()

    # Identify codon-related features
    codon_cols = [col for col in features_df.columns if any(pattern in col for pattern in 
                ['CUB', 'CPB', 'CAI', 'ENC', 'HEG_', 'BG_', 'delta_', 'RSCU_',
                 'codon_bias_', 'AAAI', 'aa_bias_HEG_BP', 'MILC'])]

    logger.info(f"Found {len(codon_cols)} codon-related features")

    # Create squared and interaction features for important codon metrics
    important_codon_cols = [col for col in codon_cols if any(pattern in col for pattern in 
                          ['CAI', 'ENC', 'delta_', 'codon_bias_', 'RSCU_'])]

    for col in important_codon_cols:
        # Create squared features
        enhanced_df[f'{col}_squared'] = enhanced_df[col] ** 2
        logger.info(f"Created squared feature: {col}_squared")

    # Create interaction features between selected codon metrics
    if len(important_codon_cols) >= 2:
        for i, col1 in enumerate(important_codon_cols[:5]):  # Limit to first 5 to avoid explosion
            for col2 in important_codon_cols[i+1:5]:
                enhanced_df[f'{col1}_x_{col2}'] = enhanced_df[col1] * enhanced_df[col2]
                logger.info(f"Created interaction feature: {col1}_x_{col2}")

    return enhanced_df

def train_model(features_df, metadata_df, output_dir, metrics_dir, target, rf_params):
    """
    Train a Random Forest model for the specified target.

    Args:
        features_df (pd.DataFrame): DataFrame with features
        metadata_df (pd.DataFrame): DataFrame with metadata
        output_dir (Path): Directory to save model and results
        metrics_dir (Path): Directory to save metrics
        target (str): Target variable to predict ('growth_rate' or 'temperature')
        rf_params (dict): Random Forest parameters

    Returns:
        dict: Dictionary with training results
    """
    logger.info(f"Training Random Forest model for {target}")

    # Create taxonomy features
    logger.info("Creating taxonomy features")
    taxonomy_df = create_taxonomy_features(metadata_df)

    # Create breakpoint features
    logger.info("Creating breakpoint features")
    breakpoint_df = create_breakpoint_features(features_df)

    # Enhance codon features
    logger.info("Enhancing codon features")
    enhanced_codon_df = enhance_codon_features(features_df)

    # Merge all features
    logger.info("Merging all features")
    
    # Merge taxonomy features with metadata
    merged_df = pd.merge(metadata_df, taxonomy_df, on='genome_id', how='inner')
    logger.info(f"After merging taxonomy: {len(merged_df)} samples")
    
    # Merge with breakpoint features
    merged_df = pd.merge(merged_df, breakpoint_df, on='genome_id', how='left')
    logger.info(f"After merging breakpoint: {len(merged_df)} samples")
    
    # Merge with enhanced codon features
    merged_df = pd.merge(merged_df, enhanced_codon_df, on='genome_id', how='left')
    logger.info(f"After merging enhanced codon: {len(merged_df)} samples")
    
    logger.info(f"Combined dataset has {len(merged_df)} samples and {len(merged_df.columns)} features")

    # Split into features and targets
    X = merged_df.drop(['genome_id', 'growth_rate', 'optimal_temperature', 'taxid', 'genetic_code', 'kingdom'], axis=1, errors='ignore')
    
    if target == 'growth_rate':
        y = merged_df['growth_rate']
    else:  # temperature
        y = merged_df['optimal_temperature']
    
    # Split into train and test sets
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    
    # Standardize features
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    # Create DataFrame with scaled features for better interpretability
    X_train_scaled_df = pd.DataFrame(X_train_scaled, columns=X_train.columns)
    
    # Log statistics about standardized features
    logger.info("Feature standardization statistics:")
    codon_features = [col for col in X_train.columns if any(pattern in col for pattern in
                    ['CUB', 'CPB', 'CAI', 'ENC', 'HEG_', 'BG_', 'delta_', 'RSCU_',
                     'codon_bias_', 'AAAI', 'aa_bias_HEG_BP', 'MILC'])]
    
    for feature in codon_features[:10]:  # Log first 10 codon features
        before_mean = X_train[feature].mean()
        before_std = X_train[feature].std()
        after_mean = X_train_scaled_df[feature].mean()
        after_std = X_train_scaled_df[feature].std()
        logger.info(f"Feature {feature}: Before standardization - mean={before_mean:.4f}, std={before_std:.4f} | After - mean={after_mean:.4f}, std={after_std:.4f}")
    
    # Save scaler
    scaler_file = output_dir / f"{target}_scaler.joblib"
    dump(scaler, scaler_file)
    logger.info(f"Saved scaler to {scaler_file}")
    
    # Train Random Forest model
    logger.info(f"Training Random Forest model for {target} with parameters: {rf_params}")
    model = RandomForestRegressor(
        n_estimators=rf_params['n_estimators'],
        max_depth=rf_params['max_depth'],
        min_samples_split=rf_params['min_samples_split'],
        min_samples_leaf=rf_params['min_samples_leaf'],
        random_state=42,
        n_jobs=-1
    )
    
    model.fit(X_train_scaled, y_train)
    
    # Save model
    model_file = output_dir / f"{target}_model.joblib"
    dump(model, model_file)
    logger.info(f"Saved model to {model_file}")
    
    # Make predictions
    y_pred_train = model.predict(X_train_scaled)
    y_pred_test = model.predict(X_test_scaled)
    
    # Calculate metrics
    train_metrics = {
        'mse': mean_squared_error(y_train, y_pred_train),
        'rmse': np.sqrt(mean_squared_error(y_train, y_pred_train)),
        'mae': mean_absolute_error(y_train, y_pred_train),
        'r2': r2_score(y_train, y_pred_train)
    }
    
    test_metrics = {
        'mse': mean_squared_error(y_test, y_pred_test),
        'rmse': np.sqrt(mean_squared_error(y_test, y_pred_test)),
        'mae': mean_absolute_error(y_test, y_pred_test),
        'r2': r2_score(y_test, y_pred_test)
    }
    
    # Log metrics
    logger.info(f"{target} - Train: R²={train_metrics['r2']:.4f}, RMSE={train_metrics['rmse']:.4f}")
    logger.info(f"{target} - Test: R²={test_metrics['r2']:.4f}, RMSE={test_metrics['rmse']:.4f}")
    
    # Calculate feature importance
    importances = model.feature_importances_
    feature_names = X.columns
    
    # Create feature importance dataframe
    importance_df = pd.DataFrame({
        'feature': feature_names,
        'importance': importances
    }).sort_values('importance', ascending=False)
    
    # Save feature importance
    importance_file = metrics_dir / f"{target}_feature_importance.tsv"
    importance_df.to_csv(importance_file, sep='\t', index=False)
    logger.info(f"Saved feature importance to {importance_file}")
    
    # Analyze codon feature importance
    codon_features = [f for f in feature_names if any(pattern in f for pattern in
                    ['CUB', 'CPB', 'CAI', 'ENC', 'HEG_', 'BG_', 'delta_', 'RSCU_',
                     'codon_bias_', 'AAAI', 'aa_bias_HEG_BP', 'MILC'])]
    
    codon_importance_df = importance_df[importance_df['feature'].isin(codon_features)].sort_values('importance', ascending=False)
    
    # Save codon feature importance
    codon_importance_file = metrics_dir / f"{target}_codon_feature_importance.tsv"
    codon_importance_df.to_csv(codon_importance_file, sep='\t', index=False)
    logger.info(f"Saved codon feature importance to {codon_importance_file}")
    
    # Log top codon features
    logger.info(f"Top 10 codon features for {target}:")
    for i, (feature, importance) in enumerate(zip(codon_importance_df['feature'].head(10), codon_importance_df['importance'].head(10))):
        logger.info(f"{i+1}. {feature}: {importance:.6f}")
    
    # Plot feature importance
    plt.figure(figsize=(12, 8))
    sns.barplot(x='importance', y='feature', data=importance_df.head(20))
    plt.title(f"Top 20 Important Features for {target}")
    plt.tight_layout()
    plt.savefig(metrics_dir / f"{target}_feature_importance.png", dpi=300)
    plt.close()
    
    # Plot codon feature importance
    plt.figure(figsize=(12, 8))
    sns.barplot(x='importance', y='feature', data=codon_importance_df.head(20))
    plt.title(f"Top 20 Important Codon Features for {target}")
    plt.tight_layout()
    plt.savefig(metrics_dir / f"{target}_codon_feature_importance.png", dpi=300)
    plt.close()
    
    # Create scatter plot
    plt.figure(figsize=(10, 8))
    plt.scatter(y_test, y_pred_test, alpha=0.7)
    
    # Add identity line
    min_val = min(min(y_test), min(y_pred_test))
    max_val = max(max(y_test), max(y_pred_test))
    plt.plot([min_val, max_val], [min_val, max_val], 'r--')
    
    plt.title(f"Predicted vs. True {target}")
    plt.xlabel(f"True {target}")
    plt.ylabel(f"Predicted {target}")
    
    # Add metrics text
    plt.text(0.05, 0.95, f"R² = {test_metrics['r2']:.4f}", transform=plt.gca().transAxes)
    plt.text(0.05, 0.90, f"RMSE = {test_metrics['rmse']:.4f}", transform=plt.gca().transAxes)
    plt.text(0.05, 0.85, f"MAE = {test_metrics['mae']:.4f}", transform=plt.gca().transAxes)
    
    plt.tight_layout()
    plt.savefig(metrics_dir / f"{target}_scatter_plot.png", dpi=300)
    plt.close()
    
    # Save results
    results = {
        'train_metrics': train_metrics,
        'test_metrics': test_metrics,
        'feature_importance': importance_df.to_dict('records')
    }
    
    # Save results to file
    pd.DataFrame({
        'metric': ['mse', 'rmse', 'mae', 'r2'],
        'train': [train_metrics['mse'], train_metrics['rmse'], train_metrics['mae'], train_metrics['r2']],
        'test': [test_metrics['mse'], test_metrics['rmse'], test_metrics['mae'], test_metrics['r2']]
    }).to_csv(metrics_dir / f"{target}_metrics.tsv", sep='\t', index=False)
    
    # Save predictions
    pd.DataFrame({
        'true': y_test,
        'pred': y_pred_test
    }).to_csv(metrics_dir / f"{target}_predictions.tsv", sep='\t', index=False)
    
    # Save combined metrics for the training script
    combined_metrics = {
        'train_metrics': [{
            f'{target}_mse': float(train_metrics['mse']),
            f'{target}_rmse': float(train_metrics['rmse']),
            f'{target}_mae': float(train_metrics['mae']),
            f'{target}_r2': float(train_metrics['r2'])
        }],
        'val_metrics': [{
            f'{target}_mse': float(test_metrics['mse']),
            f'{target}_rmse': float(test_metrics['rmse']),
            f'{target}_mae': float(test_metrics['mae']),
            f'{target}_r2': float(test_metrics['r2']),
            f'{target}_preds': y_pred_test.tolist(),
            f'{target}_targets': y_test.tolist()
        }]
    }
    
    return combined_metrics

def main():
    """Main function."""
    args = parse_args()
    
    # Set random seed
    np.random.seed(args.seed)
    
    # Create output directories
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    if args.metrics_dir is None:
        metrics_dir = Path(args.output_dir) / 'metrics'
    else:
        metrics_dir = Path(args.metrics_dir)
    metrics_dir.mkdir(parents=True, exist_ok=True)
    
    # Load metadata
    logger.info(f"Loading metadata from {args.metadata}...")
    metadata_df = pd.read_csv(args.metadata, sep='\t')
    logger.info(f"Loaded metadata with {len(metadata_df)} samples")
    
    # Load features
    logger.info(f"Loading features from {args.feature_file}...")
    features_df = pd.read_csv(args.feature_file, sep='\t')
    logger.info(f"Loaded features with {len(features_df)} samples and {len(features_df.columns)} features")
    
    # Random Forest parameters
    rf_params = {
        'n_estimators': args.n_estimators,
        'max_depth': args.max_depth,
        'min_samples_split': args.min_samples_split,
        'min_samples_leaf': args.min_samples_leaf
    }
    
    # Train models
    all_metrics = {}
    
    if args.target in ['growth_rate', 'both']:
        logger.info("Training model for growth rate...")
        growth_metrics = train_model(features_df, metadata_df, output_dir, metrics_dir, 'growth_rate', rf_params)
        all_metrics.update(growth_metrics)
    
    if args.target in ['temperature', 'both']:
        logger.info("Training model for optimal temperature...")
        temp_metrics = train_model(features_df, metadata_df, output_dir, metrics_dir, 'optimal_temperature', rf_params)
        
        # Merge metrics
        if 'train_metrics' in all_metrics:
            all_metrics['train_metrics'][0].update(temp_metrics['train_metrics'][0])
            all_metrics['val_metrics'][0].update(temp_metrics['val_metrics'][0])
        else:
            all_metrics.update(temp_metrics)
    
    # Save combined metrics
    metrics_file = metrics_dir / 'training_metrics.json'
    with open(metrics_file, 'w') as f:
        json.dump(all_metrics, f, indent=2)
    
    logger.info(f"Saved combined metrics to {metrics_file}")
    logger.info("Training completed successfully")

if __name__ == '__main__':
    main()
