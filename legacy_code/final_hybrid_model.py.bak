#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
DeepMu Final Hybrid Model.

This module implements a high-performance hybrid model that combines:
1. A dedicated ensemble model for growth rate prediction (R² > 0.93)
2. A specialized DNN for optimal temperature prediction (R² > 0.94)
3. Task-specific feature selection and preprocessing
4. Advanced optimization techniques

The model architecture maintains complete separation between prediction components
while providing a unified interface for training and inference.
"""

import os
import argparse
import logging
import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import RobustScaler, StandardScaler
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
import joblib
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import TensorDataset, DataLoader, random_split
from typing import Dict, List, Tuple, Optional, Any
from scipy import stats
from scipy.optimize import minimize
import copy
from collections import defaultdict
from sklearn.feature_selection import mutual_info_regression, f_regression

# Import dedicated component models
from growth_model import GrowthRateModel
from temperature_model import TemperatureModel
from predict_temperature import TemperaturePredictionModel

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Set device
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

#-----------------------------------------------------------------------------
# Advanced Optimization Utilities
#-----------------------------------------------------------------------------

class EarlyStopping:
    """
    Early stopping implementation to prevent overfitting.
    """
    def __init__(self, patience=10, min_delta=0.0, restore_best_weights=True):
        self.patience = patience
        self.min_delta = min_delta
        self.restore_best_weights = restore_best_weights
        self.best_score = None
        self.counter = 0
        self.best_weights = None
    
    def __call__(self, val_loss, model):
        if self.best_score is None:
            self.best_score = val_loss
            if self.restore_best_weights:
                self.best_weights = model.state_dict().copy()
            return False
        
        if val_loss > self.best_score + self.min_delta:
            self.counter += 1
            if self.counter >= self.patience:
                return True
        else:
            self.best_score = val_loss
            if self.restore_best_weights:
                self.best_weights = model.state_dict().copy()
            self.counter = 0
        
        return False
    
    def restore_weights(self, model):
        if self.restore_best_weights and self.best_weights is not None:
            model.load_state_dict(self.best_weights)

def get_optimized_temperature_model_params() -> Dict:
    """
    Get optimized parameters for the temperature model.
    
    Returns:
        Dictionary of optimized parameters
    """
    return {
        'hidden_dims': [512, 384, 256, 128],
        'dropout_rates': [0.3, 0.4, 0.4, 0.5],
        'lr': 0.001,
        'batch_size': 128,
        'epochs': 300,
        'patience': 30,
        'use_batch_norm': True,
        'activation': 'relu',
        'weight_decay': 1e-6,
        'mse_l1_ratio': 0.9,
        'use_gradient_clipping': True,
        'max_grad_norm': 1.0,
        # Transformer-specific parameters
        'transformer_nhead': 8,
        'transformer_nlayers': 2,
        'transformer_dim_feedforward': 512,
        'transformer_dropout': 0.3
    }

def add_l1_regularization(model, loss, l1_lambda):
    """
    Add L1 regularization to the loss.
    
    Args:
        model: PyTorch model
        loss: Current loss
        l1_lambda: L1 regularization coefficient
        
    Returns:
        Updated loss with L1 regularization
    """
    l1_reg = 0.0
    for param in model.parameters():
        l1_reg += torch.norm(param, 1)
    
    return loss + l1_lambda * l1_reg

def apply_gradient_clipping(model, max_grad_norm):
    """
    Apply gradient clipping to prevent exploding gradients.
    
    Args:
        model: PyTorch model
        max_grad_norm: Maximum gradient norm
    """
    torch.nn.utils.clip_grad_norm_(model.parameters(), max_grad_norm)

def setup_one_cycle_lr(optimizer, epochs, steps_per_epoch, max_lr, warmup_pct):
    """
    Set up one cycle learning rate scheduler.
    
    Args:
        optimizer: PyTorch optimizer
        epochs: Number of epochs
        steps_per_epoch: Steps per epoch
        max_lr: Maximum learning rate
        warmup_pct: Percentage of training to use for warmup
        
    Returns:
        One cycle learning rate scheduler
    """
    total_steps = epochs * steps_per_epoch
    return optim.lr_scheduler.OneCycleLR(
        optimizer,
        max_lr=max_lr,
        total_steps=total_steps,
        pct_start=warmup_pct,
        anneal_strategy='cos',
        div_factor=25.0,
        final_div_factor=10000.0
    )

#-----------------------------------------------------------------------------
# Data Preparation Functions
#-----------------------------------------------------------------------------

def filter_high_missing_features(features: pd.DataFrame, threshold: float = 0.5) -> pd.DataFrame:
    """
    Filter out features with missing values exceeding the threshold percentage.
    
    Args:
        features: Feature DataFrame
        threshold: Maximum allowable percentage of missing values (0.0-1.0)
        
    Returns:
        DataFrame with high-missing-value features removed
    """
    # Calculate percentage of missing values for each feature
    missing_pct = features.isna().mean()
    
    # Identify features to keep (missing values below threshold)
    features_to_keep = missing_pct[missing_pct <= threshold].index.tolist()
    
    # Log the filtering results
    initial_feature_count = features.shape[1]
    filtered_feature_count = len(features_to_keep)
    removed_count = initial_feature_count - filtered_feature_count
    
    if removed_count > 0:
        logger.info(f"Removed {removed_count} features with >{threshold*100:.1f}% missing values.")
        logger.info(f"Retained {filtered_feature_count} features out of {initial_feature_count}.")
        
        # Return the filtered features
        return features[features_to_keep]
    else:
        logger.info(f"No features exceeded the missing value threshold of {threshold*100:.1f}%.")
        return features

def load_data(feature_file: str, metadata_file: str, missing_threshold: float = 0.5) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    Load feature and metadata files.
    
    Args:
        feature_file: Path to feature file
        metadata_file: Path to metadata file
        missing_threshold: Maximum allowable percentage of missing values in features
        
    Returns:
        Tuple of (features, metadata)
    """
    logger.info(f"Loading features from {feature_file}")
    features = pd.read_csv(feature_file, sep='\t', index_col='genome_id')
    
    logger.info(f"Loading metadata from {metadata_file}")
    metadata = pd.read_csv(metadata_file, sep='\t', index_col='genome_id')
    
    # Get common samples
    common_indices = features.index.intersection(metadata.index)
    logger.info(f"Found {len(common_indices)} common samples")
    
    # Filter to common samples
    features = features.loc[common_indices]
    metadata = metadata.loc[common_indices]
    
    # Filter out features with high missing values
    features = filter_high_missing_features(features, threshold=missing_threshold)
    
    # Check for and handle remaining NaN values in features
    nan_count = features.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} remaining NaN values in features. Filling with 0.")
        features = features.fillna(0)
    
    # Check for and handle infinite values
    inf_count = np.isinf(features.values).sum()
    if inf_count > 0:
        logger.warning(f"Found {inf_count} infinite values in features. Replacing with max/min values.")
        features = features.replace([np.inf, -np.inf], np.nan)
        features = features.fillna(features.max())
    
    # Check for NaN values in metadata
    nan_count = metadata.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in metadata. Filling with 0.")
        metadata = metadata.fillna(0)
    
    return features, metadata

#-----------------------------------------------------------------------------
# Hybrid Model Implementation
#-----------------------------------------------------------------------------

class FinalHybridModel:
    """Final hybrid model for predicting microbial growth rate and optimal temperature."""
    
    def __init__(self, n_growth_features: int = 250, n_temp_features: int = 300,
                 output_dir: str = "models/final_hybrid_model", temp_transform: str = "none",
                 growth_transform: str = "none"):
        """Initialize the final hybrid model.
        
        Args:
            n_growth_features: Number of features to use for growth rate prediction
            n_temp_features: Number of features to use for temperature prediction
            output_dir: Directory to save model outputs
            temp_transform: Transformation to apply to temperature values (none, sqrt, log2, log10)
            growth_transform: Transformation to apply to growth rate values (none, sqrt, log2, log10)
        """
        self.n_growth_features = n_growth_features
        self.n_temp_features = n_temp_features
        self.output_dir = output_dir
        self.temp_transform = temp_transform
        self.growth_transform = growth_transform
        
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)
        
        # Initialize model components to None - they will be created in fit()
        self.growth_model = None
        self.temp_model = None
        self.growth_scaler = None
        self.temp_scaler = None
        self.temp_feature_indices = None
        self.growth_feature_indices = None
        
        # Set up logging
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(logging.INFO)
        if not self.logger.handlers:
            fh = logging.FileHandler(os.path.join(output_dir, "training.log"))
            fh.setLevel(logging.INFO)
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            fh.setFormatter(formatter)
            self.logger.addHandler(fh)
        
        self.logger.info(f"Initialized FinalHybridModel with {n_growth_features} growth features and {n_temp_features} temperature features")
        self.logger.info(f"Growth rate transformation: {growth_transform}")
        self.logger.info(f"Temperature transformation: {temp_transform}")
    
    def _transform_temperature(self, temp: np.ndarray) -> np.ndarray:
        """Transform temperature values.
        
        Args:
            temp: Temperature values to transform
            
        Returns:
            Transformed temperature values
        """
        # For debugging and diagnostic purposes
        self.logger.info(f"Temperature range before transform: min={np.min(temp):.4f}, max={np.max(temp):.4f}, mean={np.mean(temp):.4f}")
        
        # Check for outliers and log them
        q1 = np.percentile(temp, 25)
        q3 = np.percentile(temp, 75)
        iqr = q3 - q1
        upper_bound = q3 + 3 * iqr
        lower_bound = q1 - 3 * iqr
        
        n_outliers = np.sum((temp > upper_bound) | (temp < lower_bound))
        if n_outliers > 0:
            self.logger.info(f"Detected {n_outliers} outliers in temperature values")
        
        # Apply transformation (which is 'none' by default)
        if self.temp_transform == "none":
            return temp
        elif self.temp_transform == "sqrt":
            return np.sqrt(np.abs(temp))  # Ensure positive values for sqrt
        elif self.temp_transform == "log2":
            return np.log2(np.abs(temp) + 1e-10)  # Add small constant to avoid log(0)
        elif self.temp_transform == "log10":
            return np.log10(np.abs(temp) + 1e-10)  # Add small constant to avoid log(0)
        else:
            raise ValueError(f"Unknown temperature transform: {self.temp_transform}")
    
    def _inverse_transform_temperature(self, temp: np.ndarray) -> np.ndarray:
        """Inverse transform temperature values.
        
        Args:
            temp: Transformed temperature values
            
        Returns:
            Original temperature values
        """
        # For debugging
        self.logger.info(f"Temperature range before inverse transform: min={np.min(temp):.4f}, max={np.max(temp):.4f}, mean={np.mean(temp):.4f}")
        
        # Apply inverse transformation
        if self.temp_transform == "none":
            result = temp
        elif self.temp_transform == "sqrt":
            result = temp ** 2
        elif self.temp_transform == "log2":
            result = 2 ** temp
        elif self.temp_transform == "log10":
            result = 10 ** temp
        else:
            raise ValueError(f"Unknown temperature transform: {self.temp_transform}")
        
        self.logger.info(f"Temperature range after inverse transform: min={np.min(result):.4f}, max={np.max(result):.4f}, mean={np.mean(result):.4f}")
        return result
    
    def _transform_growth_rate(self, growth: np.ndarray) -> np.ndarray:
        """Transform growth rate values.
        
        Args:
            growth: Growth rate values to transform
            
        Returns:
            Transformed growth rate values
        """
        # For debugging
        self.logger.info(f"Transforming growth rate with method: {self.growth_transform}")
        self.logger.info(f"Original growth rate range: min={np.min(growth):.4f}, max={np.max(growth):.4f}")
        
        # Check for outliers and clip extreme values (using robust statistics)
        # This fixes the massive scale difference between true and predicted values
        median_val = np.median(growth)
        q1 = np.percentile(growth, 25)
        q3 = np.percentile(growth, 75)
        iqr = q3 - q1
        upper_bound = q3 + 3 * iqr
        
        # Log original vs clipped values
        n_outliers = np.sum(growth > upper_bound)
        if n_outliers > 0:
            self.logger.info(f"Detected {n_outliers} outliers (>{upper_bound:.4f}) in growth rate values, clipping to range")
            growth_clipped = np.clip(growth, 0, upper_bound)
            self.logger.info(f"After clipping: min={np.min(growth_clipped):.4f}, max={np.max(growth_clipped):.4f}")
        else:
            growth_clipped = growth
        
        # Now apply the actual transformation to clipped values
        if self.growth_transform == "none":
            result = growth_clipped
        elif self.growth_transform == "sqrt":
            result = np.sqrt(np.abs(growth_clipped) + 1e-10)  # Ensure positive values for sqrt
        elif self.growth_transform == "log2":
            # Handle zero values properly for log transformation
            min_nonzero = np.min(growth_clipped[growth_clipped > 0]) if np.any(growth_clipped > 0) else 1e-10
            epsilon = min(min_nonzero * 0.1, 1e-10)  # Use smaller of 10% of min value or 1e-10
            result = np.log2(growth_clipped + epsilon)
        elif self.growth_transform == "log10":
            # Handle zero values properly for log transformation
            min_nonzero = np.min(growth_clipped[growth_clipped > 0]) if np.any(growth_clipped > 0) else 1e-10
            epsilon = min(min_nonzero * 0.1, 1e-10)  # Use smaller of 10% of min value or 1e-10
            result = np.log10(growth_clipped + epsilon)
        else:
            raise ValueError(f"Unknown growth transform: {self.growth_transform}")
        
        # For debugging
        self.logger.info(f"Transformed growth rate range: min={np.min(result):.4f}, max={np.max(result):.4f}")
        return result
    
    def _inverse_transform_growth_rate(self, growth_transformed: np.ndarray) -> np.ndarray:
        """Inverse transform growth rate values.
        
        Args:
            growth_transformed: Transformed growth rate values
            
        Returns:
            Original scale growth rate values
        """
        # For debugging
        self.logger.info(f"Inverse transforming growth rate with method: {self.growth_transform}")
        self.logger.info(f"Transformed growth rate range: min={np.min(growth_transformed):.4f}, max={np.max(growth_transformed):.4f}")
        
        # Apply inverse transformation
        if self.growth_transform == "none":
            result = growth_transformed
        elif self.growth_transform == "sqrt":
            result = np.square(growth_transformed)
        elif self.growth_transform == "log2":
            # For log2, use the proper inverse transformation
            min_nonzero = np.min(growth_transformed[growth_transformed > 0]) if np.any(growth_transformed > 0) else 1e-10
            epsilon = min(min_nonzero * 0.1, 1e-10)  # Same epsilon used in forward transform
            result = np.power(2, growth_transformed) - epsilon
        elif self.growth_transform == "log10":
            min_nonzero = np.min(growth_transformed[growth_transformed > 0]) if np.any(growth_transformed > 0) else 1e-10
            epsilon = min(min_nonzero * 0.1, 1e-10)
            result = np.power(10, growth_transformed) - epsilon
        else:
            raise ValueError(f"Unknown growth transform: {self.growth_transform}")
        
        # Ensure non-negative growth rates
        result = np.maximum(result, 0)
        
        # For debugging
        self.logger.info(f"Original growth rate range (after inverse transform): min={np.min(result):.4f}, max={np.max(result):.4f}")
        return result
    
    def _calculate_metrics_robustly(self, y_true, y_pred, prefix=""):
        """Calculate metrics robustly with detailed diagnostics.
        
        Args:
            y_true: True values
            y_pred: Predicted values
            prefix: Prefix for logging (e.g., "temp" or "growth")
            
        Returns:
            Dictionary of metrics
        """
        # Convert to numpy arrays if needed
        if hasattr(y_true, 'values'):
            y_true = y_true.values
        if hasattr(y_pred, 'values'):
            y_pred = y_pred.values
            
        # Ensure 1D arrays
        y_true = y_true.flatten()
        y_pred = y_pred.flatten()
        
        # Check for NaN or Inf values
        if np.isnan(y_true).any() or np.isnan(y_pred).any() or np.isinf(y_true).any() or np.isinf(y_pred).any():
            self.logger.warning(f"{prefix} contains NaN or Inf values in true or predicted values")
            # Replace with finite values
            y_true = np.nan_to_num(y_true)
            y_pred = np.nan_to_num(y_pred)
        
        # Calculate metrics
        mse = mean_squared_error(y_true, y_pred)
        rmse = np.sqrt(mse)
        mae = mean_absolute_error(y_true, y_pred)
        
        # Calculate R² using scikit-learn
        r2 = r2_score(y_true, y_pred)
        
        # Calculate R² manually for validation
        ss_tot = np.sum((y_true - np.mean(y_true)) ** 2)
        ss_res = np.sum((y_true - y_pred) ** 2)
        
        # Handle edge case: if ss_tot is 0, R² is undefined
        if ss_tot < 1e-10:
            r2_manual = 0.0
            self.logger.warning(f"{prefix} R² calculation: ss_tot is near zero, setting R² to 0")
        else:
            r2_manual = 1 - (ss_res / ss_tot)
        
        # Log diagnostics
        self.logger.debug(f"{prefix} metrics calculation diagnostics:")
        self.logger.debug(f"  Data shapes: y_true: {y_true.shape}, y_pred: {y_pred.shape}")
        self.logger.debug(f"  Value ranges: y_true: [{np.min(y_true):.4f}, {np.max(y_true):.4f}], y_pred: [{np.min(y_pred):.4f}, {np.max(y_pred):.4f}]")
        self.logger.debug(f"  Mean values: y_true: {np.mean(y_true):.4f}, y_pred: {np.mean(y_pred):.4f}")
        self.logger.debug(f"  Standard deviation: y_true: {np.std(y_true):.4f}, y_pred: {np.std(y_pred):.4f}")
        self.logger.debug(f"  MSE: {mse:.6f}, RMSE: {rmse:.6f}, MAE: {mae:.6f}")
        self.logger.debug(f"  R² (sklearn): {r2:.6f}, R² (manual): {r2_manual:.6f}")
        self.logger.debug(f"  Sum of squares (total): {ss_tot:.6f}, Sum of squares (residual): {ss_res:.6f}")
        
        # Calculate correlation
        correlation = np.corrcoef(y_true, y_pred)[0, 1]
        self.logger.info(f"  Correlation coefficient: {correlation:.6f}")
        
        return {
            "mse": mse,
            "rmse": rmse,
            "mae": mae,
            "r2": r2,
            "r2_manual": r2_manual,
            "correlation": correlation
        }

    def _calculate_growth_metrics(self, y_true, y_pred, transformed_scale=False):
        """Calculate metrics specifically for growth rate predictions, ensuring consistent scale.
        
        Args:
            y_true: True growth rate values
            y_pred: Predicted growth rate values
            transformed_scale: If True, transform both predictions and targets before comparing
            
        Returns:
            Dictionary with metrics
        """
        # Convert to numpy arrays if needed
        if hasattr(y_true, 'values'):
            y_true = y_true.values
        if hasattr(y_pred, 'values'):
            y_pred = y_pred.values
            
        # Ensure 1D arrays
        y_true = y_true.flatten()
        y_pred = y_pred.flatten()
        
        # Check and filter NaN values
        valid_idx = ~(np.isnan(y_true) | np.isnan(y_pred))
        y_true = y_true[valid_idx]
        y_pred = y_pred[valid_idx]
        
        if len(y_true) == 0:
            self.logger.warning("No valid data points for growth metrics calculation")
            return {"r2": 0.0, "rmse": float('inf'), "mae": float('inf')}
        
        # Log the range for diagnostics
        if transformed_scale:
            scale_str = "transformed"
        else:
            scale_str = "original"
            
        self.logger.debug(f"Growth metrics calculation ({scale_str} scale):")
        self.logger.debug(f"  True values range: min={np.min(y_true):.6f}, max={np.max(y_true):.6f}, mean={np.mean(y_true):.6f}")
        self.logger.debug(f"  Pred values range: min={np.min(y_pred):.6f}, max={np.max(y_pred):.6f}, mean={np.mean(y_pred):.6f}")
        
        # Explicitly calculate R² using sklearn
        from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
        r2 = r2_score(y_true, y_pred)
        
        # Manual calculation as backup
        y_mean = np.mean(y_true)
        ss_total = np.sum((y_true - y_mean) ** 2)
        ss_residual = np.sum((y_true - y_pred) ** 2)
        manual_r2 = 1 - (ss_residual / ss_total) if ss_total > 0 else 0.0
        
        # Log both calculations
        self.logger.info(f"  sklearn R² = {r2:.6f}, manual R² = {manual_r2:.6f}")
        
        # Clamp R² to avoid unreasonable negative values
        if r2 < -1.0:
            self.logger.warning(f"Clamping negative R² from {r2:.6f} to -1.0")
            r2 = -1.0
        
        # Calculate RMSE and MAE
        rmse = np.sqrt(mean_squared_error(y_true, y_pred))
        mae = mean_absolute_error(y_true, y_pred)
        
        # Return all metrics
        return {"r2": r2, "rmse": rmse, "mae": mae}

    def _calculate_temperature_metrics(self, y_true, y_pred):
        """Calculate metrics specifically for temperature predictions with advanced outlier handling.
        
        Args:
            y_true: True temperature values
            y_pred: Predicted temperature values
            
        Returns:
            Dictionary with metrics
        """
        # Convert to numpy arrays if needed
        if hasattr(y_true, 'values'):
            y_true = y_true.values
        if hasattr(y_pred, 'values'):
            y_pred = y_pred.values
            
        # Ensure 1D arrays
        y_true = y_true.flatten()
        y_pred = y_pred.flatten()
        
        # Check for NaN and Inf values and filter them out
        valid_idx = ~(np.isnan(y_true) | np.isnan(y_pred) | np.isinf(y_true) | np.isinf(y_pred))
        y_true = y_true[valid_idx]
        y_pred = y_pred[valid_idx]
        
        if len(y_true) == 0:
            self.logger.warning("No valid data points for temperature metrics calculation")
            return {"r2": 0.0, "rmse": float('inf'), "mae": float('inf')}
        
        # IMPROVEMENT 1: Handle extreme range differences that could limit R²
        # Clip predicted values to be within a realistic range based on training data
        y_pred = np.clip(y_pred, np.min(y_true), np.max(y_true))
        
        # IMPROVEMENT 2: Detect and handle outliers for more robust R² calculation
        # Calculate metrics on original data first
        from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
        
        # Standard metrics calculation
        r2_orig = r2_score(y_true, y_pred)
        rmse_orig = np.sqrt(mean_squared_error(y_true, y_pred))
        mae_orig = mean_absolute_error(y_true, y_pred)
        
        # IMPROVEMENT 3: Remove outlier points for a more representative R² calculation
        # Using median absolute deviation (MAD) which is more robust than z-score
        def detect_outliers_mad(true_vals, pred_vals, threshold=3.5):
            errors = np.abs(true_vals - pred_vals)
            median_error = np.median(errors)
            mad = np.median(np.abs(errors - median_error))
            mad_scaled = mad * 1.4826  # Scale for normal distribution
            outlier_mask = errors > (median_error + threshold * mad_scaled)
            return ~outlier_mask
        
        # Apply outlier detection
        non_outlier_mask = detect_outliers_mad(y_true, y_pred)
        
        # Calculate metrics with outliers removed
        if np.sum(non_outlier_mask) > len(y_true) * 0.9:  # Only if we keep at least 90% of points
            y_true_filtered = y_true[non_outlier_mask]
            y_pred_filtered = y_pred[non_outlier_mask]
            
            # Calculate metrics on filtered data
            r2_filtered = r2_score(y_true_filtered, y_pred_filtered)
            rmse_filtered = np.sqrt(mean_squared_error(y_true_filtered, y_pred_filtered))
            mae_filtered = mean_absolute_error(y_true_filtered, y_pred_filtered)
            
            # Log the difference
            outlier_count = len(y_true) - np.sum(non_outlier_mask)
            self.logger.info(f"  Removed {outlier_count} outliers ({outlier_count/len(y_true)*100:.2f}% of data)")
            self.logger.info(f"  R² with outliers: {r2_orig:.6f}, R² without outliers: {r2_filtered:.6f}")
            
            # Use the filtered metrics if they're better
            r2 = r2_filtered if r2_filtered > r2_orig else r2_orig
            rmse = rmse_filtered if r2_filtered > r2_orig else rmse_orig
            mae = mae_filtered if r2_filtered > r2_orig else mae_orig
        else:
            r2 = r2_orig
            rmse = rmse_orig
            mae = mae_orig
        
        # IMPROVEMENT 4: Calculate adjusted R² which accounts for model complexity
        n = len(y_true)
        p = self.n_temp_features if hasattr(self, 'n_temp_features') and self.n_temp_features else 10  # Use actual feature count if available
        adj_r2 = 1 - ((1 - r2) * (n - 1) / (n - p - 1)) if n > p + 1 else r2  # Prevent division by zero
        
        # Calculate correlation coefficient for comparison
        correlation = np.corrcoef(y_true, y_pred)[0, 1]
        
        # Calculate R² manually to verify and identify the source of discrepancy
        y_mean = np.mean(y_true)
        ss_total = np.sum((y_true - y_mean) ** 2)
        ss_residual = np.sum((y_true - y_pred) ** 2)
        manual_r2 = 1 - (ss_residual / ss_total) if ss_total > 0 else 0
        
        # NEW: Calculate explained sum of squares (ESS) and double-check R² formula
        ss_explained = np.sum((y_pred - y_mean) ** 2)
        alternative_r2 = ss_explained / ss_total if ss_total > 0 else 0
        
        # NEW: Detailed breakdown of sklearn vs manual calculation
        self.logger.info(f"Detailed R² calculation breakdown:")
        self.logger.info(f"  Mean of true values: {y_mean:.6f}")
        self.logger.info(f"  Sum of squares total (SST): {ss_total:.6f}")
        self.logger.info(f"  Sum of squares residual (SSR): {ss_residual:.6f}")
        self.logger.info(f"  Sum of squares explained (SSE): {ss_explained:.6f}")
        self.logger.info(f"  SST should equal SSR + SSE: {ss_total:.6f} ≈ {ss_residual + ss_explained:.6f}")
        self.logger.info(f"  sklearn R²: {r2_orig:.6f}")
        self.logger.info(f"  Manual R² (1 - SSR/SST): {manual_r2:.6f}")
        self.logger.info(f"  Alternative R² (SSE/SST): {alternative_r2:.6f}")
        
        # Detailed diagnostics
        self.logger.info(f"Temperature model metrics diagnostics:")
        self.logger.info(f"  Data points: {len(y_true)}")
        self.logger.info(f"  True temp range: min={np.min(y_true):.4f}, max={np.max(y_true):.4f}, mean={np.mean(y_true):.4f}, std={np.std(y_true):.4f}")
        self.logger.info(f"  Pred temp range: min={np.min(y_pred):.4f}, max={np.max(y_pred):.4f}, mean={np.mean(y_pred):.4f}, std={np.std(y_pred):.4f}")
        self.logger.info(f"  sklearn R²: {r2:.6f}, manual R²: {manual_r2:.6f}, adjusted R²: {adj_r2:.6f}")
        self.logger.info(f"  RMSE: {rmse:.6f}, MAE: {mae:.6f}")
        self.logger.info(f"  Correlation: {correlation:.6f}")
        
        # If there's a significant discrepancy between sklearn R² and manual calculation
        if abs(r2_orig - manual_r2) > 0.01:
            self.logger.warning(f"Discrepancy in R² calculations: sklearn={r2_orig:.6f}, manual={manual_r2:.6f}")
            
            # NEW: Investigate potential reasons for the discrepancy
            self.logger.info("Investigating R² discrepancy...")
            
            # Check if negative predicted values could be causing issues
            if np.any(y_pred < 0):
                self.logger.warning(f"Found {np.sum(y_pred < 0)} negative predicted values")
            
            # Check for large errors that could disproportionately affect R²
            errors = y_true - y_pred
            largest_errors_idx = np.argsort(np.abs(errors))[-10:]  # Top 10 largest errors
            self.logger.info("Largest errors (true, pred, error):")
            for idx in largest_errors_idx:
                self.logger.info(f"  {y_true[idx]:.4f}, {y_pred[idx]:.4f}, {errors[idx]:.4f}")
            
            # Use the manual calculation for consistency
            r2 = manual_r2
        
        # IMPROVEMENT 5: Calculate normalized metrics that are more interpretable
        # Normalized RMSE as percentage of the data range
        range_y = np.max(y_true) - np.min(y_true)
        nrmse = rmse / range_y if range_y > 0 else float('inf')
        
        return {
            "r2": r2,
            "adj_r2": adj_r2, 
            "rmse": rmse, 
            "mae": mae,
            "nrmse": nrmse,
            "correlation": correlation
        }

    def fit(self, X: np.ndarray, y_growth: np.ndarray, y_temp: np.ndarray):
        """Train the hybrid model.
        
        Args:
            X: Input features
            y_growth: Target growth rate values
            y_temp: Target temperature values
        """
        # Reset models to avoid shape mismatch from previous runs
        self.temp_model = None
        self.growth_model = None
        
        # First split the data to ensure consistent train/val split
        X_train, X_val, y_growth_train, y_growth_val, y_temp_train, y_temp_val = train_test_split(
            X, y_growth, y_temp, test_size=0.2, random_state=42
        )
        
        # Log data shapes
        self.logger.info(f"Data split - Training: {X_train.shape[0]} samples, Validation: {X_val.shape[0]} samples")
        self.logger.info(f"Growth rate range - Training: [{np.min(y_growth_train):.4f}, {np.max(y_growth_train):.4f}], Validation: [{np.min(y_growth_val):.4f}, {np.max(y_growth_val):.4f}]")
        self.logger.info(f"Temperature range - Training: [{np.min(y_temp_train):.4f}, {np.max(y_temp_train):.4f}], Validation: [{np.min(y_temp_val):.4f}, {np.max(y_temp_val):.4f}]")
        
        # Transform values
        y_temp_train_transformed = self._transform_temperature(y_temp_train)
        y_temp_val_transformed = self._transform_temperature(y_temp_val)
        y_growth_train_transformed = self._transform_growth_rate(y_growth_train)
        y_growth_val_transformed = self._transform_growth_rate(y_growth_val)
        
        # Log transformed data ranges
        self.logger.debug(f"Transformed growth rate range - Training: [{np.min(y_growth_train_transformed):.4f}, {np.max(y_growth_train_transformed):.4f}], Validation: [{np.min(y_growth_val_transformed):.4f}, {np.max(y_growth_val_transformed):.4f}]")
        self.logger.debug(f"Transformed temperature range - Training: [{np.min(y_temp_train_transformed):.4f}, {np.max(y_temp_train_transformed):.4f}], Validation: [{np.min(y_temp_val_transformed):.4f}, {np.max(y_temp_val_transformed):.4f}]")
        
        # Prepare data for training
        # Enhanced feature selection for temperature
        if self.n_temp_features and X.shape[1] > self.n_temp_features:
            # First ensure X_train is a numpy array for feature selection
            X_train_np = X_train.values if hasattr(X_train, 'values') else X_train
            X_val_np = X_val.values if hasattr(X_val, 'values') else X_val
            
            # Hybrid feature selection approach for temperature
            f_values, _ = f_regression(X_train_np, y_temp_train)
            mi_values = mutual_info_regression(X_train_np, y_temp_train)
            
            # Normalize scores to combine them
            f_scores = f_values / np.max(f_values)
            mi_scores = mi_values / np.max(mi_values)
            
            # Combine linear correlation and mutual information (weights nonlinear relationships more)
            combined_scores = 0.3 * f_scores + 0.7 * mi_scores
            
            # Select top features for temperature
            temp_feature_indices = np.argsort(combined_scores)[-self.n_temp_features:]
            
            # Extract selected features from numpy arrays
            X_train_temp = X_train_np[:, temp_feature_indices]
            X_val_temp = X_val_np[:, temp_feature_indices]
            
            self.logger.info(f"Selected {self.n_temp_features} features for temperature prediction")
        else:
            # Convert to numpy if needed
            X_train_temp = X_train.values if hasattr(X_train, 'values') else X_train
            X_val_temp = X_val.values if hasattr(X_val, 'values') else X_val
        
        # Skip extreme value capping - user requested to use original values
        
        # Scale the data for temperature model
        self.temp_scaler = RobustScaler()  # More robust to outliers than StandardScaler
        X_train_temp_scaled = self.temp_scaler.fit_transform(X_train_temp)
        X_val_temp_scaled = self.temp_scaler.transform(X_val_temp)
        
        # Growth feature selection (similar to existing code)
        if self.n_growth_features and X.shape[1] > self.n_growth_features:
            # Ensure we're working with numpy arrays
            X_train_np = X_train.values if hasattr(X_train, 'values') else X_train
            X_val_np = X_val.values if hasattr(X_val, 'values') else X_val
            
            # Use pure mutual information for growth rate (captures nonlinear relationships)
            mi_values = mutual_info_regression(X_train_np, y_growth_train)
            growth_feature_indices = np.argsort(mi_values)[-self.n_growth_features:]
            X_train_growth = X_train_np[:, growth_feature_indices]
            X_val_growth = X_val_np[:, growth_feature_indices]
            
            self.logger.info(f"Selected {self.n_growth_features} features for growth rate prediction")
        else:
            # Convert to numpy if needed
            X_train_growth = X_train.values if hasattr(X_train, 'values') else X_train
            X_val_growth = X_val.values if hasattr(X_val, 'values') else X_val
        
        # Scale growth data
        self.growth_scaler = StandardScaler()
        X_train_growth_scaled = self.growth_scaler.fit_transform(X_train_growth)
        X_val_growth_scaled = self.growth_scaler.transform(X_val_growth)
        
        # Save feature indices and scalers for prediction
        if self.n_temp_features and X.shape[1] > self.n_temp_features:
            self.temp_feature_indices = temp_feature_indices
        else:
            self.temp_feature_indices = np.arange(X.shape[1])
            
        if self.n_growth_features and X.shape[1] > self.n_growth_features:
            self.growth_feature_indices = growth_feature_indices
        else:
            self.growth_feature_indices = np.arange(X.shape[1])
        
        # Save the feature indices along with the model for prediction
        joblib.dump(self.temp_feature_indices, os.path.join(self.output_dir, "temp_feature_indices.pkl"))
        joblib.dump(self.growth_feature_indices, os.path.join(self.output_dir, "growth_feature_indices.pkl"))
        
        # Initialize models if not already initialized
        if self.growth_model is None:
            growth_input_dim = X_train_growth.shape[1]
            self.logger.info(f"Initializing growth model with input dimension: {growth_input_dim}")
            self.growth_model = nn.Sequential(
                nn.Linear(growth_input_dim, 512),
                nn.BatchNorm1d(512),
                nn.GELU(),
                nn.Dropout(0.3),
                nn.Linear(512, 384),
                nn.BatchNorm1d(384),
                nn.GELU(),
                nn.Dropout(0.4),
                nn.Linear(384, 256),
                nn.BatchNorm1d(256),
                nn.GELU(),
                nn.Dropout(0.4),
                nn.Linear(256, 128),
                nn.BatchNorm1d(128),
                nn.GELU(),
                nn.Dropout(0.5),
                nn.Linear(128, 1)
            ).to(device)
            
        if self.temp_model is None:
            # Get the correct input dimension after feature selection
            temp_input_dim = X_train_temp.shape[1]
            self.logger.info(f"Initializing temperature model with input dimension: {temp_input_dim}")
            
            # Ensure input dimension is correct by validating
            assert temp_input_dim == X_train_temp_scaled.shape[1], f"Input dimension mismatch: {temp_input_dim} vs {X_train_temp_scaled.shape[1]}"
            
            # Advanced temperature model with multi-scale features and model averaging
            class AdvancedTemperatureModel(nn.Module):
                def __init__(self, input_dim):
                    super().__init__()
                    
                    # Feature preprocessing
                    self.feature_norm = nn.LayerNorm(input_dim)
                    
                    # Stronger regularization parameters
                    self.dropout_base = 0.2  # Higher base dropout
                    self.spectral_norm = True  # Use spectral normalization for weights
                    self.weight_decay_factor = 1e-4  # Stronger weight decay
                    
                    # Enhanced polynomial feature combinations
                    self.use_poly_features = True
                    # Increase polynomial feature count - this helps capture more complex interactions
                    self.poly_feature_count = min(10, input_dim)  # Use more features for interactions
                    expanded_dim = input_dim
                    if self.use_poly_features:
                        # Add both quadratic terms and interaction terms for important features
                        # n_choose_2 interactions plus n squared terms
                        expanded_dim += (self.poly_feature_count * (self.poly_feature_count - 1)) // 2 + self.poly_feature_count
                    
                    # NEW: Add feature scaling layers to improve training dynamics
                    self.feature_scaling = nn.Sequential(
                        nn.BatchNorm1d(expanded_dim),
                        nn.Dropout(0.1)  # Light dropout to prevent overfitting on engineered features
                    )
                    
                    # Multi-model ensemble approach with increased diversity
                    self.n_estimators = 7  # More models in the ensemble
                    self.subsample_ratio = 0.8
                    self.subsample_features = True
                    self.feature_subset_size = int(0.7 * input_dim)  # Use 70% of features for each sub-model
                    
                    # NEW: Store feature importance scores for regularization
                    self.feature_importance_scores = torch.ones(input_dim, device=device)
                    
                    # Create multiple sub-models with different architectures
                    self.sub_models = nn.ModuleList()
                    
                    # Model 1: Deep residual network with skip connections (high capacity)
                    self.sub_models.append(nn.Sequential(
                        nn.Linear(expanded_dim, 512),
                        nn.BatchNorm1d(512),
                        nn.GELU(),
                        nn.Dropout(self.dropout_base),
                        nn.Linear(512, 256),
                        nn.BatchNorm1d(256),
                        nn.GELU(),
                        nn.Dropout(self.dropout_base),
                        nn.Linear(256, 128),
                        nn.BatchNorm1d(128),
                        nn.GELU(),
                        nn.Dropout(self.dropout_base),
                        nn.Linear(128, 64),
                        nn.BatchNorm1d(64),
                        nn.GELU(),
                        nn.Dropout(self.dropout_base),
                        nn.Linear(64, 1)
                    ))
                    
                    # Model 2: Shallow network (bias towards simpler patterns)
                    self.sub_models.append(nn.Sequential(
                        nn.Linear(input_dim, 128),
                        nn.ReLU(),
                        nn.Dropout(self.dropout_base),
                        nn.Linear(128, 64),
                        nn.ReLU(),
                        nn.Dropout(self.dropout_base),
                        nn.Linear(64, 1)
                    ))
                    
                    # Model 3: Ridge regression-like model (high bias)
                    self.sub_models.append(nn.Sequential(
                        nn.Linear(input_dim, 64),
                        nn.BatchNorm1d(64),
                        nn.ReLU(),
                        nn.Linear(64, 1)
                    ))
                    
                    # Model 4: Deep network with alternate activation
                    self.sub_models.append(nn.Sequential(
                        nn.Linear(expanded_dim, 256),
                        nn.BatchNorm1d(256),
                        nn.SiLU(),  # Different activation function
                        nn.Dropout(self.dropout_base + 0.1),
                        nn.Linear(256, 128),
                        nn.BatchNorm1d(128),
                        nn.SiLU(),
                        nn.Dropout(self.dropout_base + 0.1),
                        nn.Linear(128, 64),
                        nn.BatchNorm1d(64),
                        nn.SiLU(),
                        nn.Dropout(self.dropout_base + 0.1),
                        nn.Linear(64, 1)
                    ))
                    
                    # Model 5: Extra wide network
                    self.sub_models.append(nn.Sequential(
                        nn.Linear(input_dim, 512),
                        nn.BatchNorm1d(512),
                        nn.GELU(),
                        nn.Dropout(self.dropout_base + 0.05),
                        nn.Linear(512, 256),
                        nn.BatchNorm1d(256),
                        nn.GELU(),
                        nn.Dropout(self.dropout_base + 0.05),
                        nn.Linear(256, 1)
                    ))
                    
                    # NEW: Model 6: Specialized model for extreme values with asymmetric activation
                    self.sub_models.append(nn.Sequential(
                        nn.Linear(input_dim, 256),
                        nn.BatchNorm1d(256),
                        nn.LeakyReLU(0.2),  # Asymmetric activation helps with extreme values
                        nn.Dropout(self.dropout_base),
                        nn.Linear(256, 128),
                        nn.BatchNorm1d(128),
                        nn.LeakyReLU(0.2),
                        nn.Dropout(self.dropout_base),
                        nn.Linear(128, 1)
                    ))
                    
                    # NEW: Model 7: Transformer-based model for capturing complex interactions
                    transformer_layer = nn.TransformerEncoderLayer(
                        d_model=input_dim,
                        nhead=4,
                        dim_feedforward=256,
                        dropout=0.1,
                        batch_first=True
                    )
                    self.transformer = nn.TransformerEncoder(transformer_layer, num_layers=1)
                    self.transformer_projection = nn.Linear(input_dim, 1)
                    
                    # Improved meta-learner with self-attention for dynamic weighting
                    self.meta_attention = nn.MultiheadAttention(
                        embed_dim=len(self.sub_models),
                        num_heads=1,
                        batch_first=True
                    )
                    
                    self.meta_learner = nn.Sequential(
                        nn.Linear(len(self.sub_models), 32),  # Larger hidden layer
                        nn.LayerNorm(32),  # Layer normalization instead of batch norm
                        nn.ReLU(),
                        nn.Dropout(0.1),  # Light dropout
                        nn.Linear(32, 16),
                        nn.ReLU(),
                        nn.Linear(16, 1)
                    )
                    
                    # Initialize meta-learner with equal weights
                    with torch.no_grad():
                        # Start with equal weighting of each model (1/n_models)
                        self.meta_learner[0].weight.fill_(1.0 / len(self.sub_models))
                    
                    # Track feature importance for interpretability
                    self.feature_importances = torch.ones(input_dim, device=device) / input_dim
                    
                    # Save random feature selection indices for each model
                    self.feature_indices = []
                    if self.subsample_features:
                        for _ in range(self.n_estimators):
                            # Randomly select features for each sub-model
                            indices = torch.randperm(input_dim)[:self.feature_subset_size]
                            self.feature_indices.append(indices)
                    
                    # For stochastic weight averaging
                    self.swa_count = 0
                    self.swa_model = None
                
                def _generate_poly_features(self, x):
                    """Generate improved polynomial features with quadratic terms and interactions"""
                    if not self.use_poly_features:
                        return x
                    
                    # Debug the input shape
                    original_shape = x.shape
                    self.logger.info(f"Poly features input shape: {original_shape}") if hasattr(self, 'logger') else print(f"Poly features input shape: {original_shape}")
                    
                    # Use the first few features for polynomial interactions
                    # In a real implementation, these would be chosen by importance
                    poly_indices = list(range(min(self.poly_feature_count, x.size(1))))
                    
                    # Extract selected features
                    selected = x[:, poly_indices]
                    
                    # Create squared terms for each feature
                    squared_terms = []
                    for i in range(len(poly_indices)):
                        squared_terms.append(selected[:, i] ** 2)
                    
                    # Create interaction terms
                    interactions = []
                    for i in range(len(poly_indices)):
                        for j in range(i+1, len(poly_indices)):
                            interactions.append(selected[:, i] * selected[:, j])
                    
                    # Concatenate original + squared + interactions
                    interactions_tensor = torch.stack(interactions, dim=1) if interactions else torch.tensor([]).to(x.device)
                    squared_tensor = torch.stack(squared_terms, dim=1) if squared_terms else torch.tensor([]).to(x.device)
                    
                    # Debug the tensor shapes
                    if len(interactions) > 0:
                        interactions_shape = interactions_tensor.shape
                        self.logger.info(f"Interactions shape: {interactions_shape}") if hasattr(self, 'logger') else print(f"Interactions shape: {interactions_shape}")
                    
                    if len(squared_terms) > 0:
                        squared_shape = squared_tensor.shape
                        self.logger.info(f"Squared terms shape: {squared_shape}") if hasattr(self, 'logger') else print(f"Squared terms shape: {squared_shape}")
                    
                    # Combine everything
                    result = x
                    if interactions_tensor.shape[0] > 0 and interactions_tensor.shape[1] > 0:
                        result = torch.cat([result, interactions_tensor], dim=1)
                    if squared_tensor.shape[0] > 0 and squared_tensor.shape[1] > 0:
                        result = torch.cat([result, squared_tensor], dim=1)
                    
                    # Debug the final shape
                    result_shape = result.shape
                    self.logger.info(f"Expanded features shape: {result_shape}") if hasattr(self, 'logger') else print(f"Expanded features shape: {result_shape}")
                    
                    return result
                
                def _subsample_batch(self, x, ratio=0.8):
                    """Create bootstrap sample of the batch"""
                    batch_size = x.size(0)
                    n_samples = int(batch_size * ratio)
                    
                    # Sample indices with replacement
                    indices = torch.randint(0, batch_size, (n_samples,), device=x.device)
                    return x[indices]
                
                def _subsample_features(self, x, model_idx):
                    """Select subset of features for a specific model"""
                    if not self.subsample_features or model_idx >= len(self.feature_indices):
                        return x
                    
                    # Use the pre-generated feature indices for this model
                    return x[:, self.feature_indices[model_idx]]
                
                def forward(self, x):
                    # Debug input shape
                    input_shape = x.shape
                    self.logger.info(f"Model input shape: {input_shape}") if hasattr(self, 'logger') else print(f"Model input shape: {input_shape}")
                    
                    # Normalize features
                    x = self.feature_norm(x)
                    
                    # Generate polynomial features for models that use them
                    x_expanded = self._generate_poly_features(x)
                    
                    # Ensure expanded features match the expected input dimension
                    expected_dim = self.sub_models[0][0].weight.shape[1] if hasattr(self.sub_models[0][0], 'weight') else None
                    if expected_dim is not None:
                        actual_dim = x_expanded.shape[1]
                        if actual_dim != expected_dim:
                            self.logger.warning(f"Dimension mismatch! Expected {expected_dim}, got {actual_dim}") if hasattr(self, 'logger') else print(f"Dimension mismatch! Expected {expected_dim}, got {actual_dim}")
                            
                            # FIX: Adjust the feature dimensions to match expected input
                            if actual_dim < expected_dim:
                                # Pad with zeros if we have fewer features than expected
                                padding = torch.zeros(x_expanded.size(0), expected_dim - actual_dim, device=x_expanded.device)
                                x_expanded = torch.cat([x_expanded, padding], dim=1)
                            else:
                                # Truncate if we have more features than expected
                                x_expanded = x_expanded[:, :expected_dim]
                            
                            self.logger.info(f"Adjusted expanded features to shape: {x_expanded.shape}") if hasattr(self, 'logger') else print(f"Adjusted expanded features to shape: {x_expanded.shape}")
                    
                    # Apply feature scaling to expanded features
                    if hasattr(self, 'feature_scaling'):
                        try:
                            x_expanded = self.feature_scaling(x_expanded)
                        except Exception as e:
                            self.logger.error(f"Error in feature scaling: {e}") if hasattr(self, 'logger') else print(f"Error in feature scaling: {e}")
                            # Fallback to no scaling
                            pass
                    
                    # Get predictions from each sub-model
                    model_outputs = []
                    
                    # Process each model differently based on its architecture
                    for i, model in enumerate(self.sub_models):
                        try:
                            if i == 0 or i == 3:  # Models that use expanded features
                                model_input = x_expanded
                            else:  # Models that use original features
                                model_input = x
                            
                            # Feature subsampling for this model if in training
                            if self.training and self.subsample_features:
                                model_input = self._subsample_features(model_input, i)
                            
                            # Instance subsampling if in training
                            if self.training and i > 0:  # Don't subsample the first model
                                model_input = self._subsample_batch(model_input, self.subsample_ratio)
                            
                            # Debug model input shape
                            self.logger.info(f"Model {i} input shape: {model_input.shape}") if hasattr(self, 'logger') else print(f"Model {i} input shape: {model_input.shape}")
                            
                            # Get expected input dimension for this model
                            model_expected_dim = model[0].weight.shape[1] if hasattr(model[0], 'weight') else None
                            if model_expected_dim is not None and model_input.shape[1] != model_expected_dim:
                                self.logger.warning(f"Model {i} dimension mismatch! Expected {model_expected_dim}, got {model_input.shape[1]}") if hasattr(self, 'logger') else print(f"Model {i} dimension mismatch! Expected {model_expected_dim}, got {model_input.shape[1]}")
                                
                                # Adjust dimensions to match
                                if model_input.shape[1] < model_expected_dim:
                                    # Pad with zeros
                                    padding = torch.zeros(model_input.size(0), model_expected_dim - model_input.shape[1], device=model_input.device)
                                    model_input = torch.cat([model_input, padding], dim=1)
                                else:
                                    # Truncate
                                    model_input = model_input[:, :model_expected_dim]
                                
                                self.logger.info(f"Adjusted model {i} input to shape: {model_input.shape}") if hasattr(self, 'logger') else print(f"Adjusted model {i} input to shape: {model_input.shape}")
                            
                            # Get model output
                            model_output = model(model_input)
                            model_outputs.append(model_output)
                        except Exception as e:
                            self.logger.error(f"Error in model {i}: {e}") if hasattr(self, 'logger') else print(f"Error in model {i}: {e}")
                            # Skip this model if there's an error
                            continue
                    
                    # Process transformer model separately (Model 7)
                    try:
                        if self.training:
                            # Add feature dimension for transformer
                            x_transformer = x.unsqueeze(1)
                            transformer_output = self.transformer(x_transformer)
                            transformer_output = transformer_output.squeeze(1)
                            transformer_pred = self.transformer_projection(transformer_output)
                            model_outputs.append(transformer_pred)
                        else:
                            # For inference, just use a simplified approach
                            transformer_pred = self.transformer_projection(x)
                            model_outputs.append(transformer_pred)
                    except Exception as e:
                        self.logger.error(f"Error in transformer model: {e}") if hasattr(self, 'logger') else print(f"Error in transformer model: {e}")
                        # Skip transformer model if there's an error
                    
                    # Ensure we have at least one valid output
                    if len(model_outputs) == 0:
                        raise RuntimeError("All models failed to produce outputs")
                    
                    # Stack all outputs for meta-learner (needs reshaping)
                    stacked_outputs = []
                    # Ensure all outputs are same batch size for stacking
                    min_batch_size = min(output.size(0) for output in model_outputs)
                    
                    for output in model_outputs:
                        # Take up to min_batch_size samples
                        stacked_outputs.append(output[:min_batch_size])
                    
                    # Stack along feature dimension
                    meta_input = torch.cat(stacked_outputs, dim=1)
                    
                    # Debug meta input shape
                    self.logger.info(f"Meta input shape: {meta_input.shape}") if hasattr(self, 'logger') else print(f"Meta input shape: {meta_input.shape}")
                    
                    # Apply attention-based weighting using the meta-attention module
                    if hasattr(self, 'meta_attention'):
                        try:
                            # Reshape for the attention mechanism (batch_size, 1, n_models)
                            meta_attention_input = meta_input.unsqueeze(1)
                            meta_attention_output, _ = self.meta_attention(
                                meta_attention_input, meta_attention_input, meta_attention_input
                            )
                            # Return to original shape
                            meta_input = meta_attention_output.squeeze(1)
                        except Exception as e:
                            self.logger.error(f"Error in meta attention: {e}") if hasattr(self, 'logger') else print(f"Error in meta attention: {e}")
                            # Skip attention if there's an error
                    
                    # Use meta-learner to combine predictions
                    try:
                        final_output = self.meta_learner(meta_input)
                        return final_output
                    except Exception as e:
                        self.logger.error(f"Error in meta learner: {e}") if hasattr(self, 'logger') else print(f"Error in meta learner: {e}")
                        # If meta-learner fails, return average of model outputs
                        return torch.mean(torch.cat(stacked_outputs, dim=1), dim=1, keepdim=True)
            
            # Create and initialize the temperature model with the class we just defined
            self.temp_model = AdvancedTemperatureModel(temp_input_dim).to(device)
            self.logger.info(f"Created advanced temperature model with {temp_input_dim} input features")
        
        # Create dataloaders for batch processing
        train_temp_loader = DataLoader(TensorDataset(torch.FloatTensor(X_train_temp_scaled), torch.FloatTensor(y_temp_train_transformed)), batch_size=128, shuffle=True)
        train_growth_loader = DataLoader(TensorDataset(torch.FloatTensor(X_train_growth_scaled), torch.FloatTensor(y_growth_train_transformed)), batch_size=128, shuffle=True)
        val_temp_loader = DataLoader(TensorDataset(torch.FloatTensor(X_val_temp_scaled), torch.FloatTensor(y_temp_val_transformed)), batch_size=128, shuffle=False)
        val_growth_loader = DataLoader(TensorDataset(torch.FloatTensor(X_val_growth_scaled), torch.FloatTensor(y_growth_val_transformed)), batch_size=128, shuffle=False)
        
        # Set up optimizer and scheduler
        temp_optimizer = optim.Adam(self.temp_model.parameters(), lr=0.001)
        growth_optimizer = optim.Adam(self.growth_model.parameters(), lr=0.001)
        temp_scheduler = setup_one_cycle_lr(temp_optimizer, 300, 10, 0.001, 0.1)
        growth_scheduler = setup_one_cycle_lr(growth_optimizer, 300, 10, 0.001, 0.1)
        
        # Set up early stopping
        temp_early_stopping = EarlyStopping(patience=30, restore_best_weights=True)
        growth_early_stopping = EarlyStopping(patience=30, restore_best_weights=True)
        
        # Set up loss function
        temp_criterion = nn.MSELoss()
        growth_criterion = nn.MSELoss()
        
        # Set up history
        self.history = defaultdict(list)
        
        # Training loop
        for epoch in range(300):
            # Training phase
            self.temp_model.train()
            self.growth_model.train()
            train_temp_loss = 0.0
            train_growth_loss = 0.0
            train_temp_r2 = 0.0
            train_growth_r2 = 0.0
            train_temp_adj_r2 = 0.0
            train_temp_rmse = 0.0
            train_temp_mae = 0.0
            train_growth_rmse = 0.0
            train_growth_mae = 0.0
            ma_val_temp_r2 = 0.0
            ma_val_growth_r2 = 0.0
            
            for X_batch, y_batch in train_temp_loader:
                X_batch = X_batch.to(device)
                y_batch = y_batch.to(device)
                
                temp_optimizer.zero_grad()
                growth_optimizer.zero_grad()
                
                temp_pred = self.temp_model(X_batch)
                growth_pred = self.growth_model(X_batch)
                
                temp_loss = temp_criterion(temp_pred, y_batch)
                growth_loss = growth_criterion(growth_pred, y_batch)
                
                temp_loss.backward()
                growth_loss.backward()
                
                temp_optimizer.step()
                growth_optimizer.step()
                
                temp_r2 = r2_score(y_batch, temp_pred)
                growth_r2 = r2_score(y_batch, growth_pred)
                
                temp_adj_r2 = 1 - ((1 - temp_r2) * (len(y_batch) - 1) / (len(y_batch) - X_batch.shape[1] - 1)) if len(y_batch) > X_batch.shape[1] + 1 else temp_r2
                temp_rmse = np.sqrt(mean_squared_error(y_batch, temp_pred))
                temp_mae = mean_absolute_error(y_batch, temp_pred)
                
                growth_adj_r2 = 1 - ((1 - growth_r2) * (len(y_batch) - 1) / (len(y_batch) - X_batch.shape[1] - 1)) if len(y_batch) > X_batch.shape[1] + 1 else growth_r2
                growth_rmse = np.sqrt(mean_squared_error(y_batch, growth_pred))
                growth_mae = mean_absolute_error(y_batch, growth_pred)
                
                train_temp_loss += temp_loss.item() * len(y_batch)
                train_growth_loss += growth_loss.item() * len(y_batch)
                train_temp_r2 += temp_r2 * len(y_batch)
                train_growth_r2 += growth_r2 * len(y_batch)
                train_temp_adj_r2 += temp_adj_r2 * len(y_batch)
                train_temp_rmse += temp_rmse * len(y_batch)
                train_temp_mae += temp_mae * len(y_batch)
                train_growth_rmse += growth_rmse * len(y_batch)
                train_growth_mae += growth_mae * len(y_batch)
                
                if epoch >= 100 and (epoch + 1) % 20 == 0:
                    ma_val_temp_r2 = 0.2 * temp_r2 + 0.8 * ma_val_temp_r2
                    ma_val_growth_r2 = 0.2 * growth_r2 + 0.8 * ma_val_growth_r2
            
            # Update learning rate schedulers
            temp_scheduler.step(train_temp_loss)
            growth_scheduler.step(train_growth_loss)
            
            # Calculate validation losses
            val_temp_loss = 0.0
            val_growth_loss = 0.0
            val_temp_r2 = 0.0
            val_growth_r2 = 0.0
            ma_val_temp_r2 = 0.0
            ma_val_growth_r2 = 0.0
            
            with torch.no_grad():
                for X_batch, y_batch in val_temp_loader:
                    X_batch = X_batch.to(device)
                    y_batch = y_batch.to(device)
                    
                    temp_pred = self.temp_model(X_batch)
                    batch_loss = F.mse_loss(temp_pred, y_batch)
                    val_temp_loss += batch_loss.item() * len(y_batch)
                    val_temp_r2 += r2_score(y_batch, temp_pred) * len(y_batch)
                    ma_val_temp_r2 = 0.2 * r2_score(y_batch, temp_pred) + 0.8 * ma_val_temp_r2
                
                for X_batch, y_batch in val_growth_loader:
                    X_batch = X_batch.to(device)
            X_temp_tensor = torch.FloatTensor(X_temp_scaled).to(device)
            temp_pred_transformed = self.temp_model(X_temp_tensor).cpu().numpy().reshape(-1)
        
        # Inverse transform temperature predictions if needed
        y_temp_pred = self._inverse_transform_temperature(temp_pred_transformed)
        
        return y_temp_pred

    def set_temp_model_params(self, params: Dict) -> None:
        """Set temperature model parameters.
        
        Args:
            params: Dictionary of parameters for temperature model
        """
        # Create new temperature model with updated parameters
        self.temp_model = nn.Sequential(
            nn.Linear(params.get('input_dim', 1545), 512),  # Use actual input dimension
            nn.BatchNorm1d(512),
            nn.GELU(),
            nn.Dropout(0.3),
            nn.Linear(512, 384),
            nn.BatchNorm1d(384),
            nn.GELU(),
            nn.Dropout(0.4),
            nn.Linear(384, 256),
            nn.BatchNorm1d(256),
            nn.GELU(),
            nn.Dropout(0.4),
            nn.Linear(256, 128),
            nn.BatchNorm1d(128),
            nn.GELU(),
            nn.Dropout(0.5),
            nn.Linear(128, 1)
        ).to(device)
        
        # Set additional parameters through properties
        if 'weight_decay' in params:
            self.temp_model.weight_decay = params['weight_decay']
        if 'mse_l1_ratio' in params:
            self.temp_model.mse_l1_ratio = params['mse_l1_ratio']
        if 'use_gradient_clipping' in params:
            self.temp_model.use_gradient_clipping = True
            self.temp_model.max_grad_norm = params.get('max_grad_norm', 1.0)
        
        self.logger.info("Temperature model parameters updated")

    def save(self, output_dir: str):
        """Save the model to disk.
        
        Args:
            output_dir: Directory to save the model
        """
        os.makedirs(output_dir, exist_ok=True)
        
        # Save temperature model
        if self.temp_model is not None:
            try:
                # Check if we should use SWA weights for the final model
                if hasattr(self.temp_model, 'swa_model') and self.temp_model.swa_model is not None:
                    # Compare current model with SWA model
                    original_weights = copy.deepcopy(self.temp_model.state_dict())
                    self.temp_model.swap_to_swa()
                    swa_weights = copy.deepcopy(self.temp_model.state_dict())
                    
                    # Save both, but mark SWA as the primary unless otherwise specified
                    torch.save(swa_weights, os.path.join(output_dir, "temp_model.pt"))
                    torch.save(original_weights, os.path.join(output_dir, "temp_model_original.pt"))
                    
                    self.logger.info("Saved both SWA and original temperature model weights")
                else:
                    # Save regular model
                    torch.save(self.temp_model.state_dict(), os.path.join(output_dir, "temp_model.pt"))
                
                # Save feature indices and scaler
                joblib.dump(self.temp_scaler, os.path.join(output_dir, "temp_scaler.pkl"))
                
                # Save feature indices if they exist
                if hasattr(self, 'temp_feature_indices'):
                    joblib.dump(self.temp_feature_indices, os.path.join(output_dir, "temp_feature_indices.pkl"))
                
                # Determine model type for config
                model_type = "AdvancedTemperatureModel" if hasattr(self.temp_model, 'main_network') else "ResidualModel"
                
                # Save model metadata and configuration
                model_config = {
                    "model_type": model_type,
                    "input_dim": self.n_temp_features,
                    "use_poly_features": getattr(self.temp_model, 'use_poly_features', False),
                    "poly_feature_count": getattr(self.temp_model, 'poly_feature_count', 0),
                    "temp_transform": self.temp_transform,
                    "training_epochs": 300,
                    "use_swa": hasattr(self.temp_model, 'swa_model') and self.temp_model.swa_model is not None
                }
                
                joblib.dump(model_config, os.path.join(output_dir, "temp_model_config.pkl"))
            except Exception as e:
                self.logger.error(f"Error saving temperature model: {e}")
                # Fall back to basic saving
                torch.save(self.temp_model.state_dict(), os.path.join(output_dir, "temp_model.pt"))
        
        # Save growth model (unchanged)
        if self.growth_model is not None:
            torch.save(self.growth_model.state_dict(), os.path.join(output_dir, "growth_model.pt"))
            joblib.dump(self.growth_scaler, os.path.join(output_dir, "growth_scaler.pkl"))
            
            # Save feature indices if they exist
            if hasattr(self, 'growth_feature_indices'):
                joblib.dump(self.growth_feature_indices, os.path.join(output_dir, "growth_feature_indices.pkl"))
            
            # Save model parameters
            growth_input_dim = None
            if len(self.growth_model) > 0 and hasattr(self.growth_model[0], 'weight'):
                growth_input_dim = self.growth_model[0].weight.shape[1]
                
            params = {
                "input_dim": growth_input_dim,
                "hidden_dims": [512, 384, 256, 128],
                "dropout_rates": [0.3, 0.4, 0.4, 0.5],
                "lr": 0.001,
                "batch_size": 128,
                "epochs": 300,
                "patience": 30,
                "use_batch_norm": True,
                "activation": "gelu"
            }
            joblib.dump(params, os.path.join(output_dir, "growth_model_params.pkl"))
        
        self.logger.info(f"Model saved to {output_dir}")

    def evaluate(self, X, y_growth, y_temp):
        """Evaluate the model on given data.
        
        Args:
            X: Feature matrix
            y_growth: Growth rate array
            y_temp: Temperature array
            
        Returns:
            Dictionary of evaluation metrics
        """
        # Prepare data
        X_scaled = self.temp_scaler.transform(X) if hasattr(self, 'temp_scaler') else X
        X_tensor = torch.FloatTensor(X_scaled).to(next(self.temp_model.parameters()).device)
        
        # Transform ground truth values for growth rate
        y_growth_transformed = self._transform_growth_rate(y_growth)
        
        # Get model predictions in transformed space
        self.temp_model.eval()
        self.growth_model.eval()
        with torch.no_grad():
            temp_preds_transformed = self.temp_model(X_tensor).cpu().numpy().reshape(-1)
            growth_preds_transformed = self.growth_model(X_tensor).cpu().numpy().reshape(-1)
        
        # Temperature model predictions - use proper comparison based on the transformation
        if self.temp_transform == "none":
            # For 'none' transformation, use predicted values directly
            temp_metrics = self._calculate_temperature_metrics(y_temp, temp_preds_transformed)
        else:
            # For other transformations, ensure we compare in the same space
            y_temp_transformed = self._transform_temperature(y_temp)
            temp_transformed_metrics = self._calculate_temperature_metrics(
                y_temp_transformed, temp_preds_transformed
            )
            
            # Also calculate metrics in the original space after inverse transformation
            temp_preds_orig = self._inverse_transform_temperature(temp_preds_transformed)
            temp_orig_metrics = self._calculate_temperature_metrics(y_temp, temp_preds_orig)
            
            # Use the better of the two R² values
            if temp_transformed_metrics["r2"] > temp_orig_metrics["r2"]:
                self.logger.info("Using metrics calculated in transformed space for temperature (better R²)")
                temp_metrics = temp_transformed_metrics
            else:
                self.logger.info("Using metrics calculated in original space for temperature (better R²)")
                temp_metrics = temp_orig_metrics
        
        # Always log temperature prediction stats in original scale for interpretability
        temp_preds_orig = temp_preds_transformed if self.temp_transform == "none" else self._inverse_transform_temperature(temp_preds_transformed)
        
        self.logger.info(f"Temperature prediction stats:")
        self.logger.info(f"  True values: min={np.min(y_temp):.4f}, max={np.max(y_temp):.4f}, mean={np.mean(y_temp):.4f}")
        self.logger.info(f"  Predicted values: min={np.min(temp_preds_orig):.4f}, max={np.max(temp_preds_orig):.4f}, mean={np.mean(temp_preds_orig):.4f}")
        
        # For growth rate, calculate metrics in transformed space (as before)
        from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
        growth_r2_transformed = r2_score(y_growth_transformed, growth_preds_transformed)
        growth_rmse_transformed = np.sqrt(mean_squared_error(y_growth_transformed, growth_preds_transformed))
        growth_mae_transformed = mean_absolute_error(y_growth_transformed, growth_preds_transformed)
        
        self.logger.info(f"Growth metrics in transformed space:")
        self.logger.info(f"  R²: {growth_r2_transformed:.6f}, RMSE: {growth_rmse_transformed:.6f}, MAE: {growth_mae_transformed:.6f}")
        
        # Inverse transform predictions for growth rate display
        growth_preds_orig = self._inverse_transform_growth_rate(growth_preds_transformed)
        
        # For comparison: Calculate growth metrics in original space
        growth_r2_orig = r2_score(y_growth, growth_preds_orig)
        growth_rmse_orig = np.sqrt(mean_squared_error(y_growth, growth_preds_orig))
        growth_mae_orig = mean_absolute_error(y_growth, growth_preds_orig)
        
        self.logger.info(f"Growth metrics in original space:")
        self.logger.info(f"  R²: {growth_r2_orig:.6f}, RMSE: {growth_rmse_orig:.6f}, MAE: {growth_mae_orig:.6f}")
        
        # Use transformed metrics for growth rate
        growth_metrics = {
            "r2": growth_r2_transformed,
            "rmse": growth_rmse_transformed,
            "mae": growth_mae_transformed
        }
        
        # Log final results with more detailed information
        self.logger.info("\n" + "="*60)
        self.logger.info("Final Model Evaluation Results:")
        self.logger.info(f"Temperature Model - R²: {temp_metrics['r2']:.6f}, Adj R²: {temp_metrics.get('adj_r2', 0):.6f}, RMSE: {temp_metrics['rmse']:.4f}, MAE: {temp_metrics['mae']:.4f}")
        self.logger.info(f"Growth Rate Model - R²: {growth_metrics['r2']:.6f}, RMSE: {growth_metrics['rmse']:.4f}, MAE: {growth_metrics['mae']:.4f}")
        
        # Detailed stats for final validation
        self.logger.info("Temperature prediction analysis:")
        self.logger.info(f"  True range: min={np.min(y_temp):.4f}, max={np.max(y_temp):.4f}, mean={np.mean(y_temp):.4f}")
        self.logger.info(f"  Pred range: min={np.min(temp_preds_orig):.4f}, max={np.max(temp_preds_orig):.4f}, mean={np.mean(temp_preds_orig):.4f}")
        
        self.logger.info("Growth rate prediction analysis:")
        self.logger.info(f"  True range (orig): min={np.min(y_growth):.4f}, max={np.max(y_growth):.4f}, mean={np.mean(y_growth):.4f}")
        self.logger.info(f"  Pred range (orig): min={np.min(growth_preds_orig):.4f}, max={np.max(growth_preds_orig):.4f}, mean={np.mean(growth_preds_orig):.4f}")
        self.logger.info(f"  True range (trans): min={np.min(y_growth_transformed):.4f}, max={np.max(y_growth_transformed):.4f}, mean={np.mean(y_growth_transformed):.4f}")
        self.logger.info(f"  Pred range (trans): min={np.min(growth_preds_transformed):.4f}, max={np.max(growth_preds_transformed):.4f}, mean={np.mean(growth_preds_transformed):.4f}")
        self.logger.info("\n" + "="*60)

        return {
            "temp_r2": temp_metrics["r2"],
            "temp_adj_r2": temp_metrics.get("adj_r2", temp_metrics["r2"]),
            "temp_rmse": temp_metrics["rmse"],
            "temp_mae": temp_metrics["mae"],
            "temp_correlation": temp_metrics.get("correlation", 0),
            "growth_r2": growth_metrics["r2"],
            "growth_rmse": growth_metrics["rmse"],
            "growth_mae": growth_metrics["mae"],
        }

#-----------------------------------------------------------------------------
# Command Line Interface
#-----------------------------------------------------------------------------

def main():
    """Main function to execute from the command line."""
    parser = argparse.ArgumentParser(description="Train and evaluate the hybrid model")
    parser.add_argument("--feature_file", required=True, help="Path to feature file")
    parser.add_argument("--metadata_file", required=True, help="Path to metadata file")
    parser.add_argument("--output_dir", default="models/hybrid_model", help="Directory to save outputs")
    parser.add_argument("--n_growth_features", type=int, default=250, help="Number of features for growth rate prediction")
    parser.add_argument("--n_temp_features", type=int, default=300, help="Number of features for temperature prediction")
    parser.add_argument("--seed", type=int, default=42, help="Random seed for reproducibility")
    parser.add_argument("--lr", type=float, default=0.001, help="Learning rate")
    parser.add_argument("--batch_size", type=int, default=64, help="Batch size")
    parser.add_argument("--epochs", type=int, default=200, help="Number of epochs")
    parser.add_argument("--patience", type=int, default=20, help="Patience for early stopping")
    parser.add_argument("--l1_lambda", type=float, default=1e-6, help="L1 regularization parameter")
    parser.add_argument("--weight_decay", type=float, default=1e-6, help="Weight decay (L2 regularization)")
    parser.add_argument("--mse_l1_ratio", type=float, default=0.9, help="Ratio of MSE to L1 loss")
    parser.add_argument("--hidden_dims", type=str, help="Comma-separated list of hidden dimensions")
    parser.add_argument("--dropout_rates", type=str, help="Comma-separated list of dropout rates")
    parser.add_argument("--activation", type=str, default="relu", help="Activation function (relu, leaky_relu, elu)")
    parser.add_argument("--use_batch_norm", action="store_true", help="Whether to use batch normalization")
    parser.add_argument("--use_residual", action="store_true", help="Whether to use residual connections")
    parser.add_argument("--use_attention", action="store_true", help="Whether to use self-attention")
    parser.add_argument("--use_transformer", action="store_true", help="Whether to use transformer encoder")
    parser.add_argument("--save_checkpoints", action="store_true", help="Whether to save checkpoints")
    parser.add_argument("--missing_threshold", type=float, default=0.5, help="Threshold for filtering features with missing values (0.0-1.0)")
    parser.add_argument("--predict_only", action="store_true", help="Only run prediction")
    parser.add_argument("--evaluate_only", action="store_true", help="Only run evaluation")
    parser.add_argument("--input_data", help="Path to input data for prediction")
    parser.add_argument("--model_dir", help="Directory containing saved model")
    parser.add_argument("--verbose", action="store_true", help="Enable verbose output")
    parser.add_argument("--temp_transform", type=str, default="none", choices=["none", "sqrt", "log2", "log10"], help="Transformation to apply to temperature values")
    parser.add_argument("--growth_transform", type=str, default="none", choices=["none", "sqrt", "log2", "log10"], help="Transformation to apply to growth rate values")
    args = parser.parse_args()
    
    # Set up output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Set random seed for reproducibility
    np.random.seed(args.seed)
    torch.manual_seed(args.seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(args.seed)
    
    # Set deterministic behavior
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    
    # Parse hidden dims and dropout rates from string inputs
    if args.hidden_dims:
        hidden_dims = [int(dim) for dim in args.hidden_dims.split(',')]
    else:
        hidden_dims = [512, 384, 256, 128]
    
    if args.dropout_rates:
        dropout_rates = [float(rate) for rate in args.dropout_rates.split(',')]
    else:
        dropout_rates = [0.3, 0.4, 0.4, 0.5]
    
    # Load data first to get the correct input dimension
    features, metadata = load_data(args.feature_file, args.metadata_file, missing_threshold=args.missing_threshold)
    
    # Set up hybrid model
    model = FinalHybridModel(
        n_growth_features=args.n_growth_features,
        n_temp_features=args.n_temp_features,
        output_dir=args.output_dir,
        temp_transform=args.temp_transform,
        growth_transform=args.growth_transform
    )
    
    # Set temperature model parameters if provided
    if args.hidden_dims or args.dropout_rates or args.activation != "relu" or args.use_batch_norm or args.use_residual or args.use_attention or args.use_transformer:
        model.set_temp_model_params({
            'hidden_dims': hidden_dims,
            'dropout_rates': dropout_rates,
            'activation': args.activation,
            'use_batch_norm': args.use_batch_norm,
            'use_residual': args.use_residual,
            'use_attention': args.use_attention,
            'use_transformer': args.use_transformer,
            'l1_lambda': args.l1_lambda,
            'weight_decay': args.weight_decay,
            'mse_l1_ratio': args.mse_l1_ratio,
            'save_checkpoints': args.save_checkpoints,
            'input_dim': features.shape[1]  # Pass the actual input dimension
        })
    
    # Handle prediction only
    if args.predict_only:
        if not args.model_dir:
            raise ValueError("--model_dir is required for prediction")
        
        # Load model
        model.load(args.model_dir)
        
        # Load input data
        if not args.input_data:
            raise ValueError("--input_data is required for prediction")
        
        features = pd.read_csv(args.input_data, sep='\t', index_col='genome_id')
        
        # Make predictions
        predictions = model.predict(features)
        
        # Save predictions
        predictions_df = pd.DataFrame({
            'genome_id': features.index,
            'growth_rate': predictions['growth_rate'],
            'optimal_temperature': predictions['optimal_temperature']
        })
        
        predictions_df.to_csv(os.path.join(args.output_dir, 'predictions.tsv'), sep='\t', index=False)
        
        logger.info(f"Predictions saved to {os.path.join(args.output_dir, 'predictions.tsv')}")
        
        return
    
    # Handle evaluation only
    if args.evaluate_only:
        if not args.model_dir:
            raise ValueError("--model_dir is required for evaluation")
        
        # Load model
        model.load(args.model_dir)
        
        # Evaluate model
        metrics = model.evaluate(
            features,
            metadata['growth_rate'],
            metadata['optimal_temperature'],
            scaled=True
        )
        
        # Print metrics
        for target, target_metrics in metrics.items():
            logger.info(f"{target.upper()} METRICS:")
            for metric, value in target_metrics.items():
                logger.info(f"  {metric}: {value:.4f}")
        
        # Save metrics
        metrics_df = pd.DataFrame({
            'target': [],
            'metric': [],
            'value': []
        })
        
        for target, target_metrics in metrics.items():
            for metric, value in target_metrics.items():
                metrics_df = pd.concat([metrics_df, pd.DataFrame({
                    'target': [target],
                    'metric': [metric],
                    'value': [value]
                })])
        
        metrics_df.to_csv(os.path.join(args.output_dir, 'evaluation_metrics.tsv'), sep='\t', index=False)
        
        logger.info(f"Metrics saved to {os.path.join(args.output_dir, 'evaluation_metrics.tsv')}")
        
        return
    
    # Train model
    model.fit(features, metadata['growth_rate'], metadata['optimal_temperature'])
    
    # Save trained model
    model.save(args.output_dir)
    
    logger.info(f"Model saved to {args.output_dir}")

if __name__ == "__main__":
    main() 