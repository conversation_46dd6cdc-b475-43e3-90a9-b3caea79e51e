#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Predict with Optimized Models.
This script uses the best models for growth rate and temperature prediction,
with proper handling of transformations and outliers.
"""

import os
import argparse
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import torch
import joblib
import logging
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error

# Import model classes
from evaluate_enhanced_dnn_temperature import RegularizedDNN as TempDNN
import torch.nn as nn

# Custom model class for growth rate neural network
class GrowthRateDNN(nn.Module):
    """
    Custom neural network for growth rate prediction.
    """
    def __init__(self, input_dim, hidden_dims=[512, 512, 384, 256, 128], dropout_rates=[0.3, 0.4, 0.4, 0.5, 0.5],
                 use_batch_norm=True, activation='relu'):
        super(GrowthRateDNN, self).__init__()

        # Create layers
        layers = []
        prev_dim = input_dim

        # Choose activation function
        if activation == 'relu':
            act_fn = nn.ReLU()
        elif activation == 'leaky_relu':
            act_fn = nn.LeakyReLU(0.1)
        elif activation == 'elu':
            act_fn = nn.ELU()
        else:
            act_fn = nn.ReLU()

        # Build network with progressively increasing dropout
        for i, hidden_dim in enumerate(hidden_dims):
            # Linear layer
            layers.append(nn.Linear(prev_dim, hidden_dim))

            # Batch normalization (before activation)
            if use_batch_norm:
                layers.append(nn.BatchNorm1d(hidden_dim))

            # Activation
            layers.append(act_fn)

            # Dropout with increasing rate for deeper layers
            dropout_rate = dropout_rates[min(i, len(dropout_rates)-1)]
            layers.append(nn.Dropout(dropout_rate))

            prev_dim = hidden_dim

        self.feature_extractor = nn.Sequential(*layers)

        # Output layers for growth rate and temperature
        self.growth_output = nn.Linear(prev_dim, 1)
        self.temp_output = nn.Linear(prev_dim, 1)

    def forward(self, x):
        features = self.feature_extractor(x)
        growth_pred = self.growth_output(features)
        return growth_pred

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_data(feature_file, metadata_file, filter_outliers=True, percentile=99.0):
    """
    Load and prepare feature and metadata files.

    Args:
        feature_file: Path to feature file
        metadata_file: Path to metadata file
        filter_outliers: Whether to filter outliers in growth rate
        percentile: Percentile threshold for filtering outliers

    Returns:
        Tuple of (features_df, metadata_df)
    """
    logger.info(f"Loading features from {feature_file}")
    features_df = pd.read_csv(feature_file, sep='\t', index_col='genome_id')

    # Check for NaN values in features
    nan_count = features_df.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in features. Filling with 0.")
        features_df = features_df.fillna(0)

    # Check for infinite values
    inf_count = np.isinf(features_df.values).sum()
    if inf_count > 0:
        logger.warning(f"Found {inf_count} infinite values in features. Replacing with max/min values.")
        features_df = features_df.replace([np.inf, -np.inf], np.nan)
        features_df = features_df.fillna(features_df.max())

    logger.info(f"Loading metadata from {metadata_file}")
    metadata_df = pd.read_csv(metadata_file, sep='\t', index_col='genome_id')

    # Check for NaN values in metadata
    nan_count = metadata_df.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in metadata. Filling with 0.")
        metadata_df = metadata_df.fillna(0)

    # Get common genomes
    common_genomes = list(set(features_df.index) & set(metadata_df.index))
    logger.info(f"Found {len(common_genomes)} common genomes")

    # Filter to common genomes
    features_df = features_df.loc[common_genomes]
    metadata_df = metadata_df.loc[common_genomes]

    # Filter outliers in growth rate if requested
    if filter_outliers:
        threshold = np.percentile(metadata_df['growth_rate'], percentile)
        logger.info(f"Filtering growth rate values above {threshold:.4f} (percentile {percentile})")

        # Count outliers
        outlier_count = (metadata_df['growth_rate'] > threshold).sum()
        logger.info(f"Found {outlier_count} outliers in growth rate")

        # Log statistics before filtering
        logger.info(f"Growth rate statistics before filtering - Min: {metadata_df['growth_rate'].min():.4f}, Max: {metadata_df['growth_rate'].max():.4f}, Mean: {metadata_df['growth_rate'].mean():.4f}, Median: {metadata_df['growth_rate'].median():.4f}")

        # Filter out outliers
        outlier_indices = metadata_df[metadata_df['growth_rate'] > threshold].index
        metadata_df = metadata_df[metadata_df['growth_rate'] <= threshold]
        features_df = features_df.drop(outlier_indices, errors='ignore')

        # Log statistics after filtering
        logger.info(f"Growth rate statistics after filtering - Min: {metadata_df['growth_rate'].min():.4f}, Max: {metadata_df['growth_rate'].max():.4f}, Mean: {metadata_df['growth_rate'].mean():.4f}, Median: {metadata_df['growth_rate'].median():.4f}")

    return features_df, metadata_df

class OptimizedModelsPredictor:
    """
    Predictor using optimized models for growth rate and temperature prediction.
    """
    def __init__(self, temp_model_dir, growth_model_dir):
        """
        Initialize predictor with model directories.

        Args:
            temp_model_dir: Directory containing temperature model
            growth_model_dir: Directory containing growth rate model
        """
        self.temp_model_dir = temp_model_dir
        self.growth_model_dir = growth_model_dir
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

        # Load models
        self._load_temperature_model()
        self._load_growth_model()

    def _load_temperature_model(self):
        """Load temperature model components."""
        logger.info(f"Loading temperature model from {self.temp_model_dir}")

        # Load scaler
        self.temp_scaler = joblib.load(os.path.join(self.temp_model_dir, 'temperature_scaler.joblib'))
        logger.info(f"Temperature scaler loaded successfully")

        # Get the correct input dimension from the model file
        model_state = torch.load(
            os.path.join(self.temp_model_dir, 'temperature_model.pt'),
            map_location=self.device
        )

        # Extract input dimension from the first layer's weight shape
        input_dim = model_state['feature_extractor.0.weight'].shape[1]
        logger.info(f"Using input dimension of {input_dim} for temperature model")

        # Create model with the correct architecture
        self.temp_model = TempDNN(
            input_dim=input_dim,
            hidden_dims=[512, 512, 384, 256, 128],
            dropout_rates=[0.3, 0.4, 0.4, 0.5, 0.5],
            use_batch_norm=True,
            activation='relu'
        )

        # Load weights
        self.temp_model.load_state_dict(torch.load(
            os.path.join(self.temp_model_dir, 'temperature_model.pt'),
            map_location=self.device
        ))

        # Set model to evaluation mode
        self.temp_model.eval()
        self.temp_model.to(self.device)

        logger.info(f"Temperature model loaded successfully")

    def _load_growth_model(self):
        """Load growth rate model components."""
        logger.info(f"Loading growth rate model from {self.growth_model_dir}")

        # Load feature scaler
        self.growth_feature_scaler = joblib.load(os.path.join(self.growth_model_dir, 'feature_scaler.joblib'))
        logger.info(f"Growth rate feature scaler loaded successfully")

        # Load Random Forest model
        rf_dir = os.path.join(self.growth_model_dir, 'rf_models')
        self.growth_rf = joblib.load(os.path.join(rf_dir, 'rf_growth_model.joblib'))
        logger.info(f"Random Forest model loaded successfully")

        # Load XGBoost model
        xgb_dir = os.path.join(self.growth_model_dir, 'xgb_models')
        self.growth_xgb = joblib.load(os.path.join(xgb_dir, 'xgb_growth_model.joblib'))
        logger.info(f"XGBoost model loaded successfully")

        # Load LightGBM model
        lgb_dir = os.path.join(self.growth_model_dir, 'lgb_models')
        self.growth_lgb = joblib.load(os.path.join(lgb_dir, 'lgb_growth_model.joblib'))
        logger.info(f"LightGBM model loaded successfully")

        # Load neural network model if available
        nn_dir = os.path.join(self.growth_model_dir, 'nn_models')
        if os.path.exists(os.path.join(nn_dir, 'enhanced_nn_model.pth')):
            # Load NN model architecture
            nn_hyperparams = joblib.load(os.path.join(nn_dir, 'hyperparams.joblib'))

            # Get the correct input dimension from the model file
            nn_model_state = torch.load(
                os.path.join(nn_dir, 'enhanced_nn_model.pth'),
                map_location=self.device
            )

            # Extract input dimension from the first layer's weight shape
            try:
                input_dim = nn_model_state['feature_extractor.0.weight'].shape[1]
                logger.info(f"Using input dimension of {input_dim} for growth rate NN model")
            except:
                # Fallback to hyperparams
                input_dim = nn_hyperparams.get('input_dim', 150)
                logger.info(f"Using input dimension of {input_dim} from hyperparams for growth rate NN model")

            # Create NN model
            self.growth_nn = GrowthRateDNN(
                input_dim=input_dim,
                hidden_dims=nn_hyperparams.get('hidden_dims', [512, 512, 384, 256, 128]),
                dropout_rates=nn_hyperparams.get('dropout_rates', [0.3, 0.4, 0.4, 0.5, 0.5]),
                use_batch_norm=nn_hyperparams.get('use_batch_norm', True),
                activation=nn_hyperparams.get('activation', 'relu')
            )

            # Load weights
            self.growth_nn.load_state_dict(torch.load(
                os.path.join(nn_dir, 'enhanced_nn_model.pth'),
                map_location=self.device
            ))

            # Set model to evaluation mode
            self.growth_nn.eval()
            self.growth_nn.to(self.device)

            # Load NN feature scaler
            self.growth_nn_scaler = joblib.load(os.path.join(nn_dir, 'feature_scaler.joblib'))

            logger.info(f"Neural network model loaded successfully")
            self.has_nn_model = True
        else:
            logger.info(f"Neural network model not found")
            self.has_nn_model = False

        # Load ensemble weights
        try:
            weights = torch.load(
                os.path.join(self.growth_model_dir, 'growth_weights.pth'),
                map_location=self.device
            )

            # If we have a neural network model but the weights don't include it
            if self.has_nn_model and len(weights) == 3:
                # Adjust weights to include NN (reduce other weights by 25%)
                adjusted_weights = weights * 0.75
                # Add NN weight (25% of total)
                self.growth_weights = torch.cat([adjusted_weights, torch.tensor([0.25], device=self.device)])
                logger.info(f"Adjusted ensemble weights to include NN: {self.growth_weights.cpu().numpy()}")
            else:
                self.growth_weights = weights
                logger.info(f"Ensemble weights loaded successfully: {self.growth_weights.cpu().numpy()}")
        except:
            # Use adjusted weights as fallback
            if self.has_nn_model:
                # Add a fourth weight for the neural network
                original_weights = torch.tensor([0.33, 0.33, 0.34], device=self.device)
                # Adjust weights to include NN (reduce other weights by 25%)
                adjusted_weights = original_weights * 0.75
                # Add NN weight (25% of total)
                self.growth_weights = torch.cat([adjusted_weights, torch.tensor([0.25], device=self.device)])
            else:
                self.growth_weights = torch.tensor([0.33, 0.33, 0.34], device=self.device)
            logger.info(f"Ensemble weights not found, using adjusted weights: {self.growth_weights.cpu().numpy()}")

        # Load transform info
        self.transform_info = joblib.load(os.path.join(self.growth_model_dir, 'transform_info.joblib'))
        logger.info(f"Transform info loaded successfully: {self.transform_info}")

        logger.info(f"Growth rate model components loaded successfully")

    def predict_temperature(self, features):
        """
        Predict optimal temperature.

        Args:
            features: Feature DataFrame

        Returns:
            Array of temperature predictions
        """
        logger.info(f"Predicting optimal temperature for {len(features)} samples")

        # Get feature matrix - exclude metadata columns
        X_filtered = features.drop(['growth_rate', 'optimal_temperature', 'taxid', 'codon_table', 'kingdom'],
                                  axis=1, errors='ignore')

        # Check if we need to filter features to match the model's expected input
        expected_features = self.temp_model.feature_extractor[0].weight.shape[1]
        if X_filtered.shape[1] != expected_features:
            logger.warning(f"Feature mismatch for temperature model: model expects {expected_features} features, but got {X_filtered.shape[1]}")

            # Try to load feature names from the model
            try:
                # Get feature names from the model
                feature_names = joblib.load(os.path.join(self.temp_model_dir, 'feature_names.joblib'))
                logger.info(f"Loaded {len(feature_names)} feature names from the temperature model")

                # Filter features to match the model
                common_features = [f for f in feature_names if f in X_filtered.columns]
                logger.info(f"Found {len(common_features)} common features")

                if len(common_features) == expected_features:
                    X_filtered = X_filtered[common_features]
                    logger.info(f"Filtered features to match the temperature model: {X_filtered.shape[1]} features")
                else:
                    # If we can't match exactly, use the first n features
                    logger.warning(f"Could not match feature names exactly. Using first {expected_features} features.")
                    X_filtered = X_filtered.iloc[:, :expected_features]
            except:
                # If we can't load feature names, use the first n features
                logger.warning(f"Could not load feature names. Using first {expected_features} features.")
                X_filtered = X_filtered.iloc[:, :expected_features]

        # Scale features
        X_scaled = self.temp_scaler.transform(X_filtered.values)

        # Convert to tensor
        X_tensor = torch.tensor(X_scaled, dtype=torch.float32).to(self.device)

        # Set model to evaluation mode
        self.temp_model.eval()

        # Generate predictions
        with torch.no_grad():
            y_pred = self.temp_model(X_tensor)
            y_pred = y_pred.cpu().numpy()

        logger.info(f"Temperature predictions - Min: {y_pred.min():.4f}, Max: {y_pred.max():.4f}, Mean: {y_pred.mean():.4f}")

        return y_pred

    def predict_growth(self, features):
        """
        Predict growth rate.

        Args:
            features: Feature DataFrame

        Returns:
            Array of growth rate predictions
        """
        logger.info(f"Predicting growth rate for {len(features)} samples")

        # Get feature matrix - exclude metadata columns
        X_filtered = features.drop(['growth_rate', 'optimal_temperature', 'taxid', 'codon_table', 'kingdom'],
                                  axis=1, errors='ignore')

        # Check if we need to filter features to match the model's expected input
        expected_features = self.growth_feature_scaler.n_features_in_
        if X_filtered.shape[1] != expected_features:
            logger.warning(f"Feature mismatch: model expects {expected_features} features, but got {X_filtered.shape[1]}")

            # Try to load feature names from the model
            try:
                # Get feature names from the model
                feature_names = joblib.load(os.path.join(self.growth_model_dir, 'feature_names.joblib'))
                logger.info(f"Loaded {len(feature_names)} feature names from the model")

                # Filter features to match the model
                common_features = [f for f in feature_names if f in X_filtered.columns]
                logger.info(f"Found {len(common_features)} common features")

                if len(common_features) == expected_features:
                    X_filtered = X_filtered[common_features]
                    logger.info(f"Filtered features to match the model: {X_filtered.shape[1]} features")
                else:
                    # If we can't match exactly, use the first n features
                    logger.warning(f"Could not match feature names exactly. Using first {expected_features} features.")
                    X_filtered = X_filtered.iloc[:, :expected_features]
            except:
                # If we can't load feature names, use the first n features
                logger.warning(f"Could not load feature names. Using first {expected_features} features.")
                X_filtered = X_filtered.iloc[:, :expected_features]

        # Scale features
        X_scaled = self.growth_feature_scaler.transform(X_filtered.values)

        # Get predictions from Random Forest
        rf_pred = self.growth_rf.predict(X_scaled)
        logger.info(f"RF predictions - Min: {rf_pred.min():.4f}, Max: {rf_pred.max():.4f}, Mean: {rf_pred.mean():.4f}")

        # Get predictions from XGBoost
        xgb_pred = self.growth_xgb.predict(X_scaled)
        logger.info(f"XGB predictions - Min: {xgb_pred.min():.4f}, Max: {xgb_pred.max():.4f}, Mean: {xgb_pred.mean():.4f}")

        # Get predictions from LightGBM
        lgb_pred = self.growth_lgb.predict(X_scaled)
        logger.info(f"LGB predictions - Min: {lgb_pred.min():.4f}, Max: {lgb_pred.max():.4f}, Mean: {lgb_pred.mean():.4f}")

        # Get predictions from Neural Network if available
        if self.has_nn_model:
            # Scale features for NN
            X_nn_scaled = self.growth_nn_scaler.transform(X_filtered.values)

            # Convert to tensor
            X_nn_tensor = torch.tensor(X_nn_scaled, dtype=torch.float32).to(self.device)

            # Generate predictions
            with torch.no_grad():
                nn_pred = self.growth_nn(X_nn_tensor)
                nn_pred = nn_pred.cpu().numpy().flatten()

            logger.info(f"NN predictions - Min: {nn_pred.min():.4f}, Max: {nn_pred.max():.4f}, Mean: {nn_pred.mean():.4f}")

        # Combine predictions using ensemble weights
        if isinstance(self.growth_weights, torch.Tensor):
            weights = torch.softmax(self.growth_weights, dim=0).cpu().numpy()
        else:
            # If weights are already numpy array
            weights = self.growth_weights / np.sum(self.growth_weights)

        if self.has_nn_model:
            logger.info(f"Normalized weights: RF={weights[0]:.4f}, XGB={weights[1]:.4f}, LGB={weights[2]:.4f}, NN={weights[3]:.4f}")

            combined_pred = (
                weights[0] * rf_pred +
                weights[1] * xgb_pred +
                weights[2] * lgb_pred +
                weights[3] * nn_pred
            )
        else:
            logger.info(f"Normalized weights: RF={weights[0]:.4f}, XGB={weights[1]:.4f}, LGB={weights[2]:.4f}")

            combined_pred = (
                weights[0] * rf_pred +
                weights[1] * xgb_pred +
                weights[2] * lgb_pred
            )

        # Apply inverse transformation if needed
        if self.transform_info.get('growth_sqrt_transform', False):
            logger.info("Applying inverse square root transformation to growth rate predictions")
            combined_pred = combined_pred ** 2

        logger.info(f"Combined growth rate predictions - Min: {combined_pred.min():.4f}, Max: {combined_pred.max():.4f}, Mean: {combined_pred.mean():.4f}")

        return combined_pred

    def predict(self, features, target='both'):
        """
        Generate predictions for the specified target.

        Args:
            features: Feature DataFrame
            target: Prediction target ('growth', 'temperature', or 'both')

        Returns:
            Predictions for the specified target
        """
        if target == 'growth':
            return self.predict_growth(features)
        elif target == 'temperature':
            return self.predict_temperature(features)
        else:  # both
            growth_pred = self.predict_growth(features)
            temp_pred = self.predict_temperature(features)
            return growth_pred, temp_pred

    def evaluate(self, features, y_growth=None, y_temp=None, target='both'):
        """
        Evaluate model performance.

        Args:
            features: Feature DataFrame
            y_growth: Growth rate targets
            y_temp: Temperature targets
            target: Evaluation target ('growth', 'temperature', or 'both')

        Returns:
            Dictionary with evaluation metrics
        """
        metrics = {}

        if target in ['growth', 'both'] and y_growth is not None:
            # Predict growth rate
            y_pred_growth = self.predict_growth(features)

            # Calculate metrics
            growth_mse = mean_squared_error(y_growth, y_pred_growth)
            growth_rmse = np.sqrt(growth_mse)
            growth_r2 = r2_score(y_growth, y_pred_growth)
            growth_mae = mean_absolute_error(y_growth, y_pred_growth)

            # Calculate metrics on log scale for better evaluation
            log_growth_r2 = r2_score(np.log1p(y_growth), np.log1p(y_pred_growth))
            log_growth_rmse = np.sqrt(mean_squared_error(np.log1p(y_growth), np.log1p(y_pred_growth)))
            log_growth_mae = mean_absolute_error(np.log1p(y_growth), np.log1p(y_pred_growth))

            metrics['growth_rate'] = {
                'MSE': growth_mse,
                'RMSE': growth_rmse,
                'R2': growth_r2,
                'MAE': growth_mae,
                'Log_R2': log_growth_r2,
                'Log_RMSE': log_growth_rmse,
                'Log_MAE': log_growth_mae
            }

            logger.info(f"Growth Rate: R²={growth_r2:.4f}, RMSE={growth_rmse:.4f}, MAE={growth_mae:.4f}")
            logger.info(f"Growth Rate (log scale): R²={log_growth_r2:.4f}, RMSE={log_growth_rmse:.4f}, MAE={log_growth_mae:.4f}")

        if target in ['temperature', 'both'] and y_temp is not None:
            # Predict temperature
            y_pred_temp = self.predict_temperature(features)

            # Calculate metrics
            temp_mse = mean_squared_error(y_temp, y_pred_temp)
            temp_rmse = np.sqrt(temp_mse)
            temp_r2 = r2_score(y_temp, y_pred_temp)
            temp_mae = mean_absolute_error(y_temp, y_pred_temp)

            metrics['temperature'] = {
                'MSE': temp_mse,
                'RMSE': temp_rmse,
                'R2': temp_r2,
                'MAE': temp_mae
            }

            logger.info(f"Temperature: R²={temp_r2:.4f}, RMSE={temp_rmse:.4f}, MAE={temp_mae:.4f}")

        if target == 'both' and y_growth is not None and y_temp is not None:
            # Calculate overall metrics
            overall_r2 = (metrics['growth_rate']['R2'] + metrics['temperature']['R2']) / 2
            overall_rmse = (metrics['growth_rate']['RMSE'] + metrics['temperature']['RMSE']) / 2
            overall_mae = (metrics['growth_rate']['MAE'] + metrics['temperature']['MAE']) / 2

            metrics['overall'] = {
                'R2': overall_r2,
                'RMSE': overall_rmse,
                'MAE': overall_mae
            }

            logger.info(f"Overall: R²={overall_r2:.4f}, RMSE={overall_rmse:.4f}, MAE={overall_mae:.4f}")

        return metrics

def main():
    parser = argparse.ArgumentParser(description="Predict with optimized models")
    parser.add_argument("--feature_file", required=True, help="Path to feature file")
    parser.add_argument("--metadata_file", required=True, help="Path to metadata file")
    parser.add_argument("--output_dir", default="predictions_optimized", help="Directory to save predictions")
    parser.add_argument("--temp_model_dir", default="models/enhanced_dnn_temperature_v2", help="Directory containing the temperature model")
    parser.add_argument("--growth_model_dir", default="models/hybrid_enhanced_nn_v1", help="Directory containing the growth rate model")
    parser.add_argument("--target", default="both", choices=["growth", "temperature", "both"], help="Prediction target")
    parser.add_argument("--filter_outliers", action="store_true", help="Filter outliers in growth rate")
    parser.add_argument("--percentile", type=float, default=99.0, help="Percentile threshold for filtering outliers")
    args = parser.parse_args()

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Load data
    features, metadata = load_data(
        args.feature_file,
        args.metadata_file,
        filter_outliers=args.filter_outliers,
        percentile=args.percentile
    )

    # Extract targets
    if args.target in ['growth', 'both']:
        y_growth = metadata['growth_rate']

    if args.target in ['temperature', 'both']:
        y_temp = metadata['optimal_temperature']

    # Initialize predictor
    predictor = OptimizedModelsPredictor(
        temp_model_dir=args.temp_model_dir,
        growth_model_dir=args.growth_model_dir
    )

    # Generate predictions
    if args.target == 'growth':
        y_growth_pred = predictor.predict(features, target='growth')

        # Save predictions
        pd.DataFrame({
            'genome_id': features.index,
            'actual_growth': y_growth,
            'predicted_growth': y_growth_pred
        }).to_csv(os.path.join(args.output_dir, 'growth_predictions.tsv'), sep='\t', index=False)

        # Plot predictions
        plt.figure(figsize=(12, 5))

        # Plot with original scale
        plt.subplot(1, 2, 1)
        plt.scatter(y_growth, y_growth_pred, alpha=0.5)
        plt.plot([min(y_growth), max(y_growth)], [min(y_growth), max(y_growth)], 'r--')
        plt.xlabel('Actual Growth Rate')
        plt.ylabel('Predicted Growth Rate')
        plt.title('Growth Rate Predictions (Original Scale)')

        # Plot with log scale for better visualization
        plt.subplot(1, 2, 2)
        plt.scatter(np.log1p(y_growth), np.log1p(y_growth_pred), alpha=0.5)
        plt.plot([min(np.log1p(y_growth)), max(np.log1p(y_growth))],
                 [min(np.log1p(y_growth)), max(np.log1p(y_growth))], 'r--')
        plt.xlabel('Actual Growth Rate (log scale)')
        plt.ylabel('Predicted Growth Rate (log scale)')
        plt.title('Growth Rate Predictions (Log Scale)')

        plt.tight_layout()
        plt.savefig(os.path.join(args.output_dir, 'growth_predictions.png'))

        # Evaluate model
        metrics = predictor.evaluate(features, y_growth=y_growth, target='growth')

        # Save metrics
        pd.DataFrame({
            'metric': list(metrics['growth_rate'].keys()),
            'value': list(metrics['growth_rate'].values())
        }).to_csv(os.path.join(args.output_dir, 'growth_metrics.tsv'), sep='\t', index=False)

    elif args.target == 'temperature':
        y_temp_pred = predictor.predict(features, target='temperature')

        # Save predictions
        pd.DataFrame({
            'genome_id': features.index,
            'actual_temp': y_temp,
            'predicted_temp': y_temp_pred
        }).to_csv(os.path.join(args.output_dir, 'temperature_predictions.tsv'), sep='\t', index=False)

        # Plot predictions
        plt.figure(figsize=(10, 6))
        plt.scatter(y_temp, y_temp_pred, alpha=0.5)
        plt.plot([min(y_temp), max(y_temp)], [min(y_temp), max(y_temp)], 'r--')
        plt.xlabel('Actual Temperature')
        plt.ylabel('Predicted Temperature')
        plt.title('Temperature Predictions')
        plt.tight_layout()
        plt.savefig(os.path.join(args.output_dir, 'temperature_predictions.png'))

        # Evaluate model
        metrics = predictor.evaluate(features, y_temp=y_temp, target='temperature')

        # Save metrics
        pd.DataFrame({
            'metric': list(metrics['temperature'].keys()),
            'value': list(metrics['temperature'].values())
        }).to_csv(os.path.join(args.output_dir, 'temperature_metrics.tsv'), sep='\t', index=False)

    else:  # both
        y_growth_pred, y_temp_pred = predictor.predict(features, target='both')

        # Save predictions
        pd.DataFrame({
            'genome_id': features.index,
            'actual_growth': y_growth,
            'predicted_growth': y_growth_pred,
            'actual_temp': y_temp,
            'predicted_temp': y_temp_pred
        }).to_csv(os.path.join(args.output_dir, 'predictions.tsv'), sep='\t', index=False)

        # Plot predictions
        plt.figure(figsize=(12, 10))

        # Growth rate - original scale
        plt.subplot(2, 2, 1)
        plt.scatter(y_growth, y_growth_pred, alpha=0.5)
        plt.plot([min(y_growth), max(y_growth)], [min(y_growth), max(y_growth)], 'r--')
        plt.xlabel('Actual Growth Rate')
        plt.ylabel('Predicted Growth Rate')
        plt.title('Growth Rate Predictions (Original Scale)')

        # Growth rate - log scale
        plt.subplot(2, 2, 2)
        plt.scatter(np.log1p(y_growth), np.log1p(y_growth_pred), alpha=0.5)
        plt.plot([min(np.log1p(y_growth)), max(np.log1p(y_growth))],
                 [min(np.log1p(y_growth)), max(np.log1p(y_growth))], 'r--')
        plt.xlabel('Actual Growth Rate (log scale)')
        plt.ylabel('Predicted Growth Rate (log scale)')
        plt.title('Growth Rate Predictions (Log Scale)')

        # Temperature
        plt.subplot(2, 2, 3)
        plt.scatter(y_temp, y_temp_pred, alpha=0.5)
        plt.plot([min(y_temp), max(y_temp)], [min(y_temp), max(y_temp)], 'r--')
        plt.xlabel('Actual Temperature')
        plt.ylabel('Predicted Temperature')
        plt.title('Temperature Predictions')

        plt.tight_layout()
        plt.savefig(os.path.join(args.output_dir, 'predictions.png'))

        # Evaluate model
        metrics = predictor.evaluate(features, y_growth=y_growth, y_temp=y_temp, target='both')

        # Save metrics
        pd.DataFrame({
            'metric': ['MSE', 'RMSE', 'R2', 'MAE', 'Log_R2', 'Log_RMSE', 'Log_MAE'],
            'growth_rate': [
                metrics['growth_rate'].get('MSE', None),
                metrics['growth_rate'].get('RMSE', None),
                metrics['growth_rate'].get('R2', None),
                metrics['growth_rate'].get('MAE', None),
                metrics['growth_rate'].get('Log_R2', None),
                metrics['growth_rate'].get('Log_RMSE', None),
                metrics['growth_rate'].get('Log_MAE', None)
            ],
            'temperature': [
                metrics['temperature'].get('MSE', None),
                metrics['temperature'].get('RMSE', None),
                metrics['temperature'].get('R2', None),
                metrics['temperature'].get('MAE', None),
                None, None, None
            ],
            'overall': [
                None,
                metrics['overall'].get('RMSE', None),
                metrics['overall'].get('R2', None),
                metrics['overall'].get('MAE', None),
                None, None, None
            ]
        }).to_csv(os.path.join(args.output_dir, 'metrics.tsv'), sep='\t', index=False)

    logger.info(f"Predictions saved to {args.output_dir}")

if __name__ == "__main__":
    main()
