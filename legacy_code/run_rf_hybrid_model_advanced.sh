#!/bin/bash
# Training script for DeepMu with advanced RF hybrid model using Bayesian optimization,
# enhanced feature engineering, and model stacking

# Set paths
METADATA="training_data/metadata.tsv"
FEATURE_FILE="./features/combined_features.tsv"  # Use the combined features TSV file
OUTPUT_DIR="models/rf_hybrid_model_advanced"
METRICS_DIR="metrics/rf_hybrid_model_advanced"

# Create necessary directories
mkdir -p $METRICS_DIR
mkdir -p $OUTPUT_DIR

# Install required packages if not already installed
pip install scikit-optimize xgboost lightgbm optuna shap

# Set advanced parameters
BAYESIAN_ITERATIONS=50  # Number of iterations for Bayesian optimization
MAX_ESTIMATORS=1000  # Maximum number of estimators to consider
GROWTH_RATE_FEATURES=100  # Number of features to select for growth rate
OPTIMAL_TEMPERATURE_FEATURES=80  # Number of features to select for optimal temperature
INTERACTION_DEPTH=3  # Create interactions up to 3-way (triplets)
MAX_INTERACTIONS=150  # Maximum number of interaction features to create
CV_FOLDS=5  # Number of cross-validation folds
SEED=42

# Make the training script executable
chmod +x train_rf_hybrid_model_advanced.py

# Run the training
echo "Training advanced RF hybrid model with Bayesian optimization, enhanced feature engineering, and model stacking..."
python train_rf_hybrid_model_advanced.py \
    --metadata "$METADATA" \
    --feature-file "$FEATURE_FILE" \
    --output-dir "$OUTPUT_DIR" \
    --metrics-dir "$METRICS_DIR" \
    --bayesian-iterations "$BAYESIAN_ITERATIONS" \
    --max-estimators "$MAX_ESTIMATORS" \
    --growth-rate-features "$GROWTH_RATE_FEATURES" \
    --optimal-temperature-features "$OPTIMAL_TEMPERATURE_FEATURES" \
    --interaction-depth "$INTERACTION_DEPTH" \
    --max-interactions "$MAX_INTERACTIONS" \
    --cv-folds "$CV_FOLDS" \
    --seed "$SEED" \
    --target "both" \
    --use-stacking \
    --use-shap \
    --use-feature-selection \
    --use-advanced-interactions

echo "Training complete!"

# Generate comparison report
echo "Generating comparison report..."
python -c "
import os
import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('comparison_report')

# Define metrics directories
metrics_dirs = {
    'RF Hybrid Model': 'metrics/rf_hybrid_model',
    'RF Hybrid Model with RFE': 'metrics/rf_hybrid_model_rfe',
    'Advanced RF Hybrid Model': 'metrics/rf_hybrid_model_advanced'
}

# Collect metrics
comparison_data = {}

for model_name, metrics_dir in metrics_dirs.items():
    metrics_file = Path(metrics_dir) / 'training_metrics.json'
    if metrics_file.exists():
        try:
            with open(metrics_file, 'r') as f:
                metrics = json.load(f)

            # Extract metrics
            if isinstance(metrics, dict) and 'val_metrics' in metrics:
                # New format with val_metrics list
                val_metrics = metrics['val_metrics'][-1] if len(metrics['val_metrics']) > 0 else {}
                
                model_metrics = {}
                
                # Look for different metric naming patterns
                for metric_name in ['growth_rate_r2', 'growth_rate_mse', 'optimal_temperature_r2', 'optimal_temperature_mse']:
                    if metric_name in val_metrics:
                        model_metrics[metric_name] = val_metrics[metric_name]
                    elif metric_name.replace('growth_rate', 'growth') in val_metrics:
                        model_metrics[metric_name] = val_metrics[metric_name.replace('growth_rate', 'growth')]
                    elif metric_name.replace('optimal_temperature', 'temperature') in val_metrics:
                        model_metrics[metric_name] = val_metrics[metric_name.replace('optimal_temperature', 'temperature')]
                
                comparison_data[model_name] = model_metrics
                
            elif isinstance(metrics, dict) and 'val_loss' in metrics:
                # Old format
                comparison_data[model_name] = {
                    'best_val_loss': min(metrics['val_loss']),
                    'final_train_loss': metrics['train_loss'][-1],
                    'best_train_loss': min(metrics['train_loss']),
                    'epochs': len(metrics['train_loss'])
                }

                # Check for R² metrics in val_metrics
                if 'val_metrics' in metrics and len(metrics['val_metrics']) > 0:
                    last_val_metrics = metrics['val_metrics'][-1]

                    if 'growth_rate_r2' in last_val_metrics:
                        comparison_data[model_name]['growth_rate_r2'] = last_val_metrics['growth_rate_r2']

                    if 'optimal_temperature_r2' in last_val_metrics:
                        comparison_data[model_name]['optimal_temperature_r2'] = last_val_metrics['optimal_temperature_r2']
        except Exception as e:
            logger.warning(f'Error loading metrics for {model_name}: {e}')
    else:
        logger.warning(f'Metrics file not found for {model_name}: {metrics_file}')

# Create comparison report
if comparison_data:
    with open('$METRICS_DIR/model_comparison.md', 'w') as f:
        f.write('# Model Performance Comparison\n\n')

        # Create comparison table
        f.write('## Performance Metrics\n\n')
        f.write('| Metric | RF Hybrid Model | RF Hybrid Model with RFE | Advanced RF Hybrid Model | Improvement over RFE |\n')
        f.write('|--------|----------------|--------------------------|--------------------------|----------------------|\n')

        # Add metrics to table
        metrics_to_show = ['growth_rate_r2', 'optimal_temperature_r2', 'growth_rate_mse', 'optimal_temperature_mse']
        metric_names = {
            'growth_rate_r2': 'Growth Rate R²',
            'optimal_temperature_r2': 'Optimal Temperature R²',
            'growth_rate_mse': 'Growth Rate MSE',
            'optimal_temperature_mse': 'Optimal Temperature MSE'
        }

        for metric in metrics_to_show:
            metric_name = metric_names.get(metric, metric)
            f.write(f'| {metric_name} |')

            # RF Hybrid Model
            if 'RF Hybrid Model' in comparison_data and metric in comparison_data['RF Hybrid Model']:
                base_value = comparison_data['RF Hybrid Model'][metric]
                if isinstance(base_value, float):
                    f.write(f' {base_value:.4f} |')
                else:
                    f.write(f' {base_value} |')
            else:
                f.write(' N/A |')

            # RF Hybrid Model with RFE
            if 'RF Hybrid Model with RFE' in comparison_data and metric in comparison_data['RF Hybrid Model with RFE']:
                rfe_value = comparison_data['RF Hybrid Model with RFE'][metric]
                if isinstance(rfe_value, float):
                    f.write(f' {rfe_value:.4f} |')
                else:
                    f.write(f' {rfe_value} |')
            else:
                f.write(' N/A |')
                rfe_value = None

            # Advanced RF Hybrid Model
            if 'Advanced RF Hybrid Model' in comparison_data and metric in comparison_data['Advanced RF Hybrid Model']:
                advanced_value = comparison_data['Advanced RF Hybrid Model'][metric]
                if isinstance(advanced_value, float):
                    f.write(f' {advanced_value:.4f} |')
                else:
                    f.write(f' {advanced_value} |')
            else:
                f.write(' N/A |')
                advanced_value = None

            # Calculate improvement over RFE
            if rfe_value is not None and advanced_value is not None:
                if isinstance(rfe_value, (int, float)) and isinstance(advanced_value, (int, float)):
                    if 'mse' in metric:
                        # For MSE, lower is better
                        improvement = ((rfe_value - advanced_value) / rfe_value) * 100
                        f.write(f' {improvement:.2f}% |')
                    else:
                        # For R², higher is better
                        improvement = ((advanced_value - rfe_value) / abs(rfe_value)) * 100 if rfe_value != 0 else float('inf')
                        f.write(f' {improvement:.2f}% |')
                else:
                    f.write(' N/A |')
            else:
                f.write(' N/A |')

            f.write('\n')

        # Add feature analysis section
        f.write('\n## Feature Analysis\n\n')

        # Load feature importance files
        growth_importance_file = Path('$METRICS_DIR/growth_rate_feature_importance.tsv')
        temp_importance_file = Path('$METRICS_DIR/optimal_temperature_feature_importance.tsv')
        
        if growth_importance_file.exists() and temp_importance_file.exists():
            growth_importance = pd.read_csv(growth_importance_file, sep='\t')
            temp_importance = pd.read_csv(temp_importance_file, sep='\t')
            
            f.write('### Top Growth Rate Features (Advanced)\n\n')
            for i, row in growth_importance.head(10).iterrows():
                f.write(f'{i+1}. {row['feature']} ({row['importance']:.6f})\n')
            
            f.write('\n### Top Optimal Temperature Features (Advanced)\n\n')
            for i, row in temp_importance.head(10).iterrows():
                f.write(f'{i+1}. {row['feature']} ({row['importance']:.6f})\n')
            
            # Find common important features
            growth_top_features = set(growth_importance.head(20)['feature'])
            temp_top_features = set(temp_importance.head(20)['feature'])
            common_features = growth_top_features.intersection(temp_top_features)
            
            f.write('\n### Common Important Features\n\n')
            for feature in common_features:
                growth_imp = growth_importance[growth_importance['feature'] == feature]['importance'].values[0]
                temp_imp = temp_importance[temp_importance['feature'] == feature]['importance'].values[0]
                f.write(f'- {feature} (Growth: {growth_imp:.6f}, Temperature: {temp_imp:.6f})\n')
            
            f.write(f'\nTotal common important features: {len(common_features)}\n')
            f.write(f'Percentage of growth rate features: {len(common_features) / 20 * 100:.2f}%\n')
            f.write(f'Percentage of optimal temperature features: {len(common_features) / 20 * 100:.2f}%\n')

        # Add model architecture section
        f.write('\n## Model Architecture\n\n')
        
        # Load hyperparameters if available
        hyperparams_file = Path('$METRICS_DIR/hyperparameters.json')
        if hyperparams_file.exists():
            try:
                with open(hyperparams_file, 'r') as f_hyper:
                    hyperparams = json.load(f_hyper)
                
                f.write('### Growth Rate Model\n\n')
                if 'growth_rate' in hyperparams:
                    f.write('| Parameter | Value |\n')
                    f.write('|-----------|-------|\n')
                    for param, value in hyperparams['growth_rate'].items():
                        f.write(f'| {param} | {value} |\n')
                
                f.write('\n### Optimal Temperature Model\n\n')
                if 'optimal_temperature' in hyperparams:
                    f.write('| Parameter | Value |\n')
                    f.write('|-----------|-------|\n')
                    for param, value in hyperparams['optimal_temperature'].items():
                        f.write(f'| {param} | {value} |\n')
            except Exception as e:
                logger.warning(f'Error loading hyperparameters: {e}')

        # Add conclusion
        f.write('\n## Conclusion\n\n')
        f.write('The Advanced RF Hybrid Model builds upon the RFE model with several key improvements:\n\n')
        f.write('1. **Bayesian Hyperparameter Optimization**: Efficiently explores the hyperparameter space to find optimal configurations for both growth rate and temperature prediction.\n')
        f.write('2. **Model Stacking**: Combines multiple models (Random Forest, XGBoost, LightGBM) to leverage the strengths of each algorithm and improve prediction accuracy.\n')
        f.write('3. **Advanced Feature Engineering**: Creates more sophisticated feature interactions and transformations based on domain knowledge and data-driven insights.\n')
        f.write('4. **SHAP Feature Importance**: Uses SHAP values for more accurate and interpretable feature importance, providing better insights into model decisions.\n')
        f.write('5. **Improved Cross-Validation**: Implements more robust cross-validation strategies to ensure model generalization.\n')
        f.write('6. **Ensemble Techniques**: Leverages ensemble methods to reduce variance and improve prediction stability.\n')
        f.write('7. **Optimized Feature Selection**: Refines the feature selection process to identify the most predictive features for each target variable.\n')

        logger.info('Comparison report generated successfully')
else:
    logger.error('No comparison data available')
"

echo "Comparison report generated!"

echo "All tasks completed successfully!"
