#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Train Hybrid Model with Enhanced Neural Network.
This script trains a hybrid model that combines ensemble methods for growth rate prediction
with an enhanced neural network for temperature prediction.
"""

import os
import argparse
import logging
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from sklearn.preprocessing import RobustScaler

from hybrid_model_with_enhanced_nn import HybridModelWithEnhancedNN

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_data(feature_file: str, metadata_file: str) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    Load feature and metadata files.
    
    Args:
        feature_file: Path to feature file
        metadata_file: Path to metadata file
        
    Returns:
        Tuple of (features, metadata)
    """
    logger.info(f"Loading features from {feature_file}")
    features = pd.read_csv(feature_file, sep='\t', index_col='genome_id')
    
    logger.info(f"Loading metadata from {metadata_file}")
    metadata = pd.read_csv(metadata_file, sep='\t', index_col='genome_id')
    
    # Get common samples
    common_indices = features.index.intersection(metadata.index)
    logger.info(f"Found {len(common_indices)} common samples")
    
    # Filter to common samples
    features = features.loc[common_indices]
    metadata = metadata.loc[common_indices]
    
    # Check for NaN values in features
    nan_count = features.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in features. Filling with 0.")
        features = features.fillna(0)
    
    # Check for infinite values
    inf_count = np.isinf(features.values).sum()
    if inf_count > 0:
        logger.warning(f"Found {inf_count} infinite values in features. Replacing with max/min values.")
        features = features.replace([np.inf, -np.inf], np.nan)
        features = features.fillna(features.max())
    
    # Check for NaN values in metadata
    nan_count = metadata.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in metadata. Filling with 0.")
        metadata = metadata.fillna(0)
    
    return features, metadata

def prepare_data_with_selective_transform(
    features: pd.DataFrame, 
    metadata: pd.DataFrame,
    n_features: int = 150, 
    output_dir: Optional[str] = None
) -> Tuple[pd.DataFrame, pd.Series, pd.Series, pd.DataFrame, pd.Series, pd.Series, pd.DataFrame, pd.Series, pd.Series, List[str], Dict[str, Dict]]:
    """
    Prepare data for multi-task learning with selective transformation and proper train/validation/test split.
    Applies square root transformation only to growth rate, keeping temperature in original scale.

    Args:
        features: Feature DataFrame
        metadata: Metadata DataFrame
        n_features: Number of features to select
        output_dir: Directory to save outputs

    Returns:
        Tuple of (X_train, y_train_growth, y_train_temp, X_val, y_val_growth, y_val_temp, 
                 X_test, y_test_growth, y_test_temp, selected_features, transform_info)
    """
    logger.info("Preparing data for multi-task learning with selective transformation...")

    # Extract targets
    y_growth_original = metadata['growth_rate']
    y_temp_original = metadata['optimal_temperature']
    
    # Apply square root transformation only to growth rate
    logger.info("Applying square root transformation to growth rate only")
    y_growth = np.sqrt(y_growth_original)
    y_temp = y_temp_original  # Keep temperature in original scale
    
    # Create bins for stratification based on transformed growth rate
    n_bins = 10
    y_binned = pd.qcut(y_growth, n_bins, labels=False, duplicates='drop')
    
    # Check if we have enough samples in each bin
    bin_counts = pd.Series(y_binned).value_counts()
    min_bin_count = bin_counts.min()
    
    if min_bin_count < 3:
        logger.warning(f"Some bins have very few samples (min: {min_bin_count}). Using fewer bins.")
        n_bins = max(2, min(5, min_bin_count))
        y_binned = pd.qcut(y_growth, n_bins, labels=False, duplicates='drop')
    
    # First split data into train+val and test sets with stratification
    X_train_val, X_test, y_growth_train_val, y_growth_test, y_temp_train_val, y_temp_test, y_binned_train_val, _ = train_test_split(
        features, y_growth, y_temp, y_binned, test_size=0.2, random_state=42, shuffle=True, stratify=y_binned
    )

    # Then split train+val into train and validation sets with stratification
    X_train, X_val, y_growth_train, y_growth_val, y_temp_train, y_temp_val = train_test_split(
        X_train_val, y_growth_train_val, y_temp_train_val, test_size=0.25, random_state=42, 
        shuffle=True, stratify=y_binned_train_val
    )

    logger.info(f"Data split: Train={X_train.shape[0]} samples, Validation={X_val.shape[0]} samples, Test={X_test.shape[0]} samples")
    
    # Check distribution of target variables in each split
    logger.info(f"Growth Rate (sqrt) - Train: mean={y_growth_train.mean():.4f}, std={y_growth_train.std():.4f}")
    logger.info(f"Growth Rate (sqrt) - Validation: mean={y_growth_val.mean():.4f}, std={y_growth_val.std():.4f}")
    logger.info(f"Growth Rate (sqrt) - Test: mean={y_growth_test.mean():.4f}, std={y_growth_test.std():.4f}")
    
    logger.info(f"Temperature - Train: mean={y_temp_train.mean():.4f}, std={y_temp_train.std():.4f}")
    logger.info(f"Temperature - Validation: mean={y_temp_val.mean():.4f}, std={y_temp_val.std():.4f}")
    logger.info(f"Temperature - Test: mean={y_temp_test.mean():.4f}, std={y_temp_test.std():.4f}")
    
    # Apply target scaling using RobustScaler for both targets
    growth_scaler = RobustScaler()
    temp_scaler = RobustScaler()
    
    # Reshape for scaler
    y_growth_train_reshaped = y_growth_train.values.reshape(-1, 1)
    y_growth_val_reshaped = y_growth_val.values.reshape(-1, 1)
    y_growth_test_reshaped = y_growth_test.values.reshape(-1, 1)
    
    y_temp_train_reshaped = y_temp_train.values.reshape(-1, 1)
    y_temp_val_reshaped = y_temp_val.values.reshape(-1, 1)
    y_temp_test_reshaped = y_temp_test.values.reshape(-1, 1)
    
    # Fit scalers on training data only
    growth_scaler.fit(y_growth_train_reshaped)
    temp_scaler.fit(y_temp_train_reshaped)
    
    # Transform all sets
    y_growth_train_scaled = growth_scaler.transform(y_growth_train_reshaped).flatten()
    y_growth_val_scaled = growth_scaler.transform(y_growth_val_reshaped).flatten()
    y_growth_test_scaled = growth_scaler.transform(y_growth_test_reshaped).flatten()
    
    y_temp_train_scaled = temp_scaler.transform(y_temp_train_reshaped).flatten()
    y_temp_val_scaled = temp_scaler.transform(y_temp_val_reshaped).flatten()
    y_temp_test_scaled = temp_scaler.transform(y_temp_test_reshaped).flatten()
    
    # Convert back to pandas Series with original indices
    y_growth_train = pd.Series(y_growth_train_scaled, index=y_growth_train.index)
    y_growth_val = pd.Series(y_growth_val_scaled, index=y_growth_val.index)
    y_growth_test = pd.Series(y_growth_test_scaled, index=y_growth_test.index)
    
    y_temp_train = pd.Series(y_temp_train_scaled, index=y_temp_train.index)
    y_temp_val = pd.Series(y_temp_val_scaled, index=y_temp_val.index)
    y_temp_test = pd.Series(y_temp_test_scaled, index=y_temp_test.index)
    
    logger.info("Applied target scaling using RobustScaler for both targets")
    
    # For simplicity, we'll use all features for now
    # In a real implementation, you would want to use feature selection here
    selected_features = features.columns.tolist()[:n_features]
    
    # Filter to selected features
    X_train = X_train[selected_features]
    X_val = X_val[selected_features]
    X_test = X_test[selected_features]

    logger.info(f"Prepared data for multi-task learning with {len(selected_features)} features")
    
    # Store transformation information
    transform_info = {
        'growth_scaler': growth_scaler,
        'temp_scaler': temp_scaler,
        'growth_sqrt_transform': True,
        'temp_sqrt_transform': False
    }
    
    # Save transformation info if output_dir is provided
    if output_dir:
        import joblib
        os.makedirs(output_dir, exist_ok=True)
        joblib.dump(transform_info, os.path.join(output_dir, 'transform_info.joblib'))
        logger.info(f"Saved transformation info to {output_dir}")

    return (
        X_train, y_growth_train, y_temp_train, 
        X_val, y_growth_val, y_temp_val, 
        X_test, y_growth_test, y_temp_test, 
        selected_features, transform_info
    )

def train_model(
    X_train: pd.DataFrame, 
    y_growth_train: pd.Series,
    y_temp_train: pd.Series,
    X_val: pd.DataFrame, 
    y_growth_val: pd.Series,
    y_temp_val: pd.Series,
    hidden_dims: List[int] = [512, 512, 384, 256, 128],
    dropout_rates: List[float] = [0.3, 0.4, 0.4, 0.5, 0.5],
    output_dir: Optional[str] = None
) -> HybridModelWithEnhancedNN:
    """
    Train hybrid model with enhanced neural network.
    
    Args:
        X_train: Training features
        y_growth_train: Training growth rate targets
        y_temp_train: Training temperature targets
        X_val: Validation features
        y_growth_val: Validation growth rate targets
        y_temp_val: Validation temperature targets
        hidden_dims: List of hidden layer dimensions for temperature neural network
        dropout_rates: List of dropout rates for temperature neural network
        output_dir: Directory to save outputs
        
    Returns:
        Trained model
    """
    logger.info("Training hybrid model with enhanced neural network...")
    
    # Initialize model
    model = HybridModelWithEnhancedNN(
        growth_ensemble_weight_lr=0.01,
        growth_ensemble_weight_epochs=50,
        temp_nn_hidden_dims=hidden_dims,
        temp_nn_dropout_rates=dropout_rates,
        temp_nn_lr=0.001,
        temp_nn_batch_size=64,
        temp_nn_epochs=100,
        temp_nn_patience=15,
        variance_percentile=25
    )
    
    # Fit model
    model.fit(
        X_train, 
        y_growth_train, 
        y_temp_train,
        X_val, 
        y_growth_val, 
        y_temp_val,
        output_dir
    )
    
    return model

def evaluate_model(
    model: HybridModelWithEnhancedNN, 
    X_test: pd.DataFrame, 
    y_growth_test: pd.Series,
    y_temp_test: pd.Series,
    transform_info: Dict[str, Dict],
    output_dir: Optional[str] = None
) -> Dict[str, Dict[str, float]]:
    """
    Evaluate trained model on test set.
    
    Args:
        model: Trained model
        X_test: Test features
        y_growth_test: Test growth rate targets
        y_temp_test: Test temperature targets
        transform_info: Dictionary with transformation information
        output_dir: Directory to save outputs
        
    Returns:
        Dictionary with evaluation metrics
    """
    logger.info("Evaluating hybrid model with enhanced neural network...")
    
    # Make predictions (these are already in the transformed + scaled space)
    y_pred_growth, y_pred_temp = model.predict(X_test)
    
    # Evaluate in scaled space
    metrics_scaled = {
        'overall': {},
        'growth_rate': {},
        'temperature': {}
    }
    
    # Growth rate metrics (scaled)
    growth_mse = mean_squared_error(y_growth_test, y_pred_growth)
    growth_rmse = np.sqrt(growth_mse)
    growth_r2 = r2_score(y_growth_test, y_pred_growth)
    growth_mae = mean_absolute_error(y_growth_test, y_pred_growth)
    
    metrics_scaled['growth_rate'] = {
        'mse': growth_mse,
        'rmse': growth_rmse,
        'r2': growth_r2,
        'mae': growth_mae
    }
    
    # Temperature metrics (scaled)
    temp_mse = mean_squared_error(y_temp_test, y_pred_temp)
    temp_rmse = np.sqrt(temp_mse)
    temp_r2 = r2_score(y_temp_test, y_pred_temp)
    temp_mae = mean_absolute_error(y_temp_test, y_pred_temp)
    
    metrics_scaled['temperature'] = {
        'mse': temp_mse,
        'rmse': temp_rmse,
        'r2': temp_r2,
        'mae': temp_mae
    }
    
    # Overall metrics (scaled)
    # Simple average of R² values
    overall_r2 = (growth_r2 + temp_r2) / 2
    
    metrics_scaled['overall'] = {
        'r2': overall_r2
    }
    
    # Log scaled metrics
    logger.info(f"Final metrics (scaled) - Overall R²: {overall_r2:.4f}")
    logger.info(f"Final metrics (scaled) - Growth Rate R²: {growth_r2:.4f}")
    logger.info(f"Final metrics (scaled) - Temperature R²: {temp_r2:.4f}")
    
    # Inverse transform predictions to original space
    growth_scaler = transform_info['growth_scaler']
    temp_scaler = transform_info['temp_scaler']
    
    # Reshape for scaler
    y_pred_growth_reshaped = np.array(y_pred_growth).reshape(-1, 1)
    y_pred_temp_reshaped = np.array(y_pred_temp).reshape(-1, 1)
    
    y_growth_test_reshaped = np.array(y_growth_test).reshape(-1, 1)
    y_temp_test_reshaped = np.array(y_temp_test).reshape(-1, 1)
    
    # Inverse transform
    y_pred_growth_unscaled = growth_scaler.inverse_transform(y_pred_growth_reshaped).flatten()
    y_pred_temp_unscaled = temp_scaler.inverse_transform(y_pred_temp_reshaped).flatten()
    
    y_growth_test_unscaled = growth_scaler.inverse_transform(y_growth_test_reshaped).flatten()
    y_temp_test_unscaled = temp_scaler.inverse_transform(y_temp_test_reshaped).flatten()
    
    # Apply inverse square root transformation to growth rate if needed
    if transform_info['growth_sqrt_transform']:
        y_pred_growth_unscaled = y_pred_growth_unscaled ** 2
        y_growth_test_unscaled = y_growth_test_unscaled ** 2
    
    # Evaluate in original space
    metrics_original = {
        'overall': {},
        'growth_rate': {},
        'temperature': {}
    }
    
    # Growth rate metrics (original)
    growth_mse_orig = mean_squared_error(y_growth_test_unscaled, y_pred_growth_unscaled)
    growth_rmse_orig = np.sqrt(growth_mse_orig)
    growth_r2_orig = r2_score(y_growth_test_unscaled, y_pred_growth_unscaled)
    growth_mae_orig = mean_absolute_error(y_growth_test_unscaled, y_pred_growth_unscaled)
    
    metrics_original['growth_rate'] = {
        'mse': growth_mse_orig,
        'rmse': growth_rmse_orig,
        'r2': growth_r2_orig,
        'mae': growth_mae_orig
    }
    
    # Temperature metrics (original)
    temp_mse_orig = mean_squared_error(y_temp_test_unscaled, y_pred_temp_unscaled)
    temp_rmse_orig = np.sqrt(temp_mse_orig)
    temp_r2_orig = r2_score(y_temp_test_unscaled, y_pred_temp_unscaled)
    temp_mae_orig = mean_absolute_error(y_temp_test_unscaled, y_pred_temp_unscaled)
    
    metrics_original['temperature'] = {
        'mse': temp_mse_orig,
        'rmse': temp_rmse_orig,
        'r2': temp_r2_orig,
        'mae': temp_mae_orig
    }
    
    # Overall metrics (original)
    # Simple average of R² values
    overall_r2_orig = (growth_r2_orig + temp_r2_orig) / 2
    
    metrics_original['overall'] = {
        'r2': overall_r2_orig
    }
    
    # Log original metrics
    logger.info(f"Final metrics (original) - Overall R²: {overall_r2_orig:.4f}")
    logger.info(f"Final metrics (original) - Growth Rate R²: {growth_r2_orig:.4f}")
    logger.info(f"Final metrics (original) - Temperature R²: {temp_r2_orig:.4f}")
    
    # Combine metrics
    metrics = {
        'scaled': metrics_scaled,
        'original': metrics_original
    }
    
    # Plot predictions vs. actuals for both tasks
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
        
        # Plot growth rate predictions vs. actuals
        plt.figure(figsize=(10, 8))
        plt.scatter(y_growth_test_unscaled, y_pred_growth_unscaled, alpha=0.5)
        plt.plot([min(y_growth_test_unscaled), max(y_growth_test_unscaled)], 
                 [min(y_growth_test_unscaled), max(y_growth_test_unscaled)], 'r--')
        plt.xlabel('Actual Growth Rate')
        plt.ylabel('Predicted Growth Rate')
        plt.title(f'Growth Rate Predictions (R² = {growth_r2_orig:.4f})')
        plt.savefig(os.path.join(output_dir, 'growth_rate_predictions.png'))
        
        # Plot temperature predictions vs. actuals
        plt.figure(figsize=(10, 8))
        plt.scatter(y_temp_test_unscaled, y_pred_temp_unscaled, alpha=0.5)
        plt.plot([min(y_temp_test_unscaled), max(y_temp_test_unscaled)], 
                 [min(y_temp_test_unscaled), max(y_temp_test_unscaled)], 'r--')
        plt.xlabel('Actual Temperature (°C)')
        plt.ylabel('Predicted Temperature (°C)')
        plt.title(f'Temperature Predictions (R² = {temp_r2_orig:.4f})')
        plt.savefig(os.path.join(output_dir, 'temperature_predictions.png'))
    
        # Save metrics
        import json
        with open(os.path.join(output_dir, 'metrics.json'), 'w') as f:
            json.dump(metrics, f, indent=4)
    
    return metrics

def main():
    parser = argparse.ArgumentParser(description="Train and evaluate hybrid model with enhanced neural network")
    parser.add_argument("--feature_file", required=True, help="Path to feature file")
    parser.add_argument("--metadata_file", required=True, help="Path to metadata file")
    parser.add_argument("--output_dir", default="models/hybrid_enhanced_nn", help="Directory to save outputs")
    parser.add_argument("--n_features", type=int, default=150, help="Number of features to select")
    parser.add_argument("--hidden_dims", type=str, default="512,512,384,256,128", help="Hidden layer dimensions (comma-separated)")
    parser.add_argument("--dropout_rates", type=str, default="0.3,0.4,0.4,0.5,0.5", help="Dropout rates (comma-separated)")
    args = parser.parse_args()

    # Parse hidden dimensions and dropout rates
    hidden_dims = [int(dim) for dim in args.hidden_dims.split(',')]
    dropout_rates = [float(rate) for rate in args.dropout_rates.split(',')]

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Load data
    features, metadata = load_data(args.feature_file, args.metadata_file)

    # Prepare data with selective transformation and proper train/validation/test split
    (
        X_train, y_growth_train, y_temp_train, 
        X_val, y_growth_val, y_temp_val, 
        X_test, y_growth_test, y_temp_test, 
        selected_features, transform_info
    ) = prepare_data_with_selective_transform(
        features, metadata, args.n_features, args.output_dir
    )

    # Train model
    model = train_model(
        X_train, y_growth_train, y_temp_train,
        X_val, y_growth_val, y_temp_val,
        hidden_dims=hidden_dims,
        dropout_rates=dropout_rates,
        output_dir=args.output_dir
    )

    # Evaluate model on the test set
    metrics = evaluate_model(
        model, 
        X_test, 
        y_growth_test, 
        y_temp_test,
        transform_info,
        args.output_dir
    )

    # Print final metrics
    logger.info("Hybrid model with enhanced neural network - training and evaluation completed")
    logger.info(f"Final metrics (scaled) - Overall R²: {metrics['scaled']['overall']['r2']:.4f}")
    logger.info(f"Final metrics (scaled) - Growth Rate R²: {metrics['scaled']['growth_rate']['r2']:.4f}")
    logger.info(f"Final metrics (scaled) - Temperature R²: {metrics['scaled']['temperature']['r2']:.4f}")
    
    logger.info(f"Final metrics (original) - Overall R²: {metrics['original']['overall']['r2']:.4f}")
    logger.info(f"Final metrics (original) - Growth Rate R²: {metrics['original']['growth_rate']['r2']:.4f}")
    logger.info(f"Final metrics (original) - Temperature R²: {metrics['original']['temperature']['r2']:.4f}")

if __name__ == "__main__":
    main()
