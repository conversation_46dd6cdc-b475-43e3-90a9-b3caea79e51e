#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Run Hybrid Model with Preserved Components v5.
This script implements a strict separation approach that:
1. Uses the exact growth rate prediction model from original implementation
2. Uses the exact temperature prediction model from original implementation
3. Trains them completely separately
4. Only combines their predictions, not their architectures
"""

import os
import argparse
import logging
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
import joblib
from typing import Dict, List, Tuple, Optional

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Helper functions for data loading and evaluation
def load_data(feature_file: str, metadata_file: str) -> <PERSON>ple[pd.DataFrame, pd.DataFrame]:
    """
    Load feature and metadata files.
    
    Args:
        feature_file: Path to feature file
        metadata_file: Path to metadata file
        
    Returns:
        Tuple of (features, metadata)
    """
    logger.info(f"Loading features from {feature_file}")
    features = pd.read_csv(feature_file, sep='\t', index_col='genome_id')
    
    logger.info(f"Loading metadata from {metadata_file}")
    metadata = pd.read_csv(metadata_file, sep='\t', index_col='genome_id')
    
    # Get common samples
    common_indices = features.index.intersection(metadata.index)
    logger.info(f"Found {len(common_indices)} common samples")
    
    # Filter to common samples
    features = features.loc[common_indices]
    metadata = metadata.loc[common_indices]
    
    # Check for NaN values in features
    nan_count = features.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in features. Filling with 0.")
        features = features.fillna(0)
    
    # Check for infinite values
    inf_count = np.isinf(features.values).sum()
    if inf_count > 0:
        logger.warning(f"Found {inf_count} infinite values in features. Replacing with max/min values.")
        features = features.replace([np.inf, -np.inf], np.nan)
        features = features.fillna(features.max())
    
    # Check for NaN values in metadata
    nan_count = metadata.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in metadata. Filling with 0.")
        metadata = metadata.fillna(0)
    
    return features, metadata

# Import growth rate components
from hybrid_model_with_enhanced_nn import HybridModelWithEnhancedNN
# Import temperature prediction components
from hybrid_model_with_enhanced_nn import FeatureDataset

# RegularizedDNN from the best temperature model implementation
class RegularizedDNN(nn.Module):
    """
    Regularized Deep Neural Network with batch normalization, dropout, and residual connections.
    This is the exact architecture from the successful standalone temperature model.
    """
    def __init__(self, input_dim, hidden_dims=[512, 512, 512, 384, 256, 128], 
                 dropout_rates=[0.2, 0.25, 0.3, 0.35, 0.4, 0.45], 
                 use_batch_norm=True, activation='leaky_relu', use_residual=True):
        super(RegularizedDNN, self).__init__()
        
        # Create layers
        layers = []
        prev_dim = input_dim
        
        # Choose activation function
        if activation == 'relu':
            act_fn = nn.ReLU()
        elif activation == 'leaky_relu':
            act_fn = nn.LeakyReLU(0.1)
        elif activation == 'elu':
            act_fn = nn.ELU()
        else:
            act_fn = nn.ReLU()
        
        # Store dimensions for residual connections
        self.layer_dims = [input_dim] + hidden_dims
        self.use_residual = use_residual
        self.residual_layers = {}
        
        # Build network with progressively increasing dropout
        for i, hidden_dim in enumerate(hidden_dims):
            # Linear layer
            layers.append(nn.Linear(prev_dim, hidden_dim))
            
            # Batch normalization (before activation)
            if use_batch_norm:
                layers.append(nn.BatchNorm1d(hidden_dim))
            
            # Activation
            layers.append(act_fn)
            
            # Dropout with increasing rate for deeper layers
            dropout_rate = dropout_rates[min(i, len(dropout_rates)-1)]
            layers.append(nn.Dropout(dropout_rate))
            
            # If using residual connections and dimensions match
            if use_residual and i > 0 and hidden_dims[i-1] == hidden_dim:
                # Store the index of this layer for residual connections
                self.residual_layers[i] = len(layers) - 1
            
            prev_dim = hidden_dim
        
        self.feature_extractor = nn.Sequential(*layers)
        
        # Output layer with no activation or dropout
        self.output_layer = nn.Linear(prev_dim, 1)
        
        # Initialize weights with improved method
        self._init_weights()
    
    def _init_weights(self):
        """Initialize weights with improved initialization."""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                # Use Xavier/Glorot initialization for better stability
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
    
    def forward(self, x):
        features = self.feature_extractor(x)
        output = self.output_layer(features)
        return output.squeeze()

# Temperature model that uses the exact architecture from the standalone implementation
class TemperatureModel:
    """
    Temperature prediction model using the exact architecture from the standalone implementation.
    """
    def __init__(self, 
                 lr: float = 0.0005,
                 batch_size: int = 32,
                 epochs: int = 250,
                 patience: int = 25):
        self.lr = lr
        self.batch_size = batch_size
        self.epochs = epochs
        self.patience = patience
        self.model = None
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.feature_scaler = StandardScaler()
        
    def fit(self, X_train: pd.DataFrame, y_train: pd.Series, 
            X_val: pd.DataFrame, y_val: pd.Series,
            output_dir: Optional[str] = None) -> None:
        """
        Train the temperature model with the exact architecture from standalone implementation.
        
        Args:
            X_train: Training features
            y_train: Training temperature targets
            X_val: Validation features
            y_val: Validation temperature targets
            output_dir: Directory to save outputs
        """
        logger.info("Training temperature model...")
        
        # Scale features
        X_train_scaled = self.feature_scaler.fit_transform(X_train)
        X_val_scaled = self.feature_scaler.transform(X_val)
        
        # Create datasets and dataloaders
        train_dataset = FeatureDataset(X_train_scaled, y_train.values)
        val_dataset = FeatureDataset(X_val_scaled, y_val.values)
        
        train_loader = DataLoader(train_dataset, batch_size=self.batch_size, shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size=self.batch_size * 2, shuffle=False)
        
        # Initialize model with the optimal architecture from the standalone model
        input_dim = X_train.shape[1]
        
        # Use the exact architecture that worked best
        self.model = RegularizedDNN(
            input_dim=input_dim,
            hidden_dims=[512, 512, 512, 384, 256, 128],
            dropout_rates=[0.2, 0.25, 0.3, 0.35, 0.4, 0.45],
            use_batch_norm=True,
            activation='leaky_relu',
            use_residual=True
        ).to(self.device)
        
        # Use AdamW optimizer with carefully tuned parameters
        optimizer = optim.AdamW(
            self.model.parameters(), 
            lr=self.lr,
            weight_decay=5e-5,
            betas=(0.9, 0.999)
        )
        
        # Use OneCycleLR scheduler
        scheduler = optim.lr_scheduler.OneCycleLR(
            optimizer,
            max_lr=0.005,  # 10x peak learning rate
            epochs=self.epochs,
            steps_per_epoch=len(train_loader),
            pct_start=0.3,  # 30% warmup
            anneal_strategy='cos',
            div_factor=25.0,
            final_div_factor=10000.0
        )
        
        # Combined loss function
        mse_criterion = nn.MSELoss()
        l1_criterion = nn.L1Loss()
        
        # Early stopping
        best_val_loss = float('inf')
        best_model_state = None
        patience_counter = 0
        
        # Training loop
        logger.info(f"Beginning temperature model training for {self.epochs} epochs...")
        
        for epoch in range(self.epochs):
            # Training phase
            self.model.train()
            train_loss = 0.0
            
            for features, targets in train_loader:
                features, targets = features.to(self.device), targets.to(self.device)
                
                # Zero gradients
                optimizer.zero_grad()
                
                # Forward pass
                outputs = self.model(features)
                
                # Combined loss - 85% MSE, 15% L1 (optimal from v4)
                mse_loss = mse_criterion(outputs, targets)
                l1_loss = l1_criterion(outputs, targets)
                loss = 0.85 * mse_loss + 0.15 * l1_loss
                
                # L1 regularization
                l1_lambda = 5e-6
                l1_norm = sum(p.abs().sum() for p in self.model.parameters())
                loss += l1_lambda * l1_norm
                
                # Backward pass
                loss.backward()
                
                # Gradient clipping
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), 0.5)
                
                # Update weights
                optimizer.step()
                
                # Update learning rate
                scheduler.step()
                
                # Add batch loss
                train_loss += loss.item() * features.size(0)
            
            # Calculate average loss
            train_loss /= len(train_loader.dataset)
            
            # Validation phase
            self.model.eval()
            val_loss = 0.0
            predictions = []
            actuals = []
            
            with torch.no_grad():
                for features, targets in val_loader:
                    features, targets = features.to(self.device), targets.to(self.device)
                    
                    # Forward pass
                    outputs = self.model(features)
                    
                    # Calculate loss
                    mse_loss = mse_criterion(outputs, targets)
                    l1_loss = l1_criterion(outputs, targets)
                    loss = 0.85 * mse_loss + 0.15 * l1_loss
                    
                    # Add batch loss
                    val_loss += loss.item() * features.size(0)
                    
                    # Store predictions and targets for metrics
                    predictions.extend(outputs.cpu().numpy())
                    actuals.extend(targets.cpu().numpy())
            
            # Calculate average loss and metrics
            val_loss /= len(val_loader.dataset)
            val_rmse = np.sqrt(mean_squared_error(actuals, predictions))
            val_r2 = r2_score(actuals, predictions)
            
            # Log metrics
            if (epoch + 1) % 10 == 0 or epoch == 0:
                logger.info(f"Epoch {epoch+1}/{self.epochs} - "
                           f"Train Loss: {train_loss:.4f}, "
                           f"Val Loss: {val_loss:.4f}, "
                           f"Val RMSE: {val_rmse:.4f}, "
                           f"Val R²: {val_r2:.4f}")
            
            # Early stopping
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                best_model_state = self.model.state_dict().copy()
                patience_counter = 0
            else:
                patience_counter += 1
                
            if patience_counter >= self.patience:
                logger.info(f"Early stopping at epoch {epoch+1}")
                break
        
        # Load best model
        if best_model_state is not None:
            self.model.load_state_dict(best_model_state)
            logger.info(f"Loaded best model with validation loss: {best_val_loss:.4f}")
        
        # Final metrics
        self.model.eval()
        predictions = []
        actuals = []
        
        with torch.no_grad():
            for features, targets in val_loader:
                features, targets = features.to(self.device), targets.to(self.device)
                outputs = self.model(features)
                predictions.extend(outputs.cpu().numpy())
                actuals.extend(targets.cpu().numpy())
        
        final_rmse = np.sqrt(mean_squared_error(actuals, predictions))
        final_r2 = r2_score(actuals, predictions)
        final_mae = mean_absolute_error(actuals, predictions)
        
        logger.info(f"Final Temperature Model - "
                   f"Val RMSE: {final_rmse:.4f}, "
                   f"Val R²: {final_r2:.4f}, "
                   f"Val MAE: {final_mae:.4f}")
        
        # Save model if output_dir provided
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)
            # Save model
            torch.save(self.model.state_dict(), os.path.join(output_dir, 'temperature_model.pt'))
            # Save scaler
            joblib.dump(self.feature_scaler, os.path.join(output_dir, 'temp_feature_scaler.joblib'))
            logger.info(f"Temperature model saved to {output_dir}")
    
    def predict(self, X: pd.DataFrame) -> np.ndarray:
        """
        Predict temperature using the standalone model.
        
        Args:
            X: Feature DataFrame
            
        Returns:
            Array of temperature predictions
        """
        # Scale features
        X_scaled = self.feature_scaler.transform(X)
        
        # Convert to tensor
        X_tensor = torch.tensor(X_scaled, dtype=torch.float32).to(self.device)
        
        # Make prediction
        self.model.eval()
        with torch.no_grad():
            temp_pred = self.model(X_tensor).cpu().numpy()
            
        return temp_pred

# A wrapper class that combines both models but trains them separately
class PreservedComponentsHybridModel:
    """
    Hybrid model that preserves the original components.
    Growth rate and temperature models are trained completely separately.
    """
    def __init__(self, 
                n_growth_features: int = 150,
                n_temp_features: int = 250,
                output_dir: Optional[str] = None):
        """
        Initialize the hybrid model with preserved components.
        
        Args:
            n_growth_features: Number of features for growth rate model
            n_temp_features: Number of features for temperature model
            output_dir: Directory to save outputs
        """
        self.n_growth_features = n_growth_features
        self.n_temp_features = n_temp_features
        self.output_dir = output_dir
        
        # Create growth rate model
        self.growth_model = HybridModelWithEnhancedNN(
            growth_ensemble_weight_lr=0.01,
            growth_ensemble_weight_epochs=50,
            temp_nn_hidden_dims=[512, 512, 384, 256, 128],
            temp_nn_dropout_rates=[0.3, 0.4, 0.4, 0.5, 0.5],
            temp_nn_lr=0.001,
            temp_nn_batch_size=64,
            temp_nn_epochs=100,
            temp_nn_patience=15,
            variance_percentile=25
        )
        
        # Create temperature model
        self.temp_model = TemperatureModel(
            lr=0.0005,
            batch_size=32,
            epochs=250,
            patience=25
        )
        
        # Transform info
        self.transform_info = None
    
    def _select_growth_features(self, X: pd.DataFrame) -> Tuple[pd.DataFrame, List[str]]:
        """
        Select features for growth rate prediction.
        Prioritizes codon usage, tRNA, and other growth-related features.
        
        Args:
            X: Feature DataFrame
            
        Returns:
            Tuple of (selected features DataFrame, list of feature names)
        """
        # Priority features for growth rate prediction
        growth_priority_features = [col for col in X.columns if any(x in col.lower() for x in ['codon', 'tRNA', 'rRNA', 'growth'])]
        
        # Add remaining features up to n_growth_features
        remaining_features = [col for col in X.columns if col not in growth_priority_features]
        selected_features = growth_priority_features + remaining_features
        selected_features = selected_features[:self.n_growth_features]
        
        logger.info(f"Selected {len(selected_features)} features for growth rate prediction")
        
        return X[selected_features], selected_features
    
    def _select_temp_features(self, X: pd.DataFrame) -> Tuple[pd.DataFrame, List[str]]:
        """
        Select features for temperature prediction.
        Prioritizes GC content, amino acid, and other temperature-related features.
        
        Args:
            X: Feature DataFrame
            
        Returns:
            Tuple of (selected features DataFrame, list of feature names)
        """
        # Priority features for temperature prediction
        temp_priority_features = []
        
        # GC content related features
        temp_priority_features.extend([col for col in X.columns if 'GC' in col])
        
        # Amino acid and protein related features
        temp_priority_features.extend([col for col in X.columns if any(x in col.lower() for x in ['amino', 'protein', 'keto', 'charged', 'polar'])])
        
        # Add remaining features up to n_temp_features
        remaining_features = [col for col in X.columns if col not in temp_priority_features]
        selected_features = temp_priority_features + remaining_features
        selected_features = selected_features[:self.n_temp_features]
        
        logger.info(f"Selected {len(selected_features)} features for temperature prediction")
        
        return X[selected_features], selected_features
    
    def prepare_data(self, 
                    features: pd.DataFrame, 
                    metadata: pd.DataFrame) -> Tuple:
        """
        Prepare data for both growth rate and temperature models.
        
        Args:
            features: Feature DataFrame
            metadata: Metadata DataFrame
            
        Returns:
            Tuple containing all prepared data sets
        """
        logger.info("Preparing data for hybrid model with preserved components...")
        
        # Extract targets
        y_growth_original = metadata['growth_rate']
        y_temp_original = metadata['optimal_temperature']
        
        # Apply square root transformation to growth rate
        logger.info("Applying square root transformation to growth rate only")
        y_growth = np.sqrt(y_growth_original)
        y_temp = y_temp_original  # Keep temperature in original scale
        
        # Create bins for stratification based on both targets (combined)
        growth_bins = pd.qcut(y_growth, 5, labels=False, duplicates='drop')
        temp_bins = pd.qcut(y_temp, 5, labels=False, duplicates='drop')
        combined_bins = growth_bins * 10 + temp_bins  # Combine bins for better stratification
        
        # Split data into train, validation, and test sets
        X_train_val, X_test, y_growth_train_val, y_growth_test, y_temp_train_val, y_temp_test, bins_train_val, _ = train_test_split(
            features, y_growth, y_temp, combined_bins, test_size=0.2, random_state=42, shuffle=True, stratify=combined_bins
        )
        
        X_train, X_val, y_growth_train, y_growth_val, y_temp_train, y_temp_val = train_test_split(
            X_train_val, y_growth_train_val, y_temp_train_val, test_size=0.25, random_state=42, 
            shuffle=True, stratify=bins_train_val
        )
        
        logger.info(f"Data split: Train={X_train.shape[0]} samples, Validation={X_val.shape[0]} samples, Test={X_test.shape[0]} samples")
        
        # Apply RobustScaler for both targets
        growth_scaler = RobustScaler()
        temp_scaler = RobustScaler()
        
        # Reshape for scaler
        y_growth_train_reshaped = y_growth_train.values.reshape(-1, 1)
        y_growth_val_reshaped = y_growth_val.values.reshape(-1, 1)
        y_growth_test_reshaped = y_growth_test.values.reshape(-1, 1)
        
        y_temp_train_reshaped = y_temp_train.values.reshape(-1, 1)
        y_temp_val_reshaped = y_temp_val.values.reshape(-1, 1)
        y_temp_test_reshaped = y_temp_test.values.reshape(-1, 1)
        
        # Fit scalers on training data only
        growth_scaler.fit(y_growth_train_reshaped)
        temp_scaler.fit(y_temp_train_reshaped)
        
        # Transform all sets
        y_growth_train_scaled = growth_scaler.transform(y_growth_train_reshaped).flatten()
        y_growth_val_scaled = growth_scaler.transform(y_growth_val_reshaped).flatten()
        y_growth_test_scaled = growth_scaler.transform(y_growth_test_reshaped).flatten()
        
        y_temp_train_scaled = temp_scaler.transform(y_temp_train_reshaped).flatten()
        y_temp_val_scaled = temp_scaler.transform(y_temp_val_reshaped).flatten()
        y_temp_test_scaled = temp_scaler.transform(y_temp_test_reshaped).flatten()
        
        # Convert back to pandas Series with original indices
        y_growth_train = pd.Series(y_growth_train_scaled, index=y_growth_train.index)
        y_growth_val = pd.Series(y_growth_val_scaled, index=y_growth_val.index)
        y_growth_test = pd.Series(y_growth_test_scaled, index=y_growth_test.index)
        
        y_temp_train = pd.Series(y_temp_train_scaled, index=y_temp_train.index)
        y_temp_val = pd.Series(y_temp_val_scaled, index=y_temp_val.index)
        y_temp_test = pd.Series(y_temp_test_scaled, index=y_temp_test.index)
        
        logger.info("Applied target scaling using RobustScaler for both targets")
        
        # Store transformation information
        self.transform_info = {
            'growth_scaler': growth_scaler,
            'temp_scaler': temp_scaler,
            'growth_sqrt_transform': True,
            'temp_sqrt_transform': False
        }
        
        # Save transformation info if output_dir is provided
        if self.output_dir:
            os.makedirs(self.output_dir, exist_ok=True)
            joblib.dump(self.transform_info, os.path.join(self.output_dir, 'transform_info.joblib'))
        
        # Select features for growth rate and temperature separately
        X_train_growth, growth_features = self._select_growth_features(X_train)
        X_val_growth, _ = self._select_growth_features(X_val)
        X_test_growth, _ = self._select_growth_features(X_test)
        
        X_train_temp, temp_features = self._select_temp_features(X_train)
        X_val_temp, _ = self._select_temp_features(X_val)
        X_test_temp, _ = self._select_temp_features(X_test)
        
        # Save selected features
        if self.output_dir:
            joblib.dump(growth_features, os.path.join(self.output_dir, 'growth_features.joblib'))
            joblib.dump(temp_features, os.path.join(self.output_dir, 'temp_features.joblib'))
        
        return (
            # Growth rate data
            X_train_growth, y_growth_train, 
            X_val_growth, y_growth_val,
            X_test_growth, y_growth_test,
            # Temperature data
            X_train_temp, y_temp_train,
            X_val_temp, y_temp_val,
            X_test_temp, y_temp_test,
            # Original full data for reference
            X_train, X_val, X_test,
            # Transform info and features
            growth_features, temp_features
        )
    
    def fit(self, 
           features: pd.DataFrame, 
           metadata: pd.DataFrame) -> None:
        """
        Fit the hybrid model - train growth rate and temperature models separately.
        
        Args:
            features: Feature DataFrame
            metadata: Metadata DataFrame
        """
        # Prepare data - completely separate for each task
        (X_train_growth, y_growth_train, 
         X_val_growth, y_growth_val,
         X_test_growth, y_growth_test,
         X_train_temp, y_temp_train,
         X_val_temp, y_temp_val,
         X_test_temp, y_temp_test,
         X_train, X_val, X_test,
         growth_features, temp_features) = self.prepare_data(features, metadata)
        
        # Create directories
        growth_dir = os.path.join(self.output_dir, 'growth_model') if self.output_dir else None
        temp_dir = os.path.join(self.output_dir, 'temp_model') if self.output_dir else None
        
        if growth_dir:
            os.makedirs(growth_dir, exist_ok=True)
        if temp_dir:
            os.makedirs(temp_dir, exist_ok=True)
        
        # Train growth rate model
        logger.info("Training growth rate model...")
        # We'll train the growth rate model independently
        # We pass in dummy temperature targets (not used) to satisfy the API
        dummy_temp_train = pd.Series(np.zeros(len(y_growth_train)), index=y_growth_train.index)
        dummy_temp_val = pd.Series(np.zeros(len(y_growth_val)), index=y_growth_val.index)
        
        self.growth_model.fit(
            X_train_growth,
            y_growth_train,
            dummy_temp_train,
            X_val_growth,
            y_growth_val,
            dummy_temp_val,
            growth_dir
        )
        
        # Train temperature model
        logger.info("Training temperature model...")
        self.temp_model.fit(
            X_train_temp,
            y_temp_train,
            X_val_temp,
            y_temp_val,
            temp_dir
        )
        
        # Evaluate models
        growth_metrics = self._evaluate_growth_model(X_test_growth, y_growth_test)
        temp_metrics = self._evaluate_temp_model(X_test_temp, y_temp_test)
        
        # Calculate combined metrics
        overall_r2 = (growth_metrics['r2'] + temp_metrics['r2']) / 2
        overall_rmse = (growth_metrics['rmse'] + temp_metrics['rmse']) / 2
        overall_mae = (growth_metrics['mae'] + temp_metrics['mae']) / 2
        
        logger.info(f"Final Growth Rate Metrics - R²: {growth_metrics['r2']:.4f}, RMSE: {growth_metrics['rmse']:.4f}, MAE: {growth_metrics['mae']:.4f}")
        logger.info(f"Final Temperature Metrics - R²: {temp_metrics['r2']:.4f}, RMSE: {temp_metrics['rmse']:.4f}, MAE: {temp_metrics['mae']:.4f}")
        logger.info(f"Overall Metrics - R²: {overall_r2:.4f}, RMSE: {overall_rmse:.4f}, MAE: {overall_mae:.4f}")
        
        # Save metrics
        if self.output_dir:
            metrics = {
                'growth_rate': growth_metrics,
                'temperature': temp_metrics,
                'overall': {
                    'r2': overall_r2,
                    'rmse': overall_rmse,
                    'mae': overall_mae
                }
            }
            joblib.dump(metrics, os.path.join(self.output_dir, 'metrics.joblib'))
    
    def _evaluate_growth_model(self, X_test: pd.DataFrame, y_test: pd.Series) -> Dict:
        """
        Evaluate growth rate model.
        
        Args:
            X_test: Test features
            y_test: Test targets
            
        Returns:
            Dictionary of metrics
        """
        # Make predictions
        y_pred = self.growth_model.predict_growth(X_test)
        
        # Calculate metrics
        r2 = r2_score(y_test, y_pred)
        rmse = np.sqrt(mean_squared_error(y_test, y_pred))
        mae = mean_absolute_error(y_test, y_pred)
        
        return {'r2': r2, 'rmse': rmse, 'mae': mae}
    
    def _evaluate_temp_model(self, X_test: pd.DataFrame, y_test: pd.Series) -> Dict:
        """
        Evaluate temperature model.
        
        Args:
            X_test: Test features
            y_test: Test targets
            
        Returns:
            Dictionary of metrics
        """
        # Make predictions
        y_pred = self.temp_model.predict(X_test)
        
        # Calculate metrics
        r2 = r2_score(y_test, y_pred)
        rmse = np.sqrt(mean_squared_error(y_test, y_pred))
        mae = mean_absolute_error(y_test, y_pred)
        
        return {'r2': r2, 'rmse': rmse, 'mae': mae}
    
    def predict(self, X: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """
        Generate predictions for both growth rate and temperature.
        
        Args:
            X: Features
            
        Returns:
            Tuple of (growth_rate_predictions, temperature_predictions)
        """
        # Select features for each model
        X_growth, _ = self._select_growth_features(X)
        X_temp, _ = self._select_temp_features(X)
        
        # Make predictions
        growth_pred = self.growth_model.predict_growth(X_growth)
        temp_pred = self.temp_model.predict(X_temp)
        
        return growth_pred, temp_pred
    
    def evaluate(self, 
                X: pd.DataFrame, 
                y_growth: pd.Series, 
                y_temp: pd.Series) -> Dict:
        """
        Evaluate model on data.
        
        Args:
            X: Features
            y_growth: Growth rate targets
            y_temp: Temperature targets
            
        Returns:
            Dictionary of evaluation metrics
        """
        # Select features for each model
        X_growth, _ = self._select_growth_features(X)
        X_temp, _ = self._select_temp_features(X)
        
        # Make predictions
        growth_pred = self.growth_model.predict_growth(X_growth)
        temp_pred = self.temp_model.predict(X_temp)
        
        # Calculate metrics for growth rate
        growth_r2 = r2_score(y_growth, growth_pred)
        growth_rmse = np.sqrt(mean_squared_error(y_growth, growth_pred))
        growth_mae = mean_absolute_error(y_growth, growth_pred)
        
        # Calculate metrics for temperature
        temp_r2 = r2_score(y_temp, temp_pred)
        temp_rmse = np.sqrt(mean_squared_error(y_temp, temp_pred))
        temp_mae = mean_absolute_error(y_temp, temp_pred)
        
        # Calculate overall metrics
        overall_r2 = (growth_r2 + temp_r2) / 2
        overall_rmse = (growth_rmse + temp_rmse) / 2
        overall_mae = (growth_mae + temp_mae) / 2
        
        # Log metrics
        logger.info(f"Evaluation metrics - Growth Rate: R²={growth_r2:.4f}, RMSE={growth_rmse:.4f}, MAE={growth_mae:.4f}")
        logger.info(f"Evaluation metrics - Temperature: R²={temp_r2:.4f}, RMSE={temp_rmse:.4f}, MAE={temp_mae:.4f}")
        logger.info(f"Evaluation metrics - Overall: R²={overall_r2:.4f}, RMSE={overall_rmse:.4f}, MAE={overall_mae:.4f}")
        
        return {
            'growth_rate': {'r2': growth_r2, 'rmse': growth_rmse, 'mae': growth_mae},
            'temperature': {'r2': temp_r2, 'rmse': temp_rmse, 'mae': temp_mae},
            'overall': {'r2': overall_r2, 'rmse': overall_rmse, 'mae': overall_mae}
        }

def main():
    """Main function."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Train hybrid model with preserved components')
    parser.add_argument('--feature_file', type=str, required=True, help='Path to feature file (TSV)')
    parser.add_argument('--metadata_file', type=str, required=True, help='Path to metadata file (TSV)')
    parser.add_argument('--output_dir', type=str, required=True, help='Directory to save outputs')
    parser.add_argument('--n_growth_features', type=int, default=150, help='Number of features for growth rate model')
    parser.add_argument('--n_temp_features', type=int, default=250, help='Number of features for temperature model')
    parser.add_argument('--seed', type=int, default=42, help='Random seed')
    
    args = parser.parse_args()
    
    # Set random seeds for reproducibility
    np.random.seed(args.seed)
    torch.manual_seed(args.seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(args.seed)
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Load data
    features, metadata = load_data(args.feature_file, args.metadata_file)
    
    # Initialize model
    model = PreservedComponentsHybridModel(
        n_growth_features=args.n_growth_features,
        n_temp_features=args.n_temp_features,
        output_dir=args.output_dir
    )
    
    # Fit model
    model.fit(features, metadata)
    
    # Save command line arguments
    with open(os.path.join(args.output_dir, 'args.txt'), 'w') as f:
        for arg, value in vars(args).items():
            f.write(f"{arg}: {value}\n")
    
    logger.info(f"Model and results saved to {args.output_dir}")
    logger.info("Done!")

if __name__ == "__main__":
    main() 