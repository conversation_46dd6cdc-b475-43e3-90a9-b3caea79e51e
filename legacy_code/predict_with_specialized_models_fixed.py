#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Predict and Train with Specialized Models (Enhanced).
This script uses the best specialized models for each prediction target:
1. Enhanced DNN for temperature prediction (R² = 0.9548)
2. Hybrid with enhanced NN for growth rate prediction (R² = 0.9207)

Features:
- Z-score-based outlier detection (>7 z-scores)
- NA feature removal (>50% missing values)
- Training functionality for both models
"""

import os
import argparse
import logging
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import torch
import joblib
from typing import Dict, List, Tuple, Optional, Union
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from sklearn.preprocessing import RobustScaler, StandardScaler
from scipy import stats

# Import necessary model components
from train_enhanced_dnn_temperature import RegularizedDNN

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import the hybrid model if available
try:
    from hybrid_model_with_enhanced_temp_dnn import HybridModelWithEnhancedTempDNN
    HYBRID_MODEL_AVAILABLE = True
except ImportError:
    logger.warning("Hybrid model not available. Training will use only the enhanced DNN model.")
    HYBRID_MODEL_AVAILABLE = False

class SpecializedModelsPredictor:
    """
    Predictor that uses specialized models for each prediction target.
    """
    def __init__(
        self,
        temp_model_dir: str = 'models/enhanced_dnn_temperature_v2',
        growth_model_dir: str = 'models/hybrid_enhanced_nn_v1'
    ):
        """
        Initialize specialized models predictor.

        Args:
            temp_model_dir: Directory containing the temperature model
            growth_model_dir: Directory containing the growth rate model
        """
        self.temp_model_dir = temp_model_dir
        self.growth_model_dir = growth_model_dir

        # Load models
        self._load_temp_model()
        self._load_growth_model()

        logger.info("Specialized models predictor initialized successfully")

    def _load_temp_model(self):
        """Load temperature model."""
        logger.info(f"Loading temperature model from {self.temp_model_dir}")

        # Load feature scaler
        self.temp_feature_scaler = joblib.load(os.path.join(self.temp_model_dir, 'temperature_scaler.joblib'))

        # Create a copy of the scaler without feature names to avoid warnings
        if hasattr(self.temp_feature_scaler, 'feature_names_in_'):
            self.temp_feature_scaler_no_names = joblib.load(os.path.join(self.temp_model_dir, 'temperature_scaler.joblib'))
            if hasattr(self.temp_feature_scaler_no_names, 'feature_names_in_'):
                delattr(self.temp_feature_scaler_no_names, 'feature_names_in_')
        else:
            self.temp_feature_scaler_no_names = self.temp_feature_scaler

        logger.info(f"Temperature feature scaler loaded successfully")

        # Load transform info if available
        transform_info_path = os.path.join(self.temp_model_dir, 'transform_info.joblib')
        if os.path.exists(transform_info_path):
            self.temp_transform_info = joblib.load(transform_info_path)
            logger.info(f"Temperature transform info loaded successfully: {self.temp_transform_info}")
        else:
            # Create default transform info
            self.temp_transform_info = {
                'temp_scaler': None,
                'temp_sqrt_transform': False
            }
            logger.info(f"Using default temperature transform info: {self.temp_transform_info}")

        # Load model architecture
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

        # Get input dimension from scaler
        input_dim = len(self.temp_feature_scaler.mean_)

        # Create model with the same architecture
        self.temp_model = RegularizedDNN(
            input_dim=input_dim,
            hidden_dims=[512, 512, 384, 256, 128],
            dropout_rates=[0.3, 0.4, 0.4, 0.5, 0.5],
            use_batch_norm=True,
            activation='relu'
        )

        # Load weights
        self.temp_model.load_state_dict(torch.load(
            os.path.join(self.temp_model_dir, 'temperature_model.pt'),
            map_location=device
        ))

        # Set model to evaluation mode
        self.temp_model.eval()

        # Set device
        self.device = device
        self.temp_model.to(self.device)

        logger.info(f"Temperature model loaded successfully")

    def _load_growth_model(self):
        """Load growth rate model."""
        logger.info(f"Loading growth rate model from {self.growth_model_dir}")

        # Load feature scaler
        self.growth_feature_scaler = joblib.load(os.path.join(self.growth_model_dir, 'feature_scaler.joblib'))

        # Create a copy of the scaler without feature names to avoid warnings
        if hasattr(self.growth_feature_scaler, 'feature_names_in_'):
            self.growth_feature_scaler_no_names = joblib.load(os.path.join(self.growth_model_dir, 'feature_scaler.joblib'))
            if hasattr(self.growth_feature_scaler_no_names, 'feature_names_in_'):
                delattr(self.growth_feature_scaler_no_names, 'feature_names_in_')
        else:
            self.growth_feature_scaler_no_names = self.growth_feature_scaler

        logger.info(f"Growth rate feature scaler loaded successfully")

        # Load Random Forest model
        rf_dir = os.path.join(self.growth_model_dir, 'rf_models')
        self.growth_rf = joblib.load(os.path.join(rf_dir, 'rf_growth_model.joblib'))
        logger.info(f"Random Forest model loaded successfully")

        # Load XGBoost model
        xgb_dir = os.path.join(self.growth_model_dir, 'xgb_models')
        self.growth_xgb = joblib.load(os.path.join(xgb_dir, 'xgb_growth_model.joblib'))
        logger.info(f"XGBoost model loaded successfully")

        # Load ensemble weights
        self.growth_weights = torch.load(
            os.path.join(self.growth_model_dir, 'growth_weights.pth'),
            map_location=torch.device('cpu')
        ).cpu().numpy()
        logger.info(f"Ensemble weights loaded successfully: {self.growth_weights}")

        # Load transform info
        transform_info_path = os.path.join(self.growth_model_dir, 'transform_info.joblib')
        if os.path.exists(transform_info_path):
            self.transform_info = joblib.load(transform_info_path)
            logger.info(f"Transform info loaded successfully: {self.transform_info}")
        else:
            logger.warning(f"No transform_info.joblib file found in {self.growth_model_dir}")
            self.transform_info = {
                'growth_sqrt_transform': True,
                'growth_scaler': RobustScaler()
            }
            logger.info(f"Using default transform info: {self.transform_info}")

    def predict_temperature(self, X: pd.DataFrame) -> np.ndarray:
        """
        Generate temperature predictions using the enhanced DNN model.

        Args:
            X: Features DataFrame

        Returns:
            Temperature predictions
        """
        # Handle feature mismatch
        if hasattr(self.temp_feature_scaler, 'feature_names_in_'):
            # Filter features to match training data
            feature_names = self.temp_feature_scaler.feature_names_in_
            common_cols = [col for col in X.columns if col in feature_names]

            # Check if we have all required features
            if len(common_cols) < len(feature_names):
                logger.warning(f"Missing {len(feature_names) - len(common_cols)} features required by the temperature model")
                # Use only available features
                X_filtered = X[common_cols]
                # Fill missing features with zeros
                missing_cols = [col for col in feature_names if col not in common_cols]
                for col in missing_cols:
                    X_filtered[col] = 0
                # Reorder columns to match the order expected by the model
                X_filtered = X_filtered[feature_names]
            else:
                # Use only the features the model was trained on, in the correct order
                X_filtered = X[feature_names]
        else:
            # If no feature names, check dimensions
            expected_features = self.temp_feature_scaler.mean_.shape[0]
            if X.shape[1] != expected_features:
                logger.warning(f"Temperature feature count mismatch: got {X.shape[1]}, expected {expected_features}")
                # If too many features, truncate
                if X.shape[1] > expected_features:
                    X_filtered = X.iloc[:, :expected_features]
                else:
                    # If too few features, pad with zeros
                    X_filtered = pd.DataFrame(np.zeros((X.shape[0], expected_features)), index=X.index)
                    X_filtered.iloc[:, :X.shape[1]] = X.values
            else:
                X_filtered = X

        # Scale features using the scaler without feature names to avoid warnings
        X_filtered_np = X_filtered.values
        X_scaled = self.temp_feature_scaler_no_names.transform(X_filtered_np)

        # Convert to tensor
        X_tensor = torch.tensor(X_scaled, dtype=torch.float32).to(self.device)

        # Generate predictions
        with torch.no_grad():
            self.temp_model.eval()  # Ensure model is in evaluation mode
            temp_pred = self.temp_model(X_tensor)
            temp_pred = temp_pred.cpu().numpy()

        logger.info(f"Raw temperature predictions - Min: {temp_pred.min():.4f}, Max: {temp_pred.max():.4f}, Mean: {temp_pred.mean():.4f}")

        # Apply inverse scaling if needed
        if hasattr(self, 'temp_transform_info') and self.temp_transform_info.get('temp_scaler') is not None:
            # Reshape for scaler
            temp_pred_reshaped = temp_pred.reshape(-1, 1)
            # Apply inverse transform
            temp_pred = self.temp_transform_info['temp_scaler'].inverse_transform(temp_pred_reshaped).flatten()
            logger.info(f"Unscaled temperature predictions - Min: {temp_pred.min():.4f}, Max: {temp_pred.max():.4f}, Mean: {temp_pred.mean():.4f}")

        # Apply inverse sqrt transform if needed
        if hasattr(self, 'temp_transform_info') and self.temp_transform_info.get('temp_sqrt_transform', False):
            temp_pred = temp_pred ** 2
            logger.info(f"Sqrt-inverse temperature predictions - Min: {temp_pred.min():.4f}, Max: {temp_pred.max():.4f}, Mean: {temp_pred.mean():.4f}")

        # Ensure predictions are non-negative
        temp_pred = np.maximum(temp_pred, 0)

        # Apply reasonable bounds to catch any extreme predictions
        min_temp = 0.0  # Minimum reasonable temperature (0°C)
        max_temp = 120.0  # Maximum reasonable temperature (120°C for extreme thermophiles)

        # Check if there are any extreme predictions
        extreme_count = np.sum((temp_pred < min_temp) | (temp_pred > max_temp))
        if extreme_count > 0:
            logger.warning(f"Found {extreme_count} extreme temperature predictions. Clipping to range [{min_temp}, {max_temp}]")
            temp_pred = np.clip(temp_pred, min_temp, max_temp)

        logger.info(f"Final temperature predictions - Min: {temp_pred.min():.4f}, Max: {temp_pred.max():.4f}, Mean: {temp_pred.mean():.4f}")

        return temp_pred

    def predict_growth(self, X: pd.DataFrame) -> np.ndarray:
        """
        Generate growth rate predictions.

        Args:
            X: Features DataFrame

        Returns:
            Growth rate predictions
        """
        # Handle feature mismatch
        if hasattr(self.growth_feature_scaler, 'feature_names_in_'):
            # Filter features to match training data
            feature_names = self.growth_feature_scaler.feature_names_in_
            common_cols = [col for col in X.columns if col in feature_names]

            # Check if we have all required features
            if len(common_cols) < len(feature_names):
                logger.warning(f"Missing {len(feature_names) - len(common_cols)} features required by the model")
                # Use only available features
                X_filtered = X[common_cols]
                # Fill missing features with zeros
                missing_cols = [col for col in feature_names if col not in common_cols]
                for col in missing_cols:
                    X_filtered[col] = 0
                # Reorder columns to match the order expected by the model
                X_filtered = X_filtered[feature_names]
            else:
                # Use only the features the model was trained on, in the correct order
                X_filtered = X[feature_names]
        else:
            # If no feature names, check dimensions
            expected_features = self.growth_feature_scaler.mean_.shape[0]
            if X.shape[1] != expected_features:
                logger.warning(f"Feature count mismatch: got {X.shape[1]}, expected {expected_features}")
                # If too many features, truncate
                if X.shape[1] > expected_features:
                    X_filtered = X.iloc[:, :expected_features]
                else:
                    # If too few features, pad with zeros
                    X_filtered = pd.DataFrame(np.zeros((X.shape[0], expected_features)), index=X.index)
                    X_filtered.iloc[:, :X.shape[1]] = X.values
            else:
                X_filtered = X

        # Scale features using the scaler without feature names to avoid warnings
        X_filtered_np = X_filtered.values
        X_scaled = self.growth_feature_scaler_no_names.transform(X_filtered_np)

        # Get predictions from Random Forest
        rf_pred = self.growth_rf.predict(X_scaled)
        logger.info(f"RF predictions - Min: {rf_pred.min():.4f}, Max: {rf_pred.max():.4f}, Mean: {rf_pred.mean():.4f}")

        # Get predictions from XGBoost
        xgb_pred = self.growth_xgb.predict(X_scaled)
        logger.info(f"XGB predictions - Min: {xgb_pred.min():.4f}, Max: {xgb_pred.max():.4f}, Mean: {xgb_pred.mean():.4f}")

        # Use XGBoost predictions as LightGBM predictions (as fallback)
        lgb_pred = xgb_pred
        logger.info(f"LGB predictions (fallback) - Min: {lgb_pred.min():.4f}, Max: {lgb_pred.max():.4f}, Mean: {lgb_pred.mean():.4f}")

        # Combine predictions using ensemble weights
        if isinstance(self.growth_weights, torch.Tensor):
            weights = torch.softmax(self.growth_weights, dim=0).cpu().numpy()
        else:
            # If weights are already numpy array, normalize them
            weights = self.growth_weights / np.sum(self.growth_weights)

        logger.info(f"Normalized weights: RF={weights[0]:.4f}, XGB={weights[1]:.4f}, LGB={weights[2]:.4f}")

        combined_pred = (
            weights[0] * rf_pred +
            weights[1] * xgb_pred +
            weights[2] * lgb_pred
        )

        logger.info(f"Combined predictions (scaled) - Min: {combined_pred.min():.4f}, Max: {combined_pred.max():.4f}, Mean: {combined_pred.mean():.4f}")

        # The model was trained on sqrt-transformed and RobustScaler-transformed growth rate
        # We need to convert back to the original space

        # First, inverse transform the RobustScaler
        combined_pred_reshaped = combined_pred.reshape(-1, 1)

        # Check if transform_info contains a growth_scaler
        if hasattr(self.transform_info, 'get') and self.transform_info.get('growth_scaler') is not None:
            # Use the scaler from transform_info
            combined_pred_unscaled = self.transform_info['growth_scaler'].inverse_transform(combined_pred_reshaped).flatten()
        else:
            # Create a default scaler
            scaler = RobustScaler()
            # Fit the scaler on a range of values that might be encountered
            y_growth_sqrt = np.sqrt(np.linspace(0.01, 10, 1000)).reshape(-1, 1)
            scaler.fit(y_growth_sqrt)
            combined_pred_unscaled = scaler.inverse_transform(combined_pred_reshaped).flatten()

        logger.info(f"Combined predictions (unscaled) - Min: {combined_pred_unscaled.min():.4f}, Max: {combined_pred_unscaled.max():.4f}, Mean: {combined_pred_unscaled.mean():.4f}")

        # Then, inverse transform the sqrt transformation
        if hasattr(self.transform_info, 'get') and self.transform_info.get('growth_sqrt_transform', False):
            combined_pred_orig = combined_pred_unscaled ** 2
        else:
            combined_pred_orig = combined_pred_unscaled ** 2  # Default to sqrt transform

        # Ensure predictions are non-negative
        combined_pred_orig = np.maximum(combined_pred_orig, 0)

        logger.info(f"Final predictions - Min: {combined_pred_orig.min():.4f}, Max: {combined_pred_orig.max():.4f}, Mean: {combined_pred_orig.mean():.4f}")

        return combined_pred_orig

    def predict(self, X: pd.DataFrame, target: str = 'both'):
        """
        Generate predictions for the specified target.

        Args:
            X: Features DataFrame
            target: Prediction target ('growth', 'temperature', or 'both')

        Returns:
            Predictions for the specified target
        """
        if target == 'growth':
            return self.predict_growth(X)
        elif target == 'temperature':
            return self.predict_temperature(X)
        elif target == 'both':
            growth_pred = self.predict_growth(X)
            temp_pred = self.predict_temperature(X)
            return growth_pred, temp_pred
        else:
            raise ValueError(f"Invalid target: {target}. Must be 'growth', 'temperature', or 'both'.")

    def evaluate(self, X: pd.DataFrame, y_growth=None, y_temp=None, target: str = 'both'):
        """
        Evaluate model on data.

        Args:
            X: Features DataFrame
            y_growth: Growth rate targets (optional)
            y_temp: Temperature targets (optional)
            target: Prediction target ('growth', 'temperature', or 'both')

        Returns:
            Dictionary of evaluation metrics
        """
        results = {}

        if target in ['growth', 'both'] and y_growth is not None:
            # Generate growth rate predictions
            y_growth_pred = self.predict_growth(X)

            # Calculate metrics
            growth_metrics = {
                'R2': r2_score(y_growth, y_growth_pred),
                'RMSE': np.sqrt(mean_squared_error(y_growth, y_growth_pred)),
                'MAE': mean_absolute_error(y_growth, y_growth_pred)
            }

            results['growth_rate'] = growth_metrics

            logger.info(f"Growth Rate: R²={growth_metrics['R2']:.4f}, RMSE={growth_metrics['RMSE']:.4f}, MAE={growth_metrics['MAE']:.4f}")

        if target in ['temperature', 'both'] and y_temp is not None:
            # Generate temperature predictions
            y_temp_pred = self.predict_temperature(X)

            # Calculate metrics
            temp_metrics = {
                'R2': r2_score(y_temp, y_temp_pred),
                'RMSE': np.sqrt(mean_squared_error(y_temp, y_temp_pred)),
                'MAE': mean_absolute_error(y_temp, y_temp_pred)
            }

            results['temperature'] = temp_metrics

            logger.info(f"Temperature: R²={temp_metrics['R2']:.4f}, RMSE={temp_metrics['RMSE']:.4f}, MAE={temp_metrics['MAE']:.4f}")

        if target == 'both' and 'growth_rate' in results and 'temperature' in results:
            # Calculate overall metrics (average R2)
            overall_metrics = {
                'R2': (results['growth_rate']['R2'] + results['temperature']['R2']) / 2,
                'RMSE': (results['growth_rate']['RMSE'] + results['temperature']['RMSE']) / 2,
                'MAE': (results['growth_rate']['MAE'] + results['temperature']['MAE']) / 2
            }

            results['overall'] = overall_metrics

            logger.info(f"Overall: R²={overall_metrics['R2']:.4f}, RMSE={overall_metrics['RMSE']:.4f}, MAE={overall_metrics['MAE']:.4f}")

        return results

def detect_outliers(values, zscore_threshold=7.0):
    """
    Detect outliers using z-score method.

    Args:
        values: Series of values
        zscore_threshold: Z-score threshold for outlier detection

    Returns:
        Boolean Series indicating outliers
    """
    logger.info(f"Detecting outliers using z-score method (threshold: {zscore_threshold})...")

    # Drop NaN values
    valid_values = values.dropna()

    # Calculate z-scores
    z_scores = stats.zscore(valid_values)

    # Identify outliers based on z-score
    outliers_array = abs(z_scores) > zscore_threshold

    # Convert back to Series with original indices
    outliers = pd.Series(False, index=values.index)
    outliers[valid_values.index] = outliers_array

    # Log results
    logger.info(f"Identified {outliers.sum()} outliers ({outliers.sum()/len(values):.2%})")

    return outliers

def remove_high_na_features(features, na_threshold=0.5):
    """
    Remove features with high percentage of NA values.

    Args:
        features: Feature DataFrame
        na_threshold: Threshold for NA percentage (0.5 = 50%)

    Returns:
        Feature DataFrame with high-NA features removed
    """
    # Calculate NA percentage for each feature
    na_percentage = features.isna().mean()

    # Identify features with high NA percentage
    high_na_features = na_percentage[na_percentage > na_threshold].index.tolist()

    if high_na_features:
        logger.info(f"Removing {len(high_na_features)} features with >{na_threshold*100:.0f}% NA values")
        features = features.drop(columns=high_na_features)
    else:
        logger.info(f"No features with >{na_threshold*100:.0f}% NA values found")

    return features

def load_data(feature_file: str, metadata_file: str, use_zscore: bool = True,
              zscore_threshold: float = 7.0, na_threshold: float = 0.5) -> tuple:
    """
    Load feature and metadata files with z-score-based outlier detection and NA feature removal.

    Args:
        feature_file: Path to feature file
        metadata_file: Path to metadata file
        use_zscore: Whether to use z-score method for outlier detection
        zscore_threshold: Z-score threshold for outlier detection
        na_threshold: Threshold for NA percentage in features (0.5 = 50%)

    Returns:
        Tuple of (features, metadata)
    """
    logger.info(f"Loading features from {feature_file}")
    features = pd.read_csv(feature_file, sep='\t', index_col='genome_id')

    logger.info(f"Loading metadata from {metadata_file}")
    metadata = pd.read_csv(metadata_file, sep='\t', index_col='genome_id')

    # Get common samples
    common_indices = features.index.intersection(metadata.index)
    logger.info(f"Found {len(common_indices)} common samples")

    # Filter to common samples
    features = features.loc[common_indices]
    metadata = metadata.loc[common_indices]

    # Remove features with high NA percentage
    features = remove_high_na_features(features, na_threshold)

    # Log statistics before filtering
    logger.info(f"Growth rate statistics before filtering - Min: {metadata['growth_rate'].min():.4f}, Max: {metadata['growth_rate'].max():.4f}, Mean: {metadata['growth_rate'].mean():.4f}, Median: {metadata['growth_rate'].median():.4f}")
    logger.info(f"Temperature statistics before filtering - Min: {metadata['optimal_temperature'].min():.4f}, Max: {metadata['optimal_temperature'].max():.4f}, Mean: {metadata['optimal_temperature'].mean():.4f}, Median: {metadata['optimal_temperature'].median():.4f}")

    # Detect outliers using z-score method if requested
    if use_zscore:
        # Apply square root transformation to growth rate for better outlier detection
        y_growth_sqrt = np.sqrt(metadata['growth_rate'])

        # Detect outliers in growth rate (using transformed values)
        growth_outliers = detect_outliers(y_growth_sqrt, zscore_threshold)
        logger.info(f"Identified {growth_outliers.sum()} growth rate outliers ({growth_outliers.sum()/len(metadata):.2%})")

        # Detect outliers in temperature
        temp_outliers = detect_outliers(metadata['optimal_temperature'], zscore_threshold)
        logger.info(f"Identified {temp_outliers.sum()} temperature outliers ({temp_outliers.sum()/len(metadata):.2%})")

        # Combine outliers (either growth rate or temperature)
        combined_outliers = growth_outliers | temp_outliers
        logger.info(f"Identified {combined_outliers.sum()} combined outliers ({combined_outliers.sum()/len(metadata):.2%})")

        # Filter out outliers
        non_outlier_indices = metadata.index[~combined_outliers]
        features = features.loc[non_outlier_indices]
        metadata = metadata.loc[non_outlier_indices]

        # Log statistics after filtering
        logger.info(f"Growth rate statistics after filtering - Min: {metadata['growth_rate'].min():.4f}, Max: {metadata['growth_rate'].max():.4f}, Mean: {metadata['growth_rate'].mean():.4f}, Median: {metadata['growth_rate'].median():.4f}")
        logger.info(f"Temperature statistics after filtering - Min: {metadata['optimal_temperature'].min():.4f}, Max: {metadata['optimal_temperature'].max():.4f}, Mean: {metadata['optimal_temperature'].mean():.4f}, Median: {metadata['optimal_temperature'].median():.4f}")

    # Check for NaN values in features
    nan_count = features.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in features. Filling with 0.")
        features = features.fillna(0)

    # Check for infinite values
    inf_count = np.isinf(features.values).sum()
    if inf_count > 0:
        logger.warning(f"Found {inf_count} infinite values in features. Replacing with max/min values.")
        features = features.replace([np.inf, -np.inf], np.nan)
        features = features.fillna(features.max())

    # Check for NaN values in metadata
    nan_count = metadata.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in metadata. Filling with 0.")
        metadata = metadata.fillna(0)

    return features, metadata

def prepare_data_for_training(features: pd.DataFrame, metadata: pd.DataFrame, n_features: int = 150) -> Tuple:
    """
    Prepare data for training with proper train/validation/test split.

    Args:
        features: Feature DataFrame
        metadata: Metadata DataFrame
        n_features: Number of features to select

    Returns:
        Tuple of (X_train, y_growth_train, y_temp_train, X_val, y_growth_val, y_temp_val,
                 X_test, y_growth_test, y_temp_test, selected_features, transform_info)
    """
    logger.info("Preparing data for training with proper train/validation/test split...")

    # Extract targets
    y_growth_original = metadata['growth_rate']
    y_temp_original = metadata['optimal_temperature']

    # Apply square root transformation to growth rate
    logger.info("Applying square root transformation to growth rate")
    y_growth = np.sqrt(y_growth_original)
    y_temp = y_temp_original  # Keep temperature in original scale

    # Create bins for stratification based on transformed growth rate
    n_bins = 10
    y_binned = pd.qcut(y_growth, n_bins, labels=False, duplicates='drop')

    # Check if we have enough samples in each bin
    bin_counts = pd.Series(y_binned).value_counts()
    min_bin_count = bin_counts.min()

    if min_bin_count < 3:
        logger.warning(f"Some bins have very few samples (min: {min_bin_count}). Using fewer bins.")
        n_bins = max(2, min(5, min_bin_count))
        y_binned = pd.qcut(y_growth, n_bins, labels=False, duplicates='drop')

    # First split data into train+val and test sets with stratification
    X_train_val, X_test, y_growth_train_val, y_growth_test, y_temp_train_val, y_temp_test, y_binned_train_val, _ = train_test_split(
        features, y_growth, y_temp, y_binned, test_size=0.2, random_state=42, shuffle=True, stratify=y_binned
    )

    # Then split train+val into train and validation sets with stratification
    X_train, X_val, y_growth_train, y_growth_val, y_temp_train, y_temp_val = train_test_split(
        X_train_val, y_growth_train_val, y_temp_train_val, test_size=0.25, random_state=42,
        shuffle=True, stratify=y_binned_train_val
    )

    logger.info(f"Data split: Train={X_train.shape[0]} samples, Validation={X_val.shape[0]} samples, Test={X_test.shape[0]} samples")

    # Check distribution of target variables in each split
    logger.info(f"Growth Rate (sqrt) - Train: mean={y_growth_train.mean():.4f}, std={y_growth_train.std():.4f}")
    logger.info(f"Growth Rate (sqrt) - Validation: mean={y_growth_val.mean():.4f}, std={y_growth_val.std():.4f}")
    logger.info(f"Growth Rate (sqrt) - Test: mean={y_growth_test.mean():.4f}, std={y_growth_test.std():.4f}")

    logger.info(f"Temperature - Train: mean={y_temp_train.mean():.4f}, std={y_temp_train.std():.4f}")
    logger.info(f"Temperature - Validation: mean={y_temp_val.mean():.4f}, std={y_temp_val.std():.4f}")
    logger.info(f"Temperature - Test: mean={y_temp_test.mean():.4f}, std={y_temp_test.std():.4f}")

    # Apply target scaling using RobustScaler for both targets
    growth_scaler = RobustScaler()
    temp_scaler = RobustScaler()

    # Reshape for scaler
    y_growth_train_reshaped = y_growth_train.values.reshape(-1, 1)
    y_growth_val_reshaped = y_growth_val.values.reshape(-1, 1)
    y_growth_test_reshaped = y_growth_test.values.reshape(-1, 1)

    y_temp_train_reshaped = y_temp_train.values.reshape(-1, 1)
    y_temp_val_reshaped = y_temp_val.values.reshape(-1, 1)
    y_temp_test_reshaped = y_temp_test.values.reshape(-1, 1)

    # Fit scalers on training data only
    growth_scaler.fit(y_growth_train_reshaped)
    temp_scaler.fit(y_temp_train_reshaped)

    # Transform all sets
    y_growth_train_scaled = growth_scaler.transform(y_growth_train_reshaped).flatten()
    y_growth_val_scaled = growth_scaler.transform(y_growth_val_reshaped).flatten()
    y_growth_test_scaled = growth_scaler.transform(y_growth_test_reshaped).flatten()

    y_temp_train_scaled = temp_scaler.transform(y_temp_train_reshaped).flatten()
    y_temp_val_scaled = temp_scaler.transform(y_temp_val_reshaped).flatten()
    y_temp_test_scaled = temp_scaler.transform(y_temp_test_reshaped).flatten()

    # Convert back to pandas Series with original indices
    y_growth_train = pd.Series(y_growth_train_scaled, index=y_growth_train.index)
    y_growth_val = pd.Series(y_growth_val_scaled, index=y_growth_val.index)
    y_growth_test = pd.Series(y_growth_test_scaled, index=y_growth_test.index)

    y_temp_train = pd.Series(y_temp_train_scaled, index=y_temp_train.index)
    y_temp_val = pd.Series(y_temp_val_scaled, index=y_temp_val.index)
    y_temp_test = pd.Series(y_temp_test_scaled, index=y_temp_test.index)

    logger.info("Applied target scaling using RobustScaler for both targets")

    # For simplicity, we'll use all features for now
    # In a real implementation, you would want to use feature selection here
    selected_features = features.columns.tolist()[:n_features]

    # Filter to selected features
    X_train = X_train[selected_features]
    X_val = X_val[selected_features]
    X_test = X_test[selected_features]

    logger.info(f"Prepared data for training with {len(selected_features)} features")

    # Store transformation information
    transform_info = {
        'growth_scaler': growth_scaler,
        'temp_scaler': temp_scaler,
        'growth_sqrt_transform': True,
        'temp_sqrt_transform': False
    }

    return (
        X_train, y_growth_train, y_temp_train,
        X_val, y_growth_val, y_temp_val,
        X_test, y_growth_test, y_temp_test,
        selected_features, transform_info
    )

def train_temperature_model(
    X_train: pd.DataFrame,
    y_temp_train: pd.Series,
    X_val: pd.DataFrame,
    y_temp_val: pd.Series,
    hidden_dims: List[int] = [512, 512, 384, 256, 128],
    dropout_rates: List[float] = [0.3, 0.4, 0.4, 0.5, 0.5],
    output_dir: str = None
) -> RegularizedDNN:
    """
    Train enhanced DNN for temperature prediction.

    Args:
        X_train: Training features
        y_temp_train: Training temperature targets
        X_val: Validation features
        y_temp_val: Validation temperature targets
        hidden_dims: List of hidden layer dimensions
        dropout_rates: List of dropout rates
        output_dir: Directory to save outputs

    Returns:
        Trained model
    """
    logger.info("Training enhanced DNN for temperature prediction...")

    # Initialize feature scaler
    feature_scaler = StandardScaler()
    X_train_scaled = feature_scaler.fit_transform(X_train)
    X_val_scaled = feature_scaler.transform(X_val)

    # Initialize model
    input_dim = X_train.shape[1]
    model = RegularizedDNN(
        input_dim=input_dim,
        hidden_dims=hidden_dims,
        dropout_rates=dropout_rates,
        use_batch_norm=True,
        activation='relu'
    )

    # Train model
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model.to(device)

    # Convert data to tensors
    X_train_tensor = torch.tensor(X_train_scaled, dtype=torch.float32).to(device)
    y_temp_train_tensor = torch.tensor(y_temp_train.values, dtype=torch.float32).to(device)

    X_val_tensor = torch.tensor(X_val_scaled, dtype=torch.float32).to(device)
    y_temp_val_tensor = torch.tensor(y_temp_val.values, dtype=torch.float32).to(device)

    # Define loss function and optimizer
    criterion = torch.nn.MSELoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)

    # Training loop
    batch_size = 64
    epochs = 100
    patience = 15
    best_val_loss = float('inf')
    best_model_state = None
    patience_counter = 0

    for epoch in range(epochs):
        # Training
        model.train()
        train_loss = 0.0

        # Batch training
        for i in range(0, len(X_train_tensor), batch_size):
            # Get batch
            X_batch = X_train_tensor[i:i+batch_size]
            y_batch = y_temp_train_tensor[i:i+batch_size]

            # Zero gradients
            optimizer.zero_grad()

            # Forward pass
            outputs = model(X_batch)

            # Calculate loss
            loss = criterion(outputs, y_batch)

            # Backward pass and optimization
            loss.backward()
            optimizer.step()

            train_loss += loss.item() * len(X_batch)

        train_loss /= len(X_train_tensor)

        # Validation
        model.eval()
        with torch.no_grad():
            val_outputs = model(X_val_tensor)
            val_loss = criterion(val_outputs, y_temp_val_tensor).item()

            # Calculate R²
            val_r2 = r2_score(y_temp_val_tensor.cpu().numpy(), val_outputs.cpu().numpy())

        # Print progress
        if (epoch + 1) % 10 == 0:
            logger.info(f"Epoch {epoch+1}/{epochs}: Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}, Val R²: {val_r2:.4f}")

        # Check for early stopping
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            best_model_state = model.state_dict().copy()
            patience_counter = 0
        else:
            patience_counter += 1

        if patience_counter >= patience:
            logger.info(f"Early stopping at epoch {epoch+1}")
            break

    # Load best model
    model.load_state_dict(best_model_state)

    # Save model if output_dir is provided
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)

        # Save model
        torch.save(best_model_state, os.path.join(output_dir, 'temperature_model.pt'))

        # Save scaler
        joblib.dump(feature_scaler, os.path.join(output_dir, 'temperature_scaler.joblib'))

        logger.info(f"Saved temperature model to {output_dir}")

    return model, feature_scaler

def train_hybrid_model(
    X_train: pd.DataFrame,
    y_growth_train: pd.Series,
    y_temp_train: pd.Series,
    X_val: pd.DataFrame,
    y_growth_val: pd.Series,
    y_temp_val: pd.Series,
    transform_info: Dict,
    output_dir: str = None
) -> None:
    """
    Train hybrid model for growth rate and temperature prediction.

    Args:
        X_train: Training features
        y_growth_train: Training growth rate targets
        y_temp_train: Training temperature targets
        X_val: Validation features
        y_growth_val: Validation growth rate targets
        y_temp_val: Validation temperature targets
        transform_info: Dictionary with transformation information
        output_dir: Directory to save outputs
    """
    if not HYBRID_MODEL_AVAILABLE:
        logger.warning("Hybrid model not available. Skipping hybrid model training.")
        return None

    logger.info("Training hybrid model for growth rate and temperature prediction...")

    # Initialize model
    model = HybridModelWithEnhancedTempDNN(
        growth_ensemble_weight_lr=0.01,
        growth_ensemble_weight_epochs=50,
        temp_dnn_hidden_dims=[512, 512, 384, 256, 128],
        temp_dnn_dropout_rates=[0.3, 0.4, 0.4, 0.5, 0.5],
        temp_dnn_lr=0.001,
        temp_dnn_batch_size=64,
        temp_dnn_epochs=100,
        temp_dnn_patience=15,
        variance_percentile=25
    )

    # Train model
    model.fit(
        X_train,
        y_growth_train,
        y_temp_train,
        X_val,
        y_growth_val,
        y_temp_val,
        output_dir=output_dir
    )

    # Save transform info
    if output_dir:
        joblib.dump(transform_info, os.path.join(output_dir, 'transform_info.joblib'))
        logger.info(f"Saved transform info to {output_dir}")

    return model

def main():
    parser = argparse.ArgumentParser(description="Predict and Train with Specialized Models")
    parser.add_argument("--feature_file", required=True, help="Path to feature file")
    parser.add_argument("--metadata_file", required=True, help="Path to metadata file")
    parser.add_argument("--output_dir", default="predictions", help="Directory to save predictions")
    parser.add_argument("--temp_model_dir", default="models/enhanced_dnn_temperature_v2", help="Directory containing the temperature model")
    parser.add_argument("--growth_model_dir", default="models/hybrid_enhanced_nn_v1", help="Directory containing the growth rate model")
    parser.add_argument("--target", default="both", choices=["growth", "temperature", "both"], help="Prediction target")
    parser.add_argument("--train", action="store_true", help="Train the models instead of just predicting")
    parser.add_argument("--n_features", type=int, default=150, help="Number of features to select for training")
    parser.add_argument("--use_zscore", action="store_true", help="Use z-score method for outlier detection")
    parser.add_argument("--zscore_threshold", type=float, default=7.0, help="Z-score threshold for outlier detection")
    parser.add_argument("--na_threshold", type=float, default=0.5, help="Threshold for NA percentage in features (0.5 = 50%)")
    args = parser.parse_args()

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Load data with z-score-based outlier detection and NA feature removal
    features, metadata = load_data(
        args.feature_file,
        args.metadata_file,
        use_zscore=args.use_zscore,
        zscore_threshold=args.zscore_threshold,
        na_threshold=args.na_threshold
    )

    # Extract targets
    y_growth = metadata['growth_rate']
    y_temp = metadata['optimal_temperature']

    if args.train:
        logger.info("Training mode activated")

        # Prepare data for training
        (
            X_train, y_growth_train, y_temp_train,
            X_val, y_growth_val, y_temp_val,
            X_test, y_growth_test, y_temp_test,
            selected_features, transform_info
        ) = prepare_data_for_training(features, metadata, args.n_features)

        # Create model directories
        temp_model_dir = os.path.join(args.output_dir, 'temperature_model')
        growth_model_dir = os.path.join(args.output_dir, 'growth_model')
        os.makedirs(temp_model_dir, exist_ok=True)
        os.makedirs(growth_model_dir, exist_ok=True)

        # Train temperature model
        if args.target in ['temperature', 'both']:
            logger.info("Training temperature model...")
            temp_model, temp_scaler = train_temperature_model(
                X_train, y_temp_train,
                X_val, y_temp_val,
                output_dir=temp_model_dir
            )

            # Evaluate temperature model on test set
            X_test_scaled = temp_scaler.transform(X_test)
            device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
            X_test_tensor = torch.tensor(X_test_scaled, dtype=torch.float32).to(device)

            with torch.no_grad():
                y_temp_pred = temp_model(X_test_tensor)
                y_temp_pred = y_temp_pred.cpu().numpy()

            # Calculate metrics
            temp_r2 = r2_score(y_temp_test, y_temp_pred)
            temp_rmse = np.sqrt(mean_squared_error(y_temp_test, y_temp_pred))
            temp_mae = mean_absolute_error(y_temp_test, y_temp_pred)

            logger.info(f"Temperature model test metrics - R²: {temp_r2:.4f}, RMSE: {temp_rmse:.4f}, MAE: {temp_mae:.4f}")

            # Save metrics
            pd.DataFrame({
                'metric': ['R2', 'RMSE', 'MAE'],
                'value': [temp_r2, temp_rmse, temp_mae]
            }).to_csv(os.path.join(temp_model_dir, 'test_metrics.tsv'), sep='\t', index=False)

            # Plot predictions
            plt.figure(figsize=(10, 6))
            plt.scatter(y_temp_test, y_temp_pred, alpha=0.5)
            plt.plot([min(y_temp_test), max(y_temp_test)], [min(y_temp_test), max(y_temp_test)], 'r--')
            plt.xlabel('Actual Temperature')
            plt.ylabel('Predicted Temperature')
            plt.title(f'Temperature Predictions (Test Set, R² = {temp_r2:.4f})')
            plt.tight_layout()
            plt.savefig(os.path.join(temp_model_dir, 'test_predictions.png'))

        # Train hybrid model for growth rate
        if args.target in ['growth', 'both']:
            logger.info("Training growth rate model...")

            if HYBRID_MODEL_AVAILABLE:
                # Train hybrid model
                hybrid_model = train_hybrid_model(
                    X_train, y_growth_train, y_temp_train,
                    X_val, y_growth_val, y_temp_val,
                    transform_info,
                    output_dir=growth_model_dir
                )

                if hybrid_model:
                    # Evaluate hybrid model on test set
                    metrics = hybrid_model.evaluate(X_test, y_growth_test, y_temp_test)

                    logger.info(f"Growth rate model test metrics - R²: {metrics['growth_rate']['R2']:.4f}, RMSE: {metrics['growth_rate']['RMSE']:.4f}, MAE: {metrics['growth_rate']['MAE']:.4f}")

                    # Save metrics
                    pd.DataFrame({
                        'metric': list(metrics['growth_rate'].keys()),
                        'value': list(metrics['growth_rate'].values())
                    }).to_csv(os.path.join(growth_model_dir, 'test_metrics.tsv'), sep='\t', index=False)

                    # Generate predictions
                    y_growth_pred, _ = hybrid_model.predict(X_test)

                    # Plot predictions
                    plt.figure(figsize=(10, 6))
                    plt.scatter(y_growth_test, y_growth_pred, alpha=0.5)
                    plt.plot([min(y_growth_test), max(y_growth_test)], [min(y_growth_test), max(y_growth_test)], 'r--')
                    plt.xlabel('Actual Growth Rate')
                    plt.ylabel('Predicted Growth Rate')
                    plt.title(f'Growth Rate Predictions (Test Set, R² = {metrics["growth_rate"]["R2"]:.4f})')
                    plt.tight_layout()
                    plt.savefig(os.path.join(growth_model_dir, 'test_predictions.png'))
            else:
                logger.warning("Hybrid model not available. Skipping growth rate model training.")

        logger.info(f"Training completed. Models saved to {args.output_dir}")
    else:
        logger.info("Prediction mode activated")

        # Initialize predictor
        predictor = SpecializedModelsPredictor(
            temp_model_dir=args.temp_model_dir,
            growth_model_dir=args.growth_model_dir
        )

        # Generate predictions
        if args.target == 'growth':
            y_growth_pred = predictor.predict(features, target='growth')

            # Save predictions
            pd.DataFrame({
                'genome_id': features.index,
                'actual_growth': y_growth,
                'predicted_growth': y_growth_pred
            }).to_csv(os.path.join(args.output_dir, 'growth_predictions.tsv'), sep='\t', index=False)

            # Plot predictions
            plt.figure(figsize=(10, 6))
            plt.scatter(y_growth, y_growth_pred, alpha=0.5)
            plt.plot([min(y_growth), max(y_growth)], [min(y_growth), max(y_growth)], 'r--')
            plt.xlabel('Actual Growth Rate')
            plt.ylabel('Predicted Growth Rate')
            plt.title('Growth Rate Predictions')
            plt.tight_layout()
            plt.savefig(os.path.join(args.output_dir, 'growth_predictions.png'))

            # Evaluate model
            metrics = predictor.evaluate(features, y_growth=y_growth, target='growth')

            # Save metrics
            pd.DataFrame({
                'metric': list(metrics['growth_rate'].keys()),
                'value': list(metrics['growth_rate'].values())
            }).to_csv(os.path.join(args.output_dir, 'growth_metrics.tsv'), sep='\t', index=False)

        elif args.target == 'temperature':
            y_temp_pred = predictor.predict(features, target='temperature')

            # Save predictions
            pd.DataFrame({
                'genome_id': features.index,
                'actual_temp': y_temp,
                'predicted_temp': y_temp_pred
            }).to_csv(os.path.join(args.output_dir, 'temperature_predictions.tsv'), sep='\t', index=False)

            # Plot predictions
            plt.figure(figsize=(10, 6))
            plt.scatter(y_temp, y_temp_pred, alpha=0.5)
            plt.plot([min(y_temp), max(y_temp)], [min(y_temp), max(y_temp)], 'r--')
            plt.xlabel('Actual Temperature')
            plt.ylabel('Predicted Temperature')
            plt.title('Temperature Predictions')
            plt.tight_layout()
            plt.savefig(os.path.join(args.output_dir, 'temperature_predictions.png'))

            # Evaluate model
            metrics = predictor.evaluate(features, y_temp=y_temp, target='temperature')

            # Save metrics
            pd.DataFrame({
                'metric': list(metrics['temperature'].keys()),
                'value': list(metrics['temperature'].values())
            }).to_csv(os.path.join(args.output_dir, 'temperature_metrics.tsv'), sep='\t', index=False)

        else:  # both
            y_growth_pred, y_temp_pred = predictor.predict(features, target='both')

            # Save predictions
            pd.DataFrame({
                'genome_id': features.index,
                'actual_growth': y_growth,
                'predicted_growth': y_growth_pred,
                'actual_temp': y_temp,
                'predicted_temp': y_temp_pred
            }).to_csv(os.path.join(args.output_dir, 'predictions.tsv'), sep='\t', index=False)

            # Plot predictions
            plt.figure(figsize=(12, 5))

            plt.subplot(1, 2, 1)
            plt.scatter(y_growth, y_growth_pred, alpha=0.5)
            plt.plot([min(y_growth), max(y_growth)], [min(y_growth), max(y_growth)], 'r--')
            plt.xlabel('Actual Growth Rate')
            plt.ylabel('Predicted Growth Rate')
            plt.title('Growth Rate Predictions')

            plt.subplot(1, 2, 2)
            plt.scatter(y_temp, y_temp_pred, alpha=0.5)
            plt.plot([min(y_temp), max(y_temp)], [min(y_temp), max(y_temp)], 'r--')
            plt.xlabel('Actual Temperature')
            plt.ylabel('Predicted Temperature')
            plt.title('Temperature Predictions')

            plt.tight_layout()
            plt.savefig(os.path.join(args.output_dir, 'predictions.png'))

            # Evaluate model
            metrics = predictor.evaluate(features, y_growth=y_growth, y_temp=y_temp, target='both')

            # Save metrics
            pd.DataFrame({
                'metric': ['R2', 'RMSE', 'MAE'],
                'growth_rate': [metrics['growth_rate']['R2'], metrics['growth_rate']['RMSE'], metrics['growth_rate']['MAE']],
                'temperature': [metrics['temperature']['R2'], metrics['temperature']['RMSE'], metrics['temperature']['MAE']],
                'overall': [metrics['overall']['R2'], metrics['overall']['RMSE'], metrics['overall']['MAE']]
            }).to_csv(os.path.join(args.output_dir, 'metrics.tsv'), sep='\t', index=False)

        logger.info(f"Predictions saved to {args.output_dir}")

if __name__ == "__main__":
    main()
