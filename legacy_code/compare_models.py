#!/usr/bin/env python3
import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import argparse
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error

def load_metrics(model_dirs):
    """Load metrics from each model directory.
    
    Args:
        model_dirs: List of model directories
        
    Returns:
        Dictionary of model metrics
    """
    metrics = {}
    
    for model_dir in model_dirs:
        model_name = os.path.basename(model_dir)
        metrics_file = os.path.join(model_dir, 'test_metrics.csv')
        
        if os.path.exists(metrics_file):
            metrics[model_name] = pd.read_csv(metrics_file)
        else:
            print(f"Warning: Metrics file not found in {model_dir}")
    
    return metrics

def create_comparison_table(metrics):
    """Create a comparison table of different models.
    
    Args:
        metrics: Dictionary of model metrics
        
    Returns:
        DataFrame with comparison metrics
    """
    comparison = pd.DataFrame()
    
    for model_name, model_metrics in metrics.items():
        # Extract relevant metrics
        row = {
            'Model': model_name,
            'Growth Rate R²': model_metrics['growth_r2'].values[0],
            'Growth Rate RMSE': model_metrics['growth_rmse'].values[0],
            'Growth Rate MAE': model_metrics['growth_mae'].values[0],
            'Temperature R²': model_metrics['temp_r2'].values[0],
            'Temperature RMSE': model_metrics['temp_rmse'].values[0],
            'Temperature MAE': model_metrics['temp_mae'].values[0]
        }
        
        # Add to comparison table
        comparison = pd.concat([comparison, pd.DataFrame([row])], ignore_index=True)
    
    return comparison

def plot_comparison(comparison, output_dir):
    """Plot comparison of different models.
    
    Args:
        comparison: DataFrame with comparison metrics
        output_dir: Directory to save plots
    """
    os.makedirs(output_dir, exist_ok=True)
    
    # Set style for plots
    sns.set(style='whitegrid')
    plt.figure(figsize=(12, 8))
    
    # Plot R² comparison
    plt.subplot(2, 2, 1)
    sns.barplot(x='Model', y='Growth Rate R²', data=comparison)
    plt.title('Growth Rate R² Comparison')
    plt.xticks(rotation=45)
    plt.ylim(0.9, 1.0)  # Assuming R² values are high
    
    plt.subplot(2, 2, 2)
    sns.barplot(x='Model', y='Temperature R²', data=comparison)
    plt.title('Temperature R² Comparison')
    plt.xticks(rotation=45)
    plt.ylim(0.8, 0.9)  # Adjust based on your typical R² values
    
    # Plot RMSE comparison
    plt.subplot(2, 2, 3)
    sns.barplot(x='Model', y='Growth Rate RMSE', data=comparison)
    plt.title('Growth Rate RMSE Comparison')
    plt.xticks(rotation=45)
    
    plt.subplot(2, 2, 4)
    sns.barplot(x='Model', y='Temperature RMSE', data=comparison)
    plt.title('Temperature RMSE Comparison')
    plt.xticks(rotation=45)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'model_comparison.png'), dpi=300)
    plt.close()
    
    # Plot MAE comparison
    plt.figure(figsize=(12, 6))
    
    plt.subplot(1, 2, 1)
    sns.barplot(x='Model', y='Growth Rate MAE', data=comparison)
    plt.title('Growth Rate MAE Comparison')
    plt.xticks(rotation=45)
    
    plt.subplot(1, 2, 2)
    sns.barplot(x='Model', y='Temperature MAE', data=comparison)
    plt.title('Temperature MAE Comparison')
    plt.xticks(rotation=45)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'model_comparison_mae.png'), dpi=300)
    
    print(f"Plots saved to {output_dir}")

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Compare model performance')
    parser.add_argument('--model_dirs', nargs='+', required=True, help='List of model directories to compare')
    parser.add_argument('--output_dir', type=str, default='comparison_results', help='Directory to save comparison results')
    args = parser.parse_args()
    
    # Load metrics from each model
    metrics = load_metrics(args.model_dirs)
    
    # Create comparison table
    comparison = create_comparison_table(metrics)
    
    # Print comparison table
    print("\nModel Comparison:")
    print(comparison.to_string(index=False))
    
    # Save comparison table
    os.makedirs(args.output_dir, exist_ok=True)
    comparison.to_csv(os.path.join(args.output_dir, 'model_comparison.csv'), index=False)
    
    # Create and save plots
    plot_comparison(comparison, args.output_dir)
    
    # Find best model for each target
    best_growth_model = comparison.loc[comparison['Growth Rate R²'].idxmax()]['Model']
    best_temp_model = comparison.loc[comparison['Temperature R²'].idxmax()]['Model']
    
    print(f"\nBest model for growth rate: {best_growth_model}")
    print(f"Best model for temperature: {best_temp_model}")

if __name__ == '__main__':
    main()