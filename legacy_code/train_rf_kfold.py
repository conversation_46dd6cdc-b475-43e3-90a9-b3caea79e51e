#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Train a Random Forest model for DeepMu using k-fold cross validation
to get more robust performance estimates and optimize hyperparameters.

This script includes support for:
1. K-fold cross-validation for reliable performance estimation
2. Hyperparameter tuning to find optimal model settings
3. Feature importance analysis by category
4. Detailed visualization of model performance
5. Support for both growth rate and temperature prediction
"""

import os
import argparse
import logging
import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from sklearn.model_selection import KFold, GridSearchCV
import joblib
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def train_rf_with_kfold(feature_dir, metadata_file, output_dir, target='growth_rate', metrics_dir=None,
                        n_estimators=200, max_depth=None, min_samples_split=2, min_samples_leaf=1,
                        max_features='sqrt', n_folds=5, random_state=42, tune_hyperparams=False):
    """
    Train a Random Forest model using k-fold cross validation.

    Args:
        feature_dir: Directory containing feature files
        metadata_file: Path to metadata file
        output_dir: Directory to save model and results
        target: Target variable to predict ('growth_rate' or 'temperature')
        metrics_dir: Directory to save metrics and plots
        n_estimators: Number of trees in the forest
        max_depth: Maximum depth of the trees
        min_samples_split: Minimum number of samples required to split an internal node
        min_samples_leaf: Minimum number of samples required to be at a leaf node
        max_features: Number of features to consider for the best split
        n_folds: Number of folds for cross-validation
        random_state: Random state for reproducibility
        tune_hyperparams: Whether to perform hyperparameter tuning

    Returns:
        Dictionary with training results
    """
    logger.info(f"Training Random Forest model for {target} with {n_folds}-fold cross validation")

    # Create output directory
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)

    # Create metrics directory
    if metrics_dir is None:
        metrics_dir = Path(f'metrics/rf_kfold_{n_folds}')
    else:
        metrics_dir = Path(metrics_dir)
    metrics_dir.mkdir(parents=True, exist_ok=True)

    # Load metadata
    logger.info(f"Loading metadata from {metadata_file}")
    metadata = pd.read_csv(metadata_file, sep='\t')
    if 'genome_id' not in metadata.columns:
        metadata = metadata.reset_index()
    logger.info(f"Loaded metadata with {len(metadata)} samples")

    # Load features
    logger.info("Loading genomic features")
    if os.path.isdir(feature_dir):
        features_file = os.path.join(feature_dir, 'combined_features.tsv')
    else:
        features_file = feature_dir  # Assume feature_dir is the path to the combined features file

    logger.info(f"Loading combined features from {features_file}")
    features = pd.read_csv(features_file, sep='\t')
    if 'genome_id' not in features.columns:
        features = features.reset_index()

    # Merge features with metadata
    common_ids = set(metadata['genome_id']).intersection(set(features['genome_id']))
    logger.info(f"Common IDs between metadata and features: {len(common_ids)}")

    # Filter metadata and features to common IDs
    metadata = metadata[metadata['genome_id'].isin(common_ids)]
    features = features[features['genome_id'].isin(common_ids)]

    # Ensure same order
    metadata = metadata.set_index('genome_id')
    features = features.set_index('genome_id')
    metadata = metadata.loc[features.index]

    # Check for NaN values in metadata
    if metadata[target].isna().any():
        logger.warning(f"Found NaN values in {target}. Removing affected rows.")
        metadata = metadata.dropna(subset=[target])
        # Update features to match metadata
        features = features.loc[metadata.index]
        logger.info(f"After removing NaN values, {len(metadata)} samples remain")

    # Check for NaN values in features
    nan_count = features.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in features. Filling with zeros.")
        features = features.fillna(0)

    # Check for infinite values in features
    inf_count = np.isinf(features).sum().sum()
    if inf_count > 0:
        logger.warning(f"Found {inf_count} infinite values in features. Replacing with zeros.")
        features = features.replace([np.inf, -np.inf], 0)

    # Get target variable
    if target == 'growth_rate':
        y = metadata['growth_rate'].values
        target_name = 'Growth Rate'
    else:  # temperature
        y = metadata['optimal_temperature'].values
        target_name = 'Optimal Temperature'

    # Get feature matrix - exclude metadata columns
    metadata_cols = ['genome_id', 'growth_rate', 'optimal_temperature', 'taxid', 'genetic_code', 'kingdom']
    feature_cols = [col for col in features.columns if col not in metadata_cols]

    X = features[feature_cols].values
    feature_names = feature_cols

    logger.info(f"Feature matrix shape: {X.shape}")
    logger.info(f"Target vector shape: {y.shape}")

    # Check for NaN or infinite values in feature matrix
    if np.isnan(X).any():
        logger.warning(f"Found NaN values in feature matrix. Filling with zeros.")
        X = np.nan_to_num(X, nan=0.0)

    if np.isinf(X).any():
        logger.warning(f"Found infinite values in feature matrix. Replacing with zeros.")
        X = np.nan_to_num(X, posinf=0.0, neginf=0.0)

    # Standardize features
    try:
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)

        # Check for NaN or infinite values after scaling
        if np.isnan(X_scaled).any():
            logger.warning(f"Scaling produced NaN values. Using robust scaling.")
            X_scaled = np.nan_to_num(X_scaled, nan=0.0)

        if np.isinf(X_scaled).any():
            logger.warning(f"Scaling produced infinite values. Using robust scaling.")
            X_scaled = np.nan_to_num(X_scaled, posinf=0.0, neginf=0.0)

    except Exception as e:
        logger.warning(f"Error during scaling: {str(e)}. Using unscaled data.")
        X_scaled = X

    # Save scaler
    scaler_path = os.path.join(output_dir, f'{target}_scaler.joblib')
    joblib.dump(scaler, scaler_path)
    logger.info(f"Saved scaler to {scaler_path}")

    # Initialize k-fold cross validation
    kf = KFold(n_splits=n_folds, shuffle=True, random_state=random_state)

    # Initialize lists to store results
    train_r2_scores = []
    train_rmse_scores = []
    train_mae_scores = []
    test_r2_scores = []
    test_rmse_scores = []
    test_mae_scores = []
    feature_importances = []
    fold_predictions = []

    # Hyperparameter tuning if requested
    if tune_hyperparams:
        logger.info("Performing hyperparameter tuning")
        param_grid = {
            'n_estimators': [100, 200, 300],
            'max_depth': [None, 10, 20, 30],
            'min_samples_split': [2, 5, 10],
            'min_samples_leaf': [1, 2, 4],
            'max_features': ['sqrt', 'log2', 0.3]
        }

        # Initialize GridSearchCV
        grid_search = GridSearchCV(
            RandomForestRegressor(random_state=random_state),
            param_grid=param_grid,
            cv=kf,
            scoring='neg_mean_squared_error',
            n_jobs=-1,
            verbose=1
        )

        # Fit GridSearchCV
        grid_search.fit(X_scaled, y)

        # Get best parameters
        best_params = grid_search.best_params_
        logger.info(f"Best parameters: {best_params}")

        # Update parameters
        n_estimators = best_params['n_estimators']
        max_depth = best_params['max_depth']
        min_samples_split = best_params['min_samples_split']
        min_samples_leaf = best_params['min_samples_leaf']
        max_features = best_params['max_features']

    # Perform k-fold cross validation
    for fold, (train_idx, test_idx) in enumerate(kf.split(X_scaled)):
        logger.info(f"Training fold {fold+1}/{n_folds}")

        # Split data
        X_train, X_test = X_scaled[train_idx], X_scaled[test_idx]
        y_train, y_test = y[train_idx], y[test_idx]

        # Train model
        model = RandomForestRegressor(
            n_estimators=n_estimators,
            max_depth=max_depth,
            min_samples_split=min_samples_split,
            min_samples_leaf=min_samples_leaf,
            max_features=max_features,
            random_state=random_state,
            n_jobs=-1
        )
        model.fit(X_train, y_train)

        # Make predictions
        y_train_pred = model.predict(X_train)
        y_test_pred = model.predict(X_test)

        # Calculate metrics
        train_r2 = r2_score(y_train, y_train_pred)
        train_rmse = np.sqrt(mean_squared_error(y_train, y_train_pred))
        train_mae = mean_absolute_error(y_train, y_train_pred)

        test_r2 = r2_score(y_test, y_test_pred)
        test_rmse = np.sqrt(mean_squared_error(y_test, y_test_pred))
        test_mae = mean_absolute_error(y_test, y_test_pred)

        # Store metrics
        train_r2_scores.append(train_r2)
        train_rmse_scores.append(train_rmse)
        train_mae_scores.append(train_mae)
        test_r2_scores.append(test_r2)
        test_rmse_scores.append(test_rmse)
        test_mae_scores.append(test_mae)

        # Store feature importances
        feature_importances.append(model.feature_importances_)

        # Store predictions
        fold_predictions.append({
            'fold': fold + 1,
            'test_indices': test_idx,
            'actual': y_test,
            'predicted': y_test_pred
        })

        logger.info(f"Fold {fold+1} - Train: R²={train_r2:.4f}, RMSE={train_rmse:.4f}, MAE={train_mae:.4f}")
        logger.info(f"Fold {fold+1} - Test: R²={test_r2:.4f}, RMSE={test_rmse:.4f}, MAE={test_mae:.4f}")

    # Calculate average metrics
    avg_train_r2 = np.mean(train_r2_scores)
    avg_train_rmse = np.mean(train_rmse_scores)
    avg_train_mae = np.mean(train_mae_scores)
    avg_test_r2 = np.mean(test_r2_scores)
    avg_test_rmse = np.mean(test_rmse_scores)
    avg_test_mae = np.mean(test_mae_scores)

    std_train_r2 = np.std(train_r2_scores)
    std_train_rmse = np.std(train_rmse_scores)
    std_train_mae = np.std(train_mae_scores)
    std_test_r2 = np.std(test_r2_scores)
    std_test_rmse = np.std(test_rmse_scores)
    std_test_mae = np.std(test_mae_scores)

    logger.info(f"Average Train: R²={avg_train_r2:.4f}±{std_train_r2:.4f}, RMSE={avg_train_rmse:.4f}±{std_train_rmse:.4f}, MAE={avg_train_mae:.4f}±{std_train_mae:.4f}")
    logger.info(f"Average Test: R²={avg_test_r2:.4f}±{std_test_r2:.4f}, RMSE={avg_test_rmse:.4f}±{std_test_rmse:.4f}, MAE={avg_test_mae:.4f}±{std_test_mae:.4f}")

    # Calculate average feature importances
    avg_feature_importances = np.mean(feature_importances, axis=0)

    # Create feature importance dataframe
    importance_df = pd.DataFrame({
        'feature': feature_names,
        'importance': avg_feature_importances
    })
    importance_df = importance_df.sort_values('importance', ascending=False)

    # Save feature importance
    importance_path = os.path.join(metrics_dir, f'{target}_feature_importance.tsv')
    importance_df.to_csv(importance_path, sep='\t', index=False)
    logger.info(f"Saved feature importance to {importance_path}")

    # Categorize features
    feature_categories = {
        'codon': [],
        'aa': [],
        'genomic': [],
        'rna': [],
        'pi': [],
        'taxonomy': [],
        'pathway': [],
        'other': []
    }

    # Categorize features based on patterns
    for feature in feature_names:
        feature_lower = feature.lower()

        # Codon features
        if any(keyword in feature_lower for keyword in [
            'cub', 'cpb', 'cai', 'enc', 'rscu', 'codon', 'heg', 'bg', 'delta',
            'deviation', 'divergence', 'composite', 'context_bias', 'aaai', 'milc'
        ]):
            feature_categories['codon'].append(feature)

        # Amino acid features
        elif any(keyword in feature_lower for keyword in [
            'aa_', 'amino', 'protein', 'arsc', 'nitrogen', 'carbon', 'c_arsc', 'n_arsc'
        ]):
            feature_categories['aa'].append(feature)

        # Genomic features
        elif any(keyword in feature_lower for keyword in [
            'genome', 'gc_', 'gc1', 'gc2', 'gc3', 'dinuc', 'size', 'length', 'skew',
            '_change_points', '_sign_changes', '_mean', '_std', '_min', '_max', '_range',
            'breakpoint', 'freq_'
        ]):
            feature_categories['genomic'].append(feature)

        # RNA features
        elif any(keyword in feature_lower for keyword in [
            'trna', 'rrna', 'tai', 'rna', 'rrna_count', 'trna_count'
        ]):
            feature_categories['rna'].append(feature)

        # Protein pI features
        elif any(keyword in feature_lower for keyword in [
            'pi_', 'isoelectric', 'ph', 'pi_mean', 'pi_median', 'pi_std'
        ]):
            feature_categories['pi'].append(feature)

        # Taxonomy features
        elif any(keyword in feature_lower for keyword in [
            'phylum', 'class', 'order', 'family', 'genus', 'taxid', 'taxonomy'
        ]):
            feature_categories['taxonomy'].append(feature)

        # Pathway features
        elif any(keyword in feature_lower for keyword in [
            'pathway', 'kegg', 'ko_', 'module', 'completeness'
        ]) or feature.startswith('ko'):
            feature_categories['pathway'].append(feature)

        # Other features
        else:
            feature_categories['other'].append(feature)

    # Log feature counts by category
    for category, features in feature_categories.items():
        logger.info(f"Found {len(features)} {category} features")

    # Create and save feature importance by category
    for category, features in feature_categories.items():
        if not features:
            continue

        # Create category feature importance dataframe
        category_importance_df = importance_df[importance_df['feature'].isin(features)]
        category_importance_df = category_importance_df.sort_values('importance', ascending=False)

        # Save category feature importance
        category_importance_path = os.path.join(metrics_dir, f'{target}_{category}_feature_importance.tsv')
        category_importance_df.to_csv(category_importance_path, sep='\t', index=False)
        logger.info(f"Saved {category} feature importance to {category_importance_path}")

        # Log top features in this category
        if len(category_importance_df) > 0:
            logger.info(f"Top 10 {category} features for {target}:")
            for i, (feature, imp) in enumerate(zip(category_importance_df['feature'].head(10),
                                                category_importance_df['importance'].head(10))):
                logger.info(f"{i+1}. {feature}: {imp:.6f}")

    # Create feature importance by category summary
    category_summary = []
    for category, features in feature_categories.items():
        if not features:
            continue

        category_importance = importance_df[importance_df['feature'].isin(features)]['importance'].sum()
        category_summary.append({
            'category': category,
            'feature_count': len(features),
            'total_importance': category_importance
        })

    # Create and save category summary
    category_summary_df = pd.DataFrame(category_summary)
    category_summary_df = category_summary_df.sort_values('total_importance', ascending=False)
    category_summary_path = os.path.join(metrics_dir, f'{target}_category_summary.tsv')
    category_summary_df.to_csv(category_summary_path, sep='\t', index=False)
    logger.info(f"Saved category summary to {category_summary_path}")

    # Log category summary
    logger.info(f"Feature importance by category for {target}:")
    for i, row in category_summary_df.iterrows():
        logger.info(f"{row['category']}: {row['feature_count']} features, {row['total_importance']:.6f} total importance")

    # Train final model on all data
    logger.info("Training final model on all data")
    final_model = RandomForestRegressor(
        n_estimators=n_estimators,
        max_depth=max_depth,
        min_samples_split=min_samples_split,
        min_samples_leaf=min_samples_leaf,
        max_features=max_features,
        random_state=random_state,
        n_jobs=-1
    )
    final_model.fit(X_scaled, y)

    # Save final model
    model_path = os.path.join(output_dir, f'{target}_model.joblib')
    joblib.dump(final_model, model_path)
    logger.info(f"Saved final model to {model_path}")

    # Combine all test predictions
    all_predictions = pd.DataFrame()
    for fold_pred in fold_predictions:
        fold_df = pd.DataFrame({
            'fold': fold_pred['fold'],
            'actual': fold_pred['actual'],
            'predicted': fold_pred['predicted']
        })
        all_predictions = pd.concat([all_predictions, fold_df])

    # Save all predictions
    predictions_path = os.path.join(metrics_dir, f'{target}_all_predictions.tsv')
    all_predictions.to_csv(predictions_path, sep='\t', index=False)

    # Plot overall feature importance
    plt.figure(figsize=(14, 10))
    bars = plt.barh(importance_df['feature'].values[:20], importance_df['importance'].values[:20], color='skyblue')
    plt.xlabel('Importance', fontsize=12)
    plt.ylabel('Feature', fontsize=12)
    plt.title(f'Top 20 Features for {target_name}', fontsize=14)
    plt.grid(True, alpha=0.3, axis='x')

    # Add values to bars
    for bar in bars:
        width = bar.get_width()
        plt.text(width + 0.002, bar.get_y() + bar.get_height()/2,
                 f'{width:.4f}', ha='left', va='center', fontsize=10)

    plt.tight_layout()
    plt.savefig(os.path.join(metrics_dir, f'{target}_feature_importance.png'), dpi=300)
    plt.close()

    # Plot feature importance by category
    plt.figure(figsize=(12, 8))
    bars = plt.bar(category_summary_df['category'], category_summary_df['total_importance'], color='lightgreen')
    plt.xlabel('Feature Category', fontsize=12)
    plt.ylabel('Total Importance', fontsize=12)
    plt.title(f'Feature Importance by Category for {target_name}', fontsize=14)
    plt.xticks(rotation=45, ha='right')
    plt.grid(True, alpha=0.3, axis='y')

    # Add values to bars
    for bar in bars:
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2, height + 0.01,
                 f'{height:.4f}', ha='center', va='bottom', fontsize=10)

    plt.tight_layout()
    plt.savefig(os.path.join(metrics_dir, f'{target}_category_importance.png'), dpi=300)
    plt.close()

    # Plot feature importance for each category
    for category, features in feature_categories.items():
        if not features:
            continue

        category_importance_df = importance_df[importance_df['feature'].isin(features)]
        if len(category_importance_df) > 0:
            plt.figure(figsize=(14, 10))
            top_n = min(20, len(category_importance_df))
            bars = plt.barh(
                category_importance_df['feature'].values[:top_n],
                category_importance_df['importance'].values[:top_n],
                color='lightblue'
            )
            plt.xlabel('Importance', fontsize=12)
            plt.ylabel('Feature', fontsize=12)
            plt.title(f'Top {top_n} {category.capitalize()} Features for {target_name}', fontsize=14)
            plt.grid(True, alpha=0.3, axis='x')

            # Add values to bars
            for bar in bars:
                width = bar.get_width()
                plt.text(width + 0.002, bar.get_y() + bar.get_height()/2,
                         f'{width:.4f}', ha='left', va='center', fontsize=10)

            plt.tight_layout()
            plt.savefig(os.path.join(metrics_dir, f'{target}_{category}_importance.png'), dpi=300)
            plt.close()

    # Plot actual vs predicted
    plt.figure(figsize=(12, 10))

    # Create a colormap for the folds
    colors = plt.cm.tab10(np.linspace(0, 1, n_folds))

    # Plot each fold with a different color
    for fold in range(1, n_folds + 1):
        fold_data = all_predictions[all_predictions['fold'] == fold]
        plt.scatter(
            fold_data['actual'],
            fold_data['predicted'],
            alpha=0.7,
            color=colors[fold-1],
            edgecolor='k',
            s=80,
            label=f'Fold {fold}'
        )

    # Add identity line
    min_val = min(all_predictions['actual'].min(), all_predictions['predicted'].min())
    max_val = max(all_predictions['actual'].max(), all_predictions['predicted'].max())
    plt.plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2)

    # Calculate overall metrics
    r2 = r2_score(all_predictions['actual'], all_predictions['predicted'])
    rmse = np.sqrt(mean_squared_error(all_predictions['actual'], all_predictions['predicted']))
    mae = mean_absolute_error(all_predictions['actual'], all_predictions['predicted'])

    # Add metrics text
    plt.text(0.05, 0.95, f"R² = {r2:.4f}", transform=plt.gca().transAxes, fontsize=14,
             bbox=dict(facecolor='white', alpha=0.8, edgecolor='gray', boxstyle='round,pad=0.5'))
    plt.text(0.05, 0.88, f"RMSE = {rmse:.4f}", transform=plt.gca().transAxes, fontsize=14,
             bbox=dict(facecolor='white', alpha=0.8, edgecolor='gray', boxstyle='round,pad=0.5'))
    plt.text(0.05, 0.81, f"MAE = {mae:.4f}", transform=plt.gca().transAxes, fontsize=14,
             bbox=dict(facecolor='white', alpha=0.8, edgecolor='gray', boxstyle='round,pad=0.5'))

    plt.xlabel(f'Actual {target_name}', fontsize=14)
    plt.ylabel(f'Predicted {target_name}', fontsize=14)
    plt.title(f'Actual vs Predicted {target_name}', fontsize=16)
    plt.legend(fontsize=12)
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig(os.path.join(metrics_dir, f'{target}_actual_vs_predicted.png'), dpi=300)
    plt.close()

    # Plot cross-validation metrics
    plt.figure(figsize=(15, 8))

    # R² scores
    plt.subplot(1, 3, 1)
    data = pd.DataFrame({
        'Train': train_r2_scores,
        'Test': test_r2_scores
    })
    sns.boxplot(data=data, palette=['lightblue', 'lightgreen'])
    plt.title('R² Scores', fontsize=14)
    plt.ylim(0, 1)
    plt.grid(True, alpha=0.3)

    # Add mean values as text
    plt.text(0, 0.05, f"Mean: {np.mean(train_r2_scores):.4f}", ha='center', fontsize=12)
    plt.text(1, 0.05, f"Mean: {np.mean(test_r2_scores):.4f}", ha='center', fontsize=12)

    # RMSE scores
    plt.subplot(1, 3, 2)
    data = pd.DataFrame({
        'Train': train_rmse_scores,
        'Test': test_rmse_scores
    })
    sns.boxplot(data=data, palette=['lightblue', 'lightgreen'])
    plt.title('RMSE Scores', fontsize=14)
    plt.grid(True, alpha=0.3)

    # Add mean values as text
    plt.text(0, min(train_rmse_scores) * 0.9, f"Mean: {np.mean(train_rmse_scores):.4f}", ha='center', fontsize=12)
    plt.text(1, min(test_rmse_scores) * 0.9, f"Mean: {np.mean(test_rmse_scores):.4f}", ha='center', fontsize=12)

    # MAE scores
    plt.subplot(1, 3, 3)
    data = pd.DataFrame({
        'Train': train_mae_scores,
        'Test': test_mae_scores
    })
    sns.boxplot(data=data, palette=['lightblue', 'lightgreen'])
    plt.title('MAE Scores', fontsize=14)
    plt.grid(True, alpha=0.3)

    # Add mean values as text
    plt.text(0, min(train_mae_scores) * 0.9, f"Mean: {np.mean(train_mae_scores):.4f}", ha='center', fontsize=12)
    plt.text(1, min(test_mae_scores) * 0.9, f"Mean: {np.mean(test_mae_scores):.4f}", ha='center', fontsize=12)

    plt.tight_layout()
    plt.savefig(os.path.join(metrics_dir, f'{target}_cv_metrics.png'), dpi=300)
    plt.close()

    # Plot metrics by fold
    plt.figure(figsize=(15, 10))

    # R² scores by fold
    plt.subplot(2, 2, 1)
    plt.plot(range(1, n_folds + 1), train_r2_scores, 'o-', color='blue', label='Train')
    plt.plot(range(1, n_folds + 1), test_r2_scores, 'o-', color='green', label='Test')
    plt.xlabel('Fold', fontsize=12)
    plt.ylabel('R²', fontsize=12)
    plt.title('R² by Fold', fontsize=14)
    plt.grid(True, alpha=0.3)
    plt.legend()

    # RMSE scores by fold
    plt.subplot(2, 2, 2)
    plt.plot(range(1, n_folds + 1), train_rmse_scores, 'o-', color='blue', label='Train')
    plt.plot(range(1, n_folds + 1), test_rmse_scores, 'o-', color='green', label='Test')
    plt.xlabel('Fold', fontsize=12)
    plt.ylabel('RMSE', fontsize=12)
    plt.title('RMSE by Fold', fontsize=14)
    plt.grid(True, alpha=0.3)
    plt.legend()

    # MAE scores by fold
    plt.subplot(2, 2, 3)
    plt.plot(range(1, n_folds + 1), train_mae_scores, 'o-', color='blue', label='Train')
    plt.plot(range(1, n_folds + 1), test_mae_scores, 'o-', color='green', label='Test')
    plt.xlabel('Fold', fontsize=12)
    plt.ylabel('MAE', fontsize=12)
    plt.title('MAE by Fold', fontsize=14)
    plt.grid(True, alpha=0.3)
    plt.legend()

    # Train vs Test R² scatter
    plt.subplot(2, 2, 4)
    plt.scatter(train_r2_scores, test_r2_scores, s=100, alpha=0.7, color='purple', edgecolor='k')

    # Add fold numbers as labels
    for i, (x, y) in enumerate(zip(train_r2_scores, test_r2_scores)):
        plt.text(x, y, str(i+1), fontsize=12)

    # Add diagonal line
    min_r2 = min(min(train_r2_scores), min(test_r2_scores))
    max_r2 = max(max(train_r2_scores), max(test_r2_scores))
    plt.plot([min_r2, max_r2], [min_r2, max_r2], 'r--', linewidth=2)

    plt.xlabel('Train R²', fontsize=12)
    plt.ylabel('Test R²', fontsize=12)
    plt.title('Train vs Test R²', fontsize=14)
    plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(metrics_dir, f'{target}_metrics_by_fold.png'), dpi=300)
    plt.close()

    # Calculate overall metrics from all predictions
    overall_r2 = r2_score(all_predictions['actual'], all_predictions['predicted'])
    overall_rmse = np.sqrt(mean_squared_error(all_predictions['actual'], all_predictions['predicted']))
    overall_mae = mean_absolute_error(all_predictions['actual'], all_predictions['predicted'])

    # Save results
    results = {
        'avg_train_r2': avg_train_r2,
        'avg_train_rmse': avg_train_rmse,
        'avg_train_mae': avg_train_mae,
        'avg_test_r2': avg_test_r2,
        'avg_test_rmse': avg_test_rmse,
        'avg_test_mae': avg_test_mae,
        'std_train_r2': std_train_r2,
        'std_train_rmse': std_train_rmse,
        'std_train_mae': std_train_mae,
        'std_test_r2': std_test_r2,
        'std_test_rmse': std_test_rmse,
        'std_test_mae': std_test_mae,
        'overall_r2': overall_r2,
        'overall_rmse': overall_rmse,
        'overall_mae': overall_mae,
        'n_estimators': n_estimators,
        'max_depth': max_depth,
        'min_samples_split': min_samples_split,
        'min_samples_leaf': min_samples_leaf,
        'max_features': max_features,
        'n_folds': n_folds,
        'feature_count': len(feature_names),
        'sample_count': len(y)
    }

    # Save results as TSV
    results_df = pd.DataFrame([results])
    results_path = os.path.join(metrics_dir, f'{target}_results.tsv')
    results_df.to_csv(results_path, sep='\t', index=False)
    logger.info(f"Saved results to {results_path}")

    # Save results as JSON
    json_path = os.path.join(metrics_dir, f'{target}_results.json')
    with open(json_path, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    logger.info(f"Saved results as JSON to {json_path}")

    # Save model configuration
    config = {
        'n_estimators': n_estimators,
        'max_depth': max_depth,
        'min_samples_split': min_samples_split,
        'min_samples_leaf': min_samples_leaf,
        'max_features': max_features,
        'n_folds': n_folds,
        'random_state': random_state,
        'feature_count': len(feature_names),
        'sample_count': y.shape[0] if hasattr(y, 'shape') else len(y) if hasattr(y, '__len__') else 0,
        'target': target,
        'tune_hyperparams': tune_hyperparams
    }

    config_path = os.path.join(output_dir, f'{target}_config.json')
    with open(config_path, 'w') as f:
        json.dump(config, f, indent=2, default=str)
    logger.info(f"Saved model configuration to {config_path}")

    # Log final results
    logger.info(f"Final results for {target_name}:")
    logger.info(f"  Train R²: {avg_train_r2:.4f} ± {std_train_r2:.4f}")
    logger.info(f"  Test R²: {avg_test_r2:.4f} ± {std_test_r2:.4f}")
    logger.info(f"  Train RMSE: {avg_train_rmse:.4f} ± {std_train_rmse:.4f}")
    logger.info(f"  Test RMSE: {avg_test_rmse:.4f} ± {std_test_rmse:.4f}")
    logger.info(f"  Train MAE: {avg_train_mae:.4f} ± {std_train_mae:.4f}")
    logger.info(f"  Test MAE: {avg_test_mae:.4f} ± {std_test_mae:.4f}")
    logger.info(f"  Overall R²: {overall_r2:.4f}")
    logger.info(f"  Overall RMSE: {overall_rmse:.4f}")
    logger.info(f"  Overall MAE: {overall_mae:.4f}")

    return results

def main():
    parser = argparse.ArgumentParser(description='Train a Random Forest model for DeepMu using k-fold cross validation')
    parser.add_argument('--feature-file', type=str, default='training_data/combined_features.tsv',
                        help='Path to combined features file (TSV)')
    parser.add_argument('--metadata', type=str, default='training_data/metadata.tsv',
                        help='Path to metadata file (TSV)')
    parser.add_argument('--output-dir', type=str, default='models/rf_kfold',
                        help='Directory to save model and results')
    parser.add_argument('--metrics-dir', type=str, default=None,
                        help='Directory to save metrics and plots')
    parser.add_argument('--target', type=str, choices=['growth_rate', 'temperature', 'both'], default='both',
                        help='Target variable to predict')
    parser.add_argument('--n-estimators', type=int, default=200,
                        help='Number of trees in the forest')
    parser.add_argument('--max-depth', type=int, default=None,
                        help='Maximum depth of the trees (None for unlimited)')
    parser.add_argument('--min-samples-split', type=int, default=2,
                        help='Minimum number of samples required to split an internal node')
    parser.add_argument('--min-samples-leaf', type=int, default=1,
                        help='Minimum number of samples required to be at a leaf node')
    parser.add_argument('--max-features', type=str, default='sqrt',
                        help='Number of features to consider for the best split (sqrt, log2, or float)')
    parser.add_argument('--n-folds', type=int, default=5,
                        help='Number of folds for cross-validation')
    parser.add_argument('--random-state', type=int, default=42,
                        help='Random state for reproducibility')
    parser.add_argument('--tune-hyperparams', action='store_true',
                        help='Perform hyperparameter tuning')

    args = parser.parse_args()

    # Set metrics directory if not provided
    if args.metrics_dir is None:
        args.metrics_dir = f'metrics/rf_kfold_{args.n_folds}'

    # Log all arguments
    logger.info("Training with the following parameters:")
    for arg, value in vars(args).items():
        logger.info(f"  {arg}: {value}")

    try:
        # Create output and metrics directories
        os.makedirs(args.output_dir, exist_ok=True)
        os.makedirs(args.metrics_dir, exist_ok=True)

        # Train models
        if args.target in ['growth_rate', 'both']:
            logger.info("Training Random Forest model for growth rate prediction")
            train_rf_with_kfold(
                args.feature_file,
                args.metadata,
                args.output_dir,
                'growth_rate',
                args.metrics_dir,
                args.n_estimators,
                args.max_depth,
                args.min_samples_split,
                args.min_samples_leaf,
                args.max_features,
                args.n_folds,
                args.random_state,
                args.tune_hyperparams
            )

        if args.target in ['temperature', 'both']:
            logger.info("Training Random Forest model for optimal temperature prediction")
            train_rf_with_kfold(
                args.feature_file,
                args.metadata,
                args.output_dir,
                'temperature',
                args.metrics_dir,
                args.n_estimators,
                args.max_depth,
                args.min_samples_split,
                args.min_samples_leaf,
                args.max_features,
                args.n_folds,
                args.random_state,
                args.tune_hyperparams
            )

        logger.info("Random Forest model training with k-fold cross validation completed successfully")
        return 0

    except Exception as e:
        logger.error(f"Error training Random Forest model: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == '__main__':
    main()
