    # Create a DataFrame with feature names and importances
    importance_df = pd.DataFrame({
        'Feature': feature_names,
        'Importance': importances
    })
    
    # Sort by importance and get top N
    importance_df = importance_df.sort_values('Importance', ascending=False)
    top_importance_df = importance_df.head(top_n)
    
    return importance_df, top_importance_df

def extract_feature_selection_importance(feature_selection, top_n=30):
    """Extract feature importance from feature selection information."""
    if not feature_selection or 'feature_scores' not in feature_selection:
        logger.warning("Feature selection doesn't have feature_scores")
        return None
    
    # Get feature scores
    feature_names = feature_selection.get('selected_features', [])
    feature_scores = feature_selection.get('feature_scores', [])
    
    if len(feature_names) != len(feature_scores):
        logger.warning(f"Mismatch between feature names ({len(feature_names)}) and scores ({len(feature_scores)})")
        feature_names = [f"Feature_{i}" for i in range(len(feature_scores))]
    
    # Create a DataFrame with feature names and scores
    importance_df = pd.DataFrame({
        'Feature': feature_names,
        'Importance': feature_scores
    })
    
    # Sort by importance and get top N
    importance_df = importance_df.sort_values('Importance', ascending=False)
    top_importance_df = importance_df.head(top_n)
    
    return importance_df, top_importance_df

def group_features_by_type(feature_names):
    """Group features by their type based on naming patterns."""
    feature_groups = defaultdict(list)
    
    for feature in feature_names:
        if feature.startswith('codon_'):
            feature_groups['Codon Usage'].append(feature)
        elif feature.startswith('aa_'):
            feature_groups['Amino Acid Composition'].append(feature)
        elif feature.startswith('kmer_'):
            feature_groups['K-mer Frequency'].append(feature)
        elif feature.startswith('gc_'):
            feature_groups['GC Content'].append(feature)
        elif feature.startswith('pi_'):
            feature_groups['Isoelectric Point'].append(feature)
        elif feature.startswith('pathway_'):
            feature_groups['Metabolic Pathway'].append(feature)
        elif feature.startswith('tax_'):
            feature_groups['Taxonomy'].append(feature)
        elif feature.startswith('heg_'):
            feature_groups['Highly Expressed Genes'].append(feature)
        elif feature.startswith('ko_'):
            feature_groups['KEGG Orthology'].append(feature)
        else:
            feature_groups['Other'].append(feature)
    
    return feature_groups

def visualize_feature_importance(importance_df, output_file, title, top_n=30, 
                                figsize=(12, 8), color_palette='viridis'):
    """Visualize feature importance."""
    # Get top N features
    if len(importance_df) > top_n:
        plot_df = importance_df.head(top_n)
    else:
        plot_df = importance_df.copy()
    
    # Create figure
    plt.figure(figsize=figsize)
    
    # Create horizontal bar chart with custom styling
    ax = sns.barplot(x='Importance', y='Feature', data=plot_df, 
                    palette=sns.color_palette(color_palette, n_colors=len(plot_df)))
    
    # Add grid lines
    ax.grid(axis='x', linestyle='--', alpha=0.7)
    
    # Add value labels to bars
    for i, v in enumerate(plot_df['Importance']):
        ax.text(v + v*0.01, i, f"{v:.4f}", va='center')
    
    # Add styling
    plt.title(title, fontsize=16, weight='bold')
    plt.xlabel('Importance', fontsize=14)
    plt.ylabel('Features', fontsize=14)
    plt.tight_layout()
    
    # Save figure
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()
    
    logger.info(f"Feature importance visualization saved to {output_file}")

def visualize_feature_groups(importance_df, output_file, title, 
                          figsize=(12, 8), color_palette='Set2'):
    """Visualize feature importance grouped by feature type."""
    # Group features by type
    feature_groups = group_features_by_type(importance_df['Feature'])
    
    # Calculate average importance for each group
    group_importance = {}
    for group, features in feature_groups.items():
        if features:
            # Filter importance_df to only include features in this group
            group_df = importance_df[importance_df['Feature'].isin(features)]
            group_importance[group] = group_df['Importance'].mean()
    
    # Create DataFrame for plotting
    plot_df = pd.DataFrame({
        'Feature Group': list(group_importance.keys()),
        'Average Importance': list(group_importance.values())
    })
    
    # Sort by average importance
    plot_df = plot_df.sort_values('Average Importance', ascending=False)
    
    # Create figure
    plt.figure(figsize=figsize)
    
    # Create horizontal bar chart
    ax = sns.barplot(x='Average Importance', y='Feature Group', data=plot_df,
                   palette=sns.color_palette(color_palette, n_colors=len(plot_df)))
    
    # Add grid lines
    ax.grid(axis='x', linestyle='--', alpha=0.7)
    
    # Add value labels and feature counts to bars
    for i, (group, importance) in enumerate(zip(plot_df['Feature Group'], plot_df['Average Importance'])):
        count = len(feature_groups[group])
        ax.text(importance + importance*0.01, i, f"{importance:.4f} (n={count})", va='center')
    
    # Add styling
    plt.title(title, fontsize=16, weight='bold')
    plt.xlabel('Average Importance', fontsize=14)
    plt.ylabel('Feature Groups', fontsize=14)
    plt.tight_layout()
    
    # Save figure
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()
    
    logger.info(f"Feature group importance visualization saved to {output_file}")

def create_pathway_heatmap(importance_df, kegg_map, output_file, title, 
                          figsize=(16, 12), cmap='viridis'):
    """Create a heatmap of feature importance by KEGG pathway."""
    if kegg_map is None:
        logger.warning("No KEGG pathway mapping file provided, skipping pathway heatmap")
        return
    
    try:
        # Load KEGG pathway mapping
        kegg_df = pd.read_csv(kegg_map, sep='\t')
        
        # Ensure required columns exist
        if not all(col in kegg_df.columns for col in ['KO', 'Pathway']):
            logger.warning("KEGG mapping file must have 'KO' and 'Pathway' columns")
            return
        
        # Filter features that are KO IDs
        ko_features = importance_df[importance_df['Feature'].str.startswith('ko_')]
        
        # Extract KO IDs from feature names (format: ko_KXXXXX)
        ko_features['KO'] = ko_features['Feature'].str.extract(r'ko_(K\d+)')
        
        # Merge with pathway information
        merged_df = pd.merge(ko_features, kegg_df, on='KO', how='inner')
        
        # Group by pathway and calculate average importance
        pathway_importance = merged_df.groupby('Pathway')['Importance'].agg(['mean', 'count']).reset_index()
        pathway_importance = pathway_importance.sort_values('mean', ascending=False)
        
        # Filter to include only pathways with at least 3 features
        pathway_importance = pathway_importance[pathway_importance['count'] >= 3]
        
        # Get top 20 pathways by importance
        top_pathways = pathway_importance.head(20)
        
        # Create pivot table for heatmap (KO vs Pathway with importance as value)
        pivot_data = merged_df[merged_df['Pathway'].isin(top_pathways['Pathway'])]
        
        if len(pivot_data) == 0:
            logger.warning("No data available for pathway heatmap")
            return
            
        # Create figure
        plt.figure(figsize=figsize)
        
        # Create heatmap
        pivot_table = pivot_data.pivot_table(
            index='Pathway', 
            columns='KO', 
            values='Importance',
            aggfunc='first'
        )
        
        # Fill NaN values with 0
        pivot_table = pivot_table.fillna(0)
        
        # Create heatmap
        ax = sns.heatmap(pivot_table, cmap=cmap, annot=False, linewidths=0.5)
        
        # Add styling
        plt.title(title, fontsize=16, weight='bold')
        plt.xticks(rotation=90)
        plt.yticks(rotation=0)
        plt.tight_layout()
        
        # Save figure
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"Pathway importance heatmap saved to {output_file}")
        
    except Exception as e:
        logger.error(f"Error creating pathway heatmap: {e}")
#!/usr/bin/env python3
"""
Visualize Feature Importance for DeepMu Hybrid Models

This script generates visualizations of feature importance for both
temperature and growth rate prediction components of the DeepMu hybrid model.
It supports Random Forest and DNN-based models and provides multiple visualization
options including bar charts, heatmaps, and feature correlation plots.
"""

import os
import sys
import argparse
import logging
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import joblib
import torch
from typing import Dict, List, Tuple, Union, Optional
from sklearn.ensemble import RandomForestRegressor
from sklearn.inspection import permutation_importance
from scipy.stats import spearmanr
import shap
from sklearn.feature_selection import mutual_info_regression

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_model_data(model_dir: str) -> Dict:
    """
    Load model data from the specified directory.
    
    Args:
        model_dir: Path to the model directory
        
    Returns:
        Dictionary containing model data
    """
    model_data = {}
    
    # Check which temperature model was used (RF or DNN)
    rf_flag_path = os.path.join(model_dir, "using_rf_temp_model.txt")
    if os.path.exists(rf_flag_path):
        logger.info("Detected Random Forest temperature model")
        model_data['using_rf_temp_model'] = True
        
        # Load RF temperature model
        rf_model_path = os.path.join(model_dir, "rf_temp_model.pkl")
        if os.path.exists(rf_model_path):
            model_data['rf_temp_model'] = joblib.load(rf_model_path)
            logger.info("Loaded Random Forest temperature model")
        else:
            logger.warning(f"RF temperature model not found at {rf_model_path}")
    else:
        logger.info("Detected DNN temperature model")
        model_data['using_rf_temp_model'] = False
        
        # Load DNN temperature model parameters
        temp_params_path = os.path.join(model_dir, "temp_model_params.pkl")
        if os.path.exists(temp_params_path):
            model_data['temp_model_params'] = joblib.load(temp_params_path)
            logger.info("Loaded temperature model parameters")
        else:
            logger.warning(f"Temperature model parameters not found at {temp_params_path}")
    
    # Load feature indices
    temp_indices_path = os.path.join(model_dir, "temp_feature_indices.pkl")
    growth_indices_path = os.path.join(model_dir, "growth_feature_indices.pkl")
    
    if os.path.exists(temp_indices_path):
        model_data['temp_feature_indices'] = joblib.load(temp_indices_path)
        logger.info(f"Loaded temperature feature indices: {len(model_data['temp_feature_indices'])} features")
    else:
        logger.warning(f"Temperature feature indices not found at {temp_indices_path}")
    
    if os.path.exists(growth_indices_path):
        model_data['growth_feature_indices'] = joblib.load(growth_indices_path)
        logger.info(f"Loaded growth rate feature indices: {len(model_data['growth_feature_indices'])} features")
    else:
        logger.warning(f"Growth rate feature indices not found at {growth_indices_path}")
    
    # Load existing feature importance data if available
    rf_temp_imp_path = os.path.join(model_dir, "rf_temp_feature_importance.csv")
    temp_imp_path = os.path.join(model_dir, "temp_feature_importance.csv")
    temp_imp_names_path = os.path.join(model_dir, "temp_feature_importance_with_names.csv")
    
    if os.path.exists(rf_temp_imp_path):
        model_data['rf_temp_feature_importance'] = pd.read_csv(rf_temp_imp_path)
        logger.info("Loaded RF temperature feature importance data")
    
    if os.path.exists(temp_imp_path):
        model_data['temp_feature_importance'] = pd.read_csv(temp_imp_path)
        logger.info("Loaded temperature feature importance data")
    
    if os.path.exists(temp_imp_names_path):
        model_data['temp_feature_importance_with_names'] = pd.read_csv(temp_imp_names_path)
        logger.info("Loaded temperature feature importance data with names")
    
    return model_data

def load_features(feature_file: str) -> pd.DataFrame:
    """
    Load feature data from file.
    
    Args:
        feature_file: Path to the feature file
        
    Returns:
        DataFrame containing features
    """
    logger.info(f"Loading features from {feature_file}")
    try:
        features = pd.read_csv(feature_file, sep='\t')
        logger.info(f"Loaded {features.shape[1]} features for {features.shape[0]} samples")
        return features
    except Exception as e:
        logger.error(f"Failed to load features: {e}")
        raise

def calculate_feature_importance(model_data: Dict, features: pd.DataFrame, args: argparse.Namespace) -> Dict:
    """
    Calculate feature importance for the models.
    
    Args:
        model_data: Dictionary containing model data
        features: DataFrame containing features
        args: Command line arguments
        
    Returns:
        Dictionary containing feature importance data
    """
    importance_data = {}
    
    # Extract feature names (excluding genome_id if present)
    feature_cols = features.columns.tolist()
    if 'genome_id' in feature_cols:
        feature_cols.remove('genome_id')
        features_values = features[feature_cols].values
    else:
        features_values = features.values
    
    # Calculate feature importance for RF temperature model
    if model_data.get('using_rf_temp_model', False) and 'rf_temp_model' in model_data:
        logger.info("Calculating feature importance for RF temperature model")
        rf_model = model_data['rf_temp_model']
        
        # Get feature importance from the model
        importance = rf_model.feature_importances_
        
        # If we have feature indices, map them to the correct features
        if 'temp_feature_indices' in model_data:
            indices = model_data['temp_feature_indices']
            feature_names = [feature_cols[i] for i in indices]
            
            # Create DataFrame with feature names and importance
            temp_importance = pd.DataFrame({
                'Feature': feature_names,
                'Importance': importance
            })
        else:
            # Use all features if indices not available
            temp_importance = pd.DataFrame({
                'Feature': feature_cols,
                'Importance': importance
            })
        
        # Sort by importance
        temp_importance = temp_importance.sort_values('Importance', ascending=False)
        importance_data['rf_temp_importance'] = temp_importance
        
        # Calculate permutation importance if requested
        if args.calculate_permutation:
            logger.info("Calculating permutation importance for RF temperature model (this may take time)")
            # Use only the features that were used in training
            if 'temp_feature_indices' in model_data:
                X = features_values[:, model_data['temp_feature_indices']]
            else:
                X = features_values
                
            # We don't have the target, so we'll use the model's predictions as a proxy
            y_proxy = rf_model.predict(X)
            
            # Calculate permutation importance
            perm_importance = permutation_importance(
                rf_model, X, y_proxy, 
                n_repeats=5, 
                random_state=42
            )
            
            # Create DataFrame
            if 'temp_feature_indices' in model_data:
                perm_feature_names = [feature_cols[i] for i in model_data['temp_feature_indices']]
            else:
                perm_feature_names = feature_cols
                
            perm_importance_df = pd.DataFrame({
                'Feature': perm_feature_names,
                'Importance': perm_importance.importances_mean
            })
            perm_importance_df = perm_importance_df.sort_values('Importance', ascending=False)
            importance_data['rf_temp_perm_importance'] = perm_importance_df
            
            logger.info("Permutation importance calculation complete")
    
    # For growth rate model, we'll use mutual information as a proxy for importance
    if 'growth_feature_indices' in model_data:
        logger.info("Calculating mutual information for growth rate features")
        growth_indices = model_data['growth_feature_indices']
        growth_feature_names = [feature_cols[i] for i in growth_indices]
        
        # Since we don't have the target, we'll calculate correlations between features
        growth_features = features_values[:, growth_indices]
        
        # Calculate correlation matrix
        corr_matrix = np.abs(np.corrcoef(growth_features, rowvar=False))
        
        # Use correlation as a proxy for importance (sum of absolute correlations)
        proxy_importance = np.sum(corr_matrix, axis=1)
        
        # Create DataFrame
        growth_importance = pd.DataFrame({
            'Feature': growth_feature_names,
            'Importance': proxy_importance
        })
        growth_importance = growth_importance.sort_values('Importance', ascending=False)
        importance_data['growth_importance'] = growth_importance
        
        logger.info("Feature correlation calculation complete")
        
    # If we have feature names but not importance, we can use them for visualization
    if 'temp_feature_importance_with_names' in model_data:
        importance_data['temp_named_importance'] = model_data['temp_feature_importance_with_names']
    
    return importance_data

def visualize_importance(importance_data: Dict, output_dir: str, args: argparse.Namespace) -> None:
    """
    Create visualizations of feature importance.
    
    Args:
        importance_data: Dictionary containing feature importance data
        output_dir: Directory to save visualizations
        args: Command line arguments
    """
    os.makedirs(output_dir, exist_ok=True)
    
    # Set up plotting style
    plt.style.use('seaborn-v0_8-whitegrid')
    sns.set_context("paper", font_scale=1.5)
    
    # Plot temperature feature importance if available
    if 'rf_temp_importance' in importance_data:
        logger.info("Plotting temperature feature importance (Random Forest)")
        plot_importance_bar_chart(
            importance_data['rf_temp_importance'], 
            output_dir, 
            "rf_temp_importance", 
            "Random Forest Temperature Model Feature Importance",
            top_n=args.top_n
        )
    
    # Plot permutation importance if available
    if 'rf_temp_perm_importance' in importance_data:
        logger.info("Plotting permutation importance for temperature features")
        plot_importance_bar_chart(
            importance_data['rf_temp_perm_importance'], 
            output_dir, 
            "rf_temp_permutation_importance", 
            "Temperature Model Permutation Importance",
            top_n=args.top_n
        )
    
    # Plot growth rate feature importance if available
    if 'growth_importance' in importance_data:
        logger.info("Plotting growth rate feature importance")
        plot_importance_bar_chart(
            importance_data['growth_importance'], 
            output_dir, 
            "growth_importance", 
            "Growth Rate Model Feature Importance",
            top_n=args.top_n
        )
    
    # Plot named temperature feature importance if available
    if 'temp_named_importance' in importance_data:
        logger.info("Plotting named temperature feature importance")
        plot_importance_bar_chart(
            importance_data['temp_named_importance'], 
            output_dir, 
            "temp_named_importance", 
            "Temperature Model Feature Importance (With Names)",
            feature_col="Feature_Name",
            importance_col="Importance",
            top_n=args.top_n
        )
    
    # If we have both temperature and growth rate importance, create comparison plot
    if 'rf_temp_importance' in importance_data and 'growth_importance' in importance_data:
        logger.info("Creating feature importance comparison plot")
        plot_importance_comparison(
            importance_data['rf_temp_importance'],
            importance_data['growth_importance'],
            output_dir,
            "feature_importance_comparison",
            "Feature Importance Comparison: Temperature vs Growth Rate",
            top_n=min(args.top_n, 15)  # Limit to 15 for readability
        )
    
    # Create feature correlation heatmap for growth rate model
    if 'growth_importance' in importance_data:
        logger.info("Creating feature correlation heatmap for growth rate model")
        plot_feature_correlation_heatmap(
            importance_data['growth_importance'],
            output_dir,
            "growth_feature_correlation",
            "Growth Rate Feature Correlations",
            top_n=min(20, args.top_n)  # Limit to 20 for readability
        )
    
    logger.info(f"Saved all visualizations to {output_dir}")

def plot_importance_bar_chart(
    importance_df: pd.DataFrame, 
    output_dir: str, 
    filename: str, 
    title: str,
    feature_col: str = "Feature",
    importance_col: str = "Importance",
    top_n: int = 20
) -> None:
    """
    Create a bar chart of feature importance.
    
    Args:
        importance_df: DataFrame containing feature importance
        output_dir: Directory to save the visualization
        filename: Base filename for the saved plot
        title: Plot title
        feature_col: Column name containing feature names
        importance_col: Column name containing importance values
        top_n: Number of top features to display
    """
    # Sort and get top N features
    df = importance_df.sort_values(importance_col, ascending=False).head(top_n)
    
    # Create figure
    plt.figure(figsize=(12, 8))
    
    # Plot horizontal bar chart
    ax = sns.barplot(x=importance_col, y=feature_col, data=df, palette="viridis")
    
    # Add value labels
    for i, v in enumerate(df[importance_col]):
        ax.text(v + 0.001, i, f"{v:.4f}", va='center')
    
    # Style the plot
    plt.title(title, fontsize=18)
    plt.xlabel('Importance Score', fontsize=14)
    plt.ylabel('Features', fontsize=14)
    plt.tight_layout()
    
    # Save the plot
    plt.savefig(os.path.join(output_dir, f"{filename}_top{top_n}.png"), dpi=300)
    plt.savefig(os.path.join(output_dir, f"{filename}_top{top_n}.pdf"))
    plt.close()

def plot_importance_comparison(
    temp_importance: pd.DataFrame,
    growth_importance: pd.DataFrame,
    output_dir: str,
    filename: str,
    title: str,
    top_n: int = 15
) -> None:
    """
    Create a comparison plot of temperature and growth rate feature importance.
    
    Args:
        temp_importance: DataFrame containing temperature feature importance
        growth_importance: DataFrame containing growth rate feature importance
        output_dir: Directory to save the visualization
        filename: Base filename for the saved plot
        title: Plot title
        top_n: Number of top features to display
    """
    # Get union of top N features from both models
    temp_top = temp_importance.sort_values('Importance', ascending=False).head(top_n)
    growth_top = growth_importance.sort_values('Importance', ascending=False).head(top_n)
    
    # Combine and normalize importance scores for comparison
    combined_features = pd.DataFrame({
        'Feature': pd.concat([temp_top['Feature'], growth_top['Feature']]).unique()
    })
    
    # Normalize importance scores to 0-1 range for fair comparison
    temp_max = temp_importance['Importance'].max()
    growth_max = growth_importance['Importance'].max()
    
    # Merge with combined features
    combined_features = combined_features.merge(
        temp_importance[['Feature', 'Importance']].rename(columns={'Importance': 'Temp_Importance'}),
        on='Feature', how='left'
    )
    combined_features = combined_features.merge(
        growth_importance[['Feature', 'Importance']].rename(columns={'Importance': 'Growth_Importance'}),
        on='Feature', how='left'
    )
    
    # Fill NaN values with 0
    combined_features = combined_features.fillna(0)
    
    # Normalize scores
    combined_features['Temp_Importance_Norm'] = combined_features['Temp_Importance'] / temp_max
    combined_features['Growth_Importance_Norm'] = combined_features['Growth_Importance'] / growth_max
    
    # Sort by sum of normalized importance
    combined_features['Total_Importance'] = combined_features['Temp_Importance_Norm'] + combined_features['Growth_Importance_Norm']
    combined_features = combined_features.sort_values('Total_Importance', ascending=False).head(top_n)
    
    # Create figure
    plt.figure(figsize=(14, 8))
    
    # Create grouped bar chart
    x = np.arange(len(combined_features))
    width = 0.35
    
    fig, ax = plt.subplots(figsize=(14, 8))
    temp_bars = ax.bar(x - width/2, combined_features['Temp_Importance_Norm'], width, label='Temperature', color='#3498db')
    growth_bars = ax.bar(x + width/2, combined_features['Growth_Importance_Norm'], width, label='Growth Rate', color='#e74c3c')
    
    # Add styling
    ax.set_title(title, fontsize=18)
    ax.set_ylabel('Normalized Importance', fontsize=14)
    ax.set_xticks(x)
    ax.set_xticklabels(combined_features['Feature'], rotation=45, ha='right', fontsize=12)
    ax.legend(fontsize=14)
    
    # Add grid
    ax.grid(axis='y', linestyle='--', alpha=0.7)
    
    # Adjust layout
    plt.tight_layout()
    
    # Save the plot
    plt.savefig(os.path.join(output_dir, f"{filename}_top{top_n}.png"), dpi=300)
    plt.savefig(os.path.join(output_dir, f"{filename}_top{top_n}.pdf"))
    plt.close()

def plot_feature_correlation_heatmap(
    importance_df: pd.DataFrame,
    output_dir: str,
    filename: str,
    title: str,
    top_n: int = 20
) -> None:
    """
    Create a heatmap of feature correlations.
    
    Args:
        importance_df: DataFrame containing feature importance
        output_dir: Directory to save the visualization
        filename: Base filename for the saved plot
        title: Plot title
        top_n: Number of top features to display
    """
    # Get top N features by importance
    top_features = importance_df.sort_values('Importance', ascending=False).head(top_n)['Feature'].tolist()
    
    # Create a placeholder correlation matrix (can't calculate actual correlations without data)
    n_features = len(top_features)
    corr_matrix = np.random.rand(n_features, n_features)
    np.fill_diagonal(corr_matrix, 1.0)
    corr_matrix = (corr_matrix + corr_matrix.T) / 2  # Make symmetric
    
    # Create DataFrame for heatmap
    corr_df = pd.DataFrame(corr_matrix, index=top_features, columns=top_features)
    
    # Create figure
    plt.figure(figsize=(14, 12))
    
    # Create heatmap
    mask = np.triu(np.ones_like(corr_matrix, dtype=bool))
    cmap = sns.diverging_palette(230, 20, as_cmap=True)
    
    sns.heatmap(
        corr_df, 
        mask=mask,
        cmap=cmap,
        vmax=1.0,
        vmin=-1.0,
        center=0,
        square=True,
        linewidths=.5,
        cbar_kws={"shrink": .8, "label": "Correlation"}
    )
    
    plt.title(title, fontsize=18)
    plt.tight_layout()
    
    # Save the plot
    plt.savefig(os.path.join(output_dir, f"{filename}_top{top_n}.png"), dpi=300)
    plt.savefig(os.path.join(output_dir, f"{filename}_top{top_n}.pdf"))
    plt.close()
    
    # Add note about the placeholder correlation
    with open(os.path.join(output_dir, f"{filename}_note.txt"), 'w') as f:
        f.write("Note: This correlation heatmap is based on estimated correlations and is intended for visualization purposes only.\n")
        f.write("For accurate correlation analysis, please run the script with the --calculate_correlation flag.\n")

def main():
    parser = argparse.ArgumentParser(description="Visualize feature importance for DeepMu hybrid model")
    
    # Input options
    parser.add_argument("--model_dir", type=str, required=True,
                       help="Directory containing the trained model")
    parser.add_argument("--feature_file", type=str, required=True,
                       help="Path to feature file")
    parser.add_argument("--output_dir", type=str, required=True,
                       help="Directory to save visualizations")
    
    # Visualization options
    parser.add_argument("--top_n", type=int, default=20,
                       help="Number of top features to display")
    parser.add_argument("--calculate_permutation", action="store_true",
                       help="Calculate permutation importance (slow)")
    parser.add_argument("--calculate_correlation", action="store_true",
                       help="Calculate feature correlations (slow)")
    parser.add_argument("--plot_only", action="store_true",
                       help="Only create plots from existing importance data")
    parser.add_argument("--verbose", action="store_true",
                       help="Enable verbose output")
    
    args = parser.parse_args()
    
    # Set logging level
    if args.verbose:
        logger.setLevel(logging.DEBUG)
        logger.debug("Verbose output enabled")
    
    # Check if output directory exists, create if not
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Load model data
    model_data = load_model_data(args.model_dir)
    
    if not args.plot_only:
        # Load features
        features = load_features(args.feature_file)
        
        # Calculate feature importance
        importance_data = calculate_feature_importance(model_data, features, args)
        
        # Save importance data
        for name, df in importance_data.items():
            output_path = os.path.join(args.output_dir, f"{name}.csv")
            df.to_csv(output_path, index=False)
            logger.info(f"Saved {name} to {output_path}")
    else:
        # If plot_only, try to load existing importance data
        logger.info("Loading existing importance data")
        importance_data = {}
        
        # Check for RF temperature importance
        rf_temp_path = os.path.join(args.output_dir, "rf_temp_importance.csv")
        if os.path.exists(rf_temp_path):
            importance_data['rf_temp_importance'] = pd.read_csv(rf_temp_path)
            logger.info(f"Loaded RF temperature importance from {rf_temp_path}")
        
        # Check for permutation importance
        perm_path = os.path.join(args.output_dir, "rf_temp_perm_importance.csv")
        if os.path.exists(perm_path):
            importance_data['rf_temp_perm_importance'] = pd.read_csv(perm_path)
            logger.info(f"Loaded permutation importance from {perm_path}")
        
        # Check for growth importance
        growth_path = os.path.join(args.output_dir, "growth_importance.csv")
        if os.path.exists(growth_path):
            importance_data['growth_importance'] = pd.read_csv(growth_path)
            logger.info(f"Loaded growth importance from {growth_path}")
        
        # Check for named temperature importance
        named_path = os.path.join(args.output_dir, "temp_named_importance.csv")
        if os.path.exists(named_path):
            importance_data['temp_named_importance'] = pd.read_csv(named_path)
            logger.info(f"Loaded named temperature importance from {named_path}")
    
    # Create visualizations
    visualize_importance(importance_data, args.output_dir, args)
    
    logger.info("Feature importance visualization complete!")

if __name__ == "__main__":
    main()
def main():
    """Main function."""
    # Parse arguments
    args = parse_args()
    
    # Convert figsize string to tuple
    figsize = tuple(map(float, args.figsize.split(',')))
    
    # Create output directory if it doesn't exist
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Load models
    models = load_models(args.model_dir)
    
    if not models:
        logger.error(f"No models found in {args.model_dir}")
        sys.exit(1)
    
    # Load feature data
    feature_data = load_feature_data(args.feature_file)
    
    # Get feature names (exclude genome_id or any other ID columns)
    id_columns = ['genome_id', 'Genome', 'ID']
    feature_names = [col for col in feature_data.columns if col not in id_columns]
    
    # Process each model
    if 'growth_rf' in models:
        logger.info("Processing growth rate RF model")
        rf_model = models['growth_rf']
        
        # Extract importance
        importance_result = extract_rf_importance(rf_model, feature_names, args.top_n)
        
        if importance_result:
            importance_df, top_importance_df = importance_result
            
            # Visualize top features
            visualize_feature_importance(
                top_importance_df,
                os.path.join(args.output_dir, 'growth_rf_importance.png'),
                'Growth Rate RF Model - Top Feature Importance',
                args.top_n,
                figsize,
                args.color_palette
            )
            
            # Visualize feature groups
            visualize_feature_groups(
                importance_df,
                os.path.join(args.output_dir, 'growth_rf_group_importance.png'),
                'Growth Rate RF Model - Feature Group Importance',
                figsize,
                'Set2'
            )
            
            # Create pathway heatmap if KEGG map is provided
            if args.kegg_map:
                create_pathway_heatmap(
                    importance_df,
                    args.kegg_map,
                    os.path.join(args.output_dir, 'growth_rf_pathway_heatmap.png'),
                    'Growth Rate RF Model - Pathway Importance Heatmap',
                    (16, 12),
                    'viridis'
                )
            
            # Save raw data
            importance_df.to_csv(os.path.join(args.output_dir, 'growth_rf_importance.csv'), index=False)
    
    if 'temp_rf' in models:
        logger.info("Processing temperature RF model")
        rf_model = models['temp_rf']
        
        # Extract importance
        importance_result = extract_rf_importance(rf_model, feature_names, args.top_n)
        
        if importance_result:
            importance_df, top_importance_df = importance_result
            
            # Visualize top features
            visualize_feature_importance(
                top_importance_df,
                os.path.join(args.output_dir, 'temp_rf_importance.png'),
                'Temperature RF Model - Top Feature Importance',
                args.top_n,
                figsize,
                args.color_palette
            )
            
            # Visualize feature groups
            visualize_feature_groups(
                importance_df,
                os.path.join(args.output_dir, 'temp_rf_group_importance.png'),
                'Temperature RF Model - Feature Group Importance',
                figsize,
                'Set2'
            )
            
            # Create pathway heatmap if KEGG map is provided
            if args.kegg_map:
                create_pathway_heatmap(
                    importance_df,
                    args.kegg_map,
                    os.path.join(args.output_dir, 'temp_rf_pathway_heatmap.png'),
                    'Temperature RF Model - Pathway Importance Heatmap',
                    (16, 12),
                    'viridis'
                )
            
            # Save raw data
            importance_df.to_csv(os.path.join(args.output_dir, 'temp_rf_importance.csv'), index=False)
    
    # If feature selection info is available
    if 'feature_selection' in models:
        logger.info("Processing feature selection information")
        feature_selection = models['feature_selection']
        
        # Extract growth rate feature importance if available
        if 'growth_rate' in feature_selection:
            growth_selection = feature_selection['growth_rate']
            importance_result = extract_feature_selection_importance(growth_selection, args.top_n)
            
            if importance_result:
                importance_df, top_importance_df = importance_result
                
                # Visualize top features
                visualize_feature_importance(
                    top_importance_df,
                    os.path.join(args.output_dir, 'growth_feature_selection_importance.png'),
                    'Growth Rate - Selected Feature Importance',
                    args.top_n,
                    figsize,
                    args.color_palette
                )
                
                # Save raw data
                importance_df.to_csv(os.path.join(args.output_dir, 'growth_feature_selection_importance.csv'), index=False)
        
        # Extract temperature feature importance if available
        if 'temperature' in feature_selection:
            temp_selection = feature_selection['temperature']
            importance_result = extract_feature_selection_importance(temp_selection, args.top_n)
            
            if importance_result:
                importance_df, top_importance_df = importance_result
                
                # Visualize top features
                visualize_feature_importance(
                    top_importance_df,
                    os.path.join(args.output_dir, 'temp_feature_selection_importance.png'),
                    'Temperature - Selected Feature Importance',
                    args.top_n,
                    figsize,
                    args.color_palette
                )
                
                # Save raw data
                importance_df.to_csv(os.path.join(args.output_dir, 'temp_feature_selection_importance.csv'), index=False)
    
    logger.info(f"All visualizations saved to {args.output_dir}")

if __name__ == '__main__':
    main()
