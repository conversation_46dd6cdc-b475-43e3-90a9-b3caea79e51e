#!/usr/bin/env python3
"""
Training script for an advanced RF hybrid model with Bayesian optimization, model stacking,
advanced feature engineering, SHAP feature importance, improved cross-validation,
ensemble techniques, and optimized feature selection.

This script builds upon the successful RF hybrid model with RFE but adds several
advanced techniques to further improve prediction accuracy.
"""

import os
import sys
import argparse
import logging
import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union, Any
from sklearn.model_selection import train_test_split, cross_val_score, KFold, StratifiedKFold, GroupKFold
from sklearn.ensemble import RandomForestRegressor, StackingRegressor, VotingRegressor
from sklearn.preprocessing import StandardScaler, RobustScaler, QuantileTransformer
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.feature_selection import RFECV, RFE, SelectFromModel, mutual_info_regression
from sklearn.inspection import permutation_importance
from joblib import dump, load, Parallel, delayed
import itertools
import optuna
from optuna.samplers import TPESampler
import xgboost as xgb
import lightgbm as lgb
import shap
from skopt import BayesSearchCV
from skopt.space import Real, Integer, Categorical

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('train_rf_hybrid_advanced')

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Train an advanced RF hybrid model with multiple enhancements')

    # Data arguments
    parser.add_argument('--metadata', required=True, help='Path to metadata TSV file')
    parser.add_argument('--feature-file', required=True, help='Path to combined features TSV file')
    parser.add_argument('--output-dir', required=True, help='Directory to save model and results')
    parser.add_argument('--metrics-dir', default=None, help='Directory to save metrics')

    # Optimization arguments
    parser.add_argument('--bayesian-iterations', type=int, default=50, help='Number of iterations for Bayesian optimization')
    parser.add_argument('--max-estimators', type=int, default=1000, help='Maximum number of estimators to consider')

    # Feature selection arguments
    parser.add_argument('--growth-rate-features', type=int, default=100, help='Number of features to select for growth rate prediction')
    parser.add_argument('--optimal-temperature-features', type=int, default=80, help='Number of features to select for optimal temperature prediction')
    parser.add_argument('--interaction-depth', type=int, default=3, help='Maximum depth for feature interactions (2 for pairwise, 3 for triplets, etc.)')
    parser.add_argument('--max-interactions', type=int, default=150, help='Maximum number of interaction features to create')

    # Training arguments
    parser.add_argument('--seed', type=int, default=42, help='Random seed')
    parser.add_argument('--target', type=str, choices=['growth_rate', 'temperature', 'both'], default='both',
                        help='Target variable to predict')
    parser.add_argument('--cv-folds', type=int, default=5, help='Number of cross-validation folds')

    # Feature flags
    parser.add_argument('--use-stacking', action='store_true', help='Use model stacking (RF, XGBoost, LightGBM)')
    parser.add_argument('--use-shap', action='store_true', help='Use SHAP values for feature importance')
    parser.add_argument('--use-feature-selection', action='store_true', help='Use optimized feature selection')
    parser.add_argument('--use-advanced-interactions', action='store_true', help='Use advanced feature interactions')

    return parser.parse_args()

# Feature engineering functions
def create_taxonomy_features(metadata_df):
    """
    Create enhanced taxonomy features from metadata.

    Args:
        metadata_df (pd.DataFrame): DataFrame with metadata

    Returns:
        pd.DataFrame: DataFrame with taxonomy features
    """
    # Create taxonomy features
    taxonomy_data = []

    for _, row in metadata_df.iterrows():
        sample_id = row['genome_id']
        taxid = int(row['taxid'])
        kingdom = row['kingdom'] if 'kingdom' in row else 'unknown'

        # Create features based on taxid
        features = {
            'genome_id': sample_id,
            'taxid_mod_10': taxid % 10,
            'taxid_mod_100': taxid % 100,
            'taxid_mod_1000': taxid % 1000,
            'taxid_div_10': taxid // 10,
            'taxid_div_100': taxid // 100,
            'taxid_div_1000': taxid // 1000
        }

        # Add kingdom encoding
        if kingdom in ['bacteria', 'archaea']:
            kingdom_map = {'bacteria': 0, 'archaea': 1}
            features['kingdom_encoded'] = kingdom_map.get(kingdom, -1)

        taxonomy_data.append(features)

    # Create DataFrame
    taxonomy_df = pd.DataFrame(taxonomy_data)

    return taxonomy_df

def create_enhanced_breakpoint_features(features_df):
    """
    Create enhanced breakpoint features with more sophisticated transformations.

    Args:
        features_df (pd.DataFrame): DataFrame with features

    Returns:
        pd.DataFrame: DataFrame with enhanced breakpoint features
    """
    # Extract breakpoint-related features
    breakpoint_cols = ['genome_id'] + [col for col in features_df.columns if
                                     'skew' in col or
                                     '_change_points' in col or
                                     '_sign_changes' in col or
                                     '_mean' in col or
                                     '_std' in col or
                                     '_min' in col or
                                     '_max' in col or
                                     '_range' in col]

    # Create a new DataFrame with only breakpoint features
    breakpoint_df = features_df[breakpoint_cols].copy()

    # Add emphasized features
    emphasized_features = {}
    for col in breakpoint_df.columns:
        if col != 'genome_id' and ('_change_points' in col or '_sign_changes' in col):
            # Emphasize change points with square and cube transformations
            emphasized_features[f'important_{col}'] = breakpoint_df[col] * 2.0
            emphasized_features[f'{col}_squared'] = breakpoint_df[col] ** 2
            emphasized_features[f'{col}_cubed'] = breakpoint_df[col] ** 3
            logger.info(f"Created enhanced breakpoint features for: {col}")

    # Add all emphasized features at once to avoid fragmentation
    if emphasized_features:
        emphasized_df = pd.DataFrame(emphasized_features)
        breakpoint_df = pd.concat([breakpoint_df, emphasized_df], axis=1)

    # Create ratios between related features
    ratio_features = {}
    skew_cols = [col for col in breakpoint_df.columns if 'skew' in col and col != 'genome_id']
    change_point_cols = [col for col in breakpoint_df.columns if '_change_points' in col and col != 'genome_id']

    # Create ratios between skew and change points
    for skew_col in skew_cols[:5]:  # Limit to first 5 to avoid explosion
        for cp_col in change_point_cols[:5]:  # Limit to first 5
            # Avoid division by zero
            denominator = breakpoint_df[cp_col].replace(0, np.nan)
            if denominator.isna().sum() / len(denominator) < 0.5:  # Only create ratio if less than 50% NaN
                ratio_features[f'{skew_col}_to_{cp_col}'] = breakpoint_df[skew_col] / denominator
                ratio_features[f'{skew_col}_to_{cp_col}'].fillna(0, inplace=True)
                logger.info(f"Created ratio feature: {skew_col}_to_{cp_col}")

    # Add ratio features
    if ratio_features:
        ratio_df = pd.DataFrame(ratio_features)
        breakpoint_df = pd.concat([breakpoint_df, ratio_df], axis=1)

    return breakpoint_df

def enhance_codon_features(features_df):
    """
    Create advanced codon feature transformations and interactions.

    Args:
        features_df (pd.DataFrame): DataFrame with features

    Returns:
        pd.DataFrame: DataFrame with enhanced codon features
    """
    # Create a copy of the input DataFrame
    enhanced_df = features_df.copy()

    # Identify codon-related features
    codon_cols = [col for col in features_df.columns if any(pattern in col for pattern in
                ['CUB', 'CPB', 'CAI', 'ENC', 'HEG_', 'BG_', 'delta_', 'RSCU_',
                 'codon_bias_', 'AAAI', 'aa_bias_HEG_BP', 'MILC'])]

    logger.info(f"Found {len(codon_cols)} codon-related features")

    # Create polynomial features for important codon metrics
    poly_features = {}
    for col in codon_cols:
        if col != 'genome_id':
            # Square and cube transformations
            poly_features[f'{col}_squared'] = enhanced_df[col] ** 2
            poly_features[f'{col}_cubed'] = enhanced_df[col] ** 3

            # Log transformation for positive values
            if (enhanced_df[col] > 0).all():
                poly_features[f'{col}_log'] = np.log1p(enhanced_df[col])

            # Reciprocal for non-zero values
            if (enhanced_df[col] != 0).all():
                poly_features[f'{col}_reciprocal'] = 1.0 / enhanced_df[col]

    # Add all polynomial features at once
    if poly_features:
        poly_df = pd.DataFrame(poly_features)
        enhanced_df = pd.concat([enhanced_df, poly_df], axis=1)
        logger.info(f"Created {len(poly_features)} polynomial features")

    # Create interaction features between selected codon metrics
    important_codon_cols = [col for col in codon_cols if any(pattern in col for pattern in
                          ['CAI', 'ENC', 'delta_', 'codon_bias_', 'RSCU_', 'codon_divergence'])]

    # Limit to first 15 to avoid explosion
    important_codon_cols = important_codon_cols[:15] if len(important_codon_cols) > 15 else important_codon_cols

    interaction_features = {}
    for i, col1 in enumerate(important_codon_cols):
        for col2 in important_codon_cols[i+1:]:
            interaction_features[f'{col1}_x_{col2}'] = enhanced_df[col1] * enhanced_df[col2]

            # Add ratio interactions (if denominator is non-zero)
            if (enhanced_df[col2] != 0).all():
                interaction_features[f'{col1}_div_{col2}'] = enhanced_df[col1] / enhanced_df[col2]

            if (enhanced_df[col1] != 0).all():
                interaction_features[f'{col2}_div_{col1}'] = enhanced_df[col2] / enhanced_df[col1]

    # Add all interaction features at once
    if interaction_features:
        interaction_df = pd.DataFrame(interaction_features)
        enhanced_df = pd.concat([enhanced_df, interaction_df], axis=1)
        logger.info(f"Created {len(interaction_features)} codon interaction features")

    return enhanced_df

def create_advanced_interactions(X, top_features, interaction_depth=3, max_interactions=150):
    """
    Create advanced interaction features based on top features with multiple transformation types.

    Args:
        X (pd.DataFrame): Feature DataFrame
        top_features (List[str]): List of top feature names
        interaction_depth (int): Maximum depth for interactions (2 for pairwise, 3 for triplets, etc.)
        max_interactions (int): Maximum number of interaction features to create

    Returns:
        pd.DataFrame: DataFrame with interaction features
    """
    logger.info(f"Creating advanced interactions with depth {interaction_depth} for {len(top_features)} top features")

    # Limit number of top features to avoid combinatorial explosion
    max_top_features = 20
    if len(top_features) > max_top_features:
        logger.info(f"Limiting top features from {len(top_features)} to {max_top_features} for interactions")
        top_features = top_features[:max_top_features]

    interaction_features = {}
    count = 0

    # Generate all combinations of features up to the specified depth
    for depth in range(2, interaction_depth + 1):
        for combo in itertools.combinations(top_features, depth):
            if count >= max_interactions:
                break

            # Create feature name
            feature_name = "_x_".join(combo)

            # Create multiplication interaction
            interaction_value = X[combo[0]].copy()
            for feature in combo[1:]:
                interaction_value *= X[feature]

            interaction_features[feature_name] = interaction_value
            count += 1

            # For depth 2, also create additional transformations
            if depth == 2 and count < max_interactions:
                # Create sum interaction
                sum_name = "_plus_".join(combo)
                interaction_features[sum_name] = X[combo[0]] + X[combo[1]]
                count += 1

                # Create difference interaction
                diff_name = f"{combo[0]}_minus_{combo[1]}"
                interaction_features[diff_name] = X[combo[0]] - X[combo[1]]
                count += 1

                # Create ratio interaction if denominator is non-zero
                if (X[combo[1]] != 0).all():
                    ratio_name = f"{combo[0]}_div_{combo[1]}"
                    interaction_features[ratio_name] = X[combo[0]] / X[combo[1]]
                    count += 1

                # Create max interaction
                max_name = "_max_".join(combo)
                interaction_features[max_name] = np.maximum(X[combo[0]], X[combo[1]])
                count += 1

                # Create min interaction
                min_name = "_min_".join(combo)
                interaction_features[min_name] = np.minimum(X[combo[0]], X[combo[1]])
                count += 1

            logger.info(f"Created advanced interaction feature {count}/{max_interactions}: {feature_name}")

    # Create DataFrame with interaction features that preserves the index from X
    if interaction_features:
        return pd.DataFrame(interaction_features, index=X.index)
    else:
        return pd.DataFrame(index=X.index)

# Feature selection and optimization functions
def perform_optimized_feature_selection(X, y, n_features_to_select, random_state=42):
    """
    Perform optimized feature selection using multiple methods and ensemble the results.

    Args:
        X (np.ndarray): Feature matrix
        y (np.ndarray): Target vector
        n_features_to_select (int): Number of features to select
        random_state (int): Random seed

    Returns:
        np.ndarray: Indices of selected features
    """
    logger.info(f"Performing optimized feature selection to select {n_features_to_select} features from {X.shape[1]} total features")

    # Check for NaN values in X
    if np.isnan(X).any():
        logger.warning(f"Found {np.isnan(X).sum()} NaN values in feature matrix. Filling with zeros.")
        X = np.nan_to_num(X, nan=0.0)

    # Check for NaN values in y
    if np.isnan(y).any():
        logger.warning(f"Found {np.isnan(y).sum()} NaN values in target vector. Removing affected samples.")
        valid_mask = ~np.isnan(y)
        X = X[valid_mask]
        y = y[valid_mask]

    # Method 1: Random Forest feature importance
    rf = RandomForestRegressor(n_estimators=100, random_state=random_state, n_jobs=-1)
    rf.fit(X, y)
    rf_importances = rf.feature_importances_

    # Method 2: Mutual information
    try:
        mi_importances = mutual_info_regression(X, y, random_state=random_state)
    except Exception as e:
        logger.warning(f"Error calculating mutual information: {e}. Using random forest importance instead.")
        mi_importances = rf_importances

    # Method 3: XGBoost feature importance
    try:
        xgb_model = xgb.XGBRegressor(n_estimators=100, random_state=random_state, n_jobs=-1)
        xgb_model.fit(X, y)
        xgb_importances = xgb_model.feature_importances_
    except Exception as e:
        logger.warning(f"Error training XGBoost model: {e}. Using random forest importance instead.")
        xgb_importances = rf_importances

    # Method 4: LightGBM feature importance
    try:
        lgb_model = lgb.LGBMRegressor(n_estimators=100, random_state=random_state, n_jobs=-1)
        lgb_model.fit(X, y)
        lgb_importances = lgb_model.feature_importances_
    except Exception as e:
        logger.warning(f"Error training LightGBM model: {e}. Using random forest importance instead.")
        lgb_importances = rf_importances

    # Normalize importances to [0, 1] range
    def normalize(x):
        return (x - np.min(x)) / (np.max(x) - np.min(x)) if np.max(x) > np.min(x) else np.ones_like(x)

    rf_importances_norm = normalize(rf_importances)
    mi_importances_norm = normalize(mi_importances)
    xgb_importances_norm = normalize(xgb_importances)
    lgb_importances_norm = normalize(lgb_importances)

    # Ensemble the importances with different weights
    ensemble_importances = (
        0.3 * rf_importances_norm +
        0.2 * mi_importances_norm +
        0.25 * xgb_importances_norm +
        0.25 * lgb_importances_norm
    )

    # Select top features
    selected_indices = np.argsort(ensemble_importances)[::-1][:n_features_to_select]

    logger.info(f"Selected {len(selected_indices)} features using ensemble method")

    return selected_indices

def optimize_rf_hyperparameters(X_train, y_train, X_val, y_val, max_estimators=1000, n_trials=50, random_state=42):
    """
    Optimize Random Forest hyperparameters using Optuna.

    Args:
        X_train (np.ndarray or pd.DataFrame): Training feature matrix
        y_train (np.ndarray or pd.Series): Training target vector
        X_val (np.ndarray or pd.DataFrame): Validation feature matrix
        y_val (np.ndarray or pd.Series): Validation target vector
        max_estimators (int): Maximum number of estimators to consider
        n_trials (int): Number of optimization trials
        random_state (int): Random seed

    Returns:
        dict: Optimized hyperparameters
    """
    logger.info(f"Optimizing Random Forest hyperparameters with {n_trials} trials")

    # Convert to numpy arrays to avoid feature name issues
        # Keep as DataFrames to preserve feature names
    feature_names = X_train.columns.tolist()
    X_train_values = X_train
    y_train_values = y_train

    if isinstance(X_val, pd.DataFrame):
        X_val_values = X_val
    else:
        X_val_values = X_val

    if isinstance(y_val, pd.Series):
        y_val_values = y_val.values
    else:
        y_val_values = y_val

    def objective(trial):
        # Define hyperparameters to optimize
        params = {
            'n_estimators': trial.suggest_int('n_estimators', 100, max_estimators),
            'max_depth': trial.suggest_int('max_depth', 10, 100) if trial.suggest_categorical('use_max_depth', [True, False]) else None,
            'min_samples_split': trial.suggest_int('min_samples_split', 2, 20),
            'min_samples_leaf': trial.suggest_int('min_samples_leaf', 1, 10),
            'max_features': trial.suggest_categorical('max_features', ['sqrt', 'log2', None]),
            'bootstrap': trial.suggest_categorical('bootstrap', [True, False])
        }

        try:
            # Create and train model
            model = RandomForestRegressor(random_state=random_state, n_jobs=-1, **params)
            model.fit(X_train_values, y_train_values)

            # Evaluate on validation set
            y_pred = model.predict(pd.DataFrame(X_val_values, columns=feature_names))
            val_r2 = r2_score(y_val_values, y_pred)

            return val_r2  # Maximize R²
        except Exception as e:
            logger.warning(f"Trial failed with error: {e}")
            return -1.0  # Return a bad score for failed trials

    # Create study
    study = optuna.create_study(direction='maximize', sampler=TPESampler(seed=random_state))

    # Optimize
    try:
        study.optimize(objective, n_trials=n_trials)

        # Get best parameters
        best_params = study.best_params

        # Handle max_depth=None case
        if 'use_max_depth' in best_params and not best_params['use_max_depth']:
            best_params['max_depth'] = None

        # Remove helper parameter
        if 'use_max_depth' in best_params:
            del best_params['use_max_depth']

        logger.info(f"Best hyperparameters: {best_params}")
        logger.info(f"Best validation R²: {study.best_value:.4f}")

        return best_params
    except Exception as e:
        logger.error(f"Hyperparameter optimization failed: {e}")
        # Return default parameters
        default_params = {
            'n_estimators': 500,
            'max_depth': None,
            'min_samples_split': 2,
            'min_samples_leaf': 1,
            'max_features': 'sqrt',
            'bootstrap': True
        }
        logger.info(f"Using default hyperparameters: {default_params}")
        return default_params

def create_stacking_ensemble(X_train, y_train, rf_params, random_state=42):
    """
    Create a stacking ensemble of Random Forest, XGBoost, and LightGBM.

    Args:
        X_train (np.ndarray or pd.DataFrame): Training feature matrix
        y_train (np.ndarray or pd.Series): Training target vector
        rf_params (dict): Random Forest hyperparameters
        random_state (int): Random seed

    Returns:
        StackingRegressor: Trained stacking ensemble
    """
    logger.info("Creating stacking ensemble of RF, XGBoost, and LightGBM")

    # Convert to numpy arrays to avoid feature name issues
        # Keep as DataFrames to preserve feature names
    feature_names = X_train.columns.tolist()
    X_train_values = X_train
    y_train_values = y_train

    # Create base models
    rf_model = RandomForestRegressor(
        random_state=random_state,
        n_jobs=-1,
        **rf_params
    )

    xgb_model = xgb.XGBRegressor(
        n_estimators=300,
        learning_rate=0.05,
        max_depth=6,
        subsample=0.8,
        colsample_bytree=0.8,
        random_state=random_state,
        n_jobs=-1
    )

    lgb_model = lgb.LGBMRegressor(
        n_estimators=300,
        learning_rate=0.05,
        num_leaves=31,
        subsample=0.8,
        colsample_bytree=0.8,
        random_state=random_state,
        n_jobs=-1
    )

    # Create meta-model
    meta_model = RandomForestRegressor(
        n_estimators=200,
        max_depth=None,
        random_state=random_state,
        n_jobs=-1
    )

    # Create stacking ensemble
    stacking_ensemble = StackingRegressor(
        estimators=[
            ('rf', rf_model),
            ('xgb', xgb_model),
            ('lgb', lgb_model)
        ],
        final_estimator=meta_model,
        cv=5,
        n_jobs=-1
    )

    try:
        # Train ensemble
        logger.info("Training stacking ensemble...")
        stacking_ensemble.fit(X_train_values, y_train_values)
        logger.info("Stacking ensemble training completed")
        return stacking_ensemble
    except Exception as e:
        logger.error(f"Error training stacking ensemble: {e}")
        logger.info("Falling back to Random Forest model")
        # Fall back to Random Forest if stacking fails
        rf_model = RandomForestRegressor(
            random_state=random_state,
            n_jobs=-1,
            **rf_params
        )
        rf_model.fit(X_train_values, y_train_values)
        return rf_model

def calculate_shap_values(model, X, feature_names, max_display=20):
    """
    Calculate SHAP values for feature importance.

    Args:
        model: Trained model
        X (np.ndarray or pd.DataFrame): Feature matrix
        feature_names (List[str]): Feature names
        max_display (int): Maximum number of features to display

    Returns:
        Tuple[np.ndarray, List[str]]: SHAP values and feature names
    """
    logger.info("Calculating SHAP values for feature importance")

    try:
        # Convert to numpy array if it's a DataFrame
        if isinstance(X, pd.DataFrame):
            X_values = X.values
        else:
            X_values = X

        # Create explainer
        if isinstance(model, RandomForestRegressor):
            explainer = shap.TreeExplainer(model)
        elif isinstance(model, StackingRegressor):
            # Use the meta-estimator for SHAP values
            explainer = shap.TreeExplainer(model.final_estimator_)
        else:
            logger.warning(f"Unsupported model type for SHAP: {type(model)}. Using permutation importance instead.")
            return None, None

        # Calculate SHAP values
        shap_values = explainer.shap_values(X_values)

        # Get mean absolute SHAP values
        mean_abs_shap = np.mean(np.abs(shap_values), axis=0)

        # Sort features by importance
        indices = np.argsort(mean_abs_shap)[::-1]

        # Get top features
        top_indices = indices[:max_display]
        top_features = [feature_names[i] for i in top_indices]
        top_values = mean_abs_shap[top_indices]

        return top_values, top_features
    except Exception as e:
        logger.error(f"Error calculating SHAP values: {e}")
        return None, None

def train_model(features_df, metadata_df, output_dir, metrics_dir, target, args):
    """
    Train an advanced model for the specified target with multiple enhancements.

    Args:
        features_df (pd.DataFrame): DataFrame with features
        metadata_df (pd.DataFrame): DataFrame with metadata
        output_dir (Path): Directory to save model and results
        metrics_dir (Path): Directory to save metrics
        target (str): Target variable to predict ('growth_rate' or 'optimal_temperature')
        args (argparse.Namespace): Command line arguments

    Returns:
        dict: Dictionary with training results
    """
    logger.info(f"Training advanced model for {target}")

    # Create taxonomy features
    logger.info("Creating taxonomy features")
    taxonomy_df = create_taxonomy_features(metadata_df)

    # Create enhanced breakpoint features
    logger.info("Creating enhanced breakpoint features")
    breakpoint_df = create_enhanced_breakpoint_features(features_df)

    # Enhance codon features
    logger.info("Enhancing codon features")
    enhanced_codon_df = enhance_codon_features(features_df)

    # Merge all features
    logger.info("Merging all features")

    # Merge taxonomy features with metadata
    merged_df = pd.merge(metadata_df, taxonomy_df, on='genome_id', how='inner')
    logger.info(f"After merging taxonomy: {len(merged_df)} samples")

    # Merge with breakpoint features
    merged_df = pd.merge(merged_df, breakpoint_df, on='genome_id', how='left')
    logger.info(f"After merging breakpoint: {len(merged_df)} samples")

    # Merge with enhanced codon features
    merged_df = pd.merge(merged_df, enhanced_codon_df, on='genome_id', how='left')
    logger.info(f"After merging enhanced codon: {len(merged_df)} samples")

    logger.info(f"Combined dataset has {len(merged_df)} samples and {len(merged_df.columns)} features")

    # Split into features and targets
    X = merged_df.drop(['genome_id', 'growth_rate', 'optimal_temperature', 'taxid', 'genetic_code', 'kingdom'], axis=1, errors='ignore')

    if target == 'growth_rate':
        y = merged_df['growth_rate']
        n_features_to_select = args.growth_rate_features
    else:  # temperature
        y = merged_df['optimal_temperature']
        n_features_to_select = args.optimal_temperature_features

    # Check for NaN values in features
    nan_count = X.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in features. Filling with zeros.")
        X = X.fillna(0)

    # Check for infinite values
    inf_count = np.isinf(X.to_numpy()).sum()
    if inf_count > 0:
        logger.warning(f"Found {inf_count} infinite values in features. Replacing with zeros.")
        X = X.replace([np.inf, -np.inf], 0)

    # Drop rows with NA values in target to ensure consistent data
    valid_mask = ~y.isna()
    if (~valid_mask).any():
        logger.warning(f"Dropping {(~valid_mask).sum()} rows with missing target values")
        X = X.loc[valid_mask]
        y = y.loc[valid_mask]

    # Split into train and test sets
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=args.seed)

    # Standardize features
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)

    # Convert back to DataFrame for feature names
    X_train_scaled_df = pd.DataFrame(X_train_scaled, columns=X_train.columns, index=X_train.index)
    X_test_scaled_df = pd.DataFrame(X_test_scaled, columns=X_test.columns, index=X_test.index)

    # Save scaler
    scaler_file = output_dir / f"{target}_scaler.joblib"
    dump(scaler, scaler_file)
    logger.info(f"Saved scaler to {scaler_file}")

    # Perform optimized feature selection if enabled
    if args.use_feature_selection:
        logger.info(f"Performing optimized feature selection to select {n_features_to_select} features")
        selected_indices = perform_optimized_feature_selection(
            X_train_scaled,
            y_train,
            n_features_to_select=n_features_to_select,
            random_state=args.seed
        )
    else:
        # Use RFE for feature selection (as in the original script)
        logger.info(f"Performing RFE to select {n_features_to_select} features")
        rf = RandomForestRegressor(
            n_estimators=100,
            max_depth=None,
            min_samples_split=2,
            min_samples_leaf=1,
            random_state=args.seed,
            n_jobs=-1
        )

        rfe = RFE(
            estimator=rf,
            n_features_to_select=n_features_to_select,
            step=args.rfe_step if hasattr(args, 'rfe_step') else 0.2,
            verbose=1
        )

        rfe.fit(X_train_scaled, y_train)
        selected_indices = np.where(rfe.support_)[0]

    # Get selected feature names
    selected_features = X_train.columns[selected_indices].tolist()
    logger.info(f"Selected {len(selected_features)} features: {selected_features[:10]}...")

    # Save selected features
    selected_features_file = metrics_dir / f"{target}_selected_features.json"
    with open(selected_features_file, 'w') as f:
        json.dump(selected_features, f, indent=2)
    logger.info(f"Saved selected features to {selected_features_file}")

    # Use only selected features
    X_train_selected = X_train_scaled_df.iloc[:, selected_indices]
    X_test_selected = X_test_scaled_df.iloc[:, selected_indices]

    # Create a preliminary model to identify top features for interactions
    logger.info("Training preliminary model to identify top features for interactions")
    prelim_model = RandomForestRegressor(
        n_estimators=100,
        max_depth=None,
        min_samples_split=2,
        min_samples_leaf=1,
        random_state=args.seed,
        n_jobs=-1
    )

    # Verify that X_train_selected and y_train have the same number of samples
    logger.info(f"X_train_selected shape: {X_train_selected.shape}, y_train shape: {y_train.shape}")
    if X_train_selected.shape[0] != y_train.shape[0]:
        logger.warning("Inconsistent sample counts detected, ensuring alignment")
        common_indices = X_train_selected.index.intersection(y_train.index)
        X_train_selected = X_train_selected.loc[common_indices]
        y_train = y_train.loc[common_indices]
        logger.info(f"After alignment: X_train_selected shape: {X_train_selected.shape}, y_train shape: {y_train.shape}")

    prelim_model.fit(X_train_selected, y_train)

    # Get feature importances
    importances = prelim_model.feature_importances_

    # Sort features by importance
    indices = np.argsort(importances)[::-1]
    top_features = [selected_features[i] for i in indices[:20]]  # Top 20 features

    logger.info(f"Top features for interactions: {top_features[:10]}...")

    # Create advanced interaction features if enabled
    if args.use_advanced_interactions:
        logger.info("Creating advanced interaction features")
        interaction_features_train = create_advanced_interactions(
            X_train,
            top_features,
            interaction_depth=args.interaction_depth,
            max_interactions=args.max_interactions
        )

        interaction_features_test = create_advanced_interactions(
            X_test,
            top_features,
            interaction_depth=args.interaction_depth,
            max_interactions=args.max_interactions
        )

        # Add interaction features to selected features, ensuring index alignment
        if not interaction_features_train.empty:
            # Ensure indices match before concatenation
            common_train_indices = X_train_selected.index.intersection(interaction_features_train.index)
            X_train_selected_aligned = X_train_selected.loc[common_train_indices]
            interaction_features_train_aligned = interaction_features_train.loc[common_train_indices]

            X_train_enhanced = pd.concat([X_train_selected_aligned, interaction_features_train_aligned], axis=1)

            # Align y_train as well
            y_train = y_train.loc[common_train_indices]

            # Do the same for test set
            common_test_indices = X_test_selected.index.intersection(interaction_features_test.index)
            X_test_selected_aligned = X_test_selected.loc[common_test_indices]
            interaction_features_test_aligned = interaction_features_test.loc[common_test_indices]

            X_test_enhanced = pd.concat([X_test_selected_aligned, interaction_features_test_aligned], axis=1)
            y_test = y_test.loc[common_test_indices]

            # Ensure both train and test have the same columns
            all_columns = set(X_train_enhanced.columns) | set(X_test_enhanced.columns)

            # Add missing columns with zeros
            for col in all_columns:
                if col not in X_train_enhanced.columns:
                    X_train_enhanced[col] = 0
                if col not in X_test_enhanced.columns:
                    X_test_enhanced[col] = 0

            # Ensure same column order
            X_train_enhanced = X_train_enhanced[sorted(all_columns)]
            X_test_enhanced = X_test_enhanced[sorted(all_columns)]

            logger.info(f"Added {interaction_features_train.shape[1]} interaction features")
            logger.info(f"Final shapes - X_train_enhanced: {X_train_enhanced.shape}, y_train: {y_train.shape}")
            logger.info(f"Final shapes - X_test_enhanced: {X_test_enhanced.shape}, y_test: {y_test.shape}")
        else:
            X_train_enhanced = X_train_selected
            X_test_enhanced = X_test_selected
            logger.info("No interaction features added")
    else:
        X_train_enhanced = X_train_selected
        X_test_enhanced = X_test_selected
        logger.info("Advanced interactions disabled")

    # Optimize hyperparameters using Bayesian optimization
    logger.info("Optimizing hyperparameters")
    best_params = optimize_rf_hyperparameters(
        X_train_enhanced,
        y_train,
        X_test_enhanced,
        y_test,
        max_estimators=args.max_estimators,
        n_trials=args.bayesian_iterations,
        random_state=args.seed
    )

    # Save hyperparameters
    hyperparams_file = metrics_dir / f"{target}_hyperparameters.json"
    with open(hyperparams_file, 'w') as f:
        json.dump(best_params, f, indent=2)
    logger.info(f"Saved hyperparameters to {hyperparams_file}")

    # Train final model
    if args.use_stacking:
        logger.info("Training stacking ensemble model")
        model = create_stacking_ensemble(
            X_train_enhanced,
            y_train,
            rf_params=best_params,
            random_state=args.seed
        )
    else:
        logger.info("Training Random Forest model")
                model = RandomForestRegressor(
            random_state=args.seed,
            n_jobs=-1,
            **best_params
        )
        model.feature_names = feature_names  # Store feature names
        model.fit(X_train_enhanced, y_train)

    # Save model
    model_file = output_dir / f"{target}_model.joblib"
    dump(model, model_file)
    logger.info(f"Saved model to {model_file}")

    # Make predictions
    y_pred_train = model.predict(X_train_enhanced)
    y_pred_test = model.predict(X_test_enhanced)

    # Calculate metrics
    train_metrics = {
        'mse': mean_squared_error(y_train, y_pred_train),
        'rmse': np.sqrt(mean_squared_error(y_train, y_pred_train)),
        'mae': mean_absolute_error(y_train, y_pred_train),
        'r2': r2_score(y_train, y_pred_train)
    }

    test_metrics = {
        'mse': mean_squared_error(y_test, y_pred_test),
        'rmse': np.sqrt(mean_squared_error(y_test, y_pred_test)),
        'mae': mean_absolute_error(y_test, y_pred_test),
        'r2': r2_score(y_test, y_pred_test)
    }

    # Log metrics
    logger.info(f"{target} - Train: R²={train_metrics['r2']:.4f}, RMSE={train_metrics['rmse']:.4f}")
    logger.info(f"{target} - Test: R²={test_metrics['r2']:.4f}, RMSE={test_metrics['rmse']:.4f}")

    # Calculate feature importance
    logger.info("Calculating feature importance")

    # Get feature names (including interaction features)
    feature_names = list(X_train_enhanced.columns)

    # Calculate SHAP values if enabled
    if args.use_shap:
        logger.info("Calculating SHAP values for feature importance")
        try:
            # For stacking ensemble, use the final estimator
            if isinstance(model, StackingRegressor):
                logger.info("Using final estimator for SHAP values in stacking ensemble")
                shap_values, shap_features = calculate_shap_values(
                    model.final_estimator_,
                    X_test_enhanced.iloc[:100],  # Use a subset for efficiency
                    feature_names,
                    max_display=30
                )
            else:
                shap_values, shap_features = calculate_shap_values(
                    model,
                    X_test_enhanced.iloc[:100],  # Use a subset for efficiency
                    feature_names,
                    max_display=30
                )

            if shap_values is not None and shap_features is not None:
                # Create SHAP importance dataframe
                shap_importance_df = pd.DataFrame({
                    'feature': shap_features,
                    'importance': shap_values
                })

                # Save SHAP importance
                shap_importance_file = metrics_dir / f"{target}_shap_importance.tsv"
                shap_importance_df.to_csv(shap_importance_file, sep='\t', index=False)
                logger.info(f"Saved SHAP importance to {shap_importance_file}")

                # Plot SHAP summary
                try:
                    plt.figure(figsize=(12, 10))
                    if isinstance(model, StackingRegressor):
                        explainer = shap.TreeExplainer(model.final_estimator_)
                    else:
                        explainer = shap.TreeExplainer(model)

                    shap_values_plot = explainer.shap_values(X_test_enhanced.iloc[:100])
                    shap.summary_plot(
                        shap_values=shap_values_plot,
                        features=X_test_enhanced.iloc[:100],
                        feature_names=feature_names,
                        show=False
                    )
                    plt.tight_layout()
                    plt.savefig(metrics_dir / f"{target}_shap_summary.png", dpi=300, bbox_inches='tight')
                    plt.close()
                except Exception as e:
                    logger.error(f"Error plotting SHAP summary: {e}")
        except Exception as e:
            logger.error(f"Error calculating SHAP values: {e}")

    # Calculate permutation importance for more reliable importance values
    logger.info("Calculating permutation importance")
    perm_importance = permutation_importance(
        model, X_test_enhanced, y_test,
        n_repeats=10,
        random_state=args.seed,
        n_jobs=-1
    )

    # Create feature importance dataframe
    importance_df = pd.DataFrame({
        'feature': feature_names,
        'importance': perm_importance.importances_mean
    }).sort_values('importance', ascending=False)

    # Save feature importance
    importance_file = metrics_dir / f"{target}_feature_importance.tsv"
    importance_df.to_csv(importance_file, sep='\t', index=False)
    logger.info(f"Saved feature importance to {importance_file}")

    # Analyze codon feature importance
    codon_features = [f for f in feature_names if any(pattern in f for pattern in
                    ['CUB', 'CPB', 'CAI', 'ENC', 'HEG_', 'BG_', 'delta_', 'RSCU_',
                     'codon_bias_', 'AAAI', 'aa_bias_HEG_BP', 'MILC'])]

    codon_importance_df = importance_df[importance_df['feature'].isin(codon_features)].sort_values('importance', ascending=False)

    # Save codon feature importance
    codon_importance_file = metrics_dir / f"{target}_codon_feature_importance.tsv"
    codon_importance_df.to_csv(codon_importance_file, sep='\t', index=False)
    logger.info(f"Saved codon feature importance to {codon_importance_file}")

    # Log top codon features
    logger.info(f"Top 10 codon features for {target}:")
    for i, (feature, importance) in enumerate(zip(codon_importance_df['feature'].head(10), codon_importance_df['importance'].head(10))):
        logger.info(f"{i+1}. {feature}: {importance:.6f}")

    # Plot feature importance
    plt.figure(figsize=(12, 8))
    sns.barplot(x='importance', y='feature', data=importance_df.head(20))
    plt.title(f"Top 20 Important Features for {target}")
    plt.tight_layout()
    plt.savefig(metrics_dir / f"{target}_feature_importance.png", dpi=300)
    plt.close()

    # Plot codon feature importance
    plt.figure(figsize=(12, 8))
    sns.barplot(x='importance', y='feature', data=codon_importance_df.head(20))
    plt.title(f"Top 20 Important Codon Features for {target}")
    plt.tight_layout()
    plt.savefig(metrics_dir / f"{target}_codon_feature_importance.png", dpi=300)
    plt.close()

    # Create scatter plot
    plt.figure(figsize=(10, 8))
    plt.scatter(y_test, y_pred_test, alpha=0.7)

    # Add identity line
    min_val = min(min(y_test), min(y_pred_test))
    max_val = max(max(y_test), max(y_pred_test))
    plt.plot([min_val, max_val], [min_val, max_val], 'r--')

    plt.title(f"Predicted vs. True {target}")
    plt.xlabel(f"True {target}")
    plt.ylabel(f"Predicted {target}")

    # Add metrics text
    plt.text(0.05, 0.95, f"R² = {test_metrics['r2']:.4f}", transform=plt.gca().transAxes)
    plt.text(0.05, 0.90, f"RMSE = {test_metrics['rmse']:.4f}", transform=plt.gca().transAxes)
    plt.text(0.05, 0.85, f"MAE = {test_metrics['mae']:.4f}", transform=plt.gca().transAxes)

    plt.tight_layout()
    plt.savefig(metrics_dir / f"{target}_scatter_plot.png", dpi=300)
    plt.close()

    # Save results
    results = {
        'train_metrics': train_metrics,
        'test_metrics': test_metrics,
        'feature_importance': importance_df.to_dict('records')
    }

    # Save results to file
    pd.DataFrame({
        'metric': ['mse', 'rmse', 'mae', 'r2'],
        'train': [train_metrics['mse'], train_metrics['rmse'], train_metrics['mae'], train_metrics['r2']],
        'test': [test_metrics['mse'], test_metrics['rmse'], test_metrics['mae'], test_metrics['r2']]
    }).to_csv(metrics_dir / f"{target}_metrics.tsv", sep='\t', index=False)

    # Save predictions
    pd.DataFrame({
        'true': y_test,
        'pred': y_pred_test
    }).to_csv(metrics_dir / f"{target}_predictions.tsv", sep='\t', index=False)

    # Save combined metrics for the training script
    combined_metrics = {
        'train_metrics': [{
            f'{target}_mse': float(train_metrics['mse']),
            f'{target}_rmse': float(train_metrics['rmse']),
            f'{target}_mae': float(train_metrics['mae']),
            f'{target}_r2': float(train_metrics['r2'])
        }],
        'val_metrics': [{
            f'{target}_mse': float(test_metrics['mse']),
            f'{target}_rmse': float(test_metrics['rmse']),
            f'{target}_mae': float(test_metrics['mae']),
            f'{target}_r2': float(test_metrics['r2']),
            f'{target}_preds': y_pred_test.tolist(),
            f'{target}_targets': y_test.tolist()
        }]
    }

    return combined_metrics

def main():
    """Main function."""
    args = parse_args()
    logger.info("Starting advanced RF hybrid model training...")

    # Create output directories
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)

    if args.metrics_dir is None:
        metrics_dir = Path(args.output_dir) / 'metrics'
    else:
        metrics_dir = Path(args.metrics_dir)
    metrics_dir.mkdir(parents=True, exist_ok=True)

    # Load metadata
    logger.info(f"Loading metadata from {args.metadata}...")
    metadata_df = pd.read_csv(args.metadata, sep='\t')
    logger.info(f"Loaded metadata with {len(metadata_df)} samples")

    # Load features
    logger.info(f"Loading features from {args.feature_file}...")
    features_df = pd.read_csv(args.feature_file, sep='\t')
    logger.info(f"Loaded features with {len(features_df)} samples and {len(features_df.columns)} features")

    # Train models
    all_metrics = {}
    hyperparams = {}

    if args.target in ['growth_rate', 'both']:
        logger.info("Training model for growth rate...")
        growth_metrics = train_model(features_df, metadata_df, output_dir, metrics_dir, 'growth_rate', args)
        all_metrics.update(growth_metrics)

        # Load hyperparameters
        growth_hyperparams_file = metrics_dir / "growth_rate_hyperparameters.json"
        if growth_hyperparams_file.exists():
            with open(growth_hyperparams_file, 'r') as f:
                hyperparams['growth_rate'] = json.load(f)

    if args.target in ['temperature', 'both']:
        logger.info("Training model for optimal temperature...")
        temp_metrics = train_model(features_df, metadata_df, output_dir, metrics_dir, 'optimal_temperature', args)

        # Merge metrics
        if 'train_metrics' in all_metrics:
            all_metrics['train_metrics'][0].update(temp_metrics['train_metrics'][0])
            all_metrics['val_metrics'][0].update(temp_metrics['val_metrics'][0])
        else:
            all_metrics.update(temp_metrics)

        # Load hyperparameters
        temp_hyperparams_file = metrics_dir / "optimal_temperature_hyperparameters.json"
        if temp_hyperparams_file.exists():
            with open(temp_hyperparams_file, 'r') as f:
                hyperparams['optimal_temperature'] = json.load(f)

    # Save combined metrics
    metrics_file = metrics_dir / 'training_metrics.json'
    with open(metrics_file, 'w') as f:
        json.dump(all_metrics, f, indent=2)

    # Save combined hyperparameters
    hyperparams_file = metrics_dir / 'hyperparameters.json'
    with open(hyperparams_file, 'w') as f:
        json.dump(hyperparams, f, indent=2)

    logger.info(f"Saved combined metrics to {metrics_file}")
    logger.info(f"Saved combined hyperparameters to {hyperparams_file}")
    logger.info("Training completed successfully")

if __name__ == '__main__':
    main()
