#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Compare the performance of different hybrid model versions.
This script loads and analyzes the results from different hybrid model versions
to find the best configuration for both growth rate and temperature prediction.
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import argparse
import glob
from typing import List, Dict, Tuple
import re

def parse_metrics_from_log(log_file: str) -> Dict[str, float]:
    """Parse metrics from training log file."""
    metrics = {}
    
    try:
        with open(log_file, 'r') as f:
            content = f.read()
            
            # Find overall metrics
            overall_r2_scaled = re.search(r"Final metrics \(scaled\) - Overall R²: ([\d\.]+)", content)
            if overall_r2_scaled:
                metrics['overall_r2_scaled'] = float(overall_r2_scaled.group(1))
                
            overall_r2_original = re.search(r"Final metrics \(original\) - Overall R²: ([\d\.]+)", content)
            if overall_r2_original:
                metrics['overall_r2_original'] = float(overall_r2_original.group(1))
            
            # Find growth rate metrics
            growth_r2_scaled = re.search(r"Final metrics \(scaled\) - Growth Rate R²: ([\d\.]+)", content)
            if growth_r2_scaled:
                metrics['growth_r2_scaled'] = float(growth_r2_scaled.group(1))
                
            growth_r2_original = re.search(r"Final metrics \(original\) - Growth Rate R²: ([\d\.]+)", content)
            if growth_r2_original:
                metrics['growth_r2_original'] = float(growth_r2_original.group(1))
            
            # Find temperature metrics
            temp_r2_scaled = re.search(r"Final metrics \(scaled\) - Temperature R²: ([\d\.]+)", content)
            if temp_r2_scaled:
                metrics['temp_r2_scaled'] = float(temp_r2_scaled.group(1))
                
            temp_r2_original = re.search(r"Final metrics \(original\) - Temperature R²: ([\d\.]+)", content)
            if temp_r2_original:
                metrics['temp_r2_original'] = float(temp_r2_original.group(1))
            
            # Find additional metrics if available
            val_temp_r2 = re.search(r"Final Temperature Model - Val R²: ([\d\.]+)", content)
            if val_temp_r2:
                metrics['val_temp_r2'] = float(val_temp_r2.group(1))
            
            val_growth_r2 = re.search(r"Validation R² with optimized weights - Growth: ([\d\.]+)", content)
            if val_growth_r2:
                metrics['val_growth_r2'] = float(val_growth_r2.group(1))
        
        # Ensure required metrics exist
        if 'temp_r2_original' not in metrics and 'temp_r2_scaled' in metrics:
            metrics['temp_r2_original'] = metrics['temp_r2_scaled']
            
        if 'growth_r2_original' not in metrics and 'growth_r2_scaled' in metrics:
            metrics['growth_r2_original'] = metrics['growth_r2_scaled']
            
        if 'overall_r2_original' not in metrics and 'overall_r2_scaled' in metrics:
            metrics['overall_r2_original'] = metrics['overall_r2_scaled']
    
    except Exception as e:
        print(f"Error parsing log file {log_file}: {e}")
        # Return empty metrics with default values
        metrics = {
            'overall_r2_scaled': 0,
            'overall_r2_original': 0,
            'growth_r2_scaled': 0,
            'growth_r2_original': 0,
            'temp_r2_scaled': 0,
            'temp_r2_original': 0
        }
    
    return metrics

def find_log_files(model_dirs: List[str]) -> Dict[str, str]:
    """Find log files for each model directory."""
    log_files = {}
    
    for model_dir in model_dirs:
        model_name = os.path.basename(model_dir)
        
        # Try to find nohup.out in the model directory
        nohup_path = os.path.join(model_dir, 'nohup.out')
        if os.path.exists(nohup_path):
            log_files[model_name] = nohup_path
            continue
        
        # Try to find .log files in the model directory
        log_paths = glob.glob(os.path.join(model_dir, '*.log'))
        if log_paths:
            log_files[model_name] = log_paths[0]
            continue
            
        # If no log files in the model directory, look for terminal output files
        terminal_paths = glob.glob(os.path.join(model_dir, 'terminal_output.txt'))
        if terminal_paths:
            log_files[model_name] = terminal_paths[0]
            continue
            
        # If no terminal output, try to create one from stdout captured in model_dir/stdout.txt
        stdout_path = os.path.join(model_dir, 'stdout.txt')
        if os.path.exists(stdout_path):
            log_files[model_name] = stdout_path
            continue
            
        # If no log files in the model directory, try in the parent directory
        parent_dir = os.path.dirname(model_dir)
        log_pattern = f'*{model_name}*.log'
        parent_log_paths = glob.glob(os.path.join(parent_dir, log_pattern))
        
        if parent_log_paths:
            log_files[model_name] = parent_log_paths[0]
            continue
            
        # If no log file found, try to capture terminal output directly
        try:
            # Create terminal output file
            terminal_output_path = os.path.join(model_dir, 'terminal_output.txt')
            os.system(f"cat /dev/null > {terminal_output_path}")
            
            # Try to find metrics in any file in the model directory
            for root, _, files in os.walk(model_dir):
                for file in files:
                    if file.endswith('.txt') or file.endswith('.log'):
                        file_path = os.path.join(root, file)
                        try:
                            with open(file_path, 'r') as f:
                                content = f.read()
                                if "Final metrics" in content:
                                    with open(terminal_output_path, 'w') as out_f:
                                        out_f.write(content)
                                    log_files[model_name] = terminal_output_path
                                    break
                        except:
                            continue
        except:
            pass
    
    return log_files

def compare_models(model_dirs: List[str], output_dir: str = None) -> pd.DataFrame:
    """
    Compare different model versions.
    
    Args:
        model_dirs: List of model directories to compare
        output_dir: Directory to save comparison results
        
    Returns:
        DataFrame with model comparison
    """
    # Find log files for each model
    log_files = find_log_files(model_dirs)
    
    # Extract metrics from each log file
    model_metrics = {}
    for model_name, log_file in log_files.items():
        if os.path.exists(log_file):
            metrics = parse_metrics_from_log(log_file)
            model_metrics[model_name] = metrics
        else:
            print(f"Warning: Log file not found for {model_name}")
    
    # Create comparison DataFrame
    df = pd.DataFrame.from_dict(model_metrics, orient='index')
    
    # Fill missing values
    df = df.fillna(0)
    
    # Ensure required columns exist
    required_columns = ['temp_r2_original', 'growth_r2_original', 'overall_r2_original']
    for col in required_columns:
        if col not in df.columns:
            df[col] = 0
    
    # Sort by temperature R²
    df = df.sort_values('temp_r2_original', ascending=False)
    
    # Save comparison if output_dir provided
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
        
        # Save to CSV
        df.to_csv(os.path.join(output_dir, 'model_comparison.csv'))
        
        # Create bar chart for temperature R²
        plt.figure(figsize=(12, 6))
        df['temp_r2_original'].plot(kind='bar', color='lightblue')
        plt.title('Temperature R² Comparison')
        plt.ylabel('R²')
        plt.xlabel('Model')
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'temperature_r2_comparison.png'))
        
        # Create bar chart for growth rate R²
        plt.figure(figsize=(12, 6))
        df['growth_r2_original'].plot(kind='bar', color='lightgreen')
        plt.title('Growth Rate R² Comparison')
        plt.ylabel('R²')
        plt.xlabel('Model')
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'growth_rate_r2_comparison.png'))
        
        # Create combined bar chart
        plt.figure(figsize=(12, 6))
        df[['growth_r2_original', 'temp_r2_original']].plot(kind='bar')
        plt.title('Model Performance Comparison')
        plt.ylabel('R²')
        plt.xlabel('Model')
        plt.xticks(rotation=45)
        plt.legend(['Growth Rate', 'Temperature'])
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'combined_r2_comparison.png'))
    
    return df

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Compare different hybrid model versions')
    parser.add_argument('--model_dirs', type=str, nargs='+', required=True,
                       help='List of model directories to compare')
    parser.add_argument('--output_dir', type=str, default='model_comparison',
                       help='Directory to save comparison results')
    
    args = parser.parse_args()
    
    # Compare models
    df = compare_models(args.model_dirs, args.output_dir)
    
    # Print results
    print("Model Comparison Results:")
    print(df)
    
    # Find best models
    best_temp_model = df['temp_r2_original'].idxmax()
    best_growth_model = df['growth_r2_original'].idxmax()
    best_overall_model = df['overall_r2_original'].idxmax()
    
    print(f"\nBest Temperature Model: {best_temp_model} (R² = {df.loc[best_temp_model, 'temp_r2_original']:.4f})")
    print(f"Best Growth Rate Model: {best_growth_model} (R² = {df.loc[best_growth_model, 'growth_r2_original']:.4f})")
    print(f"Best Overall Model: {best_overall_model} (R² = {df.loc[best_overall_model, 'overall_r2_original']:.4f})")
    
    # Save summary
    if args.output_dir:
        with open(os.path.join(args.output_dir, 'summary.txt'), 'w') as f:
            f.write("Model Comparison Results:\n")
            f.write(str(df) + "\n\n")
            f.write(f"Best Temperature Model: {best_temp_model} (R² = {df.loc[best_temp_model, 'temp_r2_original']:.4f})\n")
            f.write(f"Best Growth Rate Model: {best_growth_model} (R² = {df.loc[best_growth_model, 'growth_r2_original']:.4f})\n")
            f.write(f"Best Overall Model: {best_overall_model} (R² = {df.loc[best_overall_model, 'overall_r2_original']:.4f})\n")
        
        print(f"Comparison results saved to {args.output_dir}")

if __name__ == "__main__":
    main() 