#!/usr/bin/env python3
"""
Hyperparameter search script for the enhanced RLVR model.

This script runs a series of enhanced RLVR model training runs with different hyperparameter
combinations to find the best performing model.
"""

import os
import subprocess
import itertools
import json
from pathlib import Path

# Define hyperparameter search space
param_grid = {
    "hidden_dim": [384, 512, 768],
    "num_heads": [6, 8, 12],
    "num_layers": [2, 3, 4],
    "dropout": [0.2, 0.3, 0.4],
    "activation": ["gelu", "silu"],
    "interaction_layers": [1, 2, 3],
    "alpha": [0.6, 0.7, 0.8],
    "beta": [0.1, 0.2, 0.3],
    "gamma": [0.05, 0.1, 0.15],
    "accuracy_scale": [0.3, 0.5, 0.8],
    "lr": [1e-5, 5e-5, 1e-4],
    "weight_decay": [1e-5, 1e-4, 5e-4],
    "batch_size": [32, 64, 128],
    "n_features": [75, 100, 150]
}

# Define a smaller subset for initial testing
param_subset = {
    "hidden_dim": [512, 768],
    "num_heads": [8, 12],
    "num_layers": [3, 4],
    "dropout": [0.3, 0.4],
    "activation": ["silu"],
    "interaction_layers": [2],
    "alpha": [0.7, 0.8],
    "beta": [0.2],
    "gamma": [0.1],
    "accuracy_scale": [0.5],
    "lr": [5e-5],
    "weight_decay": [1e-4],
    "batch_size": [64],
    "n_features": [100, 150]
}

# Use the subset for faster testing
search_space = param_subset

# Generate parameter combinations (limit to a reasonable number)
keys = list(search_space.keys())
values = list(search_space.values())
combinations = list(itertools.product(*values))

# Limit to a maximum number of combinations
max_combinations = 8
if len(combinations) > max_combinations:
    print(f"Limiting to {max_combinations} combinations out of {len(combinations)} possible")
    combinations = combinations[:max_combinations]

# Create base directory for results
base_dir = Path("hyperparam_search_enhanced")
base_dir.mkdir(exist_ok=True)

# Track best model
best_r2 = float('-inf')
best_params = None
best_run_id = None

# First, enhance the RLVR agent with improved architecture
subprocess.run(["python", "enhance_rlvr_agent_simple.py"], check=True)

# Run training with each parameter combination
for i, combo in enumerate(combinations):
    # Create parameter dictionary
    params = dict(zip(keys, combo))

    # Create run ID
    run_id = f"run_{i+1:03d}"
    run_dir = base_dir / run_id
    model_dir = run_dir / "model"
    metrics_dir = run_dir / "metrics"

    # Create directories
    model_dir.mkdir(parents=True, exist_ok=True)
    metrics_dir.mkdir(parents=True, exist_ok=True)

    # Build command
    cmd = [
        "python", "train_rlvr_model.py",
        "--feature-file", "training_data/combined_features.tsv",
        "--metadata", "training_data/metadata.tsv",
        "--output-dir", str(model_dir),
        "--metrics-dir", str(metrics_dir),
        "--hidden-dim", str(params["hidden_dim"]),
        "--num-heads", str(params["num_heads"]),
        "--num-layers", str(params["num_layers"]),
        "--dropout", str(params["dropout"]),
        "--use-layer-norm",
        "--use-residual",
        "--use-value-head",
        "--activation", params["activation"],
        "--use-enhanced-agent",
        "--use-feature-interaction",
        "--interaction-layers", str(params["interaction_layers"]),
        "--alpha", str(params["alpha"]),
        "--beta", str(params["beta"]),
        "--gamma", str(params["gamma"]),
        "--accuracy-scale", str(params["accuracy_scale"]),
        "--lr", str(params["lr"]),
        "--weight-decay", str(params["weight_decay"]),
        "--batch-size", str(params["batch_size"]),
        "--epochs", "150",
        "--early-stopping-patience", "15",
        "--entropy-coef", "0.01",
        "--value-loss-coef", "0.5",
        "--max-grad-norm", "1.0",
        "--seed", "42",
        "--feature-selection",
        "--n-features", str(params["n_features"])
    ]

    # Print current run info
    print(f"\n{'='*80}")
    print(f"Running {run_id} with parameters:")
    for k, v in params.items():
        print(f"  {k}: {v}")
    print(f"{'='*80}\n")

    # Run the command
    try:
        subprocess.run(cmd, check=True)

        # Check results
        metrics_file = metrics_dir / "test_metrics.json"
        if metrics_file.exists():
            with open(metrics_file, 'r') as f:
                metrics = json.load(f)

            # Get test R²
            test_r2 = metrics.get("test_r2", float('-inf'))
            print(f"Run {run_id} completed with test R²: {test_r2:.4f}")

            # Update best model if better
            if test_r2 > best_r2:
                best_r2 = test_r2
                best_params = params
                best_run_id = run_id
                print(f"New best model found!")
        else:
            print(f"No metrics file found for run {run_id}")

    except subprocess.CalledProcessError as e:
        print(f"Error running {run_id}: {e}")

    # Save parameters to file
    params_file = run_dir / "params.json"
    with open(params_file, 'w') as f:
        json.dump(params, f, indent=2)

# Print best model
print("\n" + "="*80)
print(f"Best model: {best_run_id} with test R²: {best_r2:.4f}")
print("Parameters:")
for k, v in best_params.items():
    print(f"  {k}: {v}")
print("="*80)

# Save best parameters to file
best_params_file = base_dir / "best_params.json"
with open(best_params_file, 'w') as f:
    json.dump({
        "run_id": best_run_id,
        "test_r2": best_r2,
        "params": best_params
    }, f, indent=2)

print(f"\nBest parameters saved to {best_params_file}")

# Create a script to run the best model
best_model_script = """#!/bin/bash
# Run script for training the enhanced RLVR model with the best hyperparameters found

# First, enhance the RLVR agent with improved architecture
python enhance_rlvr_agent_v2.py

# Make script executable
chmod +x train_rlvr_model.py

# Create output directories
mkdir -p models/best_enhanced_rlvr_model
mkdir -p metrics/best_enhanced_rlvr_model

# Run training script with best hyperparameters
python train_rlvr_model.py \\
    --feature-file training_data/combined_features.tsv \\
    --metadata training_data/metadata.tsv \\
    --output-dir models/best_enhanced_rlvr_model \\
    --metrics-dir metrics/best_enhanced_rlvr_model \\
    --hidden-dim {hidden_dim} \\
    --num-heads {num_heads} \\
    --num-layers {num_layers} \\
    --dropout {dropout} \\
    --use-layer-norm \\
    --use-residual \\
    --use-value-head \\
    --activation {activation} \\
    --use-enhanced-agent \\
    --use-feature-interaction \\
    --interaction-layers {interaction_layers} \\
    --alpha {alpha} \\
    --beta {beta} \\
    --gamma {gamma} \\
    --accuracy-scale {accuracy_scale} \\
    --lr {lr} \\
    --weight-decay {weight_decay} \\
    --batch-size {batch_size} \\
    --epochs 300 \\
    --early-stopping-patience 30 \\
    --entropy-coef 0.01 \\
    --value-loss-coef 0.5 \\
    --max-grad-norm 1.0 \\
    --seed 42 \\
    --feature-selection \\
    --n-features {n_features}
"""

# Create the best model script with the best parameters
best_model_script_formatted = best_model_script.format(**best_params)
best_model_script_path = base_dir / "run_best_enhanced_model.sh"
with open(best_model_script_path, 'w') as f:
    f.write(best_model_script_formatted)

# Make the script executable
os.chmod(best_model_script_path, 0o755)

print(f"Best model script saved to {best_model_script_path}")
print(f"Run it with: bash {best_model_script_path}")
