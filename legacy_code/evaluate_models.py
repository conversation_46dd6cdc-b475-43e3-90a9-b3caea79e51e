#!/usr/bin/env python3
"""
Script to demonstrate model comparison with synthetic data
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from scipy.stats import spearmanr, pearsonr
import json
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import train_test_split
import argparse
import torch
import joblib
from typing import Dict, List, Tuple, Union

# Setup logging
import logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('evaluate_models')

def load_data(feature_file: str, metadata_file: str) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    Load and prepare data for evaluation.
    
    Args:
        feature_file: Path to feature file
        metadata_file: Path to metadata file
        
    Returns:
        Tuple of (features, metadata)
    """
    logger.info("Loading data...")
    features = pd.read_csv(feature_file, sep='\t')
    metadata = pd.read_csv(metadata_file, sep='\t')
    
    # Merge features and metadata
    data = pd.merge(features, metadata, on='genome_id', how='inner')
    
    return features, metadata

def load_model(model_path: str) -> object:
    """
    Load a model from file.
    
    Args:
        model_path: Path to model file
        
    Returns:
        Loaded model
    """
    if model_path.endswith('.pkl'):
        return joblib.load(model_path)
    elif model_path.endswith('.pth'):
        # Load PyTorch model
        model = torch.load(model_path)
        model.eval()
        return model
    else:
        raise ValueError(f"Unsupported model file format: {model_path}")

def evaluate_model(model: object, X: Union[pd.DataFrame, np.ndarray], 
                  y: Union[pd.Series, np.ndarray]) -> Dict[str, float]:
    """
    Evaluate model performance.
    
    Args:
        model: Model to evaluate
        X: Input features
        y: True target values
        
    Returns:
        Dictionary of evaluation metrics
    """
    if hasattr(model, 'predict'):
        y_pred = model.predict(X)
    else:
        # Handle PyTorch models
        model.eval()
        with torch.no_grad():
            if isinstance(X, pd.DataFrame):
                X = torch.FloatTensor(X.values)
            elif isinstance(X, np.ndarray):
                X = torch.FloatTensor(X)
            y_pred = model(X).numpy()
    
    metrics = {
        'R2': r2_score(y, y_pred),
        'RMSE': np.sqrt(mean_squared_error(y, y_pred)),
        'MAE': mean_absolute_error(y, y_pred)
    }
    
    return metrics, y_pred

def plot_model_comparison(results: Dict[str, Dict[str, float]], 
                         output_dir: str) -> None:
    """
    Create comparison plots for all models.
    
    Args:
        results: Dictionary of model results
        output_dir: Directory to save plots
    """
    # Create comparison plots
    plt.figure(figsize=(15, 10))
    
    # Plot R2 comparison
    plt.subplot(2, 2, 1)
    models = list(results['temperature'].keys())
    temp_r2 = [results['temperature'][m]['R2'] for m in models]
    growth_r2 = [results['growth_rate'][m]['R2'] for m in models]
    
    x = np.arange(len(models))
    width = 0.35
    
    plt.bar(x - width/2, temp_r2, width, label='Temperature')
    plt.bar(x + width/2, growth_r2, width, label='Growth Rate')
    
    plt.xlabel('Model Type')
    plt.ylabel('R² Score')
    plt.title('R² Comparison')
    plt.xticks(x, models, rotation=45)
    plt.legend()
    
    # Plot RMSE comparison
    plt.subplot(2, 2, 2)
    temp_rmse = [results['temperature'][m]['RMSE'] for m in models]
    growth_rmse = [results['growth_rate'][m]['RMSE'] for m in models]
    
    plt.bar(x - width/2, temp_rmse, width, label='Temperature')
    plt.bar(x + width/2, growth_rmse, width, label='Growth Rate')
    
    plt.xlabel('Model Type')
    plt.ylabel('RMSE')
    plt.title('RMSE Comparison')
    plt.xticks(x, models, rotation=45)
    plt.legend()
    
    # Plot MAE comparison
    plt.subplot(2, 2, 3)
    temp_mae = [results['temperature'][m]['MAE'] for m in models]
    growth_mae = [results['growth_rate'][m]['MAE'] for m in models]
    
    plt.bar(x - width/2, temp_mae, width, label='Temperature')
    plt.bar(x + width/2, growth_mae, width, label='Growth Rate')
    
    plt.xlabel('Model Type')
    plt.ylabel('MAE')
    plt.title('MAE Comparison')
    plt.xticks(x, models, rotation=45)
    plt.legend()
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'model_comparison.png'))
    plt.close()

def generate_summary_report(results: Dict[str, Dict[str, float]], 
                          output_dir: str) -> None:
    """
    Generate summary report of model performance.
    
    Args:
        results: Dictionary of model results
        output_dir: Directory to save report
    """
    # Create summary tables
    temp_df = pd.DataFrame(results['temperature']).T
    growth_df = pd.DataFrame(results['growth_rate']).T
    
    # Save tables
    temp_df.to_csv(os.path.join(output_dir, 'temperature_metrics.csv'))
    growth_df.to_csv(os.path.join(output_dir, 'growth_rate_metrics.csv'))
    
    # Generate summary report
    with open(os.path.join(output_dir, 'summary_report.txt'), 'w') as f:
        f.write('# DeepMu Model Evaluation Summary\n\n')
        
        # Temperature results
        f.write('## Temperature Prediction\n\n')
        f.write(temp_df.to_string())
        f.write('\n\n')
        
        # Growth rate results
        f.write('## Growth Rate Prediction\n\n')
        f.write(growth_df.to_string())
        f.write('\n\n')
        
        # Best models
        f.write('## Best Models\n\n')
        
        # Temperature
        best_temp = max(results['temperature'].items(), 
                       key=lambda x: x[1]['R2'])
        f.write(f'Best Temperature Model: {best_temp[0]}\n')
        f.write(f'R²: {best_temp[1]["R2"]:.4f}\n')
        f.write(f'RMSE: {best_temp[1]["RMSE"]:.4f}\n')
        f.write(f'MAE: {best_temp[1]["MAE"]:.4f}\n\n')
        
        # Growth rate
        best_growth = max(results['growth_rate'].items(), 
                         key=lambda x: x[1]['R2'])
        f.write(f'Best Growth Rate Model: {best_growth[0]}\n')
        f.write(f'R²: {best_growth[1]["R2"]:.4f}\n')
        f.write(f'RMSE: {best_growth[1]["RMSE"]:.4f}\n')
        f.write(f'MAE: {best_growth[1]["MAE"]:.4f}\n')

def evaluate_models(args: argparse.Namespace) -> None:
    """
    Evaluate all models and generate comparison reports.
    
    Args:
        args: Command line arguments
    """
    # Load data
    features, metadata = load_data(args.feature_file, args.metadata_file)
    
    # Split data
    X_train, X_test, y_train, y_test = train_test_split(
        features, metadata, test_size=0.2, random_state=42
    )
    
    # Load models
    logger.info("Loading models...")
    models = {
        'RF Temperature': load_model(args.rf_temp_model),
        'RF Growth Rate': load_model(args.rf_growth_model),
        'DNN Temperature': load_model(args.dnn_temp_model),
        'DNN Growth Rate': load_model(args.dnn_growth_model),
        'Ensemble Temperature': load_model(args.ensemble_temp_model),
        'Ensemble Growth Rate': load_model(args.ensemble_growth_model)
    }
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Evaluate models
    logger.info("Evaluating models...")
    results = {
        'temperature': {},
        'growth_rate': {}
    }
    
    # Temperature models
    for name, model in [('RF Temperature', models['RF Temperature']),
                       ('DNN Temperature', models['DNN Temperature']),
                       ('Ensemble Temperature', models['Ensemble Temperature'])]:
        metrics, _ = evaluate_model(model, X_test, y_test['optimal_temperature'])
        results['temperature'][name] = metrics
    
    # Growth rate models
    for name, model in [('RF Growth Rate', models['RF Growth Rate']),
                       ('DNN Growth Rate', models['DNN Growth Rate']),
                       ('Ensemble Growth Rate', models['Ensemble Growth Rate'])]:
        metrics, _ = evaluate_model(model, X_test, y_test['growth_rate'])
        results['growth_rate'][name] = metrics
    
    # Generate plots and reports
    logger.info("Generating comparison plots and reports...")
    plot_model_comparison(results, args.output_dir)
    generate_summary_report(results, args.output_dir)
    
    logger.info("Evaluation complete!")

def main():
    parser = argparse.ArgumentParser(description='Evaluate DeepMu models')
    
    # Input files
    parser.add_argument('--feature_file', type=str, required=True,
                      help='Path to feature file')
    parser.add_argument('--metadata_file', type=str, required=True,
                      help='Path to metadata file')
    
    # Model paths
    parser.add_argument('--rf_temp_model', type=str, required=True,
                      help='Path to Random Forest temperature model')
    parser.add_argument('--rf_growth_model', type=str, required=True,
                      help='Path to Random Forest growth rate model')
    parser.add_argument('--dnn_temp_model', type=str, required=True,
                      help='Path to DNN temperature model')
    parser.add_argument('--dnn_growth_model', type=str, required=True,
                      help='Path to DNN growth rate model')
    parser.add_argument('--ensemble_temp_model', type=str, required=True,
                      help='Path to ensemble temperature model')
    parser.add_argument('--ensemble_growth_model', type=str, required=True,
                      help='Path to ensemble growth rate model')
    
    # Output settings
    parser.add_argument('--output_dir', type=str, required=True,
                      help='Directory to save evaluation results')
    parser.add_argument('--verbose', action='store_true',
                      help='Enable verbose output')
    
    args = parser.parse_args()
    
    if args.verbose:
        logger.setLevel(logging.DEBUG)
    
    evaluate_models(args)

if __name__ == '__main__':
    main() 