#!/bin/bash

METADATA="./training_data/metadata10.tsv"

DeepMu feature batch --metadata ${METADATA} --genome-dir ./training_data/genomes/ --cds-dir ./training_data/cds_ffn --faa-dir ./training_data/cds_faa --ko-dir ./training_data/kegg/ko_files/ --trna-dir ./training_data/rna_files/ --rrna-dir ./training_data/rna_files/ --output-dir features --combine --output-tsv ./features/combined_features.tsv --kegg-map ./training_data/kegg/pathway_mapping.txt --heg-ko-list ./training_data/kegg/heg_ko_list.txt --advanced-codon-features --num-processes 4
