#!/bin/bash

# DeepMu: Final Hybrid Model Training Script with Robust Outlier Detection
# This script first identifies outliers in the dataset and then trains the model
# on clean data for optimal performance across different dataset sizes.
# IMPROVED VERSION FOR TEMPERATURE PREDICTION

# Set a clean output directory
OUTPUT_DIR="models/deepmu_improved_temp_model"

# Create output directory
mkdir -p "$OUTPUT_DIR"

# Define color codes for output formatting
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Display initial message
echo -e "${BLUE}${BOLD}========================================================${NC}"
echo -e "${BLUE}${BOLD}   DeepMu - Final Hybrid Model with Improved Temperature Model   ${NC}"
echo -e "${BLUE}${BOLD}========================================================${NC}"
echo -e "${GREEN}This script uses the improved model configuration:${NC}"
echo -e "  - Temperature model: ${YELLOW}Enhanced DNN with residual connections and GELU activation${NC}"
echo -e "  - Temperature transformation: ${YELLOW}none${NC}"
echo -e "  - Growth rate transformation: ${YELLOW}log2${NC}"
echo -e "  - Outlier detection and removal${NC}"
echo -e ""
echo -e "${YELLOW}The script will:${NC}"
echo -e "  1. Identify and filter outliers from the dataset"
echo -e "  2. Train a model with improved architecture for temperature prediction"
echo -e "  3. Apply appropriate data transformations (none for temperature, log2 for growth)"
echo -e ""
echo -e "${YELLOW}Output will be saved to:${NC} ${OUTPUT_DIR}"
echo ""

# Check if prerequisites are available
echo -e "${YELLOW}Checking prerequisites...${NC}"
if [ ! -f "./training_data/combined_features.tsv" ]; then
    echo -e "${RED}Error: Feature file not found. Please ensure './training_data/combined_features.tsv' exists.${NC}"
    exit 1
fi

if [ ! -f "./training_data/metadata.tsv" ]; then
    echo -e "${RED}Error: Metadata file not found. Please ensure './training_data/metadata.tsv' exists.${NC}"
    exit 1
fi

echo -e "${GREEN}All prerequisites found.${NC}"
echo ""

# Detect dataset size and adapt parameters
echo -e "${YELLOW}Checking dataset size...${NC}"
DATASET_SIZE=$(wc -l < "./training_data/combined_features.tsv")
DATASET_SIZE=$((DATASET_SIZE - 1))  # Subtract header line

echo -e "Detected ${BOLD}${DATASET_SIZE}${NC} genomes in dataset"

# Adjust parameters based on dataset size
if [ $DATASET_SIZE -lt 3000 ]; then
    echo -e "${YELLOW}Small dataset detected (< 3000 genomes). Optimizing parameters for smaller dataset with stable training...${NC}"
    # Reduced L1 regularization for temperature model to prevent underfitting
    L1_LAMBDA="5e-6"  
    # Reduced weight decay to allow more flexibility in the model
    WEIGHT_DECAY="5e-6"  
    # Deeper architecture with more capacity
    HIDDEN_DIMS="512,512,512,384,256,128"  
    # Reduced dropout rates to prevent underfitting
    DROPOUT_RATES="0.2,0.25,0.3,0.35,0.4,0.45"  
    MAX_EPOCHS=600  # More epochs for complete training
    PATIENCE=50    # Increased patience for more stable convergence
    BATCH_SIZE=32   # Smaller batch size for better generalization
    
    # Improved parameters for temperature model
    EXTRA_PARAMS="--activation gelu --use_batch_norm --use_one_cycle_lr --use_residual"
    
    # Increase the number of features for temperature
    N_GROWTH_FEATURES=150
    N_TEMP_FEATURES=500  # More features for temperature may help with complex patterns
elif [ $DATASET_SIZE -gt 5000 ]; then
    echo -e "${YELLOW}Large dataset detected (> 5000 genomes). Optimizing parameters for larger dataset with stable training...${NC}"
    # Reduced L1 regularization for temperature model
    L1_LAMBDA="5e-6"  
    # Reduced weight decay
    WEIGHT_DECAY="5e-6"  
    # Deeper architecture with more capacity
    HIDDEN_DIMS="768,768,512,512,384,256"  
    # Adjusted dropout rates
    DROPOUT_RATES="0.2,0.25,0.3,0.35,0.4,0.45"  
    MAX_EPOCHS=500  # Fewer epochs for larger dataset
    PATIENCE=40     # Higher patience
    BATCH_SIZE=64   # Larger batch size for efficiency with large dataset
    
    # Improved parameters for temperature model
    EXTRA_PARAMS="--activation gelu --use_batch_norm --use_one_cycle_lr --use_residual"
    
    # Update the number of features
    N_GROWTH_FEATURES=250
    N_TEMP_FEATURES=600  # More features for temperature with large dataset
else
    echo -e "${YELLOW}Medium dataset detected. Using optimized architecture with enhanced regularization...${NC}"
    # Reduced L1 regularization for temperature model
    L1_LAMBDA="5e-6"  
    # Reduced weight decay
    WEIGHT_DECAY="5e-6"  
    # Deeper architecture with more capacity
    HIDDEN_DIMS="640,640,512,384,256,128"  
    # Adjusted dropout rates
    DROPOUT_RATES="0.2,0.25,0.3,0.35,0.4,0.45"  
    MAX_EPOCHS=550  # Balanced number of epochs
    PATIENCE=45     # Balanced patience
    BATCH_SIZE=48   # Balanced batch size
    
    # Improved parameters for temperature model
    EXTRA_PARAMS="--activation gelu --use_batch_norm --use_one_cycle_lr --use_residual"
    
    # Update the number of features
    N_GROWTH_FEATURES=200
    N_TEMP_FEATURES=550  # More features for temperature
fi

# Run outlier detection as a separate step to generate cleaned datasets
echo -e "${YELLOW}Running outlier detection with transformations and Z-score thresholds...${NC}"
python detect_outliers.py \
    --feature_file "./training_data/combined_features.tsv" \
    --metadata_file "./training_data/metadata.tsv" \
    --output_dir "$OUTPUT_DIR" \
    --zscore_threshold $([ $DATASET_SIZE -lt 3000 ] && echo "6.0" || echo "7.0") \
    --max_removal_pct $([ $DATASET_SIZE -lt 3000 ] && echo "15.0" || echo "20.0") \
    --visualize_outliers

# Check if outlier detection was successful
if [ $? -eq 0 ]; then
    echo -e "${GREEN}Outlier detection completed. Using cleaned datasets for training.${NC}"
    FEATURES_FILE="${OUTPUT_DIR}/cleaned_features.tsv"
    METADATA_FILE="${OUTPUT_DIR}/cleaned_metadata.tsv"
else
    echo -e "${YELLOW}Warning: Outlier detection failed or no significant outliers found. Using original datasets.${NC}"
    FEATURES_FILE="./training_data/combined_features.tsv"
    METADATA_FILE="./training_data/metadata.tsv"
fi

# Train with fixed transformations: none for temperature, log2 for growth rate
echo -e "\n${BLUE}${BOLD}========================================================${NC}"
echo -e "${BLUE}${BOLD}   Training Final Model with Improved Temperature Model   ${NC}"
echo -e "${BLUE}${BOLD}   Temperature: none, Growth Rate: log2   ${NC}"
echo -e "${BLUE}${BOLD}========================================================${NC}"

# Set environment variables for deterministic behavior
export PYTHONHASHSEED=42
export TORCH_CUDNN_DETERMINISTIC=1
export TORCH_DETERMINISTIC=1
export CUBLAS_WORKSPACE_CONFIG=:4096:8

# Run the Python script with optimized parameters
python final_hybrid_model.py \
    --feature_file "$FEATURES_FILE" \
    --metadata_file "$METADATA_FILE" \
    --output_dir "$OUTPUT_DIR" \
    --n_growth_features $N_GROWTH_FEATURES \
    --n_temp_features $N_TEMP_FEATURES \
    --seed 42 \
    --lr 0.001 \
    --batch_size $BATCH_SIZE \
    --patience $PATIENCE \
    --epochs $MAX_EPOCHS \
    --l1_lambda $L1_LAMBDA \
    --weight_decay $WEIGHT_DECAY \
    --mse_l1_ratio 0.9 \
    --hidden_dims "$HIDDEN_DIMS" \
    --dropout_rates "$DROPOUT_RATES" \
    --activation "gelu" \
    --use_batch_norm \
    --use_residual \
    --save_checkpoints \
    --missing_threshold 0.5 \
    --temp_transform "none" \
    --growth_transform "log2" \
    --verbose $EXTRA_PARAMS 2>&1 | tee "${OUTPUT_DIR}/training.log"

# Check if training was successful
if [ $? -eq 0 ]; then
    echo ""
    echo -e "${GREEN}${BOLD}========================================================${NC}"
    echo -e "${GREEN}${BOLD}   Training completed successfully!   ${NC}"
    echo -e "${GREEN}${BOLD}========================================================${NC}"
    echo ""
    echo -e "${GREEN}Model saved to:${NC} ${OUTPUT_DIR}"
    
else
    echo ""
    echo -e "${RED}${BOLD}========================================================${NC}"
    echo -e "${RED}${BOLD}   Training failed!   ${NC}"
    echo -e "${RED}${BOLD}========================================================${NC}"
    echo ""
    echo -e "${RED}Please check the error messages above for details.${NC}"
fi

echo ""
echo -e "${GREEN}Done.${NC}" 
