#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Feature Engineering for Temperature Prediction.

This module implements advanced feature engineering techniques specifically
designed to improve temperature prediction in the DeepMu model.
"""

import numpy as np
import pandas as pd
from sklearn.decomposition import PCA
from sklearn.feature_selection import mutual_info_regression
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
from typing import Tuple, List, Dict, Optional

def create_interaction_features(X: pd.DataFrame, top_k: int = 50) -> pd.DataFrame:
    """
    Create interaction features between the most important features.
    
    Args:
        X: DataFrame containing the original features
        top_k: Number of top features to consider for interactions
        
    Returns:
        DataFrame with original and interaction features
    """
    # Get feature importance using a simple Random Forest
    rf = RandomForestRegressor(n_estimators=50, max_depth=10, random_state=42)
    rf.fit(X, np.random.randn(X.shape[0]))  # Dummy target for feature importance
    
    # Get top k features
    feature_importances = pd.Series(rf.feature_importances_, index=X.columns)
    top_features = feature_importances.nlargest(top_k).index.tolist()
    
    # Create interaction features
    X_interactions = X.copy()
    
    # Pairwise products of top features
    for i in range(len(top_features)):
        for j in range(i+1, len(top_features)):
            feat_i = top_features[i]
            feat_j = top_features[j]
            interaction_name = f"interaction_{feat_i}_{feat_j}"
            X_interactions[interaction_name] = X[feat_i] * X[feat_j]
    
    return X_interactions

def create_nonlinear_features(X: pd.DataFrame, features: Optional[List[str]] = None) -> pd.DataFrame:
    """
    Create nonlinear transformations of features.
    
    Args:
        X: DataFrame containing the original features
        features: List of features to transform (if None, use all features)
        
    Returns:
        DataFrame with original and nonlinear features
    """
    if features is None:
        # Use all features but limit to avoid explosion in dimensionality
        features = X.columns.tolist()[:100]
    
    X_nonlinear = X.copy()
    
    for feat in features:
        # Square
        X_nonlinear[f"{feat}_squared"] = X[feat] ** 2
        
        # Square root (with handling of negative values)
        X_nonlinear[f"{feat}_sqrt"] = np.sqrt(np.abs(X[feat])) * np.sign(X[feat])
        
        # Log transformation (with handling of zero/negative values)
        X_nonlinear[f"{feat}_log"] = np.log1p(X[feat] - X[feat].min() + 1e-5)
        
        # Exponential
        scaled_feat = (X[feat] - X[feat].mean()) / (X[feat].std() + 1e-8)
        X_nonlinear[f"{feat}_exp"] = np.exp(np.clip(scaled_feat, -5, 5))
    
    return X_nonlinear

def create_pca_features(X: pd.DataFrame, n_components: int = 50) -> pd.DataFrame:
    """
    Create PCA features from the original feature set.
    
    Args:
        X: DataFrame containing the original features
        n_components: Number of PCA components to create
        
    Returns:
        DataFrame with original and PCA features
    """
    # Standardize the data
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    # Apply PCA
    pca = PCA(n_components=min(n_components, X.shape[1], X.shape[0]))
    pca_features = pca.fit_transform(X_scaled)
    
    # Create DataFrame with PCA features
    pca_df = pd.DataFrame(
        pca_features,
        index=X.index,
        columns=[f"pca_{i}" for i in range(pca_features.shape[1])]
    )
    
    # Combine with original features
    X_with_pca = pd.concat([X, pca_df], axis=1)
    
    return X_with_pca

def create_domain_specific_features(X: pd.DataFrame, metadata_df: pd.DataFrame) -> pd.DataFrame:
    """
    Create domain-specific features for temperature prediction based on biological knowledge.
    
    Args:
        X: DataFrame containing the original features
        metadata_df: DataFrame containing metadata
        
    Returns:
        DataFrame with original and domain-specific features
    """
    X_domain = X.copy()
    
    # Add kingdom information as one-hot encoded features
    if 'kingdom' in metadata_df.columns:
        kingdom_dummies = pd.get_dummies(metadata_df['kingdom'], prefix='kingdom')
        X_domain = pd.concat([X_domain, kingdom_dummies.loc[X_domain.index]], axis=1)
    
    # Create features related to GC content (if available)
    gc_related_cols = [col for col in X.columns if 'gc_content' in col.lower()]
    if gc_related_cols:
        # GC content is correlated with optimal growth temperature
        for col in gc_related_cols:
            X_domain[f"{col}_squared"] = X[col] ** 2
    
    # Create features related to amino acid composition (if available)
    aa_cols = [col for col in X.columns if any(aa in col.lower() for aa in 
                                              ['alanine', 'arginine', 'asparagine', 'aspartic',
                                               'cysteine', 'glutamine', 'glutamic', 'glycine',
                                               'histidine', 'isoleucine', 'leucine', 'lysine',
                                               'methionine', 'phenylalanine', 'proline', 'serine',
                                               'threonine', 'tryptophan', 'tyrosine', 'valine'])]
    
    if aa_cols:
        # Thermophiles tend to have different amino acid compositions
        # Create ratios of certain amino acids that are known to be important for thermostability
        charged_aa = [col for col in aa_cols if any(aa in col.lower() for aa in 
                                                  ['arginine', 'lysine', 'aspartic', 'glutamic'])]
        
        hydrophobic_aa = [col for col in aa_cols if any(aa in col.lower() for aa in 
                                                      ['alanine', 'isoleucine', 'leucine', 
                                                       'phenylalanine', 'valine'])]
        
        if charged_aa and hydrophobic_aa:
            # Calculate average values for charged and hydrophobic amino acids
            X_domain['charged_aa_avg'] = X[charged_aa].mean(axis=1)
            X_domain['hydrophobic_aa_avg'] = X[hydrophobic_aa].mean(axis=1)
            
            # Calculate ratio of charged to hydrophobic amino acids
            X_domain['charged_hydrophobic_ratio'] = X_domain['charged_aa_avg'] / (X_domain['hydrophobic_aa_avg'] + 1e-8)
    
    return X_domain

def engineer_temperature_features(
    X: pd.DataFrame, 
    metadata_df: pd.DataFrame,
    use_interactions: bool = True,
    use_nonlinear: bool = True,
    use_pca: bool = True,
    use_domain: bool = True,
    n_pca_components: int = 50,
    top_k_interactions: int = 50
) -> pd.DataFrame:
    """
    Apply comprehensive feature engineering for temperature prediction.
    
    Args:
        X: DataFrame containing the original features
        metadata_df: DataFrame containing metadata
        use_interactions: Whether to create interaction features
        use_nonlinear: Whether to create nonlinear features
        use_pca: Whether to create PCA features
        use_domain: Whether to create domain-specific features
        n_pca_components: Number of PCA components to create
        top_k_interactions: Number of top features to consider for interactions
        
    Returns:
        DataFrame with engineered features
    """
    X_engineered = X.copy()
    
    # Apply feature engineering techniques
    if use_domain:
        X_engineered = create_domain_specific_features(X_engineered, metadata_df)
    
    if use_nonlinear:
        # Select top features for nonlinear transformations to avoid dimensionality explosion
        rf = RandomForestRegressor(n_estimators=50, max_depth=10, random_state=42)
        rf.fit(X, metadata_df.loc[X.index, 'optimal_temperature'])
        feature_importances = pd.Series(rf.feature_importances_, index=X.columns)
        top_features = feature_importances.nlargest(100).index.tolist()
        
        X_engineered = create_nonlinear_features(X_engineered, features=top_features)
    
    if use_interactions:
        X_engineered = create_interaction_features(X_engineered, top_k=top_k_interactions)
    
    if use_pca:
        X_engineered = create_pca_features(X_engineered, n_components=n_pca_components)
    
    return X_engineered
