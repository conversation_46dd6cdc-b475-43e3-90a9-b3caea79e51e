import os
import logging
import json
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from sklearn.ensemble import RandomForestRegressor
from sklearn.feature_selection import SelectFromModel, mutual_info_regression
import joblib
import argparse
from typing import Dict, List, Tuple, Optional, Union
import warnings
import math
import torch.nn.functional as F

# Import the CNN+DNN model for temperature prediction
from cnn_dnn_temp_model import CNNDNNTemperatureModel

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

class CyclicLR(torch.optim.lr_scheduler._LRScheduler):
    """Cyclical learning rate scheduler.
    
    Based on the paper "Cyclical Learning Rates for Training Neural Networks"
    by <PERSON>.
    """
    
    def __init__(self, optimizer, base_lr, max_lr, step_size_up=2000, step_size_down=None, 
                 mode='triangular', gamma=1.0, scale_fn=None, scale_mode='cycle', cycle_momentum=False,
                 base_momentum=0.8, max_momentum=0.9, last_epoch=-1):
        if not isinstance(optimizer, torch.optim.Optimizer):
            raise TypeError(f'{type(optimizer).__name__} is not an Optimizer')
        self.optimizer = optimizer

        base_lrs = self._format_param('base_lr', optimizer, base_lr)
        if last_epoch == -1:
            for lr, group in zip(base_lrs, optimizer.param_groups):
                group['lr'] = lr

        self.max_lrs = self._format_param('max_lr', optimizer, max_lr)

        step_size_up = float(step_size_up)
        step_size_down = float(step_size_down) if step_size_down is not None else step_size_up
        self.total_size = step_size_up + step_size_down
        self.step_ratio = step_size_up / self.total_size

        if mode not in ['triangular', 'triangular2', 'exp_range'] \
                and scale_fn is None:
            raise ValueError('mode is invalid and scale_fn is None')

        self.mode = mode
        self.gamma = gamma

        if scale_fn is None:
            if self.mode == 'triangular':
                self.scale_fn = self._triangular_scale_fn
                self.scale_mode = 'cycle'
            elif self.mode == 'triangular2':
                self.scale_fn = self._triangular2_scale_fn
                self.scale_mode = 'cycle'
            elif self.mode == 'exp_range':
                self.scale_fn = self._exp_range_scale_fn
                self.scale_mode = 'iterations'
        else:
            self.scale_fn = scale_fn
            self.scale_mode = scale_mode

        self.cycle_momentum = cycle_momentum
        if cycle_momentum:
            if 'momentum' not in optimizer.defaults and not hasattr(optimizer, 'param_groups'):
                self.cycle_momentum = False
                print("Warning: cycle_momentum is set but optimizer doesn't support momentum. Setting cycle_momentum=False")
            else:
                self.cycle_momentum = True
                base_momentums = self._format_param('base_momentum', optimizer, base_momentum)
                if last_epoch == -1:
                    for momentum, group in zip(base_momentums, optimizer.param_groups):
                        if 'momentum' in group:
                            group['momentum'] = momentum
                if hasattr(optimizer, 'param_groups') and optimizer.param_groups and 'momentum' in optimizer.param_groups[0]:
                    self.base_momentums = [group['momentum'] for group in optimizer.param_groups]
                    self.max_momentums = self._format_param('max_momentum', optimizer, max_momentum)
                else:
                    self.cycle_momentum = False
                    print("Warning: cycle_momentum is set but optimizer doesn't have momentum parameter. Setting cycle_momentum=False")

        super(CyclicLR, self).__init__(optimizer, last_epoch)

    def _format_param(self, name, optimizer, param):
        """Return correctly formatted lr/momentum for each param group."""
        if isinstance(param, (list, tuple)):
            if len(param) != len(optimizer.param_groups):
                raise ValueError(f"expected {len(optimizer.param_groups)} values for {name}, got {len(param)}")
            return param
        else:
            return [param] * len(optimizer.param_groups)

    def _triangular_scale_fn(self, x):
        return 1.0

    def _triangular2_scale_fn(self, x):
        return 1 / (2. ** (x - 1))

    def _exp_range_scale_fn(self, x):
        return self.gamma ** x

    def get_lr(self):
        """Calculates the learning rate at batch index. This function treats
        `self.last_epoch` as the last batch index.
        """
        cycle = math.floor(1 + self.last_epoch / self.total_size)
        x = 1. + self.last_epoch / self.total_size - cycle
        if x <= self.step_ratio:
            scale_factor = x / self.step_ratio
        else:
            scale_factor = (x - 1) / (self.step_ratio - 1)

        lrs = []
        for base_lr, max_lr in zip(self.base_lrs, self.max_lrs):
            base_height = (max_lr - base_lr) * scale_factor
            if self.scale_mode == 'cycle':
                lr = base_lr + base_height * self.scale_fn(cycle)
            else:
                lr = base_lr + base_height * self.scale_fn(self.last_epoch)
            lrs.append(lr)

        return lrs

    def get_momentum(self):
        """Calculates the momentum at batch index. This function treats
        `self.last_epoch` as the last batch index.
        """
        if not self.cycle_momentum:
            return None

        cycle = math.floor(1 + self.last_epoch / self.total_size)
        x = 1. + self.last_epoch / self.total_size - cycle
        if x <= self.step_ratio:
            scale_factor = x / self.step_ratio
        else:
            scale_factor = (x - 1) / (self.step_ratio - 1)

        momentums = []
        for base_momentum, max_momentum in zip(self.base_momentums, self.max_momentums):
            diff = max_momentum - base_momentum
            if self.scale_mode == 'cycle':
                momentum = max_momentum - diff * self.scale_fn(cycle) * scale_factor
            else:
                momentum = max_momentum - diff * self.scale_fn(self.last_epoch) * scale_factor
            momentums.append(momentum)

        return momentums

    def step(self, epoch=None):
        """Update parameters and rate"""
        if epoch is None:
            epoch = self.last_epoch + 1
        self.last_epoch = epoch
        for param_group, lr in zip(self.optimizer.param_groups, self.get_lr()):
            param_group['lr'] = lr

        if self.cycle_momentum:
            momentums = self.get_momentum()
            if momentums is not None:
                for param_group, momentum in zip(self.optimizer.param_groups, momentums):
                    if 'momentum' in param_group:
                        param_group['momentum'] = momentum

class GrowthRateModel(nn.Module):
    """Deep Neural Network model for growth rate prediction."""
    
    def __init__(self, input_dim: int, hidden_dims: Tuple[int, ...] = (128, 64, 32)):
        """Initialize the growth rate model.
        
        Args:
            input_dim: Dimension of input features
            hidden_dims: Tuple of hidden dimensions
        """
        super(GrowthRateModel, self).__init__()
        
        # DNN layers
        self.fc_layers = nn.ModuleList()
        
        # First layer
        self.fc_layers.append(nn.Linear(input_dim, hidden_dims[0]))
        
        # Hidden layers
        for i in range(len(hidden_dims) - 1):
            self.fc_layers.append(nn.Linear(hidden_dims[i], hidden_dims[i+1]))
        
        # Output layer
        self.output_layer = nn.Linear(hidden_dims[-1], 1)
        
        # Dropout for regularization
        self.dropout = nn.Dropout(0.2)
        
        # Batch normalization
        self.bn_layers = nn.ModuleList()
        for dim in hidden_dims:
            self.bn_layers.append(nn.BatchNorm1d(dim))
            
    def forward(self, x):
        """Forward pass through the model.
        
        Args:
            x: Input tensor of shape (batch_size, input_dim)
            
        Returns:
            Output tensor of shape (batch_size, 1)
        """
        # Check for NaN values and replace with zeros
        if torch.isnan(x).any():
            x = torch.nan_to_num(x, nan=0.0)
            
        # Forward through hidden layers
        for i, (fc_layer, bn_layer) in enumerate(zip(self.fc_layers, self.bn_layers)):
            x = fc_layer(x)
            
            # Check for NaN values after linear layer
            if torch.isnan(x).any():
                x = torch.nan_to_num(x, nan=0.0)
                
            x = bn_layer(x)
            x = torch.relu(x)
            x = self.dropout(x)
        
        # Output layer
        x = self.output_layer(x)
        
        # Final NaN check
        if torch.isnan(x).any():
            x = torch.nan_to_num(x, nan=0.0)
            
        return x

class HybridModel:
    """Hybrid model combining DNN for growth rate and CNN+DNN for temperature prediction."""
    
    def __init__(self, logger=None):
        """Initialize the hybrid model.
        
        Args:
            logger: Logger instance
        """
        self.growth_model = None
        self.temp_model = None
        self.logger = logger or logging.getLogger(__name__)
        
        # For feature preprocessing
        self.growth_scaler = None
        self.temp_scaler = None
        self.growth_feature_indices = None
        self.temp_feature_indices = None
        
        # For data transformation
        self.growth_transform = None
        self.temp_transform = None
        
        # For output directory
        self.output_dir = None
        
    def _initialize_models(self, growth_input_dim: int, temp_input_dim: int, 
                           growth_hidden_dims: Tuple[int, ...] = (128, 64, 32),
                           temp_hidden_dims: Tuple[int, ...] = (256, 128, 64, 32),
                           cnn_filters: Tuple[int, ...] = (128, 64, 32, 16),
                           kernel_sizes: Tuple[int, ...] = (3, 5, 7, 9),
                           dropout_rate: float = 0.3,
                           l2_reg: float = 0.001,
                           device: str = 'cpu'):
        """Initialize the growth rate and temperature models.
        
        Args:
            growth_input_dim: Dimension of growth rate input features
            temp_input_dim: Dimension of temperature input features
            growth_hidden_dims: Tuple of hidden dimensions for growth rate model
            temp_hidden_dims: Tuple of hidden dimensions for temperature model
            cnn_filters: Tuple of CNN filter numbers for temperature model
            kernel_sizes: Tuple of kernel sizes for CNN layers
            dropout_rate: Dropout rate for model regularization
            l2_reg: L2 regularization strength
            device: Device to run the models on
        """
        # Initialize growth rate model
        self.growth_model = GrowthRateModel(growth_input_dim, growth_hidden_dims).to(device)
        
        # Initialize temperature model with enhanced architecture
        self.temp_model = CNNDNNTemperatureModel(
            temp_input_dim, 
            hidden_dims=temp_hidden_dims,
            cnn_filters=cnn_filters,
            kernel_sizes=kernel_sizes,
            dropout_rate=dropout_rate,
            l2_reg=l2_reg,
            use_residual=True
        ).to(device)
        
        # Initialize weights properly
        self._init_weights(self.growth_model)
        self._init_weights(self.temp_model)
        
    def _init_weights(self, model):
        """Initialize model weights for better training.
        
        Args:
            model: PyTorch model
        """
        for m in model.modules():
            if isinstance(m, nn.Linear):
                nn.init.kaiming_normal_(m.weight, mode='fan_in', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
            elif isinstance(m, nn.Conv1d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
            elif isinstance(m, nn.BatchNorm1d):
                nn.init.ones_(m.weight)
                nn.init.zeros_(m.bias)

    def _select_features(self, X, y, n_features, method='combined'):
        """Select top features based on specified method.
        
        Args:
            X: Feature matrix
            y: Target array
            n_features: Number of features to select
            method: Feature selection method ('correlation', 'mutual_info', 'model_based', 'combined')
            
        Returns:
            Indices of selected features
        """
        # Handle NaN values in input data
        X_clean = np.nan_to_num(X, nan=0.0)
        y_clean = np.nan_to_num(y, nan=0.0)
        
        self.logger.info(f"Using feature selection method: {method}")
        
        if method == 'correlation':
            # Calculate correlation between each feature and the target
            correlations = []
            for i in range(X_clean.shape[1]):
                try:
                    corr = np.abs(np.corrcoef(X_clean[:, i], y_clean)[0, 1])
                    if np.isnan(corr):
                        corr = 0
                except:
                    corr = 0
                correlations.append((i, corr))
            
            # Sort by correlation and select top n_features
            correlations.sort(key=lambda x: x[1], reverse=True)
            selected_indices = [idx for idx, _ in correlations[:n_features]]
            
        elif method == 'mutual_info':
            try:
                # Calculate mutual information between features and target
                mutual_info = mutual_info_regression(X_clean, y_clean)
                indices = np.argsort(mutual_info)[::-1]  # Sort in descending order
                selected_indices = indices[:n_features].tolist()
            except Exception as e:
                self.logger.warning(f"Error in mutual_info method: {e}. Falling back to correlation method.")
                return self._select_features(X, y, n_features, method='correlation')
            
        elif method == 'model_based':
            try:
                # Use a model to select features
                model = RandomForestRegressor(n_estimators=100, random_state=42)
                selector = SelectFromModel(model, max_features=n_features)
                selector.fit(X_clean, y_clean)
                
                # Get selected feature indices
                selected_mask = selector.get_support()
                selected_indices = [i for i, selected in enumerate(selected_mask) if selected]
                
                # If fewer than n_features were selected, add more based on feature importance
                if len(selected_indices) < n_features:
                    model.fit(X_clean, y_clean)
                    importances = model.feature_importances_
                    remaining_indices = [i for i in range(X_clean.shape[1]) if i not in selected_indices]
                    remaining_importances = [(i, importances[i]) for i in remaining_indices]
                    remaining_importances.sort(key=lambda x: x[1], reverse=True)
                    
                    additional_indices = [idx for idx, _ in remaining_importances[:n_features - len(selected_indices)]]
                    selected_indices.extend(additional_indices)
            except Exception as e:
                self.logger.warning(f"Error in model_based method: {e}. Falling back to correlation method.")
                return self._select_features(X, y, n_features, method='correlation')
            
        elif method == 'combined':
            try:
                # Calculate correlation
                correlations = []
                for i in range(X_clean.shape[1]):
                    try:
                        corr = np.abs(np.corrcoef(X_clean[:, i], y_clean)[0, 1])
                        if np.isnan(corr):
                            corr = 0
                    except:
                        corr = 0
                    correlations.append(corr)
                
                # Handle any remaining NaN values in correlations
                correlations = np.nan_to_num(np.array(correlations), nan=0.0)
                
                try:
                    # Calculate mutual information
                    mutual_info = mutual_info_regression(X_clean, y_clean)
                    
                    # Handle any NaN values in mutual_info
                    mutual_info = np.nan_to_num(mutual_info, nan=0.0)
                except Exception as e:
                    self.logger.warning(f"Error calculating mutual info: {e}. Using zeros instead.")
                    mutual_info = np.zeros(X_clean.shape[1])
                
                try:
                    # Get model-based feature importance
                    model = RandomForestRegressor(n_estimators=100, random_state=42)
                    model.fit(X_clean, y_clean)
                    importances = model.feature_importances_
                except Exception as e:
                    self.logger.warning(f"Error in random forest importance: {e}. Using zeros instead.")
                    importances = np.zeros(X_clean.shape[1])
                
                # Normalize scores to [0, 1] range
                corr_max = np.max(correlations)
                mi_max = np.max(mutual_info)
                imp_max = np.max(importances)
                
                corr_norm = correlations / corr_max if corr_max > 0 else np.zeros_like(correlations)
                mi_norm = mutual_info / mi_max if mi_max > 0 else np.zeros_like(mutual_info)
                imp_norm = importances / imp_max if imp_max > 0 else np.zeros_like(importances)
                
                # Combine scores (equal weighting)
                combined_scores = (corr_norm + mi_norm + imp_norm) / 3
                indices = np.argsort(combined_scores)[::-1]  # Sort in descending order
                selected_indices = indices[:n_features].tolist()
            except Exception as e:
                self.logger.warning(f"Error in combined method: {e}. Falling back to correlation method.")
                return self._select_features(X, y, n_features, method='correlation')
        
        else:
            raise ValueError(f"Unknown feature selection method: {method}")
        
        # Check if enough features were selected
        if len(selected_indices) < n_features:
            self.logger.warning(f"Only {len(selected_indices)} features were selected. Adding random features to reach {n_features}.")
            remaining_indices = [i for i in range(X_clean.shape[1]) if i not in selected_indices]
            if remaining_indices:
                np.random.seed(42)
                additional_indices = np.random.choice(remaining_indices, 
                                                     size=min(n_features - len(selected_indices), len(remaining_indices)), 
                                                     replace=False)
                selected_indices.extend(additional_indices)
        
        self.logger.info(f"Selected {len(selected_indices)} features using {method} method.")
        return selected_indices
    
    def _transform(self, y, transform_type):
        """Apply transformation to the target variable.
        
        Args:
            y: Target array
            transform_type: Type of transformation ('none', 'log', 'log2', 'log10', 'sqrt')
            
        Returns:
            Transformed array
        """
        if transform_type == 'none':
            return y
        elif transform_type == 'log':
            return np.log(y)
        elif transform_type == 'log2':
            return np.log2(y)
        elif transform_type == 'log10':
            return np.log10(y)
        elif transform_type == 'sqrt':
            return np.sqrt(y)
        else:
            raise ValueError(f"Unknown transformation: {transform_type}")
    
    def _inverse_transform(self, y, transform_type):
        """Apply inverse transformation to the target variable.
        
        Args:
            y: Transformed array
            transform_type: Type of transformation ('none', 'log', 'log2', 'log10', 'sqrt')
            
        Returns:
            Original array
        """
        if transform_type == 'none':
            return y
        elif transform_type == 'log':
            return np.exp(y)
        elif transform_type == 'log2':
            return np.power(2, y)
        elif transform_type == 'log10':
            return np.power(10, y)
        elif transform_type == 'sqrt':
            return np.power(y, 2)
        else:
            raise ValueError(f"Unknown transformation: {transform_type}")
            
    def _create_temperature_curriculum(self, X_train_temp_scaled, y_temp_train, batch_size, num_phases=5):
        """Create a curriculum for temperature prediction training.
        
        Creates a more balanced curriculum that ensures representation across the entire temperature range,
        gradually increasing the difficulty level by expanding the temperature range in each phase.
        
        Args:
            X_train_temp_scaled: Scaled temperature features
            y_temp_train: Temperature target values
            batch_size: Batch size for training
            num_phases: Number of curriculum phases
            
        Returns:
            List of DataLoader objects for each curriculum phase
        """
        # Convert to numpy arrays if needed
        if torch.is_tensor(y_temp_train):
            y_temp_train = y_temp_train.cpu().numpy()
        elif hasattr(y_temp_train, 'values'):
            y_temp_train = y_temp_train.values
        
        # Find temperature range
        temp_min = np.min(y_temp_train)
        temp_max = np.max(y_temp_train)
        temp_range = temp_max - temp_min
        
        self.logger.info(f"Temperature range: {temp_min:.2f} to {temp_max:.2f}")
        
        # Create temperature bins for more balanced representation
        temp_bins = np.linspace(temp_min, temp_max, 10)  # 10 temperature bins
        
        # Create curriculum phases
        curriculum_loaders = []
        
        for phase in range(num_phases):
            # Define temperature range for this phase
            # Start with moderate temps (middle 20%) and gradually expand to full range
            if phase == 0:
                # First phase: middle 20% of temperature range
                mid_point = (temp_max + temp_min) / 2
                phase_range = temp_range * 0.2
                phase_min = mid_point - phase_range / 2
                phase_max = mid_point + phase_range / 2
            else:
                # Gradually expand the range with each phase
                expansion_ratio = (phase + 1) / num_phases
                phase_range = temp_range * min(1.0, expansion_ratio * 1.2)  # Slightly faster expansion
                phase_min = mid_point - phase_range / 2
                phase_max = mid_point + phase_range / 2
                
                # Ensure we cover the full range in the final phase
                if phase == num_phases - 1:
                    phase_min = temp_min
                    phase_max = temp_max
            
            # Find indices of samples within this phase's temperature range
            phase_indices = np.where((y_temp_train >= phase_min) & (y_temp_train <= phase_max))[0]
            
            # If we don't have enough samples, expand range slightly
            while len(phase_indices) < batch_size * 3:  # Ensure at least 3 batches worth of samples
                phase_min = max(temp_min, phase_min - temp_range * 0.05)
                phase_max = min(temp_max, phase_max + temp_range * 0.05)
                phase_indices = np.where((y_temp_train >= phase_min) & (y_temp_train <= phase_max))[0]
                if phase_min <= temp_min and phase_max >= temp_max:
                    break  # We've covered the full range, can't expand further
            
            # Important: To prevent catastrophic forgetting, in later phases, include a balanced
            # sample from previous phases in addition to the new samples
            if phase > 0:
                # Calculate how many samples to take from each bin
                samples_per_bin = max(10, min(100, len(phase_indices) // 10))
                
                # Add samples from across the temperature range to prevent forgetting
                balanced_indices = []
                for i in range(len(temp_bins) - 1):
                    bin_min, bin_max = temp_bins[i], temp_bins[i+1]
                    bin_indices = np.where((y_temp_train >= bin_min) & (y_temp_train <= bin_max))[0]
                    if len(bin_indices) > 0:
                        # Take a sample from this bin (random selection if more than samples_per_bin available)
                        if len(bin_indices) > samples_per_bin:
                            selected = np.random.choice(bin_indices, samples_per_bin, replace=False)
                        else:
                            selected = bin_indices
                        balanced_indices.extend(selected)
                
                # Combine balanced indices with phase indices
                combined_indices = np.union1d(phase_indices, balanced_indices)
                phase_indices = combined_indices
            
            # Shuffle the indices
            np.random.shuffle(phase_indices)
            
            self.logger.info(f"Curriculum phase {phase+1}: {len(phase_indices)} samples, "
                           f"temperature range {phase_min:.2f}-{phase_max:.2f}")
            
            # Create tensor dataset for this phase
            phase_X = torch.FloatTensor(X_train_temp_scaled[phase_indices])
            phase_y = torch.FloatTensor(y_temp_train[phase_indices].reshape(-1, 1))
            phase_dataset = TensorDataset(phase_X, phase_y)
            
            # Create data loader
            phase_loader = DataLoader(phase_dataset, batch_size=batch_size, shuffle=True)
            curriculum_loaders.append(phase_loader)
        
        return curriculum_loaders

    def _calculate_temp_weights(self, y_batch, temp_min, temp_max, temp_mean):
        """Calculate sample weights based on temperature values.
        
        Assigns higher weights to extreme temperatures (far from mean)
        to encourage the model to better predict the full range.
        
        Args:
            y_batch: Batch of temperature values (tensor)
            temp_min: Minimum temperature in dataset
            temp_max: Maximum temperature in dataset
            temp_mean: Mean temperature in dataset
            
        Returns:
            Tensor of weights for each sample
        """
        # Convert to numpy if needed
        if torch.is_tensor(y_batch):
            y_numpy = y_batch.cpu().detach().numpy().flatten()
        else:
            y_numpy = y_batch.flatten()
        
        # Calculate normalized distance from mean
        dist_from_mean = np.abs(y_numpy - temp_mean)
        max_possible_dist = max(temp_max - temp_mean, temp_mean - temp_min)
        normalized_dist = dist_from_mean / max_possible_dist
        
        # Apply a non-linear scaling (exponential) to emphasize extreme values
        # Formula: base_weight + extra_weight * exp(factor * normalized_dist)
        # This ensures even values near the mean have some weight (base_weight)
        base_weight = 0.5
        extra_weight = 0.5
        factor = 1.5
        weights = base_weight + extra_weight * np.exp(factor * normalized_dist)
        
        # Normalize weights to average to 1.0
        weights = weights / np.mean(weights)
        
        return torch.FloatTensor(weights).to(y_batch.device)

    def fit(self, features: np.ndarray, growth_rates: np.ndarray, temperatures: np.ndarray,
            test_size: float = 0.2, val_size: float = 0.2, n_growth_features: int = 250,
            n_temp_features: int = 1000, batch_size: int = 128, epochs: int = 300,
            growth_transform: str = 'log2', temp_transform: str = 'sqrt',
            feature_selection_method: str = 'combined',
            device: str = 'cpu', early_stopping_patience: int = 30,
            growth_hidden_dims: Tuple[int, ...] = (128, 64, 32),
            temp_hidden_dims: Tuple[int, ...] = (256, 192, 128, 64, 32),
            cnn_filters: Tuple[int, ...] = (128, 96, 64, 32),
            kernel_sizes: Tuple[int, ...] = (3, 5, 7, 9),
            dropout_rate: float = 0.3,
            l2_reg: float = 0.001,
            output_dir: str = None,
            use_curriculum: bool = True,
            curriculum_phases: int = 5,
            use_weighted_loss: bool = True):
        """Fit the hybrid model on the given data.
        
        Args:
            features: Feature matrix
            growth_rates: Growth rate array
            temperatures: Temperature array
            test_size: Fraction of data to use for testing
            val_size: Fraction of training data to use for validation
            n_growth_features: Number of features to use for growth rate prediction
            n_temp_features: Number of features to use for temperature prediction
            batch_size: Batch size for training
            epochs: Number of epochs for training
            growth_transform: Transformation to apply to growth rates ('none', 'log', 'log2', 'log10', 'sqrt')
            temp_transform: Transformation to apply to temperatures ('none', 'log', 'log2', 'log10', 'sqrt')
            feature_selection_method: Method for feature selection ('correlation', 'mutual_info', 'model_based', 'combined')
            device: Device to run the models on
            early_stopping_patience: Number of epochs to wait for improvement before stopping
            growth_hidden_dims: Tuple of hidden dimensions for growth rate model
            temp_hidden_dims: Tuple of hidden dimensions for temperature model
            cnn_filters: Tuple of CNN filter numbers for temperature model
            kernel_sizes: Tuple of kernel sizes for CNN layers
            dropout_rate: Dropout rate for model regularization
            l2_reg: L2 regularization strength
            output_dir: Directory to save model and results
            use_curriculum: Whether to use curriculum learning for temperature model
            curriculum_phases: Number of phases for curriculum learning
            use_weighted_loss: Whether to use weighted loss for temperature model
        """
        self.growth_transform = growth_transform
        self.temp_transform = temp_transform
        self.output_dir = output_dir
        
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)
        
        # Calculate temperature statistics for weighted loss
        if use_weighted_loss:
            temp_min = np.min(temperatures)
            temp_max = np.max(temperatures)
            temp_mean = np.mean(temperatures)
            self.logger.info(f"Using weighted loss for temperature model. "
                           f"Range: {temp_min:.2f}-{temp_max:.2f}, Mean: {temp_mean:.2f}")
        
        # Split data into train, validation, and test sets
        X_train_val, X_test, y_growth_train_val, y_growth_test, y_temp_train_val, y_temp_test = train_test_split(
            features, growth_rates, temperatures, test_size=test_size, random_state=42
        )
        
        X_train, X_val, y_growth_train, y_growth_val, y_temp_train, y_temp_val = train_test_split(
            X_train_val, y_growth_train_val, y_temp_train_val, test_size=val_size, random_state=42
        )
        
        # Apply transformations to target variables
        y_growth_train_transformed = self._transform(y_growth_train, growth_transform)
        y_growth_val_transformed = self._transform(y_growth_val, growth_transform)
        y_growth_test_transformed = self._transform(y_growth_test, growth_transform)
        
        y_temp_train_transformed = self._transform(y_temp_train, temp_transform)
        y_temp_val_transformed = self._transform(y_temp_val, temp_transform)
        y_temp_test_transformed = self._transform(y_temp_test, temp_transform)
        
        # Select top features for growth rate and temperature prediction
        self.logger.info(f"Selecting features using {feature_selection_method} method...")
        self.growth_feature_indices = self._select_features(X_train, y_growth_train_transformed, n_growth_features, feature_selection_method)
        self.temp_feature_indices = self._select_features(X_train, y_temp_train_transformed, n_temp_features, feature_selection_method)
        
        X_train_growth = X_train[:, self.growth_feature_indices]
        X_val_growth = X_val[:, self.growth_feature_indices]
        X_test_growth = X_test[:, self.growth_feature_indices]
        
        X_train_temp = X_train[:, self.temp_feature_indices]
        X_val_temp = X_val[:, self.temp_feature_indices]
        X_test_temp = X_test[:, self.temp_feature_indices]
        
        # Standardize features
        self.growth_scaler = StandardScaler().fit(X_train_growth)
        self.temp_scaler = StandardScaler().fit(X_train_temp)
        
        X_train_growth_scaled = self.growth_scaler.transform(X_train_growth)
        X_val_growth_scaled = self.growth_scaler.transform(X_val_growth)
        X_test_growth_scaled = self.growth_scaler.transform(X_test_growth)
        
        X_train_temp_scaled = self.temp_scaler.transform(X_train_temp)
        X_val_temp_scaled = self.temp_scaler.transform(X_val_temp)
        X_test_temp_scaled = self.temp_scaler.transform(X_test_temp)
        
        # Create datasets
        train_growth_dataset = TensorDataset(
            torch.FloatTensor(X_train_growth_scaled),
            torch.FloatTensor(y_growth_train_transformed.reshape(-1, 1) if not hasattr(y_growth_train_transformed, 'iloc') else y_growth_train_transformed.values.reshape(-1, 1))
        )
        
        val_growth_dataset = TensorDataset(
            torch.FloatTensor(X_val_growth_scaled),
            torch.FloatTensor(y_growth_val_transformed.reshape(-1, 1) if not hasattr(y_growth_val_transformed, 'iloc') else y_growth_val_transformed.values.reshape(-1, 1))
        )
        
        # For temperature, create standard dataset for non-curriculum learning
        train_temp_dataset = TensorDataset(
            torch.FloatTensor(X_train_temp_scaled),
            torch.FloatTensor(y_temp_train_transformed.reshape(-1, 1) if not hasattr(y_temp_train_transformed, 'iloc') else y_temp_train_transformed.values.reshape(-1, 1))
        )
        
        val_temp_dataset = TensorDataset(
            torch.FloatTensor(X_val_temp_scaled),
            torch.FloatTensor(y_temp_val_transformed.reshape(-1, 1) if not hasattr(y_temp_val_transformed, 'iloc') else y_temp_val_transformed.values.reshape(-1, 1))
        )
        
        # Create data loaders for growth rate model (unchanged)
        train_growth_loader = DataLoader(train_growth_dataset, batch_size=batch_size, shuffle=True)
        val_growth_loader = DataLoader(val_growth_dataset, batch_size=batch_size)
        
        # Regular dataloader for temperature model (used when not using curriculum)
        train_temp_loader = DataLoader(train_temp_dataset, batch_size=batch_size, shuffle=True)
        val_temp_loader = DataLoader(val_temp_dataset, batch_size=batch_size)
        
        # For temperature model with curriculum learning
        if use_curriculum:
            # Create curriculum for temperature model
            self.logger.info("Creating curriculum for temperature model training...")
            temp_curriculum_loaders = self._create_temperature_curriculum(
                X_train_temp_scaled, 
                y_temp_train_transformed, 
                batch_size, 
                num_phases=curriculum_phases
            )
            self.logger.info(f"Created {len(temp_curriculum_loaders)} curriculum phases")
            
            # Calculate epochs per curriculum phase
            epochs_per_phase = max(epochs // curriculum_phases, 1)
            self.logger.info(f"Training {epochs_per_phase} epochs per curriculum phase")
        
        # Initialize models
        self._initialize_models(
            growth_input_dim=X_train_growth_scaled.shape[1],
            temp_input_dim=X_train_temp_scaled.shape[1],
            growth_hidden_dims=growth_hidden_dims,
            temp_hidden_dims=temp_hidden_dims,
            cnn_filters=cnn_filters,
            kernel_sizes=kernel_sizes,
            dropout_rate=dropout_rate,
            l2_reg=l2_reg,
            device=device
        )
        
        # Set up optimizers and schedulers
        growth_optimizer = optim.AdamW(self.growth_model.parameters(), lr=0.001, weight_decay=1e-4)
        temp_optimizer = optim.AdamW(self.temp_model.parameters(), lr=0.001, weight_decay=1e-4)
        
        # Set up cyclical learning rate schedulers - using cycle_momentum=False for AdamW
        growth_scheduler = CyclicLR(
            growth_optimizer, 
            base_lr=0.0001, 
            max_lr=0.003,
            step_size_up=len(train_growth_loader) * 5,  # 5 epochs for each cycle
            mode='triangular2',
            cycle_momentum=False  # AdamW doesn't use momentum
        )
        
        temp_scheduler = CyclicLR(
            temp_optimizer, 
            base_lr=0.0001, 
            max_lr=0.003,
            step_size_up=len(train_temp_loader) * 5,  # 5 epochs for each cycle
            mode='triangular2',
            cycle_momentum=False  # AdamW doesn't use momentum
        )
        
        # Use MSE loss
        criterion = nn.MSELoss()
        
        # Training variables
        best_growth_val_loss = float('inf')
        best_temp_val_loss = float('inf')
        best_growth_model_state = None
        best_temp_model_state = None
        growth_patience_counter = 0
        temp_patience_counter = 0
        
        # For tracking metrics
        train_growth_metrics = {'epoch': [], 'loss': [], 'r2': [], 'rmse': [], 'mae': []}
        val_growth_metrics = {'epoch': [], 'loss': [], 'r2': [], 'rmse': [], 'mae': []}
        train_temp_metrics = {'epoch': [], 'loss': [], 'r2': [], 'rmse': [], 'mae': []}
        val_temp_metrics = {'epoch': [], 'loss': [], 'r2': [], 'rmse': [], 'mae': []}
        
        # Train the model
        epoch_counter = 0
        
        if use_curriculum:
            # Train through each curriculum phase
            for phase, curr_temp_loader in enumerate(temp_curriculum_loaders):
                phase_start_epoch = epoch_counter
                phase_epochs = epochs_per_phase
                if phase == len(temp_curriculum_loaders) - 1:
                    # Use remaining epochs for final phase
                    phase_epochs = epochs - epoch_counter
                    
                self.logger.info(f"Starting curriculum phase {phase+1}/{curriculum_phases} "
                               f"(epochs {epoch_counter+1}-{epoch_counter+phase_epochs})")
                
                # Train for this curriculum phase
                for epoch in range(phase_epochs):
                    current_epoch = epoch_counter + epoch
                    
                    # Training mode
                    self.growth_model.train()
                    self.temp_model.train()
                    
                    train_growth_loss = 0
                    train_temp_loss = 0
                    
                    growth_preds_train = []
                    growth_targets_train = []
                    temp_preds_train = []
                    temp_targets_train = []
                    
                    # Train growth rate model (UNCHANGED)
                    for X_batch, y_batch in train_growth_loader:
                        X_batch = X_batch.to(device)
                        y_batch = y_batch.to(device)
                        
                        # Zero the gradients
                        growth_optimizer.zero_grad()
                        
                        # Forward pass
                        outputs = self.growth_model(X_batch)
                        
                        # Calculate loss
                        loss = criterion(outputs, y_batch)
                        
                        # Backward pass and optimize
                        loss.backward()
                        
                        # Monitor gradient norms
                        growth_grad_norm = sum(p.grad.norm(2).item() ** 2 for p in self.growth_model.parameters() if p.grad is not None) ** 0.5
                        
                        # Apply gradient clipping - stricter threshold for the first few epochs
                        clip_threshold = 0.5 if current_epoch < 10 else 1.0
                        torch.nn.utils.clip_grad_norm_(self.growth_model.parameters(), max_norm=clip_threshold)
                        
                        growth_optimizer.step()
                        growth_scheduler.step()
                        
                        train_growth_loss += loss.item()
                        
                        # Store predictions and targets for metrics
                        growth_preds_train.append(outputs.detach().cpu().numpy())
                        growth_targets_train.append(y_batch.detach().cpu().numpy())
                    
                    # Average training loss
                    train_growth_loss /= len(train_growth_loader)
                    
                    # Train temperature model with curriculum
                    for X_batch, y_batch in curr_temp_loader:
                        X_batch = X_batch.to(device)
                        y_batch = y_batch.to(device)
                        
                        # Zero the gradients
                        temp_optimizer.zero_grad()
                        
                        # Forward pass
                        outputs, l2_loss = self.temp_model(X_batch)
                        
                        # Calculate loss with L2 regularization
                        if use_weighted_loss:
                            # Use weighted MSE loss for temperature model
                            weights = self._calculate_temp_weights(y_batch, temp_min, temp_max, temp_mean)
                            weights = weights.view(-1, 1).to(device)
                            unweighted_loss = F.mse_loss(outputs, y_batch, reduction='none')
                            loss = (unweighted_loss * weights).mean() + l2_loss
                        else:
                            # Standard MSE loss
                            loss = criterion(outputs, y_batch) + l2_loss
                        
                        # Backward pass and optimize
                        loss.backward()
                        
                        # Monitor gradient norms
                        temp_grad_norm = sum(p.grad.norm(2).item() ** 2 for p in self.temp_model.parameters() if p.grad is not None) ** 0.5
                        
                        # Apply gradient clipping - stricter threshold for the first few epochs
                        clip_threshold = 0.5 if current_epoch < 10 else 1.0
                        torch.nn.utils.clip_grad_norm_(self.temp_model.parameters(), max_norm=clip_threshold)
                        
                        temp_optimizer.step()
                        temp_scheduler.step()
                        
                        train_temp_loss += loss.item()
                        
                        # Store predictions and targets for metrics
                        temp_preds_train.append(outputs.detach().cpu().numpy())
                        temp_targets_train.append(y_batch.detach().cpu().numpy())
                    
                    # Average training loss
                    train_temp_loss /= len(curr_temp_loader)
                    
                    # Validation
                    self.growth_model.eval()
                    self.temp_model.eval()
                    
                    val_growth_loss = 0
                    val_temp_loss = 0
                    
                    growth_preds = []
                    temp_preds = []
                    growth_targets = []
                    temp_targets = []
                    
                    with torch.no_grad():
                        # Validate growth rate model
                        for X_batch, y_batch in val_growth_loader:
                            X_batch = X_batch.to(device)
                            y_batch = y_batch.to(device)
                            
                            # Forward pass
                            outputs = self.growth_model(X_batch)
                            
                            # Calculate loss
                            loss = criterion(outputs, y_batch)
                            val_growth_loss += loss.item()
                            
                            # Store predictions and targets for metrics
                            growth_preds.append(outputs.cpu().numpy())
                            growth_targets.append(y_batch.cpu().numpy())
                        
                        # Validate temperature model
                        for X_batch, y_batch in val_temp_loader:
                            X_batch = X_batch.to(device)
                            y_batch = y_batch.to(device)
                            
                            # Forward pass - in eval mode, only returns predictions
                            outputs = self.temp_model(X_batch)
                            
                            # Calculate loss
                            loss = criterion(outputs, y_batch)
                            val_temp_loss += loss.item()
                            
                            # Store predictions and targets for metrics
                            temp_preds.append(outputs.cpu().numpy())
                            temp_targets.append(y_batch.cpu().numpy())
                    
                    # Average validation loss
                    val_growth_loss /= len(val_growth_loader)
                    val_temp_loss /= len(val_temp_loader)
                    
                    # Concatenate predictions and targets for training metrics
                    growth_preds_train = np.concatenate(growth_preds_train).flatten()
                    growth_targets_train = np.concatenate(growth_targets_train).flatten()
                    temp_preds_train = np.concatenate(temp_preds_train).flatten()
                    temp_targets_train = np.concatenate(temp_targets_train).flatten()
                    
                    # Clean predictions and targets from NaN values for metric calculation
                    # Create masks for non-NaN values
                    growth_train_mask = ~(np.isnan(growth_preds_train) | np.isnan(growth_targets_train))
                    temp_train_mask = ~(np.isnan(temp_preds_train) | np.isnan(temp_targets_train))
                    
                    # Filter out NaN values
                    growth_preds_train_clean = growth_preds_train[growth_train_mask]
                    growth_targets_train_clean = growth_targets_train[growth_train_mask]
                    temp_preds_train_clean = temp_preds_train[temp_train_mask]
                    temp_targets_train_clean = temp_targets_train[temp_train_mask]
                    
                    # Calculate training metrics with clean data
                    growth_r2_train = r2_score(growth_targets_train_clean, growth_preds_train_clean) if len(growth_targets_train_clean) > 0 else np.nan
                    growth_rmse_train = np.sqrt(mean_squared_error(growth_targets_train_clean, growth_preds_train_clean)) if len(growth_targets_train_clean) > 0 else np.nan
                    growth_mae_train = mean_absolute_error(growth_targets_train_clean, growth_preds_train_clean) if len(growth_targets_train_clean) > 0 else np.nan
                    
                    temp_r2_train = r2_score(temp_targets_train_clean, temp_preds_train_clean) if len(temp_targets_train_clean) > 0 else np.nan
                    temp_rmse_train = np.sqrt(mean_squared_error(temp_targets_train_clean, temp_preds_train_clean)) if len(temp_targets_train_clean) > 0 else np.nan
                    temp_mae_train = mean_absolute_error(temp_targets_train_clean, temp_preds_train_clean) if len(temp_targets_train_clean) > 0 else np.nan
                    
                    # Concatenate predictions and targets for validation metrics
                    growth_preds = np.concatenate(growth_preds).flatten()
                    growth_targets = np.concatenate(growth_targets).flatten()
                    temp_preds = np.concatenate(temp_preds).flatten()
                    temp_targets = np.concatenate(temp_targets).flatten()
                    
                    # Clean validation predictions and targets
                    growth_val_mask = ~(np.isnan(growth_preds) | np.isnan(growth_targets))
                    temp_val_mask = ~(np.isnan(temp_preds) | np.isnan(temp_targets))
                    
                    growth_preds_clean = growth_preds[growth_val_mask]
                    growth_targets_clean = growth_targets[growth_val_mask]
                    temp_preds_clean = temp_preds[temp_val_mask]
                    temp_targets_clean = temp_targets[temp_val_mask]
                    
                    # Calculate metrics with clean data
                    growth_r2 = r2_score(growth_targets_clean, growth_preds_clean) if len(growth_targets_clean) > 0 else np.nan
                    growth_rmse = np.sqrt(mean_squared_error(growth_targets_clean, growth_preds_clean)) if len(growth_targets_clean) > 0 else np.nan
                    growth_mae = mean_absolute_error(growth_targets_clean, growth_preds_clean) if len(growth_targets_clean) > 0 else np.nan
                    
                    temp_r2 = r2_score(temp_targets_clean, temp_preds_clean) if len(temp_targets_clean) > 0 else np.nan
                    temp_rmse = np.sqrt(mean_squared_error(temp_targets_clean, temp_preds_clean)) if len(temp_targets_clean) > 0 else np.nan
                    temp_mae = mean_absolute_error(temp_targets_clean, temp_preds_clean) if len(temp_targets_clean) > 0 else np.nan
                    
                    # Log NaN statistics
                    if current_epoch % 10 == 0:
                        growth_train_nan_percent = 100 * (1 - np.mean(growth_train_mask))
                        temp_train_nan_percent = 100 * (1 - np.mean(temp_train_mask))
                        growth_val_nan_percent = 100 * (1 - np.mean(growth_val_mask))
                        temp_val_nan_percent = 100 * (1 - np.mean(temp_val_mask))
                        
                        self.logger.info(f"NaN statistics: Growth train {growth_train_nan_percent:.2f}%, "
                                       f"Temp train {temp_train_nan_percent:.2f}%, "
                                       f"Growth val {growth_val_nan_percent:.2f}%, "
                                       f"Temp val {temp_val_nan_percent:.2f}%")
                    
                    # Store metrics for plotting
                    train_growth_metrics['epoch'].append(current_epoch)
                    train_growth_metrics['loss'].append(train_growth_loss)
                    train_growth_metrics['r2'].append(growth_r2_train)
                    train_growth_metrics['rmse'].append(growth_rmse_train)
                    train_growth_metrics['mae'].append(growth_mae_train)
                    
                    val_growth_metrics['epoch'].append(current_epoch)
                    val_growth_metrics['loss'].append(val_growth_loss)
                    val_growth_metrics['r2'].append(growth_r2)
                    val_growth_metrics['rmse'].append(growth_rmse)
                    val_growth_metrics['mae'].append(growth_mae)
                    
                    train_temp_metrics['epoch'].append(current_epoch)
                    train_temp_metrics['loss'].append(train_temp_loss)
                    train_temp_metrics['r2'].append(temp_r2_train)
                    train_temp_metrics['rmse'].append(temp_rmse_train)
                    train_temp_metrics['mae'].append(temp_mae_train)
                    
                    val_temp_metrics['epoch'].append(current_epoch)
                    val_temp_metrics['loss'].append(val_temp_loss)
                    val_temp_metrics['r2'].append(temp_r2)
                    val_temp_metrics['rmse'].append(temp_rmse)
                    val_temp_metrics['mae'].append(temp_mae)
                    
                    # Check for early stopping
                    if val_growth_loss < best_growth_val_loss:
                        best_growth_val_loss = val_growth_loss
                        best_growth_model_state = self.growth_model.state_dict().copy()
                        growth_patience_counter = 0
                    else:
                        growth_patience_counter += 1
                    
                    if val_temp_loss < best_temp_val_loss:
                        best_temp_val_loss = val_temp_loss
                        best_temp_model_state = self.temp_model.state_dict().copy()
                        temp_patience_counter = 0
                    else:
                        temp_patience_counter += 1
                    
                    # Log progress every 10 epochs
                    if (current_epoch + 1) % 10 == 0:
                        self.logger.info("="*60)
                        self.logger.info(f"Epoch {current_epoch + 1}/{epochs}")
                        
                        self.logger.info("Growth Rate Model:")
                        self.logger.info(f"Train - Loss: {train_growth_loss:.4f}, R2: {growth_r2_train:.6f}, RMSE: {growth_rmse_train:.4f}, MAE: {growth_mae_train:.4f}")
                        self.logger.info(f"Val   - Loss: {val_growth_loss:.4f}, R2: {growth_r2:.6f}, RMSE: {growth_rmse:.4f}, MAE: {growth_mae:.4f}")
                        
                        self.logger.info("Temperature Model (CNN+DNN):")
                        self.logger.info(f"Train - Loss: {train_temp_loss:.4f}, R2: {temp_r2_train:.6f}, RMSE: {temp_rmse_train:.4f}, MAE: {temp_mae_train:.4f}")
                        self.logger.info(f"Val   - Loss: {val_temp_loss:.4f}, R2: {temp_r2:.6f}, RMSE: {temp_rmse:.4f}, MAE: {temp_mae:.4f}")
                        
                        # Additional metrics in original scale
                        self.logger.info("Metrics in original scale:")
                        
                        # Calculate metrics in original scale for growth rate
                        growth_preds_orig = self._inverse_transform(growth_preds, self.growth_transform)
                        growth_targets_orig = self._inverse_transform(growth_targets, self.growth_transform)
                        
                        # Clean transformed data
                        growth_orig_mask = ~(np.isnan(growth_preds_orig) | np.isnan(growth_targets_orig))
                        growth_preds_orig_clean = growth_preds_orig[growth_orig_mask]
                        growth_targets_orig_clean = growth_targets_orig[growth_orig_mask]
                        
                        # Only calculate if we have clean data
                        if len(growth_targets_orig_clean) > 0:
                            growth_r2_orig = r2_score(growth_targets_orig_clean, growth_preds_orig_clean)
                            growth_rmse_orig = np.sqrt(mean_squared_error(growth_targets_orig_clean, growth_preds_orig_clean))
                            growth_mae_orig = mean_absolute_error(growth_targets_orig_clean, growth_preds_orig_clean)
                        else:
                            growth_r2_orig = np.nan
                            growth_rmse_orig = np.nan
                            growth_mae_orig = np.nan
                        
                        # Calculate metrics in original scale for temperature
                        temp_preds_orig = self._inverse_transform(temp_preds, self.temp_transform)
                        temp_targets_orig = self._inverse_transform(temp_targets, self.temp_transform)
                        
                        # Clean transformed data
                        temp_orig_mask = ~(np.isnan(temp_preds_orig) | np.isnan(temp_targets_orig))
                        temp_preds_orig_clean = temp_preds_orig[temp_orig_mask]
                        temp_targets_orig_clean = temp_targets_orig[temp_orig_mask]
                        
                        # Only calculate if we have clean data
                        if len(temp_targets_orig_clean) > 0:
                            temp_r2_orig = r2_score(temp_targets_orig_clean, temp_preds_orig_clean)
                            temp_rmse_orig = np.sqrt(mean_squared_error(temp_targets_orig_clean, temp_preds_orig_clean))
                            temp_mae_orig = mean_absolute_error(temp_targets_orig_clean, temp_preds_orig_clean)
                        else:
                            temp_r2_orig = np.nan
                            temp_rmse_orig = np.nan
                            temp_mae_orig = np.nan
                        
                        self.logger.info(f"Growth Rate - R2 (orig): {growth_r2_orig:.6f}, RMSE (orig): {growth_rmse_orig:.4f}, MAE (orig): {growth_mae_orig:.4f}")
                        self.logger.info(f"Temperature - R2 (orig): {temp_r2_orig:.6f}, RMSE (orig): {temp_rmse_orig:.4f}, MAE (orig): {temp_mae_orig:.4f}")
                    
                    # Early stopping
                    if growth_patience_counter >= early_stopping_patience and temp_patience_counter >= early_stopping_patience:
                        self.logger.info(f"Early stopping triggered at epoch {current_epoch + 1}, curriculum phase {phase+1}")
                        break
                
                # Update epoch counter for next phase
                epoch_counter += phase_epochs
                
                # Early stopping check between phases
                if growth_patience_counter >= early_stopping_patience and temp_patience_counter >= early_stopping_patience:
                    break
        else:
            # Original training loop without curriculum (for growth and temperature models)
            for epoch in range(epochs):
                # Training mode
                self.growth_model.train()
                self.temp_model.train()
                
                train_growth_loss = 0
                train_temp_loss = 0
                
                growth_preds_train = []
                growth_targets_train = []
                temp_preds_train = []
                temp_targets_train = []
                
                # Train growth rate model
                for X_batch, y_batch in train_growth_loader:
                    X_batch = X_batch.to(device)
                    y_batch = y_batch.to(device)
                    
                    # Zero the gradients
                    growth_optimizer.zero_grad()
                    
                    # Forward pass
                    outputs = self.growth_model(X_batch)
                    
                    # Calculate loss
                    loss = criterion(outputs, y_batch)
                    
                    # Backward pass and optimize
                    loss.backward()
                    
                    # Monitor gradient norms
                    growth_grad_norm = sum(p.grad.norm(2).item() ** 2 for p in self.growth_model.parameters() if p.grad is not None) ** 0.5
                    
                    # Apply gradient clipping - stricter threshold for the first few epochs
                    clip_threshold = 0.5 if epoch < 10 else 1.0
                    torch.nn.utils.clip_grad_norm_(self.growth_model.parameters(), max_norm=clip_threshold)
                    
                    growth_optimizer.step()
                    growth_scheduler.step()
                    
                    train_growth_loss += loss.item()
                    
                    # Store predictions and targets for metrics
                    growth_preds_train.append(outputs.detach().cpu().numpy())
                    growth_targets_train.append(y_batch.detach().cpu().numpy())
                
                # Average training loss
                train_growth_loss /= len(train_growth_loader)
                
                # Train temperature model
                for X_batch, y_batch in train_temp_loader:
                    X_batch = X_batch.to(device)
                    y_batch = y_batch.to(device)
                    
                    # Zero the gradients
                    temp_optimizer.zero_grad()
                    
                    # Forward pass
                    outputs, l2_loss = self.temp_model(X_batch)
                    
                    # Calculate loss with L2 regularization
                    if use_weighted_loss:
                        # Use weighted MSE loss for temperature model
                        weights = self._calculate_temp_weights(y_batch, temp_min, temp_max, temp_mean)
                        weights = weights.view(-1, 1).to(device)
                        unweighted_loss = F.mse_loss(outputs, y_batch, reduction='none')
                        loss = (unweighted_loss * weights).mean() + l2_loss
                    else:
                        # Standard MSE loss
                        loss = criterion(outputs, y_batch) + l2_loss
                    
                    # Backward pass and optimize
                    loss.backward()
                    
                    # Monitor gradient norms
                    temp_grad_norm = sum(p.grad.norm(2).item() ** 2 for p in self.temp_model.parameters() if p.grad is not None) ** 0.5
                    
                    # Apply gradient clipping - stricter threshold for the first few epochs
                    clip_threshold = 0.5 if epoch < 10 else 1.0
                    torch.nn.utils.clip_grad_norm_(self.temp_model.parameters(), max_norm=clip_threshold)
                    
                    temp_optimizer.step()
                    temp_scheduler.step()
                    
                    train_temp_loss += loss.item()
                    
                    # Store predictions and targets for metrics
                    temp_preds_train.append(outputs.detach().cpu().numpy())
                    temp_targets_train.append(y_batch.detach().cpu().numpy())
                
                # Average training loss
                train_temp_loss /= len(train_temp_loader)
                
                # Validation
                self.growth_model.eval()
                self.temp_model.eval()
                
                val_growth_loss = 0
                val_temp_loss = 0
                
                growth_preds = []
                temp_preds = []
                growth_targets = []
                temp_targets = []
                
                with torch.no_grad():
                    # Validate growth rate model
                    for X_batch, y_batch in val_growth_loader:
                        X_batch = X_batch.to(device)
                        y_batch = y_batch.to(device)
                        
                        # Forward pass
                        outputs = self.growth_model(X_batch)
                        
                        # Calculate loss
                        loss = criterion(outputs, y_batch)
                        val_growth_loss += loss.item()
                        
                        # Store predictions and targets for metrics
                        growth_preds.append(outputs.cpu().numpy())
                        growth_targets.append(y_batch.cpu().numpy())
                    
                    # Validate temperature model
                    for X_batch, y_batch in val_temp_loader:
                        X_batch = X_batch.to(device)
                        y_batch = y_batch.to(device)
                        
                        # Forward pass - in eval mode, only returns predictions
                        outputs = self.temp_model(X_batch)
                        
                        # Calculate loss
                        loss = criterion(outputs, y_batch)
                        val_temp_loss += loss.item()
                        
                        # Store predictions and targets for metrics
                        temp_preds.append(outputs.cpu().numpy())
                        temp_targets.append(y_batch.cpu().numpy())
                
                # Average validation loss
                val_growth_loss /= len(val_growth_loader)
                val_temp_loss /= len(val_temp_loader)
                
                # Calculate metrics
                # Concatenate predictions and targets for training metrics
                growth_preds_train = np.concatenate(growth_preds_train).flatten()
                growth_targets_train = np.concatenate(growth_targets_train).flatten()
                temp_preds_train = np.concatenate(temp_preds_train).flatten()
                temp_targets_train = np.concatenate(temp_targets_train).flatten()
                
                # Clean predictions and targets from NaN values for metric calculation
                growth_train_mask = ~(np.isnan(growth_preds_train) | np.isnan(growth_targets_train))
                temp_train_mask = ~(np.isnan(temp_preds_train) | np.isnan(temp_targets_train))
                
                # Filter out NaN values
                growth_preds_train_clean = growth_preds_train[growth_train_mask]
                growth_targets_train_clean = growth_targets_train[growth_train_mask]
                temp_preds_train_clean = temp_preds_train[temp_train_mask]
                temp_targets_train_clean = temp_targets_train[temp_train_mask]
                
                # Calculate training metrics with clean data
                growth_r2_train = r2_score(growth_targets_train_clean, growth_preds_train_clean) if len(growth_targets_train_clean) > 0 else np.nan
                growth_rmse_train = np.sqrt(mean_squared_error(growth_targets_train_clean, growth_preds_train_clean)) if len(growth_targets_train_clean) > 0 else np.nan
                growth_mae_train = mean_absolute_error(growth_targets_train_clean, growth_preds_train_clean) if len(growth_targets_train_clean) > 0 else np.nan
                
                temp_r2_train = r2_score(temp_targets_train_clean, temp_preds_train_clean) if len(temp_targets_train_clean) > 0 else np.nan
                temp_rmse_train = np.sqrt(mean_squared_error(temp_targets_train_clean, temp_preds_train_clean)) if len(temp_targets_train_clean) > 0 else np.nan
                temp_mae_train = mean_absolute_error(temp_targets_train_clean, temp_preds_train_clean) if len(temp_targets_train_clean) > 0 else np.nan
                
                # Concatenate predictions and targets for validation metrics
                growth_preds = np.concatenate(growth_preds).flatten()
                growth_targets = np.concatenate(growth_targets).flatten()
                temp_preds = np.concatenate(temp_preds).flatten()
                temp_targets = np.concatenate(temp_targets).flatten()
                
                # Clean validation predictions and targets
                growth_val_mask = ~(np.isnan(growth_preds) | np.isnan(growth_targets))
                temp_val_mask = ~(np.isnan(temp_preds) | np.isnan(temp_targets))
                
                growth_preds_clean = growth_preds[growth_val_mask]
                growth_targets_clean = growth_targets[growth_val_mask]
                temp_preds_clean = temp_preds[temp_val_mask]
                temp_targets_clean = temp_targets[temp_val_mask]
                
                # Calculate metrics with clean data
                growth_r2 = r2_score(growth_targets_clean, growth_preds_clean) if len(growth_targets_clean) > 0 else np.nan
                growth_rmse = np.sqrt(mean_squared_error(growth_targets_clean, growth_preds_clean)) if len(growth_targets_clean) > 0 else np.nan
                growth_mae = mean_absolute_error(growth_targets_clean, growth_preds_clean) if len(growth_targets_clean) > 0 else np.nan
                
                temp_r2 = r2_score(temp_targets_clean, temp_preds_clean) if len(temp_targets_clean) > 0 else np.nan
                temp_rmse = np.sqrt(mean_squared_error(temp_targets_clean, temp_preds_clean)) if len(temp_targets_clean) > 0 else np.nan
                temp_mae = mean_absolute_error(temp_targets_clean, temp_preds_clean) if len(temp_targets_clean) > 0 else np.nan
                
                # Log NaN statistics
                if epoch % 10 == 0:
                    growth_train_nan_percent = 100 * (1 - np.mean(growth_train_mask))
                    temp_train_nan_percent = 100 * (1 - np.mean(temp_train_mask))
                    growth_val_nan_percent = 100 * (1 - np.mean(growth_val_mask))
                    temp_val_nan_percent = 100 * (1 - np.mean(temp_val_mask))
                    
                    self.logger.info(f"NaN statistics: Growth train {growth_train_nan_percent:.2f}%, "
                                   f"Temp train {temp_train_nan_percent:.2f}%, "
                                   f"Growth val {growth_val_nan_percent:.2f}%, "
                                   f"Temp val {temp_val_nan_percent:.2f}%")
                
                # Store metrics for plotting
                train_growth_metrics['epoch'].append(epoch)
                train_growth_metrics['loss'].append(train_growth_loss)
                train_growth_metrics['r2'].append(growth_r2_train)
                train_growth_metrics['rmse'].append(growth_rmse_train)
                train_growth_metrics['mae'].append(growth_mae_train)
                
                val_growth_metrics['epoch'].append(epoch)
                val_growth_metrics['loss'].append(val_growth_loss)
                val_growth_metrics['r2'].append(growth_r2)
                val_growth_metrics['rmse'].append(growth_rmse)
                val_growth_metrics['mae'].append(growth_mae)
                
                train_temp_metrics['epoch'].append(epoch)
                train_temp_metrics['loss'].append(train_temp_loss)
                train_temp_metrics['r2'].append(temp_r2_train)
                train_temp_metrics['rmse'].append(temp_rmse_train)
                train_temp_metrics['mae'].append(temp_mae_train)
                
                val_temp_metrics['epoch'].append(epoch)
                val_temp_metrics['loss'].append(val_temp_loss)
                val_temp_metrics['r2'].append(temp_r2)
                val_temp_metrics['rmse'].append(temp_rmse)
                val_temp_metrics['mae'].append(temp_mae)
                
                # Check for early stopping
                if val_growth_loss < best_growth_val_loss:
                    best_growth_val_loss = val_growth_loss
                    best_growth_model_state = self.growth_model.state_dict().copy()
                    growth_patience_counter = 0
                else:
                    growth_patience_counter += 1
                
                if val_temp_loss < best_temp_val_loss:
                    best_temp_val_loss = val_temp_loss
                    best_temp_model_state = self.temp_model.state_dict().copy()
                    temp_patience_counter = 0
                else:
                    temp_patience_counter += 1
                
                # Log progress every 10 epochs
                if (epoch + 1) % 10 == 0:
                    self.logger.info("="*60)
                    self.logger.info(f"Epoch {epoch + 1}/{epochs}")
                    
                    self.logger.info("Growth Rate Model:")
                    self.logger.info(f"Train - Loss: {train_growth_loss:.4f}, R2: {growth_r2_train:.6f}, RMSE: {growth_rmse_train:.4f}, MAE: {growth_mae_train:.4f}")
                    self.logger.info(f"Val   - Loss: {val_growth_loss:.4f}, R2: {growth_r2:.6f}, RMSE: {growth_rmse:.4f}, MAE: {growth_mae:.4f}")
                    
                    self.logger.info("Temperature Model (CNN+DNN):")
                    self.logger.info(f"Train - Loss: {train_temp_loss:.4f}, R2: {temp_r2_train:.6f}, RMSE: {temp_rmse_train:.4f}, MAE: {temp_mae_train:.4f}")
                    self.logger.info(f"Val   - Loss: {val_temp_loss:.4f}, R2: {temp_r2:.6f}, RMSE: {temp_rmse:.4f}, MAE: {temp_mae:.4f}")
                    
                    # Additional metrics in original scale
                    self.logger.info("Metrics in original scale:")
                    
                    # Calculate metrics in original scale for growth rate
                    growth_preds_orig = self._inverse_transform(growth_preds, self.growth_transform)
                    growth_targets_orig = self._inverse_transform(growth_targets, self.growth_transform)
                    
                    # Clean transformed data
                    growth_orig_mask = ~(np.isnan(growth_preds_orig) | np.isnan(growth_targets_orig))
                    growth_preds_orig_clean = growth_preds_orig[growth_orig_mask]
                    growth_targets_orig_clean = growth_targets_orig[growth_orig_mask]
                    
                    # Only calculate if we have clean data
                    if len(growth_targets_orig_clean) > 0:
                        growth_r2_orig = r2_score(growth_targets_orig_clean, growth_preds_orig_clean)
                        growth_rmse_orig = np.sqrt(mean_squared_error(growth_targets_orig_clean, growth_preds_orig_clean))
                        growth_mae_orig = mean_absolute_error(growth_targets_orig_clean, growth_preds_orig_clean)
                    else:
                        growth_r2_orig = np.nan
                        growth_rmse_orig = np.nan
                        growth_mae_orig = np.nan
                    
                    # Calculate metrics in original scale for temperature
                    temp_preds_orig = self._inverse_transform(temp_preds, self.temp_transform)
                    temp_targets_orig = self._inverse_transform(temp_targets, self.temp_transform)
                    
                    # Clean transformed data
                    temp_orig_mask = ~(np.isnan(temp_preds_orig) | np.isnan(temp_targets_orig))
                    temp_preds_orig_clean = temp_preds_orig[temp_orig_mask]
                    temp_targets_orig_clean = temp_targets_orig[temp_orig_mask]
                    
                    # Only calculate if we have clean data
                    if len(temp_targets_orig_clean) > 0:
                        temp_r2_orig = r2_score(temp_targets_orig_clean, temp_preds_orig_clean)
                        temp_rmse_orig = np.sqrt(mean_squared_error(temp_targets_orig_clean, temp_preds_orig_clean))
                        temp_mae_orig = mean_absolute_error(temp_targets_orig_clean, temp_preds_orig_clean)
                    else:
                        temp_r2_orig = np.nan
                        temp_rmse_orig = np.nan
                        temp_mae_orig = np.nan
                    
                    self.logger.info(f"Growth Rate - R2 (orig): {growth_r2_orig:.6f}, RMSE (orig): {growth_rmse_orig:.4f}, MAE (orig): {growth_mae_orig:.4f}")
                    self.logger.info(f"Temperature - R2 (orig): {temp_r2_orig:.6f}, RMSE (orig): {temp_rmse_orig:.4f}, MAE (orig): {temp_mae_orig:.4f}")
                
                # Early stopping
                if growth_patience_counter >= early_stopping_patience and temp_patience_counter >= early_stopping_patience:
                    self.logger.info(f"Early stopping triggered at epoch {epoch + 1}")
                    break
        
        # Load the best models
        if best_growth_model_state is not None:
            self.growth_model.load_state_dict(best_growth_model_state)
        
        if best_temp_model_state is not None:
            self.temp_model.load_state_dict(best_temp_model_state)
        
        # Save training metrics
        if output_dir:
            # Convert metrics to DataFrame and save
            train_growth_df = pd.DataFrame(train_growth_metrics)
            val_growth_df = pd.DataFrame(val_growth_metrics)
            train_temp_df = pd.DataFrame(train_temp_metrics)
            val_temp_df = pd.DataFrame(val_temp_metrics)
            
            train_growth_df.to_csv(os.path.join(output_dir, 'train_growth_metrics.csv'), index=False)
            val_growth_df.to_csv(os.path.join(output_dir, 'val_growth_metrics.csv'), index=False)
            train_temp_df.to_csv(os.path.join(output_dir, 'train_temp_metrics.csv'), index=False)
            val_temp_df.to_csv(os.path.join(output_dir, 'val_temp_metrics.csv'), index=False)
        
        # Final evaluation on test set
        test_metrics = self.evaluate(
            X_test, 
            y_growth_test_transformed, 
            y_temp_test_transformed
        )
        
        self.logger.info("\n============================================================")
        self.logger.info("Test Set Evaluation:")
        self.logger.info(f"Growth Rate - R²: {test_metrics['growth_r2']:.4f}, RMSE: {test_metrics['growth_rmse']:.4f}, MAE: {test_metrics['growth_mae']:.4f}")
        self.logger.info(f"Temperature - R²: {test_metrics['temp_r2']:.4f}, RMSE: {test_metrics['temp_rmse']:.4f}, MAE: {test_metrics['temp_mae']:.4f}")
        self.logger.info("Original Scale Metrics:")
        self.logger.info(f"Growth Rate - R²: {test_metrics['growth_r2_orig']:.4f}, RMSE (orig): {test_metrics['growth_rmse_orig']:.4f}, MAE (orig): {test_metrics['growth_mae_orig']:.4f}")
        self.logger.info(f"Temperature - R²: {test_metrics['temp_r2_orig']:.4f}, RMSE (orig): {test_metrics['temp_rmse_orig']:.4f}, MAE (orig): {test_metrics['temp_mae_orig']:.4f}")
        
        # Save test metrics to file if output directory is provided
        if output_dir:
            metrics_file = os.path.join(output_dir, 'test_metrics.csv')
            pd.DataFrame([test_metrics]).to_csv(metrics_file, index=False)
            self.logger.info(f"Saved test metrics to {metrics_file}")
        
        # Save the model
        if output_dir:
            self.save(output_dir)
            self.logger.info(f"Model saved to {output_dir}")
    
    def evaluate(self, X, y_growth, y_temp):
        """Evaluate the model on given data.
        
        Args:
            X: Feature matrix
            y_growth: Growth rate array
            y_temp: Temperature array
            
        Returns:
            Dictionary of evaluation metrics
        """
        # Convert to numpy if needed
        X_np = X.values if hasattr(X, 'values') else X
        
        # Apply feature selection
        X_growth = X_np[:, self.growth_feature_indices]
        X_temp = X_np[:, self.temp_feature_indices]
        
        # Standardize features
        X_growth_scaled = self.growth_scaler.transform(X_growth)
        X_temp_scaled = self.temp_scaler.transform(X_temp)
        
        # Get device from models
        device = next(self.growth_model.parameters()).device
        
        # Convert to tensors and move to the same device as the models
        X_growth_tensor = torch.FloatTensor(X_growth_scaled).to(device)
        X_temp_tensor = torch.FloatTensor(X_temp_scaled).to(device)
        
        # Make predictions
        self.growth_model.eval()
        self.temp_model.eval()
        
        with torch.no_grad():
            growth_preds = self.growth_model(X_growth_tensor).cpu().numpy().flatten()
            # In eval mode, temp_model only returns predictions (no l2_loss)
            temp_preds = self.temp_model(X_temp_tensor).cpu().numpy().flatten()
        
        # Handle NaN values in predictions
        growth_mask = ~(np.isnan(growth_preds) | np.isnan(y_growth))
        temp_mask = ~(np.isnan(temp_preds) | np.isnan(y_temp))
        
        growth_preds_clean = growth_preds[growth_mask]
        y_growth_clean = y_growth[growth_mask]
        temp_preds_clean = temp_preds[temp_mask]
        y_temp_clean = y_temp[temp_mask]
        
        self.logger.info(f"Evaluation NaN statistics: Growth {100*(1-np.mean(growth_mask)):.2f}%, Temperature {100*(1-np.mean(temp_mask)):.2f}%")
        
        # Calculate metrics in transformed space
        growth_r2 = r2_score(y_growth_clean, growth_preds_clean) if len(y_growth_clean) > 0 else np.nan
        growth_rmse = np.sqrt(mean_squared_error(y_growth_clean, growth_preds_clean)) if len(y_growth_clean) > 0 else np.nan
        growth_mae = mean_absolute_error(y_growth_clean, growth_preds_clean) if len(y_growth_clean) > 0 else np.nan
        
        temp_r2 = r2_score(y_temp_clean, temp_preds_clean) if len(y_temp_clean) > 0 else np.nan
        temp_rmse = np.sqrt(mean_squared_error(y_temp_clean, temp_preds_clean)) if len(y_temp_clean) > 0 else np.nan
        temp_mae = mean_absolute_error(y_temp_clean, temp_preds_clean) if len(y_temp_clean) > 0 else np.nan
        
        # Apply inverse transformations for reporting
        y_growth_orig = self._inverse_transform(y_growth_clean, self.growth_transform)
        growth_preds_orig = self._inverse_transform(growth_preds_clean, self.growth_transform)
        
        y_temp_orig = self._inverse_transform(y_temp_clean, self.temp_transform)
        temp_preds_orig = self._inverse_transform(temp_preds_clean, self.temp_transform)
        
        # Calculate metrics in original scale
        growth_r2_orig = r2_score(y_growth_orig, growth_preds_orig) if len(y_growth_orig) > 0 else np.nan
        growth_rmse_orig = np.sqrt(mean_squared_error(y_growth_orig, growth_preds_orig)) if len(y_growth_orig) > 0 else np.nan
        growth_mae_orig = mean_absolute_error(y_growth_orig, growth_preds_orig) if len(y_growth_orig) > 0 else np.nan
        
        temp_r2_orig = r2_score(y_temp_orig, temp_preds_orig) if len(y_temp_orig) > 0 else np.nan
        temp_rmse_orig = np.sqrt(mean_squared_error(y_temp_orig, temp_preds_orig)) if len(y_temp_orig) > 0 else np.nan
        temp_mae_orig = mean_absolute_error(y_temp_orig, temp_preds_orig) if len(y_temp_orig) > 0 else np.nan
        
        # Calculate additional validation metrics
        # Explained variance score
        temp_ev = 1 - (np.var(y_temp_clean - temp_preds_clean) / np.var(y_temp_clean)) if len(y_temp_clean) > 0 else np.nan
        temp_ev_orig = 1 - (np.var(y_temp_orig - temp_preds_orig) / np.var(y_temp_orig)) if len(y_temp_orig) > 0 else np.nan
        
        # Log detailed analysis
        self.logger.info("\nDetailed Temperature Prediction Analysis:")
        self.logger.info(f"Temperature Range: {np.min(y_temp_orig):.2f} to {np.max(y_temp_orig):.2f}")
        self.logger.info(f"Predicted Range: {np.min(temp_preds_orig):.2f} to {np.max(temp_preds_orig):.2f}")
        self.logger.info(f"Explained Variance: {temp_ev:.4f} (transformed), {temp_ev_orig:.4f} (original)")
        
        # Return metrics
        return {
            'growth_r2': growth_r2,
            'growth_rmse': growth_rmse,
            'growth_mae': growth_mae,
            'temp_r2': temp_r2,
            'temp_rmse': temp_rmse,
            'temp_mae': temp_mae,
            'growth_r2_orig': growth_r2_orig,
            'growth_rmse_orig': growth_rmse_orig,
            'growth_mae_orig': growth_mae_orig,
            'temp_r2_orig': temp_r2_orig,
            'temp_rmse_orig': temp_rmse_orig,
            'temp_mae_orig': temp_mae_orig,
            'temp_explained_variance': temp_ev,
            'temp_explained_variance_orig': temp_ev_orig
        }
    
    def predict(self, X):
        """Make predictions.
        
        Args:
            X: Input features
            
        Returns:
            Tuple of (predicted growth rates, predicted temperatures)
        """
        # Convert to numpy if needed
        X_np = X.values if hasattr(X, 'values') else X
        
        # Apply feature selection
        X_growth = X_np[:, self.growth_feature_indices]
        X_temp = X_np[:, self.temp_feature_indices]
        
        # Standardize features
        X_growth_scaled = self.growth_scaler.transform(X_growth)
        X_temp_scaled = self.temp_scaler.transform(X_temp)
        
        # Get device from models
        device = next(self.growth_model.parameters()).device
        
        # Convert to tensors and move to the same device as the models
        X_growth_tensor = torch.FloatTensor(X_growth_scaled).to(device)
        X_temp_tensor = torch.FloatTensor(X_temp_scaled).to(device)
        
        # Make predictions
        self.growth_model.eval()
        self.temp_model.eval()
        
        with torch.no_grad():
            growth_preds = self.growth_model(X_growth_tensor).cpu().numpy().flatten()
            # In eval mode, temp_model only returns predictions (no l2_loss)
            temp_preds = self.temp_model(X_temp_tensor).cpu().numpy().flatten()
        
        # Inverse transform predictions
        growth_preds_orig = self._inverse_transform(growth_preds, self.growth_transform)
        temp_preds_orig = self._inverse_transform(temp_preds, self.temp_transform)
        
        return growth_preds_orig, temp_preds_orig
    
    def save(self, output_dir):
        """Save the model.
        
        Args:
            output_dir: Directory to save the model
        """
        os.makedirs(output_dir, exist_ok=True)
        
        # Save model weights
        torch.save(self.growth_model.state_dict(), os.path.join(output_dir, 'growth_model.pt'))
        torch.save(self.temp_model.state_dict(), os.path.join(output_dir, 'temp_model.pt'))
        
        # Save scalers
        joblib.dump(self.growth_scaler, os.path.join(output_dir, 'growth_scaler.pkl'))
        joblib.dump(self.temp_scaler, os.path.join(output_dir, 'temp_scaler.pkl'))
        
        # Save feature indices
        joblib.dump(self.growth_feature_indices, os.path.join(output_dir, 'growth_feature_indices.pkl'))
        joblib.dump(self.temp_feature_indices, os.path.join(output_dir, 'temp_feature_indices.pkl'))
        
        # Save transformations
        with open(os.path.join(output_dir, 'transforms.json'), 'w') as f:
            json.dump({
                'growth_transform': self.growth_transform,
                'temp_transform': self.temp_transform
            }, f)
        
        # Save model architecture parameters
        model_params = {
            'growth_input_dim': self.growth_model.fc_layers[0].in_features,
            'growth_hidden_dims': [layer.out_features for layer in self.growth_model.fc_layers],
            'temp_input_dim': self.temp_model.input_dim,
            'temp_hidden_dims': [layer.out_features for layer in self.temp_model.cnn_dnn_layers],
            'cnn_filters': [layer.out_channels for layer in self.temp_model.cnn_layers],
            'kernel_sizes': [layer.kernel_size[0] for layer in self.temp_model.cnn_layers],
            'dropout_rate': self.temp_model.dropout.p,
            'l2_reg': self.temp_model.l2_reg,
            'use_residual': self.temp_model.use_residual
        }
        
        with open(os.path.join(output_dir, 'model_params.json'), 'w') as f:
            json.dump(model_params, f)
    
    @classmethod
    def load(cls, model_dir):
        """Load the model from directory.
        
        Args:
            model_dir: Directory containing saved model
            
        Returns:
            Loaded model
        """
        # Initialize model
        model = cls()
        
        # Load model parameters
        with open(os.path.join(model_dir, 'model_params.json'), 'r') as f:
            model_params = json.load(f)
        
        # Load transformations
        with open(os.path.join(model_dir, 'transforms.json'), 'r') as f:
            transforms = json.load(f)
        
        model.growth_transform = transforms['growth_transform']
        model.temp_transform = transforms['temp_transform']
        
        # Load feature indices
        model.growth_feature_indices = joblib.load(os.path.join(model_dir, 'growth_feature_indices.pkl'))
        model.temp_feature_indices = joblib.load(os.path.join(model_dir, 'temp_feature_indices.pkl'))
        
        # Load scalers
        model.growth_scaler = joblib.load(os.path.join(model_dir, 'growth_scaler.pkl'))
        model.temp_scaler = joblib.load(os.path.join(model_dir, 'temp_scaler.pkl'))
        
        # Initialize models
        model._initialize_models(
            growth_input_dim=model_params['growth_input_dim'],
            temp_input_dim=model_params['temp_input_dim'],
            growth_hidden_dims=tuple(model_params['growth_hidden_dims']),
            temp_hidden_dims=tuple(model_params['temp_hidden_dims']),
            cnn_filters=tuple(model_params['cnn_filters']),
            kernel_sizes=tuple(model_params['kernel_sizes']),
            dropout_rate=model_params.get('dropout_rate', 0.3),
            l2_reg=model_params.get('l2_reg', 0.001)
        )
        
        # Load model weights
        model.growth_model.load_state_dict(torch.load(os.path.join(model_dir, 'growth_model.pt'), map_location='cpu'))
        model.temp_model.load_state_dict(torch.load(os.path.join(model_dir, 'temp_model.pt'), map_location='cpu'))
        
        # Set models to eval mode
        model.growth_model.eval()
        model.temp_model.eval()
        
        model.output_dir = model_dir
        
        return model

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Train CNN+DNN hybrid model with enhanced temperature prediction')
    
    # Data arguments
    parser.add_argument('--feature_file', type=str, required=True, help='Path to feature file')
    parser.add_argument('--metadata_file', type=str, required=True, help='Path to metadata file')
    parser.add_argument('--output_dir', type=str, required=True, help='Directory to save model and results')
    
    # Feature selection arguments
    parser.add_argument('--n_growth_features', type=int, default=250, help='Number of features for growth rate prediction')
    parser.add_argument('--n_temp_features', type=int, default=1000, help='Number of features for temperature prediction')
    parser.add_argument('--feature_selection', type=str, default='combined', 
                        choices=['correlation', 'mutual_info', 'model_based', 'combined'],
                        help='Feature selection method')
    
    # Training arguments
    parser.add_argument('--batch_size', type=int, default=128, help='Batch size for training')
    parser.add_argument('--epochs', type=int, default=300, help='Number of epochs for training')
    parser.add_argument('--patience', type=int, default=30, help='Patience for early stopping')
    
    # Transformation arguments
    parser.add_argument('--growth_transform', type=str, default='log2', 
                       choices=['none', 'log', 'log2', 'log10', 'sqrt'], 
                       help='Transformation for growth rates')
    parser.add_argument('--temp_transform', type=str, default='sqrt', 
                       choices=['none', 'log', 'log2', 'log10', 'sqrt'], 
                       help='Transformation for temperatures')
    
    # Model architecture arguments
    parser.add_argument('--growth_hidden_dims', type=str, default='128,64,32', 
                       help='Hidden dimensions for growth model (comma-separated)')
    parser.add_argument('--temp_hidden_dims', type=str, default='256,192,128,64,32', 
                       help='Hidden dimensions for temperature model (comma-separated)')
    parser.add_argument('--cnn_filters', type=str, default='128,96,64,32', 
                       help='CNN filters for temperature model (comma-separated)')
    parser.add_argument('--kernel_sizes', type=str, default='3,5,7,9', 
                       help='Kernel sizes for CNN layers (comma-separated)')
    
    # Regularization arguments
    parser.add_argument('--dropout_rate', type=float, default=0.3, 
                       help='Dropout rate for regularization')
    parser.add_argument('--l2_reg', type=float, default=0.001, 
                       help='L2 regularization strength')
    
    # Curriculum learning arguments
    parser.add_argument('--curriculum', action='store_true', default=True,
                       help='Whether to use curriculum learning for temperature model')
    parser.add_argument('--no-curriculum', dest='curriculum', action='store_false',
                      help='Disable curriculum learning for temperature model')
    parser.add_argument('--curriculum_phases', type=int, default=5,
                       help='Number of curriculum phases')
    
    # Weighted loss arguments
    parser.add_argument('--weighted_loss', action='store_true', default=True,
                      help='Use weighted loss for temperature model to emphasize extreme values')
    parser.add_argument('--no-weighted_loss', dest='weighted_loss', action='store_false',
                     help='Disable weighted loss for temperature model')
    
    return parser.parse_args()

def main():
    """Main function."""
    args = parse_args()
    
    # Set up logging
    logger = logging.getLogger(__name__)
    
    # Parse comma-separated arguments
    growth_hidden_dims = tuple(map(int, args.growth_hidden_dims.split(',')))
    temp_hidden_dims = tuple(map(int, args.temp_hidden_dims.split(',')))
    cnn_filters = tuple(map(int, args.cnn_filters.split(',')))
    kernel_sizes = tuple(map(int, args.kernel_sizes.split(',')))
    
    # Load data
    features = pd.read_csv(args.feature_file, sep='\t', index_col=0)
    metadata = pd.read_csv(args.metadata_file, sep='\t', index_col=0)
    
    # Align indices
    common_indices = features.index.intersection(metadata.index)
    features = features.loc[common_indices]
    metadata = metadata.loc[common_indices]
    
    # Initialize model
    model = HybridModel(logger=logger)
    
    # Determine device
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    logger.info(f"Using device: {device}")
    
    # Display training configuration
    logger.info("\n============================================================")
    logger.info("Enhanced DeepMu Hybrid Model Training Configuration")
    logger.info("============================================================")
    logger.info(f"Feature selection method: {args.feature_selection}")
    logger.info(f"Number of growth features: {args.n_growth_features}")
    logger.info(f"Number of temperature features: {args.n_temp_features}")
    logger.info(f"Growth transformation: {args.growth_transform}")
    logger.info(f"Temperature transformation: {args.temp_transform}")
    logger.info(f"Batch size: {args.batch_size}")
    logger.info(f"Temperature model CNN filters: {cnn_filters}")
    logger.info(f"Temperature model kernel sizes: {kernel_sizes}")
    logger.info(f"Temperature model hidden dimensions: {temp_hidden_dims}")
    logger.info(f"Dropout rate: {args.dropout_rate}")
    logger.info(f"L2 regularization: {args.l2_reg}")
    logger.info(f"Using curriculum learning: {args.curriculum}")
    if args.curriculum:
        logger.info(f"Curriculum phases: {args.curriculum_phases}")
    logger.info(f"Using weighted loss for extreme values: {args.weighted_loss}")
    logger.info("============================================================\n")
    
    # Train model
    model.fit(
        features.values, 
        metadata['growth_rate'].values, 
        metadata['optimal_temperature'].values,
        n_growth_features=args.n_growth_features,
        n_temp_features=args.n_temp_features,
        batch_size=args.batch_size,
        epochs=args.epochs,
        growth_transform=args.growth_transform,
        temp_transform=args.temp_transform,
        feature_selection_method=args.feature_selection,
        device=device,
        early_stopping_patience=args.patience,
        growth_hidden_dims=growth_hidden_dims,
        temp_hidden_dims=temp_hidden_dims,
        cnn_filters=cnn_filters,
        kernel_sizes=kernel_sizes,
        dropout_rate=args.dropout_rate,
        l2_reg=args.l2_reg,
        output_dir=args.output_dir,
        use_curriculum=args.curriculum,
        curriculum_phases=args.curriculum_phases,
        use_weighted_loss=args.weighted_loss
    )

if __name__ == '__main__':
    main() 