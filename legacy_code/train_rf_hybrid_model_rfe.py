#!/usr/bin/env python3
"""
Training script for an improved RF hybrid model with Recursive Feature Elimination (RFE)
and enhanced feature interactions.

This script builds on the successful RF hybrid model but adds RFE for feature selection
and more sophisticated feature interactions to improve test set performance.
"""

import os
import sys
import argparse
import logging
import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union, Any
from sklearn.model_selection import train_test_split, cross_val_score, KFold
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.feature_selection import RFECV, RFE
from sklearn.inspection import permutation_importance
from joblib import dump, load
import itertools

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('train_rf_hybrid_rfe')

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Train an improved RF hybrid model with RFE and enhanced feature interactions')

    # Data arguments
    parser.add_argument('--metadata', required=True, help='Path to metadata TSV file')
    parser.add_argument('--feature-file', required=True, help='Path to combined features TSV file')
    parser.add_argument('--output-dir', required=True, help='Directory to save model and results')
    parser.add_argument('--metrics-dir', default=None, help='Directory to save metrics')

    # Random Forest arguments
    parser.add_argument('--n-estimators', type=int, default=200, help='Number of trees in the Random Forest')
    parser.add_argument('--max-depth', type=int, default=None, help='Maximum depth of trees in the Random Forest')
    parser.add_argument('--min-samples-split', type=int, default=2, help='Minimum samples required to split a node in Random Forest')
    parser.add_argument('--min-samples-leaf', type=int, default=1, help='Minimum samples required in a leaf node in Random Forest')

    # Feature selection arguments
    parser.add_argument('--rfe-step', type=float, default=0.2, help='Step size for RFE (fraction of features to remove at each step)')
    parser.add_argument('--growth-rate-features', type=int, default=100, help='Number of features to select for growth rate prediction')
    parser.add_argument('--optimal-temperature-features', type=int, default=100, help='Number of features to select for optimal temperature prediction')
    parser.add_argument('--interaction-depth', type=int, default=2, help='Maximum depth for feature interactions (2 for pairwise, 3 for triplets, etc.)')
    parser.add_argument('--max-interactions', type=int, default=50, help='Maximum number of interaction features to create')

    # Training arguments
    parser.add_argument('--seed', type=int, default=42, help='Random seed')
    parser.add_argument('--target', type=str, choices=['growth_rate', 'temperature', 'both'], default='both',
                        help='Target variable to predict')
    parser.add_argument('--cv-folds', type=int, default=5, help='Number of cross-validation folds')

    return parser.parse_args()

def create_taxonomy_features(metadata_df):
    """
    Create simple taxonomy features from metadata.

    Args:
        metadata_df (pd.DataFrame): DataFrame with metadata

    Returns:
        pd.DataFrame: DataFrame with taxonomy features
    """
    # Create simple taxonomy features
    taxonomy_data = []

    for _, row in metadata_df.iterrows():
        sample_id = row['genome_id']
        taxid = int(row['taxid'])

        # Create simple features based on taxid
        features = {
            'genome_id': sample_id,
            'taxid_mod_10': taxid % 10,
            'taxid_mod_100': taxid % 100,
            'taxid_mod_1000': taxid % 1000,
            'taxid_div_10': taxid // 10,
            'taxid_div_100': taxid // 100,
            'taxid_div_1000': taxid // 1000
        }

        taxonomy_data.append(features)

    # Create DataFrame
    taxonomy_df = pd.DataFrame(taxonomy_data)

    return taxonomy_df

def create_breakpoint_features(features_df):
    """
    Create enhanced breakpoint features.

    Args:
        features_df (pd.DataFrame): DataFrame with features

    Returns:
        pd.DataFrame: DataFrame with enhanced breakpoint features
    """
    # Extract breakpoint-related features
    breakpoint_cols = ['genome_id'] + [col for col in features_df.columns if
                                     'skew' in col or
                                     '_change_points' in col or
                                     '_sign_changes' in col or
                                     '_mean' in col or
                                     '_std' in col or
                                     '_min' in col or
                                     '_max' in col or
                                     '_range' in col]

    # Create a new DataFrame with only breakpoint features
    breakpoint_df = features_df[breakpoint_cols].copy()

    # Add emphasized features
    emphasized_features = {}
    for col in breakpoint_df.columns:
        if col != 'genome_id' and ('_change_points' in col or '_sign_changes' in col):
            emphasized_features[f'important_{col}'] = breakpoint_df[col] * 2.0
            logger.info(f"Emphasizing important change point feature: important_{col}")
    
    # Add all emphasized features at once to avoid fragmentation
    if emphasized_features:
        emphasized_df = pd.DataFrame(emphasized_features)
        breakpoint_df = pd.concat([breakpoint_df, emphasized_df], axis=1)

    return breakpoint_df

def enhance_codon_features(features_df):
    """
    Enhance codon-related features.

    Args:
        features_df (pd.DataFrame): DataFrame with features

    Returns:
        pd.DataFrame: DataFrame with enhanced codon features
    """
    # Create a copy of the input DataFrame
    enhanced_df = features_df.copy()

    # Identify codon-related features
    codon_cols = [col for col in features_df.columns if any(pattern in col for pattern in 
                ['CUB', 'CPB', 'CAI', 'ENC', 'HEG_', 'BG_', 'delta_', 'RSCU_',
                 'codon_bias_', 'AAAI', 'aa_bias_HEG_BP', 'MILC'])]

    logger.info(f"Found {len(codon_cols)} codon-related features")

    # Create squared features for important codon metrics
    squared_features = {}
    for col in codon_cols:
        if col != 'genome_id':
            squared_features[f'{col}_squared'] = enhanced_df[col] ** 2
            logger.info(f"Created squared feature: {col}_squared")
    
    # Add all squared features at once
    if squared_features:
        squared_df = pd.DataFrame(squared_features)
        enhanced_df = pd.concat([enhanced_df, squared_df], axis=1)

    # Create interaction features between selected codon metrics
    important_codon_cols = [col for col in codon_cols if any(pattern in col for pattern in 
                          ['CAI', 'ENC', 'delta_', 'codon_bias_', 'RSCU_'])]
    
    # Limit to first 10 to avoid explosion
    important_codon_cols = important_codon_cols[:10] if len(important_codon_cols) > 10 else important_codon_cols
    
    interaction_features = {}
    for i, col1 in enumerate(important_codon_cols):
        for col2 in important_codon_cols[i+1:]:
            interaction_features[f'{col1}_x_{col2}'] = enhanced_df[col1] * enhanced_df[col2]
            logger.info(f"Created interaction feature: {col1}_x_{col2}")
    
    # Add all interaction features at once
    if interaction_features:
        interaction_df = pd.DataFrame(interaction_features)
        enhanced_df = pd.concat([enhanced_df, interaction_df], axis=1)

    return enhanced_df

def create_advanced_interactions(X, top_features, interaction_depth=2, max_interactions=50):
    """
    Create advanced interaction features based on top features.
    
    Args:
        X (pd.DataFrame): Feature DataFrame
        top_features (List[str]): List of top feature names
        interaction_depth (int): Maximum depth for interactions (2 for pairwise, 3 for triplets, etc.)
        max_interactions (int): Maximum number of interaction features to create
        
    Returns:
        pd.DataFrame: DataFrame with interaction features
    """
    logger.info(f"Creating advanced interactions with depth {interaction_depth} for {len(top_features)} top features")
    
    # Limit number of top features to avoid combinatorial explosion
    max_top_features = 15
    if len(top_features) > max_top_features:
        logger.info(f"Limiting top features from {len(top_features)} to {max_top_features} for interactions")
        top_features = top_features[:max_top_features]
    
    interaction_features = {}
    count = 0
    
    # Generate all combinations of features up to the specified depth
    for depth in range(2, interaction_depth + 1):
        for combo in itertools.combinations(top_features, depth):
            if count >= max_interactions:
                break
                
            # Create feature name
            feature_name = "_x_".join(combo)
            
            # Create interaction feature (multiply all features in the combination)
            interaction_value = X[combo[0]].copy()
            for feature in combo[1:]:
                interaction_value *= X[feature]
            
            interaction_features[feature_name] = interaction_value
            count += 1
            
            logger.info(f"Created advanced interaction feature {count}/{max_interactions}: {feature_name}")
    
    # Create DataFrame with interaction features that preserves the index from X
    if interaction_features:
        return pd.DataFrame(interaction_features, index=X.index)
    else:
        return pd.DataFrame(index=X.index)

def perform_rfe(X, y, n_features_to_select, rf_params, cv=5):
    """
    Perform Recursive Feature Elimination (RFE) to select the best features.
    
    Args:
        X (np.ndarray): Feature matrix
        y (np.ndarray): Target vector
        n_features_to_select (int): Number of features to select
        rf_params (dict): Random Forest parameters
        cv (int): Number of cross-validation folds
        
    Returns:
        Tuple[np.ndarray, List[str]]: Selected feature matrix and feature names
    """
    logger.info(f"Performing RFE to select {n_features_to_select} features from {X.shape[1]} total features")
    
    # Create estimator
    estimator = RandomForestRegressor(
        n_estimators=rf_params['n_estimators'],
        max_depth=rf_params['max_depth'],
        min_samples_split=rf_params['min_samples_split'],
        min_samples_leaf=rf_params['min_samples_leaf'],
        random_state=42,
        n_jobs=-1
    )
    
    # Create RFE
    rfe = RFE(
        estimator=estimator,
        n_features_to_select=n_features_to_select,
        step=0.2,  # Remove 20% of features at each step
        verbose=1
    )
    
    # Fit RFE
    rfe.fit(X, y)
    
    # Get selected features
    selected_indices = np.where(rfe.support_)[0]
    
    logger.info(f"RFE selected {len(selected_indices)} features")
    
    return selected_indices

def train_model(features_df, metadata_df, output_dir, metrics_dir, target, rf_params, args):
    """
    Train a Random Forest model for the specified target with RFE and enhanced feature interactions.

    Args:
        features_df (pd.DataFrame): DataFrame with features
        metadata_df (pd.DataFrame): DataFrame with metadata
        output_dir (Path): Directory to save model and results
        metrics_dir (Path): Directory to save metrics
        target (str): Target variable to predict ('growth_rate' or 'optimal_temperature')
        rf_params (dict): Random Forest parameters
        args (argparse.Namespace): Command line arguments

    Returns:
        dict: Dictionary with training results
    """
    logger.info(f"Training Random Forest model for {target} with RFE and enhanced feature interactions")

    # Create taxonomy features
    logger.info("Creating taxonomy features")
    taxonomy_df = create_taxonomy_features(metadata_df)

    # Create breakpoint features
    logger.info("Creating breakpoint features")
    breakpoint_df = create_breakpoint_features(features_df)

    # Enhance codon features
    logger.info("Enhancing codon features")
    enhanced_codon_df = enhance_codon_features(features_df)

    # Merge all features
    logger.info("Merging all features")
    
    # Merge taxonomy features with metadata
    merged_df = pd.merge(metadata_df, taxonomy_df, on='genome_id', how='inner')
    logger.info(f"After merging taxonomy: {len(merged_df)} samples")
    
    # Merge with breakpoint features
    merged_df = pd.merge(merged_df, breakpoint_df, on='genome_id', how='left')
    logger.info(f"After merging breakpoint: {len(merged_df)} samples")
    
    # Merge with enhanced codon features
    merged_df = pd.merge(merged_df, enhanced_codon_df, on='genome_id', how='left')
    logger.info(f"After merging enhanced codon: {len(merged_df)} samples")
    
    logger.info(f"Combined dataset has {len(merged_df)} samples and {len(merged_df.columns)} features")

    # Split into features and targets
    X = merged_df.drop(['genome_id', 'growth_rate', 'optimal_temperature', 'taxid', 'genetic_code', 'kingdom'], axis=1, errors='ignore')
    
    if target == 'growth_rate':
        y = merged_df['growth_rate']
        n_features_to_select = args.growth_rate_features
    else:  # temperature
        y = merged_df['optimal_temperature']
        n_features_to_select = args.optimal_temperature_features
    
    # Drop rows with NA values to ensure consistent data
    valid_mask = ~y.isna()
    if (~valid_mask).any():
        logger.warning(f"Dropping {(~valid_mask).sum()} rows with missing target values")
        X = X.loc[valid_mask]
        y = y.loc[valid_mask]
    
    # Split into train and test sets
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    
    # Standardize features
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    # Convert back to DataFrame for feature names
    X_train_scaled_df = pd.DataFrame(X_train_scaled, columns=X_train.columns, index=X_train.index)
    X_test_scaled_df = pd.DataFrame(X_test_scaled, columns=X_test.columns, index=X_test.index)
    
    # Log statistics about standardized features
    logger.info("Feature standardization statistics:")
    codon_features = [col for col in X_train.columns if any(pattern in col for pattern in
                    ['CUB', 'CPB', 'CAI', 'ENC', 'HEG_', 'BG_', 'delta_', 'RSCU_',
                     'codon_bias_', 'AAAI', 'aa_bias_HEG_BP', 'MILC'])]
    
    for feature in codon_features[:5]:  # Log first 5 codon features
        before_mean = X_train[feature].mean()
        before_std = X_train[feature].std()
        after_mean = X_train_scaled_df[feature].mean()
        after_std = X_train_scaled_df[feature].std()
        logger.info(f"Feature {feature}: Before standardization - mean={before_mean:.4f}, std={before_std:.4f} | After - mean={after_mean:.4f}, std={after_std:.4f}")
    
    # Save scaler
    scaler_file = output_dir / f"{target}_scaler.joblib"
    dump(scaler, scaler_file)
    logger.info(f"Saved scaler to {scaler_file}")
    
    # Perform RFE to select the best features
    logger.info(f"Performing RFE to select {n_features_to_select} features")
    selected_indices = perform_rfe(X_train_scaled, y_train, n_features_to_select, rf_params, cv=args.cv_folds)
    
    # Get selected feature names
    selected_features = X_train.columns[selected_indices].tolist()
    logger.info(f"Selected {len(selected_features)} features: {selected_features[:10]}...")
    
    # Save selected features
    selected_features_file = metrics_dir / f"{target}_selected_features.json"
    with open(selected_features_file, 'w') as f:
        json.dump(selected_features, f, indent=2)
    logger.info(f"Saved selected features to {selected_features_file}")
    
    # Create a preliminary model to identify top features for interactions
    logger.info("Training preliminary model to identify top features for interactions")
    prelim_model = RandomForestRegressor(
        n_estimators=rf_params['n_estimators'],
        max_depth=rf_params['max_depth'],
        min_samples_split=rf_params['min_samples_split'],
        min_samples_leaf=rf_params['min_samples_leaf'],
        random_state=42,
        n_jobs=-1
    )
    
    # Use only selected features
    X_train_selected = X_train_scaled_df.iloc[:, selected_indices]
    X_test_selected = X_test_scaled_df.iloc[:, selected_indices]
    
    # Verify that X_train_selected and y_train have the same number of samples
    logger.info(f"X_train_selected shape: {X_train_selected.shape}, y_train shape: {y_train.shape}")
    if X_train_selected.shape[0] != y_train.shape[0]:
        logger.warning("Inconsistent sample counts detected, ensuring alignment")
        common_indices = X_train_selected.index.intersection(y_train.index)
        X_train_selected = X_train_selected.loc[common_indices]
        y_train = y_train.loc[common_indices]
        logger.info(f"After alignment: X_train_selected shape: {X_train_selected.shape}, y_train shape: {y_train.shape}")
    
    prelim_model.fit(X_train_selected, y_train)
    
    # Get feature importances
    importances = prelim_model.feature_importances_
    
    # Sort features by importance
    indices = np.argsort(importances)[::-1]
    top_features = [selected_features[i] for i in indices[:20]]  # Top 20 features
    
    logger.info(f"Top features for interactions: {top_features[:10]}...")
    
    # Create advanced interaction features
    logger.info("Creating advanced interaction features")
    interaction_features_train = create_advanced_interactions(
        X_train, 
        top_features, 
        interaction_depth=args.interaction_depth,
        max_interactions=args.max_interactions
    )
    
    interaction_features_test = create_advanced_interactions(
        X_test, 
        top_features, 
        interaction_depth=args.interaction_depth,
        max_interactions=args.max_interactions
    )
    
    # Add interaction features to selected features, ensuring index alignment
    if not interaction_features_train.empty:
        # Ensure indices match before concatenation
        common_train_indices = X_train_selected.index.intersection(interaction_features_train.index)
        X_train_selected_aligned = X_train_selected.loc[common_train_indices]
        interaction_features_train_aligned = interaction_features_train.loc[common_train_indices]
        
        X_train_enhanced = pd.concat([X_train_selected_aligned, interaction_features_train_aligned], axis=1)
        
        # Align y_train as well
        y_train = y_train.loc[common_train_indices]
        
        # Do the same for test set
        common_test_indices = X_test_selected.index.intersection(interaction_features_test.index)
        X_test_selected_aligned = X_test_selected.loc[common_test_indices]
        interaction_features_test_aligned = interaction_features_test.loc[common_test_indices]
        
        X_test_enhanced = pd.concat([X_test_selected_aligned, interaction_features_test_aligned], axis=1)
        y_test = y_test.loc[common_test_indices]
        
        logger.info(f"Added {interaction_features_train.shape[1]} interaction features")
        logger.info(f"Final shapes - X_train_enhanced: {X_train_enhanced.shape}, y_train: {y_train.shape}")
    else:
        X_train_enhanced = X_train_selected
        X_test_enhanced = X_test_selected
        logger.info("No interaction features added")
    
    # Train final model
    logger.info(f"Training final model with {X_train_enhanced.shape[1]} features")
    model = RandomForestRegressor(
        n_estimators=rf_params['n_estimators'],
        max_depth=rf_params['max_depth'],
        min_samples_split=rf_params['min_samples_split'],
        min_samples_leaf=rf_params['min_samples_leaf'],
        random_state=42,
        n_jobs=-1
    )
    
    # Verify shapes before fitting
    logger.info(f"Before fitting - X_train_enhanced: {X_train_enhanced.shape}, y_train: {y_train.shape}")
    if X_train_enhanced.shape[0] != y_train.shape[0]:
        raise ValueError(f"Inconsistent sample counts: X={X_train_enhanced.shape[0]}, y={y_train.shape[0]}")
        
    model.fit(X_train_enhanced, y_train)
    
    # Save model
    model_file = output_dir / f"{target}_model.joblib"
    dump(model, model_file)
    logger.info(f"Saved model to {model_file}")
    
    # Make predictions
    y_pred_train = model.predict(X_train_enhanced)
    y_pred_test = model.predict(X_test_enhanced)
    
    # Calculate metrics
    train_metrics = {
        'mse': mean_squared_error(y_train, y_pred_train),
        'rmse': np.sqrt(mean_squared_error(y_train, y_pred_train)),
        'mae': mean_absolute_error(y_train, y_pred_train),
        'r2': r2_score(y_train, y_pred_train)
    }
    
    test_metrics = {
        'mse': mean_squared_error(y_test, y_pred_test),
        'rmse': np.sqrt(mean_squared_error(y_test, y_pred_test)),
        'mae': mean_absolute_error(y_test, y_pred_test),
        'r2': r2_score(y_test, y_pred_test)
    }
    
    # Log metrics
    logger.info(f"{target} - Train: R²={train_metrics['r2']:.4f}, RMSE={train_metrics['rmse']:.4f}")
    logger.info(f"{target} - Test: R²={test_metrics['r2']:.4f}, RMSE={test_metrics['rmse']:.4f}")
    
    # Calculate feature importance
    logger.info("Calculating feature importance")
    
    # Get feature names (including interaction features)
    feature_names = list(X_train_enhanced.columns)
    
    # Calculate permutation importance for more reliable importance values
    logger.info("Calculating permutation importance")
    perm_importance = permutation_importance(
        model, X_test_enhanced, y_test, 
        n_repeats=10, 
        random_state=42,
        n_jobs=-1
    )
    
    # Create feature importance dataframe
    importance_df = pd.DataFrame({
        'feature': feature_names,
        'importance': perm_importance.importances_mean
    }).sort_values('importance', ascending=False)
    
    # Save feature importance
    importance_file = metrics_dir / f"{target}_feature_importance.tsv"
    importance_df.to_csv(importance_file, sep='\t', index=False)
    logger.info(f"Saved feature importance to {importance_file}")
    
    # Analyze codon feature importance
    codon_features = [f for f in feature_names if any(pattern in f for pattern in
                    ['CUB', 'CPB', 'CAI', 'ENC', 'HEG_', 'BG_', 'delta_', 'RSCU_',
                     'codon_bias_', 'AAAI', 'aa_bias_HEG_BP', 'MILC'])]
    
    codon_importance_df = importance_df[importance_df['feature'].isin(codon_features)].sort_values('importance', ascending=False)
    
    # Save codon feature importance
    codon_importance_file = metrics_dir / f"{target}_codon_feature_importance.tsv"
    codon_importance_df.to_csv(codon_importance_file, sep='\t', index=False)
    logger.info(f"Saved codon feature importance to {codon_importance_file}")
    
    # Log top codon features
    logger.info(f"Top 10 codon features for {target}:")
    for i, (feature, importance) in enumerate(zip(codon_importance_df['feature'].head(10), codon_importance_df['importance'].head(10))):
        logger.info(f"{i+1}. {feature}: {importance:.6f}")
    
    # Plot feature importance
    plt.figure(figsize=(12, 8))
    sns.barplot(x='importance', y='feature', data=importance_df.head(20))
    plt.title(f"Top 20 Important Features for {target}")
    plt.tight_layout()
    plt.savefig(metrics_dir / f"{target}_feature_importance.png", dpi=300)
    plt.close()
    
    # Plot codon feature importance
    plt.figure(figsize=(12, 8))
    sns.barplot(x='importance', y='feature', data=codon_importance_df.head(20))
    plt.title(f"Top 20 Important Codon Features for {target}")
    plt.tight_layout()
    plt.savefig(metrics_dir / f"{target}_codon_feature_importance.png", dpi=300)
    plt.close()
    
    # Create scatter plot
    plt.figure(figsize=(10, 8))
    plt.scatter(y_test, y_pred_test, alpha=0.7)
    
    # Add identity line
    min_val = min(min(y_test), min(y_pred_test))
    max_val = max(max(y_test), max(y_pred_test))
    plt.plot([min_val, max_val], [min_val, max_val], 'r--')
    
    plt.title(f"Predicted vs. True {target}")
    plt.xlabel(f"True {target}")
    plt.ylabel(f"Predicted {target}")
    
    # Add metrics text
    plt.text(0.05, 0.95, f"R² = {test_metrics['r2']:.4f}", transform=plt.gca().transAxes)
    plt.text(0.05, 0.90, f"RMSE = {test_metrics['rmse']:.4f}", transform=plt.gca().transAxes)
    plt.text(0.05, 0.85, f"MAE = {test_metrics['mae']:.4f}", transform=plt.gca().transAxes)
    
    plt.tight_layout()
    plt.savefig(metrics_dir / f"{target}_scatter_plot.png", dpi=300)
    plt.close()
    
    # Save results
    results = {
        'train_metrics': train_metrics,
        'test_metrics': test_metrics,
        'feature_importance': importance_df.to_dict('records')
    }
    
    # Save results to file
    pd.DataFrame({
        'metric': ['mse', 'rmse', 'mae', 'r2'],
        'train': [train_metrics['mse'], train_metrics['rmse'], train_metrics['mae'], train_metrics['r2']],
        'test': [test_metrics['mse'], test_metrics['rmse'], test_metrics['mae'], test_metrics['r2']]
    }).to_csv(metrics_dir / f"{target}_metrics.tsv", sep='\t', index=False)
    
    # Save predictions
    pd.DataFrame({
        'true': y_test,
        'pred': y_pred_test
    }).to_csv(metrics_dir / f"{target}_predictions.tsv", sep='\t', index=False)
    
    # Save combined metrics for the training script
    combined_metrics = {
        'train_metrics': [{
            f'{target}_mse': float(train_metrics['mse']),
            f'{target}_rmse': float(train_metrics['rmse']),
            f'{target}_mae': float(train_metrics['mae']),
            f'{target}_r2': float(train_metrics['r2'])
        }],
        'val_metrics': [{
            f'{target}_mse': float(test_metrics['mse']),
            f'{target}_rmse': float(test_metrics['rmse']),
            f'{target}_mae': float(test_metrics['mae']),
            f'{target}_r2': float(test_metrics['r2']),
            f'{target}_preds': y_pred_test.tolist(),
            f'{target}_targets': y_test.tolist()
        }]
    }
    
    return combined_metrics

def main():
    """Main function."""
    args = parse_args()
    
    # Set random seed
    np.random.seed(args.seed)
    
    # Create output directories
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    if args.metrics_dir is None:
        metrics_dir = Path(args.output_dir) / 'metrics'
    else:
        metrics_dir = Path(args.metrics_dir)
    metrics_dir.mkdir(parents=True, exist_ok=True)
    
    # Load metadata
    logger.info(f"Loading metadata from {args.metadata}...")
    metadata_df = pd.read_csv(args.metadata, sep='\t')
    logger.info(f"Loaded metadata with {len(metadata_df)} samples")
    
    # Load features
    logger.info(f"Loading features from {args.feature_file}...")
    features_df = pd.read_csv(args.feature_file, sep='\t')
    logger.info(f"Loaded features with {len(features_df)} samples and {len(features_df.columns)} features")
    
    # Random Forest parameters
    rf_params = {
        'n_estimators': args.n_estimators,
        'max_depth': args.max_depth,
        'min_samples_split': args.min_samples_split,
        'min_samples_leaf': args.min_samples_leaf
    }
    
    # Train models
    all_metrics = {}
    
    if args.target in ['growth_rate', 'both']:
        logger.info("Training model for growth rate...")
        growth_metrics = train_model(features_df, metadata_df, output_dir, metrics_dir, 'growth_rate', rf_params, args)
        all_metrics.update(growth_metrics)
    
    if args.target in ['temperature', 'both']:
        logger.info("Training model for optimal temperature...")
        temp_metrics = train_model(features_df, metadata_df, output_dir, metrics_dir, 'optimal_temperature', rf_params, args)
        
        # Merge metrics
        if 'train_metrics' in all_metrics:
            all_metrics['train_metrics'][0].update(temp_metrics['train_metrics'][0])
            all_metrics['val_metrics'][0].update(temp_metrics['val_metrics'][0])
        else:
            all_metrics.update(temp_metrics)
    
    # Save combined metrics
    metrics_file = metrics_dir / 'training_metrics.json'
    with open(metrics_file, 'w') as f:
        json.dump(all_metrics, f, indent=2)
    
    logger.info(f"Saved combined metrics to {metrics_file}")
    logger.info("Training completed successfully")

if __name__ == '__main__':
    main()
