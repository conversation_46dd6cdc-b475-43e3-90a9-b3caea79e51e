import torch
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from pathlib import Path
from deepmu.models.tabnet_model import TabNetRegressor

def test_tabnet_feature_importance():
    """Test TabNet feature importance with a simple synthetic dataset."""
    # Create synthetic data
    np.random.seed(42)
    n_samples = 1000
    n_features = 20
    
    # Generate features with varying importance
    X = np.random.randn(n_samples, n_features)
    
    # Generate target with some features being more important
    important_features = [0, 3, 7, 10, 17]
    y = X[:, important_features].sum(axis=1) + np.random.normal(0, 0.1, size=n_samples)
    
    # Convert to torch tensors
    X_tensor = torch.FloatTensor(X)
    y_tensor = torch.FloatTensor(y).view(-1, 1)
    
    # Create and train TabNet model
    model = TabNetRegressor(
        input_dim=n_features,
        output_dim=1,
        n_d=8,
        n_a=8,
        n_steps=3,
        gamma=1.3,
        n_independent=2,
        n_shared=2,
        dropout=0.1
    )
    
    # Configure simple training
    optimizer = torch.optim.Adam(model.parameters(), lr=0.01)
    criterion = torch.nn.MSELoss()
    
    # Train for a few epochs
    model.train()
    epochs = 50
    for epoch in range(epochs):
        optimizer.zero_grad()
        output, sparse_loss = model(X_tensor)
        loss = criterion(output, y_tensor) + 0.001 * sparse_loss
        loss.backward()
        optimizer.step()
        
        if (epoch + 1) % 10 == 0:
            print(f'Epoch {epoch+1}/{epochs}, Loss: {loss.item():.4f}')
    
    # Get feature importance
    model.eval()
    importance = model.feature_importance(X_tensor)
    
    # Verify feature importance is computed correctly
    print("\nFeature importance values:")
    for i, imp in enumerate(importance):
        print(f"Feature {i}: {imp:.4f}" + (" (important)" if i in important_features else ""))
    
    # Plot feature importance
    plt.figure(figsize=(10, 6))
    sorted_idx = np.argsort(importance)
    plt.barh(range(n_features), importance[sorted_idx])
    plt.yticks(range(n_features), [f"Feature {i}" for i in sorted_idx])
    plt.xlabel('Importance')
    plt.title('TabNet Feature Importance')
    plt.tight_layout()
    plt.savefig('tabnet_feature_importance_test.png')
    plt.close()
    
    # Check if important features are recognized
    sorted_features = np.argsort(importance)[::-1]
    top_n = 10  # Check top 10 features
    correctly_identified = sum(1 for f in important_features if f in sorted_features[:top_n])
    accuracy = correctly_identified / len(important_features)
    
    print(f"\nTop {top_n} features: {sorted_features[:top_n]}")
    print(f"Important features correctly identified: {correctly_identified}/{len(important_features)}")
    print(f"Accuracy: {accuracy:.2f}")
    
    return importance, accuracy > 0.6  # Success if more than 60% of important features are identified

if __name__ == '__main__':
    importance, success = test_tabnet_feature_importance()
    if success:
        print("\nSuccess! TabNet feature importance is working correctly.")
    else:
        print("\nTest failed! TabNet feature importance needs improvement.") 