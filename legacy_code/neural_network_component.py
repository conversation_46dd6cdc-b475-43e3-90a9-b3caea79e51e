#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Neural Network Component for Growth Rate Prediction.
This module provides a PyTorch neural network model with batch normalization
that can be used as part of an ensemble for growth rate prediction.
"""

import os
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import logging
from typing import Dict, List, Tuple, Optional
from sklearn.preprocessing import StandardScaler

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TabularDataset(Dataset):
    """Dataset for tabular data."""
    def __init__(self, features: np.ndarray, targets: np.ndarray):
        """
        Initialize dataset.
        
        Args:
            features: Feature matrix
            targets: Target vector
        """
        self.features = torch.tensor(features, dtype=torch.float32)
        self.targets = torch.tensor(targets, dtype=torch.float32).reshape(-1, 1)
        
    def __len__(self):
        return len(self.features)
    
    def __getitem__(self, idx):
        return self.features[idx], self.targets[idx]

class SimpleTabularNN(nn.Module):
    """
    A simple neural network for tabular data with batch normalization.
    """
    def __init__(
        self,
        input_dim: int,
        hidden_dim: int = 128,
        dropout: float = 0.3
    ):
        """
        Initialize neural network.
        
        Args:
            input_dim: Number of input features
            hidden_dim: Size of hidden layers
            dropout: Dropout rate
        """
        super().__init__()
        
        # Create MLP layers with skip connections and batch normalization
        self.layer1 = nn.Linear(input_dim, hidden_dim)
        self.bn1 = nn.BatchNorm1d(hidden_dim)
        
        self.layer2 = nn.Linear(hidden_dim, hidden_dim)
        self.bn2 = nn.BatchNorm1d(hidden_dim)
        
        self.layer3 = nn.Linear(hidden_dim, hidden_dim // 2)
        self.bn3 = nn.BatchNorm1d(hidden_dim // 2)
        
        self.output = nn.Linear(hidden_dim // 2, 1)
        
        self.dropout = nn.Dropout(dropout)
        self.activation = nn.LeakyReLU(0.1)
    
    def forward(self, x):
        """
        Forward pass.
        
        Args:
            x: Input tensor
            
        Returns:
            Output tensor
        """
        # First layer with batch norm and activation
        x1 = self.layer1(x)
        x1 = self.bn1(x1)
        x1 = self.activation(x1)
        x1 = self.dropout(x1)
        
        # Second layer with skip connection
        x2 = self.layer2(x1)
        x2 = self.bn2(x2)
        x2 = self.activation(x2)
        x2 = self.dropout(x2)
        
        # Third layer
        x3 = self.layer3(x2)
        x3 = self.bn3(x3)
        x3 = self.activation(x3)
        x3 = self.dropout(x3)
        
        # Output layer
        return self.output(x3)

class NeuralNetworkComponent:
    """
    Neural Network component for growth rate prediction.
    """
    def __init__(
        self,
        hidden_dim: int = 128,
        dropout: float = 0.3,
        lr: float = 0.001,
        batch_size: int = 64,
        epochs: int = 100,
        patience: int = 10,
        device: str = None
    ):
        """
        Initialize neural network component.
        
        Args:
            hidden_dim: Size of hidden layers
            dropout: Dropout rate
            lr: Learning rate
            batch_size: Batch size for training
            epochs: Maximum number of epochs
            patience: Patience for early stopping
            device: Device to use (cpu or cuda)
        """
        self.hidden_dim = hidden_dim
        self.dropout = dropout
        self.lr = lr
        self.batch_size = batch_size
        self.epochs = epochs
        self.patience = patience
        
        # Determine device
        if device is None:
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = torch.device(device)
            
        logger.info(f"Using device: {self.device}")
        
        # Initialize model, optimizer, and loss function
        self.model = None
        self.optimizer = None
        self.criterion = nn.MSELoss()
        
        # Initialize scaler
        self.scaler = StandardScaler()
        
    def fit(self, X: pd.DataFrame, y: pd.Series, X_val: pd.DataFrame = None, y_val: pd.Series = None) -> Dict[str, List[float]]:
        """
        Fit neural network to data.
        
        Args:
            X: Training features
            y: Training targets
            X_val: Validation features (optional)
            y_val: Validation targets (optional)
            
        Returns:
            Dictionary of training history
        """
        # Scale features
        X_scaled = self.scaler.fit_transform(X)
        
        # Convert validation data if provided
        if X_val is not None and y_val is not None:
            X_val_scaled = self.scaler.transform(X_val)
            val_dataset = TabularDataset(X_val_scaled, y_val.values)
            val_loader = DataLoader(val_dataset, batch_size=self.batch_size, shuffle=False)
        else:
            val_loader = None
        
        # Create dataset and dataloader
        train_dataset = TabularDataset(X_scaled, y.values)
        train_loader = DataLoader(train_dataset, batch_size=self.batch_size, shuffle=True)
        
        # Initialize model
        input_dim = X.shape[1]
        self.model = SimpleTabularNN(
            input_dim=input_dim,
            hidden_dim=self.hidden_dim,
            dropout=self.dropout
        ).to(self.device)
        
        # Initialize optimizer with weight decay
        self.optimizer = optim.Adam(self.model.parameters(), lr=self.lr, weight_decay=1e-5)
        
        # Learning rate scheduler
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, mode='min', factor=0.5, patience=5
        )
        
        # Training loop
        best_val_loss = float('inf')
        epochs_no_improve = 0
        history = {'train_loss': [], 'val_loss': []}
        
        logger.info(f"Starting neural network training for {self.epochs} epochs...")
        for epoch in range(self.epochs):
            # Train
            self.model.train()
            train_loss = 0.0
            
            for features, targets in train_loader:
                features, targets = features.to(self.device), targets.to(self.device)
                
                self.optimizer.zero_grad()
                outputs = self.model(features)
                loss = self.criterion(outputs, targets)
                loss.backward()
                self.optimizer.step()
                
                train_loss += loss.item() * features.size(0)
            
            train_loss /= len(train_loader.dataset)
            history['train_loss'].append(train_loss)
            
            # Validate
            if val_loader is not None:
                val_loss = self._validate(val_loader)
                history['val_loss'].append(val_loss)
                
                # Update learning rate
                scheduler.step(val_loss)
                
                # Early stopping
                if val_loss < best_val_loss:
                    best_val_loss = val_loss
                    epochs_no_improve = 0
                    # Save best model
                    self.best_model_state = self.model.state_dict().copy()
                else:
                    epochs_no_improve += 1
                
                if epochs_no_improve >= self.patience:
                    logger.info(f"Early stopping after {epoch+1} epochs without improvement")
                    break
                
                logger.info(f"Epoch {epoch+1}/{self.epochs}: Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}")
            else:
                logger.info(f"Epoch {epoch+1}/{self.epochs}: Train Loss: {train_loss:.4f}")
        
        # Load best model if validation was used
        if val_loader is not None and hasattr(self, 'best_model_state'):
            self.model.load_state_dict(self.best_model_state)
            logger.info(f"Loaded best model with validation loss: {best_val_loss:.4f}")
        
        return history
    
    def _validate(self, val_loader: DataLoader) -> float:
        """
        Validate model on validation data.
        
        Args:
            val_loader: Validation data loader
            
        Returns:
            Validation loss
        """
        self.model.eval()
        val_loss = 0.0
        
        with torch.no_grad():
            for features, targets in val_loader:
                features, targets = features.to(self.device), targets.to(self.device)
                
                outputs = self.model(features)
                loss = self.criterion(outputs, targets)
                
                val_loss += loss.item() * features.size(0)
        
        return val_loss / len(val_loader.dataset)
    
    def predict(self, X: pd.DataFrame) -> np.ndarray:
        """
        Generate predictions.
        
        Args:
            X: Features
            
        Returns:
            Predictions
        """
        if self.model is None:
            raise ValueError("Model has not been trained yet")
        
        # Scale features
        X_scaled = self.scaler.transform(X)
        
        # Convert to tensor
        X_tensor = torch.tensor(X_scaled, dtype=torch.float32).to(self.device)
        
        # Generate predictions
        self.model.eval()
        with torch.no_grad():
            predictions = self.model(X_tensor).cpu().numpy()
        
        return predictions.flatten()
    
    def save(self, output_dir: str) -> None:
        """
        Save model to disk.
        
        Args:
            output_dir: Directory to save model
        """
        if self.model is None:
            raise ValueError("Model has not been trained yet")
        
        os.makedirs(output_dir, exist_ok=True)
        
        # Save model
        torch.save(self.model.state_dict(), os.path.join(output_dir, 'nn_model.pth'))
        
        # Save scaler
        import joblib
        joblib.dump(self.scaler, os.path.join(output_dir, 'nn_scaler.joblib'))
        
        logger.info(f"Neural network model saved to {output_dir}")
    
    def load(self, input_dir: str) -> None:
        """
        Load model from disk.
        
        Args:
            input_dir: Directory to load model from
        """
        # Load scaler
        import joblib
        self.scaler = joblib.load(os.path.join(input_dir, 'nn_scaler.joblib'))
        
        # Determine input dimension from scaler
        input_dim = len(self.scaler.mean_)
        
        # Initialize model
        self.model = SimpleTabularNN(
            input_dim=input_dim,
            hidden_dim=self.hidden_dim,
            dropout=self.dropout
        ).to(self.device)
        
        # Load model weights
        self.model.load_state_dict(torch.load(
            os.path.join(input_dir, 'nn_model.pth'),
            map_location=self.device
        ))
        
        logger.info(f"Neural network model loaded from {input_dir}")
