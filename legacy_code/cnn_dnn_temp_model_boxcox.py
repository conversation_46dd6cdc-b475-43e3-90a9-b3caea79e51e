import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from scipy import stats
from scipy.special import inv_boxcox

class BoxCoxTransformer:
    """Box-Cox transformation for temperature data."""
    
    def __init__(self, lambda_param=None):
        """Initialize the Box-Cox transformer.
        
        Args:
            lambda_param: Optional lambda parameter for Box-Cox transform.
                         If None, will be estimated from data.
        """
        self.lambda_param = lambda_param
        self.offset = None  # Offset to ensure positive values
        
    def fit(self, y):
        """Fit the transformer to the data.
        
        Args:
            y: Target values to fit the transformer
        """
        # Add offset to ensure positive values
        self.offset = -np.min(y) + 1e-6
        y_shifted = y + self.offset
        
        if self.lambda_param is None:
            # Estimate lambda parameter
            self.lambda_param = stats.boxcox_normmax(y_shifted)
        
        return self
    
    def transform(self, y):
        """Transform the data using Box-Cox transformation.
        
        Args:
            y: Target values to transform
            
        Returns:
            Transformed values
        """
        y_shifted = y + self.offset
        return stats.boxcox(y_shifted, self.lambda_param)
    
    def inverse_transform(self, y_transformed):
        """Inverse transform the data.
        
        Args:
            y_transformed: Transformed values to convert back
            
        Returns:
            Original scale values
        """
        return inv_boxcox(y_transformed, self.lambda_param) - self.offset

class CNNDNNTemperatureModelBoxCox(nn.Module):
    """Enhanced CNN+DNN model for temperature prediction with Box-Cox transformation.
    
    This model:
    1. Uses Box-Cox transformation to handle skewed temperature distribution
    2. Maintains the dual-branch architecture for robust predictions
    3. Includes inverse transformation for final predictions
    """
    
    def __init__(self, input_dim, hidden_dims=(256, 128, 64, 32), cnn_filters=(128, 64, 32, 16), 
                 kernel_sizes=(3, 5, 7, 9), dropout_rate=0.3, l2_reg=0.001, use_residual=True,
                 boxcox_lambda=None):
        """Initialize the enhanced CNN+DNN temperature model with Box-Cox transformation.
        
        Args:
            input_dim: Dimension of input features
            hidden_dims: Tuple of hidden dimensions for DNN layers
            cnn_filters: Tuple of CNN filter numbers
            kernel_sizes: Tuple of kernel sizes for CNN layers
            dropout_rate: Dropout rate for regularization
            l2_reg: L2 regularization factor
            use_residual: Whether to use residual connections
            boxcox_lambda: Optional lambda parameter for Box-Cox transform
        """
        super(CNNDNNTemperatureModelBoxCox, self).__init__()
        
        # Store parameters
        self.input_dim = input_dim
        self.use_residual = use_residual
        self.l2_reg = l2_reg
        
        # Initialize Box-Cox transformer
        self.boxcox = BoxCoxTransformer(lambda_param=boxcox_lambda)
        
        # Input normalization
        self.input_norm = nn.LayerNorm(input_dim)
        
        # ======================= PRIMARY BRANCH (CNN) ===========================
        # CNN layers
        self.cnn_layers = nn.ModuleList()
        self.bn_cnn_layers = nn.ModuleList()
        self.residual_layers = nn.ModuleList() if use_residual else None
        
        # Create 1D CNN layers
        in_channels = 1
        
        for i, (filters, kernel_size) in enumerate(zip(cnn_filters, kernel_sizes)):
            padding = kernel_size // 2  # Same padding
            self.cnn_layers.append(
                nn.Conv1d(in_channels, filters, kernel_size, padding=padding)
            )
            self.bn_cnn_layers.append(nn.BatchNorm1d(filters))
            
            # Add residual connections where filter sizes match or can be projected
            if use_residual and i > 0:
                if in_channels == filters:
                    # Identity residual connection
                    self.residual_layers.append(nn.Identity())
                else:
                    # Projection residual connection
                    self.residual_layers.append(
                        nn.Conv1d(in_channels, filters, kernel_size=1)
                    )
            
            in_channels = filters
        
        # Spatial dropout for regularization
        self.spatial_dropout = nn.Dropout2d(dropout_rate)
        
        # Average pooling and max pooling
        self.avg_pool = nn.AdaptiveAvgPool1d(1)
        self.max_pool = nn.AdaptiveMaxPool1d(1)
        
        # Calculate CNN output dimension
        cnn_output_dim = cnn_filters[-1] * 2  # Combined avg and max pooling
        
        # DNN layers after CNN
        self.cnn_dnn_layers = nn.ModuleList()
        self.cnn_bn_layers = nn.ModuleList()
        
        # First DNN layer after CNN
        self.cnn_dnn_layers.append(nn.Linear(cnn_output_dim, hidden_dims[0]))
        self.cnn_bn_layers.append(nn.BatchNorm1d(hidden_dims[0]))
        
        # Hidden DNN layers
        for i in range(len(hidden_dims) - 1):
            self.cnn_dnn_layers.append(nn.Linear(hidden_dims[i], hidden_dims[i+1]))
            self.cnn_bn_layers.append(nn.BatchNorm1d(hidden_dims[i+1]))
        
        # Primary branch output
        self.cnn_output = nn.Linear(hidden_dims[-1], 1)
        
        # ====================== SPECIALIZED BRANCH (DNN) ======================
        # Direct DNN branch optimized for extreme values
        wider_dims = [int(dim * 1.5) for dim in hidden_dims]  # 50% wider network
        
        self.extreme_layers = nn.ModuleList()
        self.extreme_bn_layers = nn.ModuleList()
        
        # First layer with direct input
        self.extreme_layers.append(nn.Linear(input_dim, wider_dims[0]))
        self.extreme_bn_layers.append(nn.BatchNorm1d(wider_dims[0]))
        
        # Hidden layers
        for i in range(len(wider_dims) - 1):
            self.extreme_layers.append(nn.Linear(wider_dims[i], wider_dims[i+1]))
            self.extreme_bn_layers.append(nn.BatchNorm1d(wider_dims[i+1]))
            
        # Extreme specialized output
        self.extreme_output = nn.Linear(wider_dims[-1], 1)
        
        # ====================== RANGE EXTENSION LAYER ======================
        # Final layer that combines both branches
        self.combine_layer = nn.Linear(2, 1)  # Combines the two branch outputs
        
        # ====================== SHARED COMPONENTS ======================
        # Dropout for regularization
        self.dropout = nn.Dropout(dropout_rate)
    
    def fit_transform(self, y):
        """Fit the Box-Cox transformer and transform the target values.
        
        Args:
            y: Target values to fit and transform
            
        Returns:
            Transformed target values
        """
        return self.boxcox.fit(y).transform(y)
    
    def transform(self, y):
        """Transform target values using fitted Box-Cox transformer.
        
        Args:
            y: Target values to transform
            
        Returns:
            Transformed target values
        """
        return self.boxcox.transform(y)
    
    def inverse_transform(self, y_transformed):
        """Inverse transform predictions back to original scale.
        
        Args:
            y_transformed: Transformed predictions
            
        Returns:
            Predictions in original scale
        """
        return self.boxcox.inverse_transform(y_transformed)
            
    def forward(self, x, return_transformed=False):
        """Forward pass through the model.
        
        Args:
            x: Input tensor of shape (batch_size, input_dim)
            return_transformed: Whether to return transformed predictions
            
        Returns:
            If return_transformed:
                Transformed predictions (tensor, shape [batch_size, 1])
            Else:
                Predictions in original scale (tensor, shape [batch_size, 1])
        """
        batch_size = x.size(0)
        if torch.isnan(x).any():
            x = torch.nan_to_num(x, nan=0.0)
        x_norm = self.input_norm(x)
        x_cnn = x_norm.view(batch_size, 1, self.input_dim)
        for i, (cnn_layer, bn_layer) in enumerate(zip(self.cnn_layers, self.bn_cnn_layers)):
            identity = x_cnn
            x_cnn = cnn_layer(x_cnn)
            if torch.isnan(x_cnn).any():
                x_cnn = torch.nan_to_num(x_cnn, nan=0.0)
            x_cnn = bn_layer(x_cnn)
            x_cnn = F.relu(x_cnn)
            if self.use_residual and i > 0:
                res_layer = self.residual_layers[i-1]
                x_cnn = x_cnn + res_layer(identity)
        x_cnn = x_cnn.unsqueeze(3)
        x_cnn = self.spatial_dropout(x_cnn)
        x_cnn = x_cnn.squeeze(3)
        avg_pooled = self.avg_pool(x_cnn).view(batch_size, -1)
        max_pooled = self.max_pool(x_cnn).view(batch_size, -1)
        x_cnn = torch.cat([avg_pooled, max_pooled], dim=1)
        for i, (dnn_layer, bn_layer) in enumerate(zip(self.cnn_dnn_layers, self.cnn_bn_layers)):
            x_cnn = dnn_layer(x_cnn)
            if torch.isnan(x_cnn).any():
                x_cnn = torch.nan_to_num(x_cnn, nan=0.0)
            x_cnn = bn_layer(x_cnn)
            x_cnn = F.relu(x_cnn)
            x_cnn = self.dropout(x_cnn)
        primary_output = self.cnn_output(x_cnn)
        x_extreme = x_norm
        for i, (extreme_layer, bn_layer) in enumerate(zip(self.extreme_layers, self.extreme_bn_layers)):
            x_extreme = extreme_layer(x_extreme)
            if torch.isnan(x_extreme).any():
                x_extreme = torch.nan_to_num(x_extreme, nan=0.0)
            x_extreme = bn_layer(x_extreme)
            x_extreme = F.relu(x_extreme)
            x_extreme = self.dropout(x_extreme)
        extreme_output = self.extreme_output(x_extreme)
        combined = torch.cat([primary_output, extreme_output], dim=1)
        transformed_pred = self.combine_layer(combined)
        # Always return tensor for training (requires grad)
        if self.training or return_transformed:
            return transformed_pred
        # For evaluation, apply inverse transform (detach from graph)
        transformed_pred_np = transformed_pred.detach().cpu().numpy()
        original_pred_np = self.inverse_transform(transformed_pred_np)
        original_pred = torch.from_numpy(original_pred_np).float().to(transformed_pred.device)
        return original_pred 