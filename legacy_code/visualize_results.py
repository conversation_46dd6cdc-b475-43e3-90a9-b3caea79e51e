#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Visualize the results of the temperature model.
Creates scatter plots and error distributions for predicted vs. actual temperatures.
"""

import os
import sys
import numpy as np
import pandas as pd
import torch
import matplotlib.pyplot as plt
import seaborn as sns
import joblib
import argparse
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error

# Set style
sns.set(style='whitegrid', font_scale=1.2)
plt.rcParams['figure.figsize'] = (10, 8)

def load_data():
    """Load the dataset used for training and testing."""
    features = pd.read_csv("training_data/combined_features.tsv", sep='\t', index_col='genome_id')
    metadata = pd.read_csv("training_data/metadata.tsv", sep='\t', index_col='genome_id')
    
    # Get common samples
    common_indices = features.index.intersection(metadata.index)
    features = features.loc[common_indices]
    metadata = metadata.loc[common_indices]
    
    # Handle missing values
    features = features.fillna(0)
    
    return features, metadata

def predict_temperatures(model_dir="models/fixed_temp_model"):
    """Make predictions using the trained model."""
    # Load data
    features, metadata = load_data()
    temperatures = metadata['optimal_temperature']
    
    # Load model artifacts
    try:
        model_info = joblib.load(os.path.join(model_dir, "model_info.pkl"))
        scaler = joblib.load(os.path.join(model_dir, "scaler.pkl"))
        
        # Try to load feature indices
        try:
            feature_indices = joblib.load(os.path.join(model_dir, "feature_indices.pkl"))
        except:
            # If no feature indices, use all features
            feature_indices = None
            
        # Determine model type
        if os.path.exists(os.path.join(model_dir, "model.pt")):
            # Load the model class based on the model directory
            if 'fixed_temp_model' in model_dir:
                # Use the fixed temperature model
                from fixed_temp_model import AdvancedTemperatureModel
                model = AdvancedTemperatureModel(input_dim=model_info["input_dim"]).to(torch.device('cuda' if torch.cuda.is_available() else 'cpu'))
            else:
                # Fall back to simplified model
                from simplified_model import TemperatureModel
                model = TemperatureModel(input_dim=model_info["input_dim"]).to(torch.device('cuda' if torch.cuda.is_available() else 'cpu'))
            
            # Load trained weights
            model.load_state_dict(torch.load(os.path.join(model_dir, "model.pt")))
            model.eval()
        else:
            print(f"No model found in {model_dir}")
            sys.exit(1)
        
    except Exception as e:
        print(f"Error loading model: {e}")
        sys.exit(1)
    
    # Select top variance features if needed
    if feature_indices is not None:
        selected_features = features[feature_indices]
    else:
        feature_variance = features.var().sort_values(ascending=False)
        input_dim = model_info.get("input_dim", 800)
        selected_features = features[feature_variance.index[:input_dim].tolist()]
    
    # Split data (same as in training)
    from sklearn.model_selection import train_test_split
    X_train, X_val, y_train, y_val = train_test_split(
        selected_features, temperatures, test_size=0.2, random_state=42
    )
    
    # Scale features
    X_train_scaled = scaler.transform(X_train)
    X_val_scaled = scaler.transform(X_val)
    
    # Make predictions
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    with torch.no_grad():
        X_train_tensor = torch.FloatTensor(X_train_scaled).to(device)
        X_val_tensor = torch.FloatTensor(X_val_scaled).to(device)
        
        train_preds = model(X_train_tensor).cpu().numpy().flatten()
        val_preds = model(X_val_tensor).cpu().numpy().flatten()
    
    return {
        'train': {'true': y_train, 'pred': train_preds},
        'val': {'true': y_val, 'pred': val_preds}
    }

def visualize_results(results, output_dir="plots"):
    """Create visualizations of model performance."""
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Create figure with two subplots
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 7))
    
    # Training set results
    train_true = results['train']['true']
    train_pred = results['train']['pred']
    train_r2 = r2_score(train_true, train_pred)
    train_rmse = np.sqrt(mean_squared_error(train_true, train_pred))
    
    ax1.scatter(train_true, train_pred, alpha=0.5, c='blue')
    ax1.plot([0, 100], [0, 100], 'r--')  # Perfect prediction line
    ax1.set_xlabel('True Temperature (°C)')
    ax1.set_ylabel('Predicted Temperature (°C)')
    ax1.set_title(f'Training Set (R² = {train_r2:.4f}, RMSE = {train_rmse:.2f}°C)')
    ax1.set_xlim(0, 100)
    ax1.set_ylim(0, 100)
    
    # Validation set results
    val_true = results['val']['true']
    val_pred = results['val']['pred']
    val_r2 = r2_score(val_true, val_pred)
    val_rmse = np.sqrt(mean_squared_error(val_true, val_pred))
    
    ax2.scatter(val_true, val_pred, alpha=0.5, c='green')
    ax2.plot([0, 100], [0, 100], 'r--')  # Perfect prediction line
    ax2.set_xlabel('True Temperature (°C)')
    ax2.set_ylabel('Predicted Temperature (°C)')
    ax2.set_title(f'Validation Set (R² = {val_r2:.4f}, RMSE = {val_rmse:.2f}°C)')
    ax2.set_xlim(0, 100)
    ax2.set_ylim(0, 100)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'temperature_prediction_results.png'), dpi=300)
    print(f"Visualization saved to {os.path.join(output_dir, 'temperature_prediction_results.png')}")
    
    # Create error distribution plot
    plt.figure(figsize=(10, 6))
    
    # Calculate errors
    train_errors = train_true.values - train_pred
    val_errors = val_true.values - val_pred
    
    # Plot error distributions
    sns.histplot(train_errors, kde=True, label='Training', alpha=0.5, color='blue')
    sns.histplot(val_errors, kde=True, label='Validation', alpha=0.5, color='green')
    
    plt.axvline(x=0, color='r', linestyle='--')
    plt.title('Error Distribution (True - Predicted)')
    plt.xlabel('Error (°C)')
    plt.ylabel('Frequency')
    plt.legend()
    plt.savefig(os.path.join(output_dir, 'temperature_error_distribution.png'), dpi=300)
    print(f"Error distribution saved to {os.path.join(output_dir, 'temperature_error_distribution.png')}")
    
    # Calculate metrics
    train_rmse = np.sqrt(np.mean((train_true.values - train_pred) ** 2))
    val_rmse = np.sqrt(np.mean((val_true.values - val_pred) ** 2))
    
    train_mae = np.mean(np.abs(train_true.values - train_pred))
    val_mae = np.mean(np.abs(val_true.values - val_pred))

    # Manual R² calculation for comparison
    def manual_r2(y_true, y_pred):
        y_mean = np.mean(y_true)
        ss_total = np.sum((y_true - y_mean) ** 2)
        ss_residual = np.sum((y_true - y_pred) ** 2)
        return 1 - (ss_residual / ss_total) if ss_total > 0 else 0.0
    
    train_manual_r2 = manual_r2(train_true.values, train_pred)
    val_manual_r2 = manual_r2(val_true.values, val_pred)
    
    print("\nModel Performance Metrics:")
    print("Training Set:")
    print(f"  R² (sklearn): {train_r2:.6f}")
    print(f"  R² (manual):  {train_manual_r2:.6f}")
    print(f"  RMSE: {train_rmse:.4f}°C")
    print(f"  MAE:  {train_mae:.4f}°C")
    
    print("\nValidation Set:")
    print(f"  R² (sklearn): {val_r2:.6f}")
    print(f"  R² (manual):  {val_manual_r2:.6f}")
    print(f"  RMSE: {val_rmse:.4f}°C")
    print(f"  MAE:  {val_mae:.4f}°C")
    
    # Save metrics to a text file
    with open(os.path.join(output_dir, 'temperature_metrics.txt'), 'w') as f:
        f.write("Model Performance Metrics:\n")
        f.write("Training Set:\n")
        f.write(f"  R² (sklearn): {train_r2:.6f}\n")
        f.write(f"  R² (manual):  {train_manual_r2:.6f}\n")
        f.write(f"  RMSE: {train_rmse:.4f}°C\n")
        f.write(f"  MAE:  {train_mae:.4f}°C\n\n")
        
        f.write("Validation Set:\n")
        f.write(f"  R² (sklearn): {val_r2:.6f}\n")
        f.write(f"  R² (manual):  {val_manual_r2:.6f}\n")
        f.write(f"  RMSE: {val_rmse:.4f}°C\n")
        f.write(f"  MAE:  {val_mae:.4f}°C\n")
    
    # Create error by temperature plot
    plt.figure(figsize=(10, 6))
    plt.scatter(val_true, val_errors, alpha=0.5, c='green')
    plt.axhline(y=0, color='r', linestyle='--')
    plt.title('Prediction Error vs. True Temperature')
    plt.xlabel('True Temperature (°C)')
    plt.ylabel('Error (°C)')
    plt.savefig(os.path.join(output_dir, 'error_by_temperature.png'), dpi=300)
    print(f"Error analysis saved to {os.path.join(output_dir, 'error_by_temperature.png')}")
    
    # Additional analysis of large errors
    val_errors_abs = np.abs(val_errors)
    large_error_indices = np.argsort(val_errors_abs)[-10:]  # Top 10 largest errors
    
    print("\nTop 10 largest prediction errors:")
    for idx in large_error_indices:
        true_val = val_true.iloc[idx]
        pred_val = val_pred[idx]
        error = true_val - pred_val
        print(f"  True: {true_val:.2f}°C, Pred: {pred_val:.2f}°C, Error: {error:.2f}°C")
    
    # Save error analysis to file
    with open(os.path.join(output_dir, 'error_analysis.txt'), 'w') as f:
        f.write("Top 10 largest prediction errors:\n")
        for idx in large_error_indices:
            true_val = val_true.iloc[idx]
            pred_val = val_pred[idx]
            error = true_val - pred_val
            f.write(f"  True: {true_val:.2f}°C, Pred: {pred_val:.2f}°C, Error: {error:.2f}°C\n")

def main():
    """Main function to run visualization."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Visualize temperature model results")
    parser.add_argument("--model_dir", default="models/fixed_temp_model", 
                        help="Directory containing the trained model")
    parser.add_argument("--output_dir", default="plots", 
                        help="Directory to save visualization outputs")
    args = parser.parse_args()
    
    # Get predictions
    results = predict_temperatures(args.model_dir)
    
    # Create visualizations
    visualize_results(results, args.output_dir)

if __name__ == "__main__":
    main() 