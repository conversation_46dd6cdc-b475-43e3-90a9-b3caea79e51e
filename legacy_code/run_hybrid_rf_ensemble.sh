#!/bin/bash

# Run Hybrid Model with RF Ensemble Temperature Prediction

# Set up environment
PYTHONPATH=$(pwd):$PYTHONPATH
export PYTHONPATH

# Create output directory
OUTPUT_DIR="models/hybrid_rf_ensemble"
mkdir -p $OUTPUT_DIR

# Log start time
echo "Starting hybrid model training with RF ensemble temperature at $(date)"

# Run the model
python hybrid_rf_ensemble_model.py \
    --features training_data/combined_features.tsv \
    --metadata training_data/metadata.tsv \
    --output_dir $OUTPUT_DIR \
    --growth_model models/growth_model.joblib \
    --optimize_hp \
    --n_estimators 300 \
    --max_depth 20

# Log completion
echo "Training completed at $(date)"

# Generate summary report
echo "Generating summary report..."
python -c "
import joblib
import os
import json
import numpy as np
from sklearn.metrics import r2_score, mean_squared_error

# Load test results
results = joblib.load('$OUTPUT_DIR/test_results.joblib')

# Create report
report = ['# Hybrid Model with RF Ensemble Temperature - Evaluation Report\n\n']

# Add temperature results
if 'temperature' in results:
    report.append('## Temperature Prediction Results\n\n')
    report.append(f'- Test R²: {results[\"temperature\"][\"r2\"]:.6f}\n')
    report.append(f'- Test RMSE: {results[\"temperature\"][\"rmse\"]:.4f}°C\n')
    report.append(f'- Test MAE: {results[\"temperature\"][\"mae\"]:.4f}°C\n\n')

# Add growth results
if 'growth' in results:
    report.append('## Growth Rate Prediction Results\n\n')
    report.append(f'- Test R²: {results[\"growth\"][\"r2\"]:.6f}\n')
    report.append(f'- Test RMSE: {results[\"growth\"][\"rmse\"]:.6f}\n')
    report.append(f'- Test MAE: {results[\"growth\"][\"mae\"]:.6f}\n\n')

# Load RF ensemble weights if available
temp_model_dir = os.path.join('$OUTPUT_DIR', 'temp_model')
weights_path = os.path.join(temp_model_dir, 'ensemble_weights.joblib')

if os.path.exists(weights_path):
    weights = joblib.load(weights_path)
    
    report.append('## Temperature Model Ensemble Weights\n\n')
    report.append('| Model | Weight |\n')
    report.append('|-------|--------|\n')
    
    for model, weight in weights.items():
        report.append(f'| {model.upper()} | {weight:.4f} |\n')
    
    report.append('\n')

# Add comparison with previous models if available
report.append('## Comparison with Previous Models\n\n')
report.append('| Model | Temperature R² | Growth Rate R² |\n')
report.append('|-------|----------------|----------------|\n')

current_temp_r2 = results.get('temperature', {}).get('r2', 0)
current_growth_r2 = results.get('growth', {}).get('r2', 0)

report.append(f'| RF Ensemble Hybrid | {current_temp_r2:.4f} | {current_growth_r2:.4f} |\n')

# Try to add enhanced model comparison
enhanced_results_path = 'models/enhanced_temp_model/test_results.joblib'
if os.path.exists(enhanced_results_path):
    try:
        enhanced_results = joblib.load(enhanced_results_path)
        enhanced_temp_r2 = enhanced_results.get('temperature', {}).get('r2', 0)
        enhanced_growth_r2 = enhanced_results.get('growth', {}).get('r2', 0)
        report.append(f'| Enhanced Neural Network | {enhanced_temp_r2:.4f} | {enhanced_growth_r2:.4f} |\n')
    except:
        pass

# Try to add fixed model comparison
fixed_results_path = 'models/fixed_temp_model/test_results.joblib'
if os.path.exists(fixed_results_path):
    try:
        fixed_results = joblib.load(fixed_results_path)
        fixed_temp_r2 = fixed_results.get('temperature', {}).get('r2', 0)
        fixed_growth_r2 = fixed_results.get('growth', {}).get('r2', 0)
        report.append(f'| Fixed Neural Network | {fixed_temp_r2:.4f} | {fixed_growth_r2:.4f} |\n')
    except:
        pass

# Write report
with open('$OUTPUT_DIR/evaluation_report.md', 'w') as f:
    f.writelines(report)

print('Evaluation report generated at $OUTPUT_DIR/evaluation_report.md')
"

echo "Done!" 