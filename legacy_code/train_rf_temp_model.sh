#!/bin/bash

# DeepMu: Training Script for Random Forest Temperature Model
# This script trains a Random Forest model for temperature prediction

# Set a clean output directory
OUTPUT_DIR="models/rf_temp_model"

# Create output directory
mkdir -p "$OUTPUT_DIR"

# Define color codes for output formatting
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Display initial message
echo -e "${BLUE}${BOLD}========================================================${NC}"
echo -e "${BLUE}${BOLD}   DeepMu - Random Forest Temperature Model   ${NC}"
echo -e "${BLUE}${BOLD}========================================================${NC}"
echo -e "${GREEN}This script trains a Random Forest model for temperature prediction${NC}"
echo -e ""
echo -e "${YELLOW}Output will be saved to:${NC} ${OUTPUT_DIR}"
echo ""

# Check if prerequisites are available
echo -e "${YELLOW}Checking prerequisites...${NC}"
if [ ! -f "./training_data/combined_features.tsv" ]; then
    echo -e "${RED}Error: Feature file not found. Please ensure './training_data/combined_features.tsv' exists.${NC}"
    exit 1
fi

if [ ! -f "./training_data/metadata.tsv" ]; then
    echo -e "${RED}Error: Metadata file not found. Please ensure './training_data/metadata.tsv' exists.${NC}"
    exit 1
fi

echo -e "${GREEN}All prerequisites found.${NC}"
echo ""

# Run the Random Forest temperature model training script
python rf_temp_hybrid_model.py \
    --feature_file="./training_data/combined_features.tsv" \
    --metadata_file="./training_data/metadata.tsv" \
    --output_dir=$OUTPUT_DIR \
    --n_growth_features=250 \
    --n_temp_features=800 \
    --use_rf_temp_model \
    --rf_n_estimators=300 \
    --rf_max_depth=20 \
    --rf_min_samples_leaf=2 \
    --temp_transform=none \
    --growth_transform=log2 \
    --batch_size=64 \
    --epochs=300 \
    --patience=40 \
    --hidden_dims="640,512,384,256,192" \
    --dropout_rates="0.2,0.25,0.3,0.35,0.4" \
    --lr=0.0008 \
    --weight_decay=2e-5 \
    --use_batch_norm \
    --use_residual \
    --verbose

if [ $? -eq 0 ]; then
    echo -e "\n${GREEN}${BOLD}========================================================${NC}"
    echo -e "${GREEN}${BOLD}   Training completed successfully!                       ${NC}"
    echo -e "${GREEN}${BOLD}========================================================${NC}"
    echo -e "\n${GREEN}Random Forest temperature model saved to:${NC} $OUTPUT_DIR"
else
    echo -e "\n${RED}${BOLD}========================================================${NC}"
    echo -e "${RED}${BOLD}   Training failed!                                     ${NC}"
    echo -e "${RED}${BOLD}========================================================${NC}"
    echo -e "\n${RED}Please check the error messages above for details.${NC}"
fi

echo -e "\n${GREEN}Done.${NC}"
