#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Train and evaluate the improved growth rate model.
"""

import os
import argparse
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Union, Optional
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
import matplotlib.pyplot as plt
import seaborn as sns

# Import custom modules
from enhanced_feature_selection import select_features_ensemble
from improved_growth_rate_model import ImprovedGrowthRateModel

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_data(feature_file: str, metadata_file: str) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    Load feature and metadata files.
    
    Args:
        feature_file: Path to feature file
        metadata_file: Path to metadata file
        
    Returns:
        Tuple of (features, metadata)
    """
    logger.info(f"Loading features from {feature_file}")
    features = pd.read_csv(feature_file, sep='\t', index_col=0)
    
    logger.info(f"Loading metadata from {metadata_file}")
    metadata = pd.read_csv(metadata_file, sep='\t', index_col=0)
    
    # Fill NaN values
    features = features.fillna(0)
    metadata = metadata.fillna(0)
    
    # Get common indices
    common_indices = features.index.intersection(metadata.index)
    logger.info(f"Found {len(common_indices)} common samples")
    
    # Filter to common indices
    features = features.loc[common_indices]
    metadata = metadata.loc[common_indices]
    
    return features, metadata

def prepare_data(features: pd.DataFrame, metadata: pd.DataFrame, 
                n_features: int = 150, output_dir: Optional[str] = None) -> Tuple[pd.DataFrame, pd.Series, pd.DataFrame, pd.Series, List[str]]:
    """
    Prepare data for growth rate prediction.
    
    Args:
        features: Feature DataFrame
        metadata: Metadata DataFrame
        n_features: Number of features to select
        output_dir: Directory to save outputs
        
    Returns:
        Tuple of (X_train, y_train, X_test, y_test, selected_features)
    """
    logger.info("Preparing data for growth rate prediction...")
    
    # Extract target
    y = metadata['growth_rate']
    
    # Split data
    X_train, X_test, y_train, y_test = train_test_split(
        features, y, test_size=0.2, random_state=42
    )
    
    # Select features using ensemble of methods
    if output_dir:
        feature_output_dir = os.path.join(output_dir, 'features')
        os.makedirs(feature_output_dir, exist_ok=True)
    else:
        feature_output_dir = None
    
    selected_features = select_features_ensemble(
        X_train, y_train, n_features, feature_output_dir
    )
    
    # Filter to selected features
    X_train = X_train[selected_features]
    X_test = X_test[selected_features]
    
    logger.info(f"Prepared data for growth rate prediction with {len(selected_features)} features")
    
    return X_train, y_train, X_test, y_test, selected_features

def train_model(X_train: pd.DataFrame, y_train: pd.Series, 
               X_val: pd.DataFrame, y_val: pd.Series,
               output_dir: Optional[str] = None) -> ImprovedGrowthRateModel:
    """
    Train growth rate prediction model.
    
    Args:
        X_train: Training features
        y_train: Training target
        X_val: Validation features
        y_val: Validation target
        output_dir: Directory to save outputs
        
    Returns:
        Trained growth rate model
    """
    logger.info("Training improved growth rate model...")
    
    # Initialize model
    model = ImprovedGrowthRateModel()
    
    # Train model
    model.fit(X_train, y_train, X_val, y_val)
    
    # Evaluate model
    metrics = model.evaluate(X_val, y_val)
    
    # Save model if output_dir is provided
    if output_dir:
        model_output_dir = os.path.join(output_dir, 'model')
        os.makedirs(model_output_dir, exist_ok=True)
        
        # Save model
        model.save(model_output_dir)
        
        # Plot feature importance
        model.plot_feature_importance(model_output_dir)
        
        # Plot predictions
        model.plot_predictions(X_val, y_val, model_output_dir)
    
    return model

def evaluate_model(model: ImprovedGrowthRateModel, X_test: pd.DataFrame, y_test: pd.Series,
                  output_dir: Optional[str] = None) -> Dict[str, float]:
    """
    Evaluate model on test data.
    
    Args:
        model: Trained growth rate model
        X_test: Test features
        y_test: Test target
        output_dir: Directory to save outputs
        
    Returns:
        Dictionary of evaluation metrics
    """
    logger.info("Evaluating model on test data...")
    
    # Evaluate model
    metrics = model.evaluate(X_test, y_test)
    
    # Save metrics if output_dir is provided
    if output_dir:
        metrics_df = pd.DataFrame({
            'Metric': ['R2', 'RMSE', 'MAE'],
            'Value': [metrics['R2'], metrics['RMSE'], metrics['MAE']]
        })
        
        metrics_df.to_csv(os.path.join(output_dir, 'test_metrics.csv'), index=False)
        
        # Plot predictions
        model.plot_predictions(X_test, y_test, output_dir)
    
    return metrics

def main():
    parser = argparse.ArgumentParser(description="Train and evaluate improved growth rate model")
    parser.add_argument("--feature_file", required=True, help="Path to feature file")
    parser.add_argument("--metadata_file", required=True, help="Path to metadata file")
    parser.add_argument("--output_dir", default="models/improved_growth_rate", help="Directory to save outputs")
    parser.add_argument("--n_features", type=int, default=150, help="Number of features to select")
    args = parser.parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Load data
    features, metadata = load_data(args.feature_file, args.metadata_file)
    
    # Prepare data
    X_train, y_train, X_test, y_test, selected_features = prepare_data(
        features, metadata, args.n_features, args.output_dir
    )
    
    # Train model
    model = train_model(X_train, y_train, X_test, y_test, args.output_dir)
    
    # Evaluate model
    metrics = evaluate_model(model, X_test, y_test, args.output_dir)
    
    logger.info("Improved growth rate model training and evaluation completed")
    logger.info(f"Test metrics - R²: {metrics['R2']:.4f}, RMSE: {metrics['RMSE']:.4f}, MAE: {metrics['MAE']:.4f}")

if __name__ == "__main__":
    main()
