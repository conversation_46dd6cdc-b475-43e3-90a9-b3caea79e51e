#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Hybrid Model with Improved Temperature DNN.
This module provides a hybrid model that combines ensemble methods for growth rate prediction
with an improved DNN for temperature prediction, designed to match the standalone model's performance.
"""

import os
import numpy as np
import pandas as pd
import logging
from typing import Dict, Tuple, Optional, List
import joblib
import torch
import torch.nn as nn
import torch.optim as optim
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from sklearn.ensemble import RandomForestRegressor
import xgboost as xgb
from sklearn.feature_selection import SelectFromModel

from improved_lightgbm_component import ImprovedLightGBMComponent
from enhanced_temp_dnn_component import EnhancedTempDNNComponent

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class HybridModelWithImprovedTempDNN:
    """
    Hybrid Model that combines ensemble methods for growth rate prediction
    with an improved DNN for temperature prediction, designed to match the standalone model's performance.
    """
    def __init__(
        self,
        # Growth rate ensemble parameters
        growth_ensemble_weight_lr: float = 0.01,
        growth_ensemble_weight_epochs: int = 50,
        # Temperature DNN parameters
        temp_dnn_hidden_dims: list = [512, 512, 384, 256, 128],
        temp_dnn_dropout_rates: list = [0.3, 0.4, 0.4, 0.5, 0.5],
        temp_dnn_lr: float = 0.001,
        temp_dnn_batch_size: int = 64,
        temp_dnn_epochs: int = 200,  # Increased from 100 to 200
        temp_dnn_patience: int = 25,  # Increased from 15 to 25
        # General parameters
        variance_percentile: int = 25,
        # Feature selection parameters
        use_feature_selection: bool = True,
        feature_selection_threshold: str = 'median',
        # Temperature model specific parameters
        temp_dnn_weight_decay: float = 1e-5,
        temp_dnn_max_lr: float = 0.01,
        temp_dnn_warmup_pct: float = 0.1,
        temp_dnn_use_residual: bool = True
    ):
        """
        Initialize hybrid model with improved temperature DNN.
        
        Args:
            growth_ensemble_weight_lr: Learning rate for growth rate ensemble weight optimization
            growth_ensemble_weight_epochs: Number of epochs for growth rate ensemble weight optimization
            temp_dnn_hidden_dims: List of hidden layer dimensions for temperature DNN
            temp_dnn_dropout_rates: List of dropout rates for temperature DNN
            temp_dnn_lr: Learning rate for temperature DNN
            temp_dnn_batch_size: Batch size for temperature DNN training
            temp_dnn_epochs: Maximum number of epochs for temperature DNN
            temp_dnn_patience: Patience for early stopping in temperature DNN
            variance_percentile: Percentile threshold for variance filtering
            use_feature_selection: Whether to use feature selection
            feature_selection_threshold: Threshold for feature selection
            temp_dnn_weight_decay: Weight decay for temperature DNN
            temp_dnn_max_lr: Maximum learning rate for one-cycle scheduler
            temp_dnn_warmup_pct: Warmup percentage for one-cycle scheduler
            temp_dnn_use_residual: Whether to use residual connections in temperature DNN
        """
        self.growth_ensemble_weight_lr = growth_ensemble_weight_lr
        self.growth_ensemble_weight_epochs = growth_ensemble_weight_epochs
        self.temp_dnn_hidden_dims = temp_dnn_hidden_dims
        self.temp_dnn_dropout_rates = temp_dnn_dropout_rates
        self.temp_dnn_lr = temp_dnn_lr
        self.temp_dnn_batch_size = temp_dnn_batch_size
        self.temp_dnn_epochs = temp_dnn_epochs
        self.temp_dnn_patience = temp_dnn_patience
        self.variance_percentile = variance_percentile
        self.use_feature_selection = use_feature_selection
        self.feature_selection_threshold = feature_selection_threshold
        self.temp_dnn_weight_decay = temp_dnn_weight_decay
        self.temp_dnn_max_lr = temp_dnn_max_lr
        self.temp_dnn_warmup_pct = temp_dnn_warmup_pct
        self.temp_dnn_use_residual = temp_dnn_use_residual
        
        # Initialize growth rate ensemble models
        self.growth_rf = RandomForestRegressor(
            n_estimators=200,
            max_depth=15,
            min_samples_split=5,
            min_samples_leaf=2,
            max_features='sqrt',
            bootstrap=True,
            n_jobs=-1,
            random_state=42
        )
        
        self.growth_xgb = xgb.XGBRegressor(
            n_estimators=200,
            max_depth=6,
            learning_rate=0.05,
            subsample=0.8,
            colsample_bytree=0.8,
            reg_alpha=0.01,
            reg_lambda=1.0,
            n_jobs=-1,
            random_state=42
        )
        
        # Initialize improved LightGBM component for growth rate
        self.growth_lgb = ImprovedLightGBMComponent(
            n_estimators=200,
            learning_rate=0.05,
            max_depth=10,
            min_data_in_leaf=20,
            min_gain_to_split=0.1,
            reg_alpha=0.1,
            reg_lambda=1.0,
            subsample=0.8,
            colsample_bytree=0.8,
            random_state=42,
            verbose=-1
        )
        
        # Initialize enhanced DNN for temperature
        self.temp_dnn = EnhancedTempDNNComponent(
            hidden_dims=temp_dnn_hidden_dims,
            dropout_rates=temp_dnn_dropout_rates,
            lr=temp_dnn_lr,
            batch_size=temp_dnn_batch_size,
            epochs=temp_dnn_epochs,
            patience=temp_dnn_patience,
            use_batch_norm=True,
            use_gradient_clipping=True,
            max_grad_norm=1.0,
            lr_scheduler='one_cycle',
            warmup_epochs=int(temp_dnn_epochs * temp_dnn_warmup_pct),
            activation='relu'
        )
        
        # Initialize ensemble weights for growth rate
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # Weights for growth rate (RF, XGB, LGB)
        self.growth_weights = nn.Parameter(torch.tensor([0.33, 0.33, 0.34], dtype=torch.float32))
        
        # Initialize feature scalers
        self.feature_scaler = StandardScaler()
        
        # Initialize feature selector
        self.feature_selector = None
        
    def filter_low_variance_features(self, X: pd.DataFrame, percentile: float = 25) -> pd.DataFrame:
        """
        Filter out low-variance features.
        
        Args:
            X: Feature DataFrame
            percentile: Percentile threshold for variance filtering
            
        Returns:
            Filtered feature DataFrame
        """
        # Calculate variance for each feature
        variances = X.var()
        
        # Determine threshold
        threshold = np.percentile(variances, percentile)
        
        # Select high-variance features
        high_var_cols = variances[variances > threshold].index.tolist()
        
        logger.info(f"Removed {X.shape[1] - len(high_var_cols)} low-variance features (below {percentile}th percentile)")
        
        return X[high_var_cols]
    
    def select_features(self, X: pd.DataFrame, y: pd.Series, threshold: str = 'median') -> pd.DataFrame:
        """
        Select features using a tree-based feature selector.
        
        Args:
            X: Feature DataFrame
            y: Target Series
            threshold: Threshold for feature selection ('median', 'mean', or float)
            
        Returns:
            DataFrame with selected features
        """
        if not self.use_feature_selection:
            return X
        
        # Initialize feature selector
        selector = SelectFromModel(
            estimator=xgb.XGBRegressor(
                n_estimators=100,
                max_depth=6,
                learning_rate=0.05,
                subsample=0.8,
                colsample_bytree=0.8,
                reg_alpha=0.01,
                reg_lambda=1.0,
                n_jobs=-1,
                random_state=42
            ),
            threshold=threshold
        )
        
        # Fit selector
        selector.fit(X, y)
        
        # Get selected features
        selected_features = X.columns[selector.get_support()].tolist()
        
        logger.info(f"Selected {len(selected_features)} features using tree-based feature selector")
        
        # Store selector for later use
        self.feature_selector = selector
        
        return X[selected_features]
    
    def fit(
        self, 
        X_train: pd.DataFrame, 
        y_growth_train: pd.Series, 
        y_temp_train: pd.Series,
        X_val: pd.DataFrame, 
        y_growth_val: pd.Series, 
        y_temp_val: pd.Series,
        output_dir: Optional[str] = None
    ) -> None:
        """
        Fit hybrid model to data.
        
        Args:
            X_train: Training features
            y_growth_train: Training growth rate targets
            y_temp_train: Training temperature targets
            X_val: Validation features
            y_growth_val: Validation growth rate targets
            y_temp_val: Validation temperature targets
            output_dir: Directory to save outputs
        """
        # Step 1: Apply variance filtering
        X_train_filtered = self.filter_low_variance_features(X_train, self.variance_percentile)
        X_val_filtered = X_val[X_train_filtered.columns]
        
        # Step 2: Scale features
        X_train_scaled = self.feature_scaler.fit_transform(X_train_filtered)
        X_val_scaled = self.feature_scaler.transform(X_val_filtered)
        
        # Convert back to DataFrame
        X_train_scaled_df = pd.DataFrame(
            X_train_scaled, 
            columns=X_train_filtered.columns,
            index=X_train_filtered.index
        )
        
        X_val_scaled_df = pd.DataFrame(
            X_val_scaled, 
            columns=X_val_filtered.columns,
            index=X_val_filtered.index
        )
        
        # Step 3: Train growth rate ensemble models
        logger.info("Training growth rate ensemble models...")
        
        # Train Random Forest for growth rate
        logger.info("Training Random Forest for growth rate...")
        self.growth_rf.fit(X_train_scaled, y_growth_train)
        
        # Train XGBoost for growth rate
        logger.info("Training XGBoost for growth rate...")
        self.growth_xgb.fit(X_train_scaled, y_growth_train)
        
        # Train LightGBM for growth rate
        logger.info("Training LightGBM for growth rate...")
        # Create dummy temperature targets (not used, but required by the API)
        dummy_temp = pd.Series(np.zeros(len(y_growth_train)), index=y_growth_train.index)
        dummy_temp_val = pd.Series(np.zeros(len(y_growth_val)), index=y_growth_val.index)
        
        self.growth_lgb.fit(
            X_train_scaled_df, 
            y_growth_train, 
            dummy_temp,
            X_val_scaled_df, 
            y_growth_val, 
            dummy_temp_val
        )
        
        # Step 4: Feature selection for temperature prediction
        if self.use_feature_selection:
            logger.info("Performing feature selection for temperature prediction...")
            X_train_temp = self.select_features(X_train_scaled_df, y_temp_train, self.feature_selection_threshold)
            X_val_temp = X_val_scaled_df[X_train_temp.columns]
            logger.info(f"Selected {X_train_temp.shape[1]} features for temperature prediction")
        else:
            X_train_temp = X_train_scaled_df
            X_val_temp = X_val_scaled_df
        
        # Step 5: Train enhanced DNN for temperature
        logger.info("Training enhanced DNN for temperature...")
        
        self.temp_dnn.fit(
            X_train_temp, 
            y_temp_train,
            X_val_temp, 
            y_temp_val,
            output_dir=os.path.join(output_dir, 'temp_dnn') if output_dir else None
        )
        
        # Step 6: Optimize ensemble weights for growth rate
        logger.info("Optimizing ensemble weights for growth rate...")
        self._optimize_growth_ensemble_weights(
            X_val_scaled_df, 
            y_growth_val
        )
        
        # Save model if output_dir is provided
        if output_dir:
            self.save(output_dir)
    
    def _optimize_growth_ensemble_weights(
        self, 
        X_val: pd.DataFrame, 
        y_growth_val: pd.Series
    ) -> None:
        """
        Optimize ensemble weights for growth rate using validation data.
        
        Args:
            X_val: Validation features
            y_growth_val: Validation growth rate targets
        """
        # Get predictions from all models
        # Random Forest predictions
        rf_growth_pred = self.growth_rf.predict(X_val.values)
        
        # XGBoost predictions
        xgb_growth_pred = self.growth_xgb.predict(X_val.values)
        
        # LightGBM predictions
        lgb_growth_pred, _ = self.growth_lgb.predict(X_val)
        
        # Convert to tensors
        rf_growth_tensor = torch.tensor(rf_growth_pred, dtype=torch.float32).to(self.device)
        xgb_growth_tensor = torch.tensor(xgb_growth_pred, dtype=torch.float32).to(self.device)
        lgb_growth_tensor = torch.tensor(lgb_growth_pred, dtype=torch.float32).to(self.device)
        
        y_growth_val_tensor = torch.tensor(y_growth_val.values, dtype=torch.float32).to(self.device)
        
        # Initialize weights parameters
        growth_weights = nn.Parameter(torch.tensor([0.33, 0.33, 0.34], dtype=torch.float32).to(self.device))
        
        # Initialize optimizer
        optimizer = optim.Adam([growth_weights], lr=self.growth_ensemble_weight_lr)
        
        # Initialize loss function
        criterion = nn.MSELoss()
        
        # Training loop
        best_loss = float('inf')
        best_growth_weights = growth_weights.clone()
        
        for epoch in range(self.growth_ensemble_weight_epochs):
            # Zero gradients
            optimizer.zero_grad()
            
            # Apply softmax to ensure weights sum to 1
            normalized_growth_weights = torch.softmax(growth_weights, dim=0)
            
            # Combine predictions for growth rate
            combined_growth_pred = (
                normalized_growth_weights[0] * rf_growth_tensor +
                normalized_growth_weights[1] * xgb_growth_tensor +
                normalized_growth_weights[2] * lgb_growth_tensor
            )
            
            # Calculate loss
            loss = criterion(combined_growth_pred, y_growth_val_tensor)
            
            # Backward pass and optimization
            loss.backward()
            optimizer.step()
            
            # Track best weights
            if loss.item() < best_loss:
                best_loss = loss.item()
                best_growth_weights = normalized_growth_weights.clone().detach()
            
            if (epoch + 1) % 10 == 0:
                logger.info(f"Epoch {epoch+1}/{self.growth_ensemble_weight_epochs}: Loss: {loss.item():.6f}")
                logger.info(f"Growth weights: {normalized_growth_weights.cpu().detach().numpy()}")
        
        # Set final weights
        self.growth_weights = best_growth_weights
        
        logger.info(f"Final growth weights: {self.growth_weights.cpu().numpy()}")
        
        # Calculate R² with optimized weights
        combined_growth_pred = (
            self.growth_weights[0].item() * rf_growth_pred + 
            self.growth_weights[1].item() * xgb_growth_pred +
            self.growth_weights[2].item() * lgb_growth_pred
        )
        
        growth_r2 = r2_score(y_growth_val, combined_growth_pred)
        
        logger.info(f"Validation R² with optimized weights - Growth: {growth_r2:.4f}")
    
    def predict(self, X: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """
        Generate predictions for both growth rate and temperature.
        
        Args:
            X: Features
            
        Returns:
            Tuple of (growth_rate_predictions, temperature_predictions)
        """
        # Filter features to match training data
        common_cols = [col for col in X.columns if col in self.feature_scaler.feature_names_in_]
        X_filtered = X[common_cols]
        
        # Scale features
        X_scaled = self.feature_scaler.transform(X_filtered)
        
        # Convert to DataFrame for models that need feature names
        X_scaled_df = pd.DataFrame(
            X_scaled, 
            columns=X_filtered.columns,
            index=X_filtered.index
        )
        
        # Get predictions from Random Forest for growth rate
        rf_growth_pred = self.growth_rf.predict(X_scaled)
        
        # Get predictions from XGBoost for growth rate
        xgb_growth_pred = self.growth_xgb.predict(X_scaled)
        
        # Get predictions from LightGBM for growth rate
        lgb_growth_pred, _ = self.growth_lgb.predict(X_scaled_df)
        
        # Combine predictions for growth rate using learned weights
        growth_weights = self.growth_weights.cpu().numpy()
        
        combined_growth_pred = (
            growth_weights[0] * rf_growth_pred + 
            growth_weights[1] * xgb_growth_pred +
            growth_weights[2] * lgb_growth_pred
        )
        
        # Apply feature selection for temperature prediction if used during training
        if self.feature_selector is not None:
            X_temp = X_scaled_df.iloc[:, self.feature_selector.get_support()]
        else:
            X_temp = X_scaled_df
        
        # Get predictions from enhanced DNN for temperature
        temp_pred = self.temp_dnn.predict(X_temp)
        
        return combined_growth_pred, temp_pred
    
    def predict_growth(self, X: pd.DataFrame) -> np.ndarray:
        """
        Generate growth rate predictions only.
        
        Args:
            X: Features
            
        Returns:
            Growth rate predictions
        """
        growth_pred, _ = self.predict(X)
        return growth_pred
    
    def predict_temperature(self, X: pd.DataFrame) -> np.ndarray:
        """
        Generate temperature predictions only.
        
        Args:
            X: Features
            
        Returns:
            Temperature predictions
        """
        _, temp_pred = self.predict(X)
        return temp_pred
    
    def evaluate(
        self, 
        X: pd.DataFrame, 
        y_growth: pd.Series, 
        y_temp: pd.Series
    ) -> Dict[str, Dict[str, float]]:
        """
        Evaluate model on data.
        
        Args:
            X: Features
            y_growth: Growth rate targets
            y_temp: Temperature targets
            
        Returns:
            Dictionary of evaluation metrics for each task
        """
        # Generate predictions
        y_growth_pred, y_temp_pred = self.predict(X)
        
        # Calculate metrics for growth rate
        growth_metrics = {
            'R2': r2_score(y_growth, y_growth_pred),
            'RMSE': np.sqrt(mean_squared_error(y_growth, y_growth_pred)),
            'MAE': mean_absolute_error(y_growth, y_growth_pred)
        }
        
        # Calculate metrics for temperature
        temp_metrics = {
            'R2': r2_score(y_temp, y_temp_pred),
            'RMSE': np.sqrt(mean_squared_error(y_temp, y_temp_pred)),
            'MAE': mean_absolute_error(y_temp, y_temp_pred)
        }
        
        # Calculate overall metrics (average R2)
        overall_metrics = {
            'R2': (growth_metrics['R2'] + temp_metrics['R2']) / 2,
            'RMSE': (growth_metrics['RMSE'] + temp_metrics['RMSE']) / 2,
            'MAE': (growth_metrics['MAE'] + temp_metrics['MAE']) / 2
        }
        
        # Log metrics
        logger.info(f"Evaluation metrics - Growth Rate: R²={growth_metrics['R2']:.4f}, RMSE={growth_metrics['RMSE']:.4f}, MAE={growth_metrics['MAE']:.4f}")
        logger.info(f"Evaluation metrics - Temperature: R²={temp_metrics['R2']:.4f}, RMSE={temp_metrics['RMSE']:.4f}, MAE={temp_metrics['MAE']:.4f}")
        logger.info(f"Evaluation metrics - Overall: R²={overall_metrics['R2']:.4f}, RMSE={overall_metrics['RMSE']:.4f}, MAE={overall_metrics['MAE']:.4f}")
        
        return {
            'growth_rate': growth_metrics,
            'temperature': temp_metrics,
            'overall': overall_metrics
        }
    
    def save(self, output_dir: str) -> None:
        """
        Save model to disk.
        
        Args:
            output_dir: Directory to save model
        """
        os.makedirs(output_dir, exist_ok=True)
        
        # Save growth rate ensemble models
        rf_dir = os.path.join(output_dir, 'rf_models')
        os.makedirs(rf_dir, exist_ok=True)
        joblib.dump(self.growth_rf, os.path.join(rf_dir, 'rf_growth_model.joblib'))
        
        xgb_dir = os.path.join(output_dir, 'xgb_models')
        os.makedirs(xgb_dir, exist_ok=True)
        joblib.dump(self.growth_xgb, os.path.join(xgb_dir, 'xgb_growth_model.joblib'))
        
        lgb_dir = os.path.join(output_dir, 'lgb_models')
        os.makedirs(lgb_dir, exist_ok=True)
        self.growth_lgb.save(lgb_dir)
        
        # Save enhanced DNN for temperature
        dnn_dir = os.path.join(output_dir, 'dnn_models')
        os.makedirs(dnn_dir, exist_ok=True)
        self.temp_dnn.save(dnn_dir)
        
        # Save feature scaler
        joblib.dump(self.feature_scaler, os.path.join(output_dir, 'feature_scaler.joblib'))
        
        # Save feature selector if used
        if self.feature_selector is not None:
            joblib.dump(self.feature_selector, os.path.join(output_dir, 'feature_selector.joblib'))
        
        # Save ensemble weights
        torch.save(self.growth_weights, os.path.join(output_dir, 'growth_weights.pth'))
        
        # Save model configuration
        config = {
            'growth_ensemble_weight_lr': self.growth_ensemble_weight_lr,
            'growth_ensemble_weight_epochs': self.growth_ensemble_weight_epochs,
            'temp_dnn_hidden_dims': self.temp_dnn_hidden_dims,
            'temp_dnn_dropout_rates': self.temp_dnn_dropout_rates,
            'temp_dnn_lr': self.temp_dnn_lr,
            'temp_dnn_batch_size': self.temp_dnn_batch_size,
            'temp_dnn_epochs': self.temp_dnn_epochs,
            'temp_dnn_patience': self.temp_dnn_patience,
            'variance_percentile': self.variance_percentile,
            'use_feature_selection': self.use_feature_selection,
            'feature_selection_threshold': self.feature_selection_threshold,
            'temp_dnn_weight_decay': self.temp_dnn_weight_decay,
            'temp_dnn_max_lr': self.temp_dnn_max_lr,
            'temp_dnn_warmup_pct': self.temp_dnn_warmup_pct,
            'temp_dnn_use_residual': self.temp_dnn_use_residual
        }
        joblib.dump(config, os.path.join(output_dir, 'config.joblib'))
        
        logger.info(f"Hybrid model with improved temperature DNN saved to {output_dir}")
    
    def load(self, input_dir: str) -> None:
        """
        Load model from disk.
        
        Args:
            input_dir: Directory to load model from
        """
        # Load growth rate ensemble models
        rf_dir = os.path.join(input_dir, 'rf_models')
        self.growth_rf = joblib.load(os.path.join(rf_dir, 'rf_growth_model.joblib'))
        
        xgb_dir = os.path.join(input_dir, 'xgb_models')
        self.growth_xgb = joblib.load(os.path.join(xgb_dir, 'xgb_growth_model.joblib'))
        
        lgb_dir = os.path.join(input_dir, 'lgb_models')
        self.growth_lgb = ImprovedLightGBMComponent()
        self.growth_lgb.load(lgb_dir)
        
        # Load enhanced DNN for temperature
        dnn_dir = os.path.join(input_dir, 'dnn_models')
        self.temp_dnn = EnhancedTempDNNComponent()
        self.temp_dnn.load(dnn_dir)
        
        # Load feature scaler
        self.feature_scaler = joblib.load(os.path.join(input_dir, 'feature_scaler.joblib'))
        
        # Load feature selector if it exists
        feature_selector_path = os.path.join(input_dir, 'feature_selector.joblib')
        if os.path.exists(feature_selector_path):
            self.feature_selector = joblib.load(feature_selector_path)
        
        # Load ensemble weights
        self.growth_weights = torch.load(os.path.join(input_dir, 'growth_weights.pth'), map_location=self.device)
        
        # Load model configuration
        config = joblib.load(os.path.join(input_dir, 'config.joblib'))
        self.growth_ensemble_weight_lr = config['growth_ensemble_weight_lr']
        self.growth_ensemble_weight_epochs = config['growth_ensemble_weight_epochs']
        self.temp_dnn_hidden_dims = config['temp_dnn_hidden_dims']
        self.temp_dnn_dropout_rates = config['temp_dnn_dropout_rates']
        self.temp_dnn_lr = config['temp_dnn_lr']
        self.temp_dnn_batch_size = config['temp_dnn_batch_size']
        self.temp_dnn_epochs = config['temp_dnn_epochs']
        self.temp_dnn_patience = config['temp_dnn_patience']
        self.variance_percentile = config['variance_percentile']
        self.use_feature_selection = config.get('use_feature_selection', False)
        self.feature_selection_threshold = config.get('feature_selection_threshold', 'median')
        self.temp_dnn_weight_decay = config.get('temp_dnn_weight_decay', 1e-5)
        self.temp_dnn_max_lr = config.get('temp_dnn_max_lr', 0.01)
        self.temp_dnn_warmup_pct = config.get('temp_dnn_warmup_pct', 0.1)
        self.temp_dnn_use_residual = config.get('temp_dnn_use_residual', True)
        
        logger.info(f"Hybrid model with improved temperature DNN loaded from {input_dir}")
