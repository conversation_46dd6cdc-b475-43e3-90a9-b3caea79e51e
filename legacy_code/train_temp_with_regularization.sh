#!/bin/bash

# DeepMu: Training Script with Enhanced Regularization for Temperature Prediction
# This script implements stronger regularization techniques to address overfitting
# in the temperature prediction model.

# Set a clean output directory
OUTPUT_DIR="models/deepmu_temp_regularized"

# Create output directory
mkdir -p "$OUTPUT_DIR"

# Define color codes for output formatting
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Display initial message
echo -e "${BLUE}${BOLD}========================================================${NC}"
echo -e "${BLUE}${BOLD}   DeepMu - Temperature Model with Enhanced Regularization   ${NC}"
echo -e "${BLUE}${BOLD}========================================================${NC}"
echo -e "${GREEN}This script implements stronger regularization techniques:${NC}"
echo -e "  1. ${YELLOW}Increased L2 regularization (weight decay)${NC}"
echo -e "  2. ${YELLOW}Higher dropout rates${NC}"
echo -e "  3. ${YELLOW}Reduced model complexity${NC}"
echo -e "  4. ${YELLOW}Early stopping with patience${NC}"
echo -e ""
echo -e "${YELLOW}Output will be saved to:${NC} ${OUTPUT_DIR}"
echo ""

# Check if prerequisites are available
echo -e "${YELLOW}Checking prerequisites...${NC}"
if [ ! -f "./training_data/combined_features.tsv" ]; then
    echo -e "${RED}Error: Feature file not found. Please ensure './training_data/combined_features.tsv' exists.${NC}"
    exit 1
fi

if [ ! -f "./training_data/metadata.tsv" ]; then
    echo -e "${RED}Error: Metadata file not found. Please ensure './training_data/metadata.tsv' exists.${NC}"
    exit 1
fi

echo -e "${GREEN}All prerequisites found.${NC}"
echo ""

# Step 1: Filter the dataset to remove unreliable temperature data
echo -e "${YELLOW}Step 1: Filtering dataset to remove unreliable temperature data...${NC}"
python improved_temp_data_filter.py \
    --feature-file "./training_data/combined_features.tsv" \
    --metadata-file "./training_data/metadata.tsv" \
    --output-dir "$OUTPUT_DIR/filtered_data" \
    --reliability-threshold 0.65 \
    --prediction-error-threshold 1.8

# Check if filtering was successful
if [ $? -eq 0 ]; then
    echo -e "${GREEN}Dataset filtering completed. Using filtered datasets for training.${NC}"
    FEATURES_FILE="${OUTPUT_DIR}/filtered_data/filtered_features.tsv"
    METADATA_FILE="${OUTPUT_DIR}/filtered_data/filtered_metadata.tsv"
else
    echo -e "${RED}Error: Dataset filtering failed. Exiting.${NC}"
    exit 1
fi

# Step 2: Train the model with enhanced regularization
echo -e "${YELLOW}Step 2: Training model with enhanced regularization...${NC}"

# Set parameters for training with enhanced regularization
HIDDEN_DIMS="384,384,256,256,128"  # Reduced model complexity
DROPOUT_RATES="0.3,0.4,0.5,0.5,0.5"  # Higher dropout rates
MAX_EPOCHS=500  # Fewer epochs to prevent overfitting
PATIENCE=40  # More aggressive early stopping
BATCH_SIZE=64  # Larger batch size for more stable gradients
N_GROWTH_FEATURES=150  # Fewer features for growth rate
N_TEMP_FEATURES=800  # Fewer features for temperature to reduce overfitting
L1_LAMBDA="1e-5"  # Increased L1 regularization
WEIGHT_DECAY="5e-5"  # Increased weight decay (L2 regularization)
LEARNING_RATE="0.0005"  # Lower learning rate for better convergence

# Set environment variables for deterministic behavior
export PYTHONHASHSEED=42
export TORCH_CUDNN_DETERMINISTIC=1
export TORCH_DETERMINISTIC=1
export CUBLAS_WORKSPACE_CONFIG=:4096:8

# Run the Python script with optimized parameters
echo -e "\n${BLUE}${BOLD}========================================================${NC}"
echo -e "${BLUE}${BOLD}   Training Model with Enhanced Regularization   ${NC}"
echo -e "${BLUE}${BOLD}========================================================${NC}"

python final_hybrid_model.py \
    --feature_file "$FEATURES_FILE" \
    --metadata_file "$METADATA_FILE" \
    --output_dir "$OUTPUT_DIR" \
    --n_growth_features $N_GROWTH_FEATURES \
    --n_temp_features $N_TEMP_FEATURES \
    --seed 42 \
    --lr $LEARNING_RATE \
    --batch_size $BATCH_SIZE \
    --patience $PATIENCE \
    --epochs $MAX_EPOCHS \
    --l1_lambda $L1_LAMBDA \
    --weight_decay $WEIGHT_DECAY \
    --mse_l1_ratio 0.95 \
    --hidden_dims "$HIDDEN_DIMS" \
    --dropout_rates "$DROPOUT_RATES" \
    --activation "leaky_relu" \
    --use_batch_norm \
    --use_residual \
    --use_attention \
    --use_one_cycle_lr \
    --save_checkpoints \
    --missing_threshold 0.5 \
    --temp_transform "none" \
    --growth_transform "log2" \
    --verbose 2>&1 | tee "${OUTPUT_DIR}/training.log"

# Check if training was successful
if [ $? -eq 0 ]; then
    echo ""
    echo -e "${GREEN}${BOLD}========================================================${NC}"
    echo -e "${GREEN}${BOLD}   Training completed successfully!   ${NC}"
    echo -e "${GREEN}${BOLD}========================================================${NC}"
    echo ""
    echo -e "${GREEN}Model saved to:${NC} ${OUTPUT_DIR}"
    
else
    echo ""
    echo -e "${RED}${BOLD}========================================================${NC}"
    echo -e "${RED}${BOLD}   Training failed!   ${NC}"
    echo -e "${RED}${BOLD}========================================================${NC}"
    echo ""
    echo -e "${RED}Please check the error messages above for details.${NC}"
fi

# Step 3: Evaluate the model on the test set
echo -e "${YELLOW}Step 3: Evaluating model on the test set...${NC}"

# Extract test metrics from the log file
echo -e "${GREEN}Test Metrics:${NC}"
grep "Test Metrics:" -A 3 "${OUTPUT_DIR}/training.log"

echo ""
echo -e "${GREEN}Done.${NC}"
