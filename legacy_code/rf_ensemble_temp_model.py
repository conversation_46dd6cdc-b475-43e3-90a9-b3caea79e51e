#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Random Forest Ensemble Temperature Model for DeepMu.

This module implements an ensemble of random forest models for optimal temperature prediction
with feature importance analysis and advanced hyperparameter optimization.
"""

import os
import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestRegressor, ExtraTreesRegressor, GradientBoostingRegressor
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from sklearn.model_selection import train_test_split, KFold, RandomizedSearchCV
from sklearn.preprocessing import RobustScaler, StandardScaler
from sklearn.feature_selection import SelectFromModel
import xgboost as xgb
import lightgbm as lgb
import joblib
import logging
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, Tuple, Union, List, Optional
import time
import shap
import argparse

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RFEnsembleTemperatureModel:
    """Ensemble of tree-based models for temperature prediction."""
    
    def __init__(self, output_dir: str = "models/rf_ensemble_temp"):
        """Initialize the model.
        
        Args:
            output_dir: Directory to save model outputs
        """
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        
        # Create plots directory
        self.plots_dir = os.path.join(output_dir, "plots")
        os.makedirs(self.plots_dir, exist_ok=True)
        
        # Set up logging to file
        file_handler = logging.FileHandler(os.path.join(output_dir, "training.log"))
        file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
        logger.addHandler(file_handler)
        
        # Initialize model components
        self.models = {
            'rf': None,       # Random Forest
            'et': None,       # Extra Trees
            'gb': None,       # Gradient Boosting
            'xgb': None,      # XGBoost
            'lgb': None       # LightGBM
        }
        
        # Initialize feature processors
        self.scaler = None
        self.feature_selector = None
        self.feature_indices = None
        self.feature_importance = None
        
        # Initialize ensemble weights (will be optimized during training)
        self.ensemble_weights = {
            'rf': 0.2,
            'et': 0.2,
            'gb': 0.2,
            'xgb': 0.2,
            'lgb': 0.2
        }
        
        # For storing metrics
        self.metrics = {}
        
        # For tracking if the model is trained
        self.is_trained = False
    
    def load_data(self, feature_file: str, metadata_file: str, n_features: int = 1000) -> Tuple[pd.DataFrame, pd.Series]:
        """Load and prepare data."""
        logger.info(f"Loading features from {feature_file}")
        features = pd.read_csv(feature_file, sep='\t', index_col='genome_id')
        
        logger.info(f"Loading metadata from {metadata_file}")
        metadata = pd.read_csv(metadata_file, sep='\t', index_col='genome_id')
        
        # Get common samples
        common_indices = features.index.intersection(metadata.index)
        logger.info(f"Found {len(common_indices)} common samples")
        
        features = features.loc[common_indices]
        metadata = metadata.loc[common_indices]
        
        # Handle missing values
        features = features.fillna(0)
        
        # Select top n_features with highest variance if needed
        if n_features and features.shape[1] > n_features:
            logger.info(f"Selecting top {n_features} features by variance")
            feature_variance = features.var().sort_values(ascending=False)
            selected_features = feature_variance.index[:n_features].tolist()
            self.feature_indices = selected_features
            return features[selected_features], metadata['optimal_temperature']
        else:
            self.feature_indices = features.columns.tolist()
            return features, metadata['optimal_temperature']
    
    def prepare_training_data(self, features: pd.DataFrame, temperatures: pd.Series) -> Dict:
        """Prepare data for model training."""
        # Split data
        X_train, X_val, y_train, y_val = train_test_split(
            features, temperatures, test_size=0.2, random_state=42
        )
        
        logger.info(f"Training set: {X_train.shape[0]} samples, Validation set: {X_val.shape[0]} samples")
        
        # Scale features (optional for tree-based models but can sometimes help)
        self.scaler = RobustScaler()
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_val_scaled = self.scaler.transform(X_val)
        
        # Convert back to DataFrame to keep feature names
        X_train_scaled_df = pd.DataFrame(X_train_scaled, columns=X_train.columns, index=X_train.index)
        X_val_scaled_df = pd.DataFrame(X_val_scaled, columns=X_val.columns, index=X_val.index)
        
        return {
            "X_train": X_train,
            "X_val": X_val,
            "y_train": y_train,
            "y_val": y_val,
            "X_train_scaled": X_train_scaled_df,
            "X_val_scaled": X_val_scaled_df
        }
    
    def select_features(self, X_train: pd.DataFrame, y_train: pd.Series, max_features: int = 500) -> pd.DataFrame:
        """Select most important features using a preliminary RandomForest model."""
        logger.info(f"Selecting up to {max_features} most important features")
        
        # Train a preliminary random forest model
        preliminary_rf = RandomForestRegressor(
            n_estimators=100,
            max_depth=15,
            random_state=42,
            n_jobs=-1
        )
        
        preliminary_rf.fit(X_train, y_train)
        
        # Create feature selector
        self.feature_selector = SelectFromModel(
            estimator=preliminary_rf,
            max_features=max_features,
            threshold=-np.inf  # Keeps max_features features
        )
        
        self.feature_selector.fit(X_train, y_train)
        
        # Get selected feature indices
        feature_mask = self.feature_selector.get_support()
        selected_features = X_train.columns[feature_mask].tolist()
        
        logger.info(f"Selected {len(selected_features)} features")
        
        # Update feature indices
        self.feature_indices = selected_features
        
        return X_train[selected_features]
    
    def optimize_hyperparameters(self, X_train: pd.DataFrame, y_train: pd.Series) -> Dict:
        """Perform hyperparameter optimization for RandomForest model."""
        logger.info("Optimizing RandomForest hyperparameters")
        
        # Define parameter grid
        param_grid = {
            'n_estimators': [100, 200, 300, 500],
            'max_depth': [15, 20, 25, 30, None],
            'min_samples_split': [2, 5, 10],
            'min_samples_leaf': [1, 2, 4],
            'max_features': ['sqrt', 'log2', 0.3, 0.5, 0.7]
        }
        
        # Create cross-validation strategy
        cv = KFold(n_splits=3, shuffle=True, random_state=42)
        
        # Initialize search
        rf_search = RandomizedSearchCV(
            estimator=RandomForestRegressor(random_state=42, n_jobs=-1),
            param_distributions=param_grid,
            n_iter=20,  # Try 20 different combinations
            scoring='r2',
            cv=cv,
            verbose=1,
            random_state=42,
            n_jobs=-1
        )
        
        # Perform search
        rf_search.fit(X_train, y_train)
        
        # Log best parameters
        logger.info(f"Best RandomForest parameters: {rf_search.best_params_}")
        logger.info(f"Best RandomForest CV score: {rf_search.best_score_:.6f}")
        
        return rf_search.best_params_
    
    def train_models(self, data_dict: Dict, optimize_hp: bool = True, n_estimators: int = 300,
                    max_depth: int = 20, feature_selection: bool = True) -> Dict:
        """Train all models in the ensemble."""
        start_time = time.time()
        logger.info("Training ensemble of tree-based models")
        
        # Extract data
        X_train = data_dict["X_train"]
        y_train = data_dict["y_train"]
        X_val = data_dict["X_val"]
        y_val = data_dict["y_val"]
        
        # Apply feature selection if requested
        if feature_selection:
            X_train_selected = self.select_features(X_train, y_train)
            X_val_selected = X_val[self.feature_indices]
        else:
            X_train_selected = X_train
            X_val_selected = X_val
        
        # Optimize hyperparameters if requested
        if optimize_hp:
            best_params = self.optimize_hyperparameters(X_train_selected, y_train)
            n_estimators = best_params.get('n_estimators', n_estimators)
            max_depth = best_params.get('max_depth', max_depth)
            min_samples_split = best_params.get('min_samples_split', 5)
            min_samples_leaf = best_params.get('min_samples_leaf', 2)
            max_features = best_params.get('max_features', 'sqrt')
        else:
            min_samples_split = 5
            min_samples_leaf = 2
            max_features = 'sqrt'
        
        # Train Random Forest
        logger.info(f"Training RandomForest with {n_estimators} trees, max_depth={max_depth}")
        self.models['rf'] = RandomForestRegressor(
            n_estimators=n_estimators,
            max_depth=max_depth,
            min_samples_split=min_samples_split,
            min_samples_leaf=min_samples_leaf,
            max_features=max_features,
            random_state=42,
            n_jobs=-1
        )
        self.models['rf'].fit(X_train_selected, y_train)
        
        # Train Extra Trees
        logger.info("Training ExtraTrees")
        self.models['et'] = ExtraTreesRegressor(
            n_estimators=n_estimators,
            max_depth=max_depth,
            min_samples_split=min_samples_split,
            min_samples_leaf=min_samples_leaf,
            max_features=max_features,
            random_state=42,
            n_jobs=-1
        )
        self.models['et'].fit(X_train_selected, y_train)
        
        # Train Gradient Boosting
        logger.info("Training GradientBoosting")
        self.models['gb'] = GradientBoostingRegressor(
            n_estimators=min(200, n_estimators),  # Limit n_estimators for speed
            max_depth=min(8, max_depth),          # Limit max_depth for speed
            min_samples_split=min_samples_split,
            min_samples_leaf=min_samples_leaf,
            random_state=42
        )
        self.models['gb'].fit(X_train_selected, y_train)
        
        # Train XGBoost
        logger.info("Training XGBoost")
        self.models['xgb'] = xgb.XGBRegressor(
            n_estimators=n_estimators,
            max_depth=min(10, max_depth),
            learning_rate=0.05,
            subsample=0.8,
            colsample_bytree=0.8,
            random_state=42,
            n_jobs=-1
        )
        self.models['xgb'].fit(X_train_selected, y_train)
        
        # Train LightGBM
        logger.info("Training LightGBM")
        self.models['lgb'] = lgb.LGBMRegressor(
            n_estimators=n_estimators,
            max_depth=min(12, max_depth),
            learning_rate=0.05,
            subsample=0.8,
            colsample_bytree=0.8,
            random_state=42,
            n_jobs=-1
        )
        self.models['lgb'].fit(X_train_selected, y_train)
        
        logger.info(f"All models trained in {time.time() - start_time:.2f} seconds")
        
        # Evaluate individual models
        model_preds = {}
        model_metrics = {}
        
        for model_name, model in self.models.items():
            # Make predictions
            train_preds = model.predict(X_train_selected)
            val_preds = model.predict(X_val_selected)
            
            # Calculate metrics
            train_r2 = r2_score(y_train, train_preds)
            val_r2 = r2_score(y_val, val_preds)
            train_rmse = np.sqrt(mean_squared_error(y_train, train_preds))
            val_rmse = np.sqrt(mean_squared_error(y_val, val_preds))
            
            # Store predictions and metrics
            model_preds[model_name] = {
                "train": train_preds,
                "val": val_preds
            }
            
            model_metrics[model_name] = {
                "train_r2": train_r2,
                "val_r2": val_r2,
                "train_rmse": train_rmse,
                "val_rmse": val_rmse
            }
            
            logger.info(f"{model_name} - Train R²: {train_r2:.6f}, Val R²: {val_r2:.6f}, "
                       f"Val RMSE: {val_rmse:.4f}")
        
        # Compute feature importance
        self._compute_feature_importance(X_train_selected)
        
        # Optimize ensemble weights
        self.optimize_ensemble_weights(model_preds, y_val)
        
        # Store metrics
        self.metrics = model_metrics
        
        # Mark as trained
        self.is_trained = True
        
        return model_metrics
    
    def _compute_feature_importance(self, X_train: pd.DataFrame) -> None:
        """Compute and visualize feature importance across models."""
        logger.info("Computing feature importance")
        
        # Get feature importance from Random Forest (most reliable)
        if self.models['rf'] is not None:
            rf_importance = self.models['rf'].feature_importances_
            
            # Create DataFrame
            importance_df = pd.DataFrame({
                'Feature': X_train.columns,
                'Importance': rf_importance
            })
            
            importance_df = importance_df.sort_values('Importance', ascending=False)
            
            # Store feature importance
            self.feature_importance = importance_df
            
            # Plot top 30 most important features
            plt.figure(figsize=(12, 10))
            sns.barplot(x='Importance', y='Feature', data=importance_df.head(30))
            plt.title('Top 30 Most Important Features (Random Forest)')
            plt.tight_layout()
            plt.savefig(os.path.join(self.plots_dir, 'feature_importance.png'))
            plt.close()
            
            # Generate SHAP values for top 100 samples (for visualization only)
            try:
                top_features = importance_df.head(20)['Feature'].tolist()
                X_sample = X_train[top_features].sample(min(100, len(X_train)), random_state=42)
                
                explainer = shap.TreeExplainer(self.models['rf'])
                shap_values = explainer.shap_values(X_sample)
                
                plt.figure(figsize=(12, 8))
                shap.summary_plot(shap_values, X_sample, plot_type="bar", show=False)
                plt.tight_layout()
                plt.savefig(os.path.join(self.plots_dir, 'shap_importance.png'))
                plt.close()
                
                # Create SHAP dependency plots for top 3 features
                for i, feature in enumerate(top_features[:3]):
                    plt.figure(figsize=(10, 7))
                    shap.dependence_plot(feature, shap_values, X_sample, show=False)
                    plt.tight_layout()
                    plt.savefig(os.path.join(self.plots_dir, f'shap_dependency_{i}_{feature}.png'))
                    plt.close()
                
                logger.info(f"SHAP analysis plots saved to {self.plots_dir}")
            except Exception as e:
                logger.warning(f"SHAP analysis failed: {str(e)}")
    
    def optimize_ensemble_weights(self, model_preds: Dict, y_val: pd.Series) -> None:
        """Optimize weights for ensemble predictions using validation performance."""
        logger.info("Optimizing ensemble weights")
        
        # Get validation predictions for each model
        val_preds = {model_name: preds["val"] for model_name, preds in model_preds.items()}
        
        # Calculate R² for each model on validation set
        val_r2_scores = {model_name: r2_score(y_val, preds) for model_name, preds in val_preds.items()}
        
        # Normalize R² scores to use as weights
        total_r2 = sum(val_r2_scores.values())
        self.ensemble_weights = {model_name: score / total_r2 for model_name, score in val_r2_scores.items()}
        
        # Log weights
        logger.info("Ensemble weights:")
        for model_name, weight in self.ensemble_weights.items():
            logger.info(f"  {model_name}: {weight:.4f}")
        
        # Calculate ensemble predictions
        ensemble_val_preds = np.zeros_like(list(val_preds.values())[0])
        for model_name, preds in val_preds.items():
            ensemble_val_preds += preds * self.ensemble_weights[model_name]
        
        # Calculate ensemble metrics
        ensemble_r2 = r2_score(y_val, ensemble_val_preds)
        ensemble_rmse = np.sqrt(mean_squared_error(y_val, ensemble_val_preds))
        
        logger.info(f"Ensemble - Val R²: {ensemble_r2:.6f}, Val RMSE: {ensemble_rmse:.4f}")
        
        # Check if ensemble is better than best individual model
        best_model = max(val_r2_scores.items(), key=lambda x: x[1])
        logger.info(f"Best individual model: {best_model[0]} with R²: {best_model[1]:.6f}")
        
        if ensemble_r2 > best_model[1]:
            logger.info(f"Ensemble improves over best model by {(ensemble_r2 - best_model[1]) * 100:.4f}%")
        else:
            logger.info(f"Ensemble performs worse than best model by {(best_model[1] - ensemble_r2) * 100:.4f}%")
            logger.info("Consider using only the best model instead of ensemble")
    
    def evaluate(self, X_train: pd.DataFrame, y_train: pd.Series, 
                X_val: pd.DataFrame, y_val: pd.Series) -> Dict:
        """Evaluate the ensemble model with detailed visualizations."""
        if not self.is_trained:
            raise ValueError("Model not trained yet. Call train_models() first.")
        
        logger.info("Evaluating ensemble model")
        
        # Apply feature selection if it was used
        if self.feature_selector is not None:
            X_train_eval = X_train[self.feature_indices]
            X_val_eval = X_val[self.feature_indices]
        else:
            X_train_eval = X_train
            X_val_eval = X_val
        
        # Get predictions from each model
        train_predictions = {}
        val_predictions = {}
        
        for model_name, model in self.models.items():
            train_predictions[model_name] = model.predict(X_train_eval)
            val_predictions[model_name] = model.predict(X_val_eval)
        
        # Create ensemble predictions
        ensemble_train_preds = np.zeros_like(train_predictions[list(train_predictions.keys())[0]])
        ensemble_val_preds = np.zeros_like(val_predictions[list(val_predictions.keys())[0]])
        
        for model_name in self.models.keys():
            weight = self.ensemble_weights[model_name]
            ensemble_train_preds += train_predictions[model_name] * weight
            ensemble_val_preds += val_predictions[model_name] * weight
        
        # Calculate metrics
        metrics = {
            "ensemble": {
                "train_r2": r2_score(y_train, ensemble_train_preds),
                "val_r2": r2_score(y_val, ensemble_val_preds),
                "train_rmse": np.sqrt(mean_squared_error(y_train, ensemble_train_preds)),
                "val_rmse": np.sqrt(mean_squared_error(y_val, ensemble_val_preds)),
                "train_mae": mean_absolute_error(y_train, ensemble_train_preds),
                "val_mae": mean_absolute_error(y_val, ensemble_val_preds)
            }
        }
        
        # Log overall results
        logger.info("Final Evaluation Results:")
        logger.info(f"Ensemble - Train R²: {metrics['ensemble']['train_r2']:.6f}, "
                   f"Val R²: {metrics['ensemble']['val_r2']:.6f}")
        logger.info(f"Ensemble - Train RMSE: {metrics['ensemble']['train_rmse']:.4f}, "
                   f"Val RMSE: {metrics['ensemble']['val_rmse']:.4f}")
        
        # Plot predictions
        self._plot_predictions(y_train, ensemble_train_preds, y_val, ensemble_val_preds)
        
        # Plot model comparison
        self._plot_model_comparison(y_val, val_predictions, ensemble_val_preds)
        
        return metrics
    
    def _plot_predictions(self, y_train, train_preds, y_val, val_preds):
        """Create visualizations of model performance."""
        # Create figure with two subplots
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 7))
        
        # Training set results
        train_r2 = r2_score(y_train, train_preds)
        train_rmse = np.sqrt(mean_squared_error(y_train, train_preds))
        
        ax1.scatter(y_train, train_preds, alpha=0.5, c='blue')
        ax1.plot([0, 100], [0, 100], 'r--')  # Perfect prediction line
        ax1.set_xlabel('True Temperature (°C)')
        ax1.set_ylabel('Predicted Temperature (°C)')
        ax1.set_title(f'Training Set (R² = {train_r2:.4f}, RMSE = {train_rmse:.2f}°C)')
        ax1.set_xlim(0, 100)
        ax1.set_ylim(0, 100)
        
        # Validation set results
        val_r2 = r2_score(y_val, val_preds)
        val_rmse = np.sqrt(mean_squared_error(y_val, val_preds))
        
        ax2.scatter(y_val, val_preds, alpha=0.5, c='green')
        ax2.plot([0, 100], [0, 100], 'r--')  # Perfect prediction line
        ax2.set_xlabel('True Temperature (°C)')
        ax2.set_ylabel('Predicted Temperature (°C)')
        ax2.set_title(f'Validation Set (R² = {val_r2:.4f}, RMSE = {val_rmse:.2f}°C)')
        ax2.set_xlim(0, 100)
        ax2.set_ylim(0, 100)
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.plots_dir, 'prediction_results.png'))
        plt.close()
        
        # Create error distribution plot
        plt.figure(figsize=(10, 6))
        
        # Calculate errors
        train_errors = y_train - train_preds
        val_errors = y_val - val_preds
        
        # Plot error distributions
        sns.histplot(train_errors, kde=True, label='Training', alpha=0.5, color='blue')
        sns.histplot(val_errors, kde=True, label='Validation', alpha=0.5, color='green')
        
        plt.axvline(x=0, color='r', linestyle='--')
        plt.title('Error Distribution (True - Predicted)')
        plt.xlabel('Error (°C)')
        plt.ylabel('Frequency')
        plt.legend()
        plt.savefig(os.path.join(self.plots_dir, 'error_distribution.png'))
        plt.close()
        
        # Create error by temperature plot
        plt.figure(figsize=(10, 6))
        plt.scatter(y_val, val_errors, alpha=0.5, c='green')
        plt.axhline(y=0, color='r', linestyle='--')
        plt.title('Prediction Error vs. True Temperature')
        plt.xlabel('True Temperature (°C)')
        plt.ylabel('Error (°C)')
        plt.savefig(os.path.join(self.plots_dir, 'error_by_temperature.png'))
        plt.close()
        
        logger.info(f"Prediction analysis plots saved to {self.plots_dir}")
    
    def _plot_model_comparison(self, y_val, model_val_preds, ensemble_val_preds):
        """Plot comparison of different models in the ensemble."""
        # Calculate R² for each model
        model_r2 = {model_name: r2_score(y_val, preds) for model_name, preds in model_val_preds.items()}
        model_r2['ensemble'] = r2_score(y_val, ensemble_val_preds)
        
        # Plot R² comparison
        plt.figure(figsize=(10, 6))
        bars = plt.bar(model_r2.keys(), model_r2.values())
        plt.axhline(y=model_r2['ensemble'], color='r', linestyle='--', 
                   label=f'Ensemble R²: {model_r2["ensemble"]:.4f}')
        
        # Add values on top of bars
        for bar in bars:
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height + 0.005,
                    f'{height:.4f}', ha='center', va='bottom')
        
        plt.title('Model Comparison (Validation R²)')
        plt.ylabel('R² Score')
        plt.ylim(min(0.7, min(model_r2.values()) - 0.05), 
                max(1.0, max(model_r2.values()) + 0.05))
        plt.grid(axis='y', linestyle='--', alpha=0.7)
        plt.legend()
        plt.savefig(os.path.join(self.plots_dir, 'model_comparison.png'))
        plt.close()
        
        logger.info(f"Model comparison plot saved to {self.plots_dir}")
    
    def save(self) -> None:
        """Save the trained model and all associated artifacts."""
        if not self.is_trained:
            raise ValueError("Model not trained yet. Call train_models() first.")
        
        logger.info(f"Saving model to {self.output_dir}")
        
        # Create model directory
        os.makedirs(self.output_dir, exist_ok=True)
        
        # Save individual models
        for model_name, model in self.models.items():
            if model is not None:
                joblib.dump(model, os.path.join(self.output_dir, f"{model_name}_model.joblib"))
        
        # Save scaler
        if self.scaler is not None:
            joblib.dump(self.scaler, os.path.join(self.output_dir, "scaler.joblib"))
        
        # Save feature selector
        if self.feature_selector is not None:
            joblib.dump(self.feature_selector, os.path.join(self.output_dir, "feature_selector.joblib"))
        
        # Save feature indices
        if self.feature_indices is not None:
            joblib.dump(self.feature_indices, os.path.join(self.output_dir, "feature_indices.joblib"))
        
        # Save ensemble weights
        joblib.dump(self.ensemble_weights, os.path.join(self.output_dir, "ensemble_weights.joblib"))
        
        # Save metrics
        joblib.dump(self.metrics, os.path.join(self.output_dir, "metrics.joblib"))
        
        # Save feature importance
        if self.feature_importance is not None:
            self.feature_importance.to_csv(os.path.join(self.output_dir, "feature_importance.csv"))
        
        logger.info("Model saved successfully")
    
    def load(self, model_dir: str) -> None:
        """Load a trained model from disk."""
        logger.info(f"Loading model from {model_dir}")
        
        # Load individual models
        for model_name in self.models.keys():
            model_path = os.path.join(model_dir, f"{model_name}_model.joblib")
            if os.path.exists(model_path):
                self.models[model_name] = joblib.load(model_path)
                logger.info(f"Loaded {model_name} model")
        
        # Load scaler
        scaler_path = os.path.join(model_dir, "scaler.joblib")
        if os.path.exists(scaler_path):
            self.scaler = joblib.load(scaler_path)
        
        # Load feature selector
        feature_selector_path = os.path.join(model_dir, "feature_selector.joblib")
        if os.path.exists(feature_selector_path):
            self.feature_selector = joblib.load(feature_selector_path)
        
        # Load feature indices
        feature_indices_path = os.path.join(model_dir, "feature_indices.joblib")
        if os.path.exists(feature_indices_path):
            self.feature_indices = joblib.load(feature_indices_path)
        
        # Load ensemble weights
        weights_path = os.path.join(model_dir, "ensemble_weights.joblib")
        if os.path.exists(weights_path):
            self.ensemble_weights = joblib.load(weights_path)
        
        # Load metrics
        metrics_path = os.path.join(model_dir, "metrics.joblib")
        if os.path.exists(metrics_path):
            self.metrics = joblib.load(metrics_path)
        
        # Load feature importance
        importance_path = os.path.join(model_dir, "feature_importance.csv")
        if os.path.exists(importance_path):
            self.feature_importance = pd.read_csv(importance_path)
        
        # Set trained flag
        self.is_trained = all(model is not None for model in self.models.values())
        
        logger.info(f"Model loaded successfully. Is trained: {self.is_trained}")
    
    def predict(self, features: pd.DataFrame) -> np.ndarray:
        """Make predictions using the ensemble model."""
        if not self.is_trained:
            raise ValueError("Model not trained yet. Call train_models() first.")
        
        logger.info(f"Making predictions for {len(features)} samples")
        
        # Apply feature selection if it was used
        if self.feature_indices is not None:
            # Keep only selected features that exist in the input
            available_features = [f for f in self.feature_indices if f in features.columns]
            features = features[available_features]
            
            logger.info(f"Using {len(available_features)} of {len(self.feature_indices)} selected features")
            
            # If we're missing some features, need to handle this
            if len(available_features) < len(self.feature_indices):
                logger.warning(f"Missing {len(self.feature_indices) - len(available_features)} features")
        
        # Get predictions from each model
        model_predictions = {}
        
        for model_name, model in self.models.items():
            if model is not None:
                try:
                    model_predictions[model_name] = model.predict(features)
                except Exception as e:
                    logger.error(f"Error predicting with {model_name}: {str(e)}")
        
        # Check if we have any predictions
        if not model_predictions:
            raise ValueError("No models were able to make predictions")
        
        # Create ensemble predictions
        ensemble_preds = np.zeros_like(list(model_predictions.values())[0])
        
        # Normalize weights for available models
        available_models = list(model_predictions.keys())
        available_weights = {m: self.ensemble_weights[m] for m in available_models}
        total_weight = sum(available_weights.values())
        normalized_weights = {m: w / total_weight for m, w in available_weights.items()}
        
        for model_name, preds in model_predictions.items():
            ensemble_preds += preds * normalized_weights[model_name]
        
        return ensemble_preds

def main():
    """Main function to train the model."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Train RF ensemble temperature model")
    parser.add_argument("--features", type=str, default="training_data/combined_features.tsv", 
                        help="Path to feature file")
    parser.add_argument("--metadata", type=str, default="training_data/metadata.tsv", 
                        help="Path to metadata file")
    parser.add_argument("--output_dir", type=str, default="models/rf_ensemble_temp", 
                        help="Output directory")
    parser.add_argument("--n_features", type=int, default=1000, 
                        help="Number of top features by variance to use")
    parser.add_argument("--n_estimators", type=int, default=300, 
                        help="Number of estimators for RF models")
    parser.add_argument("--max_depth", type=int, default=20, 
                        help="Maximum depth for RF models")
    parser.add_argument("--optimize_hyperparams", type=str, default="true", 
                        help="Whether to optimize hyperparameters (true/false)")
    parser.add_argument("--feature_selection", type=str, default="true", 
                        help="Whether to perform feature selection (true/false)")
    
    args = parser.parse_args()
    
    # Convert string booleans to actual booleans
    optimize_hp = args.optimize_hyperparams.lower() == "true"
    feature_selection = args.feature_selection.lower() == "true"
    
    # Create model
    model = RFEnsembleTemperatureModel(output_dir=args.output_dir)
    
    # Load data
    features, temperatures = model.load_data(
        args.features,
        args.metadata,
        n_features=args.n_features
    )
    
    logger.info(f"Training model with {features.shape[1]} features and {len(temperatures)} samples")
    
    # Prepare data
    data = model.prepare_training_data(features, temperatures)
    
    # Train models
    model.train_models(
        data,
        optimize_hp=optimize_hp,
        n_estimators=args.n_estimators,
        max_depth=args.max_depth,
        feature_selection=feature_selection
    )
    
    # Evaluate model
    model.evaluate(
        data["X_train"],
        data["y_train"],
        data["X_val"],
        data["y_val"]
    )
    
    # Save model
    model.save()
    
    logger.info("Training complete!")

if __name__ == "__main__":
    # Configure matplotlib to use non-interactive backend
    import matplotlib
    matplotlib.use('Agg')
    
    # Set seaborn style
    import seaborn as sns
    sns.set(style='whitegrid', font_scale=1.2)
    
    main() 