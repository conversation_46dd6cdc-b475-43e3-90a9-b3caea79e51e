#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Evaluate Enhanced DNN for Temperature Prediction.
This script evaluates the enhanced DNN model for temperature prediction on the test set.
"""

import os
import argparse
import logging
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
import joblib
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RegularizedDNN(nn.Module):
    """
    Regularized Deep Neural Network with batch normalization, dropout, and gradient clipping.
    """
    def __init__(self, input_dim, hidden_dims=[512, 512, 384, 256, 128], dropout_rates=[0.3, 0.4, 0.4, 0.5, 0.5], 
                 use_batch_norm=True, activation='relu'):
        super(RegularizedDNN, self).__init__()
        
        # Create layers
        layers = []
        prev_dim = input_dim
        
        # Choose activation function
        if activation == 'relu':
            act_fn = nn.ReLU()
        elif activation == 'leaky_relu':
            act_fn = nn.LeakyReLU(0.1)
        elif activation == 'elu':
            act_fn = nn.ELU()
        else:
            act_fn = nn.ReLU()
        
        # Build network with progressively increasing dropout
        for i, hidden_dim in enumerate(hidden_dims):
            # Linear layer
            layers.append(nn.Linear(prev_dim, hidden_dim))
            
            # Batch normalization (before activation)
            if use_batch_norm:
                layers.append(nn.BatchNorm1d(hidden_dim))
            
            # Activation
            layers.append(act_fn)
            
            # Dropout with increasing rate for deeper layers
            dropout_rate = dropout_rates[min(i, len(dropout_rates)-1)]
            layers.append(nn.Dropout(dropout_rate))
            
            prev_dim = hidden_dim
        
        self.feature_extractor = nn.Sequential(*layers)
        
        # Output layer with no activation or dropout
        self.output_layer = nn.Linear(prev_dim, 1)
        
        # Initialize weights with improved method
        self._init_weights()
    
    def _init_weights(self):
        """Initialize weights with improved initialization."""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                # Use Xavier/Glorot initialization for better stability
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
    
    def forward(self, x):
        features = self.feature_extractor(x)
        output = self.output_layer(features)
        return output.squeeze()

# Define dataset class
class FeatureDataset(Dataset):
    def __init__(self, features, targets):
        self.features = torch.tensor(features, dtype=torch.float32)
        self.targets = torch.tensor(targets, dtype=torch.float32)
    
    def __len__(self):
        return len(self.features)
    
    def __getitem__(self, idx):
        return self.features[idx], self.targets[idx]

def load_data(feature_file, metadata_file):
    """
    Load and prepare feature and metadata files.
    
    Args:
        feature_file: Path to the combined feature file (TSV)
        metadata_file: Path to the metadata file (TSV)
        
    Returns:
        Tuple of (features_df, metadata_df)
    """
    logger.info(f"Loading features from {feature_file}")
    features_df = pd.read_csv(feature_file, sep='\t', index_col='genome_id')
    
    # Check for NaN values in features
    nan_count = features_df.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in features. Filling with 0.")
        features_df = features_df.fillna(0)
    
    # Check for infinite values
    inf_count = np.isinf(features_df.values).sum()
    if inf_count > 0:
        logger.warning(f"Found {inf_count} infinite values in features. Replacing with max/min values.")
        features_df = features_df.replace([np.inf, -np.inf], np.nan)
        features_df = features_df.fillna(features_df.max())

    logger.info(f"Loading metadata from {metadata_file}")
    metadata_df = pd.read_csv(metadata_file, sep='\t', index_col='genome_id')
    
    # Check for NaN values in metadata
    nan_count = metadata_df.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in metadata. Filling with 0.")
        metadata_df = metadata_df.fillna(0)

    # Get common genomes
    common_genomes = list(set(features_df.index) & set(metadata_df.index))
    logger.info(f"Found {len(common_genomes)} common genomes")

    # Filter to common genomes
    features_df = features_df.loc[common_genomes]
    metadata_df = metadata_df.loc[common_genomes]
    
    return features_df, metadata_df

def evaluate_model(model_dir, feature_file, metadata_file, output_dir=None, batch_size=64):
    """
    Evaluate the enhanced DNN model for temperature prediction on the test set.
    
    Args:
        model_dir: Directory containing the trained model
        feature_file: Path to combined feature file (TSV)
        metadata_file: Path to metadata file (TSV)
        output_dir: Directory to save evaluation results
        batch_size: Batch size for evaluation
        
    Returns:
        Dictionary with evaluation results
    """
    logger.info(f"Evaluating enhanced DNN model for temperature prediction")
    
    # Create output directory if provided
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
    
    # Load and prepare data
    features_df, metadata_df = load_data(feature_file, metadata_file)
    
    # Get target variable (temperature)
    y = metadata_df['optimal_temperature'].values
    target_name = 'Optimal Temperature'
    logger.info(f"Temperature statistics - Min: {y.min():.4f}, Max: {y.max():.4f}, Mean: {y.mean():.4f}")
    
    # Get feature matrix - exclude metadata columns
    X = features_df.drop(['growth_rate', 'optimal_temperature', 'taxid', 'codon_table', 'kingdom'], 
                     axis=1, errors='ignore').values
    feature_names = features_df.drop(['growth_rate', 'optimal_temperature', 'taxid', 'codon_table', 'kingdom'], 
                                 axis=1, errors='ignore').columns.tolist()
    
    # Check for NaN values in features
    if np.isnan(X).any():
        logger.warning(f"Found NaN values in feature matrix. Filling with 0.")
        X = np.nan_to_num(X, 0)
    
    # Check for infinite values
    if np.isinf(X).any():
        logger.warning(f"Found infinite values in feature matrix. Replacing with max/min values.")
        X = np.nan_to_num(X, np.nan)
        X = np.nan_to_num(X, np.nanmax(X))
    
    # Load scaler
    scaler_path = os.path.join(model_dir, 'temperature_scaler.joblib')
    scaler = joblib.load(scaler_path)
    logger.info(f"Loaded scaler from {scaler_path}")
    
    # Standardize features
    X_scaled = scaler.transform(X)
    
    # Split data into train, validation, and test sets with stratification
    # For regression, we create bins for stratification
    if len(np.unique(y)) > 10:  # If it's a continuous variable with many unique values
        bins = pd.qcut(y, 5, labels=False, duplicates='drop')
    else:  # For discrete variables or those with few unique values
        bins = y  # Use the values directly for stratification
    
    # First split into train+val and test
    X_train_val, X_test, y_train_val, y_test, bins_train_val, _ = train_test_split(
        X_scaled, y, bins, test_size=0.2, random_state=42, stratify=bins
    )
    
    # Then split train+val into train and validation
    X_train, X_val, y_train, y_val = train_test_split(
        X_train_val, y_train_val, test_size=0.25, random_state=42, stratify=bins_train_val
    )
    
    # Log data split statistics
    logger.info(f"Training set: {X_train.shape[0]} samples")
    logger.info(f"Validation set: {X_val.shape[0]} samples")
    logger.info(f"Test set: {X_test.shape[0]} samples")
    logger.info(f"Training target statistics - Min: {y_train.min():.4f}, Max: {y_train.max():.4f}, Mean: {y_train.mean():.4f}")
    logger.info(f"Validation target statistics - Min: {y_val.min():.4f}, Max: {y_val.max():.4f}, Mean: {y_val.mean():.4f}")
    logger.info(f"Test target statistics - Min: {y_test.min():.4f}, Max: {y_test.max():.4f}, Mean: {y_test.mean():.4f}")
    
    # Create datasets and dataloaders
    train_dataset = FeatureDataset(X_train, y_train)
    val_dataset = FeatureDataset(X_val, y_val)
    test_dataset = FeatureDataset(X_test, y_test)
    
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=False)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
    
    # Set device
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    logger.info(f"Using device: {device}")
    
    # Load model
    model_path = os.path.join(model_dir, 'temperature_model.pt')
    
    # Create model with the same architecture
    input_dim = X_scaled.shape[1]
    model = RegularizedDNN(
        input_dim=input_dim, 
        hidden_dims=[512, 512, 384, 256, 128], 
        dropout_rates=[0.3, 0.4, 0.4, 0.5, 0.5],
        use_batch_norm=True,
        activation='relu'
    )
    
    # Load weights
    model.load_state_dict(torch.load(model_path, map_location=device))
    model.to(device)
    logger.info(f"Loaded model from {model_path}")
    
    # Set model to evaluation mode
    model.eval()
    
    # Define loss function
    criterion = nn.MSELoss()
    
    # Evaluate on train, validation, and test sets
    results = {}
    
    # Function to evaluate on a dataset
    def evaluate_dataset(loader, dataset_name):
        all_targets = []
        all_predictions = []
        running_loss = 0.0
        
        with torch.no_grad():
            for inputs, targets in loader:
                inputs, targets = inputs.to(device), targets.to(device)
                
                # Forward pass
                outputs = model(inputs)
                loss = criterion(outputs, targets)
                
                running_loss += loss.item() * inputs.size(0)
                
                # Store predictions and targets for metrics
                all_targets.extend(targets.cpu().numpy())
                all_predictions.extend(outputs.cpu().numpy())
        
        # Calculate metrics
        loss = running_loss / len(loader.dataset)
        rmse = np.sqrt(mean_squared_error(all_targets, all_predictions))
        r2 = r2_score(all_targets, all_predictions)
        mae = mean_absolute_error(all_targets, all_predictions)
        
        logger.info(f"{dataset_name} - Loss: {loss:.4f}, RMSE: {rmse:.4f}, R²: {r2:.4f}, MAE: {mae:.4f}")
        
        return {
            'loss': loss,
            'rmse': rmse,
            'r2': r2,
            'mae': mae,
            'targets': all_targets,
            'predictions': all_predictions
        }
    
    # Evaluate on each dataset
    train_results = evaluate_dataset(train_loader, "Train")
    val_results = evaluate_dataset(val_loader, "Validation")
    test_results = evaluate_dataset(test_loader, "Test")
    
    results['train'] = train_results
    results['val'] = val_results
    results['test'] = test_results
    
    # Save results if output_dir is provided
    if output_dir:
        # Save metrics
        metrics_df = pd.DataFrame({
            'dataset': ['train', 'val', 'test'],
            'loss': [train_results['loss'], val_results['loss'], test_results['loss']],
            'rmse': [train_results['rmse'], val_results['rmse'], test_results['rmse']],
            'r2': [train_results['r2'], val_results['r2'], test_results['r2']],
            'mae': [train_results['mae'], val_results['mae'], test_results['mae']]
        })
        metrics_df.to_csv(os.path.join(output_dir, 'temperature_metrics.tsv'), sep='\t', index=False)
        
        # Plot actual vs predicted for test set
        plt.figure(figsize=(10, 8))
        plt.scatter(test_results['targets'], test_results['predictions'], alpha=0.5)
        plt.plot([min(test_results['targets']), max(test_results['targets'])], 
                 [min(test_results['targets']), max(test_results['targets'])], 'r--')
        plt.xlabel(f'Actual {target_name}')
        plt.ylabel(f'Predicted {target_name}')
        plt.title(f'Actual vs Predicted {target_name} (Test Set, R² = {test_results["r2"]:.4f})')
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'temperature_test_predictions.png'))
        
        # Save test predictions
        pd.DataFrame({
            'actual': test_results['targets'],
            'predicted': test_results['predictions']
        }).to_csv(os.path.join(output_dir, 'temperature_test_predictions.tsv'), sep='\t', index=False)
        
        logger.info(f"Saved evaluation results to {output_dir}")
    
    return results

def main():
    parser = argparse.ArgumentParser(description='Evaluate Enhanced DNN model for temperature prediction')
    parser.add_argument('--model-dir', type=str, required=True, 
                        help='Directory containing the trained model')
    parser.add_argument('--feature-file', type=str, default='./training_data/combined_features.tsv', 
                        help='Path to the combined feature file (TSV)')
    parser.add_argument('--metadata', type=str, default='./training_data/metadata.tsv', 
                        help='Path to metadata file')
    parser.add_argument('--output-dir', type=str, default=None, 
                        help='Directory to save evaluation results')
    parser.add_argument('--batch-size', type=int, default=64, 
                        help='Batch size for evaluation')
    
    args = parser.parse_args()
    
    try:
        # Create output directory if provided
        if args.output_dir:
            os.makedirs(args.output_dir, exist_ok=True)
        
        # Evaluate model
        results = evaluate_model(
            args.model_dir,
            args.feature_file,
            args.metadata,
            args.output_dir,
            args.batch_size
        )
        
        logger.info("Enhanced DNN model for temperature prediction evaluation completed successfully")
        return 0
    
    except Exception as e:
        logger.error(f"Error evaluating Enhanced DNN model for temperature prediction: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == '__main__':
    main()
