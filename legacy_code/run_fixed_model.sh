#!/bin/bash

# Run the fixed temperature model for DeepMu
# This script runs the improved temperature model that fixes the R² calculation issues

echo "Running fixed temperature model..."
mkdir -p models/fixed_temp_model

# Run the training script
python fixed_temp_model.py

# Check if training was successful
if [ $? -eq 0 ]; then
    echo "Training completed successfully!"
    echo "Model saved to models/fixed_temp_model/"
    
    # Optional: Run evaluation visualization
    echo "Running visualization..."
    python visualize_results.py --model_dir models/fixed_temp_model
    
    echo "Done!"
else
    echo "Training failed. See logs for details."
fi 