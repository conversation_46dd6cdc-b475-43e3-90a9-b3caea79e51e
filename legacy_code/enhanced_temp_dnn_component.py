#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Enhanced DNN Component for Temperature Prediction.
This module provides a PyTorch DNN model with advanced architecture and One-Cycle Learning Rate Schedule
specifically for optimal temperature prediction.
"""

import os
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import logging
from typing import Dict, List, Tuple, Optional
from sklearn.preprocessing import StandardScaler

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TemperatureDataset(Dataset):
    """Dataset for temperature prediction."""
    def __init__(self, features: np.ndarray, temperature: np.ndarray):
        """
        Initialize dataset.
        
        Args:
            features: Feature matrix
            temperature: Temperature target vector
        """
        self.features = torch.tensor(features, dtype=torch.float32)
        self.temperature = torch.tensor(temperature, dtype=torch.float32).reshape(-1, 1)
        
    def __len__(self):
        return len(self.features)
    
    def __getitem__(self, idx):
        return self.features[idx], self.temperature[idx]

class EnhancedDNN(nn.Module):
    """
    Enhanced Deep Neural Network with batch normalization, gradient clipping, and residual connections.
    """
    def __init__(
        self,
        input_dim: int,
        hidden_dims: List[int] = [512, 512, 384, 256, 128],
        dropout_rates: List[float] = [0.3, 0.4, 0.4, 0.5, 0.5],
        use_batch_norm: bool = True,
        activation: str = 'relu'
    ):
        """
        Initialize neural network.
        
        Args:
            input_dim: Number of input features
            hidden_dims: List of hidden layer dimensions
            dropout_rates: List of dropout rates for each layer
            use_batch_norm: Whether to use batch normalization
            activation: Activation function to use ('relu', 'leaky_relu', 'elu')
        """
        super().__init__()
        
        # Ensure dropout_rates has at least as many elements as hidden_dims
        if len(dropout_rates) < len(hidden_dims):
            dropout_rates = dropout_rates + [dropout_rates[-1]] * (len(hidden_dims) - len(dropout_rates))
        
        # Choose activation function
        if activation == 'relu':
            act_fn = nn.ReLU()
        elif activation == 'leaky_relu':
            act_fn = nn.LeakyReLU(0.1)
        elif activation == 'elu':
            act_fn = nn.ELU()
        else:
            act_fn = nn.ReLU()
        
        # Create layers
        layers = []
        prev_dim = input_dim
        
        # Build network with progressively increasing dropout
        for i, hidden_dim in enumerate(hidden_dims):
            # Linear layer
            layers.append(nn.Linear(prev_dim, hidden_dim))
            
            # Batch normalization (before activation)
            if use_batch_norm:
                layers.append(nn.BatchNorm1d(hidden_dim))
            
            # Activation
            layers.append(act_fn)
            
            # Dropout with increasing rate for deeper layers
            dropout_rate = dropout_rates[i]
            layers.append(nn.Dropout(dropout_rate))
            
            prev_dim = hidden_dim
        
        self.feature_extractor = nn.Sequential(*layers)
        
        # Output layer for temperature
        self.output_layer = nn.Linear(prev_dim, 1)
        
        # Initialize weights with improved method
        self._init_weights()
    
    def _init_weights(self):
        """Initialize weights with improved initialization."""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                # Use Xavier/Glorot initialization for better stability
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
    
    def forward(self, x):
        """
        Forward pass.
        
        Args:
            x: Input tensor
            
        Returns:
            Temperature prediction
        """
        features = self.feature_extractor(x)
        temp = self.output_layer(features)
        
        return temp.squeeze()

class EarlyStopping:
    """Early stopping to prevent overfitting"""
    def __init__(self, patience=10, min_delta=0, restore_best_weights=True):
        self.patience = patience
        self.min_delta = min_delta
        self.restore_best_weights = restore_best_weights
        self.best_score = None
        self.best_weights = None
        self.counter = 0
        self.early_stop = False
    
    def __call__(self, val_loss, model):
        score = -val_loss
        
        if self.best_score is None:
            self.best_score = score
            self.best_weights = {k: v.cpu().clone() for k, v in model.state_dict().items()}
        elif score < self.best_score + self.min_delta:
            self.counter += 1
            if self.counter >= self.patience:
                self.early_stop = True
        else:
            self.best_score = score
            self.best_weights = {k: v.cpu().clone() for k, v in model.state_dict().items()}
            self.counter = 0
        
        return self.early_stop
    
    def restore_weights(self, model):
        if self.restore_best_weights and self.best_weights is not None:
            model.load_state_dict(self.best_weights)

class EnhancedTempDNNComponent:
    """
    Enhanced DNN component for temperature prediction.
    """
    def __init__(
        self,
        hidden_dims: List[int] = [512, 512, 384, 256, 128],
        dropout_rates: List[float] = [0.3, 0.4, 0.4, 0.5, 0.5],
        lr: float = 0.001,
        batch_size: int = 64,
        epochs: int = 100,
        patience: int = 15,
        use_batch_norm: bool = True,
        use_gradient_clipping: bool = True,
        max_grad_norm: float = 1.0,
        lr_scheduler: str = 'one_cycle',
        warmup_epochs: int = 5,
        activation: str = 'relu',
        device: str = None
    ):
        """
        Initialize enhanced DNN component.
        
        Args:
            hidden_dims: List of hidden layer dimensions
            dropout_rates: List of dropout rates for each layer
            lr: Learning rate
            batch_size: Batch size for training
            epochs: Maximum number of epochs
            patience: Patience for early stopping
            use_batch_norm: Whether to use batch normalization
            use_gradient_clipping: Whether to use gradient clipping
            max_grad_norm: Maximum gradient norm for clipping
            lr_scheduler: Learning rate scheduler type ('one_cycle', 'plateau', 'none')
            warmup_epochs: Number of warmup epochs for learning rate schedulers
            activation: Activation function to use ('relu', 'leaky_relu', 'elu')
            device: Device to use (cpu or cuda)
        """
        self.hidden_dims = hidden_dims
        self.dropout_rates = dropout_rates
        self.lr = lr
        self.batch_size = batch_size
        self.epochs = epochs
        self.patience = patience
        self.use_batch_norm = use_batch_norm
        self.use_gradient_clipping = use_gradient_clipping
        self.max_grad_norm = max_grad_norm
        self.lr_scheduler = lr_scheduler
        self.warmup_epochs = warmup_epochs
        self.activation = activation
        
        # Determine device
        if device is None:
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = torch.device(device)
            
        logger.info(f"Using device: {self.device}")
        
        # Initialize model, optimizer, and loss function
        self.model = None
        self.optimizer = None
        self.criterion = nn.MSELoss()
        
        # Initialize scaler
        self.feature_scaler = StandardScaler()
        self.temp_scaler = None
        
    def fit(
        self, 
        X: pd.DataFrame, 
        y_temp: pd.Series,
        X_val: pd.DataFrame = None, 
        y_val_temp: pd.Series = None,
        output_dir: str = None
    ) -> Dict[str, List[float]]:
        """
        Fit enhanced DNN to data.
        
        Args:
            X: Training features
            y_temp: Training temperature targets
            X_val: Validation features (optional)
            y_val_temp: Validation temperature targets (optional)
            output_dir: Directory to save outputs
            
        Returns:
            Dictionary of training history
        """
        # Scale features
        X_scaled = self.feature_scaler.fit_transform(X)
        
        # Store scalers for targets if provided
        if self.temp_scaler is not None:
            y_temp_scaled = y_temp.values
        else:
            y_temp_scaled = y_temp.values
        
        # Convert validation data if provided
        if X_val is not None and y_val_temp is not None:
            X_val_scaled = self.feature_scaler.transform(X_val)
            
            if self.temp_scaler is not None:
                y_val_temp_scaled = y_val_temp.values
            else:
                y_val_temp_scaled = y_val_temp.values
                
            val_dataset = TemperatureDataset(X_val_scaled, y_val_temp_scaled)
            val_loader = DataLoader(val_dataset, batch_size=self.batch_size, shuffle=False)
        else:
            val_loader = None
        
        # Create dataset and dataloader
        train_dataset = TemperatureDataset(X_scaled, y_temp_scaled)
        train_loader = DataLoader(train_dataset, batch_size=self.batch_size, shuffle=True)
        
        # Initialize model
        input_dim = X.shape[1]
        self.model = EnhancedDNN(
            input_dim=input_dim,
            hidden_dims=self.hidden_dims,
            dropout_rates=self.dropout_rates,
            use_batch_norm=self.use_batch_norm,
            activation=self.activation
        ).to(self.device)
        
        # Initialize optimizer with weight decay
        self.optimizer = optim.AdamW(self.model.parameters(), lr=self.lr, weight_decay=1e-5)
        
        # Learning rate scheduler
        if self.lr_scheduler == 'one_cycle':
            # Calculate total steps
            steps_per_epoch = len(train_loader)
            total_steps = steps_per_epoch * self.epochs
            
            # Set the max_lr for one cycle
            max_lr = self.lr * 10
            
            logger.info(f"Setting up OneCycleLR with max_lr={max_lr}, total_steps={total_steps}")
            
            scheduler = optim.lr_scheduler.OneCycleLR(
                self.optimizer, 
                max_lr=max_lr, 
                total_steps=total_steps,
                pct_start=self.warmup_epochs/self.epochs,  # Warmup percentage
                anneal_strategy='cos'
            )
        elif self.lr_scheduler == 'plateau':
            scheduler = optim.lr_scheduler.ReduceLROnPlateau(
                self.optimizer, mode='min', factor=0.5, patience=5, verbose=True, min_lr=1e-6
            )
        else:  # 'none'
            # Dummy scheduler that keeps the learning rate constant
            scheduler = optim.lr_scheduler.LambdaLR(
                self.optimizer, lambda epoch: 1.0
            )
        
        # Early stopping
        early_stopping = EarlyStopping(patience=self.patience, min_delta=0.001, restore_best_weights=True)
        
        # Training history
        history = {
            'train_loss': [], 
            'val_loss': [], 
            'learning_rates': []
        }
        
        logger.info(f"Starting enhanced DNN training for {self.epochs} epochs...")
        for epoch in range(self.epochs):
            # Train
            self.model.train()
            train_loss = 0.0
            
            for features, targets in train_loader:
                features = features.to(self.device)
                targets = targets.to(self.device)
                
                self.optimizer.zero_grad()
                outputs = self.model(features)
                
                # Calculate loss
                loss = self.criterion(outputs, targets.squeeze())
                
                loss.backward()
                
                # Gradient clipping to prevent exploding gradients
                if self.use_gradient_clipping:
                    torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=self.max_grad_norm)
                
                self.optimizer.step()
                
                # Update learning rate for one-cycle scheduler
                if self.lr_scheduler == 'one_cycle':
                    scheduler.step()
                
                # Track loss
                train_loss += loss.item() * features.size(0)
            
            # Calculate average loss
            train_loss /= len(train_loader.dataset)
            
            # Store in history
            history['train_loss'].append(train_loss)
            history['learning_rates'].append(self.optimizer.param_groups[0]['lr'])
            
            # Validate
            if val_loader is not None:
                val_loss = self._validate(val_loader)
                
                # Store in history
                history['val_loss'].append(val_loss)
                
                # Update learning rate for plateau scheduler
                if self.lr_scheduler == 'plateau':
                    scheduler.step(val_loss)
                
                # Early stopping
                if early_stopping(val_loss, self.model):
                    logger.info(f"Early stopping after {epoch+1} epochs without improvement")
                    break
                
                logger.info(
                    f"Epoch {epoch+1}/{self.epochs}: "
                    f"Train Loss: {train_loss:.4f}, "
                    f"Val Loss: {val_loss:.4f}, "
                    f"LR: {self.optimizer.param_groups[0]['lr']:.6f}"
                )
            else:
                logger.info(
                    f"Epoch {epoch+1}/{self.epochs}: "
                    f"Train Loss: {train_loss:.4f}, "
                    f"LR: {self.optimizer.param_groups[0]['lr']:.6f}"
                )
        
        # Load best model if validation was used
        if val_loader is not None:
            early_stopping.restore_weights(self.model)
            logger.info("Loaded best model weights")
        
        # Save model if output_dir is provided
        if output_dir is not None:
            self.save(output_dir)
        
        return history
    
    def _validate(self, val_loader: DataLoader) -> float:
        """
        Validate model on validation data.
        
        Args:
            val_loader: Validation data loader
            
        Returns:
            Validation loss
        """
        self.model.eval()
        val_loss = 0.0
        
        with torch.no_grad():
            for features, targets in val_loader:
                features = features.to(self.device)
                targets = targets.to(self.device)
                
                outputs = self.model(features)
                
                # Calculate loss
                loss = self.criterion(outputs, targets.squeeze())
                
                # Track loss
                val_loss += loss.item() * features.size(0)
        
        # Calculate average loss
        val_loss /= len(val_loader.dataset)
        
        return val_loss
    
    def predict(self, X: pd.DataFrame) -> np.ndarray:
        """
        Generate temperature predictions.
        
        Args:
            X: Features
            
        Returns:
            Temperature predictions
        """
        if self.model is None:
            raise ValueError("Model has not been trained yet")
        
        # Scale features
        X_scaled = self.feature_scaler.transform(X)
        
        # Convert to tensor
        X_tensor = torch.tensor(X_scaled, dtype=torch.float32).to(self.device)
        
        # Generate predictions
        self.model.eval()
        with torch.no_grad():
            temp_pred = self.model(X_tensor)
            temp_pred = temp_pred.cpu().numpy()
        
        # Inverse transform if scaler is available
        if self.temp_scaler is not None:
            temp_pred = self.temp_scaler.inverse_transform(temp_pred.reshape(-1, 1)).flatten()
        
        return temp_pred
    
    def evaluate(
        self, 
        X: pd.DataFrame, 
        y_temp: pd.Series
    ) -> Dict[str, float]:
        """
        Evaluate model on data.
        
        Args:
            X: Features
            y_temp: Temperature targets
            
        Returns:
            Dictionary of evaluation metrics
        """
        # Generate predictions
        y_temp_pred = self.predict(X)
        
        # Calculate metrics
        temp_metrics = {
            'R2': r2_score(y_temp, y_temp_pred),
            'RMSE': np.sqrt(mean_squared_error(y_temp, y_temp_pred)),
            'MAE': mean_absolute_error(y_temp, y_temp_pred)
        }
        
        # Log metrics
        logger.info(f"Temperature evaluation metrics - R²={temp_metrics['R2']:.4f}, RMSE={temp_metrics['RMSE']:.4f}, MAE={temp_metrics['MAE']:.4f}")
        
        return temp_metrics
    
    def save(self, output_dir: str) -> None:
        """
        Save model to disk.
        
        Args:
            output_dir: Directory to save model
        """
        if self.model is None:
            raise ValueError("Model has not been trained yet")
        
        os.makedirs(output_dir, exist_ok=True)
        
        # Save model
        torch.save(self.model.state_dict(), os.path.join(output_dir, 'enhanced_temp_dnn_model.pth'))
        
        # Save scalers
        import joblib
        joblib.dump(self.feature_scaler, os.path.join(output_dir, 'feature_scaler.joblib'))
        
        if self.temp_scaler is not None:
            joblib.dump(self.temp_scaler, os.path.join(output_dir, 'temp_scaler.joblib'))
        
        # Save hyperparameters
        hyperparams = {
            'hidden_dims': self.hidden_dims,
            'dropout_rates': self.dropout_rates,
            'lr': self.lr,
            'batch_size': self.batch_size,
            'epochs': self.epochs,
            'patience': self.patience,
            'use_batch_norm': self.use_batch_norm,
            'use_gradient_clipping': self.use_gradient_clipping,
            'max_grad_norm': self.max_grad_norm,
            'lr_scheduler': self.lr_scheduler,
            'warmup_epochs': self.warmup_epochs,
            'activation': self.activation
        }
        joblib.dump(hyperparams, os.path.join(output_dir, 'hyperparams.joblib'))
        
        logger.info(f"Enhanced temperature DNN model saved to {output_dir}")
    
    def load(self, input_dir: str) -> None:
        """
        Load model from disk.
        
        Args:
            input_dir: Directory to load model from
        """
        import joblib
        
        # Load scalers
        self.feature_scaler = joblib.load(os.path.join(input_dir, 'feature_scaler.joblib'))
        
        # Try to load target scaler if it exists
        try:
            self.temp_scaler = joblib.load(os.path.join(input_dir, 'temp_scaler.joblib'))
        except:
            self.temp_scaler = None
        
        # Load hyperparameters
        hyperparams = joblib.load(os.path.join(input_dir, 'hyperparams.joblib'))
        self.hidden_dims = hyperparams['hidden_dims']
        self.dropout_rates = hyperparams['dropout_rates']
        self.lr = hyperparams['lr']
        self.batch_size = hyperparams['batch_size']
        self.epochs = hyperparams['epochs']
        self.patience = hyperparams['patience']
        self.use_batch_norm = hyperparams['use_batch_norm']
        self.use_gradient_clipping = hyperparams['use_gradient_clipping']
        self.max_grad_norm = hyperparams['max_grad_norm']
        self.lr_scheduler = hyperparams['lr_scheduler']
        self.warmup_epochs = hyperparams['warmup_epochs']
        self.activation = hyperparams['activation']
        
        # Determine input dimension from scaler
        input_dim = len(self.feature_scaler.mean_)
        
        # Initialize model
        self.model = EnhancedDNN(
            input_dim=input_dim,
            hidden_dims=self.hidden_dims,
            dropout_rates=self.dropout_rates,
            use_batch_norm=self.use_batch_norm,
            activation=self.activation
        ).to(self.device)
        
        # Load model weights
        self.model.load_state_dict(torch.load(
            os.path.join(input_dir, 'enhanced_temp_dnn_model.pth'),
            map_location=self.device
        ))
        
        logger.info(f"Enhanced temperature DNN model loaded from {input_dir}")
