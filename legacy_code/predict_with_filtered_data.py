#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Predict with Direct Models using filtered data.
This script filters out extreme outliers in the growth rate data before prediction.
"""

import os
import argparse
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import torch
import joblib
import logging
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import the original predictor
from predict_with_direct_models import DirectModelsPredictor, load_data

def filter_outliers(metadata, column, percentile=99.5):
    """
    Filter out extreme outliers in the specified column.
    
    Args:
        metadata: DataFrame containing the data
        column: Column name to filter
        percentile: Percentile threshold for filtering (default: 99.5)
        
    Returns:
        Filtered DataFrame
    """
    threshold = np.percentile(metadata[column], percentile)
    logger.info(f"Filtering {column} values above {threshold:.4f} (percentile {percentile})")
    
    # Count outliers
    outlier_count = (metadata[column] > threshold).sum()
    logger.info(f"Found {outlier_count} outliers in {column}")
    
    # Filter out outliers
    filtered_metadata = metadata[metadata[column] <= threshold].copy()
    
    # Log statistics before and after filtering
    logger.info(f"{column} statistics before filtering - Min: {metadata[column].min():.4f}, Max: {metadata[column].max():.4f}, Mean: {metadata[column].mean():.4f}, Median: {metadata[column].median():.4f}")
    logger.info(f"{column} statistics after filtering - Min: {filtered_metadata[column].min():.4f}, Max: {filtered_metadata[column].max():.4f}, Mean: {filtered_metadata[column].mean():.4f}, Median: {filtered_metadata[column].median():.4f}")
    
    return filtered_metadata

def apply_transformations(y_growth, transform_type='sqrt'):
    """
    Apply transformations to growth rate values.
    
    Args:
        y_growth: Growth rate values
        transform_type: Type of transformation ('sqrt', 'log', or 'none')
        
    Returns:
        Transformed growth rate values
    """
    if transform_type == 'sqrt':
        logger.info(f"Applying square root transformation to growth rate")
        y_transformed = np.sqrt(y_growth)
    elif transform_type == 'log':
        logger.info(f"Applying log transformation to growth rate")
        # Add a small constant to avoid log(0)
        y_transformed = np.log1p(y_growth)
    else:
        logger.info(f"No transformation applied to growth rate")
        y_transformed = y_growth
    
    logger.info(f"Growth rate statistics after transformation - Min: {y_transformed.min():.4f}, Max: {y_transformed.max():.4f}, Mean: {y_transformed.mean():.4f}, Median: {y_transformed.median():.4f}")
    
    return y_transformed

def main():
    parser = argparse.ArgumentParser(description="Predict with Direct Models using filtered data")
    parser.add_argument("--feature_file", required=True, help="Path to feature file")
    parser.add_argument("--metadata_file", required=True, help="Path to metadata file")
    parser.add_argument("--output_dir", default="predictions_filtered", help="Directory to save predictions")
    parser.add_argument("--temp_model_dir", default="models/enhanced_dnn_temperature_v2", help="Directory containing the temperature model")
    parser.add_argument("--growth_model_dir", default="models/hybrid_enhanced_nn_v1", help="Directory containing the growth rate model")
    parser.add_argument("--target", default="both", choices=["growth", "temperature", "both"], help="Prediction target")
    parser.add_argument("--percentile", type=float, default=99.5, help="Percentile threshold for filtering outliers")
    parser.add_argument("--transform", default="sqrt", choices=["sqrt", "log", "none"], help="Transformation to apply to growth rate")
    args = parser.parse_args()

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Load data
    features, metadata = load_data(args.feature_file, args.metadata_file)
    
    # Filter outliers in growth rate
    if args.target in ['growth', 'both']:
        metadata_filtered = filter_outliers(metadata, 'growth_rate', args.percentile)
        
        # Filter features to match filtered metadata
        features_filtered = features.loc[metadata_filtered.index]
        
        # Extract targets
        y_growth = metadata_filtered['growth_rate']
        
        # Apply transformations to growth rate
        y_growth_transformed = apply_transformations(y_growth, args.transform)
        
        # Update metadata with transformed growth rate
        metadata_filtered['growth_rate_transformed'] = y_growth_transformed
    else:
        # No filtering needed for temperature-only prediction
        metadata_filtered = metadata
        features_filtered = features
    
    # Extract temperature target
    if args.target in ['temperature', 'both']:
        y_temp = metadata_filtered['optimal_temperature']
    
    # Initialize predictor
    predictor = DirectModelsPredictor(
        temp_model_dir=args.temp_model_dir,
        growth_model_dir=args.growth_model_dir
    )
    
    # Generate predictions
    if args.target == 'growth':
        y_growth_pred = predictor.predict(features_filtered, target='growth')
        
        # Save predictions
        pd.DataFrame({
            'genome_id': features_filtered.index,
            'actual_growth': y_growth,
            'predicted_growth': y_growth_pred
        }).to_csv(os.path.join(args.output_dir, 'growth_predictions.tsv'), sep='\t', index=False)
        
        # Plot predictions
        plt.figure(figsize=(12, 5))
        
        # Plot with original scale
        plt.subplot(1, 2, 1)
        plt.scatter(y_growth, y_growth_pred, alpha=0.5)
        plt.plot([min(y_growth), max(y_growth)], [min(y_growth), max(y_growth)], 'r--')
        plt.xlabel('Actual Growth Rate')
        plt.ylabel('Predicted Growth Rate')
        plt.title('Growth Rate Predictions (Original Scale)')
        
        # Plot with log scale for better visualization
        plt.subplot(1, 2, 2)
        plt.scatter(np.log1p(y_growth), np.log1p(y_growth_pred), alpha=0.5)
        plt.plot([min(np.log1p(y_growth)), max(np.log1p(y_growth))], 
                 [min(np.log1p(y_growth)), max(np.log1p(y_growth))], 'r--')
        plt.xlabel('Actual Growth Rate (log scale)')
        plt.ylabel('Predicted Growth Rate (log scale)')
        plt.title('Growth Rate Predictions (Log Scale)')
        
        plt.tight_layout()
        plt.savefig(os.path.join(args.output_dir, 'growth_predictions.png'))
        
        # Evaluate model
        metrics = predictor.evaluate(features_filtered, y_growth=y_growth, target='growth')
        
        # Calculate additional metrics on log scale for better evaluation
        log_metrics = {
            'R2_log': r2_score(np.log1p(y_growth), np.log1p(y_growth_pred)),
            'RMSE_log': np.sqrt(mean_squared_error(np.log1p(y_growth), np.log1p(y_growth_pred))),
            'MAE_log': mean_absolute_error(np.log1p(y_growth), np.log1p(y_growth_pred))
        }
        
        # Combine metrics
        all_metrics = {
            'R2': metrics['growth_rate']['R2'],
            'RMSE': metrics['growth_rate']['RMSE'],
            'MAE': metrics['growth_rate']['MAE'],
            'R2_log': log_metrics['R2_log'],
            'RMSE_log': log_metrics['RMSE_log'],
            'MAE_log': log_metrics['MAE_log']
        }
        
        # Save metrics
        pd.DataFrame({
            'metric': list(all_metrics.keys()),
            'value': list(all_metrics.values())
        }).to_csv(os.path.join(args.output_dir, 'growth_metrics.tsv'), sep='\t', index=False)
        
        logger.info(f"Growth Rate: R²={all_metrics['R2']:.4f}, RMSE={all_metrics['RMSE']:.4f}, MAE={all_metrics['MAE']:.4f}")
        logger.info(f"Growth Rate (log scale): R²={all_metrics['R2_log']:.4f}, RMSE={all_metrics['RMSE_log']:.4f}, MAE={all_metrics['MAE_log']:.4f}")
        
    elif args.target == 'temperature':
        y_temp_pred = predictor.predict(features_filtered, target='temperature')
        
        # Save predictions
        pd.DataFrame({
            'genome_id': features_filtered.index,
            'actual_temp': y_temp,
            'predicted_temp': y_temp_pred
        }).to_csv(os.path.join(args.output_dir, 'temperature_predictions.tsv'), sep='\t', index=False)
        
        # Plot predictions
        plt.figure(figsize=(10, 6))
        plt.scatter(y_temp, y_temp_pred, alpha=0.5)
        plt.plot([min(y_temp), max(y_temp)], [min(y_temp), max(y_temp)], 'r--')
        plt.xlabel('Actual Temperature')
        plt.ylabel('Predicted Temperature')
        plt.title('Temperature Predictions')
        plt.tight_layout()
        plt.savefig(os.path.join(args.output_dir, 'temperature_predictions.png'))
        
        # Evaluate model
        metrics = predictor.evaluate(features_filtered, y_temp=y_temp, target='temperature')
        
        # Save metrics
        pd.DataFrame({
            'metric': list(metrics['temperature'].keys()),
            'value': list(metrics['temperature'].values())
        }).to_csv(os.path.join(args.output_dir, 'temperature_metrics.tsv'), sep='\t', index=False)
        
    else:  # both
        y_growth_pred, y_temp_pred = predictor.predict(features_filtered, target='both')
        
        # Save predictions
        pd.DataFrame({
            'genome_id': features_filtered.index,
            'actual_growth': y_growth,
            'predicted_growth': y_growth_pred,
            'actual_temp': y_temp,
            'predicted_temp': y_temp_pred
        }).to_csv(os.path.join(args.output_dir, 'predictions.tsv'), sep='\t', index=False)
        
        # Plot predictions
        plt.figure(figsize=(12, 10))
        
        # Growth rate - original scale
        plt.subplot(2, 2, 1)
        plt.scatter(y_growth, y_growth_pred, alpha=0.5)
        plt.plot([min(y_growth), max(y_growth)], [min(y_growth), max(y_growth)], 'r--')
        plt.xlabel('Actual Growth Rate')
        plt.ylabel('Predicted Growth Rate')
        plt.title('Growth Rate Predictions (Original Scale)')
        
        # Growth rate - log scale
        plt.subplot(2, 2, 2)
        plt.scatter(np.log1p(y_growth), np.log1p(y_growth_pred), alpha=0.5)
        plt.plot([min(np.log1p(y_growth)), max(np.log1p(y_growth))], 
                 [min(np.log1p(y_growth)), max(np.log1p(y_growth))], 'r--')
        plt.xlabel('Actual Growth Rate (log scale)')
        plt.ylabel('Predicted Growth Rate (log scale)')
        plt.title('Growth Rate Predictions (Log Scale)')
        
        # Temperature
        plt.subplot(2, 2, 3)
        plt.scatter(y_temp, y_temp_pred, alpha=0.5)
        plt.plot([min(y_temp), max(y_temp)], [min(y_temp), max(y_temp)], 'r--')
        plt.xlabel('Actual Temperature')
        plt.ylabel('Predicted Temperature')
        plt.title('Temperature Predictions')
        
        plt.tight_layout()
        plt.savefig(os.path.join(args.output_dir, 'predictions.png'))
        
        # Evaluate model
        metrics = predictor.evaluate(features_filtered, y_growth=y_growth, y_temp=y_temp, target='both')
        
        # Calculate additional metrics on log scale for growth rate
        log_metrics = {
            'R2_log': r2_score(np.log1p(y_growth), np.log1p(y_growth_pred)),
            'RMSE_log': np.sqrt(mean_squared_error(np.log1p(y_growth), np.log1p(y_growth_pred))),
            'MAE_log': mean_absolute_error(np.log1p(y_growth), np.log1p(y_growth_pred))
        }
        
        logger.info(f"Growth Rate: R²={metrics['growth_rate']['R2']:.4f}, RMSE={metrics['growth_rate']['RMSE']:.4f}, MAE={metrics['growth_rate']['MAE']:.4f}")
        logger.info(f"Growth Rate (log scale): R²={log_metrics['R2_log']:.4f}, RMSE={log_metrics['RMSE_log']:.4f}, MAE={log_metrics['MAE_log']:.4f}")
        logger.info(f"Temperature: R²={metrics['temperature']['R2']:.4f}, RMSE={metrics['temperature']['RMSE']:.4f}, MAE={metrics['temperature']['MAE']:.4f}")
        
        # Calculate overall metrics including log-scale metrics
        overall_metrics = {
            'R2': (metrics['growth_rate']['R2'] + metrics['temperature']['R2']) / 2,
            'RMSE': (metrics['growth_rate']['RMSE'] + metrics['temperature']['RMSE']) / 2,
            'MAE': (metrics['growth_rate']['MAE'] + metrics['temperature']['MAE']) / 2,
            'R2_log_growth': log_metrics['R2_log'],
            'RMSE_log_growth': log_metrics['RMSE_log'],
            'MAE_log_growth': log_metrics['MAE_log']
        }
        
        logger.info(f"Overall: R²={overall_metrics['R2']:.4f}, RMSE={overall_metrics['RMSE']:.4f}, MAE={overall_metrics['MAE']:.4f}")
        
        # Save metrics
        pd.DataFrame({
            'metric': ['R2', 'RMSE', 'MAE', 'R2_log', 'RMSE_log', 'MAE_log'],
            'growth_rate': [
                metrics['growth_rate']['R2'], 
                metrics['growth_rate']['RMSE'], 
                metrics['growth_rate']['MAE'],
                log_metrics['R2_log'],
                log_metrics['RMSE_log'],
                log_metrics['MAE_log']
            ],
            'temperature': [
                metrics['temperature']['R2'], 
                metrics['temperature']['RMSE'], 
                metrics['temperature']['MAE'],
                None, None, None
            ],
            'overall': [
                overall_metrics['R2'], 
                overall_metrics['RMSE'], 
                overall_metrics['MAE'],
                None, None, None
            ]
        }).to_csv(os.path.join(args.output_dir, 'metrics.tsv'), sep='\t', index=False)
    
    logger.info(f"Predictions saved to {args.output_dir}")

if __name__ == "__main__":
    main()
