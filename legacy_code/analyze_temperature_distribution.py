import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import torch
from pathlib import Path

def load_and_analyze_temperature_data(data_path):
    """Load and analyze temperature data distributions.
    
    Args:
        data_path: Path to the training data file
    """
    # Load data
    data = pd.read_csv(data_path, sep='\t')
    
    # Extract temperature column
    temp_col = 'optimal_temperature'  # Updated to match metadata.tsv
    if temp_col not in data.columns:
        raise ValueError(f"Temperature column '{temp_col}' not found in data")
    
    temperatures = data[temp_col].values
    
    # Basic statistics
    print("\n=== Temperature Distribution Statistics ===")
    print(f"Number of samples: {len(temperatures)}")
    print(f"Mean: {np.mean(temperatures):.3f}")
    print(f"Median: {np.median(temperatures):.3f}")
    print(f"Std: {np.std(temperatures):.3f}")
    print(f"Min: {np.min(temperatures):.3f}")
    print(f"Max: {np.max(temperatures):.3f}")
    print(f"Skewness: {stats.skew(temperatures):.3f}")
    print(f"Kurtosis: {stats.kurtosis(temperatures):.3f}")
    
    # Check for NaN values
    nan_count = np.isnan(temperatures).sum()
    print(f"\nNaN values: {nan_count} ({nan_count/len(temperatures)*100:.2f}%)")
    
    # Check for infinite values
    inf_count = np.isinf(temperatures).sum()
    print(f"Infinite values: {inf_count} ({inf_count/len(temperatures)*100:.2f}%)")
    
    # Check for outliers using IQR method
    Q1 = np.percentile(temperatures, 25)
    Q3 = np.percentile(temperatures, 75)
    IQR = Q3 - Q1
    lower_bound = Q1 - 1.5 * IQR
    upper_bound = Q3 + 1.5 * IQR
    outliers = temperatures[(temperatures < lower_bound) | (temperatures > upper_bound)]
    print(f"\nOutliers (IQR method): {len(outliers)} ({len(outliers)/len(temperatures)*100:.2f}%)")
    
    # Create visualizations
    plt.figure(figsize=(15, 10))
    
    # 1. Histogram with KDE
    plt.subplot(2, 2, 1)
    sns.histplot(temperatures, kde=True)
    plt.title('Temperature Distribution')
    plt.xlabel('Temperature')
    plt.ylabel('Count')
    
    # 2. Box plot
    plt.subplot(2, 2, 2)
    sns.boxplot(y=temperatures)
    plt.title('Temperature Box Plot')
    plt.ylabel('Temperature')
    
    # 3. Q-Q plot
    plt.subplot(2, 2, 3)
    stats.probplot(temperatures, dist="norm", plot=plt)
    plt.title('Q-Q Plot')
    
    # 4. Scatter plot of sorted values
    plt.subplot(2, 2, 4)
    plt.scatter(range(len(temperatures)), np.sort(temperatures))
    plt.title('Sorted Temperature Values')
    plt.xlabel('Index')
    plt.ylabel('Temperature')
    
    plt.tight_layout()
    
    # Save plots
    output_dir = Path('plots')
    output_dir.mkdir(exist_ok=True)
    plt.savefig(output_dir / 'temperature_distribution_analysis.png')
    plt.close()
    
    # Additional analysis for transformed data
    print("\n=== Transformed Data Analysis ===")
    
    # Log transform (adding small constant to handle zeros)
    log_temp = np.log1p(temperatures - np.min(temperatures) + 1e-6)
    print("\nLog-transformed statistics:")
    print(f"Mean: {np.mean(log_temp):.3f}")
    print(f"Std: {np.std(log_temp):.3f}")
    print(f"Skewness: {stats.skew(log_temp):.3f}")
    
    # Box-Cox transform
    try:
        boxcox_temp, lambda_param = stats.boxcox(temperatures - np.min(temperatures) + 1e-6)
        print("\nBox-Cox transformed statistics (lambda = {:.3f}):".format(lambda_param))
        print(f"Mean: {np.mean(boxcox_temp):.3f}")
        print(f"Std: {np.std(boxcox_temp):.3f}")
        print(f"Skewness: {stats.skew(boxcox_temp):.3f}")
    except:
        print("\nBox-Cox transformation failed - data may contain negative values")
    
    # Z-score normalization
    z_score_temp = (temperatures - np.mean(temperatures)) / np.std(temperatures)
    print("\nZ-score normalized statistics:")
    print(f"Mean: {np.mean(z_score_temp):.3f}")
    print(f"Std: {np.std(z_score_temp):.3f}")
    print(f"Skewness: {stats.skew(z_score_temp):.3f}")
    
    return {
        'raw_stats': {
            'mean': np.mean(temperatures),
            'std': np.std(temperatures),
            'skewness': stats.skew(temperatures),
            'kurtosis': stats.kurtosis(temperatures)
        },
        'transformed_stats': {
            'log': {
                'mean': np.mean(log_temp),
                'std': np.std(log_temp),
                'skewness': stats.skew(log_temp)
            },
            'z_score': {
                'mean': np.mean(z_score_temp),
                'std': np.std(z_score_temp),
                'skewness': stats.skew(z_score_temp)
            }
        }
    }

if __name__ == "__main__":
    # Use the metadata file which contains temperature information
    data_path = "training_data/metadata.tsv"
    stats = load_and_analyze_temperature_data(data_path) 