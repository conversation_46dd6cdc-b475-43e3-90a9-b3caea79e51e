#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Train a FT Transformer model for growth rate and optimal temperature prediction.

This script implements the FT Transformer model from the paper:
"Revisiting Deep Learning Models for Tabular Data" (https://arxiv.org/abs/2106.11959v2)
"""

import os
import argparse
import logging
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.model_selection import train_test_split
import matplotlib.pyplot as plt
from tab_transformer_pytorch import FTTransformer
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TabularDataset(Dataset):
    def __init__(self, features, targets, categorical_cols=None, continuous_cols=None):
        self.features = features
        self.targets = targets
        self.categorical_cols = categorical_cols or []
        self.continuous_cols = continuous_cols or []
        
    def __len__(self):
        return len(self.features)
    
    def __getitem__(self, idx):
        x_categ = torch.tensor(self.features[self.categorical_cols].iloc[idx].values, dtype=torch.long)
        x_cont = torch.tensor(self.features[self.continuous_cols].iloc[idx].values, dtype=torch.float32)
        y = torch.tensor(self.targets.iloc[idx].values, dtype=torch.float32)
        return x_categ, x_cont, y

def load_data(feature_file, metadata_file):
    """
    Load and prepare feature and metadata files.
    
    Args:
        feature_file: Path to the combined feature file (TSV)
        metadata_file: Path to the metadata file (TSV)
        
    Returns:
        Tuple of (features_df, metadata_df)
    """
    logger.info(f"Loading features from {feature_file}")
    features_df = pd.read_csv(feature_file, sep='\t', index_col='genome_id')
    
    # Check for NaN values in features
    nan_count = features_df.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in features. Filling with 0.")
        features_df = features_df.fillna(0)
    
    # Check for infinite values
    inf_count = np.isinf(features_df.values).sum()
    if inf_count > 0:
        logger.warning(f"Found {inf_count} infinite values in features. Replacing with max/min values.")
        features_df = features_df.replace([np.inf, -np.inf], np.nan)
        features_df = features_df.fillna(features_df.max())

    logger.info(f"Loading metadata from {metadata_file}")
    metadata_df = pd.read_csv(metadata_file, sep='\t', index_col='genome_id')
    
    # Check for NaN values in metadata
    nan_count = metadata_df.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in metadata. Filling with 0.")
        metadata_df = metadata_df.fillna(0)

    # Get common genomes
    common_genomes = list(set(features_df.index) & set(metadata_df.index))
    logger.info(f"Found {len(common_genomes)} common genomes")

    # Filter to common genomes
    features_df = features_df.loc[common_genomes]
    metadata_df = metadata_df.loc[common_genomes]
    
    # Merge categorical columns from metadata into features
    categorical_cols = ['kingdom', 'codon_table']
    features_df = features_df.join(metadata_df[categorical_cols])
    
    return features_df, metadata_df

def prepare_data(features_df, metadata_df):
    """
    Prepare data for FT Transformer model.
    
    Args:
        features_df: DataFrame containing features
        metadata_df: DataFrame containing metadata
        
    Returns:
        Tuple of (train_dataset, val_dataset, test_dataset, categorical_cols, continuous_cols)
    """
    # Identify categorical and continuous columns
    categorical_cols = ['kingdom', 'codon_table']
    continuous_cols = [col for col in features_df.columns if col not in categorical_cols and col != 'genome_id']
    
    # Scale continuous features
    scaler = StandardScaler()
    features_df[continuous_cols] = scaler.fit_transform(features_df[continuous_cols])
    
    # Convert categorical columns to integer codes
    for col in categorical_cols:
        features_df[col] = pd.Categorical(features_df[col]).codes
    
    # Split data
    X_train, X_temp, y_train, y_temp = train_test_split(
        features_df, metadata_df[['growth_rate', 'optimal_temperature']], 
        test_size=0.3, random_state=42
    )
    X_val, X_test, y_val, y_test = train_test_split(
        X_temp, y_temp, test_size=0.5, random_state=42
    )
    
    # Create datasets
    train_dataset = TabularDataset(X_train, y_train, categorical_cols, continuous_cols)
    val_dataset = TabularDataset(X_val, y_val, categorical_cols, continuous_cols)
    test_dataset = TabularDataset(X_test, y_test, categorical_cols, continuous_cols)
    
    return train_dataset, val_dataset, test_dataset, categorical_cols, continuous_cols

def train_epoch(model, dataloader, criterion, optimizer, device):
    model.train()
    running_loss = 0.0
    
    for x_categ, x_cont, y in dataloader:
        x_categ, x_cont, y = x_categ.to(device), x_cont.to(device), y.to(device)
        
        optimizer.zero_grad()
        pred = model(x_categ, x_cont)
        loss = criterion(pred, y)
        loss.backward()
        optimizer.step()
        
        running_loss += loss.item() * x_categ.size(0)
    
    return running_loss / len(dataloader.dataset)

def validate(model, dataloader, criterion, device):
    model.eval()
    running_loss = 0.0
    all_targets = []
    all_predictions = []
    
    with torch.no_grad():
        for x_categ, x_cont, y in dataloader:
            x_categ, x_cont, y = x_categ.to(device), x_cont.to(device), y.to(device)
            
            pred = model(x_categ, x_cont)
            loss = criterion(pred, y)
            
            running_loss += loss.item() * x_categ.size(0)
            all_targets.extend(y.cpu().numpy())
            all_predictions.extend(pred.cpu().numpy())
    
    val_loss = running_loss / len(dataloader.dataset)
    val_rmse = np.sqrt(mean_squared_error(all_targets, all_predictions))
    val_r2 = r2_score(all_targets, all_predictions)
    
    return val_loss, val_rmse, val_r2, all_targets, all_predictions

def main():
    parser = argparse.ArgumentParser(description='Train FT Transformer model')
    parser.add_argument('--feature_file', type=str, default='./training_data/combined_features.tsv',
                        help='Path to feature file')
    parser.add_argument('--metadata_file', type=str, default='./training_data/metadata.tsv',
                        help='Path to metadata file')
    parser.add_argument('--batch_size', type=int, default=32,
                        help='Batch size for training')
    parser.add_argument('--epochs', type=int, default=100,
                        help='Number of epochs to train')
    parser.add_argument('--lr', type=float, default=1e-4,
                        help='Learning rate')
    parser.add_argument('--output_dir', type=str, default='./models/fttransformer',
                        help='Directory to save model')
    args = parser.parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Load and prepare data
    features_df, metadata_df = load_data(args.feature_file, args.metadata_file)
    train_dataset, val_dataset, test_dataset, categorical_cols, continuous_cols = prepare_data(features_df, metadata_df)
    
    # Create data loaders
    train_loader = DataLoader(train_dataset, batch_size=args.batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=args.batch_size)
    test_loader = DataLoader(test_dataset, batch_size=args.batch_size)
    
    # Get number of categories for each categorical column
    categories = [len(features_df[col].unique()) for col in categorical_cols]
    
    # Initialize model
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"Using device: {device}")
    
    model = FTTransformer(
        categories=categories,
        num_continuous=len(continuous_cols),
        dim=32,
        dim_out=2,  # Two outputs: growth_rate and optimal_temperature
        depth=6,
        heads=8,
        attn_dropout=0.1,
        ff_dropout=0.1
    ).to(device)
    
    # Initialize optimizer and loss function
    optimizer = optim.Adam(model.parameters(), lr=args.lr)
    criterion = nn.MSELoss()
    
    # Training loop
    best_val_loss = float('inf')
    train_losses = []
    val_losses = []
    val_rmses = []
    val_r2s = []
    
    for epoch in range(args.epochs):
        train_loss = train_epoch(model, train_loader, criterion, optimizer, device)
        val_loss, val_rmse, val_r2, _, _ = validate(model, val_loader, criterion, device)
        
        train_losses.append(train_loss)
        val_losses.append(val_loss)
        val_rmses.append(val_rmse)
        val_r2s.append(val_r2)
        
        logger.info(f'Epoch {epoch+1}/{args.epochs}:')
        logger.info(f'Train Loss: {train_loss:.4f}')
        logger.info(f'Val Loss: {val_loss:.4f}, RMSE: {val_rmse:.4f}, R2: {val_r2:.4f}')
        
        # Save best model
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            torch.save(model.state_dict(), os.path.join(args.output_dir, 'best_model.pth'))
            logger.info(f'Saved new best model with validation loss: {best_val_loss:.4f}')
    
    # Plot training curves
    plt.figure(figsize=(15, 5))
    
    plt.subplot(1, 3, 1)
    plt.plot(train_losses, label='Train Loss')
    plt.plot(val_losses, label='Val Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('Training and Validation Loss')
    plt.legend()
    
    plt.subplot(1, 3, 2)
    plt.plot(val_rmses)
    plt.xlabel('Epoch')
    plt.ylabel('RMSE')
    plt.title('Validation RMSE')
    
    plt.subplot(1, 3, 3)
    plt.plot(val_r2s)
    plt.xlabel('Epoch')
    plt.ylabel('R²')
    plt.title('Validation R²')
    
    plt.tight_layout()
    plt.savefig(os.path.join(args.output_dir, 'training_curves.png'))
    plt.close()
    
    # Evaluate on test set
    model.load_state_dict(torch.load(os.path.join(args.output_dir, 'best_model.pth')))
    test_loss, test_rmse, test_r2, test_targets, test_predictions = validate(model, test_loader, criterion, device)
    logger.info(f'Test Loss: {test_loss:.4f}, RMSE: {test_rmse:.4f}, R2: {test_r2:.4f}')
    
    # Calculate metrics for each target separately
    test_targets = np.array(test_targets)
    test_predictions = np.array(test_predictions)
    
    growth_rate_rmse = np.sqrt(mean_squared_error(test_targets[:, 0], test_predictions[:, 0]))
    growth_rate_r2 = r2_score(test_targets[:, 0], test_predictions[:, 0])
    
    temp_rmse = np.sqrt(mean_squared_error(test_targets[:, 1], test_predictions[:, 1]))
    temp_r2 = r2_score(test_targets[:, 1], test_predictions[:, 1])
    
    logger.info(f'Growth Rate - RMSE: {growth_rate_rmse:.4f}, R2: {growth_rate_r2:.4f}')
    logger.info(f'Optimal Temperature - RMSE: {temp_rmse:.4f}, R2: {temp_r2:.4f}')
    
    # Save metrics to file
    metrics = {
        'test_loss': test_loss,
        'test_rmse': test_rmse,
        'test_r2': test_r2,
        'growth_rate_rmse': growth_rate_rmse,
        'growth_rate_r2': growth_rate_r2,
        'temp_rmse': temp_rmse,
        'temp_r2': temp_r2
    }
    
    with open(os.path.join(args.output_dir, 'metrics.txt'), 'w') as f:
        for key, value in metrics.items():
            f.write(f'{key}: {value:.4f}\n')
    
    # Plot predictions vs actual values
    plt.figure(figsize=(12, 5))
    
    plt.subplot(1, 2, 1)
    plt.scatter(test_targets[:, 0], test_predictions[:, 0], alpha=0.5)
    plt.plot([min(test_targets[:, 0]), max(test_targets[:, 0])], 
             [min(test_targets[:, 0]), max(test_targets[:, 0])], 'r--')
    plt.xlabel('Actual Growth Rate')
    plt.ylabel('Predicted Growth Rate')
    plt.title(f'Growth Rate Prediction (R² = {growth_rate_r2:.4f})')
    
    plt.subplot(1, 2, 2)
    plt.scatter(test_targets[:, 1], test_predictions[:, 1], alpha=0.5)
    plt.plot([min(test_targets[:, 1]), max(test_targets[:, 1])], 
             [min(test_targets[:, 1]), max(test_targets[:, 1])], 'r--')
    plt.xlabel('Actual Optimal Temperature')
    plt.ylabel('Predicted Optimal Temperature')
    plt.title(f'Optimal Temperature Prediction (R² = {temp_r2:.4f})')
    
    plt.tight_layout()
    plt.savefig(os.path.join(args.output_dir, 'predictions.png'))
    plt.close()

if __name__ == '__main__':
    main()
