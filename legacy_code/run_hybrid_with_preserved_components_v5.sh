#!/bin/bash

# Run the hybrid model with strictly preserved components (v5)

# Set output directory
OUTPUT_DIR="models/hybrid_with_preserved_components_v5"

# Create output directory
mkdir -p "$OUTPUT_DIR"

# Run the script
python run_hybrid_with_preserved_components_v5.py \
    --feature_file "./training_data/combined_features.tsv" \
    --metadata_file "./training_data/metadata.tsv" \
    --output_dir "$OUTPUT_DIR" \
    --n_growth_features 150 \
    --n_temp_features 250

# Update the comparison
python simple_compare_models.py --output_dir "model_comparison"

echo "Training and comparison completed. Results are in $OUTPUT_DIR and model_comparison directories." 