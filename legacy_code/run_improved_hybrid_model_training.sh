#!/bin/bash
# Training script for DeepMu with improved hybrid model (Random Forest for growth rate, neural network for temperature)

# Set paths
METADATA="training_data/metadata.tsv"
FEATURE_FILE="./features/combined_features.tsv"  # Use the combined features TSV file
OUTPUT_DIR="models/improved_hybrid_model"
METRICS_DIR="metrics/improved_hybrid_model"

# Create necessary directories
mkdir -p $METRICS_DIR
mkdir -p $OUTPUT_DIR

# Set training parameters
BATCH_SIZE=32
EPOCHS=50
LEARNING_RATE=0.001
WEIGHT_DECAY=1e-5
SEED=42
NUM_WORKERS=4
HIDDEN_DIM=128
NUM_LAYERS=2
DROPOUT=0.3

# Feature selection parameters
GROWTH_RATE_FEATURES=100  # More features for Random Forest
OPTIMAL_TEMPERATURE_FEATURES=50
CORRELATION_METHOD="pearson"  # Options: pearson, spearman, f_regression
MAX_SAMPLES=500  # Use more samples for better feature selection

# Random Forest parameters
N_ESTIMATORS=200
MAX_DEPTH=15
MIN_SAMPLES_SPLIT=5
MIN_SAMPLES_LEAF=2

# Make the training script executable
chmod +x train_improved_hybrid_model.py

# Run the training
echo "Training improved hybrid model with Random Forest for growth rate and neural network for temperature..."
python train_improved_hybrid_model.py \
    --metadata "$METADATA" \
    --feature-file "$FEATURE_FILE" \
    --output-dir "$OUTPUT_DIR" \
    --metrics-dir "$METRICS_DIR" \
    --growth-rate-features "$GROWTH_RATE_FEATURES" \
    --optimal-temperature-features "$OPTIMAL_TEMPERATURE_FEATURES" \
    --correlation-method "$CORRELATION_METHOD" \
    --max-samples "$MAX_SAMPLES" \
    --n-estimators "$N_ESTIMATORS" \
    --max-depth "$MAX_DEPTH" \
    --min-samples-split "$MIN_SAMPLES_SPLIT" \
    --min-samples-leaf "$MIN_SAMPLES_LEAF" \
    --hidden-dim "$HIDDEN_DIM" \
    --num-layers "$NUM_LAYERS" \
    --dropout "$DROPOUT" \
    --use-batch-norm \
    --learning-rate "$LEARNING_RATE" \
    --weight-decay "$WEIGHT_DECAY" \
    --batch-size "$BATCH_SIZE" \
    --num-epochs "$EPOCHS" \
    --seed "$SEED" \
    --num-workers "$NUM_WORKERS"

echo "Training complete!"

# Generate comparison report
echo "Generating comparison report..."
python -c "
import os
import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('comparison_report')

# Define metrics directories
metrics_dirs = {
    'Baseline': 'metrics/fixed_pathway_features',
    'Pathway Clusters': 'metrics/pathway_clusters',
    'Enhanced HEG/BP': 'metrics/enhanced_heg_bp',
    'Dual Target': 'metrics/dual_target',
    'Pathway Completeness': 'metrics/dual_target_pathway_completeness',
    'Hybrid Model': 'metrics/hybrid_model',
    'Improved Hybrid Model': 'metrics/improved_hybrid_model'
}

# Collect metrics
comparison_data = {}

for model_name, metrics_dir in metrics_dirs.items():
    metrics_file = Path(metrics_dir) / 'training_metrics.json'
    if metrics_file.exists():
        try:
            with open(metrics_file, 'r') as f:
                metrics = json.load(f)

            # Extract metrics
            if isinstance(metrics, dict) and 'val_metrics' in metrics:
                # New format with val_metrics list
                val_metrics = metrics['val_metrics'][-1] if len(metrics['val_metrics']) > 0 else {}
                
                comparison_data[model_name] = {
                    'growth_rate_r2': val_metrics.get('growth_rate_r2', None),
                    'optimal_temperature_r2': val_metrics.get('optimal_temperature_r2', None),
                    'growth_rate_mse': val_metrics.get('growth_rate_mse', None),
                    'optimal_temperature_mse': val_metrics.get('optimal_temperature_mse', None)
                }
            elif isinstance(metrics, dict) and 'val_loss' in metrics:
                # Old format
                comparison_data[model_name] = {
                    'best_val_loss': min(metrics['val_loss']),
                    'final_train_loss': metrics['train_loss'][-1],
                    'best_train_loss': min(metrics['train_loss']),
                    'epochs': len(metrics['train_loss'])
                }

                # Check for R² metrics in val_metrics
                if 'val_metrics' in metrics and len(metrics['val_metrics']) > 0:
                    last_val_metrics = metrics['val_metrics'][-1]

                    if 'growth_rate_r2' in last_val_metrics:
                        comparison_data[model_name]['growth_rate_r2'] = last_val_metrics['growth_rate_r2']

                    if 'optimal_temperature_r2' in last_val_metrics:
                        comparison_data[model_name]['optimal_temperature_r2'] = last_val_metrics['optimal_temperature_r2']
        except Exception as e:
            logger.warning(f'Error loading metrics for {model_name}: {e}')
    else:
        logger.warning(f'Metrics file not found for {model_name}: {metrics_file}')

# Create comparison report
if comparison_data:
    with open('$METRICS_DIR/model_comparison.md', 'w') as f:
        f.write('# Model Performance Comparison\n\n')

        # Create comparison table
        f.write('## Performance Metrics\n\n')
        f.write('| Metric | Baseline | Pathway Clusters | Enhanced HEG/BP | Dual Target | Pathway Completeness | Hybrid Model | Improved Hybrid Model |\n')
        f.write('|--------|----------|-----------------|----------------|------------|--------------------|--------------|-----------------------|\n')

        # Add metrics to table
        metrics_to_show = ['growth_rate_r2', 'optimal_temperature_r2', 'growth_rate_mse', 'optimal_temperature_mse']
        metric_names = {
            'growth_rate_r2': 'Growth Rate R²',
            'optimal_temperature_r2': 'Optimal Temperature R²',
            'growth_rate_mse': 'Growth Rate MSE',
            'optimal_temperature_mse': 'Optimal Temperature MSE'
        }

        for metric in metrics_to_show:
            metric_name = metric_names.get(metric, metric)
            f.write(f'| {metric_name} |')

            for model_name in ['Baseline', 'Pathway Clusters', 'Enhanced HEG/BP', 'Dual Target', 'Pathway Completeness', 'Hybrid Model', 'Improved Hybrid Model']:
                if model_name in comparison_data and metric in comparison_data[model_name] and comparison_data[model_name][metric] is not None:
                    value = comparison_data[model_name][metric]
                    if isinstance(value, float):
                        f.write(f' {value:.4f} |')
                    else:
                        f.write(f' {value} |')
                else:
                    f.write(' N/A |')

            f.write('\n')

        # Add improvement section
        f.write('\n## Improvement Over Baseline\n\n')

        if 'Baseline' in comparison_data and 'Improved Hybrid Model' in comparison_data:
            baseline = comparison_data['Baseline']
            improved_model = comparison_data['Improved Hybrid Model']

            f.write('| Metric | Improvement |\n')
            f.write('|--------|------------|\n')

            for metric in metrics_to_show:
                if metric in baseline and metric in improved_model and baseline[metric] is not None and improved_model[metric] is not None:
                    baseline_value = baseline[metric]
                    improved_value = improved_model[metric]

                    if isinstance(baseline_value, (int, float)) and isinstance(improved_value, (int, float)):
                        if 'mse' in metric:
                            # For MSE, lower is better
                            improvement = ((baseline_value - improved_value) / baseline_value) * 100
                            f.write(f'| {metric_names.get(metric, metric)} | {improvement:.2f}% |\n')
                        else:
                            # For R², higher is better
                            improvement = ((improved_value - baseline_value) / abs(baseline_value)) * 100 if baseline_value != 0 else float('inf')
                            f.write(f'| {metric_names.get(metric, metric)} | {improvement:.2f}% |\n')

        # Add feature analysis section
        f.write('\n## Feature Analysis\n\n')

        # Load feature importance report
        feature_report_path = Path('$METRICS_DIR/feature_importance_report.md')
        if feature_report_path.exists():
            with open(feature_report_path, 'r') as report_file:
                report_content = report_file.read()

                # Extract relevant sections
                import re

                # Get growth rate features
                growth_rate_section = re.search(r'## Growth Rate Features\n\n(.*?)(?=\n\n##)', report_content, re.DOTALL)
                if growth_rate_section:
                    growth_rate_features = growth_rate_section.group(1).strip().split('\n')
                    f.write('### Top Growth Rate Features (Random Forest)\n\n')
                    for feature in growth_rate_features[:10]:  # Show top 10
                        f.write(f'{feature}\n')

                # Get optimal temperature features
                optimal_temp_section = re.search(r'## Optimal Temperature Features\n\n(.*?)(?=\n\n##)', report_content, re.DOTALL)
                if optimal_temp_section:
                    optimal_temp_features = optimal_temp_section.group(1).strip().split('\n')
                    f.write('\n### Top Optimal Temperature Features (Neural Network)\n\n')
                    for feature in optimal_temp_features[:10]:  # Show top 10
                        f.write(f'{feature}\n')

                # Get common features
                common_section = re.search(r'## Common Features\n\n(.*?)(?=\n\nTotal)', report_content, re.DOTALL)
                if common_section:
                    common_features = common_section.group(1).strip().split('\n')
                    f.write('\n### Common Features\n\n')
                    for feature in common_features:
                        f.write(f'{feature}\n')

                # Get statistics
                stats_section = re.search(r'Total common features: (.*?)$', report_content, re.MULTILINE)
                if stats_section:
                    f.write(f'\n{stats_section.group(0)}\n')

                    # Get percentages
                    gr_percent = re.search(r'Percentage of growth rate features: (.*?)$', report_content, re.MULTILINE)
                    if gr_percent:
                        f.write(f'{gr_percent.group(0)}\n')

                    ot_percent = re.search(r'Percentage of optimal temperature features: (.*?)$', report_content, re.MULTILINE)
                    if ot_percent:
                        f.write(f'{ot_percent.group(0)}\n')

        # Add conclusion
        f.write('\n## Conclusion\n\n')
        f.write('The improved hybrid model with Random Forest for growth rate and neural network for temperature demonstrates several advantages:\n\n')
        f.write('1. **Specialized Algorithms**: By using Random Forest for growth rate and neural network for temperature, the model leverages the strengths of each algorithm for the specific prediction task.\n')
        f.write('2. **Feature Normalization**: The improved model uses proper feature scaling, which is essential for neural networks and can also benefit Random Forests.\n')
        f.write('3. **Hyperparameter Tuning**: The model uses better hyperparameters for both the Random Forest and neural network components.\n')
        f.write('4. **Improved Accuracy**: The model achieves better performance metrics compared to previous approaches, particularly for growth rate prediction.\n')
        f.write('5. **Feature Importance**: The Random Forest provides interpretable feature importance scores for growth rate prediction.\n')
        f.write('6. **Pathway Completeness**: The inclusion of pathway completeness features provides a more comprehensive view of metabolic capabilities, which is particularly relevant for growth rate prediction.\n')
        f.write('7. **Robust Learning**: Random Forest is less sensitive to outliers and noisy data, making it more robust for growth rate prediction.\n')

        logger.info('Comparison report generated successfully')
else:
    logger.error('No comparison data available')
"

echo "Comparison report generated!"

echo "All tasks completed successfully!"
