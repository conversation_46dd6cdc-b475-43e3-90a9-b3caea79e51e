#!/bin/bash

# Run the combined hybrid model for DeepMu
# This script runs both the growth rate and enhanced temperature components of the hybrid model

echo "Running combined hybrid model..."
mkdir -p models/hybrid_model/plots

# Run the training script
python combined_hybrid_model.py --output-dir models/hybrid_model --growth-features 250 --temp-features 800 --batch-size 64 --patience 30

# Check if training was successful
if [ $? -eq 0 ]; then
    echo "Training completed successfully!"
    echo "Models saved to models/hybrid_model/"
    
    # Display information about the trained models
    echo "Growth model: models/hybrid_model/growth_model/"
    echo "Temperature model: models/hybrid_model/temp_model/"
    echo "Visualization plots available in models/hybrid_model/plots/"
    
    echo "Done!"
else
    echo "Training failed. See logs for details."
fi

# Make the script executable with: chmod +x run_hybrid_model.sh 