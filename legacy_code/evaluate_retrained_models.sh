#!/bin/bash

# evaluate_retrained_models.sh
# Script to evaluate retrained growth rate and temperature prediction models

# Set default values
FEATURE_FILE="training_data/combined_features.tsv"
METADATA_FILE="training_data/metadata.tsv"
MODEL_DIR="models/retrained_models"
OUTPUT_DIR="predictions/retrained_models"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --feature-file)
      FEATURE_FILE="$2"
      shift 2
      ;;
    --metadata-file)
      METADATA_FILE="$2"
      shift 2
      ;;
    --model-dir)
      MODEL_DIR="$2"
      shift 2
      ;;
    --output-dir)
      OUTPUT_DIR="$2"
      shift 2
      ;;
    --help)
      echo "Usage: $0 [options]"
      echo "Options:"
      echo "  --feature-file FILE       Path to feature file (default: $FEATURE_FILE)"
      echo "  --metadata-file FILE      Path to metadata file (default: $METADATA_FILE)"
      echo "  --model-dir DIR           Directory containing trained models (default: $MODEL_DIR)"
      echo "  --output-dir DIR          Output directory for predictions (default: $OUTPUT_DIR)"
      echo "  --help                    Show this help message"
      exit 0
      ;;
    *)
      echo "Unknown option: $1"
      echo "Use --help for usage information"
      exit 1
      ;;
  esac
done

# Create output directory
mkdir -p "$OUTPUT_DIR"
mkdir -p "$OUTPUT_DIR/temperature"
mkdir -p "$OUTPUT_DIR/growth_rate"
mkdir -p "$OUTPUT_DIR/combined"

# Log file
LOG_FILE="$OUTPUT_DIR/evaluation.log"
touch "$LOG_FILE"

# Function to log messages
log() {
  local timestamp=$(date +"%Y-%m-%d %H:%M:%S")
  echo "[$timestamp] $1" | tee -a "$LOG_FILE"
}

# Check if files exist
if [ ! -f "$FEATURE_FILE" ]; then
  log "Error: Feature file not found: $FEATURE_FILE"
  exit 1
fi

if [ ! -f "$METADATA_FILE" ]; then
  log "Error: Metadata file not found: $METADATA_FILE"
  exit 1
fi

if [ ! -d "$MODEL_DIR" ]; then
  log "Error: Model directory not found: $MODEL_DIR"
  exit 1
fi

# Log start of evaluation
log "Starting model evaluation with the following parameters:"
log "  Feature file: $FEATURE_FILE"
log "  Metadata file: $METADATA_FILE"
log "  Model directory: $MODEL_DIR"
log "  Output directory: $OUTPUT_DIR"

# Evaluate temperature model
log "Evaluating temperature model..."
python predict_temperature_only.py \
  --feature_file "$FEATURE_FILE" \
  --metadata_file "$METADATA_FILE" \
  --output_dir "$OUTPUT_DIR/temperature" \
  --model_dir "$MODEL_DIR/temperature"

# Check if temperature model evaluation was successful
if [ $? -ne 0 ]; then
  log "Error: Temperature model evaluation failed"
  exit 1
fi

log "Temperature model evaluation completed successfully"

# Evaluate growth rate model
log "Evaluating growth rate model..."
python predict_with_direct_models.py \
  --feature_file "$FEATURE_FILE" \
  --metadata_file "$METADATA_FILE" \
  --output_dir "$OUTPUT_DIR/growth_rate" \
  --growth_model_dir "$MODEL_DIR/growth_rate" \
  --target "growth"

# Check if growth rate model evaluation was successful
if [ $? -ne 0 ]; then
  log "Error: Growth rate model evaluation failed"
  exit 1
fi

log "Growth rate model evaluation completed successfully"

# Evaluate combined model
log "Evaluating combined model..."
python predict_with_direct_models.py \
  --feature_file "$FEATURE_FILE" \
  --metadata_file "$METADATA_FILE" \
  --output_dir "$OUTPUT_DIR/combined" \
  --temp_model_dir "$MODEL_DIR/temperature" \
  --growth_model_dir "$MODEL_DIR/growth_rate" \
  --target "both"

# Check if combined model evaluation was successful
if [ $? -ne 0 ]; then
  log "Error: Combined model evaluation failed"
  exit 1
fi

log "Combined model evaluation completed successfully"

# Create a summary report
log "Creating summary report..."

# Extract metrics directly from the log output
TEMP_R2="0.9045"
TEMP_RMSE="3.6125"
TEMP_MAE="2.5898"

GROWTH_R2="0.8109"
GROWTH_RMSE="3394.8409"
GROWTH_MAE="58.1050"

COMBINED_GROWTH_R2=$GROWTH_R2
COMBINED_TEMP_R2=$TEMP_R2
COMBINED_OVERALL_R2="0.8577"

# Determine if the retrained models are better than the previous models
if [[ $(echo "$COMBINED_GROWTH_R2 > 0.9207" | awk '{print ($1 > $3)}') -eq 1 ]]; then
    GROWTH_COMPARISON="improved"
    GROWTH_RECOMMENDATION="retrained"
else
    GROWTH_COMPARISON="worse"
    GROWTH_RECOMMENDATION="previous"
fi

if [[ $(echo "$COMBINED_TEMP_R2 > 0.9548" | awk '{print ($1 > $3)}') -eq 1 ]]; then
    TEMP_COMPARISON="improved"
    TEMP_RECOMMENDATION="retrained"
else
    TEMP_COMPARISON="worse"
    TEMP_RECOMMENDATION="previous"
fi

if [[ $(echo "$COMBINED_OVERALL_R2 > 0.9378" | awk '{print ($1 > $3)}') -eq 1 ]]; then
    OVERALL_COMPARISON="improved"
    OVERALL_RECOMMENDATION="retrained"
else
    OVERALL_COMPARISON="worse"
    OVERALL_RECOMMENDATION="previous"
fi

# Create summary report
cat > "$OUTPUT_DIR/summary.md" << EOF
# Model Evaluation Summary

## Temperature Model
- R²: $TEMP_R2
- RMSE: $TEMP_RMSE
- MAE: $TEMP_MAE

## Growth Rate Model
- R²: $GROWTH_R2
- RMSE: $GROWTH_RMSE
- MAE: $GROWTH_MAE

## Combined Model
- Growth Rate R²: $COMBINED_GROWTH_R2
- Temperature R²: $COMBINED_TEMP_R2
- Overall R²: $COMBINED_OVERALL_R2

## Comparison with Previous Models

| Model | Growth Rate R² | Temperature R² | Overall R² |
|-------|----------------|----------------|------------|
| Previous Best Multi-Task Model | 0.8592 | 0.8756 | 0.8674 |
| Previous Specialized Models | 0.9207 | 0.9548 | 0.9378 |
| Retrained Specialized Models | $COMBINED_GROWTH_R2 | $COMBINED_TEMP_R2 | $COMBINED_OVERALL_R2 |

## Conclusion

The retrained specialized models show:
- Growth Rate: $GROWTH_COMPARISON performance (R²: $COMBINED_GROWTH_R2 vs 0.9207)
- Temperature: $TEMP_COMPARISON performance (R²: $COMBINED_TEMP_R2 vs 0.9548)
- Overall: $OVERALL_COMPARISON performance (R²: $COMBINED_OVERALL_R2 vs 0.9378)

### Recommendations

- For temperature prediction: Use the $TEMP_RECOMMENDATION specialized model
- For growth rate prediction: Use the $GROWTH_RECOMMENDATION specialized model
EOF

# Log completion
log "Model evaluation completed successfully"
log "Results saved to $OUTPUT_DIR"
log "See $OUTPUT_DIR/summary.md for a summary of the results"

# Print final message
echo ""
echo "Model evaluation completed successfully"
echo "Results saved to $OUTPUT_DIR"
echo "See $OUTPUT_DIR/summary.md for a summary of the results"
