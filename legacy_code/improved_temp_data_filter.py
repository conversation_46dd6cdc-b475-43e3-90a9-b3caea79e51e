#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Improved Temperature Data Filtering for DeepMu.

This script implements a sophisticated approach to filter out unreliable temperature data
while retaining as many genomes as possible for training.
"""

import os
import argparse
import logging
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from sklearn.preprocessing import StandardScaler
from typing import Tuple, List, Dict, Optional

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def calculate_reliability_score(metadata_df: pd.DataFrame) -> pd.Series:
    """
    Calculate a reliability score for temperature data based on multiple factors.
    
    Args:
        metadata_df: DataFrame containing metadata with temperature values
        
    Returns:
        Series with reliability scores for each genome
    """
    # Extract relevant columns
    temp_values = metadata_df['optimal_temperature'].values
    
    # 1. Temperature range reliability
    # Higher score for temperatures in the common range (30-70°C)
    # Lower score for extreme temperatures that might be less reliable
    temp_range_score = np.exp(-0.005 * (temp_values - 50)**2)
    
    # 2. Kingdom-based reliability
    # Bacteria and archaea tend to have more reliable temperature data
    kingdom_score = metadata_df['kingdom'].map({
        'bac': 1.0,  # Bacteria - most reliable
        'arc': 1.0,  # Archaea - most reliable
        'euk': 0.8,  # Eukaryotes - less reliable
        'vir': 0.6   # Viruses - least reliable
    }).fillna(0.7)
    
    # 3. Consistency with growth rate
    # Calculate expected temperature range based on growth rate
    # Extremely fast-growing organisms at very high temperatures might be suspicious
    growth_rates = metadata_df['growth_rate'].values
    growth_temp_consistency = np.ones_like(temp_values)
    
    # Flag potential inconsistencies between growth rate and temperature
    # Very high growth rates (>10) at extreme temperatures (>60°C) are less likely
    inconsistent_mask = (growth_rates > 10) & (temp_values > 60)
    growth_temp_consistency[inconsistent_mask] = 0.7
    
    # Combine all factors into a single reliability score
    # We can adjust the weights based on domain knowledge
    reliability_score = (
        0.4 * temp_range_score + 
        0.3 * kingdom_score + 
        0.3 * growth_temp_consistency
    )
    
    return pd.Series(reliability_score, index=metadata_df.index)

def detect_temperature_outliers(
    features_df: pd.DataFrame, 
    metadata_df: pd.DataFrame,
    reliability_threshold: float = 0.6,
    prediction_error_threshold: float = 2.0,
    n_estimators: int = 100,
    random_state: int = 42
) -> Tuple[List[str], pd.DataFrame]:
    """
    Detect outliers in temperature data using a combination of reliability scores
    and prediction errors from a simple model.
    
    Args:
        features_df: DataFrame containing genomic features
        metadata_df: DataFrame containing metadata with temperature values
        reliability_threshold: Minimum reliability score to keep a genome
        prediction_error_threshold: Maximum allowed prediction error (in standard deviations)
        n_estimators: Number of trees in the Random Forest model
        random_state: Random seed for reproducibility
        
    Returns:
        Tuple of (list of outlier genome IDs, DataFrame with outlier information)
    """
    # Calculate reliability scores
    reliability_scores = calculate_reliability_score(metadata_df)
    
    # First filter: Remove genomes with very low reliability scores
    low_reliability_genomes = reliability_scores[reliability_scores < reliability_threshold].index.tolist()
    logger.info(f"Identified {len(low_reliability_genomes)} genomes with low reliability scores (<{reliability_threshold})")
    
    # Get common indices between features and metadata
    common_indices = features_df.index.intersection(metadata_df.index)
    X = features_df.loc[common_indices]
    y = metadata_df.loc[common_indices, 'optimal_temperature']
    
    # Remove genomes with low reliability for model training
    reliable_indices = [idx for idx in common_indices if idx not in low_reliability_genomes]
    X_reliable = features_df.loc[reliable_indices]
    y_reliable = metadata_df.loc[reliable_indices, 'optimal_temperature']
    
    # Train a simple Random Forest model on reliable data
    logger.info(f"Training a Random Forest model on {len(reliable_indices)} reliable genomes")
    model = RandomForestRegressor(
        n_estimators=n_estimators,
        max_depth=10,
        min_samples_leaf=5,
        n_jobs=-1,
        random_state=random_state
    )
    model.fit(X_reliable, y_reliable)
    
    # Predict temperatures for all genomes
    y_pred = model.predict(X)
    
    # Calculate prediction errors
    errors = np.abs(y - y_pred)
    mean_error = np.mean(errors)
    std_error = np.std(errors)
    
    # Second filter: Identify genomes with large prediction errors
    error_threshold = mean_error + prediction_error_threshold * std_error
    large_error_genomes = common_indices[errors > error_threshold].tolist()
    logger.info(f"Identified {len(large_error_genomes)} genomes with large prediction errors (>{error_threshold:.2f}°C)")
    
    # Combine both filters to get final list of outliers
    outlier_genomes = list(set(low_reliability_genomes + large_error_genomes))
    logger.info(f"Total outliers identified: {len(outlier_genomes)} genomes")
    
    # Create a DataFrame with outlier information
    outlier_info = pd.DataFrame(index=common_indices)
    outlier_info['true_temperature'] = y
    outlier_info['predicted_temperature'] = y_pred
    outlier_info['prediction_error'] = errors
    outlier_info['reliability_score'] = reliability_scores.loc[common_indices]
    outlier_info['is_low_reliability'] = outlier_info.index.isin(low_reliability_genomes)
    outlier_info['is_large_error'] = outlier_info.index.isin(large_error_genomes)
    outlier_info['is_outlier'] = outlier_info.index.isin(outlier_genomes)
    
    return outlier_genomes, outlier_info

def filter_dataset(
    feature_file: str,
    metadata_file: str,
    output_dir: str,
    reliability_threshold: float = 0.6,
    prediction_error_threshold: float = 2.0,
    visualize: bool = True
) -> Tuple[str, str]:
    """
    Filter the dataset to remove unreliable temperature data.
    
    Args:
        feature_file: Path to the feature file (TSV)
        metadata_file: Path to the metadata file (TSV)
        output_dir: Directory to save the filtered dataset and visualizations
        reliability_threshold: Minimum reliability score to keep a genome
        prediction_error_threshold: Maximum allowed prediction error (in standard deviations)
        visualize: Whether to create visualizations of the filtering process
        
    Returns:
        Tuple of (filtered_features_path, filtered_metadata_path)
    """
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Load data
    logger.info(f"Loading features from {feature_file}")
    features_df = pd.read_csv(feature_file, sep='\t', index_col='genome_id')
    
    logger.info(f"Loading metadata from {metadata_file}")
    metadata_df = pd.read_csv(metadata_file, sep='\t', index_col='genome_id')
    
    # Get common indices
    common_indices = features_df.index.intersection(metadata_df.index)
    features_df = features_df.loc[common_indices]
    metadata_df = metadata_df.loc[common_indices]
    
    logger.info(f"Found {len(common_indices)} genomes with both features and metadata")
    
    # Detect outliers
    outlier_genomes, outlier_info = detect_temperature_outliers(
        features_df,
        metadata_df,
        reliability_threshold=reliability_threshold,
        prediction_error_threshold=prediction_error_threshold
    )
    
    # Save outlier information
    outlier_info_path = os.path.join(output_dir, 'temperature_outlier_info.tsv')
    outlier_info.to_csv(outlier_info_path, sep='\t')
    logger.info(f"Saved outlier information to {outlier_info_path}")
    
    # Filter out outliers
    reliable_genomes = [idx for idx in common_indices if idx not in outlier_genomes]
    filtered_features = features_df.loc[reliable_genomes]
    filtered_metadata = metadata_df.loc[reliable_genomes]
    
    logger.info(f"Retained {len(reliable_genomes)} genomes after filtering ({len(reliable_genomes)/len(common_indices)*100:.1f}%)")
    
    # Save filtered datasets
    filtered_features_path = os.path.join(output_dir, 'filtered_features.tsv')
    filtered_metadata_path = os.path.join(output_dir, 'filtered_metadata.tsv')
    
    filtered_features.to_csv(filtered_features_path, sep='\t')
    filtered_metadata.to_csv(filtered_metadata_path, sep='\t')
    
    logger.info(f"Saved filtered features to {filtered_features_path}")
    logger.info(f"Saved filtered metadata to {filtered_metadata_path}")
    
    # Create visualizations
    if visualize:
        # 1. Scatter plot of true vs. predicted temperatures
        plt.figure(figsize=(10, 8))
        plt.scatter(
            outlier_info[~outlier_info['is_outlier']]['true_temperature'],
            outlier_info[~outlier_info['is_outlier']]['predicted_temperature'],
            alpha=0.5, label='Retained'
        )
        plt.scatter(
            outlier_info[outlier_info['is_outlier']]['true_temperature'],
            outlier_info[outlier_info['is_outlier']]['predicted_temperature'],
            alpha=0.5, color='red', label='Filtered Out'
        )
        plt.plot([0, 100], [0, 100], 'k--')
        plt.xlabel('True Temperature (°C)')
        plt.ylabel('Predicted Temperature (°C)')
        plt.title('True vs. Predicted Temperature')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.savefig(os.path.join(output_dir, 'temperature_prediction.png'), dpi=300)
        
        # 2. Histogram of reliability scores
        plt.figure(figsize=(10, 6))
        plt.hist(
            outlier_info[~outlier_info['is_outlier']]['reliability_score'],
            bins=20, alpha=0.5, label='Retained'
        )
        plt.hist(
            outlier_info[outlier_info['is_outlier']]['reliability_score'],
            bins=20, alpha=0.5, color='red', label='Filtered Out'
        )
        plt.axvline(reliability_threshold, color='k', linestyle='--', label=f'Threshold ({reliability_threshold})')
        plt.xlabel('Reliability Score')
        plt.ylabel('Count')
        plt.title('Distribution of Reliability Scores')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.savefig(os.path.join(output_dir, 'reliability_scores.png'), dpi=300)
        
        # 3. Histogram of prediction errors
        plt.figure(figsize=(10, 6))
        plt.hist(
            outlier_info[~outlier_info['is_outlier']]['prediction_error'],
            bins=20, alpha=0.5, label='Retained'
        )
        plt.hist(
            outlier_info[outlier_info['is_outlier']]['prediction_error'],
            bins=20, alpha=0.5, color='red', label='Filtered Out'
        )
        mean_error = outlier_info['prediction_error'].mean()
        std_error = outlier_info['prediction_error'].std()
        error_threshold = mean_error + prediction_error_threshold * std_error
        plt.axvline(error_threshold, color='k', linestyle='--', label=f'Threshold ({error_threshold:.2f}°C)')
        plt.xlabel('Prediction Error (°C)')
        plt.ylabel('Count')
        plt.title('Distribution of Prediction Errors')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.savefig(os.path.join(output_dir, 'prediction_errors.png'), dpi=300)
        
        logger.info(f"Saved visualizations to {output_dir}")
    
    return filtered_features_path, filtered_metadata_path

def main():
    parser = argparse.ArgumentParser(description='Filter dataset to remove unreliable temperature data')
    parser.add_argument('--feature-file', type=str, default='./training_data/combined_features.tsv',
                        help='Path to the combined feature file (TSV)')
    parser.add_argument('--metadata-file', type=str, default='./training_data/metadata.tsv',
                        help='Path to the metadata file (TSV)')
    parser.add_argument('--output-dir', type=str, default='./training_data/filtered',
                        help='Directory to save the filtered dataset')
    parser.add_argument('--reliability-threshold', type=float, default=0.6,
                        help='Minimum reliability score to keep a genome')
    parser.add_argument('--prediction-error-threshold', type=float, default=2.0,
                        help='Maximum allowed prediction error (in standard deviations)')
    parser.add_argument('--no-visualize', action='store_false', dest='visualize',
                        help='Disable visualization generation')
    
    args = parser.parse_args()
    
    try:
        filter_dataset(
            args.feature_file,
            args.metadata_file,
            args.output_dir,
            reliability_threshold=args.reliability_threshold,
            prediction_error_threshold=args.prediction_error_threshold,
            visualize=args.visualize
        )
        logger.info("Filtering completed successfully")
        return 0
    
    except Exception as e:
        logger.error(f"Error filtering dataset: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == '__main__':
    main()
