#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to visualize the predictions from the DeepMu model.
"""

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import sys
import os

def visualize_predictions(predictions_file):
    """Visualize the predictions from the DeepMu model."""
    try:
        # Load the predictions
        df = pd.read_csv(predictions_file, sep='\t')
        
        # Print summary statistics
        print(f"Prediction summary statistics:")
        print(f"Growth rate: min={df['growth_rate'].min():.4f}, max={df['growth_rate'].max():.4f}, mean={df['growth_rate'].mean():.4f}, median={df['growth_rate'].median():.4f}")
        
        if 'optimal_temperature' in df.columns and df['optimal_temperature'].dtype != object:
            print(f"Optimal temperature: min={df['optimal_temperature'].min():.4f}, max={df['optimal_temperature'].max():.4f}, mean={df['optimal_temperature'].mean():.4f}, median={df['optimal_temperature'].median():.4f}")
        
        # Set up the figure
        plt.figure(figsize=(15, 10))
        
        # Create a histogram of growth rates
        plt.subplot(2, 2, 1)
        sns.histplot(df['growth_rate'], kde=True)
        plt.title('Distribution of Predicted Growth Rates')
        plt.xlabel('Growth Rate (doubling/hour)')
        plt.ylabel('Count')
        
        # Create a histogram of optimal temperatures if available
        if 'optimal_temperature' in df.columns and df['optimal_temperature'].dtype != object:
            plt.subplot(2, 2, 2)
            sns.histplot(df['optimal_temperature'], kde=True)
            plt.title('Distribution of Predicted Optimal Temperatures')
            plt.xlabel('Optimal Temperature (°C)')
            plt.ylabel('Count')
            
            # Create a scatter plot of growth rate vs. optimal temperature
            plt.subplot(2, 2, 3)
            sns.scatterplot(x='growth_rate', y='optimal_temperature', data=df)
            plt.title('Growth Rate vs. Optimal Temperature')
            plt.xlabel('Growth Rate (doubling/hour)')
            plt.ylabel('Optimal Temperature (°C)')
            
            # Add a trend line
            x = df['growth_rate']
            y = df['optimal_temperature']
            z = np.polyfit(x, y, 1)
            p = np.poly1d(z)
            plt.plot(x, p(x), "r--", alpha=0.8)
            
            # Calculate correlation
            corr = df['growth_rate'].corr(df['optimal_temperature'])
            plt.annotate(f"Correlation: {corr:.4f}", xy=(0.05, 0.95), xycoords='axes fraction')
        
        # Create a bar plot of the top 10 fastest growing organisms
        plt.subplot(2, 2, 4)
        top_10 = df.sort_values('growth_rate', ascending=False).head(10)
        sns.barplot(x='growth_rate', y='sample_id', data=top_10)
        plt.title('Top 10 Fastest Growing Organisms')
        plt.xlabel('Growth Rate (doubling/hour)')
        plt.ylabel('Sample ID')
        
        # Adjust layout and save
        plt.tight_layout()
        plt.savefig('prediction_visualization.png', dpi=300)
        print(f"Visualization saved to prediction_visualization.png")
        
        return 0
    except Exception as e:
        print(f"Error visualizing predictions: {e}")
        return 1

if __name__ == '__main__':
    if len(sys.argv) != 2:
        print(f"Usage: {sys.argv[0]} <predictions_file>")
        sys.exit(1)
    
    predictions_file = sys.argv[1]
    sys.exit(visualize_predictions(predictions_file))
