#!/bin/bash

# DeepMu: Final Hybrid Model with RF Temperature Model
# This script trains a hybrid model with Random Forest for temperature and DNN for growth rate

# Set a clean output directory
OUTPUT_DIR="models/deepmu_rf_temp_model"

# Create output directory
mkdir -p "$OUTPUT_DIR"

# Define color codes for output formatting
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Display initial message
echo -e "${BLUE}${BOLD}========================================================${NC}"
echo -e "${BLUE}${BOLD}   DeepMu - RF Temperature + DNN Growth Hybrid Model   ${NC}"
echo -e "${BLUE}${BOLD}========================================================${NC}"
echo -e "${GREEN}This script uses the optimal model configuration:${NC}"
echo -e "  - Temperature model: ${YELLOW}Random Forest Ensemble (proven >0.92 R²)${NC}"
echo -e "  - Growth rate model: ${YELLOW}DNN (unchanged, already high performance)${NC}"
echo -e "  - Temperature transformation: ${YELLOW}log2${NC}"
echo -e "  - Growth rate transformation: ${YELLOW}log2${NC}"
echo -e "  - Outlier detection and removal${NC}"
echo -e ""
echo -e "${YELLOW}The script will:${NC}"
echo -e "  1. Identify and filter outliers from the dataset"
echo -e "  2. Train a Random Forest for temperature prediction"
echo -e "  3. Keep the proven DNN for growth rate prediction"
echo -e "  4. Apply appropriate data transformations"
echo -e ""
echo -e "${YELLOW}Output will be saved to:${NC} ${OUTPUT_DIR}"
echo ""

# Check if prerequisites are available
echo -e "${YELLOW}Checking prerequisites...${NC}"
if [ ! -f "./training_data/combined_features.tsv" ]; then
    echo -e "${RED}Error: Feature file not found. Please ensure './training_data/combined_features.tsv' exists.${NC}"
    exit 1
fi

if [ ! -f "./training_data/metadata.tsv" ]; then
    echo -e "${RED}Error: Metadata file not found. Please ensure './training_data/metadata.tsv' exists.${NC}"
    exit 1
fi

echo -e "${GREEN}All prerequisites found.${NC}"
echo ""

# Detect dataset size and adapt parameters
echo -e "${YELLOW}Checking dataset size...${NC}"
DATASET_SIZE=$(wc -l < "./training_data/combined_features.tsv")
DATASET_SIZE=$((DATASET_SIZE - 1))  # Subtract header line

echo -e "Detected ${BOLD}${DATASET_SIZE}${NC} genomes in dataset"

# Set parameters regardless of dataset size, but optimize features count
if [ $DATASET_SIZE -lt 3000 ]; then
    echo -e "${YELLOW}Small dataset detected (< 3000 genomes). Optimizing parameters...${NC}"
    N_GROWTH_FEATURES=150
    N_TEMP_FEATURES=300  # More features for RF temperature model
elif [ $DATASET_SIZE -gt 5000 ]; then
    echo -e "${YELLOW}Large dataset detected (> 5000 genomes). Optimizing parameters...${NC}"
    N_GROWTH_FEATURES=250
    N_TEMP_FEATURES=500  # More features for RF temperature model
else
    echo -e "${YELLOW}Medium dataset detected. Using balanced parameters...${NC}"
    N_GROWTH_FEATURES=200
    N_TEMP_FEATURES=400  # Balanced features for RF temperature model
fi

# Run outlier detection as a separate step to generate cleaned datasets
echo -e "${YELLOW}Running outlier detection with transformations and Z-score thresholds...${NC}"
python detect_outliers.py \
    --feature_file "./training_data/combined_features.tsv" \
    --metadata_file "./training_data/metadata.tsv" \
    --output_dir "$OUTPUT_DIR" \
    --zscore_threshold $([ $DATASET_SIZE -lt 3000 ] && echo "6.0" || echo "7.0") \
    --max_removal_pct $([ $DATASET_SIZE -lt 3000 ] && echo "15.0" || echo "20.0") \
    --visualize_outliers

# Check if outlier detection was successful
if [ $? -eq 0 ]; then
    echo -e "${GREEN}Outlier detection completed. Using cleaned datasets for training.${NC}"
    FEATURES_FILE="${OUTPUT_DIR}/cleaned_features.tsv"
    METADATA_FILE="${OUTPUT_DIR}/cleaned_metadata.tsv"
else
    echo -e "${YELLOW}Warning: Outlier detection failed or no significant outliers found. Using original datasets.${NC}"
    FEATURES_FILE="./training_data/combined_features.tsv"
    METADATA_FILE="./training_data/metadata.tsv"
fi

# Train with fixed transformations and RF for temperature
echo -e "\n${BLUE}${BOLD}========================================================${NC}"
echo -e "${BLUE}${BOLD}   Training Hybrid Model with RF for Temperature   ${NC}"
echo -e "${BLUE}${BOLD}========================================================${NC}"

# Set environment variables for deterministic behavior
export PYTHONHASHSEED=42
export TORCH_CUDNN_DETERMINISTIC=1
export TORCH_DETERMINISTIC=1
export CUBLAS_WORKSPACE_CONFIG=:4096:8

# Run the Python script with optimized parameters
python rf_temp_hybrid_model.py \
    --feature_file "$FEATURES_FILE" \
    --metadata_file "$METADATA_FILE" \
    --output_dir "$OUTPUT_DIR" \
    --n_growth_features $N_GROWTH_FEATURES \
    --n_temp_features $N_TEMP_FEATURES \
    --seed 42 \
    --batch_size 64 \
    --temp_transform "log2" \
    --growth_transform "log2" \
    --rf_n_estimators 300 \
    --rf_max_depth 20 \
    --rf_min_samples_leaf 2 \
    --missing_threshold 0.5 \
    --use_rf_temp_model \
    --verbose 2>&1 | tee "${OUTPUT_DIR}/training.log"

# Check if training was successful
if [ $? -eq 0 ]; then
    echo ""
    echo -e "${GREEN}${BOLD}========================================================${NC}"
    echo -e "${GREEN}${BOLD}   Training completed successfully!   ${NC}"
    echo -e "${GREEN}${BOLD}========================================================${NC}"
    echo ""
    echo -e "${GREEN}Model saved to:${NC} ${OUTPUT_DIR}"
    
else
    echo ""
    echo -e "${RED}${BOLD}========================================================${NC}"
    echo -e "${RED}${BOLD}   Training failed!   ${NC}"
    echo -e "${RED}${BOLD}========================================================${NC}"
    echo ""
    echo -e "${RED}Please check the error messages above for details.${NC}"
fi

echo ""
echo -e "${GREEN}Done.${NC}" 