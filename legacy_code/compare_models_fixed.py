#!/usr/bin/env python3
"""
Compare different models for growth rate prediction.
"""

import os
import math
import argparse
import logging
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Optional
from sklearn.metrics import mean_squared_error, r2_score
from pytorch_tabular import TabularModel
from pytorch_tabular.config import DataConfig, OptimizerConfig, TrainerConfig
from pytorch_tabular.models import category_embedding, tab_transformer

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_data(feature_file: str, metadata_file: str) -> tuple:
    """
    Load feature and metadata files.

    Args:
        feature_file: Path to the feature file
        metadata_file: Path to the metadata file

    Returns:
        Tuple of (features, metadata)
    """
    # Load features
    features = pd.read_csv(feature_file, sep='\t', index_col=0)

    # Load metadata
    metadata = pd.read_csv(metadata_file, sep='\t', index_col=0)

    # Ensure the indices match
    common_indices = features.index.intersection(metadata.index)
    features = features.loc[common_indices]
    metadata = metadata.loc[common_indices]

    logger.info(f"Loaded {len(features)} samples with {features.shape[1]} features")

    return features, metadata

def get_model_config(model_name: str):
    """Get model configuration based on model name."""
    if model_name == "CategoryEmbedding":
        return category_embedding.CategoryEmbeddingModelConfig(
            task="regression",
            layers="256-128-64",
            activation="LeakyReLU",
            learning_rate=1e-3,
            metrics=["mean_squared_error"],
            metrics_params=[{}],
            target_range=None,  # No target range for regression
        )
    elif model_name == "TabTransformer":
        return tab_transformer.TabTransformerConfig(
            task="regression",
            input_embed_dim=32,
            num_attn_blocks=3,
            num_heads=8,
            attn_dropout=0.1,
            add_norm_dropout=0.1,
            ff_dropout=0.1,
            learning_rate=1e-3,
            metrics=["mean_squared_error"],
            metrics_params=[{}],
            target_range=None,  # No target range for regression
        )
    else:
        raise ValueError(f"Unknown model name: {model_name}")

def train_model(model_name: str, features: pd.DataFrame, targets: pd.DataFrame) -> Optional[TabularModel]:
    """
    Train a model with the given configuration
    """
    try:
        # Check for NaN values
        nan_features = features.isna().sum().sum()
        nan_targets = targets.isna().sum().sum()

        if nan_features > 0:
            logger.warning(f"Found {nan_features} NaN values in features. Filling with 0.")
            features = features.fillna(0)

        if nan_targets > 0:
            logger.warning(f"Found {nan_targets} NaN values in targets. Filling with 0.")
            targets = targets.fillna(0)

        # Get categorical and continuous columns
        categorical_cols = features.select_dtypes(include=['object', 'category']).columns.tolist()
        continuous_cols = features.select_dtypes(include=['float64', 'int64']).columns.tolist()

        # Basic configurations
        data_config = DataConfig(
            target=['growth_rate', 'optimal_temperature'],
            categorical_cols=categorical_cols,
            continuous_cols=continuous_cols,
            continuous_feature_transform="quantile_normal",
            normalize_continuous_features=True
        )

        trainer_config = TrainerConfig(
            max_epochs=20,  # Reduce epochs for faster training
            checkpoints=None,
            early_stopping="valid_loss",
            early_stopping_patience=5,
            batch_size=64,
            accelerator="auto",
            check_val_every_n_epoch=1,  # Check validation every epoch
            trainer_kwargs={"log_every_n_steps": 10}  # Log more frequently
        )

        optimizer_config = OptimizerConfig()

        # Get the model config based on the model name
        model_config = get_model_config(model_name)

        # Create the model with the appropriate configuration
        model = TabularModel(
            data_config=data_config,
            model_config=model_config,
            optimizer_config=optimizer_config,
            trainer_config=trainer_config,
        )

        return model
    except Exception as e:
        logger.error(f"Error creating {model_name} model: {e}")
        return None

def train_and_evaluate_model(model_name: str, features: pd.DataFrame, metadata: pd.DataFrame, output_dir: str) -> Dict[str, float]:
    """
    Train and evaluate a model, saving it to disk
    """
    try:
        # Prepare target variables
        targets = metadata[['growth_rate', 'optimal_temperature']]

        # Train model
        model = train_model(model_name, features, targets)

        if model is None:
            logger.error(f"Failed to create {model_name} model")
            return {
                "test_mean_squared_error": float('nan'),
                "test_mean_squared_error_0": float('nan'),
                "test_mean_squared_error_1": float('nan')
            }

        # Combine features and targets for training
        train_data = pd.concat([features, targets], axis=1)

        # Train the model
        logger.info(f"Training {model_name} model with {len(train_data)} samples")
        model.fit(train=train_data)
        logger.info(f"Training {model_name} model completed")

        # Make predictions
        try:
            predictions = model.predict(features)

            # Check the column names in predictions
            logger.info(f"Prediction columns: {predictions.columns.tolist()}")

            # PyTorch Tabular might use different column names for predictions
            # Try to find the correct column names
            if 'growth_rate' in predictions.columns and 'optimal_temperature' in predictions.columns:
                pred_growth_col = 'growth_rate'
                pred_temp_col = 'optimal_temperature'
            else:
                # Use the first two columns as predictions for growth rate and optimal temperature
                pred_cols = predictions.columns.tolist()
                if len(pred_cols) >= 2:
                    pred_growth_col = pred_cols[0]
                    pred_temp_col = pred_cols[1]
                    logger.info(f"Using {pred_growth_col} for growth rate predictions and {pred_temp_col} for temperature predictions")
                else:
                    # If there's only one prediction column, use it for both
                    pred_growth_col = pred_cols[0]
                    pred_temp_col = pred_cols[0]
                    logger.warning(f"Only one prediction column found: {pred_growth_col}. Using it for both metrics.")

            # Calculate metrics
            mse_growth = np.mean((predictions[pred_growth_col] - targets['growth_rate'])**2)
            mse_temp = np.mean((predictions[pred_temp_col] - targets['optimal_temperature'])**2)
            mse_total = (mse_growth + mse_temp) / 2

            # Calculate R² scores
            r2_growth = r2_score(targets['growth_rate'], predictions[pred_growth_col])
            r2_temp = r2_score(targets['optimal_temperature'], predictions[pred_temp_col])

            logger.info(f"Growth rate R²: {r2_growth:.4f}, Temperature R²: {r2_temp:.4f}")

        except Exception as e:
            logger.error(f"Error making predictions: {e}")
            # Return default values if prediction fails
            mse_growth = float('nan')
            mse_temp = float('nan')
            mse_total = float('nan')

        # Save model
        try:
            model_path = os.path.join(output_dir, f"{model_name}_model")
            model.save_model(model_path)
            logger.info(f"Model saved to {model_path}")
        except Exception as e:
            logger.error(f"Error saving model: {e}")

        return {
            "test_mean_squared_error": mse_total,
            "test_mean_squared_error_0": mse_growth,
            "test_mean_squared_error_1": mse_temp
        }
    except Exception as e:
        logger.error(f"Error in train_and_evaluate_model for {model_name}: {e}")
        return {
            "test_mean_squared_error": float('nan'),
            "test_mean_squared_error_0": float('nan'),
            "test_mean_squared_error_1": float('nan')
        }

def plot_results(results_list, output_dir):
    """Plot comparison of model results."""
    try:
        # Convert results list to a DataFrame for easier plotting
        results_df = pd.DataFrame(results_list)

        # Create a figure with multiple subplots
        _, axes = plt.subplots(1, 2, figsize=(15, 5))

        # Plot MSE and RMSE for each model
        sns.barplot(x='model', y='mse', data=results_df, ax=axes[0])
        axes[0].set_title('Model Comparison - MSE')
        axes[0].tick_params(axis='x', rotation=45)

        sns.barplot(x='model', y='rmse', data=results_df, ax=axes[1])
        axes[1].set_title('Model Comparison - RMSE')
        axes[1].tick_params(axis='x', rotation=45)

        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'model_comparison.png'))
        plt.close()
    except Exception as e:
        logger.error(f"Error plotting results: {e}")

def main():
    parser = argparse.ArgumentParser(description="Compare different models for growth rate prediction")
    parser.add_argument("--feature_file", required=True, help="Path to the feature file")
    parser.add_argument("--metadata_file", required=True, help="Path to the metadata file")
    parser.add_argument("--output_dir", default="models/comparison", help="Directory to save model outputs")
    args = parser.parse_args()

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Load data
    features, metadata = load_data(args.feature_file, args.metadata_file)

    # Train and evaluate models
    results = []
    for model_name in ["CategoryEmbedding", "TabTransformer"]:
        logger.info(f"Training {model_name} model")

        # Train and evaluate model
        result = train_and_evaluate_model(model_name, features, metadata, args.output_dir)

        # Store results
        # Calculate RMSE safely, handling NaN values
        def safe_sqrt(x):
            if np.isnan(x):
                return float('nan')
            return math.sqrt(x) if x >= 0 else float('nan')

        results.append({
            "model": model_name,
            "mse": result["test_mean_squared_error"],
            "rmse": safe_sqrt(result["test_mean_squared_error"]),
            "mse_0": result["test_mean_squared_error_0"],
            "rmse_0": safe_sqrt(result["test_mean_squared_error_0"]),
            "mse_1": result["test_mean_squared_error_1"],
            "rmse_1": safe_sqrt(result["test_mean_squared_error_1"])
        })

        # Print results
        logger.info(f"{model_name} Results:")

        # Handle NaN values in the results
        mse = results[-1]['mse']
        rmse = results[-1]['rmse']
        mse_0 = results[-1]['mse_0']
        rmse_0 = results[-1]['rmse_0']
        mse_1 = results[-1]['mse_1']
        rmse_1 = results[-1]['rmse_1']

        # Format the results, handling NaN values
        logger.info(f"MSE: {mse:.4f}" if not np.isnan(mse) else "MSE: NaN")
        logger.info(f"RMSE: {rmse:.4f}" if not np.isnan(rmse) else "RMSE: NaN")
        logger.info(f"MSE_0: {mse_0:.4f}" if not np.isnan(mse_0) else "MSE_0: NaN")
        logger.info(f"RMSE_0: {rmse_0:.4f}" if not np.isnan(rmse_0) else "RMSE_0: NaN")
        logger.info(f"MSE_1: {mse_1:.4f}" if not np.isnan(mse_1) else "MSE_1: NaN")
        logger.info(f"RMSE_1: {rmse_1:.4f}" if not np.isnan(rmse_1) else "RMSE_1: NaN")

    # Save results
    results_df = pd.DataFrame(results)
    results_df.to_csv(os.path.join(args.output_dir, "model_comparison.csv"), index=False)
    logger.info(f"Results saved to {os.path.join(args.output_dir, 'model_comparison.csv')}")

    # Plot results
    plot_results(results, args.output_dir)
    logger.info(f"Plot saved to {os.path.join(args.output_dir, 'model_comparison.png')}")

if __name__ == '__main__':
    main()
