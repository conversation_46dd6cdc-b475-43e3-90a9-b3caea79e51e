#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Predict Temperature with Z-Score Outlier Detection.
This script uses z-score-based outlier detection with a threshold of 7 z-scores
for temperature prediction.
"""

import os
import argparse
import logging
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import torch
import joblib
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from scipy import stats

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Define the RegularizedDNN model class
import torch.nn as nn

class RegularizedDNN(nn.Module):
    """
    Regularized Deep Neural Network with batch normalization, dropout, and gradient clipping.
    """
    def __init__(self, input_dim, hidden_dims=[512, 512, 384, 256, 128], dropout_rates=[0.3, 0.4, 0.4, 0.5, 0.5],
                 use_batch_norm=True, activation='relu'):
        super(RegularizedDNN, self).__init__()

        # Create layers
        layers = []
        prev_dim = input_dim

        # Choose activation function
        if activation == 'relu':
            act_fn = nn.ReLU()
        elif activation == 'leaky_relu':
            act_fn = nn.LeakyReLU(0.1)
        elif activation == 'elu':
            act_fn = nn.ELU()
        else:
            act_fn = nn.ReLU()

        # Build network with progressively increasing dropout
        for i, hidden_dim in enumerate(hidden_dims):
            # Linear layer
            layers.append(nn.Linear(prev_dim, hidden_dim))

            # Batch normalization (before activation)
            if use_batch_norm:
                layers.append(nn.BatchNorm1d(hidden_dim))

            # Activation
            layers.append(act_fn)

            # Dropout with increasing rate for deeper layers
            dropout_rate = dropout_rates[min(i, len(dropout_rates)-1)]
            layers.append(nn.Dropout(dropout_rate))

            prev_dim = hidden_dim

        self.feature_extractor = nn.Sequential(*layers)

        # Output layer with no activation or dropout
        self.output_layer = nn.Linear(prev_dim, 1)

        # Initialize weights with improved method
        self._init_weights()

    def _init_weights(self):
        """Initialize weights with improved initialization."""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                # Use Xavier/Glorot initialization for better stability
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.zeros_(m.bias)

    def forward(self, x):
        features = self.feature_extractor(x)
        output = self.output_layer(features)
        return output.squeeze()

def detect_temperature_outliers(temperatures, zscore_threshold=7.0, apply_transform=False):
    """
    Detect outliers in temperature using z-score method.

    Args:
        temperatures: Series of temperature values
        zscore_threshold: Z-score threshold for outlier detection
        apply_transform: Whether to apply transformation (not recommended for temperature)

    Returns:
        Boolean Series indicating outliers
    """
    logger.info(f"Detecting outliers in temperature using z-score method (threshold: {zscore_threshold})...")

    # Drop NaN values
    y_temp_valid = temperatures.dropna()

    # Apply transformation to normalize the distribution if requested
    # Note: For temperature, transformation is usually not needed
    if apply_transform:
        # For temperature: log transformation (handling zeros and negatives safely)
        y_temp_min = y_temp_valid.min()

        # If there are non-positive values, shift to make all values positive
        if y_temp_min <= 0:
            offset = abs(y_temp_min) + 1
            y_temp_trans = np.log(y_temp_valid + offset)
        else:
            y_temp_trans = np.log(y_temp_valid)
    else:
        # Use original values
        y_temp_trans = y_temp_valid

    # Calculate z-scores
    temp_z = stats.zscore(y_temp_trans)

    # Identify outliers based on z-score
    temp_outliers_array = abs(temp_z) > zscore_threshold

    # Convert back to Series with original indices
    temp_outliers = pd.Series(False, index=temperatures.index)
    temp_outliers[y_temp_valid.index] = temp_outliers_array

    # Log results
    logger.info(f"Identified {temp_outliers.sum()} temperature outliers ({temp_outliers.sum()/len(temperatures):.2%})")
    logger.info(f"Temperature Z-score threshold: {zscore_threshold}")

    return temp_outliers

def load_data(feature_file: str, metadata_file: str, zscore_threshold: float = 7.0, apply_transform: bool = False) -> tuple:
    """
    Load feature and metadata files and filter outliers using z-score method.

    Args:
        feature_file: Path to feature file
        metadata_file: Path to metadata file
        zscore_threshold: Z-score threshold for outlier detection
        apply_transform: Whether to apply transformation for outlier detection

    Returns:
        Tuple of (features, metadata)
    """
    logger.info(f"Loading features from {feature_file}")
    features = pd.read_csv(feature_file, sep='\t', index_col='genome_id')

    logger.info(f"Loading metadata from {metadata_file}")
    metadata = pd.read_csv(metadata_file, sep='\t', index_col='genome_id')

    # Get common samples
    common_indices = features.index.intersection(metadata.index)
    logger.info(f"Found {len(common_indices)} common samples")

    # Filter to common samples
    features = features.loc[common_indices]
    metadata = metadata.loc[common_indices]

    # Check for NaN values in features
    nan_count = features.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in features. Filling with 0.")
        features = features.fillna(0)

    # Check for infinite values
    inf_count = np.isinf(features.values).sum()
    if inf_count > 0:
        logger.warning(f"Found {inf_count} infinite values in features. Replacing with max/min values.")
        features = features.replace([np.inf, -np.inf], np.nan)
        features = features.fillna(features.max())

    # Check for NaN values in metadata
    nan_count = metadata.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in metadata. Filling with 0.")
        metadata = metadata.fillna(0)

    # Log statistics before filtering
    logger.info(f"Temperature statistics before filtering - Min: {metadata['optimal_temperature'].min():.4f}, Max: {metadata['optimal_temperature'].max():.4f}, Mean: {metadata['optimal_temperature'].mean():.4f}, Median: {metadata['optimal_temperature'].median():.4f}")

    # Detect outliers using z-score method
    temp_outliers = detect_temperature_outliers(metadata['optimal_temperature'], zscore_threshold, apply_transform)

    # Filter out outliers
    outlier_indices = metadata.index[temp_outliers]
    metadata = metadata[~temp_outliers]
    features = features.drop(outlier_indices, errors='ignore')

    # Log statistics after filtering
    logger.info(f"Temperature statistics after filtering - Min: {metadata['optimal_temperature'].min():.4f}, Max: {metadata['optimal_temperature'].max():.4f}, Mean: {metadata['optimal_temperature'].mean():.4f}, Median: {metadata['optimal_temperature'].median():.4f}")

    return features, metadata

def predict_temperature(features, metadata, model_dir):
    """
    Predict optimal temperature using the enhanced DNN model.

    Args:
        features: Feature DataFrame
        metadata: Metadata DataFrame
        model_dir: Directory containing the temperature model

    Returns:
        DataFrame with actual and predicted temperatures
    """
    logger.info(f"Predicting optimal temperature for {len(features)} samples")

    # Load scaler
    scaler = joblib.load(os.path.join(model_dir, 'temperature_scaler.joblib'))
    logger.info(f"Temperature scaler loaded successfully")

    # Load model
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model_state = torch.load(os.path.join(model_dir, 'temperature_model.pt'), map_location=device)

    # Extract input dimension from the first layer's weight shape
    input_dim = model_state['feature_extractor.0.weight'].shape[1]
    logger.info(f"Temperature model input dimension: {input_dim}")

    # Create model with the same architecture
    model = RegularizedDNN(
        input_dim=input_dim,
        hidden_dims=[512, 512, 384, 256, 128],
        dropout_rates=[0.3, 0.4, 0.4, 0.5, 0.5],
        use_batch_norm=True,
        activation='relu'
    )

    # Load weights
    model.load_state_dict(model_state)

    # Set model to evaluation mode
    model.eval()
    model.to(device)

    logger.info(f"Temperature model loaded successfully")

    # Get feature matrix - exclude metadata columns
    X = features.drop(['growth_rate', 'optimal_temperature', 'taxid', 'codon_table', 'kingdom'],
                     axis=1, errors='ignore')

    # Use the first input_dim features
    if X.shape[1] != input_dim:
        logger.warning(f"Feature mismatch: model expects {input_dim} features, but got {X.shape[1]}")
        X = X.iloc[:, :input_dim]
        logger.info(f"Using first {X.shape[1]} features for temperature prediction")

    # Scale features
    X_scaled = scaler.transform(X)

    # Convert to tensor
    X_tensor = torch.tensor(X_scaled, dtype=torch.float32).to(device)

    # Generate predictions
    with torch.no_grad():
        y_pred = model(X_tensor)
        y_pred = y_pred.cpu().numpy()

    # Create results DataFrame
    results = pd.DataFrame({
        'genome_id': features.index,
        'actual_temp': metadata['optimal_temperature'],
        'predicted_temp': y_pred
    })

    # Calculate metrics
    r2 = r2_score(results['actual_temp'], results['predicted_temp'])
    rmse = np.sqrt(mean_squared_error(results['actual_temp'], results['predicted_temp']))
    mae = mean_absolute_error(results['actual_temp'], results['predicted_temp'])

    logger.info(f"Temperature: R²={r2:.4f}, RMSE={rmse:.4f}, MAE={mae:.4f}")

    return results, {
        'R2': r2,
        'RMSE': rmse,
        'MAE': mae
    }

def main():
    parser = argparse.ArgumentParser(description="Predict Temperature with Z-Score Outlier Detection")
    parser.add_argument("--feature_file", required=True, help="Path to feature file")
    parser.add_argument("--metadata_file", required=True, help="Path to metadata file")
    parser.add_argument("--output_dir", default="predictions", help="Directory to save predictions")
    parser.add_argument("--model_dir", default="models/enhanced_dnn_temperature_v2", help="Directory containing the temperature model")
    parser.add_argument("--zscore_threshold", type=float, default=7.0, help="Z-score threshold for outlier detection")
    parser.add_argument("--apply_transform", action="store_true", help="Apply transformation for outlier detection (not recommended for temperature)")
    args = parser.parse_args()

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Load data with z-score outlier detection
    features, metadata = load_data(
        args.feature_file,
        args.metadata_file,
        zscore_threshold=args.zscore_threshold,
        apply_transform=args.apply_transform
    )

    # Generate predictions
    temp_results, temp_metrics = predict_temperature(features, metadata, args.model_dir)

    # Save predictions
    temp_results.to_csv(os.path.join(args.output_dir, 'temperature_predictions.tsv'), sep='\t', index=False)

    # Plot predictions
    plt.figure(figsize=(10, 6))
    plt.scatter(temp_results['actual_temp'], temp_results['predicted_temp'], alpha=0.5)
    plt.plot([min(temp_results['actual_temp']), max(temp_results['actual_temp'])],
             [min(temp_results['actual_temp']), max(temp_results['actual_temp'])], 'r--')
    plt.xlabel('Actual Temperature')
    plt.ylabel('Predicted Temperature')
    plt.title('Temperature Predictions')
    plt.tight_layout()
    plt.savefig(os.path.join(args.output_dir, 'temperature_predictions.png'))

    # Save metrics
    pd.DataFrame({
        'metric': list(temp_metrics.keys()),
        'value': list(temp_metrics.values())
    }).to_csv(os.path.join(args.output_dir, 'temperature_metrics.tsv'), sep='\t', index=False)

    logger.info(f"Predictions saved to {args.output_dir}")

if __name__ == "__main__":
    main()
