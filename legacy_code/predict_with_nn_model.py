#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Predict with Neural Network Model.
This script uses the neural network model for growth rate prediction.
"""

import os
import argparse
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import torch
import joblib
import logging
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Custom model class for growth rate neural network
class GrowthRateDNN(torch.nn.Module):
    """
    Custom neural network for growth rate prediction.
    """
    def __init__(self, input_dim, hidden_dims=[512, 512, 384, 256, 128], dropout_rates=[0.3, 0.4, 0.4, 0.5, 0.5],
                 use_batch_norm=True, activation='relu'):
        super(GrowthRateDNN, self).__init__()

        # Create layers
        layers = []
        prev_dim = input_dim

        # Choose activation function
        if activation == 'relu':
            act_fn = torch.nn.ReLU()
        elif activation == 'leaky_relu':
            act_fn = torch.nn.LeakyReLU(0.1)
        elif activation == 'elu':
            act_fn = torch.nn.ELU()
        else:
            act_fn = torch.nn.ReLU()

        # Build network with progressively increasing dropout
        for i, hidden_dim in enumerate(hidden_dims):
            # Linear layer
            layers.append(torch.nn.Linear(prev_dim, hidden_dim))

            # Batch normalization (before activation)
            if use_batch_norm:
                layers.append(torch.nn.BatchNorm1d(hidden_dim))

            # Activation
            layers.append(act_fn)

            # Dropout with increasing rate for deeper layers
            dropout_rate = dropout_rates[min(i, len(dropout_rates)-1)]
            layers.append(torch.nn.Dropout(dropout_rate))

            prev_dim = hidden_dim

        self.feature_extractor = torch.nn.Sequential(*layers)

        # Output layers for growth rate and temperature
        self.growth_output = torch.nn.Linear(prev_dim, 1)
        self.temp_output = torch.nn.Linear(prev_dim, 1)

    def forward(self, x):
        features = self.feature_extractor(x)
        growth_pred = self.growth_output(features)
        return growth_pred

def load_data(feature_file, metadata_file, filter_outliers=True, percentile=99.0):
    """
    Load and prepare feature and metadata files.

    Args:
        feature_file: Path to feature file
        metadata_file: Path to metadata file
        filter_outliers: Whether to filter outliers in growth rate
        percentile: Percentile threshold for filtering outliers

    Returns:
        Tuple of (features_df, metadata_df)
    """
    logger.info(f"Loading features from {feature_file}")
    features_df = pd.read_csv(feature_file, sep='\t', index_col='genome_id')

    # Check for NaN values in features
    nan_count = features_df.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in features. Filling with 0.")
        features_df = features_df.fillna(0)

    # Check for infinite values
    inf_count = np.isinf(features_df.values).sum()
    if inf_count > 0:
        logger.warning(f"Found {inf_count} infinite values in features. Replacing with max/min values.")
        features_df = features_df.replace([np.inf, -np.inf], np.nan)
        features_df = features_df.fillna(features_df.max())

    logger.info(f"Loading metadata from {metadata_file}")
    metadata_df = pd.read_csv(metadata_file, sep='\t', index_col='genome_id')

    # Check for NaN values in metadata
    nan_count = metadata_df.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in metadata. Filling with 0.")
        metadata_df = metadata_df.fillna(0)

    # Get common genomes
    common_genomes = list(set(features_df.index) & set(metadata_df.index))
    logger.info(f"Found {len(common_genomes)} common genomes")

    # Filter to common genomes
    features_df = features_df.loc[common_genomes]
    metadata_df = metadata_df.loc[common_genomes]

    # Filter outliers in growth rate if requested
    if filter_outliers:
        threshold = np.percentile(metadata_df['growth_rate'], percentile)
        logger.info(f"Filtering growth rate values above {threshold:.4f} (percentile {percentile})")

        # Count outliers
        outlier_count = (metadata_df['growth_rate'] > threshold).sum()
        logger.info(f"Found {outlier_count} outliers in growth rate")

        # Log statistics before filtering
        logger.info(f"Growth rate statistics before filtering - Min: {metadata_df['growth_rate'].min():.4f}, Max: {metadata_df['growth_rate'].max():.4f}, Mean: {metadata_df['growth_rate'].mean():.4f}, Median: {metadata_df['growth_rate'].median():.4f}")

        # Filter out outliers
        outlier_indices = metadata_df[metadata_df['growth_rate'] > threshold].index
        metadata_df = metadata_df[metadata_df['growth_rate'] <= threshold]
        features_df = features_df.drop(outlier_indices, errors='ignore')

        # Log statistics after filtering
        logger.info(f"Growth rate statistics after filtering - Min: {metadata_df['growth_rate'].min():.4f}, Max: {metadata_df['growth_rate'].max():.4f}, Mean: {metadata_df['growth_rate'].mean():.4f}, Median: {metadata_df['growth_rate'].median():.4f}")

    return features_df, metadata_df

def predict_growth_rate_with_nn(features, metadata, growth_model_dir):
    """
    Predict growth rate using the neural network model.

    Args:
        features: Feature DataFrame
        metadata: Metadata DataFrame
        growth_model_dir: Directory containing the growth rate model

    Returns:
        DataFrame with actual and predicted growth rates
    """
    logger.info(f"Predicting growth rate for {len(features)} samples using neural network")

    # Load transform info
    transform_info = joblib.load(os.path.join(growth_model_dir, 'transform_info.joblib'))
    logger.info(f"Transform info loaded: {transform_info}")

    # Apply square root transformation to growth rate
    if transform_info.get('growth_sqrt_transform', False):
        logger.info("Applying square root transformation to growth rate")
        y_growth_transformed = np.sqrt(metadata['growth_rate'])
    else:
        y_growth_transformed = metadata['growth_rate']

    # Load neural network model
    nn_dir = os.path.join(growth_model_dir, 'nn_models')

    # Load NN model architecture
    nn_hyperparams = joblib.load(os.path.join(nn_dir, 'hyperparams.joblib'))

    # Load NN feature scaler
    nn_scaler = joblib.load(os.path.join(nn_dir, 'feature_scaler.joblib'))

    # Get feature matrix - exclude metadata columns
    X = features.drop(['growth_rate', 'optimal_temperature', 'taxid', 'codon_table', 'kingdom'],
                      axis=1, errors='ignore')

    # Get input dimension from the model
    input_dim = nn_hyperparams.get('input_dim', 112)
    logger.info(f"Neural network model expects {input_dim} features")

    # Try to load feature names from file
    try:
        feature_names_path = os.path.join(growth_model_dir, 'feature_names.txt')
        if os.path.exists(feature_names_path):
            feature_names = np.loadtxt(feature_names_path, dtype=str)
            logger.info(f"Loaded {len(feature_names)} feature names from file")

            # Create a DataFrame with the same feature names as the model
            X_aligned = pd.DataFrame(0, index=X.index, columns=feature_names)

            # Fill in the values for the features that are present
            common_features = [f for f in feature_names if f in X.columns]
            logger.info(f"Found {len(common_features)} common features")

            for col in common_features:
                X_aligned[col] = X[col]

            # Use the aligned features
            X = X_aligned
        else:
            # Use the first input_dim features as a fallback
            logger.warning(f"Feature names file not found. Using first {input_dim} features as fallback.")
            X = X.iloc[:, :input_dim]
    except Exception as e:
        logger.error(f"Error aligning features: {e}")
        # Use the first input_dim features as a fallback
        X = X.iloc[:, :input_dim]
        logger.warning(f"Using first {X.shape[1]} features for growth rate prediction")

    # Scale features
    X_scaled = nn_scaler.transform(X)

    # Create model
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model = GrowthRateDNN(
        input_dim=input_dim,
        hidden_dims=nn_hyperparams.get('hidden_dims', [512, 512, 384, 256, 128]),
        dropout_rates=nn_hyperparams.get('dropout_rates', [0.3, 0.4, 0.4, 0.5, 0.5]),
        use_batch_norm=nn_hyperparams.get('use_batch_norm', True),
        activation=nn_hyperparams.get('activation', 'relu')
    )

    # Load weights
    model.load_state_dict(torch.load(
        os.path.join(nn_dir, 'enhanced_nn_model.pth'),
        map_location=device
    ))

    # Set model to evaluation mode
    model.eval()
    model.to(device)

    # Convert to tensor
    X_tensor = torch.tensor(X_scaled, dtype=torch.float32).to(device)

    # Generate predictions
    with torch.no_grad():
        y_pred = model(X_tensor)
        y_pred = y_pred.cpu().numpy().flatten()

    # Apply inverse transformation if needed
    if transform_info.get('growth_sqrt_transform', False):
        logger.info("Applying inverse square root transformation to growth rate predictions")
        y_pred = y_pred ** 2

    # Create results DataFrame
    results = pd.DataFrame({
        'genome_id': features.index,
        'actual_growth': metadata['growth_rate'],
        'predicted_growth': y_pred
    })

    # Calculate metrics
    r2 = r2_score(results['actual_growth'], results['predicted_growth'])
    rmse = np.sqrt(mean_squared_error(results['actual_growth'], results['predicted_growth']))
    mae = mean_absolute_error(results['actual_growth'], results['predicted_growth'])

    # Calculate metrics on log scale for better evaluation
    log_r2 = r2_score(np.log1p(results['actual_growth']), np.log1p(results['predicted_growth']))
    log_rmse = np.sqrt(mean_squared_error(np.log1p(results['actual_growth']), np.log1p(results['predicted_growth'])))
    log_mae = mean_absolute_error(np.log1p(results['actual_growth']), np.log1p(results['predicted_growth']))

    logger.info(f"Growth Rate: R²={r2:.4f}, RMSE={rmse:.4f}, MAE={mae:.4f}")
    logger.info(f"Growth Rate (log scale): R²={log_r2:.4f}, RMSE={log_rmse:.4f}, MAE={log_mae:.4f}")

    return results, {
        'R2': r2,
        'RMSE': rmse,
        'MAE': mae,
        'Log_R2': log_r2,
        'Log_RMSE': log_rmse,
        'Log_MAE': log_mae
    }

def predict_temperature(features, metadata, temp_model_dir):
    """
    Predict optimal temperature using the original model.

    Args:
        features: Feature DataFrame
        metadata: Metadata DataFrame
        temp_model_dir: Directory containing the temperature model

    Returns:
        DataFrame with actual and predicted temperatures
    """
    logger.info(f"Predicting optimal temperature for {len(features)} samples")

    # Load scaler
    scaler = joblib.load(os.path.join(temp_model_dir, 'temperature_scaler.joblib'))

    # Load model
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model_state = torch.load(os.path.join(temp_model_dir, 'temperature_model.pt'), map_location=device)

    # Get feature matrix - exclude metadata columns
    X = features.drop(['growth_rate', 'optimal_temperature', 'taxid', 'codon_table', 'kingdom'],
                      axis=1, errors='ignore')

    # Extract input dimension from the first layer's weight shape
    input_dim = model_state['feature_extractor.0.weight'].shape[1]
    logger.info(f"Temperature model expects {input_dim} features")

    # Use the first input_dim features
    if X.shape[1] != input_dim:
        logger.warning(f"Feature mismatch: model expects {input_dim} features, but got {X.shape[1]}")
        X = X.iloc[:, :input_dim]
        logger.info(f"Using first {X.shape[1]} features for temperature prediction")

    # Scale features
    X_scaled = scaler.transform(X)

    # Convert to tensor
    X_tensor = torch.tensor(X_scaled, dtype=torch.float32).to(device)

    # Create model with the same architecture
    from evaluate_enhanced_dnn_temperature import RegularizedDNN

    # Create model
    model = RegularizedDNN(
        input_dim=input_dim,
        hidden_dims=[512, 512, 384, 256, 128],
        dropout_rates=[0.3, 0.4, 0.4, 0.5, 0.5],
        use_batch_norm=True,
        activation='relu'
    )

    # Load weights
    model.load_state_dict(model_state)
    model.to(device)
    model.eval()

    # Generate predictions
    with torch.no_grad():
        y_pred = model(X_tensor)
        y_pred = y_pred.cpu().numpy()

    # Create results DataFrame
    results = pd.DataFrame({
        'genome_id': features.index,
        'actual_temp': metadata['optimal_temperature'],
        'predicted_temp': y_pred
    })

    # Calculate metrics
    r2 = r2_score(results['actual_temp'], results['predicted_temp'])
    rmse = np.sqrt(mean_squared_error(results['actual_temp'], results['predicted_temp']))
    mae = mean_absolute_error(results['actual_temp'], results['predicted_temp'])

    logger.info(f"Temperature: R²={r2:.4f}, RMSE={rmse:.4f}, MAE={mae:.4f}")

    return results, {
        'R2': r2,
        'RMSE': rmse,
        'MAE': mae
    }

def main():
    parser = argparse.ArgumentParser(description="Predict with neural network model")
    parser.add_argument("--feature_file", required=True, help="Path to feature file")
    parser.add_argument("--metadata_file", required=True, help="Path to metadata file")
    parser.add_argument("--output_dir", default="predictions_nn", help="Directory to save predictions")
    parser.add_argument("--temp_model_dir", default="models/enhanced_dnn_temperature_v2", help="Directory containing the temperature model")
    parser.add_argument("--growth_model_dir", default="models/hybrid_enhanced_nn_v1", help="Directory containing the growth rate model")
    parser.add_argument("--target", default="both", choices=["growth", "temperature", "both"], help="Prediction target")
    parser.add_argument("--filter_outliers", action="store_true", help="Filter outliers in growth rate")
    parser.add_argument("--percentile", type=float, default=99.0, help="Percentile threshold for filtering outliers")
    args = parser.parse_args()

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Load data
    features, metadata = load_data(
        args.feature_file,
        args.metadata_file,
        filter_outliers=args.filter_outliers,
        percentile=args.percentile
    )

    # Generate predictions
    if args.target == 'growth':
        growth_results, growth_metrics = predict_growth_rate_with_nn(features, metadata, args.growth_model_dir)

        # Save predictions
        growth_results.to_csv(os.path.join(args.output_dir, 'growth_predictions.tsv'), sep='\t', index=False)

        # Plot predictions
        plt.figure(figsize=(12, 5))

        # Plot with original scale
        plt.subplot(1, 2, 1)
        plt.scatter(growth_results['actual_growth'], growth_results['predicted_growth'], alpha=0.5)
        plt.plot([min(growth_results['actual_growth']), max(growth_results['actual_growth'])],
                 [min(growth_results['actual_growth']), max(growth_results['actual_growth'])], 'r--')
        plt.xlabel('Actual Growth Rate')
        plt.ylabel('Predicted Growth Rate')
        plt.title('Growth Rate Predictions (Original Scale)')

        # Plot with log scale for better visualization
        plt.subplot(1, 2, 2)
        plt.scatter(np.log1p(growth_results['actual_growth']), np.log1p(growth_results['predicted_growth']), alpha=0.5)
        plt.plot([min(np.log1p(growth_results['actual_growth'])), max(np.log1p(growth_results['actual_growth']))],
                 [min(np.log1p(growth_results['actual_growth'])), max(np.log1p(growth_results['actual_growth']))], 'r--')
        plt.xlabel('Actual Growth Rate (log scale)')
        plt.ylabel('Predicted Growth Rate (log scale)')
        plt.title('Growth Rate Predictions (Log Scale)')

        plt.tight_layout()
        plt.savefig(os.path.join(args.output_dir, 'growth_predictions.png'))

        # Save metrics
        pd.DataFrame({
            'metric': list(growth_metrics.keys()),
            'value': list(growth_metrics.values())
        }).to_csv(os.path.join(args.output_dir, 'growth_metrics.tsv'), sep='\t', index=False)

    elif args.target == 'temperature':
        temp_results, temp_metrics = predict_temperature(features, metadata, args.temp_model_dir)

        # Save predictions
        temp_results.to_csv(os.path.join(args.output_dir, 'temperature_predictions.tsv'), sep='\t', index=False)

        # Plot predictions
        plt.figure(figsize=(10, 6))
        plt.scatter(temp_results['actual_temp'], temp_results['predicted_temp'], alpha=0.5)
        plt.plot([min(temp_results['actual_temp']), max(temp_results['actual_temp'])],
                 [min(temp_results['actual_temp']), max(temp_results['actual_temp'])], 'r--')
        plt.xlabel('Actual Temperature')
        plt.ylabel('Predicted Temperature')
        plt.title('Temperature Predictions')
        plt.tight_layout()
        plt.savefig(os.path.join(args.output_dir, 'temperature_predictions.png'))

        # Save metrics
        pd.DataFrame({
            'metric': list(temp_metrics.keys()),
            'value': list(temp_metrics.values())
        }).to_csv(os.path.join(args.output_dir, 'temperature_metrics.tsv'), sep='\t', index=False)

    else:  # both
        growth_results, growth_metrics = predict_growth_rate_with_nn(features, metadata, args.growth_model_dir)
        temp_results, temp_metrics = predict_temperature(features, metadata, args.temp_model_dir)

        # Save predictions
        pd.DataFrame({
            'genome_id': features.index,
            'actual_growth': growth_results['actual_growth'],
            'predicted_growth': growth_results['predicted_growth'],
            'actual_temp': temp_results['actual_temp'],
            'predicted_temp': temp_results['predicted_temp']
        }).to_csv(os.path.join(args.output_dir, 'predictions.tsv'), sep='\t', index=False)

        # Plot predictions
        plt.figure(figsize=(12, 10))

        # Growth rate - original scale
        plt.subplot(2, 2, 1)
        plt.scatter(growth_results['actual_growth'], growth_results['predicted_growth'], alpha=0.5)
        plt.plot([min(growth_results['actual_growth']), max(growth_results['actual_growth'])],
                 [min(growth_results['actual_growth']), max(growth_results['actual_growth'])], 'r--')
        plt.xlabel('Actual Growth Rate')
        plt.ylabel('Predicted Growth Rate')
        plt.title('Growth Rate Predictions (Original Scale)')

        # Growth rate - log scale
        plt.subplot(2, 2, 2)
        plt.scatter(np.log1p(growth_results['actual_growth']), np.log1p(growth_results['predicted_growth']), alpha=0.5)
        plt.plot([min(np.log1p(growth_results['actual_growth'])), max(np.log1p(growth_results['actual_growth']))],
                 [min(np.log1p(growth_results['actual_growth'])), max(np.log1p(growth_results['actual_growth']))], 'r--')
        plt.xlabel('Actual Growth Rate (log scale)')
        plt.ylabel('Predicted Growth Rate (log scale)')
        plt.title('Growth Rate Predictions (Log Scale)')

        # Temperature
        plt.subplot(2, 2, 3)
        plt.scatter(temp_results['actual_temp'], temp_results['predicted_temp'], alpha=0.5)
        plt.plot([min(temp_results['actual_temp']), max(temp_results['actual_temp'])],
                 [min(temp_results['actual_temp']), max(temp_results['actual_temp'])], 'r--')
        plt.xlabel('Actual Temperature')
        plt.ylabel('Predicted Temperature')
        plt.title('Temperature Predictions')

        plt.tight_layout()
        plt.savefig(os.path.join(args.output_dir, 'predictions.png'))

        # Calculate overall metrics
        overall_metrics = {
            'R2': (growth_metrics['R2'] + temp_metrics['R2']) / 2,
            'RMSE': (growth_metrics['RMSE'] + temp_metrics['RMSE']) / 2,
            'MAE': (growth_metrics['MAE'] + temp_metrics['MAE']) / 2
        }

        logger.info(f"Overall: R²={overall_metrics['R2']:.4f}, RMSE={overall_metrics['RMSE']:.4f}, MAE={overall_metrics['MAE']:.4f}")

        # Save metrics
        pd.DataFrame({
            'metric': ['R2', 'RMSE', 'MAE', 'Log_R2', 'Log_RMSE', 'Log_MAE'],
            'growth_rate': [
                growth_metrics['R2'],
                growth_metrics['RMSE'],
                growth_metrics['MAE'],
                growth_metrics['Log_R2'],
                growth_metrics['Log_RMSE'],
                growth_metrics['Log_MAE']
            ],
            'temperature': [
                temp_metrics['R2'],
                temp_metrics['RMSE'],
                temp_metrics['MAE'],
                None, None, None
            ],
            'overall': [
                overall_metrics['R2'],
                overall_metrics['RMSE'],
                overall_metrics['MAE'],
                None, None, None
            ]
        }).to_csv(os.path.join(args.output_dir, 'metrics.tsv'), sep='\t', index=False)

    logger.info(f"Predictions saved to {args.output_dir}")

if __name__ == "__main__":
    main()
