#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Predict Temperature Only.
This script focuses on just the temperature prediction, which is working well.
"""

import os
import argparse
import logging
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import torch
import joblib
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error

# Import necessary model components
from train_enhanced_dnn_temperature import RegularizedDNN

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TemperaturePredictor:
    """
    Predictor that focuses on just the temperature prediction.
    """
    def __init__(
        self,
        model_dir: str = 'models/enhanced_dnn_temperature_v2'
    ):
        """
        Initialize temperature predictor.
        
        Args:
            model_dir: Directory containing the temperature model
        """
        self.model_dir = model_dir
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # Load model
        self._load_model()
        
        logger.info("Temperature predictor initialized successfully")
    
    def _load_model(self):
        """Load temperature model."""
        logger.info(f"Loading temperature model from {self.model_dir}")
        
        # Load feature scaler
        self.feature_scaler = joblib.load(os.path.join(self.model_dir, 'temperature_scaler.joblib'))
        logger.info(f"Feature scaler loaded successfully")
        
        # Create a copy of the scaler without feature names to avoid warnings
        if hasattr(self.feature_scaler, 'feature_names_in_'):
            self.feature_scaler_no_names = joblib.load(os.path.join(self.model_dir, 'temperature_scaler.joblib'))
            if hasattr(self.feature_scaler_no_names, 'feature_names_in_'):
                delattr(self.feature_scaler_no_names, 'feature_names_in_')
        else:
            self.feature_scaler_no_names = self.feature_scaler
        
        # Load model architecture
        # Get input dimension from scaler
        input_dim = len(self.feature_scaler.mean_)
        
        # Create model with the same architecture
        self.model = RegularizedDNN(
            input_dim=input_dim, 
            hidden_dims=[512, 512, 384, 256, 128], 
            dropout_rates=[0.3, 0.4, 0.4, 0.5, 0.5],
            use_batch_norm=True,
            activation='relu'
        )
        
        # Load weights
        self.model.load_state_dict(torch.load(
            os.path.join(self.model_dir, 'temperature_model.pt'),
            map_location=self.device
        ))
        
        # Set model to evaluation mode
        self.model.eval()
        self.model.to(self.device)
        
        logger.info(f"Temperature model loaded successfully")
    
    def predict(self, X: pd.DataFrame) -> np.ndarray:
        """
        Generate temperature predictions.
        
        Args:
            X: Features DataFrame
            
        Returns:
            Temperature predictions
        """
        # Handle feature mismatch
        if hasattr(self.feature_scaler, 'feature_names_in_'):
            # Filter features to match training data
            feature_names = self.feature_scaler.feature_names_in_
            common_cols = [col for col in X.columns if col in feature_names]
            
            # Check if we have all required features
            if len(common_cols) < len(feature_names):
                logger.warning(f"Missing {len(feature_names) - len(common_cols)} features required by the model")
                # Use only available features
                X_filtered = X[common_cols]
                # Fill missing features with zeros
                missing_cols = [col for col in feature_names if col not in common_cols]
                for col in missing_cols:
                    X_filtered[col] = 0
                # Reorder columns to match the order expected by the model
                X_filtered = X_filtered[feature_names]
            else:
                # Use only the features the model was trained on, in the correct order
                X_filtered = X[feature_names]
        else:
            # If no feature names, check dimensions
            expected_features = self.feature_scaler.mean_.shape[0]
            if X.shape[1] != expected_features:
                logger.warning(f"Feature count mismatch: got {X.shape[1]}, expected {expected_features}")
                # If too many features, truncate
                if X.shape[1] > expected_features:
                    X_filtered = X.iloc[:, :expected_features]
                else:
                    # If too few features, pad with zeros
                    X_filtered = pd.DataFrame(np.zeros((X.shape[0], expected_features)), index=X.index)
                    X_filtered.iloc[:, :X.shape[1]] = X.values
            else:
                X_filtered = X
        
        # Scale features using the scaler without feature names to avoid warnings
        X_filtered_np = X_filtered.values
        X_scaled = self.feature_scaler_no_names.transform(X_filtered_np)
        
        # Convert to tensor
        X_tensor = torch.tensor(X_scaled, dtype=torch.float32).to(self.device)
        
        # Generate predictions
        with torch.no_grad():
            temp_pred = self.model(X_tensor)
            temp_pred = temp_pred.cpu().numpy().flatten()
        
        logger.info(f"Temperature predictions - Min: {temp_pred.min():.4f}, Max: {temp_pred.max():.4f}, Mean: {temp_pred.mean():.4f}")
        
        return temp_pred
    
    def evaluate(self, X: pd.DataFrame, y: pd.Series) -> dict:
        """
        Evaluate model on data.
        
        Args:
            X: Features DataFrame
            y: Temperature targets
            
        Returns:
            Dictionary of evaluation metrics
        """
        # Generate predictions
        y_pred = self.predict(X)
        
        # Calculate metrics
        metrics = {
            'R2': r2_score(y, y_pred),
            'RMSE': np.sqrt(mean_squared_error(y, y_pred)),
            'MAE': mean_absolute_error(y, y_pred)
        }
        
        logger.info(f"Evaluation metrics - R²={metrics['R2']:.4f}, RMSE={metrics['RMSE']:.4f}, MAE={metrics['MAE']:.4f}")
        
        return metrics

def load_data(feature_file: str, metadata_file: str) -> tuple:
    """
    Load feature and metadata files.
    
    Args:
        feature_file: Path to feature file
        metadata_file: Path to metadata file
        
    Returns:
        Tuple of (features, metadata)
    """
    logger.info(f"Loading features from {feature_file}")
    features = pd.read_csv(feature_file, sep='\t', index_col='genome_id')
    
    logger.info(f"Loading metadata from {metadata_file}")
    metadata = pd.read_csv(metadata_file, sep='\t', index_col='genome_id')
    
    # Get common samples
    common_indices = features.index.intersection(metadata.index)
    logger.info(f"Found {len(common_indices)} common samples")
    
    # Filter to common samples
    features = features.loc[common_indices]
    metadata = metadata.loc[common_indices]
    
    # Check for NaN values in features
    nan_count = features.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in features. Filling with 0.")
        features = features.fillna(0)
    
    # Check for infinite values
    inf_count = np.isinf(features.values).sum()
    if inf_count > 0:
        logger.warning(f"Found {inf_count} infinite values in features. Replacing with max/min values.")
        features = features.replace([np.inf, -np.inf], np.nan)
        features = features.fillna(features.max())
    
    # Check for NaN values in metadata
    nan_count = metadata.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in metadata. Filling with 0.")
        metadata = metadata.fillna(0)
    
    return features, metadata

def main():
    parser = argparse.ArgumentParser(description="Predict Temperature Only")
    parser.add_argument("--feature_file", required=True, help="Path to feature file")
    parser.add_argument("--metadata_file", required=True, help="Path to metadata file")
    parser.add_argument("--output_dir", default="predictions", help="Directory to save predictions")
    parser.add_argument("--model_dir", default="models/enhanced_dnn_temperature_v2", help="Directory containing the temperature model")
    args = parser.parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Load data
    features, metadata = load_data(args.feature_file, args.metadata_file)
    
    # Extract temperature targets
    y_temp = metadata['optimal_temperature']
    
    # Initialize predictor
    predictor = TemperaturePredictor(model_dir=args.model_dir)
    
    # Generate predictions
    y_temp_pred = predictor.predict(features)
    
    # Save predictions
    pd.DataFrame({
        'genome_id': features.index,
        'actual_temp': y_temp,
        'predicted_temp': y_temp_pred
    }).to_csv(os.path.join(args.output_dir, 'temperature_predictions.tsv'), sep='\t', index=False)
    
    # Plot predictions
    plt.figure(figsize=(10, 6))
    plt.scatter(y_temp, y_temp_pred, alpha=0.5)
    plt.plot([min(y_temp), max(y_temp)], [min(y_temp), max(y_temp)], 'r--')
    plt.xlabel('Actual Temperature')
    plt.ylabel('Predicted Temperature')
    plt.title('Temperature Predictions')
    plt.tight_layout()
    plt.savefig(os.path.join(args.output_dir, 'temperature_predictions.png'))
    
    # Evaluate model
    metrics = predictor.evaluate(features, y_temp)
    
    # Save metrics
    pd.DataFrame({
        'metric': list(metrics.keys()),
        'value': list(metrics.values())
    }).to_csv(os.path.join(args.output_dir, 'temperature_metrics.tsv'), sep='\t', index=False)
    
    logger.info(f"Predictions saved to {args.output_dir}")

if __name__ == "__main__":
    main()
