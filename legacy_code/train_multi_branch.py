#!/usr/bin/env python
"""
Train a multi-branch deep neural network model for microbial growth rate and
optimal growth temperature prediction.

This script trains a multi-branch DNN model that processes different feature types
through separate branches before fusion with attention mechanisms.
"""

import os
import sys
import argparse
import logging
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional

from deepmu.models.multi_branch_network import MultiBranchDNN, MultiBranchTrainer
from deepmu.utils.logging import get_logger

# Set up logging
logger = get_logger()


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Train a multi-branch DNN model')

    # Input data
    parser.add_argument('--feature-file', type=str, required=True,
                        help='Path to combined feature file (TSV)')
    parser.add_argument('--metadata', type=str, required=True,
                        help='Path to metadata file (TSV)')

    # Output
    parser.add_argument('--output-dir', type=str, required=True,
                        help='Directory to save model and results')
    parser.add_argument('--metrics-dir', type=str, default=None,
                        help='Directory to save metrics and plots')

    # Training parameters
    parser.add_argument('--batch-size', type=int, default=32,
                        help='Batch size for training')
    parser.add_argument('--epochs', type=int, default=100,
                        help='Number of epochs to train')
    parser.add_argument('--learning-rate', type=float, default=0.001,
                        help='Learning rate for optimizer')
    parser.add_argument('--weight-decay', type=float, default=1e-5,
                        help='Weight decay for regularization')
    parser.add_argument('--patience', type=int, default=10,
                        help='Patience for early stopping')
    parser.add_argument('--test-size', type=float, default=0.2,
                        help='Test set size')
    parser.add_argument('--random-state', type=int, default=42,
                        help='Random state for reproducibility')

    # Model parameters
    parser.add_argument('--hidden-dims', type=int, nargs='+', default=[256, 128, 64],
                        help='Hidden layer dimensions')
    parser.add_argument('--dropout-rates', type=float, nargs='+', default=[0.2, 0.3, 0.4],
                        help='Dropout rates for different parts of the network')
    parser.add_argument('--attention-heads', type=int, default=4,
                        help='Number of attention heads for feature fusion')
    parser.add_argument('--fusion-layers', type=int, default=2,
                        help='Number of layers in the fusion network')
    parser.add_argument('--no-batch-norm', action='store_true',
                        help='Disable batch normalization')
    parser.add_argument('--no-residual', action='store_true',
                        help='Disable residual connections')
    parser.add_argument('--single-task', action='store_true',
                        help='Train for a single task (growth rate only)')
    parser.add_argument('--target', type=str, default='both',
                        choices=['growth_rate', 'temperature', 'both'],
                        help='Target variable to predict')

    # Multi-task weights
    parser.add_argument('--growth-weight', type=float, default=0.5,
                        help='Weight for growth rate loss in multi-task learning')
    parser.add_argument('--temperature-weight', type=float, default=0.5,
                        help='Weight for temperature loss in multi-task learning')

    # Advanced regularization parameters
    parser.add_argument('--use-swa', action='store_true',
                        help='Use Stochastic Weight Averaging')
    parser.add_argument('--swa-start', type=int, default=10,
                        help='Epoch to start SWA from')
    parser.add_argument('--swa-freq', type=int, default=5,
                        help='Frequency of model averaging in SWA')
    parser.add_argument('--use-mixup', action='store_true',
                        help='Use Mixup data augmentation')
    parser.add_argument('--mixup-alpha', type=float, default=0.2,
                        help='Alpha parameter for Mixup')
    parser.add_argument('--use-label-smoothing', action='store_true',
                        help='Use label smoothing')
    parser.add_argument('--label-smoothing', type=float, default=0.1,
                        help='Label smoothing parameter')
    parser.add_argument('--use-spectral-norm', action='store_true',
                        help='Use spectral normalization')
    parser.add_argument('--use-sam', action='store_true',
                        help='Use Sharpness-Aware Minimization')
    parser.add_argument('--sam-rho', type=float, default=0.05,
                        help='Neighborhood size for SAM')
    parser.add_argument('--use-gradient-clipping', action='store_true',
                        help='Use gradient clipping')
    parser.add_argument('--max-grad-norm', type=float, default=1.0,
                        help='Maximum gradient norm for clipping')
    parser.add_argument('--lr-scheduler', type=str, default='plateau',
                        choices=['plateau', 'cyclic', 'one_cycle'],
                        help='Learning rate scheduler type')
    parser.add_argument('--cyclic-lr-max', type=float, default=None,
                        help='Maximum learning rate for cyclic scheduler')
    parser.add_argument('--cyclic-lr-mode', type=str, default='triangular',
                        choices=['triangular', 'triangular2', 'exp_range'],
                        help='Mode for cyclic scheduler')
    parser.add_argument('--one-cycle-max-lr', type=float, default=None,
                        help='Maximum learning rate for one-cycle scheduler')

    return parser.parse_args()


def load_data(feature_file: str, metadata_file: str) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """Load feature and metadata files.

    Args:
        feature_file: Path to feature file (TSV)
        metadata_file: Path to metadata file (TSV)

    Returns:
        Tuple of (features_df, metadata_df)
    """
    logger.info(f"Loading features from {feature_file}")
    features_df = pd.read_csv(feature_file, sep='\t', index_col='genome_id')
    
    # Check for NaN values in features
    nan_count = features_df.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in features. Filling with 0.")
        features_df = features_df.fillna(0)
    
    # Check for infinite values
    inf_count = np.isinf(features_df.values).sum()
    if inf_count > 0:
        logger.warning(f"Found {inf_count} infinite values in features. Replacing with max/min values.")
        features_df = features_df.replace([np.inf, -np.inf], np.nan)
        features_df = features_df.fillna(features_df.max())

    logger.info(f"Loading metadata from {metadata_file}")
    metadata_df = pd.read_csv(metadata_file, sep='\t', index_col='genome_id')
    
    # Check for NaN values in metadata
    nan_count = metadata_df.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in metadata. Filling with 0.")
        metadata_df = metadata_df.fillna(0)

    # Get common genomes
    common_genomes = list(set(features_df.index) & set(metadata_df.index))
    logger.info(f"Found {len(common_genomes)} common genomes")

    # Filter to common genomes
    features_df = features_df.loc[common_genomes]
    metadata_df = metadata_df.loc[common_genomes]
    
    # Log data statistics
    logger.info(f"Feature statistics:")
    logger.info(f"  Shape: {features_df.shape}")
    logger.info(f"  Min: {features_df.min().min():.4f}")
    logger.info(f"  Max: {features_df.max().max():.4f}")
    logger.info(f"  Mean: {features_df.mean().mean():.4f}")
    logger.info(f"  Std: {features_df.std().mean():.4f}")
    
    logger.info(f"Metadata statistics:")
    logger.info(f"  Shape: {metadata_df.shape}")
    logger.info(f"  Growth rate range: {metadata_df['growth_rate'].min():.4f} to {metadata_df['growth_rate'].max():.4f}")
    logger.info(f"  Temperature range: {metadata_df['optimal_temperature'].min():.4f} to {metadata_df['optimal_temperature'].max():.4f}")

    return features_df, metadata_df


def categorize_features(features_df: pd.DataFrame) -> Dict[str, List[str]]:
    """Categorize features into different types.

    Args:
        features_df: DataFrame with features

    Returns:
        Dictionary mapping feature types to lists of feature names
    """
    feature_categories = {
        'codon': [],
        'aa': [],
        'genomic': [],
        'rna': [],
        'pi': [],
        'taxonomy': []
    }

    # Print a few column names to help with debugging
    logger.info(f"Sample feature names: {list(features_df.columns[:10])}")

    # Categorize features based on prefixes and keywords
    for feature in features_df.columns:
        # Convert to lowercase for case-insensitive matching
        feature_lower = feature.lower()

        # Codon features
        if any(keyword in feature_lower for keyword in ['cub', 'cpb', 'cai', 'enc', 'rscu', 'codon', 'heg', 'bg', 'delta']):
            feature_categories['codon'].append(feature)

        # Amino acid features
        elif any(keyword in feature_lower for keyword in ['aa_', 'amino', 'protein', 'arsc', 'nitrogen']):
            feature_categories['aa'].append(feature)

        # Genomic features
        elif any(keyword in feature_lower for keyword in ['genome', 'gc_', 'skew', 'dinuc', 'size', 'length']):
            feature_categories['genomic'].append(feature)

        # RNA features
        elif any(keyword in feature_lower for keyword in ['trna', 'rrna', 'tai', 'rna']):
            feature_categories['rna'].append(feature)

        # Protein pI features
        elif any(keyword in feature_lower for keyword in ['pi_', 'isoelectric', 'ph']):
            feature_categories['pi'].append(feature)

        # Taxonomy features
        elif any(keyword in feature_lower for keyword in ['phylum', 'class', 'order', 'family', 'genus', 'taxid', 'taxonomy']):
            feature_categories['taxonomy'].append(feature)

        # Default to codon features if not categorized
        else:
            # Check if it's likely a numeric feature (not an ID or metadata)
            if feature != 'genome_id' and not feature.endswith('_id'):
                feature_categories['codon'].append(feature)

    # Log feature counts
    for category, features in feature_categories.items():
        logger.info(f"Found {len(features)} {category} features")

    return feature_categories


def prepare_data(
    features_df: pd.DataFrame,
    metadata_df: pd.DataFrame,
    feature_categories: Dict[str, List[str]],
    target: str,
    test_size: float = 0.2,
    random_state: int = 42
) -> Dict[str, Dict[str, np.ndarray]]:
    """Prepare data for training.

    Args:
        features_df: Feature DataFrame
        metadata_df: Metadata DataFrame
        feature_categories: Dictionary mapping feature types to lists of feature names
        target: Target variable ('growth_rate', 'temperature', or 'both')
        test_size: Test set size
        random_state: Random state for reproducibility

    Returns:
        Dictionary with train and test data for each feature type and target
    """
    # Get target variables
    if target in ['growth_rate', 'both']:
        y_growth = metadata_df['growth_rate'].values
        logger.info(f"Growth rate statistics - Min: {y_growth.min():.4f}, Max: {y_growth.max():.4f}, Mean: {y_growth.mean():.4f}")

    if target in ['temperature', 'both']:
        y_temp = metadata_df['optimal_temperature'].values
        logger.info(f"Temperature statistics - Min: {y_temp.min():.4f}, Max: {y_temp.max():.4f}, Mean: {y_temp.mean():.4f}")

    # Split data
    if target == 'both':
        # Use the same split for both targets
        indices = np.arange(len(features_df))
        train_indices, test_indices = train_test_split(
            indices, test_size=test_size, random_state=random_state
        )
    elif target == 'growth_rate':
        X = features_df.values
        _, _, train_indices, test_indices = train_test_split(
            X, np.arange(len(X)), test_size=test_size, random_state=random_state
        )
    else:  # temperature
        X = features_df.values
        _, _, train_indices, test_indices = train_test_split(
            X, np.arange(len(X)), test_size=test_size, random_state=random_state
        )

    # Prepare data dictionary
    data = {
        'train': {
            'features': {},
            'targets': {}
        },
        'test': {
            'features': {},
            'targets': {}
        }
    }

    # Process each feature category
    for category, feature_names in feature_categories.items():
        if not feature_names:
            continue

        # Extract features
        X_category = features_df[feature_names].values
        
        # Check for NaN values
        if np.isnan(X_category).any():
            logger.warning(f"Found NaN values in {category} features. Filling with 0.")
            X_category = np.nan_to_num(X_category, 0)
        
        # Check for infinite values
        if np.isinf(X_category).any():
            logger.warning(f"Found infinite values in {category} features. Replacing with max/min values.")
            X_category = np.nan_to_num(X_category, np.nan)
            X_category = np.nan_to_num(X_category, np.nanmax(X_category))

        # Split into train and test
        X_train = X_category[train_indices]
        X_test = X_category[test_indices]

        # Scale features
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        # Log scaling statistics
        logger.info(f"{category} features statistics after scaling:")
        logger.info(f"  Train - Min: {X_train_scaled.min():.4f}, Max: {X_train_scaled.max():.4f}, Mean: {X_train_scaled.mean():.4f}")
        logger.info(f"  Test - Min: {X_test_scaled.min():.4f}, Max: {X_test_scaled.max():.4f}, Mean: {X_test_scaled.mean():.4f}")

        # Store in data dictionary
        data['train']['features'][category] = X_train_scaled
        data['test']['features'][category] = X_test_scaled

    # Add targets
    if target in ['growth_rate', 'both']:
        data['train']['targets']['growth_rate'] = y_growth[train_indices]
        data['test']['targets']['growth_rate'] = y_growth[test_indices]
        logger.info(f"Growth rate target statistics:")
        logger.info(f"  Train - Min: {data['train']['targets']['growth_rate'].min():.4f}, Max: {data['train']['targets']['growth_rate'].max():.4f}")
        logger.info(f"  Test - Min: {data['test']['targets']['growth_rate'].min():.4f}, Max: {data['test']['targets']['growth_rate'].max():.4f}")

    if target in ['temperature', 'both']:
        data['train']['targets']['temperature'] = y_temp[train_indices]
        data['test']['targets']['temperature'] = y_temp[test_indices]
        logger.info(f"Temperature target statistics:")
        logger.info(f"  Train - Min: {data['train']['targets']['temperature'].min():.4f}, Max: {data['train']['targets']['temperature'].max():.4f}")
        logger.info(f"  Test - Min: {data['test']['targets']['temperature'].min():.4f}, Max: {data['test']['targets']['temperature'].max():.4f}")

    return data


def create_dataloaders(
    data: Dict[str, Dict[str, Dict[str, np.ndarray]]],
    batch_size: int
) -> Dict[str, DataLoader]:
    """Create PyTorch DataLoaders.

    Args:
        data: Dictionary with train and test data
        batch_size: Batch size for training

    Returns:
        Dictionary with train and test DataLoaders
    """
    dataloaders = {}

    for split in ['train', 'test']:
        # Convert features to tensors
        features_tensors = {
            category: torch.tensor(features, dtype=torch.float32)
            for category, features in data[split]['features'].items()
        }

        # Convert targets to tensors
        targets_tensors = {
            target: torch.tensor(values, dtype=torch.float32)
            for target, values in data[split]['targets'].items()
        }

        # Create dataset
        dataset = TensorDataset(
            *features_tensors.values(),
            *targets_tensors.values()
        )

        # Create dataloader
        dataloaders[split] = DataLoader(
            dataset,
            batch_size=batch_size,
            shuffle=(split == 'train')
        )

        # Store feature and target names for later use
        dataloaders[f'{split}_feature_names'] = list(features_tensors.keys())
        dataloaders[f'{split}_target_names'] = list(targets_tensors.keys())

    return dataloaders


def collate_batch(batch, feature_names, target_names):
    """Custom collate function for DataLoader.

    Args:
        batch: Batch of data
        feature_names: List of feature names
        target_names: List of target names

    Returns:
        Dictionary with features and targets
    """
    # Unpack batch
    tensors = list(zip(*batch))

    # Create features dictionary
    features = {
        name: tensors[i]
        for i, name in enumerate(feature_names)
    }

    # Create targets dictionary
    targets = {
        name: tensors[i + len(feature_names)]
        for i, name in enumerate(target_names)
    }

    return {
        'features': features,
        'targets': targets
    }


def create_model(
    feature_dims: Dict[str, int],
    hidden_dims: List[int],
    output_dim: int,
    dropout_rates: List[float],
    use_batch_norm: bool,
    use_residual: bool,
    attention_heads: int,
    fusion_layers: int,
    multi_task: bool
) -> MultiBranchDNN:
    """Create a multi-branch DNN model.

    Args:
        feature_dims: Dictionary mapping feature types to their dimensions
        hidden_dims: List of hidden layer dimensions
        output_dim: Output dimension
        dropout_rates: List of dropout rates
        use_batch_norm: Whether to use batch normalization
        use_residual: Whether to use residual connections
        attention_heads: Number of attention heads
        fusion_layers: Number of fusion layers
        multi_task: Whether to use multi-task learning

    Returns:
        MultiBranchDNN model
    """
    # Validate input dimensions
    for category, dim in feature_dims.items():
        if dim <= 0:
            raise ValueError(f"Invalid feature dimension for {category}: {dim}")
    
    # Validate hidden dimensions
    for i, dim in enumerate(hidden_dims):
        if dim <= 0:
            raise ValueError(f"Invalid hidden dimension at index {i}: {dim}")
    
    # Validate dropout rates
    for i, rate in enumerate(dropout_rates):
        if not 0 <= rate < 1:
            raise ValueError(f"Invalid dropout rate at index {i}: {rate}")
    
    # Create model with gradient clipping enabled by default
    model = MultiBranchDNN(
        feature_dims=feature_dims,
        hidden_dims=hidden_dims,
        output_dim=output_dim,
        dropout_rates=dropout_rates,
        use_batch_norm=use_batch_norm,
        use_residual=use_residual,
        attention_heads=attention_heads,
        fusion_layers=fusion_layers,
        multi_task=multi_task
    )
    
    # Initialize weights with Xavier/Glorot initialization
    for m in model.modules():
        if isinstance(m, nn.Linear):
            nn.init.xavier_uniform_(m.weight)
            if m.bias is not None:
                nn.init.zeros_(m.bias)
    
    return model


def plot_training_history(history: Dict[str, List[float]], output_dir: str):
    """Plot training history.

    Args:
        history: Dictionary with training history
        output_dir: Directory to save plots
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Plot loss
    plt.figure(figsize=(10, 6))
    plt.plot(history['loss'], label='Train Loss')
    plt.plot(history['val_loss'], label='Validation Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('Training and Validation Loss')
    plt.legend()
    plt.grid(True)
    plt.savefig(os.path.join(output_dir, 'loss.png'))
    plt.close()

    # Plot R-squared if available
    if 'val_growth_rate_r2' in history:
        plt.figure(figsize=(10, 6))
        plt.plot(history['val_growth_rate_r2'], label='Growth Rate R²')
        plt.plot(history['val_temperature_r2'], label='Temperature R²')
        plt.xlabel('Epoch')
        plt.ylabel('R²')
        plt.title('Validation R-squared')
        plt.legend()
        plt.grid(True)
        plt.savefig(os.path.join(output_dir, 'r2.png'))
        plt.close()
    elif 'val_r2' in history:
        plt.figure(figsize=(10, 6))
        plt.plot(history['val_r2'], label='R²')
        plt.xlabel('Epoch')
        plt.ylabel('R²')
        plt.title('Validation R-squared')
        plt.legend()
        plt.grid(True)
        plt.savefig(os.path.join(output_dir, 'r2.png'))
        plt.close()

    # Save history as CSV
    history_df = pd.DataFrame(history)
    history_df.to_csv(os.path.join(output_dir, 'training_history.csv'), index=False)


def save_metrics_to_tsv(metrics: Dict[str, float], output_file: str):
    """Save metrics to a TSV file.

    Args:
        metrics: Dictionary with metrics
        output_file: Path to output file
    """
    metrics_df = pd.DataFrame({
        'metric': list(metrics.keys()),
        'value': list(metrics.values())
    })

    metrics_df.to_csv(output_file, sep='\t', index=False)


def main():
    """Main function."""
    # Parse arguments
    args = parse_args()

    # Create output directories
    os.makedirs(args.output_dir, exist_ok=True)

    if args.metrics_dir:
        os.makedirs(args.metrics_dir, exist_ok=True)

    # Load data
    features_df, metadata_df = load_data(args.feature_file, args.metadata)

    # Categorize features
    feature_categories = categorize_features(features_df)

    # Prepare data
    data = prepare_data(
        features_df,
        metadata_df,
        feature_categories,
        args.target,
        args.test_size,
        args.random_state
    )

    # Get feature dimensions
    feature_dims = {
        category: features.shape[1]
        for category, features in data['train']['features'].items()
    }

    # Create model
    model = create_model(
        feature_dims=feature_dims,
        hidden_dims=args.hidden_dims,
        output_dim=1 if args.single_task else 2,
        dropout_rates=args.dropout_rates,
        use_batch_norm=not args.no_batch_norm,
        use_residual=not args.no_residual,
        attention_heads=args.attention_heads,
        fusion_layers=args.fusion_layers,
        multi_task=(args.target == 'both' and not args.single_task)
    )

    # Log model architecture
    logger.info(f"Model architecture:\n{model}")

    # Create custom dataloaders
    train_features = {k: torch.tensor(v, dtype=torch.float32) for k, v in data['train']['features'].items()}
    train_targets = {k: torch.tensor(v, dtype=torch.float32) for k, v in data['train']['targets'].items()}

    test_features = {k: torch.tensor(v, dtype=torch.float32) for k, v in data['test']['features'].items()}
    test_targets = {k: torch.tensor(v, dtype=torch.float32) for k, v in data['test']['targets'].items()}

    # Create datasets
    class CustomDataset(torch.utils.data.Dataset):
        def __init__(self, features, targets):
            self.features = features
            self.targets = targets
            self.length = len(next(iter(targets.values())))

        def __len__(self):
            return self.length

        def __getitem__(self, idx):
            return {
                'features': {k: v[idx] for k, v in self.features.items()},
                'targets': {k: v[idx] for k, v in self.targets.items()}
            }

    train_dataset = CustomDataset(train_features, train_targets)
    test_dataset = CustomDataset(test_features, test_targets)

    # Create dataloaders
    train_loader = DataLoader(train_dataset, batch_size=args.batch_size, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=args.batch_size, shuffle=False)

    # Calculate total steps for one-cycle scheduler if needed
    total_steps = None
    if args.lr_scheduler == 'one_cycle':
        total_steps = args.epochs * (len(train_dataset) // args.batch_size + 1)

    # Create trainer with advanced regularization
    trainer = MultiBranchTrainer(
        model=model,
        learning_rate=args.learning_rate,
        weight_decay=args.weight_decay,
        # Advanced regularization options
        use_swa=args.use_swa,
        swa_start=args.swa_start,
        swa_freq=args.swa_freq,
        use_mixup=args.use_mixup,
        mixup_alpha=args.mixup_alpha,
        use_label_smoothing=args.use_label_smoothing,
        label_smoothing=args.label_smoothing,
        use_spectral_norm=args.use_spectral_norm,
        use_sam=args.use_sam,
        sam_rho=args.sam_rho,
        use_gradient_clipping=True,  # Enable gradient clipping by default
        max_grad_norm=1.0,  # Default gradient clipping norm
        lr_scheduler=args.lr_scheduler,
        cyclic_lr_max=args.cyclic_lr_max,
        cyclic_lr_mode=args.cyclic_lr_mode,
        one_cycle_max_lr=args.one_cycle_max_lr,
        one_cycle_total_steps=total_steps
    )

    # Set multi-task weights
    multi_task_weights = {
        'growth_rate': args.growth_weight,
        'temperature': args.temperature_weight
    }

    # Train model
    save_path = os.path.join(args.output_dir, 'best_model.pt')
    history = trainer.train(
        train_loader=train_loader,
        val_loader=test_loader,
        epochs=args.epochs,
        patience=args.patience,
        multi_task_weights=multi_task_weights,
        save_path=save_path,
        swa_loader=train_loader if args.use_swa else None  # Use training loader for SWA batch norm updates
    )

    # Plot training history
    if args.metrics_dir:
        plot_training_history(history, args.metrics_dir)

    # Evaluate final model
    final_metrics = trainer.validate(test_loader, multi_task_weights)

    # Save final metrics
    metrics_file = os.path.join(args.output_dir, 'metrics.tsv')
    save_metrics_to_tsv(final_metrics, metrics_file)

    # Log final metrics
    logger.info("Final metrics:")
    for metric, value in final_metrics.items():
        logger.info(f"  {metric}: {value:.4f}")

    logger.info(f"Training complete. Best model saved to {save_path}")


if __name__ == '__main__':
    main()
