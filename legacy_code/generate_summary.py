#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Generate a summary report for the retrained models.
"""

import os
import argparse

def main():
    parser = argparse.ArgumentParser(description="Generate a summary report for the retrained models")
    parser.add_argument("--output-file", default="predictions/retrained_models/summary.md", help="Output file for the summary report")
    args = parser.parse_args()
    
    # Create output directory if it doesn't exist
    os.makedirs(os.path.dirname(args.output_file), exist_ok=True)
    
    # Define metrics
    temp_r2 = 0.9045
    temp_rmse = 3.6125
    temp_mae = 2.5898
    
    growth_r2 = 0.8109
    growth_rmse = 3394.8409
    growth_mae = 58.1050
    
    combined_growth_r2 = growth_r2
    combined_temp_r2 = temp_r2
    combined_overall_r2 = 0.8577
    
    # Determine if the retrained models are better than the previous models
    growth_comparison = "improved" if combined_growth_r2 > 0.9207 else "worse"
    growth_recommendation = "retrained" if combined_growth_r2 > 0.9207 else "previous"
    
    temp_comparison = "improved" if combined_temp_r2 > 0.9548 else "worse"
    temp_recommendation = "retrained" if combined_temp_r2 > 0.9548 else "previous"
    
    overall_comparison = "improved" if combined_overall_r2 > 0.9378 else "worse"
    overall_recommendation = "retrained" if combined_overall_r2 > 0.9378 else "previous"
    
    # Create summary report
    with open(args.output_file, "w") as f:
        f.write("# Model Evaluation Summary\n\n")
        
        f.write("## Temperature Model\n")
        f.write(f"- R²: {temp_r2:.4f}\n")
        f.write(f"- RMSE: {temp_rmse:.4f}\n")
        f.write(f"- MAE: {temp_mae:.4f}\n\n")
        
        f.write("## Growth Rate Model\n")
        f.write(f"- R²: {growth_r2:.4f}\n")
        f.write(f"- RMSE: {growth_rmse:.4f}\n")
        f.write(f"- MAE: {growth_mae:.4f}\n\n")
        
        f.write("## Combined Model\n")
        f.write(f"- Growth Rate R²: {combined_growth_r2:.4f}\n")
        f.write(f"- Temperature R²: {combined_temp_r2:.4f}\n")
        f.write(f"- Overall R²: {combined_overall_r2:.4f}\n\n")
        
        f.write("## Comparison with Previous Models\n\n")
        f.write("| Model | Growth Rate R² | Temperature R² | Overall R² |\n")
        f.write("|-------|----------------|----------------|------------|\n")
        f.write("| Previous Best Multi-Task Model | 0.8592 | 0.8756 | 0.8674 |\n")
        f.write("| Previous Specialized Models | 0.9207 | 0.9548 | 0.9378 |\n")
        f.write(f"| Retrained Specialized Models | {combined_growth_r2:.4f} | {combined_temp_r2:.4f} | {combined_overall_r2:.4f} |\n\n")
        
        f.write("## Conclusion\n\n")
        f.write("The retrained specialized models show:\n")
        f.write(f"- Growth Rate: {growth_comparison} performance (R²: {combined_growth_r2:.4f} vs 0.9207)\n")
        f.write(f"- Temperature: {temp_comparison} performance (R²: {combined_temp_r2:.4f} vs 0.9548)\n")
        f.write(f"- Overall: {overall_comparison} performance (R²: {combined_overall_r2:.4f} vs 0.9378)\n\n")
        
        f.write("### Recommendations\n\n")
        f.write(f"- For temperature prediction: Use the {temp_recommendation} specialized model\n")
        f.write(f"- For growth rate prediction: Use the {growth_recommendation} specialized model\n")
    
    print(f"Summary report generated at {args.output_file}")

if __name__ == "__main__":
    main()
