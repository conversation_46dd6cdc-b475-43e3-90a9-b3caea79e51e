#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Reinforcement Learning-based Agent for Temperature Prediction.

This module implements a specialized RL agent for temperature prediction
using a transformer-based architecture trained with reinforcement learning
to achieve higher accuracy and stability.
"""

import os
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
import logging
import joblib
from typing import Dict, List, Tuple, Optional, Any

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class VerifiableRewardFunction:
    """
    Reward function for reinforcement learning temperature prediction.
    
    This reward function combines accuracy, stability, and consistency components
    to guide the RL agent toward optimal temperature predictions.
    """
    
    def __init__(
        self,
        baseline_model=None,
        alpha: float = 0.7,  # Weight for accuracy component
        beta: float = 0.2,   # Weight for consistency component
        gamma: float = 0.1,  # Weight for improvement component
        accuracy_scale: float = 1.0,
        clip_rewards: bool = True,
        reward_clip_range: Tuple[float, float] = (-1.0, 1.0)
    ):
        """
        Initialize the reward function.
        
        Args:
            baseline_model: Optional baseline model for comparison
            alpha: Weight for accuracy component
            beta: Weight for consistency component
            gamma: Weight for improvement component
            accuracy_scale: Scaling factor for accuracy rewards
            clip_rewards: Whether to clip rewards
            reward_clip_range: Range for reward clipping
        """
        self.baseline = baseline_model
        self.alpha = alpha
        self.beta = beta
        self.gamma = gamma
        self.accuracy_scale = accuracy_scale
        self.clip_rewards = clip_rewards
        self.reward_clip_range = reward_clip_range
        
        # Track history for adaptive scaling
        self.reward_history = []
        self.baseline_errors = []
        
    def __call__(self, y_true: np.ndarray, y_pred: np.ndarray, features: Optional[np.ndarray] = None) -> np.ndarray:
        """
        Calculate rewards based on prediction accuracy.
        
        Args:
            y_true: Ground truth temperatures
            y_pred: Predicted temperatures
            features: Input features for baseline prediction
            
        Returns:
            Array of rewards
        """
        # Ensure arrays are properly shaped
        y_true = np.asarray(y_true).reshape(-1)
        y_pred = np.asarray(y_pred).reshape(-1)
        
        # Get baseline predictions if available
        if features is not None and self.baseline is not None:
            try:
                # Extract feature array if it's in a dictionary
                if isinstance(features, dict) and 'features' in features:
                    feature_array = features['features']
                else:
                    feature_array = features
                
                baseline_pred = self.baseline.predict(feature_array)
                baseline_pred = np.asarray(baseline_pred).reshape(-1)
                baseline_error = np.abs(y_true - baseline_pred)
                self.baseline_errors.extend(baseline_error.tolist())
            except Exception as e:
                logger.warning(f"Error getting baseline predictions: {e}")
                baseline_pred = np.zeros_like(y_true)
                baseline_error = np.abs(y_true)
        else:
            # If no baseline, use zeros
            baseline_pred = np.zeros_like(y_true)
            baseline_error = np.abs(y_true)
        
        # Calculate accuracy component
        prediction_error = np.abs(y_true - y_pred)
        
        # Temperature-specific scaling: more precise reward for temperatures
        # Use inverse error scaled based on temperature ranges (usually 20-45°C)
        scaled_error = np.clip(prediction_error, 0.01, 5.0)
        accuracy_component = 1.0 / (self.accuracy_scale * scaled_error + 1.0)
        
        # Add R² component for batch predictions
        if len(y_true) > 1:
            try:
                r2 = r2_score(y_true, y_pred)
                # Scale R² to [0, 1] range
                r2_scaled = np.clip((r2 + 1) / 2, 0, 1)
                # Weight accuracy with R²
                accuracy_component = 0.7 * accuracy_component + 0.3 * r2_scaled
            except Exception as e:
                logger.warning(f"Error calculating R² score: {e}")
        
        # Calculate consistency component if baseline exists
        consistency_error = np.abs(y_pred - baseline_pred)
        scaled_consistency_error = np.clip(consistency_error, 0.01, 5.0)
        consistency_component = 1.0 / (scaled_consistency_error + 1.0)
        
        # Calculate improvement component
        improvement = np.maximum(0, baseline_error - prediction_error)
        max_possible_improvement = np.maximum(baseline_error, 1e-8)
        improvement_component = improvement / max_possible_improvement
        
        # Combine components
        rewards = (
            self.alpha * accuracy_component +
            self.beta * consistency_component +
            self.gamma * improvement_component
        )
        
        # Clip rewards if enabled
        if self.clip_rewards:
            rewards = np.clip(rewards, self.reward_clip_range[0], self.reward_clip_range[1])
        
        # Store rewards
        self.reward_history.extend(rewards.tolist())
        
        return rewards
    
    def get_reward_stats(self) -> Dict[str, float]:
        """Get statistics about rewards."""
        if not self.reward_history:
            return {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0, "count": 0}
        
        rewards = np.array(self.reward_history)
        return {
            "mean": float(np.mean(rewards)),
            "std": float(np.std(rewards)),
            "min": float(np.min(rewards)),
            "max": float(np.max(rewards)),
            "count": len(rewards)
        }
    
    def reset_history(self):
        """Reset reward history."""
        self.reward_history = []
        self.baseline_errors = []

class SelfAttention(nn.Module):
    """Self-attention module for capturing dependencies between features."""
    
    def __init__(self, embed_dim, num_heads=8, dropout=0.1):
        super().__init__()
        self.attention = nn.MultiheadAttention(
            embed_dim=embed_dim,
            num_heads=num_heads,
            dropout=dropout,
            batch_first=True
        )
        self.norm = nn.LayerNorm(embed_dim)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x):
        # Add feature dimension if needed
        if len(x.shape) == 2:
            x = x.unsqueeze(1)
            squeeze_output = True
        else:
            squeeze_output = False
            
        # Apply attention with skip connection
        residual = x
        x = self.norm(x)
        attn_output, _ = self.attention(x, x, x)
        x = residual + self.dropout(attn_output)
        
        # Remove feature dimension if added
        if squeeze_output:
            x = x.squeeze(1)
            
        return x

class TemperatureRLAgent(nn.Module):
    """
    Reinforcement learning agent for temperature prediction.
    
    Uses a transformer-based architecture to capture complex patterns
    in microbial data for optimal temperature prediction.
    """
    
    def __init__(
        self,
        input_dim: int,
        hidden_dim: int = 256,
        num_heads: int = 4,
        num_layers: int = 2,
        dropout: float = 0.2,
        use_layer_norm: bool = True,
        use_residual: bool = True,
        use_value_head: bool = True,
        activation: str = 'gelu'
    ):
        """
        Initialize the temperature RL agent.
        
        Args:
            input_dim: Dimension of input features
            hidden_dim: Dimension of hidden layers
            num_heads: Number of attention heads
            num_layers: Number of transformer layers
            dropout: Dropout rate
            use_layer_norm: Whether to use layer normalization
            use_residual: Whether to use residual connections
            use_value_head: Whether to use value head for uncertainty
            activation: Activation function to use
        """
        super().__init__()
        
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.use_value_head = use_value_head
        self.use_residual = use_residual
        
        # Select activation function
        if activation == 'gelu':
            self.activation = nn.GELU()
        elif activation == 'silu':
            self.activation = nn.SiLU()
        else:
            self.activation = nn.ReLU()
        
        # Feature encoder
        self.feature_encoder = self._create_feature_encoder(
            input_dim, hidden_dim, dropout, use_layer_norm
        )
        
        # Transformer layers
        self.transformer_layers = nn.ModuleList([
            nn.TransformerEncoderLayer(
                d_model=hidden_dim,
                nhead=num_heads,
                dim_feedforward=hidden_dim * 4,
                dropout=dropout,
                activation=activation,
                batch_first=True,
                norm_first=True  # Pre-LN architecture for stability
            ) for _ in range(num_layers)
        ])
        
        # Policy head for temperature prediction
        policy_layers = [
            nn.Linear(hidden_dim, hidden_dim // 2),
            self.activation,
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 2, 1)
        ]
        self.policy_head = nn.Sequential(*policy_layers)
        
        # Value head for uncertainty estimation (optional)
        if use_value_head:
            value_layers = [
                nn.Linear(hidden_dim, hidden_dim // 2),
                self.activation,
                nn.Dropout(dropout),
                nn.Linear(hidden_dim // 2, 1)
            ]
            self.value_head = nn.Sequential(*value_layers)
        
        # Initialize weights
        self._init_weights()
        
        # Feature importance tracking
        self.feature_importance = {
            'features': np.zeros(input_dim)
        }
    
    def _create_feature_encoder(self, input_dim, hidden_dim, dropout, use_layer_norm):
        """Create feature encoder layers."""
        layers = [
            nn.Linear(input_dim, hidden_dim),
            self.activation,
            nn.Dropout(dropout)
        ]
        
        if use_layer_norm:
            layers.append(nn.LayerNorm(hidden_dim))
        
        return nn.Sequential(*layers)
    
    def _init_weights(self):
        """Initialize weights with improved schemes for RL stability."""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                # Kaiming initialization for ReLU-like activations
                nn.init.kaiming_normal_(module.weight, nonlinearity='relu')
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
    
    def forward(self, features: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        Forward pass through the agent.
        
        Args:
            features: Input features tensor
            
        Returns:
            Dictionary with temperature and uncertainty predictions
        """
        # Encode features
        x = self.feature_encoder(features)
        
        # Add positional dimension for transformer if needed
        if len(x.shape) == 2:
            x = x.unsqueeze(1)
            
        # Apply transformer layers
        for transformer_layer in self.transformer_layers:
            x = transformer_layer(x)
            
        # Remove positional dimension if added
        if x.shape[1] == 1:
            x = x.squeeze(1)
            
        # Apply policy head for temperature prediction
        temperature = self.policy_head(x).squeeze(-1)
        
        # Apply value head for uncertainty estimation if enabled
        output = {'temperature': temperature}
        if self.use_value_head:
            uncertainty = self.value_head(x).squeeze(-1)
            output['uncertainty'] = uncertainty
            
        return output
    
    def predict(self, features: torch.Tensor) -> Dict[str, np.ndarray]:
        """
        Make temperature predictions.
        
        Args:
            features: Input features tensor
            
        Returns:
            Dictionary with temperature predictions as NumPy arrays
        """
        self.eval()
        with torch.no_grad():
            output = self.forward(features)
            
        # Convert tensors to NumPy arrays
        predictions = {}
        for key, value in output.items():
            if isinstance(value, torch.Tensor):
                predictions[key] = value.cpu().numpy()
            else:
                predictions[key] = value
                
        return predictions
    
    def get_feature_importance(self) -> Dict[str, np.ndarray]:
        """Get feature importance scores."""
        return self.feature_importance
    
    def update_feature_importance(self, gradients: torch.Tensor):
        """Update feature importance based on gradients."""
        if gradients is not None and isinstance(gradients, torch.Tensor):
            # Update importance based on gradient magnitudes
            importance = torch.abs(gradients).mean(0).cpu().numpy()
            self.feature_importance['features'] += importance 