#!/usr/bin/env python3
"""
Fixed PyTorch Tabular model for growth rate prediction.
This script addresses the data type issues that were causing NaN metrics.
"""

import os
import argparse
import logging
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.preprocessing import StandardScaler
from sklearn.impute import SimpleImputer
from pytorch_tabular import TabularModel
from pytorch_tabular.config import DataConfig, OptimizerConfig, TrainerConfig
from pytorch_tabular.models import category_embedding, tab_transformer

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_data(feature_file, metadata_file):
    """
    Load feature and metadata files.
    
    Args:
        feature_file: Path to the feature file
        metadata_file: Path to the metadata file
        
    Returns:
        Tuple of (features, metadata)
    """
    # Load features
    features = pd.read_csv(feature_file, sep='\t', index_col=0)
    
    # Load metadata
    metadata = pd.read_csv(metadata_file, sep='\t', index_col=0)
    
    # Ensure the indices match
    common_indices = features.index.intersection(metadata.index)
    features = features.loc[common_indices]
    metadata = metadata.loc[common_indices]
    
    logger.info(f"Loaded {len(features)} samples with {features.shape[1]} features")
    
    return features, metadata

def preprocess_data(features, targets):
    """
    Preprocess the data for model training.
    
    Args:
        features: Feature DataFrame
        targets: Target DataFrame
        
    Returns:
        Tuple of (processed_features, processed_targets)
    """
    # Check for NaN values
    nan_features = features.isna().sum().sum()
    nan_targets = targets.isna().sum().sum()
    
    logger.info(f"NaN values in features: {nan_features}, NaN values in targets: {nan_targets}")
    
    # Convert all features to float64
    features = features.astype(float)
    
    # Impute missing values
    imputer = SimpleImputer(strategy='mean')
    features_imputed = pd.DataFrame(
        imputer.fit_transform(features),
        columns=features.columns,
        index=features.index
    )
    
    # Scale the features
    scaler = StandardScaler()
    features_scaled = pd.DataFrame(
        scaler.fit_transform(features_imputed),
        columns=features.columns,
        index=features.index
    )
    
    # Convert to float32 for PyTorch
    features_scaled = features_scaled.astype(np.float32)
    targets = targets.astype(np.float32)
    
    logger.info(f"Preprocessed data: features: {features_scaled.shape}, targets: {targets.shape}")
    
    return features_scaled, targets

def get_model_config(model_name):
    """Get model configuration based on model name."""
    if model_name == "CategoryEmbedding":
        return category_embedding.CategoryEmbeddingModelConfig(
            task="regression",
            layers="256-128-64",
            activation="LeakyReLU",
            learning_rate=1e-3,
            metrics=["mean_squared_error"],
            metrics_params=[{}],
            target_range=None,  # No target range for regression
        )
    elif model_name == "TabTransformer":
        return tab_transformer.TabTransformerConfig(
            task="regression",
            input_embed_dim=32,
            num_attn_blocks=3,
            num_heads=8,
            attn_dropout=0.1,
            add_norm_dropout=0.1,
            ff_dropout=0.1,
            learning_rate=1e-3,
            metrics=["mean_squared_error"],
            metrics_params=[{}],
            target_range=None,  # No target range for regression
        )
    else:
        raise ValueError(f"Unknown model name: {model_name}")

def train_and_evaluate_model(model_name, features, targets, output_dir):
    """
    Train and evaluate a PyTorch Tabular model.
    
    Args:
        model_name: Name of the model to train
        features: Feature DataFrame
        targets: Target DataFrame
        output_dir: Directory to save outputs
        
    Returns:
        Dictionary of metrics
    """
    # Create model directory
    model_dir = os.path.join(output_dir, model_name.lower())
    os.makedirs(model_dir, exist_ok=True)
    
    # Split the data
    X_train, X_test, y_train, y_test = train_test_split(
        features, targets, test_size=0.2, random_state=42
    )
    
    # Combine features and targets for training
    train_data = pd.concat([X_train, y_train], axis=1)
    test_data = pd.concat([X_test, y_test], axis=1)
    
    # Create data config
    data_config = DataConfig(
        target=['growth_rate', 'optimal_temperature'],
        categorical_cols=[],
        continuous_cols=features.columns.tolist(),
        continuous_feature_transform=None,  # We've already transformed the features
        normalize_continuous_features=False  # We've already normalized the features
    )
    
    # Create trainer config
    trainer_config = TrainerConfig(
        max_epochs=20,
        checkpoints=None,
        early_stopping="valid_loss",
        early_stopping_patience=5,
        batch_size=64,
        accelerator="auto",
        check_val_every_n_epoch=1,
        trainer_kwargs={"log_every_n_steps": 10}
    )
    
    # Create optimizer config
    optimizer_config = OptimizerConfig()
    
    # Get the model config
    model_config = get_model_config(model_name)
    
    try:
        # Create the model
        logger.info(f"Creating {model_name} model...")
        model = TabularModel(
            data_config=data_config,
            model_config=model_config,
            optimizer_config=optimizer_config,
            trainer_config=trainer_config,
        )
        
        # Train the model
        logger.info(f"Training {model_name} model...")
        model.fit(train=train_data, validation=test_data)
        
        # Make predictions
        logger.info(f"Making predictions with {model_name} model...")
        predictions = model.predict(X_test)
        
        # Check prediction columns
        logger.info(f"Prediction columns: {predictions.columns.tolist()}")
        
        # Extract predictions
        growth_rate_pred = predictions['growth_rate_prediction'].values
        temp_pred = predictions['optimal_temperature_prediction'].values
        
        # Calculate metrics
        growth_rate_mse = mean_squared_error(y_test['growth_rate'], growth_rate_pred)
        growth_rate_rmse = np.sqrt(growth_rate_mse)
        growth_rate_r2 = r2_score(y_test['growth_rate'], growth_rate_pred)
        
        temp_mse = mean_squared_error(y_test['optimal_temperature'], temp_pred)
        temp_rmse = np.sqrt(temp_mse)
        temp_r2 = r2_score(y_test['optimal_temperature'], temp_pred)
        
        logger.info(f"Growth Rate - MSE: {growth_rate_mse:.4f}, RMSE: {growth_rate_rmse:.4f}, R²: {growth_rate_r2:.4f}")
        logger.info(f"Optimal Temperature - MSE: {temp_mse:.4f}, RMSE: {temp_rmse:.4f}, R²: {temp_r2:.4f}")
        
        # Plot actual vs predicted for growth rate
        plt.figure(figsize=(10, 6))
        plt.scatter(y_test['growth_rate'], growth_rate_pred, alpha=0.5)
        plt.plot([y_test['growth_rate'].min(), y_test['growth_rate'].max()], 
                 [y_test['growth_rate'].min(), y_test['growth_rate'].max()], 'r--')
        plt.xlabel('Actual Growth Rate')
        plt.ylabel('Predicted Growth Rate')
        plt.title(f'{model_name}: Actual vs Predicted Growth Rate')
        plt.savefig(os.path.join(model_dir, 'growth_rate_actual_vs_predicted.png'))
        plt.close()
        
        # Plot actual vs predicted for optimal temperature
        plt.figure(figsize=(10, 6))
        plt.scatter(y_test['optimal_temperature'], temp_pred, alpha=0.5)
        plt.plot([y_test['optimal_temperature'].min(), y_test['optimal_temperature'].max()], 
                 [y_test['optimal_temperature'].min(), y_test['optimal_temperature'].max()], 'r--')
        plt.xlabel('Actual Optimal Temperature')
        plt.ylabel('Predicted Optimal Temperature')
        plt.title(f'{model_name}: Actual vs Predicted Optimal Temperature')
        plt.savefig(os.path.join(model_dir, 'optimal_temperature_actual_vs_predicted.png'))
        plt.close()
        
        # Save model
        model_path = os.path.join(model_dir, 'model')
        model.save_model(model_path)
        logger.info(f"Model saved to {model_path}")
        
        return {
            'growth_rate_mse': growth_rate_mse,
            'growth_rate_rmse': growth_rate_rmse,
            'growth_rate_r2': growth_rate_r2,
            'temp_mse': temp_mse,
            'temp_rmse': temp_rmse,
            'temp_r2': temp_r2
        }
    
    except Exception as e:
        logger.error(f"Error training {model_name} model: {e}")
        return {
            'growth_rate_mse': float('nan'),
            'growth_rate_rmse': float('nan'),
            'growth_rate_r2': float('nan'),
            'temp_mse': float('nan'),
            'temp_rmse': float('nan'),
            'temp_r2': float('nan')
        }

def main():
    parser = argparse.ArgumentParser(description="Train PyTorch Tabular models for growth rate prediction")
    parser.add_argument("--feature_file", required=True, help="Path to the feature file")
    parser.add_argument("--metadata_file", required=True, help="Path to the metadata file")
    parser.add_argument("--output_dir", default="models/fixed_pytorch_tabular", help="Directory to save model outputs")
    args = parser.parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Load data
    features, metadata = load_data(args.feature_file, args.metadata_file)
    
    # Extract target variables
    targets = metadata[['growth_rate', 'optimal_temperature']]
    
    # Preprocess data
    features_processed, targets_processed = preprocess_data(features, targets)
    
    # Train and evaluate models
    results = {}
    for model_name in ["CategoryEmbedding", "TabTransformer"]:
        logger.info(f"Processing model: {model_name}")
        results[model_name] = train_and_evaluate_model(
            model_name, features_processed, targets_processed, args.output_dir
        )
    
    # Save overall results
    results_df = pd.DataFrame({
        'Metric': [
            'Growth Rate MSE', 'Growth Rate RMSE', 'Growth Rate R²',
            'Optimal Temperature MSE', 'Optimal Temperature RMSE', 'Optimal Temperature R²'
        ],
        'CategoryEmbedding': [
            results['CategoryEmbedding']['growth_rate_mse'],
            results['CategoryEmbedding']['growth_rate_rmse'],
            results['CategoryEmbedding']['growth_rate_r2'],
            results['CategoryEmbedding']['temp_mse'],
            results['CategoryEmbedding']['temp_rmse'],
            results['CategoryEmbedding']['temp_r2']
        ],
        'TabTransformer': [
            results['TabTransformer']['growth_rate_mse'],
            results['TabTransformer']['growth_rate_rmse'],
            results['TabTransformer']['growth_rate_r2'],
            results['TabTransformer']['temp_mse'],
            results['TabTransformer']['temp_rmse'],
            results['TabTransformer']['temp_r2']
        ]
    })
    
    results_df.to_csv(os.path.join(args.output_dir, 'model_comparison.csv'), index=False)
    logger.info(f"Results saved to {os.path.join(args.output_dir, 'model_comparison.csv')}")
    
    # Print summary
    logger.info("\nSummary of Results:")
    for model_name in results:
        logger.info(f"\n{model_name}:")
        logger.info(f"Growth Rate - RMSE: {results[model_name]['growth_rate_rmse']:.4f}, R²: {results[model_name]['growth_rate_r2']:.4f}")
        logger.info(f"Optimal Temperature - RMSE: {results[model_name]['temp_rmse']:.4f}, R²: {results[model_name]['temp_r2']:.4f}")

if __name__ == '__main__':
    main()
