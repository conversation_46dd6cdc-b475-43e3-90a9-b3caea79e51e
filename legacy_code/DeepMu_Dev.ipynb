{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# DeepMu Development Notebook\n", "\n", "This notebook demonstrates how to load and explore the DeepMu dataset for predicting microbial growth rates and optimal temperatures."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# Import necessary libraries\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.ensemble import RandomForestRegressor\n", "from sklearn.metrics import mean_squared_error, r2_score\n", "\n", "# Set visualization style\n", "sns.set(style=\"whitegrid\")\n", "plt.rcParams['figure.figsize'] = (12, 8)\n", "\n", "# For reproducibility\n", "np.random.seed(42)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load Data\n", "\n", "Load the feature and metadata files from the specified paths."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Features data shape: (2493, 1485)\n", "Features sample:\n", "         genome_id  Genome_CUB  Genome_CPB  Genome_Consistency  \\\n", "0  GCF_000010305.1    0.256414   -0.541564            0.515859   \n", "1  GCF_000023225.1    0.232784   -0.547173            0.528240   \n", "2  GCF_000218155.1    0.445106   -0.536838            0.636932   \n", "3  GCF_000290435.1    0.186197   -0.575758            0.553456   \n", "4  GCF_000332955.2    0.159820   -0.518602            0.521178   \n", "\n", "   Genome_RemoteInteraction  GC_content       GC1       GC2       GC3  \\\n", "0                  0.001378    0.644783  0.673065  0.491670  0.769612   \n", "1                  0.002029    0.596433  0.619143  0.422173  0.747982   \n", "2                  0.001716    0.695555  0.693998  0.492081  0.900585   \n", "3                  0.000798    0.359278  0.481003  0.338636  0.258196   \n", "4                  0.007788    0.442969  0.508780  0.356721  0.463406   \n", "\n", "   Context_bias  ...  pathway_completeness_ko05330  \\\n", "0      0.260520  ...                           NaN   \n", "1      0.250319  ...                           NaN   \n", "2      0.266438  ...                           NaN   \n", "3      0.250123  ...                           NaN   \n", "4      0.257307  ...                           NaN   \n", "\n", "   pathway_completeness_ko05332  pathway_completeness_ko05340  \\\n", "0                           NaN                           NaN   \n", "1                           NaN                           NaN   \n", "2                           NaN                           NaN   \n", "3                           NaN                           NaN   \n", "4                           NaN                           NaN   \n", "\n", "   pathway_completeness_ko05410  pathway_completeness_ko05412  \\\n", "0                           NaN                           NaN   \n", "1                           NaN                           NaN   \n", "2                           NaN                           NaN   \n", "3                           NaN                           NaN   \n", "4                           NaN                           NaN   \n", "\n", "   pathway_completeness_ko05414  pathway_completeness_ko05415  \\\n", "0                           NaN                           NaN   \n", "1                           NaN                           NaN   \n", "2                           NaN                           NaN   \n", "3                           NaN                           NaN   \n", "4                           NaN                           NaN   \n", "\n", "   pathway_completeness_ko05416  pathway_completeness_ko05417  \\\n", "0                           NaN                           NaN   \n", "1                           NaN                           NaN   \n", "2                           NaN                           NaN   \n", "3                           NaN                           NaN   \n", "4                           NaN                           NaN   \n", "\n", "   pathway_completeness_ko05418  \n", "0                           NaN  \n", "1                           NaN  \n", "2                           NaN  \n", "3                           NaN  \n", "4                           NaN  \n", "\n", "[5 rows x 1485 columns]\n", "\n", "Metadata shape: (2493, 6)\n", "Metadata sample:\n", "         genome_id  growth_rate  optimal_temperature    taxid  codon_table  \\\n", "0  GCA_000219855.1    12.683395                 50.0  1051632           11   \n", "1  GCA_000283575.1     2.059530                 30.0   693746           11   \n", "2  GCA_002384805.1     1.322013                 29.0   310575           11   \n", "3  GCA_002399725.1     3.757216                 35.0   732242           11   \n", "4  GCA_002454385.1     0.194455                 32.5   202611           11   \n", "\n", "  kingdom  \n", "0     bac  \n", "1     bac  \n", "2     bac  \n", "3     bac  \n", "4     bac  \n"]}], "source": ["# File paths\n", "FEATURES_FILE = \"./features/combined_features.tsv\"\n", "METADATA_FILE = \"./training_data/metadata.tsv\"\n", "\n", "# Load the feature data\n", "features_df = pd.read_csv(FEATURES_FILE, sep=\"\\t\")\n", "print(f\"Features data shape: {features_df.shape}\")\n", "print(f\"Features sample:\\n{features_df.head()}\")\n", "\n", "# Load the metadata\n", "metadata_df = pd.read_csv(METADATA_FILE, sep=\"\\t\")\n", "print(f\"\\nMetadata shape: {metadata_df.shape}\")\n", "print(f\"Metadata sample:\\n{metadata_df.head()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Exploration and Preparation\n", "\n", "Merge the features and metadata, and explore the combined dataset."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Merge features and metadata on genome_id\n", "merged_df = pd.merge(metadata_df, features_df, on=\"genome_id\", how=\"inner\")\n", "print(f\"Merged data shape: {merged_df.shape}\")\n", "print(f\"Merged data sample:\\n{merged_df.head()}\")\n", "\n", "# Check the target variables\n", "print(\"\\nGrowth rate statistics:\")\n", "print(merged_df['growth_rate'].describe())\n", "\n", "print(\"\\nOptimal temperature statistics:\")\n", "print(merged_df['optimal_temperature'].describe())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Visualization\n", "\n", "Visualize distributions and relationships between variables."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Set up the figure\n", "fig, axes = plt.subplots(1, 2, figsize=(16, 6))\n", "\n", "# Growth rate distribution\n", "sns.histplot(merged_df['growth_rate'].dropna(), kde=True, ax=axes[0])\n", "axes[0].set_title('Distribution of Growth Rate')\n", "axes[0].set_xlabel('Growth Rate')\n", "\n", "# Optimal temperature distribution\n", "sns.histplot(merged_df['optimal_temperature'].dropna(), kde=True, ax=axes[1])\n", "axes[1].set_title('Distribution of Optimal Temperature')\n", "axes[1].set_xlabel('Optimal Temperature (°C)')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Scatter plot of growth rate vs optimal temperature\n", "plt.figure(figsize=(10, 8))\n", "sns.scatterplot(x='optimal_temperature', y='growth_rate', data=merged_df.dropna())\n", "plt.title('Growth Rate vs Optimal Temperature')\n", "plt.xlabel('Optimal Temperature (°C)')\n", "plt.ylabel('Growth Rate')\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 4}