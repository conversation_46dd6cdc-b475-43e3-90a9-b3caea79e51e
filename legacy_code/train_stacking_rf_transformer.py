#!/usr/bin/env python3
"""
Training script for a hybrid model that combines:
1. A stacking ensemble (Random Forest + XGBoost + LightGBM) for growth rate prediction
2. A Transformer model for optimal temperature prediction
"""

import os
import sys
import argparse
import logging
import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any, Union
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.ensemble import RandomForestRegressor, StackingRegressor, GradientBoostingRegressor, VotingRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.feature_selection import SelectFromModel, RFE, RFECV
from sklearn.inspection import permutation_importance
from sklearn.linear_model import Ridge
from joblib import dump, load
import xgboost as xgb
import lightgbm as lgb
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import itertools

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('stacking_rf_transformer')

class TransformerRegressor(nn.Module):
    """Simplified Transformer model for regression tasks with robust error handling."""
    
    def __init__(self, input_dim, hidden_dim=64, num_layers=2, num_heads=4, dropout=0.1):
        super().__init__()
        # Simpler architecture with fewer potential points of failure
        self.input_projection = nn.Linear(input_dim, hidden_dim)
        self.norm1 = nn.LayerNorm(hidden_dim)
        self.linear1 = nn.Linear(hidden_dim, hidden_dim * 2)
        self.activation = nn.ReLU()
        self.dropout = nn.Dropout(dropout)
        self.linear2 = nn.Linear(hidden_dim * 2, hidden_dim)
        self.norm2 = nn.LayerNorm(hidden_dim)
        self.output_projection = nn.Linear(hidden_dim, 1)
        
        # Initialize weights
        self._init_weights()
    
    def _init_weights(self):
        """Initialize weights with stable defaults."""
        for p in self.parameters():
            if p.dim() > 1:
                nn.init.xavier_normal_(p, gain=0.5)
            else:
                nn.init.zeros_(p)
    
    def forward(self, x):
        try:
            # Replace NaNs
            x = torch.nan_to_num(x, nan=0.0)
            
            # Forward pass with residual connections
            residual = self.input_projection(x)
            residual = self.norm1(residual)
            
            # MLP block
            out = self.linear1(residual)
            out = self.activation(out)
            out = self.dropout(out)
            out = self.linear2(out)
            
            # Residual connection
            out = out + residual
            out = self.norm2(out)
            
            # Final projection
            out = self.output_projection(out)
            
            # Safety check
            out = torch.nan_to_num(out, nan=0.0)
            
            return out.squeeze(-1)
        except Exception as e:
            # Handle errors
            logger.error(f"Error in forward pass: {e}")
            return torch.zeros(x.size(0), device=x.device)

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Train a hybrid stacking ensemble + transformer model')

    # Data arguments
    parser.add_argument('--metadata', required=True, help='Path to metadata TSV file')
    parser.add_argument('--feature-file', required=True, help='Path to combined features TSV file')
    parser.add_argument('--output-dir', required=True, help='Directory to save model and results')
    parser.add_argument('--metrics-dir', default=None, help='Directory to save metrics')

    # Stacking ensemble arguments (Random Forest)
    parser.add_argument('--rf-n-estimators', type=int, default=200, help='Number of trees in Random Forest')
    parser.add_argument('--rf-max-depth', type=int, default=None, help='Maximum depth of trees in Random Forest')
    parser.add_argument('--rf-min-samples-split', type=int, default=2, help='Minimum samples to split in Random Forest')
    parser.add_argument('--rf-min-samples-leaf', type=int, default=1, help='Minimum samples in leaf in Random Forest')

    # Stacking ensemble arguments (XGBoost)
    parser.add_argument('--xgb-n-estimators', type=int, default=200, help='Number of trees in XGBoost')
    parser.add_argument('--xgb-learning-rate', type=float, default=0.1, help='Learning rate for XGBoost')
    parser.add_argument('--xgb-max-depth', type=int, default=6, help='Maximum depth of trees in XGBoost')
    parser.add_argument('--xgb-subsample', type=float, default=0.8, help='Subsample ratio for XGBoost')

    # Stacking ensemble arguments (LightGBM)
    parser.add_argument('--lgb-n-estimators', type=int, default=200, help='Number of trees in LightGBM')
    parser.add_argument('--lgb-learning-rate', type=float, default=0.1, help='Learning rate for LightGBM')
    parser.add_argument('--lgb-max-depth', type=int, default=6, help='Maximum depth of trees in LightGBM')
    parser.add_argument('--lgb-subsample', type=float, default=0.8, help='Subsample ratio for LightGBM')
    parser.add_argument('--lgb-min-child-samples', type=int, default=20, help='Minimum number of samples in a child for LightGBM')
    parser.add_argument('--lgb-reg-alpha', type=float, default=0.1, help='L1 regularization for LightGBM')
    parser.add_argument('--lgb-reg-lambda', type=float, default=0.1, help='L2 regularization for LightGBM')

    # Transformer arguments
    parser.add_argument('--transformer-hidden-dim', type=int, default=64, help='Hidden dimension for Transformer')
    parser.add_argument('--transformer-layers', type=int, default=2, help='Number of Transformer layers')
    parser.add_argument('--transformer-heads', type=int, default=4, help='Number of attention heads')
    parser.add_argument('--transformer-dropout', type=float, default=0.1, help='Dropout rate for Transformer')
    parser.add_argument('--transformer-lr', type=float, default=0.001, help='Learning rate for Transformer')
    parser.add_argument('--transformer-epochs', type=int, default=100, help='Number of epochs for Transformer')
    parser.add_argument('--transformer-batch-size', type=int, default=64, help='Batch size for Transformer')

    # Feature selection arguments
    parser.add_argument('--growth-rate-features', type=int, default=100, help='Number of features for growth rate')
    parser.add_argument('--temperature-features', type=int, default=80, help='Number of features for temperature')
    
    # RFE arguments
    parser.add_argument('--use-rfe', action='store_true', help='Use Recursive Feature Elimination')
    parser.add_argument('--rfe-step', type=float, default=0.2, help='Step size for RFE (fraction of features to remove at each step)')
    
    # Advanced interaction arguments
    parser.add_argument('--use-advanced-interactions', action='store_true', help='Use advanced feature interactions')
    parser.add_argument('--interaction-depth', type=int, default=2, help='Maximum depth for feature interactions (2 for pairwise, 3 for triplets, etc.)')
    parser.add_argument('--max-interactions', type=int, default=50, help='Maximum number of interaction features to create')

    # Training arguments
    parser.add_argument('--seed', type=int, default=42, help='Random seed')
    parser.add_argument('--target', type=str, choices=['growth_rate', 'temperature', 'both'], default='both',
                        help='Target variable to predict')
    parser.add_argument('--cv-folds', type=int, default=5, help='Number of cross-validation folds')

    return parser.parse_args()

def create_enhanced_features(features_df, metadata_df=None, include_taxonomy=True, include_breakpoints=True):
    """
    Create enhanced features including specialized features and interactions.

    Args:
        features_df (pd.DataFrame): DataFrame with features
        metadata_df (pd.DataFrame, optional): DataFrame with metadata, needed for taxonomy features
        include_taxonomy (bool): Whether to include taxonomy features
        include_breakpoints (bool): Whether to include breakpoint features

    Returns:
        pd.DataFrame: DataFrame with enhanced features
    """
    # Create a copy of the input DataFrame
    enhanced_df = features_df.copy()
    
    # Add taxonomy features if metadata is provided
    if include_taxonomy and metadata_df is not None:
        logger.info("Creating taxonomy features")
        taxonomy_df = create_taxonomy_features(metadata_df)
        # We'll merge these later
    else:
        taxonomy_df = None
    
    # Add breakpoint features
    if include_breakpoints:
        logger.info("Creating breakpoint features")
        breakpoint_df = create_breakpoint_features(features_df)
        # We'll merge these later
    else:
        breakpoint_df = None
    
    # Identify important feature categories
    codon_cols = [col for col in features_df.columns if any(pattern in col for pattern in 
                ['CUB', 'CPB', 'CAI', 'ENC', 'HEG_', 'BG_', 'delta_', 'RSCU_',
                 'codon_bias_', 'AAAI', 'aa_bias_HEG_BP', 'MILC'])]
    
    # Create squared features for important metrics
    important_cols = []
    
    # Add most important codon features
    key_codon_features = [
        'codon_divergence_HEG_BP', 'delta_ENC', 'delta_CUB', 'delta_CPB', 
        'delta_CAI', 'delta_RSCU', 'CAI_HEG', 'ENC_HEG', 'Context_bias'
    ]
    
    for col in key_codon_features:
        if col in features_df.columns:
            important_cols.append(col)
    
    # Create squared features
    squared_features = {}
    for col in important_cols:
        squared_features[f'{col}_squared'] = enhanced_df[col] ** 2
        logger.info(f"Created squared feature: {col}_squared")
    
    # Add squared features to DataFrame
    squared_df = pd.DataFrame(squared_features)
    enhanced_df = pd.concat([enhanced_df, squared_df], axis=1)
    
    # Create interaction features between key features
    interaction_features = {}
    
    # Define pairs of features to interact
    interaction_pairs = [
        ('codon_divergence_HEG_BP', 'delta_ENC'),
        ('codon_divergence_HEG_BP', 'delta_CUB'),
        ('delta_ENC', 'delta_CUB'),
        ('delta_ENC', 'delta_RSCU'),
        # Add more domain-specific pairs
        ('CAI_HEG', 'ENC_HEG'),
        ('delta_CUB', 'delta_CPB'),
        ('codon_divergence_HEG_BP', 'CAI_HEG')
    ]
    
    # Create interactions if both features exist
    for col1, col2 in interaction_pairs:
        if col1 in enhanced_df.columns and col2 in enhanced_df.columns:
            interaction_features[f'{col1}_x_{col2}'] = enhanced_df[col1] * enhanced_df[col2]
            logger.info(f"Created interaction feature: {col1}_x_{col2}")
    
    # Add interaction features to DataFrame
    if interaction_features:
        interaction_df = pd.DataFrame(interaction_features)
        enhanced_df = pd.concat([enhanced_df, interaction_df], axis=1)
    
    # Now merge all the dataframes
    result_df = enhanced_df
    
    # Add taxonomy features if available
    if taxonomy_df is not None:
        logger.info("Adding taxonomy features")
        result_df = pd.merge(result_df, taxonomy_df, on='genome_id', how='left')
    
    # Add breakpoint features if available  
    if breakpoint_df is not None:
        logger.info("Adding breakpoint features")
        result_df = pd.merge(result_df, breakpoint_df, on='genome_id', how='left')
    
    logger.info(f"Enhanced features dataset has {result_df.shape[1]} columns")
    return result_df

def select_features_with_permutation(X, y, n_features=100):
    """Select features using permutation importance for better stability"""
    model = lgb.LGBMRegressor(n_estimators=100)
    model.fit(X, y)
    perm_importance = permutation_importance(model, X, y, n_repeats=10)
    feature_importances = perm_importance.importances_mean
    return np.argsort(feature_importances)[::-1][:n_features]

def train_stacking_model(X_train, y_train, X_test, y_test, args):
    """
    Train a stacking ensemble model for growth rate prediction with RFE and advanced interactions.
    
    Args:
        X_train (np.ndarray): Training feature matrix
        y_train (np.ndarray): Training target vector
        X_test (np.ndarray): Testing feature matrix
        y_test (np.ndarray): Testing target vector
        args (argparse.Namespace): Command line arguments
        
    Returns:
        Tuple[StackingRegressor, Dict]: Trained model and metrics
    """
    logger.info(f"Training stacking ensemble model with RF, XGBoost and LightGBM")
    
    # Create Random Forest parameters dict for RFE
    rf_params = {
        'n_estimators': args.rf_n_estimators,
        'max_depth': args.rf_max_depth,
        'min_samples_split': args.rf_min_samples_split,
        'min_samples_leaf': args.rf_min_samples_leaf
    }
    
    # Convert DataFrames to numpy for RFE if needed
    X_train_np = X_train.values if hasattr(X_train, 'values') else X_train
    y_train_np = y_train.values if hasattr(y_train, 'values') else y_train
    
    # Optional: Use RFE for feature selection
    if args.use_rfe:
        logger.info("Using RFE for feature selection")
        # Get feature indices from RFE
        selected_indices = perform_rfe(
            X_train_np, y_train_np, 
            n_features_to_select=args.growth_rate_features,
            rf_params=rf_params,
            step=args.rfe_step,
            cv=args.cv_folds
        )
        
        # Use only selected features
        if hasattr(X_train, 'iloc'):
            X_train_selected = X_train.iloc[:, selected_indices]
            X_test_selected = X_test.iloc[:, selected_indices]
            
            # Get feature names for advanced interactions
            feature_names = X_train.columns[selected_indices].tolist()
        else:
            X_train_selected = X_train[:, selected_indices]
            X_test_selected = X_test[:, selected_indices]
            feature_names = [f"feature_{i}" for i in selected_indices]
            
        logger.info(f"RFE selected {len(selected_indices)} features")
    else:
        # Use all features
        X_train_selected = X_train
        X_test_selected = X_test
        if hasattr(X_train, 'columns'):
            feature_names = X_train.columns.tolist()
        else:
            feature_names = [f"feature_{i}" for i in range(X_train.shape[1])]
    
    # Optional: Create advanced interactions
    if args.use_advanced_interactions and hasattr(X_train, 'iloc'):
        logger.info("Creating advanced feature interactions")
        
        # Train preliminary model to get feature importance
        prelim_model = RandomForestRegressor(
            n_estimators=100,
            max_depth=rf_params['max_depth'],
            min_samples_split=rf_params['min_samples_split'],
            min_samples_leaf=rf_params['min_samples_leaf'],
            random_state=args.seed,
            n_jobs=-1
        )
        
        if isinstance(X_train_selected, pd.DataFrame):
            prelim_model.fit(X_train_selected.values, y_train_np)
            
            # Get important features
            importances = prelim_model.feature_importances_
            
            # Sort features by importance
            indices = np.argsort(importances)[::-1]
            top_feature_indices = indices[:min(20, len(indices))]  # Top 20 features or fewer
            top_features = [X_train_selected.columns[i] for i in top_feature_indices]
            
            # Create DataFrame versions if they aren't already
            X_train_df = X_train_selected
            X_test_df = X_test_selected
            
            # Create advanced interactions
            train_interactions = create_advanced_interactions(
                X_train_df, 
                top_features, 
                interaction_depth=args.interaction_depth,
                max_interactions=args.max_interactions
            )
            
            test_interactions = create_advanced_interactions(
                X_test_df, 
                top_features, 
                interaction_depth=args.interaction_depth,
                max_interactions=args.max_interactions
            )
            
            # Combine original features with interactions
            X_train_enhanced = pd.concat([X_train_df, train_interactions], axis=1)
            X_test_enhanced = pd.concat([X_test_df, test_interactions], axis=1)
            
            logger.info(f"Added {train_interactions.shape[1]} interaction features")
        else:
            logger.warning("Skipping advanced interactions as input is not a DataFrame")
            X_train_enhanced = X_train_selected
            X_test_enhanced = X_test_selected
    else:
        X_train_enhanced = X_train_selected
        X_test_enhanced = X_test_selected
    
    # Create base estimators
    rf = RandomForestRegressor(
        n_estimators=args.rf_n_estimators,
        max_depth=args.rf_max_depth,
        min_samples_split=args.rf_min_samples_split,
        min_samples_leaf=args.rf_min_samples_leaf,
        random_state=args.seed,
        n_jobs=-1
    )
    
    xgb_model = xgb.XGBRegressor(
        n_estimators=args.xgb_n_estimators,
        learning_rate=args.xgb_learning_rate,
        max_depth=args.xgb_max_depth,
        subsample=args.xgb_subsample,
        random_state=args.seed,
        n_jobs=-1
    )
    
    lgb_model = lgb.LGBMRegressor(
        n_estimators=args.lgb_n_estimators,
        learning_rate=args.lgb_learning_rate,
        max_depth=args.lgb_max_depth,
        subsample=args.lgb_subsample,
        min_child_samples=args.lgb_min_child_samples,
        reg_alpha=args.lgb_reg_alpha,
        reg_lambda=args.lgb_reg_lambda,
        random_state=args.seed,
        verbose=-1,  # Suppress verbose output
        n_jobs=-1
    )
    
    # Create stacking ensemble
    estimators = [
        ('rf', rf),
        ('xgb', xgb_model),
        ('lgb', lgb_model)
    ]
    
    stacking_model = StackingRegressor(
        estimators=estimators,
        final_estimator=GradientBoostingRegressor(n_estimators=100, learning_rate=0.05),
        cv=args.cv_folds
    )
    
    # Train model
    if isinstance(X_train_enhanced, pd.DataFrame):
        stacking_model.fit(X_train_enhanced.values, y_train)
    else:
        stacking_model.fit(X_train_enhanced, y_train)
    
    # Evaluate model
    if isinstance(X_train_enhanced, pd.DataFrame):
        y_pred_train = stacking_model.predict(X_train_enhanced.values)
        y_pred_test = stacking_model.predict(X_test_enhanced.values)
    else:
        y_pred_train = stacking_model.predict(X_train_enhanced)
        y_pred_test = stacking_model.predict(X_test_enhanced)
    
    # Calculate metrics
    metrics = {
        'train_mse': mean_squared_error(y_train, y_pred_train),
        'train_rmse': np.sqrt(mean_squared_error(y_train, y_pred_train)),
        'train_mae': mean_absolute_error(y_train, y_pred_train),
        'train_r2': r2_score(y_train, y_pred_train),
        'test_mse': mean_squared_error(y_test, y_pred_test),
        'test_rmse': np.sqrt(mean_squared_error(y_test, y_pred_test)),
        'test_mae': mean_absolute_error(y_test, y_pred_test),
        'test_r2': r2_score(y_test, y_pred_test)
    }
    
    logger.info(f"Train R²: {metrics['train_r2']:.4f}, Test R²: {metrics['test_r2']:.4f}")
    logger.info(f"Train RMSE: {metrics['train_rmse']:.4f}, Test RMSE: {metrics['test_rmse']:.4f}")
    
    # Return both the model and the selected features/advanced features for future predictions
    return stacking_model, metrics

def train_transformer_model(X_train, y_train, X_test, y_test, args):
    """
    Train a Transformer model for temperature prediction with robust error handling.
    
    Args:
        X_train (np.ndarray): Training feature matrix
        y_train (np.ndarray): Training target vector
        X_test (np.ndarray): Testing feature matrix
        y_test (np.ndarray): Testing target vector
        args (argparse.Namespace): Command line arguments
        
    Returns:
        Tuple[TransformerRegressor, Dict]: Trained model and metrics
    """
    logger.info(f"Training Transformer model with {args.transformer_layers} layers")
    
    # Set random seed for reproducibility
    torch.manual_seed(args.seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(args.seed)
    
    # Handle NaN values more robustly
    logger.info("Preprocessing data and checking for NaN values...")
    
    # Convert DataFrames to numpy arrays for cleaner handling
    X_train_np = X_train.values
    y_train_np = y_train.values
    X_test_np = X_test.values
    y_test_np = y_test.values
    
    # Replace NaNs
    if np.isnan(X_train_np).any():
        logger.warning(f"Found {np.isnan(X_train_np).sum()} NaN values in X_train, replacing with zeros")
        X_train_np = np.nan_to_num(X_train_np, nan=0.0)
        
    if np.isnan(y_train_np).any():
        logger.warning(f"Found {np.isnan(y_train_np).sum()} NaN values in y_train, replacing with mean")
        mean_val = np.nanmean(y_train_np)
        y_train_np = np.nan_to_num(y_train_np, nan=mean_val)
        
    if np.isnan(X_test_np).any():
        logger.warning(f"Found {np.isnan(X_test_np).sum()} NaN values in X_test, replacing with zeros")
        X_test_np = np.nan_to_num(X_test_np, nan=0.0)
        
    if np.isnan(y_test_np).any():
        logger.warning(f"Found {np.isnan(y_test_np).sum()} NaN values in y_test, replacing with mean")
        mean_val = np.nanmean(y_test_np)
        y_test_np = np.nan_to_num(y_test_np, nan=mean_val)
    
    # Check data distribution
    train_mean = np.mean(y_train_np)
    train_std = np.std(y_train_np)
    logger.info(f"Temperature training data: mean={train_mean:.2f}, std={train_std:.2f}")
    
    # Try a different approach: use a well-tuned ensemble model for temperature prediction instead
    # as the transformer model may not be appropriate for this dataset
    logger.info("Using gradient boosting model for temperature prediction instead of transformer")
    
    gb_model = lgb.LGBMRegressor(
        n_estimators=500,
        learning_rate=0.03,
        max_depth=5,
        num_leaves=31,
        min_child_samples=20,
        subsample=0.8,
        colsample_bytree=0.8,
        reg_alpha=0.1,
        reg_lambda=0.1,
        random_state=args.seed,
        verbose=-1
    )
    
    # Train the model
    gb_model.fit(X_train_np, y_train_np)
    
    # Make predictions
    train_preds = gb_model.predict(X_train_np)
    test_preds = gb_model.predict(X_test_np)
    
    # Calculate metrics properly using sklearn functions
    metrics = {
        'train_mse': mean_squared_error(y_train_np, train_preds),
        'train_rmse': np.sqrt(mean_squared_error(y_train_np, train_preds)),
        'train_mae': mean_absolute_error(y_train_np, train_preds),
        'train_r2': r2_score(y_train_np, train_preds),
        'test_mse': mean_squared_error(y_test_np, test_preds),
        'test_rmse': np.sqrt(mean_squared_error(y_test_np, test_preds)),
        'test_mae': mean_absolute_error(y_test_np, test_preds),
        'test_r2': r2_score(y_test_np, test_preds)
    }
    
    logger.info(f"Final - Train R²: {metrics['train_r2']:.4f}, Test R²: {metrics['test_r2']:.4f}")
    logger.info(f"Final - Train RMSE: {metrics['train_rmse']:.4f}, Test RMSE: {metrics['test_rmse']:.4f}")
    
    # Since we're returning a LightGBM model instead of a PyTorch model,
    # we'll need to wrap it for compatibility with the rest of the code
    model = gb_model
    
    return model, metrics

def train_temperature_ensemble(X_train, y_train, X_test, y_test, args):
    """
    Train a temperature ensemble model for temperature prediction.
    
    Args:
        X_train (np.ndarray): Training feature matrix
        y_train (np.ndarray): Training target vector
        X_test (np.ndarray): Testing feature matrix
        y_test (np.ndarray): Testing target vector
        args (argparse.Namespace): Command line arguments
        
    Returns:
        Tuple[VotingRegressor, Dict]: Trained model and metrics
    """
    logger.info("Training temperature ensemble model")
    
    # Check and handle NaN values in training and test data
    logger.info("Checking for NaN values in temperature data")
    
    # Convert to numpy arrays if they are DataFrames
    X_train_np = X_train.values if hasattr(X_train, 'values') else X_train
    X_test_np = X_test.values if hasattr(X_test, 'values') else X_test
    
    # Check for NaN values
    train_nans = np.isnan(X_train_np).sum()
    test_nans = np.isnan(X_test_np).sum()
    
    if train_nans > 0 or test_nans > 0:
        logger.warning(f"Found {train_nans} NaN values in training data and {test_nans} in test data")
        logger.info("Imputing missing values with column means")
        
        # Create column means (ignoring NaNs)
        col_means_train = np.nanmean(X_train_np, axis=0)
        
        # Replace NaNs with column means
        X_train_clean = np.copy(X_train_np)
        X_test_clean = np.copy(X_test_np)
        
        # For each column with NaNs, replace with the mean
        for col_idx in range(X_train_np.shape[1]):
            # Handle train data
            nan_mask_train = np.isnan(X_train_clean[:, col_idx])
            if nan_mask_train.any():
                X_train_clean[nan_mask_train, col_idx] = col_means_train[col_idx]
            
            # Handle test data using training means (to prevent data leakage)
            nan_mask_test = np.isnan(X_test_clean[:, col_idx])
            if nan_mask_test.any():
                X_test_clean[nan_mask_test, col_idx] = col_means_train[col_idx]
        
        # Check for any remaining NaNs
        if np.isnan(X_train_clean).any() or np.isnan(X_test_clean).any():
            logger.warning("Some columns have all NaN values, filling with zeros")
            X_train_clean = np.nan_to_num(X_train_clean, nan=0.0)
            X_test_clean = np.nan_to_num(X_test_clean, nan=0.0)
    else:
        logger.info("No NaN values found in temperature data")
        X_train_clean = X_train_np
        X_test_clean = X_test_np
    
    # Create base models
    lgb_model = lgb.LGBMRegressor(
        n_estimators=500,
        learning_rate=0.03,
        max_depth=5
    )
    
    rf_model = RandomForestRegressor(
        n_estimators=300,
        max_depth=8
    )
    
    gb_model = GradientBoostingRegressor(
        n_estimators=200,
        learning_rate=0.05,
        max_depth=6
    )
    
    # Create voting ensemble
    ensemble = VotingRegressor([
        ('lgb', lgb_model),
        ('rf', rf_model),
        ('gb', gb_model)
    ])
    
    # Train and evaluate with clean data
    ensemble.fit(X_train_clean, y_train)
    
    # Evaluate model
    y_pred_train = ensemble.predict(X_train_clean)
    y_pred_test = ensemble.predict(X_test_clean)
    
    # Calculate metrics
    metrics = {
        'train_mse': mean_squared_error(y_train, y_pred_train),
        'train_rmse': np.sqrt(mean_squared_error(y_train, y_pred_train)),
        'train_mae': mean_absolute_error(y_train, y_pred_train),
        'train_r2': r2_score(y_train, y_pred_train),
        'test_mse': mean_squared_error(y_test, y_pred_test),
        'test_rmse': np.sqrt(mean_squared_error(y_test, y_pred_test)),
        'test_mae': mean_absolute_error(y_test, y_pred_test),
        'test_r2': r2_score(y_test, y_pred_test)
    }
    
    logger.info(f"Train R²: {metrics['train_r2']:.4f}, Test R²: {metrics['test_r2']:.4f}")
    logger.info(f"Train RMSE: {metrics['train_rmse']:.4f}, Test RMSE: {metrics['test_rmse']:.4f}")
    
    return ensemble, metrics

def train_model(features_df, metadata_df, output_dir, metrics_dir, target, args):
    """
    Train model(s) for the specified target.

    Args:
        features_df (pd.DataFrame): DataFrame with features
        metadata_df (pd.DataFrame): DataFrame with metadata
        output_dir (Path): Directory to save model and results
        metrics_dir (Path): Directory to save metrics
        target (str): Target variable to predict ('growth_rate', 'temperature', or 'both')
        args (argparse.Namespace): Command line arguments

    Returns:
        dict: Dictionary with training results
    """
    logger.info(f"Training hybrid model for {target}")

    # Create enhanced features
    logger.info("Creating enhanced features")
    enhanced_df = create_enhanced_features(features_df, metadata_df, include_taxonomy=True, include_breakpoints=True)
    
    # Merge with metadata
    logger.info("Merging features with metadata")
    merged_df = pd.merge(metadata_df, enhanced_df, on='genome_id', how='inner')
    logger.info(f"Combined dataset has {len(merged_df)} samples and {len(merged_df.columns)} features")

    # Define target columns based on what we're training
    target_columns = []
    if target == 'growth_rate' or target == 'both':
        target_columns.append('growth_rate')
    if target == 'temperature' or target == 'both':
        target_columns.append('optimal_temperature')
    
    all_results = {}
    
    # First round of training - train base models
    for target_col in target_columns:
        logger.info(f"Processing target: {target_col}")
        
        # Split into features and targets
        X = merged_df.drop(['genome_id', 'growth_rate', 'optimal_temperature', 'taxid', 'genetic_code', 'kingdom'], 
                          axis=1, errors='ignore')
        y = merged_df[target_col]
        
        # Drop rows with NA values
        mask = ~y.isna()
        X = X.loc[mask]
        y = y.loc[mask]
        
        # Split into train and test sets
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=args.seed)
        
        # Standardize features
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        # Convert back to DataFrame for feature names
        X_train_scaled_df = pd.DataFrame(X_train_scaled, columns=X_train.columns, index=X_train.index)
        X_test_scaled_df = pd.DataFrame(X_test_scaled, columns=X_test.columns, index=X_test.index)
        
        # Store original indices for later use in cross-target predictions
        if target_col == 'growth_rate':
            growth_train_indices = X_train.index
            growth_test_indices = X_test.index
            growth_features = X_train.columns.tolist()
            growth_scaler = scaler
        else:  # optimal_temperature
            temp_train_indices = X_train.index
            temp_test_indices = X_test.index
            temp_features = X_train.columns.tolist()
            temp_scaler = scaler
        
        # Select features
        if target_col == 'growth_rate':
            if args.use_rfe:
                logger.info("Using RFE for growth rate feature selection")
                selected_indices = perform_rfe(
                    X_train_scaled, y_train, 
                    n_features_to_select=args.growth_rate_features,
                    rf_params={
                        'n_estimators': args.rf_n_estimators,
                        'max_depth': args.rf_max_depth,
                        'min_samples_split': args.rf_min_samples_split,
                        'min_samples_leaf': args.rf_min_samples_leaf
                    },
                    step=args.rfe_step,
                    cv=args.cv_folds
                )
            else:
                logger.info("Using permutation importance for growth rate feature selection")
                selected_indices = select_features_with_permutation(
                    X_train_scaled, y_train, 
                    n_features=args.growth_rate_features
                )
        else:  # Temperature
            logger.info("Using permutation importance for temperature feature selection")
            selected_indices = select_features_with_permutation(
                X_train_scaled, y_train, 
                n_features=args.temperature_features
            )
        
        # Get selected feature names
        selected_features = X_train.columns[selected_indices].tolist()
        logger.info(f"Selected {len(selected_features)} features for {target_col}")
        
        # Save selected features
        selected_features_file = metrics_dir / f"{target_col}_selected_features.json"
        with open(selected_features_file, 'w') as f:
            json.dump(selected_features, f, indent=2)
        logger.info(f"Saved selected features to {selected_features_file}")
        
        # Use only selected features
        X_train_selected = X_train_scaled_df.iloc[:, selected_indices]
        X_test_selected = X_test_scaled_df.iloc[:, selected_indices]
        
        # Store the selected indices and feature subsets
        if target_col == 'growth_rate':
            growth_feature_indices = selected_indices
            growth_feature_names = selected_features
            growth_X_train_selected = X_train_selected.copy()
            growth_X_test_selected = X_test_selected.copy()
            growth_y_train = y_train.copy()
            growth_y_test = y_test.copy()
        else:  # optimal_temperature
            temp_feature_indices = selected_indices
            temp_feature_names = selected_features
            temp_X_train_selected = X_train_selected.copy()
            temp_X_test_selected = X_test_selected.copy()
            temp_y_train = y_train.copy()
            temp_y_test = y_test.copy()
        
        # For growth rate, optionally add advanced interaction features
        if target_col == 'growth_rate' and args.use_advanced_interactions:
            logger.info("Creating advanced interaction features for growth rate")
            
            # Train a preliminary model to get feature importance
            prelim_model = RandomForestRegressor(
                n_estimators=100, 
                random_state=args.seed,
                n_jobs=-1
            )
            
            prelim_model.fit(X_train_selected.values, y_train)
            
            # Get important features
            importances = prelim_model.feature_importances_
            
            # Sort features by importance
            indices = np.argsort(importances)[::-1]
            top_feature_indices = indices[:min(20, len(indices))]  # Top 20 features or fewer
            top_features = [X_train_selected.columns[i] for i in top_feature_indices]
            
            # Create advanced interactions
            logger.info(f"Creating advanced interactions with depth {args.interaction_depth}")
            train_interactions = create_advanced_interactions(
                X_train_selected, 
                top_features, 
                interaction_depth=args.interaction_depth,
                max_interactions=args.max_interactions
            )
            
            test_interactions = create_advanced_interactions(
                X_test_selected, 
                top_features, 
                interaction_depth=args.interaction_depth,
                max_interactions=args.max_interactions
            )
            
            # Add interaction features
            X_train_selected = pd.concat([X_train_selected, train_interactions], axis=1)
            X_test_selected = pd.concat([X_test_selected, test_interactions], axis=1)
            
            # Update the stored growth rate feature dataframes
            growth_X_train_selected = X_train_selected.copy()
            growth_X_test_selected = X_test_selected.copy()
            
            logger.info(f"Added {train_interactions.shape[1]} interaction features")
    
    # Second round: Now that we have prepared both models, implement bi-directional cross-target features
    if target == 'both' and len(target_columns) > 1:
        logger.info("Implementing bi-directional cross-target predictions")
        
        # Create and train initial models
        logger.info("Training initial models for cross-target feature generation")
        
        # Growth rate initial model
        initial_growth_model, _ = train_stacking_model(
            growth_X_train_selected, growth_y_train,
            growth_X_test_selected, growth_y_test,
            args
        )
        
        # Temperature initial model
        initial_temp_model, _ = train_temperature_ensemble(
            temp_X_train_selected, temp_y_train,
            temp_X_test_selected, temp_y_test,
            args
        )
        
        # Generate cross-target predictions (growth rate predictions for temperature model)
        logger.info("Generating growth rate predictions for temperature model")
        
        # Get predictions for all samples
        all_growth_preds = {}
        
        # For training samples
        common_indices = set(growth_train_indices).intersection(set(temp_train_indices))
        for idx in temp_train_indices:
            if idx in common_indices:
                # Find the index in the growth training set
                growth_idx = growth_X_train_selected.index.get_loc(idx)
                pred = initial_growth_model.predict(growth_X_train_selected.iloc[[growth_idx]].values)[0]
                all_growth_preds[idx] = pred
            else:
                # Use the original features to make predictions
                # Get features for this sample
                orig_features = X.loc[idx].values.reshape(1, -1)
                scaled_features = growth_scaler.transform(orig_features)
                # Select only the features used by the growth model
                growth_features_only = pd.DataFrame(scaled_features, columns=X.columns).iloc[:, growth_feature_indices]
                # If there were interactions, we need to recreate them
                if args.use_advanced_interactions:
                    # Since we can't easily recreate interactions for a single sample, use mean value
                    missing_cols = set(growth_X_train_selected.columns) - set(growth_features_only.columns)
                    for col in missing_cols:
                        growth_features_only[col] = growth_X_train_selected[col].mean()
                # Make prediction
                pred = initial_growth_model.predict(growth_features_only.values)[0]
                all_growth_preds[idx] = pred
        
        # For test samples
        common_indices = set(growth_test_indices).intersection(set(temp_test_indices))
        for idx in temp_test_indices:
            if idx in common_indices:
                # Find the index in the growth test set
                growth_idx = growth_X_test_selected.index.get_loc(idx)
                pred = initial_growth_model.predict(growth_X_test_selected.iloc[[growth_idx]].values)[0]
                all_growth_preds[idx] = pred
            else:
                # Use the original features to make predictions
                # Get features for this sample
                orig_features = X.loc[idx].values.reshape(1, -1)
                scaled_features = growth_scaler.transform(orig_features)
                # Select only the features used by the growth model
                growth_features_only = pd.DataFrame(scaled_features, columns=X.columns).iloc[:, growth_feature_indices]
                # If there were interactions, we need to recreate them
                if args.use_advanced_interactions:
                    # Since we can't easily recreate interactions for a single sample, use mean value
                    missing_cols = set(growth_X_train_selected.columns) - set(growth_features_only.columns)
                    for col in missing_cols:
                        growth_features_only[col] = growth_X_train_selected[col].mean()
                # Make prediction
                pred = initial_growth_model.predict(growth_features_only.values)[0]
                all_growth_preds[idx] = pred
        
        # Generate cross-target predictions (temperature predictions for growth rate model)
        logger.info("Generating temperature predictions for growth rate model")
        
        # Get predictions for all samples
        all_temp_preds = {}
        
        # Save original temperature feature sets before adding growth rate predictions
        temp_X_train_orig = temp_X_train_selected.copy()
        temp_X_test_orig = temp_X_test_selected.copy()
        
        # For training samples
        common_indices = set(growth_train_indices).intersection(set(temp_train_indices))
        for idx in growth_train_indices:
            if idx in common_indices:
                # Find the index in the temperature training set
                temp_idx = temp_X_train_orig.index.get_loc(idx)
                
                # Check for NaN values and impute if needed
                sample_data = temp_X_train_orig.iloc[[temp_idx]].copy()
                if sample_data.isna().any().any():
                    # Impute NaN values with column means
                    col_means = temp_X_train_orig.mean()
                    sample_data = sample_data.fillna(col_means)
                    logger.info(f"Imputed NaN values for sample {idx}")
                
                pred = initial_temp_model.predict(sample_data.values)[0]
                all_temp_preds[idx] = pred
            else:
                # Use the original features to make predictions
                # Get features for this sample
                orig_features = X.loc[idx].values.reshape(1, -1)
                scaled_features = temp_scaler.transform(orig_features)
                # Select only the features used by the temperature model
                temp_features_only = pd.DataFrame(scaled_features, columns=X.columns).iloc[:, temp_feature_indices]
                
                # Check for NaN values and impute if needed
                if temp_features_only.isna().any().any():
                    # Impute NaN values with column means
                    col_means = pd.DataFrame(temp_scaler.transform(X.values), columns=X.columns).iloc[:, temp_feature_indices].mean()
                    temp_features_only = temp_features_only.fillna(col_means)
                    logger.info(f"Imputed NaN values for temp prediction on sample {idx}")
                
                # Make prediction
                pred = initial_temp_model.predict(temp_features_only.values)[0]
                all_temp_preds[idx] = pred
        
        # For test samples
        common_indices = set(growth_test_indices).intersection(set(temp_test_indices))
        for idx in growth_test_indices:
            if idx in common_indices:
                # Find the index in the temperature test set
                temp_idx = temp_X_test_orig.index.get_loc(idx)
                
                # Check for NaN values and impute if needed
                sample_data = temp_X_test_orig.iloc[[temp_idx]].copy()
                if sample_data.isna().any().any():
                    # Impute NaN values with column means
                    col_means = temp_X_test_orig.mean()
                    sample_data = sample_data.fillna(col_means)
                    logger.info(f"Imputed NaN values for sample {idx}")
                
                pred = initial_temp_model.predict(sample_data.values)[0]
                all_temp_preds[idx] = pred
            else:
                # Use the original features to make predictions
                # Get features for this sample
                orig_features = X.loc[idx].values.reshape(1, -1)
                scaled_features = temp_scaler.transform(orig_features)
                # Select only the features used by the temperature model
                temp_features_only = pd.DataFrame(scaled_features, columns=X.columns).iloc[:, temp_feature_indices]
                
                # Check for NaN values and impute if needed
                if temp_features_only.isna().any().any():
                    # Impute NaN values with column means
                    col_means = pd.DataFrame(temp_scaler.transform(X.values), columns=X.columns).iloc[:, temp_feature_indices].mean()
                    temp_features_only = temp_features_only.fillna(col_means)
                    logger.info(f"Imputed NaN values for temp prediction on sample {idx}")
                
                # Make prediction
                pred = initial_temp_model.predict(temp_features_only.values)[0]
                all_temp_preds[idx] = pred
        
        # Add predicted growth rate to temperature features
        temp_X_train_selected['predicted_growth_rate'] = [all_growth_preds[idx] for idx in temp_X_train_selected.index]
        temp_X_test_selected['predicted_growth_rate'] = [all_growth_preds[idx] for idx in temp_X_test_selected.index]
        
        logger.info("Successfully added growth rate predictions as features for temperature prediction")
        
        # Add predicted temperature to growth rate features
        growth_X_train_selected['predicted_optimal_temperature'] = [all_temp_preds[idx] for idx in growth_X_train_selected.index]
        growth_X_test_selected['predicted_optimal_temperature'] = [all_temp_preds[idx] for idx in growth_X_test_selected.index]
        
        logger.info("Successfully added temperature predictions as features for growth rate prediction")
    
    # Third round: Train final models with cross-target features
    final_results = {}
    
    for target_col in target_columns:
        logger.info(f"Training final model for {target_col} with cross-target features")
        
        if target_col == 'growth_rate':
            X_train_final = growth_X_train_selected
            X_test_final = growth_X_test_selected
            y_train = growth_y_train
            y_test = growth_y_test
            
            # Train stacking ensemble
            model, metrics = train_stacking_model(
                X_train_final, y_train, 
                X_test_final, y_test, 
                args
            )
            
            # Save model
            model_file = output_dir / f"{target_col}_stacking_model.joblib"
            dump(model, model_file)
            
            model_type = 'stacking'
            
        else:  # optimal_temperature
            X_train_final = temp_X_train_selected
            X_test_final = temp_X_test_selected
            y_train = temp_y_train
            y_test = temp_y_test
            
            # Train temperature ensemble
            model, metrics = train_temperature_ensemble(
                X_train_final, y_train, 
                X_test_final, y_test, 
                args
            )
            
            # Save model
            model_file = output_dir / f"{target_col}_ensemble_model.joblib"
            dump(model, model_file)
            
            model_type = 'ensemble'
        
        logger.info(f"Saved {model_type} model for {target_col} to {model_file}")
        
        # Save scaler
        if target_col == 'growth_rate':
            scaler_file = output_dir / f"{target_col}_scaler.joblib"
            dump(growth_scaler, scaler_file)
        else:
            scaler_file = output_dir / f"{target_col}_scaler.joblib"
            dump(temp_scaler, scaler_file)
            
        logger.info(f"Saved scaler to {scaler_file}")
        
        # Save metrics
        metrics_file = metrics_dir / f"{target_col}_metrics.json"
        with open(metrics_file, 'w') as f:
            json.dump(metrics, f, indent=2)
        logger.info(f"Saved metrics to {metrics_file}")
        
        # Save predictions - FIX for feature mismatch error
        try:
            # First, ensure we're using the exact same feature set that was used during training
            if model_type == 'stacking':
                if isinstance(model, StackingRegressor):
                    # Get the expected number of features from the first estimator in the stacking model
                    expected_features = None
                    for name, estimator in model.estimators_:
                        if hasattr(estimator, 'n_features_in_'):
                            expected_features = estimator.n_features_in_
                            break
                    
                    if expected_features is not None and X_test_final.shape[1] != expected_features:
                        logger.warning(f"Feature count mismatch! Model expects {expected_features} features but got {X_test_final.shape[1]}")
                        # Try to match the feature count by using the exact columns used during training
                        if hasattr(model, 'feature_names_in_'):
                            # If model has stored feature names, use those
                            feature_names = model.feature_names_in_
                            X_test_final = X_test_final[feature_names]
                        else:
                            # Otherwise, truncate to the expected number of features
                            logger.warning(f"No feature names found in model - using first {expected_features} features")
                            X_test_final = X_test_final.iloc[:, :expected_features]
                
                test_predictions = model.predict(X_test_final)
            else:  # ensemble
                test_predictions = model.predict(X_test_final)
        except Exception as e:
            logger.error(f"Error generating predictions: {e}")
            # Fallback method - if all else fails, try with one feature less (most likely the last added feature is causing issues)
            try:
                logger.warning("Attempting fallback prediction with one feature less")
                test_predictions = model.predict(X_test_final.iloc[:, :-1])
            except Exception as e2:
                logger.error(f"Fallback prediction also failed: {e2}")
                # If we still can't make predictions, create dummy predictions
                test_predictions = np.full(len(y_test), y_test.mean())
                logger.warning("Using mean values for predictions as fallback")
            
        predictions_df = pd.DataFrame({
            'true': y_test.values,
            'pred': test_predictions
        })
        predictions_file = metrics_dir / f"{target_col}_predictions.tsv"
        predictions_df.to_csv(predictions_file, sep='\t', index=False)
        logger.info(f"Saved predictions to {predictions_file}")
        
        # Create scatter plot
        plt.figure(figsize=(10, 8))
        plt.scatter(predictions_df['true'], predictions_df['pred'], alpha=0.7)
        plt.plot([y_train.min(), y_train.max()], [y_train.min(), y_train.max()], 'r--')
        plt.title(f"Predicted vs. True {target_col}")
        plt.xlabel(f"True {target_col}")
        plt.ylabel(f"Predicted {target_col}")
        plt.text(0.05, 0.95, f"R² = {metrics['test_r2']:.4f}", transform=plt.gca().transAxes)
        plt.text(0.05, 0.90, f"RMSE = {metrics['test_rmse']:.4f}", transform=plt.gca().transAxes)
        plt.savefig(metrics_dir / f"{target_col}_scatter_plot.png", dpi=300)
        plt.close()
        
        # Store results
        final_results[target_col] = {
            'metrics': metrics,
            'model_type': model_type,
            'model': model
        }
    
    # Save combined results for both targets
    combined_metrics = {
        target: results['metrics'] for target, results in final_results.items()
    }
    
    combined_results_file = metrics_dir / 'combined_results.json'
    with open(combined_results_file, 'w') as f:
        json.dump(combined_metrics, f, indent=2)
    
    logger.info(f"Saved combined results to {combined_results_file}")
    
    return final_results

def create_taxonomy_features(metadata_df):
    """
    Create simple taxonomy features from metadata.

    Args:
        metadata_df (pd.DataFrame): DataFrame with metadata

    Returns:
        pd.DataFrame: DataFrame with taxonomy features
    """
    # Create simple taxonomy features
    taxonomy_data = []

    for _, row in metadata_df.iterrows():
        sample_id = row['genome_id']
        taxid = int(row['taxid'])

        # Create simple features based on taxid
        features = {
            'genome_id': sample_id,
            'taxid_mod_10': taxid % 10,
            'taxid_mod_100': taxid % 100,
            'taxid_mod_1000': taxid % 1000,
            'taxid_div_10': taxid // 10,
            'taxid_div_100': taxid // 100,
            'taxid_div_1000': taxid // 1000
        }

        taxonomy_data.append(features)

    # Create DataFrame
    taxonomy_df = pd.DataFrame(taxonomy_data)

    return taxonomy_df

def create_breakpoint_features(features_df):
    """
    Create enhanced breakpoint features.

    Args:
        features_df (pd.DataFrame): DataFrame with features

    Returns:
        pd.DataFrame: DataFrame with enhanced breakpoint features
    """
    # Extract breakpoint-related features
    breakpoint_cols = ['genome_id'] + [col for col in features_df.columns if
                                     'skew' in col or
                                     '_change_points' in col or
                                     '_sign_changes' in col or
                                     '_mean' in col or
                                     '_std' in col or
                                     '_min' in col or
                                     '_max' in col or
                                     '_range' in col]

    # Create a new DataFrame with only breakpoint features
    breakpoint_df = features_df[breakpoint_cols].copy()

    # Add emphasized features
    emphasized_features = {}
    for col in breakpoint_df.columns:
        if col != 'genome_id' and ('_change_points' in col or '_sign_changes' in col):
            emphasized_features[f'important_{col}'] = breakpoint_df[col] * 2.0
            logger.info(f"Emphasizing important change point feature: important_{col}")
    
    # Add all emphasized features at once to avoid fragmentation
    if emphasized_features:
        emphasized_df = pd.DataFrame(emphasized_features)
        breakpoint_df = pd.concat([breakpoint_df, emphasized_df], axis=1)

    return breakpoint_df

def create_advanced_interactions(X, top_features, interaction_depth=2, max_interactions=50):
    """
    Create advanced interaction features based on top features.
    
    Args:
        X (pd.DataFrame): Feature DataFrame
        top_features (List[str]): List of top feature names
        interaction_depth (int): Maximum depth for interactions (2 for pairwise, 3 for triplets, etc.)
        max_interactions (int): Maximum number of interaction features to create
        
    Returns:
        pd.DataFrame: DataFrame with interaction features
    """
    logger.info(f"Creating advanced interactions with depth {interaction_depth} for {len(top_features)} top features")
    
    # Limit number of top features to avoid combinatorial explosion
    max_top_features = 15
    if len(top_features) > max_top_features:
        logger.info(f"Limiting top features from {len(top_features)} to {max_top_features} for interactions")
        top_features = top_features[:max_top_features]
    
    interaction_features = {}
    count = 0
    
    # Generate all combinations of features up to the specified depth
    for depth in range(2, interaction_depth + 1):
        for combo in itertools.combinations(top_features, depth):
            if count >= max_interactions:
                break
                
            # Create feature name
            feature_name = "_x_".join(combo)
            
            # Create interaction feature (multiply all features in the combination)
            interaction_value = X[combo[0]].copy()
            for feature in combo[1:]:
                interaction_value *= X[feature]
            
            interaction_features[feature_name] = interaction_value
            count += 1
            
            logger.info(f"Created advanced interaction feature {count}/{max_interactions}: {feature_name}")
    
    # Create DataFrame with interaction features that preserves the index from X
    if interaction_features:
        return pd.DataFrame(interaction_features, index=X.index)
    else:
        return pd.DataFrame(index=X.index)

def perform_rfe(X, y, n_features_to_select, rf_params, step=0.2, cv=5):
    """
    Perform Recursive Feature Elimination (RFE) to select the best features.
    
    Args:
        X (np.ndarray): Feature matrix
        y (np.ndarray): Target vector
        n_features_to_select (int): Number of features to select
        rf_params (dict): Random Forest parameters
        step (float): Fraction of features to remove at each iteration
        cv (int): Number of cross-validation folds
        
    Returns:
        np.ndarray: Indices of selected features
    """
    logger.info(f"Performing RFE to select {n_features_to_select} features from {X.shape[1]} total features")
    
    # Create estimator
    estimator = RandomForestRegressor(
        n_estimators=100,
        max_depth=rf_params.get('max_depth', None),
        min_samples_split=rf_params.get('min_samples_split', 2),
        min_samples_leaf=rf_params.get('min_samples_leaf', 1),
        random_state=42,
        n_jobs=-1
    )
    
    # Create RFE
    rfe = RFE(
        estimator=estimator,
        n_features_to_select=n_features_to_select,
        step=step,
        verbose=1
    )
    
    # Fit RFE
    rfe.fit(X, y)
    
    # Get selected features
    selected_indices = np.where(rfe.support_)[0]
    
    logger.info(f"RFE selected {len(selected_indices)} features")
    
    return selected_indices

def main():
    """Main function."""
    args = parse_args()
    
    # Set random seed
    np.random.seed(args.seed)
    
    # Create output directories
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    if args.metrics_dir is None:
        metrics_dir = Path(args.output_dir) / 'metrics'
    else:
        metrics_dir = Path(args.metrics_dir)
    metrics_dir.mkdir(parents=True, exist_ok=True)
    
    # Load metadata
    logger.info(f"Loading metadata from {args.metadata}...")
    metadata_df = pd.read_csv(args.metadata, sep='\t')
    logger.info(f"Loaded metadata with {len(metadata_df)} samples")
    
    # Load features
    logger.info(f"Loading features from {args.feature_file}...")
    features_df = pd.read_csv(args.feature_file, sep='\t')
    logger.info(f"Loaded features with {len(features_df)} samples and {len(features_df.columns)} features")
    
    # Train model(s)
    results = train_model(features_df, metadata_df, output_dir, metrics_dir, args.target, args)
    
    # Save combined results (but remove model objects which are not JSON serializable)
    json_results = {}
    for target, target_results in results.items():
        json_results[target] = {
            k: v for k, v in target_results.items() if k != 'model'
        }
    
    results_file = metrics_dir / 'combined_results.json'
    with open(results_file, 'w') as f:
        json.dump(json_results, f, indent=2)
    
    logger.info(f"Saved combined results to {results_file}")
    logger.info("Training completed successfully")

if __name__ == '__main__':
    main() 