#!/usr/bin/env python3
"""
Training script for a simplified RF hybrid model with feature selection and basic interactions.

This script implements a more robust approach to feature selection and interaction
for both growth rate and optimal temperature prediction.
"""

import os
import sys
import argparse
import logging
import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union, Any
from sklearn.model_selection import train_test_split, cross_val_score, KFold
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.feature_selection import SelectFromModel
from sklearn.inspection import permutation_importance
from joblib import dump, load

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('train_rf_hybrid_simple')

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Train a simplified RF hybrid model with feature selection and basic interactions')

    # Data arguments
    parser.add_argument('--metadata', required=True, help='Path to metadata TSV file')
    parser.add_argument('--feature-file', required=True, help='Path to combined features TSV file')
    parser.add_argument('--output-dir', required=True, help='Directory to save model and results')
    parser.add_argument('--metrics-dir', default=None, help='Directory to save metrics')

    # Random Forest arguments
    parser.add_argument('--n-estimators', type=int, default=300, help='Number of trees in the Random Forest')
    parser.add_argument('--max-depth', type=int, default=None, help='Maximum depth of trees in the Random Forest')
    parser.add_argument('--min-samples-split', type=int, default=2, help='Minimum samples required to split a node in Random Forest')
    parser.add_argument('--min-samples-leaf', type=int, default=1, help='Minimum samples required in a leaf node in Random Forest')

    # Feature selection arguments
    parser.add_argument('--growth-rate-features', type=int, default=100, help='Number of features to select for growth rate prediction')
    parser.add_argument('--optimal-temperature-features', type=int, default=100, help='Number of features to select for optimal temperature prediction')
    parser.add_argument('--selection-threshold', type=str, default='median', help='Threshold for feature selection (median, mean, or float value)')

    # Training arguments
    parser.add_argument('--seed', type=int, default=42, help='Random seed')
    parser.add_argument('--target', type=str, choices=['growth_rate', 'temperature', 'both'], default='both',
                        help='Target variable to predict')
    parser.add_argument('--cv-folds', type=int, default=5, help='Number of cross-validation folds')

    return parser.parse_args()

def create_enhanced_features(features_df):
    """
    Create enhanced features including squared terms and basic interactions.

    Args:
        features_df (pd.DataFrame): DataFrame with features

    Returns:
        pd.DataFrame: DataFrame with enhanced features
    """
    # Create a copy of the input DataFrame
    enhanced_df = features_df.copy()
    
    # Identify important feature categories
    codon_cols = [col for col in features_df.columns if any(pattern in col for pattern in 
                ['CUB', 'CPB', 'CAI', 'ENC', 'HEG_', 'BG_', 'delta_', 'RSCU_',
                 'codon_bias_', 'AAAI', 'aa_bias_HEG_BP', 'MILC'])]
    
    skew_cols = [col for col in features_df.columns if any(pattern in col for pattern in 
                ['skew', '_change_points', '_sign_changes'])]
    
    pathway_cols = [col for col in features_df.columns if col.startswith('ko')]
    
    # Create squared features for important metrics
    important_cols = []
    
    # Add most important codon features
    key_codon_features = [
        'codon_divergence_HEG_BP', 'delta_ENC', 'delta_CUB', 'delta_CPB', 
        'delta_CAI', 'delta_RSCU', 'CAI_HEG', 'ENC_HEG', 'Context_bias'
    ]
    
    for col in key_codon_features:
        if col in features_df.columns:
            important_cols.append(col)
    
    # Add most important amino acid features
    key_aa_features = [
        'delta_aa_freq_S', 'delta_aa_freq_P', 'delta_aa_freq_R', 
        'HEG_aa_carbon_total', 'HEG_aa_c_arsc', 'HEG_aa_freq_S'
    ]
    
    for col in key_aa_features:
        if col in features_df.columns:
            important_cols.append(col)
    
    # Add most important pathway features
    key_pathway_features = ['ko00195', 'ko03010', 'ko03430', 'ko03420', 'ko04714']
    for col in key_pathway_features:
        if col in features_df.columns:
            important_cols.append(col)
    
    # Create squared features
    squared_features = {}
    for col in important_cols:
        squared_features[f'{col}_squared'] = enhanced_df[col] ** 2
        logger.info(f"Created squared feature: {col}_squared")
    
    # Add squared features to DataFrame
    squared_df = pd.DataFrame(squared_features)
    enhanced_df = pd.concat([enhanced_df, squared_df], axis=1)
    
    # Create interaction features between key features
    interaction_features = {}
    
    # Define pairs of features to interact
    interaction_pairs = [
        ('codon_divergence_HEG_BP', 'delta_ENC'),
        ('codon_divergence_HEG_BP', 'delta_CUB'),
        ('codon_divergence_HEG_BP', 'delta_RSCU'),
        ('codon_divergence_HEG_BP', 'Context_bias'),
        ('delta_ENC', 'delta_CUB'),
        ('delta_ENC', 'delta_RSCU'),
        ('delta_CUB', 'CAI_HEG'),
        ('delta_ENC', 'ENC_HEG'),
        ('delta_aa_freq_S', 'HEG_aa_freq_S'),
        ('HEG_aa_carbon_total', 'HEG_aa_c_arsc')
    ]
    
    # Create interactions if both features exist
    for col1, col2 in interaction_pairs:
        if col1 in enhanced_df.columns and col2 in enhanced_df.columns:
            interaction_features[f'{col1}_x_{col2}'] = enhanced_df[col1] * enhanced_df[col2]
            logger.info(f"Created interaction feature: {col1}_x_{col2}")
    
    # Add interaction features to DataFrame
    if interaction_features:
        interaction_df = pd.DataFrame(interaction_features)
        enhanced_df = pd.concat([enhanced_df, interaction_df], axis=1)
    
    return enhanced_df

def select_features(X, y, n_features=100, threshold='median'):
    """
    Select the most important features using a Random Forest.
    
    Args:
        X (np.ndarray): Feature matrix
        y (np.ndarray): Target vector
        n_features (int): Number of features to select
        threshold (str or float): Threshold for feature selection
        
    Returns:
        Tuple[np.ndarray, List[int]]: Selected feature matrix and indices
    """
    logger.info(f"Selecting {n_features} features using threshold: {threshold}")
    
    # Create and fit Random Forest
    rf = RandomForestRegressor(
        n_estimators=100,
        max_depth=None,
        min_samples_split=2,
        min_samples_leaf=1,
        random_state=42,
        n_jobs=-1
    )
    
    rf.fit(X, y)
    
    # Get feature importances
    importances = rf.feature_importances_
    
    # Sort features by importance
    indices = np.argsort(importances)[::-1]
    
    # Select top features
    selected_indices = indices[:n_features]
    
    logger.info(f"Selected {len(selected_indices)} features")
    
    return selected_indices

def train_model(features_df, metadata_df, output_dir, metrics_dir, target, rf_params, args):
    """
    Train a Random Forest model for the specified target with feature selection and basic interactions.

    Args:
        features_df (pd.DataFrame): DataFrame with features
        metadata_df (pd.DataFrame): DataFrame with metadata
        output_dir (Path): Directory to save model and results
        metrics_dir (Path): Directory to save metrics
        target (str): Target variable to predict ('growth_rate' or 'optimal_temperature')
        rf_params (dict): Random Forest parameters
        args (argparse.Namespace): Command line arguments

    Returns:
        dict: Dictionary with training results
    """
    logger.info(f"Training Random Forest model for {target} with feature selection and basic interactions")

    # Create enhanced features
    logger.info("Creating enhanced features")
    enhanced_df = create_enhanced_features(features_df)
    
    # Merge with metadata
    logger.info("Merging features with metadata")
    merged_df = pd.merge(metadata_df, enhanced_df, on='genome_id', how='inner')
    logger.info(f"Combined dataset has {len(merged_df)} samples and {len(merged_df.columns)} features")

    # Split into features and targets
    X = merged_df.drop(['genome_id', 'growth_rate', 'optimal_temperature', 'taxid', 'genetic_code', 'kingdom'], axis=1, errors='ignore')
    
    if target == 'growth_rate':
        y = merged_df['growth_rate']
        n_features_to_select = args.growth_rate_features
    else:  # temperature
        y = merged_df['optimal_temperature']
        n_features_to_select = args.optimal_temperature_features
    
    # Split into train and test sets
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    
    # Standardize features
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    # Convert back to DataFrame for feature names
    X_train_scaled_df = pd.DataFrame(X_train_scaled, columns=X_train.columns)
    X_test_scaled_df = pd.DataFrame(X_test_scaled, columns=X_test.columns)
    
    # Log statistics about standardized features
    logger.info("Feature standardization statistics:")
    for feature in X_train.columns[:5]:  # Log first 5 features
        before_mean = X_train[feature].mean()
        before_std = X_train[feature].std()
        after_mean = X_train_scaled_df[feature].mean()
        after_std = X_train_scaled_df[feature].std()
        logger.info(f"Feature {feature}: Before standardization - mean={before_mean:.4f}, std={before_std:.4f} | After - mean={after_mean:.4f}, std={after_std:.4f}")
    
    # Save scaler
    scaler_file = output_dir / f"{target}_scaler.joblib"
    dump(scaler, scaler_file)
    logger.info(f"Saved scaler to {scaler_file}")
    
    # Select features
    logger.info(f"Selecting {n_features_to_select} features for {target}")
    selected_indices = select_features(
        X_train_scaled, 
        y_train, 
        n_features=n_features_to_select, 
        threshold=args.selection_threshold
    )
    
    # Get selected feature names
    selected_features = X_train.columns[selected_indices].tolist()
    logger.info(f"Selected {len(selected_features)} features: {selected_features[:10]}...")
    
    # Save selected features
    selected_features_file = metrics_dir / f"{target}_selected_features.json"
    with open(selected_features_file, 'w') as f:
        json.dump(selected_features, f, indent=2)
    logger.info(f"Saved selected features to {selected_features_file}")
    
    # Use only selected features
    X_train_selected = X_train_scaled_df.iloc[:, selected_indices]
    X_test_selected = X_test_scaled_df.iloc[:, selected_indices]
    
    # Train final model
    logger.info(f"Training final model with {X_train_selected.shape[1]} features")
    model = RandomForestRegressor(
        n_estimators=rf_params['n_estimators'],
        max_depth=rf_params['max_depth'],
        min_samples_split=rf_params['min_samples_split'],
        min_samples_leaf=rf_params['min_samples_leaf'],
        random_state=42,
        n_jobs=-1
    )
    
    model.fit(X_train_selected, y_train)
    
    # Save model
    model_file = output_dir / f"{target}_model.joblib"
    dump(model, model_file)
    logger.info(f"Saved model to {model_file}")
    
    # Make predictions
    y_pred_train = model.predict(X_train_selected)
    y_pred_test = model.predict(X_test_selected)
    
    # Calculate metrics
    train_metrics = {
        'mse': mean_squared_error(y_train, y_pred_train),
        'rmse': np.sqrt(mean_squared_error(y_train, y_pred_train)),
        'mae': mean_absolute_error(y_train, y_pred_train),
        'r2': r2_score(y_train, y_pred_train)
    }
    
    test_metrics = {
        'mse': mean_squared_error(y_test, y_pred_test),
        'rmse': np.sqrt(mean_squared_error(y_test, y_pred_test)),
        'mae': mean_absolute_error(y_test, y_pred_test),
        'r2': r2_score(y_test, y_pred_test)
    }
    
    # Log metrics
    logger.info(f"{target} - Train: R²={train_metrics['r2']:.4f}, RMSE={train_metrics['rmse']:.4f}")
    logger.info(f"{target} - Test: R²={test_metrics['r2']:.4f}, RMSE={test_metrics['rmse']:.4f}")
    
    # Calculate feature importance
    logger.info("Calculating feature importance")
    
    # Get feature names
    feature_names = selected_features
    
    # Calculate permutation importance for more reliable importance values
    logger.info("Calculating permutation importance")
    perm_importance = permutation_importance(
        model, X_test_selected, y_test, 
        n_repeats=10, 
        random_state=42,
        n_jobs=-1
    )
    
    # Create feature importance dataframe
    importance_df = pd.DataFrame({
        'feature': feature_names,
        'importance': perm_importance.importances_mean
    }).sort_values('importance', ascending=False)
    
    # Save feature importance
    importance_file = metrics_dir / f"{target}_feature_importance.tsv"
    importance_df.to_csv(importance_file, sep='\t', index=False)
    logger.info(f"Saved feature importance to {importance_file}")
    
    # Analyze codon feature importance
    codon_features = [f for f in feature_names if any(pattern in f for pattern in
                    ['CUB', 'CPB', 'CAI', 'ENC', 'HEG_', 'BG_', 'delta_', 'RSCU_',
                     'codon_bias_', 'AAAI', 'aa_bias_HEG_BP', 'MILC'])]
    
    codon_importance_df = importance_df[importance_df['feature'].isin(codon_features)].sort_values('importance', ascending=False)
    
    # Save codon feature importance
    codon_importance_file = metrics_dir / f"{target}_codon_feature_importance.tsv"
    codon_importance_df.to_csv(codon_importance_file, sep='\t', index=False)
    logger.info(f"Saved codon feature importance to {codon_importance_file}")
    
    # Log top codon features
    logger.info(f"Top 10 codon features for {target}:")
    for i, (feature, importance) in enumerate(zip(codon_importance_df['feature'].head(10), codon_importance_df['importance'].head(10))):
        logger.info(f"{i+1}. {feature}: {importance:.6f}")
    
    # Plot feature importance
    plt.figure(figsize=(12, 8))
    sns.barplot(x='importance', y='feature', data=importance_df.head(20))
    plt.title(f"Top 20 Important Features for {target}")
    plt.tight_layout()
    plt.savefig(metrics_dir / f"{target}_feature_importance.png", dpi=300)
    plt.close()
    
    # Plot codon feature importance
    plt.figure(figsize=(12, 8))
    sns.barplot(x='importance', y='feature', data=codon_importance_df.head(20))
    plt.title(f"Top 20 Important Codon Features for {target}")
    plt.tight_layout()
    plt.savefig(metrics_dir / f"{target}_codon_feature_importance.png", dpi=300)
    plt.close()
    
    # Create scatter plot
    plt.figure(figsize=(10, 8))
    plt.scatter(y_test, y_pred_test, alpha=0.7)
    
    # Add identity line
    min_val = min(min(y_test), min(y_pred_test))
    max_val = max(max(y_test), max(y_pred_test))
    plt.plot([min_val, max_val], [min_val, max_val], 'r--')
    
    plt.title(f"Predicted vs. True {target}")
    plt.xlabel(f"True {target}")
    plt.ylabel(f"Predicted {target}")
    
    # Add metrics text
    plt.text(0.05, 0.95, f"R² = {test_metrics['r2']:.4f}", transform=plt.gca().transAxes)
    plt.text(0.05, 0.90, f"RMSE = {test_metrics['rmse']:.4f}", transform=plt.gca().transAxes)
    plt.text(0.05, 0.85, f"MAE = {test_metrics['mae']:.4f}", transform=plt.gca().transAxes)
    
    plt.tight_layout()
    plt.savefig(metrics_dir / f"{target}_scatter_plot.png", dpi=300)
    plt.close()
    
    # Save results
    results = {
        'train_metrics': train_metrics,
        'test_metrics': test_metrics,
        'feature_importance': importance_df.to_dict('records')
    }
    
    # Save results to file
    pd.DataFrame({
        'metric': ['mse', 'rmse', 'mae', 'r2'],
        'train': [train_metrics['mse'], train_metrics['rmse'], train_metrics['mae'], train_metrics['r2']],
        'test': [test_metrics['mse'], test_metrics['rmse'], test_metrics['mae'], test_metrics['r2']]
    }).to_csv(metrics_dir / f"{target}_metrics.tsv", sep='\t', index=False)
    
    # Save predictions
    pd.DataFrame({
        'true': y_test,
        'pred': y_pred_test
    }).to_csv(metrics_dir / f"{target}_predictions.tsv", sep='\t', index=False)
    
    # Save combined metrics for the training script
    combined_metrics = {
        'train_metrics': [{
            f'{target}_mse': float(train_metrics['mse']),
            f'{target}_rmse': float(train_metrics['rmse']),
            f'{target}_mae': float(train_metrics['mae']),
            f'{target}_r2': float(train_metrics['r2'])
        }],
        'val_metrics': [{
            f'{target}_mse': float(test_metrics['mse']),
            f'{target}_rmse': float(test_metrics['rmse']),
            f'{target}_mae': float(test_metrics['mae']),
            f'{target}_r2': float(test_metrics['r2']),
            f'{target}_preds': y_pred_test.tolist(),
            f'{target}_targets': y_test.tolist()
        }]
    }
    
    return combined_metrics

def main():
    """Main function."""
    args = parse_args()
    
    # Set random seed
    np.random.seed(args.seed)
    
    # Create output directories
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    if args.metrics_dir is None:
        metrics_dir = Path(args.output_dir) / 'metrics'
    else:
        metrics_dir = Path(args.metrics_dir)
    metrics_dir.mkdir(parents=True, exist_ok=True)
    
    # Load metadata
    logger.info(f"Loading metadata from {args.metadata}...")
    metadata_df = pd.read_csv(args.metadata, sep='\t')
    logger.info(f"Loaded metadata with {len(metadata_df)} samples")
    
    # Load features
    logger.info(f"Loading features from {args.feature_file}...")
    features_df = pd.read_csv(args.feature_file, sep='\t')
    logger.info(f"Loaded features with {len(features_df)} samples and {len(features_df.columns)} features")
    
    # Random Forest parameters
    rf_params = {
        'n_estimators': args.n_estimators,
        'max_depth': args.max_depth,
        'min_samples_split': args.min_samples_split,
        'min_samples_leaf': args.min_samples_leaf
    }
    
    # Train models
    all_metrics = {}
    
    if args.target in ['growth_rate', 'both']:
        logger.info("Training model for growth rate...")
        growth_metrics = train_model(features_df, metadata_df, output_dir, metrics_dir, 'growth_rate', rf_params, args)
        all_metrics.update(growth_metrics)
    
    if args.target in ['temperature', 'both']:
        logger.info("Training model for optimal temperature...")
        temp_metrics = train_model(features_df, metadata_df, output_dir, metrics_dir, 'optimal_temperature', rf_params, args)
        
        # Merge metrics
        if 'train_metrics' in all_metrics:
            all_metrics['train_metrics'][0].update(temp_metrics['train_metrics'][0])
            all_metrics['val_metrics'][0].update(temp_metrics['val_metrics'][0])
        else:
            all_metrics.update(temp_metrics)
    
    # Save combined metrics
    metrics_file = metrics_dir / 'training_metrics.json'
    with open(metrics_file, 'w') as f:
        json.dump(all_metrics, f, indent=2)
    
    logger.info(f"Saved combined metrics to {metrics_file}")
    logger.info("Training completed successfully")

if __name__ == '__main__':
    main()
