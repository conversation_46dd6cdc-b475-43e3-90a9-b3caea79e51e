#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Train and evaluate the final growth rate model.
"""

import os
import argparse
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Union, Optional
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
import matplotlib.pyplot as plt
import seaborn as sns

# Import custom modules
from enhanced_feature_selection import select_features_ensemble
from final_growth_rate_model import FinalGrowthRateModel

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_data(feature_file: str, metadata_file: str) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    Load feature and metadata files.

    Args:
        feature_file: Path to feature file
        metadata_file: Path to metadata file

    Returns:
        Tuple of (features, metadata)
    """
    logger.info(f"Loading features from {feature_file}")
    features = pd.read_csv(feature_file, sep='\t', index_col=0)

    logger.info(f"Loading metadata from {metadata_file}")
    metadata = pd.read_csv(metadata_file, sep='\t', index_col=0)

    # Fill NaN values
    features = features.fillna(0)
    metadata = metadata.fillna(0)

    # Get common indices
    common_indices = features.index.intersection(metadata.index)
    logger.info(f"Found {len(common_indices)} common samples")

    # Filter to common indices
    features = features.loc[common_indices]
    metadata = metadata.loc[common_indices]

    return features, metadata

def prepare_data(features: pd.DataFrame, metadata: pd.DataFrame,
                n_features: int = 150, output_dir: Optional[str] = None) -> Tuple[pd.DataFrame, pd.Series, pd.DataFrame, pd.Series, pd.DataFrame, pd.Series, List[str], Optional[object]]:
    """
    Prepare data for growth rate prediction with improved train/validation/test split and target scaling.

    Args:
        features: Feature DataFrame
        metadata: Metadata DataFrame
        n_features: Number of features to select
        output_dir: Directory to save outputs

    Returns:
        Tuple of (X_train, y_train, X_val, y_val, X_test, y_test, selected_features, target_scaler)
    """
    logger.info("Preparing data for growth rate prediction...")

    # Extract target
    y = metadata['growth_rate']

    # Create bins for stratification to ensure similar distributions
    # This helps with ensuring similar distributions of the target variable
    n_bins = 10
    y_binned = pd.qcut(y, n_bins, labels=False, duplicates='drop')

    # Check if we have enough samples in each bin
    bin_counts = pd.Series(y_binned).value_counts()
    min_bin_count = bin_counts.min()

    if min_bin_count < 3:
        logger.warning(f"Some bins have very few samples (min: {min_bin_count}). Using fewer bins.")
        n_bins = max(2, min(5, min_bin_count))
        y_binned = pd.qcut(y, n_bins, labels=False, duplicates='drop')

    # First split data into train+val and test sets with stratification
    X_train_val, X_test, y_train_val, y_test, y_binned_train_val, _ = train_test_split(
        features, y, y_binned, test_size=0.2, random_state=42, shuffle=True, stratify=y_binned
    )

    # Then split train+val into train and validation sets with stratification
    X_train, X_val, y_train, y_val = train_test_split(
        X_train_val, y_train_val, test_size=0.25, random_state=42, shuffle=True, stratify=y_binned_train_val
    )

    logger.info(f"Data split: Train={X_train.shape[0]} samples, Validation={X_val.shape[0]} samples, Test={X_test.shape[0]} samples")

    # Check distribution of target variable in each split
    train_mean, train_std = y_train.mean(), y_train.std()
    val_mean, val_std = y_val.mean(), y_val.std()
    test_mean, test_std = y_test.mean(), y_test.std()

    logger.info(f"Target distribution - Train: mean={train_mean:.4f}, std={train_std:.4f}")
    logger.info(f"Target distribution - Validation: mean={val_mean:.4f}, std={val_std:.4f}")
    logger.info(f"Target distribution - Test: mean={test_mean:.4f}, std={test_std:.4f}")

    # Check for significant differences in distributions
    if abs(train_mean - test_mean) > 0.5 * train_std or abs(val_mean - test_mean) > 0.5 * val_std:
        logger.warning("Significant difference in target distributions between splits!")
        logger.warning("This may lead to poor generalization to the test set.")

    # Apply target scaling using RobustScaler (important improvement from hybrid model)
    from sklearn.preprocessing import RobustScaler
    target_scaler = RobustScaler()

    # Reshape for scaler
    y_train_reshaped = y_train.values.reshape(-1, 1)
    y_val_reshaped = y_val.values.reshape(-1, 1)
    y_test_reshaped = y_test.values.reshape(-1, 1)

    # Fit scaler on training data only
    target_scaler.fit(y_train_reshaped)

    # Transform all sets
    y_train_scaled = target_scaler.transform(y_train_reshaped).flatten()
    y_val_scaled = target_scaler.transform(y_val_reshaped).flatten()
    y_test_scaled = target_scaler.transform(y_test_reshaped).flatten()

    # Convert back to pandas Series with original indices
    y_train = pd.Series(y_train_scaled, index=y_train.index)
    y_val = pd.Series(y_val_scaled, index=y_val.index)
    y_test = pd.Series(y_test_scaled, index=y_test.index)

    logger.info("Applied target scaling using RobustScaler")
    logger.info(f"Scaled target distribution - Train: mean={y_train.mean():.4f}, std={y_train.std():.4f}")
    logger.info(f"Scaled target distribution - Validation: mean={y_val.mean():.4f}, std={y_val.std():.4f}")
    logger.info(f"Scaled target distribution - Test: mean={y_test.mean():.4f}, std={y_test.std():.4f}")

    # Select features using ensemble of methods
    if output_dir:
        feature_output_dir = os.path.join(output_dir, 'features')
        os.makedirs(feature_output_dir, exist_ok=True)
    else:
        feature_output_dir = None

    # Use training data for feature selection with validation data for evaluation
    selected_features = select_features_ensemble(
        X_train, y_train, n_features, feature_output_dir,
        X_val=X_val, y_val=y_val
    )

    # Filter to selected features
    X_train = X_train[selected_features]
    X_val = X_val[selected_features]
    X_test = X_test[selected_features]

    logger.info(f"Prepared data for growth rate prediction with {len(selected_features)} features")

    return X_train, y_train, X_val, y_val, X_test, y_test, selected_features, target_scaler

def train_model(X_train: pd.DataFrame, y_train: pd.Series,
               X_val: pd.DataFrame, y_val: pd.Series,
               optimize_hyperparams: bool = False,
               n_features: int = 150,
               use_interactions: bool = True,
               output_dir: Optional[str] = None) -> FinalGrowthRateModel:
    """
    Train growth rate prediction model.

    Args:
        X_train: Training features
        y_train: Training target
        X_val: Validation features
        y_val: Validation target
        optimize_hyperparams: Whether to optimize hyperparameters
        n_features: Number of features to use
        use_interactions: Whether to use interaction features
        output_dir: Directory to save outputs

    Returns:
        Trained growth rate model
    """
    logger.info("Training final growth rate model...")

    # Initialize model
    model = FinalGrowthRateModel()

    # Optimize hyperparameters if requested
    if optimize_hyperparams:
        logger.info("Optimizing hyperparameters for growth rate model...")
        model.optimize_hyperparameters(X_train, y_train, cv=5)

    # Train model
    model.fit(X_train, y_train, X_val, y_val, n_features=n_features, use_interactions=use_interactions)

    # Evaluate model
    metrics = model.evaluate(X_val, y_val)

    # Save model if output_dir is provided
    if output_dir:
        model_output_dir = os.path.join(output_dir, 'model')
        os.makedirs(model_output_dir, exist_ok=True)

        # Save model
        model.save(model_output_dir)

        # Plot feature importance
        model.plot_feature_importance(model_output_dir)

        # Plot predictions
        model.plot_predictions(X_val, y_val, model_output_dir)

    return model

def evaluate_model(model: FinalGrowthRateModel, X_test: pd.DataFrame, y_test: pd.Series,
                  output_dir: Optional[str] = None) -> Dict[str, float]:
    """
    Evaluate model on test data.

    Args:
        model: Trained growth rate model
        X_test: Test features
        y_test: Test target
        output_dir: Directory to save outputs

    Returns:
        Dictionary of evaluation metrics
    """
    logger.info("Evaluating model on test data...")

    # Evaluate model
    metrics = model.evaluate(X_test, y_test)

    # Save metrics if output_dir is provided
    if output_dir:
        metrics_df = pd.DataFrame({
            'Metric': ['R2', 'RMSE', 'MAE'],
            'Value': [metrics['R2'], metrics['RMSE'], metrics['MAE']]
        })

        metrics_df.to_csv(os.path.join(output_dir, 'test_metrics.csv'), index=False)

        # Plot predictions
        model.plot_predictions(X_test, y_test, output_dir)

    return metrics

def main():
    parser = argparse.ArgumentParser(description="Train and evaluate final growth rate model")
    parser.add_argument("--feature_file", required=True, help="Path to feature file")
    parser.add_argument("--metadata_file", required=True, help="Path to metadata file")
    parser.add_argument("--output_dir", default="models/final_growth_rate", help="Directory to save outputs")
    parser.add_argument("--n_features", type=int, default=150, help="Number of features to select")
    parser.add_argument("--optimize_hyperparams", action="store_true", help="Optimize hyperparameters")
    parser.add_argument("--use_interactions", action="store_true", help="Use interaction features")
    args = parser.parse_args()

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Load data
    features, metadata = load_data(args.feature_file, args.metadata_file)

    # Prepare data with proper train/validation/test split and target scaling
    X_train, y_train, X_val, y_val, X_test, y_test, selected_features, target_scaler = prepare_data(
        features, metadata, args.n_features, args.output_dir
    )

    # Train model using validation set for early stopping and hyperparameter tuning
    model = train_model(
        X_train, y_train, X_val, y_val,
        optimize_hyperparams=args.optimize_hyperparams,
        n_features=args.n_features,
        use_interactions=args.use_interactions,
        output_dir=args.output_dir
    )

    # Evaluate model on the test set (completely held out data)
    metrics = evaluate_model(model, X_test, y_test, args.output_dir)

    # Also evaluate on validation set for comparison
    val_metrics = model.evaluate(X_val, y_val)

    # Save the target scaler for later use
    if args.output_dir:
        import joblib
        joblib.dump(target_scaler, os.path.join(args.output_dir, 'target_scaler.joblib'))
        logger.info(f"Saved target scaler to {os.path.join(args.output_dir, 'target_scaler.joblib')}")

    # Convert metrics back to original scale for better interpretability
    if target_scaler is not None:
        # For validation metrics
        val_rmse_orig = val_metrics['RMSE'] * target_scaler.scale_[0]
        val_mae_orig = val_metrics['MAE'] * target_scaler.scale_[0]

        # For test metrics
        test_rmse_orig = metrics['RMSE'] * target_scaler.scale_[0]
        test_mae_orig = metrics['MAE'] * target_scaler.scale_[0]

        logger.info("Final growth rate model training and evaluation completed")
        logger.info(f"Validation metrics - R²: {val_metrics['R2']:.4f}, RMSE (scaled): {val_metrics['RMSE']:.4f}, MAE (scaled): {val_metrics['MAE']:.4f}")
        logger.info(f"Validation metrics (original scale) - RMSE: {val_rmse_orig:.4f}, MAE: {val_mae_orig:.4f}")
        logger.info(f"Test metrics - R²: {metrics['R2']:.4f}, RMSE (scaled): {metrics['RMSE']:.4f}, MAE (scaled): {metrics['MAE']:.4f}")
        logger.info(f"Test metrics (original scale) - RMSE: {test_rmse_orig:.4f}, MAE: {test_mae_orig:.4f}")
    else:
        logger.info("Final growth rate model training and evaluation completed")
        logger.info(f"Validation metrics - R²: {val_metrics['R2']:.4f}, RMSE: {val_metrics['RMSE']:.4f}, MAE: {val_metrics['MAE']:.4f}")
        logger.info(f"Test metrics - R²: {metrics['R2']:.4f}, RMSE: {metrics['RMSE']:.4f}, MAE: {metrics['MAE']:.4f}")

if __name__ == "__main__":
    main()
