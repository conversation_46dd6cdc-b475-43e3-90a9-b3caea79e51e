#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Predict with Best Specialized Models.
This script uses the best specialized models for each prediction target:
1. Enhanced DNN for temperature prediction (R² = 0.9548)
2. Hybrid with enhanced NN for growth rate prediction (R² = 0.9207)
"""

import os
import argparse
import logging
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import torch
import joblib
import lightgbm as lgb
from typing import Dict, Tuple, Optional, Union
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error

# Import necessary model components
from train_enhanced_dnn_temperature import RegularizedDNN

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BestModelPredictor:
    """
    Predictor that uses the best specialized models for each prediction target.
    """
    def __init__(
        self,
        temp_model_dir: str = 'models/enhanced_dnn_temperature_v2',
        growth_model_dir: str = 'models/hybrid_enhanced_nn_v1',
        transform_info_dir: Optional[str] = None
    ):
        """
        Initialize best model predictor.

        Args:
            temp_model_dir: Directory containing the temperature model
            growth_model_dir: Directory containing the growth rate model
            transform_info_dir: Directory containing transformation information
        """
        self.temp_model_dir = temp_model_dir
        self.growth_model_dir = growth_model_dir
        self.transform_info_dir = transform_info_dir

        # Load temperature model
        self.temp_model = None
        self.temp_scaler = None
        self.temp_feature_scaler = None

        # Load growth rate model
        self.growth_model = None
        self.growth_scaler = None

        # Load transformation info
        self.transform_info = None

        # Initialize models
        self._load_temp_model()
        self._load_growth_model()
        self._load_transform_info()

        logger.info("Best model predictor initialized successfully")

    def _load_temp_model(self):
        """Load temperature model."""
        logger.info(f"Loading temperature model from {self.temp_model_dir}")

        # Load feature scaler
        self.temp_feature_scaler = joblib.load(os.path.join(self.temp_model_dir, 'temperature_scaler.joblib'))

        # Load model architecture
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

        # Get input dimension from scaler
        input_dim = len(self.temp_feature_scaler.mean_)

        # Create model with the same architecture
        self.temp_model = RegularizedDNN(
            input_dim=input_dim,
            hidden_dims=[512, 512, 384, 256, 128],
            dropout_rates=[0.3, 0.4, 0.4, 0.5, 0.5],
            use_batch_norm=True,
            activation='relu'
        )

        # Load weights
        self.temp_model.load_state_dict(torch.load(
            os.path.join(self.temp_model_dir, 'temperature_model.pt'),
            map_location=device
        ))

        # Set model to evaluation mode
        self.temp_model.eval()

        # Set device
        self.device = device
        self.temp_model.to(self.device)

        logger.info("Temperature model loaded successfully")

    def _load_growth_model(self):
        """Load growth rate model."""
        logger.info(f"Loading growth rate model from {self.growth_model_dir}")

        # Load feature scaler
        self.growth_feature_scaler = joblib.load(os.path.join(self.growth_model_dir, 'feature_scaler.joblib'))

        # Load Random Forest model
        rf_dir = os.path.join(self.growth_model_dir, 'rf_models')
        self.growth_rf = joblib.load(os.path.join(rf_dir, 'rf_growth_model.joblib'))

        # Load XGBoost model
        xgb_dir = os.path.join(self.growth_model_dir, 'xgb_models')
        self.growth_xgb = joblib.load(os.path.join(xgb_dir, 'xgb_growth_model.joblib'))

        # Load LightGBM model
        lgb_dir = os.path.join(self.growth_model_dir, 'lgb_models')
        try:
            self.growth_lgb = lgb.Booster(model_file=os.path.join(lgb_dir, 'lgb_growth_model.txt'))
        except:
            try:
                # Try alternative filename
                self.growth_lgb = lgb.Booster(model_file=os.path.join(lgb_dir, 'growth_model.txt'))
            except:
                try:
                    # Try joblib format
                    self.growth_lgb = joblib.load(os.path.join(lgb_dir, 'lgb_growth_model.joblib'))
                except:
                    # Try alternative joblib filename
                    self.growth_lgb = joblib.load(os.path.join(lgb_dir, 'growth_model.joblib'))

        # Load ensemble weights
        self.growth_weights = torch.load(
            os.path.join(self.growth_model_dir, 'growth_weights.pth'),
            map_location=torch.device('cpu')
        )

        logger.info("Growth rate model loaded successfully")

    def _load_transform_info(self):
        """Load transformation information."""
        if self.transform_info_dir:
            logger.info(f"Loading transformation info from {self.transform_info_dir}")

            # Load transformation info
            self.transform_info = joblib.load(os.path.join(self.transform_info_dir, 'transform_info.joblib'))

            logger.info("Transformation info loaded successfully")

    def predict_temperature(self, X: pd.DataFrame) -> np.ndarray:
        """
        Generate temperature predictions using the specialized temperature model.

        Args:
            X: Features DataFrame

        Returns:
            Temperature predictions
        """
        # Handle older scikit-learn versions that don't have feature_names_in_
        if hasattr(self.temp_feature_scaler, 'feature_names_in_'):
            # Filter features to match training data
            common_cols = [col for col in X.columns if col in self.temp_feature_scaler.feature_names_in_]
            X_filtered = X[common_cols]
        else:
            # Use all features (assuming they're in the right order)
            X_filtered = X

        # Scale features
        X_scaled = self.temp_feature_scaler.transform(X_filtered)

        # Convert to tensor
        X_tensor = torch.tensor(X_scaled, dtype=torch.float32).to(self.device)

        # Generate predictions
        with torch.no_grad():
            temp_pred = self.temp_model(X_tensor)
            temp_pred = temp_pred.cpu().numpy()

        # Log predictions for debugging
        logger.info(f"Temperature predictions - Min: {temp_pred.min():.4f}, Max: {temp_pred.max():.4f}, Mean: {temp_pred.mean():.4f}")

        # Apply inverse transformation if available
        if self.transform_info and 'temp_scaler' in self.transform_info:
            temp_scaler = self.transform_info['temp_scaler']
            temp_pred = temp_scaler.inverse_transform(temp_pred.reshape(-1, 1)).flatten()

            # Apply inverse sqrt transformation if used
            if self.transform_info.get('temp_sqrt_transform', False):
                temp_pred = temp_pred ** 2

        return temp_pred

    def predict_growth(self, X: pd.DataFrame) -> np.ndarray:
        """
        Generate growth rate predictions using the specialized growth rate model.

        Args:
            X: Features DataFrame

        Returns:
            Growth rate predictions
        """
        # Handle older scikit-learn versions that don't have feature_names_in_
        if hasattr(self.growth_feature_scaler, 'feature_names_in_'):
            # Filter features to match training data
            common_cols = [col for col in X.columns if col in self.growth_feature_scaler.feature_names_in_]
            X_filtered = X[common_cols]
        else:
            # Use all features (assuming they're in the right order)
            X_filtered = X

        # Scale features
        X_scaled = self.growth_feature_scaler.transform(X_filtered)

        # Get predictions from Random Forest
        rf_growth_pred = self.growth_rf.predict(X_scaled)

        # Get predictions from XGBoost
        xgb_growth_pred = self.growth_xgb.predict(X_scaled)

        # Get predictions from LightGBM
        # Check if it's a Booster or a scikit-learn model
        if hasattr(self.growth_lgb, 'predict_proba'):
            # scikit-learn API
            lgb_growth_pred = self.growth_lgb.predict(X_scaled)
        elif hasattr(self.growth_lgb, 'predict'):
            # LightGBM Booster API
            if isinstance(X_scaled, pd.DataFrame):
                lgb_growth_pred = self.growth_lgb.predict(X_scaled)
            else:
                # Convert to DataFrame if needed
                lgb_growth_pred = self.growth_lgb.predict(pd.DataFrame(X_scaled, columns=X_filtered.columns))
        else:
            # Fallback
            logger.warning("Unknown LightGBM model type. Using Random Forest prediction as fallback.")
            lgb_growth_pred = rf_growth_pred

        # Combine predictions using ensemble weights
        growth_weights = self.growth_weights.cpu().numpy()

        # Log predictions for debugging
        logger.info(f"RF growth predictions - Min: {rf_growth_pred.min():.4f}, Max: {rf_growth_pred.max():.4f}, Mean: {rf_growth_pred.mean():.4f}")
        logger.info(f"XGB growth predictions - Min: {xgb_growth_pred.min():.4f}, Max: {xgb_growth_pred.max():.4f}, Mean: {xgb_growth_pred.mean():.4f}")
        logger.info(f"LGB growth predictions - Min: {lgb_growth_pred.min():.4f}, Max: {lgb_growth_pred.max():.4f}, Mean: {lgb_growth_pred.mean():.4f}")
        logger.info(f"Growth weights: RF={growth_weights[0]:.4f}, XGB={growth_weights[1]:.4f}, LGB={growth_weights[2]:.4f}")

        combined_growth_pred = (
            growth_weights[0] * rf_growth_pred +
            growth_weights[1] * xgb_growth_pred +
            growth_weights[2] * lgb_growth_pred
        )

        logger.info(f"Combined growth predictions - Min: {combined_growth_pred.min():.4f}, Max: {combined_growth_pred.max():.4f}, Mean: {combined_growth_pred.mean():.4f}")

        # Apply inverse transformation if available
        if self.transform_info and 'growth_scaler' in self.transform_info:
            growth_scaler = self.transform_info['growth_scaler']
            combined_growth_pred = growth_scaler.inverse_transform(combined_growth_pred.reshape(-1, 1)).flatten()

            # Apply inverse sqrt transformation if used
            if self.transform_info.get('growth_sqrt_transform', False):
                combined_growth_pred = combined_growth_pred ** 2

        return combined_growth_pred

    def predict(self, X: pd.DataFrame, target: str = 'both') -> Union[np.ndarray, Tuple[np.ndarray, np.ndarray]]:
        """
        Generate predictions for the specified target.

        Args:
            X: Features DataFrame
            target: Prediction target ('growth', 'temperature', or 'both')

        Returns:
            Predictions for the specified target
        """
        if target == 'growth':
            return self.predict_growth(X)
        elif target == 'temperature':
            return self.predict_temperature(X)
        elif target == 'both':
            growth_pred = self.predict_growth(X)
            temp_pred = self.predict_temperature(X)
            return growth_pred, temp_pred
        else:
            raise ValueError(f"Invalid target: {target}. Must be 'growth', 'temperature', or 'both'.")

    def evaluate(
        self,
        X: pd.DataFrame,
        y_growth: Optional[pd.Series] = None,
        y_temp: Optional[pd.Series] = None,
        target: str = 'both'
    ) -> Dict[str, Dict[str, float]]:
        """
        Evaluate model on data.

        Args:
            X: Features DataFrame
            y_growth: Growth rate targets (optional)
            y_temp: Temperature targets (optional)
            target: Prediction target ('growth', 'temperature', or 'both')

        Returns:
            Dictionary of evaluation metrics for each task
        """
        results = {}

        if target in ['growth', 'both'] and y_growth is not None:
            # Generate growth rate predictions
            y_growth_pred = self.predict_growth(X)

            # Calculate metrics for growth rate
            growth_metrics = {
                'R2': r2_score(y_growth, y_growth_pred),
                'RMSE': np.sqrt(mean_squared_error(y_growth, y_growth_pred)),
                'MAE': mean_absolute_error(y_growth, y_growth_pred)
            }

            # Calculate metrics on log scale for better evaluation
            log_r2 = r2_score(np.log1p(y_growth), np.log1p(y_growth_pred))
            log_rmse = np.sqrt(mean_squared_error(np.log1p(y_growth), np.log1p(y_growth_pred)))
            log_mae = mean_absolute_error(np.log1p(y_growth), np.log1p(y_growth_pred))

            growth_metrics.update({
                'Log_R2': log_r2,
                'Log_RMSE': log_rmse,
                'Log_MAE': log_mae
            })

            results['growth_rate'] = growth_metrics

            logger.info(f"Growth Rate: R²={growth_metrics['R2']:.4f}, RMSE={growth_metrics['RMSE']:.4f}, MAE={growth_metrics['MAE']:.4f}")
            logger.info(f"Growth Rate (log scale): R²={growth_metrics['Log_R2']:.4f}, RMSE={growth_metrics['Log_RMSE']:.4f}, MAE={growth_metrics['Log_MAE']:.4f}")

        if target in ['temperature', 'both'] and y_temp is not None:
            # Generate temperature predictions
            y_temp_pred = self.predict_temperature(X)

            # Calculate metrics for temperature
            temp_metrics = {
                'R2': r2_score(y_temp, y_temp_pred),
                'RMSE': np.sqrt(mean_squared_error(y_temp, y_temp_pred)),
                'MAE': mean_absolute_error(y_temp, y_temp_pred)
            }

            results['temperature'] = temp_metrics

            logger.info(f"Temperature: R²={temp_metrics['R2']:.4f}, RMSE={temp_metrics['RMSE']:.4f}, MAE={temp_metrics['MAE']:.4f}")

        if target == 'both' and 'growth_rate' in results and 'temperature' in results:
            # Calculate overall metrics (average R2)
            overall_metrics = {
                'R2': (results['growth_rate']['R2'] + results['temperature']['R2']) / 2,
                'RMSE': (results['growth_rate']['RMSE'] + results['temperature']['RMSE']) / 2,
                'MAE': (results['growth_rate']['MAE'] + results['temperature']['MAE']) / 2
            }

            results['overall'] = overall_metrics

            logger.info(f"Overall: R²={overall_metrics['R2']:.4f}, RMSE={overall_metrics['RMSE']:.4f}, MAE={overall_metrics['MAE']:.4f}")

        return results

def load_data(feature_file: str, metadata_file: str, filter_outliers: bool = True, percentile: float = 99.0) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    Load feature and metadata files.

    Args:
        feature_file: Path to feature file
        metadata_file: Path to metadata file
        filter_outliers: Whether to filter outliers in growth rate
        percentile: Percentile threshold for filtering outliers

    Returns:
        Tuple of (features, metadata)
    """
    logger.info(f"Loading features from {feature_file}")
    features = pd.read_csv(feature_file, sep='\t', index_col='genome_id')

    logger.info(f"Loading metadata from {metadata_file}")
    metadata = pd.read_csv(metadata_file, sep='\t', index_col='genome_id')

    # Get common samples
    common_indices = features.index.intersection(metadata.index)
    logger.info(f"Found {len(common_indices)} common samples")

    # Filter to common samples
    features = features.loc[common_indices]
    metadata = metadata.loc[common_indices]

    # Check for NaN values in features
    nan_count = features.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in features. Filling with 0.")
        features = features.fillna(0)

    # Check for infinite values
    inf_count = np.isinf(features.values).sum()
    if inf_count > 0:
        logger.warning(f"Found {inf_count} infinite values in features. Replacing with max/min values.")
        features = features.replace([np.inf, -np.inf], np.nan)
        features = features.fillna(features.max())

    # Check for NaN values in metadata
    nan_count = metadata.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in metadata. Filling with 0.")
        metadata = metadata.fillna(0)

    # Filter outliers in growth rate if requested
    if filter_outliers:
        threshold = np.percentile(metadata['growth_rate'], percentile)
        logger.info(f"Filtering growth rate values above {threshold:.4f} (percentile {percentile})")

        # Count outliers
        outlier_count = (metadata['growth_rate'] > threshold).sum()
        logger.info(f"Found {outlier_count} outliers in growth rate")

        # Log statistics before filtering
        logger.info(f"Growth rate statistics before filtering - Min: {metadata['growth_rate'].min():.4f}, Max: {metadata['growth_rate'].max():.4f}, Mean: {metadata['growth_rate'].mean():.4f}, Median: {metadata['growth_rate'].median():.4f}")

        # Filter out outliers
        outlier_indices = metadata[metadata['growth_rate'] > threshold].index
        metadata = metadata[metadata['growth_rate'] <= threshold]
        features = features.drop(outlier_indices, errors='ignore')

        # Log statistics after filtering
        logger.info(f"Growth rate statistics after filtering - Min: {metadata['growth_rate'].min():.4f}, Max: {metadata['growth_rate'].max():.4f}, Mean: {metadata['growth_rate'].mean():.4f}, Median: {metadata['growth_rate'].median():.4f}")

    return features, metadata

def main():
    parser = argparse.ArgumentParser(description="Predict with best specialized models")
    parser.add_argument("--feature_file", required=True, help="Path to feature file")
    parser.add_argument("--metadata_file", required=True, help="Path to metadata file")
    parser.add_argument("--output_dir", default="predictions", help="Directory to save predictions")
    parser.add_argument("--temp_model_dir", default="models/enhanced_dnn_temperature_v2", help="Directory containing the temperature model")
    parser.add_argument("--growth_model_dir", default="models/hybrid_enhanced_nn_v1", help="Directory containing the growth rate model")
    parser.add_argument("--transform_info_dir", default=None, help="Directory containing transformation information")
    parser.add_argument("--target", default="both", choices=["growth", "temperature", "both"], help="Prediction target")
    parser.add_argument("--filter_outliers", action="store_true", help="Filter outliers in growth rate")
    parser.add_argument("--percentile", type=float, default=99.0, help="Percentile threshold for filtering outliers")
    args = parser.parse_args()

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Load data
    features, metadata = load_data(
        args.feature_file,
        args.metadata_file,
        filter_outliers=args.filter_outliers,
        percentile=args.percentile
    )

    # Extract targets
    y_growth = metadata['growth_rate']
    y_temp = metadata['optimal_temperature']

    # Initialize predictor
    predictor = BestModelPredictor(
        temp_model_dir=args.temp_model_dir,
        growth_model_dir=args.growth_model_dir,
        transform_info_dir=args.transform_info_dir
    )

    # Generate predictions
    if args.target == 'growth':
        y_growth_pred = predictor.predict(features, target='growth')

        # Save predictions
        pd.DataFrame({
            'genome_id': features.index,
            'actual_growth': y_growth,
            'predicted_growth': y_growth_pred
        }).to_csv(os.path.join(args.output_dir, 'growth_predictions.tsv'), sep='\t', index=False)

        # Plot predictions
        plt.figure(figsize=(10, 6))
        plt.scatter(y_growth, y_growth_pred, alpha=0.5)
        plt.plot([min(y_growth), max(y_growth)], [min(y_growth), max(y_growth)], 'r--')
        plt.xlabel('Actual Growth Rate')
        plt.ylabel('Predicted Growth Rate')
        plt.title('Growth Rate Predictions')
        plt.tight_layout()
        plt.savefig(os.path.join(args.output_dir, 'growth_predictions.png'))

        # Evaluate model
        metrics = predictor.evaluate(features, y_growth=y_growth, target='growth')

        # Save metrics
        pd.DataFrame(metrics).to_csv(os.path.join(args.output_dir, 'growth_metrics.tsv'), sep='\t')

    elif args.target == 'temperature':
        y_temp_pred = predictor.predict(features, target='temperature')

        # Save predictions
        pd.DataFrame({
            'genome_id': features.index,
            'actual_temp': y_temp,
            'predicted_temp': y_temp_pred
        }).to_csv(os.path.join(args.output_dir, 'temperature_predictions.tsv'), sep='\t', index=False)

        # Plot predictions
        plt.figure(figsize=(10, 6))
        plt.scatter(y_temp, y_temp_pred, alpha=0.5)
        plt.plot([min(y_temp), max(y_temp)], [min(y_temp), max(y_temp)], 'r--')
        plt.xlabel('Actual Temperature')
        plt.ylabel('Predicted Temperature')
        plt.title('Temperature Predictions')
        plt.tight_layout()
        plt.savefig(os.path.join(args.output_dir, 'temperature_predictions.png'))

        # Evaluate model
        metrics = predictor.evaluate(features, y_temp=y_temp, target='temperature')

        # Save metrics
        pd.DataFrame(metrics).to_csv(os.path.join(args.output_dir, 'temperature_metrics.tsv'), sep='\t')

    else:  # both
        y_growth_pred, y_temp_pred = predictor.predict(features, target='both')

        # Save predictions
        pd.DataFrame({
            'genome_id': features.index,
            'actual_growth': y_growth,
            'predicted_growth': y_growth_pred,
            'actual_temp': y_temp,
            'predicted_temp': y_temp_pred
        }).to_csv(os.path.join(args.output_dir, 'predictions.tsv'), sep='\t', index=False)

        # Plot predictions
        plt.figure(figsize=(12, 5))

        plt.subplot(1, 2, 1)
        plt.scatter(y_growth, y_growth_pred, alpha=0.5)
        plt.plot([min(y_growth), max(y_growth)], [min(y_growth), max(y_growth)], 'r--')
        plt.xlabel('Actual Growth Rate')
        plt.ylabel('Predicted Growth Rate')
        plt.title('Growth Rate Predictions')

        plt.subplot(1, 2, 2)
        plt.scatter(y_temp, y_temp_pred, alpha=0.5)
        plt.plot([min(y_temp), max(y_temp)], [min(y_temp), max(y_temp)], 'r--')
        plt.xlabel('Actual Temperature')
        plt.ylabel('Predicted Temperature')
        plt.title('Temperature Predictions')

        plt.tight_layout()
        plt.savefig(os.path.join(args.output_dir, 'predictions.png'))

        # Evaluate model
        metrics = predictor.evaluate(features, y_growth=y_growth, y_temp=y_temp, target='both')

        # Save metrics
        pd.DataFrame(metrics).to_csv(os.path.join(args.output_dir, 'metrics.tsv'), sep='\t')

    logger.info(f"Predictions saved to {args.output_dir}")

if __name__ == "__main__":
    main()
