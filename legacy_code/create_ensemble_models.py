#!/usr/bin/env python3

import os
import argparse
import numpy as np
import pandas as pd
import torch
import joblib
import optuna
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from typing import Dict, List, Tuple, Union
import logging
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import RobustScaler, StandardScaler
from optimize_dnn_growth import GrowthRateDNNModel
from optimize_dnn_temp import TemperatureDNNModel
from sklearn.feature_selection import mutual_info_regression
import time

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Set device
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

class WeightedEnsemble:
    """Weighted ensemble model combining RF and DNN predictions."""
    
    def __init__(self, models: List[object], weights: List[float] = None):
        """
        Initialize ensemble model.
        
        Args:
            models: List of models (RF and DNN)
            weights: List of weights for each model (default: equal weights)
        """
        self.models = models
        self.weights = weights if weights is not None else [1/len(models)] * len(models)
        
        if len(self.weights) != len(self.models):
            raise ValueError("Number of weights must match number of models")
        
        # Normalize weights
        self.weights = np.array(self.weights) / sum(self.weights)
        
    def predict(self, X: Union[pd.DataFrame, np.ndarray]) -> np.ndarray:
        """
        Make predictions using weighted average of model predictions.
        
        Args:
            X: Input features
            
        Returns:
            Weighted average of model predictions
        """
        predictions = []
        for model in self.models:
            if hasattr(model, 'predict'):
                pred = model.predict(X)
            else:
                # Handle PyTorch models
                model.eval()
                with torch.no_grad():
                    if isinstance(X, pd.DataFrame):
                        X = torch.FloatTensor(X.values)
                    elif isinstance(X, np.ndarray):
                        X = torch.FloatTensor(X)
                    pred = model(X).cpu().numpy()
            predictions.append(pred)
            
        # Weighted average
        weighted_pred = np.zeros_like(predictions[0])
        for i, pred in enumerate(predictions):
            weighted_pred += self.weights[i] * pred
            
        return weighted_pred
    
    def evaluate(self, X: Union[pd.DataFrame, np.ndarray], 
                y: Union[pd.Series, np.ndarray]) -> Dict[str, float]:
        """
        Evaluate ensemble model performance.
        
        Args:
            X: Input features
            y: True target values
            
        Returns:
            Dictionary of evaluation metrics
        """
        y_pred = self.predict(X)
        
        metrics = {
            'R2': r2_score(y, y_pred),
            'RMSE': np.sqrt(mean_squared_error(y, y_pred)),
            'MAE': mean_absolute_error(y, y_pred)
        }
        
        return metrics

def load_model(model_path: str) -> object:
    """
    Load a model from file.
    
    Args:
        model_path: Path to model file
        
    Returns:
        Loaded model
    """
    if model_path.endswith('.pkl'):
        return joblib.load(model_path)
    elif model_path.endswith('.pth'):
        # Load PyTorch model with weights_only=False to handle sklearn objects
        try:
            # First try loading with weights_only=False
            loaded_data = torch.load(model_path, weights_only=False)
        except Exception as e:
            # If that fails, try loading with weights_only=True
            loaded_data = torch.load(model_path, weights_only=True)
            
        # Check if loaded data is a state dict or a model
        if isinstance(loaded_data, dict):
            # If it's a state dict, we need to create the model first
            # For now, we'll assume it's a TemperatureDNNModel or GrowthRateDNNModel
            # based on the model path
            
            # Find the input dimension by looking for weight tensors
            input_dim = None
            for key in loaded_data.keys():
                if 'weight' in key and len(loaded_data[key].shape) == 2:
                    input_dim = loaded_data[key].shape[1]
                    break
            
            if input_dim is None:
                raise ValueError("Could not determine input dimension from state dict")
            
            # Find the output dimension of the first layer
            first_layer_dim = None
            for key in loaded_data.keys():
                if 'weight' in key and len(loaded_data[key].shape) == 2:
                    first_layer_dim = loaded_data[key].shape[0]
                    break
            
            if first_layer_dim is None:
                raise ValueError("Could not determine first layer dimension from state dict")
            
            if 'temp' in model_path.lower():
                from optimize_dnn_temp import TemperatureDNNModel
                model = TemperatureDNNModel(
                    input_dim=input_dim,
                    hidden_dims=[first_layer_dim],
                    dropout_rates=[0.1],  # Default value
                    use_batch_norm=True,
                    use_residual=True,
                    use_attention=True
                )
            else:
                from optimize_dnn_growth import GrowthRateDNNModel
                model = GrowthRateDNNModel(
                    input_dim=input_dim,
                    hidden_dims=[first_layer_dim],
                    dropout_rates=[0.1],  # Default value
                    use_batch_norm=True,
                    use_residual=True,
                    use_attention=True
                )
            
            # Load state dict
            try:
                model.load_state_dict(loaded_data)
            except Exception as e:
                logger.warning(f"Failed to load state dict directly: {e}")
                # Try to load with strict=False
                model.load_state_dict(loaded_data, strict=False)
        else:
            model = loaded_data
            
        model.eval()
        return model
    else:
        raise ValueError(f"Unsupported model file format: {model_path}")

def optimize_rf_hyperparameters(X_train: np.ndarray, y_train: np.ndarray, 
                              X_val: np.ndarray, y_val: np.ndarray,
                              task: str = 'temperature') -> Dict:
    """Optimize Random Forest hyperparameters using Optuna."""
    
    def objective(trial):
        # Define hyperparameter search space
        params = {
            'n_estimators': trial.suggest_int('n_estimators', 100, 1000),
            'max_depth': trial.suggest_int('max_depth', 5, 30),  # Reduced max depth
            'min_samples_split': trial.suggest_int('min_samples_split', 2, 10),  # Reduced max min_samples_split
            'min_samples_leaf': trial.suggest_int('min_samples_leaf', 1, 5),
            'max_features': trial.suggest_categorical('max_features', ['sqrt', 'log2']),
            'bootstrap': trial.suggest_categorical('bootstrap', [True]),  # Always use bootstrap
            'min_weight_fraction_leaf': trial.suggest_float('min_weight_fraction_leaf', 0.0, 0.1),
            'max_leaf_nodes': trial.suggest_int('max_leaf_nodes', 100, 500)  # Reduced max leaf nodes
        }
        
        # Create and train model
        model = RandomForestRegressor(**params, random_state=42, n_jobs=-1)
        model.fit(X_train, y_train)
        
        # Evaluate on validation set
        y_pred = model.predict(X_val)
        
        # For growth rate, transform predictions back to original scale
        if task == 'growth':
            y_pred = np.power(2, y_pred) - 1e-10
            y_val_orig = np.power(2, y_val) - 1e-10
            r2 = r2_score(y_val_orig, y_pred)
            mse = mean_squared_error(y_val_orig, y_pred)
        else:
            r2 = r2_score(y_val, y_pred)
            mse = mean_squared_error(y_val, y_pred)
        
        # Combine metrics (weighted combination of R² and negative MSE)
        score = 0.8 * r2 - 0.2 * mse  # Increased weight for R²
        
        return -score  # Minimize negative score
    
    # Create study
    study = optuna.create_study(direction='minimize')
    study.optimize(objective, n_trials=100)
    
    logger.info(f"Best {task} RF parameters: {study.best_params}")
    logger.info(f"Best validation score: {-study.best_value:.4f}")
    
    return study.best_params

def optimize_dnn_hyperparameters(X_train: np.ndarray, y_train: np.ndarray,
                               X_val: np.ndarray, y_val: np.ndarray,
                               task: str = 'temperature') -> Dict:
    """Optimize DNN hyperparameters using Optuna."""
    
    def objective(trial):
        # Define hyperparameter search space
        params = {
            'n_layers': trial.suggest_int('n_layers', 3, 5),  # Increased min layers
            'first_layer_dim': trial.suggest_int('first_layer_dim', 300, 1000),
            'layer_1_dim': trial.suggest_int('layer_1_dim', 200, 500),
            'dropout_0': trial.suggest_float('dropout_0', 0.2, 0.5),
            'layer_2_dim': trial.suggest_int('layer_2_dim', 100, 300),
            'dropout_1': trial.suggest_float('dropout_1', 0.2, 0.5),
            'layer_3_dim': trial.suggest_int('layer_3_dim', 50, 200),
            'dropout_2': trial.suggest_float('dropout_2', 0.2, 0.5),
            'layer_4_dim': trial.suggest_int('layer_4_dim', 25, 100),
            'dropout_3': trial.suggest_float('dropout_3', 0.2, 0.5),
            'use_batch_norm': trial.suggest_categorical('use_batch_norm', [True, False]),
            'use_residual': trial.suggest_categorical('use_residual', [True, False]),
            'use_attention': trial.suggest_categorical('use_attention', [True, False]),
            'batch_size': trial.suggest_int('batch_size', 32, 128),
            'learning_rate': trial.suggest_float('learning_rate', 1e-4, 1e-3, log=True),
            'weight_decay': trial.suggest_float('weight_decay', 1e-6, 1e-4, log=True)
        }
        
        # Create model
        if task == 'temperature':
            model = TemperatureDNNModel(
                input_dim=X_train.shape[1],
                hidden_dims=[params['first_layer_dim'], params['layer_1_dim'], params['layer_2_dim']],
                dropout_rates=[params['dropout_0'], params['dropout_1']],
                use_batch_norm=params['use_batch_norm'],
                use_residual=params['use_residual'],
                use_attention=params['use_attention']
            ).to(device)
        else:
            model = GrowthRateDNNModel(
                input_dim=X_train.shape[1],
                hidden_dims=[params['first_layer_dim'], params['layer_1_dim'], params['layer_2_dim'], 
                            params['layer_3_dim'], params['layer_4_dim']][:params['n_layers']],
                dropout_rates=[params['dropout_0'], params['dropout_1'], params['dropout_2'], 
                             params['dropout_3']][:params['n_layers']-1],
                use_batch_norm=params['use_batch_norm'],
                use_residual=params['use_residual'],
                use_attention=params['use_attention']
            ).to(device)
        
        # Train model
        optimizer = torch.optim.Adam(model.parameters(), lr=params['learning_rate'], weight_decay=params['weight_decay'])
        criterion = torch.nn.MSELoss()
        
        # Convert data to tensors
        X_train_tensor = torch.FloatTensor(X_train).to(device)
        y_train_tensor = torch.FloatTensor(y_train).to(device)
        X_val_tensor = torch.FloatTensor(X_val).to(device)
        y_val_tensor = torch.FloatTensor(y_val).to(device)
        
        # Training loop
        best_val_r2 = float('-inf')
        best_model_state = None
        patience = 15  # Increased patience
        patience_counter = 0
        
        for epoch in range(200):  # Increased max epochs
            model.train()
            optimizer.zero_grad()
            y_pred = model(X_train_tensor)
            loss = criterion(y_pred, y_train_tensor)
            loss.backward()
            optimizer.step()
            
            # Evaluate on validation set
            model.eval()
            with torch.no_grad():
                y_val_pred = model(X_val_tensor)
                if task == 'growth':
                    # Transform predictions back to original scale
                    y_val_pred_orig = np.power(2, y_val_pred.cpu().numpy().flatten()) - 1e-10
                    y_val_orig = np.power(2, y_val) - 1e-10
                    val_r2 = r2_score(y_val_orig, y_val_pred_orig)
                else:
                    val_r2 = r2_score(y_val, y_val_pred.cpu().numpy())
                
                if val_r2 > best_val_r2:
                    best_val_r2 = val_r2
                    best_model_state = model.state_dict()
                    patience_counter = 0
                else:
                    patience_counter += 1
                    
                if patience_counter >= patience:
                    break
        
        return -best_val_r2  # Minimize negative R²
    
    # Create study
    study = optuna.create_study(direction='minimize')
    study.optimize(objective, n_trials=100)
    
    logger.info(f"Best {task} DNN parameters: {study.best_params}")
    logger.info(f"Best validation R²: {-study.best_value:.4f}")
    
    return study.best_params

def create_ensemble_models(args):
    """Create ensemble models for temperature and growth rate prediction."""
    logger.info("Loading data...")
    
    # Load data
    features = pd.read_csv(args.feature_file, sep='\t', index_col=0)
    metadata = pd.read_csv(args.metadata_file, sep='\t', index_col=0)
    
    # Find common genomes
    common_genomes = list(set(features.index) & set(metadata.index))
    features = features.loc[common_genomes]
    metadata = metadata.loc[common_genomes]
    
    # Split data into train, validation, and test sets
    X_train_val, X_test, y_train_val, y_test = train_test_split(
        features, metadata, test_size=0.2, random_state=42
    )
    X_train, X_val, y_train, y_val = train_test_split(
        X_train_val, y_train_val, test_size=0.2, random_state=42
    )
    
    # Feature selection for temperature using Random Forest importance
    n_temp_features = args.n_temp_features
    logger.info(f"Using Random Forest feature importance to select {n_temp_features} features for temperature prediction")
    
    # Ensure we're working with numpy arrays
    X_train_np = X_train.values if hasattr(X_train, 'values') else X_train
    X_val_np = X_val.values if hasattr(X_val, 'values') else X_val
    X_test_np = X_test.values if hasattr(X_test, 'values') else X_test
    
    # Initialize a Random Forest for feature importance
    rf_model = RandomForestRegressor(
        n_estimators=100,
        random_state=42,
        n_jobs=-1,
        max_depth=20,  # Limit depth to prevent overfitting
        min_samples_leaf=5  # Ensure robustness
    )
    
    # Start timer
    start_time = time.time()
    
    # Fit the Random Forest
    logger.info("Training Random Forest to determine feature importance...")
    rf_model.fit(X_train_np, y_train['optimal_temperature'])
    
    # Get feature importance
    logger.info("Calculating feature importance...")
    feature_importances = rf_model.feature_importances_
    
    # Select top features based on importance
    temp_feature_indices = np.argsort(feature_importances)[-n_temp_features:]
    
    # End timer
    end_time = time.time()
    logger.info(f"Temperature feature selection completed in {end_time - start_time:.2f} seconds")
    
    # Extract selected features
    X_train_temp = X_train_np[:, temp_feature_indices]
    X_val_temp = X_val_np[:, temp_feature_indices]
    X_test_temp = X_test_np[:, temp_feature_indices]
    
    # Scale temperature features using RobustScaler (more robust to outliers)
    temp_scaler = RobustScaler()
    X_train_temp_scaled = temp_scaler.fit_transform(X_train_temp)
    X_val_temp_scaled = temp_scaler.transform(X_val_temp)
    X_test_temp_scaled = temp_scaler.transform(X_test_temp)
    
    # Feature selection for growth rate using mutual information
    n_growth_features = args.n_growth_features
    logger.info(f"Using mutual information to select {n_growth_features} features for growth rate prediction")
    
    # Use mutual information for growth rate (captures nonlinear relationships)
    mi_values = mutual_info_regression(X_train_np, y_train['growth_rate'])
    growth_feature_indices = np.argsort(mi_values)[-n_growth_features:]
    X_train_growth = X_train_np[:, growth_feature_indices]
    X_val_growth = X_val_np[:, growth_feature_indices]
    X_test_growth = X_test_np[:, growth_feature_indices]
    
    # Scale growth rate features using StandardScaler
    growth_scaler = StandardScaler()
    X_train_growth_scaled = growth_scaler.fit_transform(X_train_growth)
    X_val_growth_scaled = growth_scaler.transform(X_val_growth)
    X_test_growth_scaled = growth_scaler.transform(X_test_growth)
    
    # Transform growth rate values using log2
    y_train_growth = np.log2(y_train['growth_rate'] + 1e-10)
    y_val_growth = np.log2(y_val['growth_rate'] + 1e-10)
    y_test_growth = np.log2(y_test['growth_rate'] + 1e-10)
    
    # Store original growth rate values for evaluation
    y_val_growth_orig = y_val['growth_rate'].values
    y_test_growth_orig = y_test['growth_rate'].values
    
    # Optimize and train growth rate models
    logger.info("Optimizing growth rate models...")
    
    # Optimize RF hyperparameters
    rf_growth_params = optimize_rf_hyperparameters(
        X_train_growth_scaled, y_train_growth,
        X_val_growth_scaled, y_val_growth,
        task='growth'
    )
    
    # Train RF model with additional parameters
    rf_growth = RandomForestRegressor(
        **rf_growth_params,
        random_state=42,
        n_jobs=-1,
        oob_score=True,  # Enable out-of-bag score
        warm_start=True,  # Enable warm start for potential further training
        min_impurity_decrease=0.0,  # Allow more splits
        ccp_alpha=0.0  # No pruning
    )
    rf_growth.fit(X_train_growth_scaled, y_train_growth)
    
    # Evaluate RF model on validation set
    rf_val_pred_log = rf_growth.predict(X_val_growth_scaled)
    rf_val_pred = np.power(2, rf_val_pred_log) - 1e-10
    rf_val_r2 = r2_score(y_val_growth_orig, rf_val_pred)
    rf_val_rmse = np.sqrt(mean_squared_error(y_val_growth_orig, rf_val_pred))
    logger.info(f"RF model validation R²: {rf_val_r2:.4f}")
    logger.info(f"RF model validation RMSE: {rf_val_rmse:.4f}")
    
    # Evaluate RF model on test set
    rf_test_pred_log = rf_growth.predict(X_test_growth_scaled)
    rf_test_pred = np.power(2, rf_test_pred_log) - 1e-10
    rf_test_r2 = r2_score(y_test_growth_orig, rf_test_pred)
    rf_test_rmse = np.sqrt(mean_squared_error(y_test_growth_orig, rf_test_pred))
    logger.info(f"RF model test R²: {rf_test_r2:.4f}")
    logger.info(f"RF model test RMSE: {rf_test_rmse:.4f}")
    
    # Optimize DNN hyperparameters
    dnn_growth_params = optimize_dnn_hyperparameters(
        X_train_growth_scaled, y_train_growth,
        X_val_growth_scaled, y_val_growth,
        task='growth'
    )
    
    # Create and train DNN model
    dnn_growth = GrowthRateDNNModel(
        input_dim=X_train_growth.shape[1],
        hidden_dims=[dnn_growth_params['first_layer_dim'], 
                    dnn_growth_params['layer_1_dim'], 
                    dnn_growth_params['layer_2_dim'],
                    dnn_growth_params['layer_3_dim'],
                    dnn_growth_params['layer_4_dim']][:dnn_growth_params['n_layers']],
        dropout_rates=[dnn_growth_params['dropout_0'], 
                      dnn_growth_params['dropout_1'],
                      dnn_growth_params['dropout_2'],
                      dnn_growth_params['dropout_3']][:dnn_growth_params['n_layers']-1],
        use_batch_norm=dnn_growth_params['use_batch_norm'],
        use_residual=dnn_growth_params['use_residual'],
        use_attention=dnn_growth_params['use_attention']
    ).to(device)
    
    # Train DNN model with learning rate scheduler
    optimizer = torch.optim.Adam(
        dnn_growth.parameters(),
        lr=dnn_growth_params['learning_rate'],
        weight_decay=dnn_growth_params['weight_decay']
    )
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='max', factor=0.5, patience=5, verbose=True
    )
    criterion = torch.nn.MSELoss()
    
    # Training loop
    best_val_r2 = float('-inf')
    best_model_state = None
    patience = 15  # Increased patience
    patience_counter = 0
    
    # Convert data to tensors
    X_train_tensor = torch.FloatTensor(X_train_growth_scaled).to(device)
    y_train_tensor = torch.FloatTensor(y_train_growth.values).to(device)
    X_val_tensor = torch.FloatTensor(X_val_growth_scaled).to(device)
    y_val_tensor = torch.FloatTensor(y_val_growth.values).to(device)
    
    for epoch in range(200):  # Increased max epochs
        dnn_growth.train()
        optimizer.zero_grad()
        y_pred = dnn_growth(X_train_tensor)
        loss = criterion(y_pred, y_train_tensor)
        loss.backward()
        optimizer.step()
        
        # Evaluate on validation set
        dnn_growth.eval()
        with torch.no_grad():
            y_val_pred = dnn_growth(X_val_tensor)
            y_val_pred_orig = np.power(2, y_val_pred.cpu().numpy().flatten()) - 1e-10
            val_r2 = r2_score(y_val_growth_orig, y_val_pred_orig)
            
            # Update learning rate
            scheduler.step(val_r2)
            
            if val_r2 > best_val_r2:
                best_val_r2 = val_r2
                best_model_state = dnn_growth.state_dict()
                patience_counter = 0
            else:
                patience_counter += 1
                
            if patience_counter >= patience:
                logger.info(f"Early stopping at epoch {epoch}")
                break
    
    # Load best model state
    dnn_growth.load_state_dict(best_model_state)
    
    # Evaluate DNN model on validation set
    dnn_growth.eval()
    with torch.no_grad():
        dnn_val_pred = dnn_growth(X_val_tensor).cpu().numpy().flatten()
        dnn_val_pred_orig = np.power(2, dnn_val_pred) - 1e-10
        dnn_val_r2 = r2_score(y_val_growth_orig, dnn_val_pred_orig)
        dnn_val_rmse = np.sqrt(mean_squared_error(y_val_growth_orig, dnn_val_pred_orig))
        logger.info(f"DNN model validation R²: {dnn_val_r2:.4f}")
        logger.info(f"DNN model validation RMSE: {dnn_val_rmse:.4f}")
    
    # Evaluate DNN model on test set
    dnn_growth.eval()
    with torch.no_grad():
        dnn_test_pred = dnn_growth(torch.FloatTensor(X_test_growth_scaled).to(device)).cpu().numpy().flatten()
        dnn_test_pred_orig = np.power(2, dnn_test_pred) - 1e-10
        dnn_test_r2 = r2_score(y_test_growth_orig, dnn_test_pred_orig)
        dnn_test_rmse = np.sqrt(mean_squared_error(y_test_growth_orig, dnn_test_pred_orig))
        logger.info(f"DNN model test R²: {dnn_test_r2:.4f}")
        logger.info(f"DNN model test RMSE: {dnn_test_rmse:.4f}")
    
    # Create growth rate ensemble with optimized weights
    # Optimize ensemble weights using validation set
    def objective_weights(weights):
        ensemble_pred_log = weights[0] * rf_val_pred_log + weights[1] * dnn_val_pred
        ensemble_pred = np.power(2, ensemble_pred_log) - 1e-10
        return -r2_score(y_val_growth_orig, ensemble_pred)
    
    from scipy.optimize import minimize
    result = minimize(
        objective_weights,
        x0=[0.4, 0.6],
        bounds=[(0, 1), (0, 1)],
        constraints={'type': 'eq', 'fun': lambda x: np.sum(x) - 1}
    )
    
    optimal_weights = result.x
    logger.info(f"Optimal ensemble weights: RF={optimal_weights[0]:.2f}, DNN={optimal_weights[1]:.2f}")
    
    # Create ensemble with optimal weights
    growth_ensemble = WeightedEnsemble(
        models=[rf_growth, dnn_growth],
        weights=optimal_weights
    )
    
    # Evaluate growth rate ensemble on validation set
    val_pred_log = growth_ensemble.predict(X_val_growth_scaled)
    val_pred = np.power(2, val_pred_log) - 1e-10
    val_r2 = r2_score(y_val_growth_orig, val_pred)
    val_rmse = np.sqrt(mean_squared_error(y_val_growth_orig, val_pred))
    logger.info(f"Growth rate ensemble validation R²: {val_r2:.4f}")
    logger.info(f"Growth rate ensemble validation RMSE: {val_rmse:.4f}")
    
    # Evaluate on test set
    test_pred_log = growth_ensemble.predict(X_test_growth_scaled)
    test_pred = np.power(2, test_pred_log) - 1e-10
    test_r2 = r2_score(y_test_growth_orig, test_pred)
    test_rmse = np.sqrt(mean_squared_error(y_test_growth_orig, test_pred))
    logger.info(f"Growth rate ensemble test R²: {test_r2:.4f}")
    logger.info(f"Growth rate ensemble test RMSE: {test_rmse:.4f}")
    
    # Save growth rate models and configurations
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Save growth rate models
    joblib.dump(rf_growth, os.path.join(args.output_dir, 'growth_rf.pkl'))
    torch.save(dnn_growth.state_dict(), os.path.join(args.output_dir, 'growth_dnn.pth'))
    joblib.dump(growth_ensemble, os.path.join(args.output_dir, 'growth_ensemble.pkl'))
    
    # Save growth rate feature scaler and selected features
    joblib.dump(growth_scaler, os.path.join(args.output_dir, 'growth_feature_scaler.pkl'))
    pd.Series(X_train_growth.columns).to_csv(os.path.join(args.output_dir, 'growth_feature_names.txt'), index=False)
    
    # Save temperature feature scaler and selected features
    joblib.dump(temp_scaler, os.path.join(args.output_dir, 'temp_feature_scaler.pkl'))
    pd.Series(X_train_temp.columns).to_csv(os.path.join(args.output_dir, 'temp_feature_names.txt'), index=False)
    
    # Save hyperparameters
    hyperparams = {
        'growth_rate': {
            'rf': rf_growth_params,
            'dnn': dnn_growth_params,
            'n_features': n_growth_features,
            'ensemble_weights': optimal_weights.tolist()
        },
        'temperature': {
            'n_features': n_temp_features
        }
    }
    joblib.dump(hyperparams, os.path.join(args.output_dir, 'hyperparameters.pkl'))
    
    logger.info(f"Models and configurations saved to {args.output_dir}")

def main():
    parser = argparse.ArgumentParser(description='Create ensemble models')
    
    # Input data paths
    parser.add_argument('--feature_file', type=str, required=True,
                      help='Path to feature file')
    parser.add_argument('--metadata_file', type=str, required=True,
                      help='Path to metadata file')
    
    # Output settings
    parser.add_argument('--output_dir', type=str, required=True,
                      help='Directory to save models and configurations')
    
    # Feature selection parameters
    parser.add_argument('--n_temp_features', type=int, default=250,
                      help='Number of features to use for temperature prediction')
    parser.add_argument('--n_growth_features', type=int, default=250,
                      help='Number of features to use for growth rate prediction')
    
    args = parser.parse_args()
    create_ensemble_models(args)

if __name__ == '__main__':
    main() 