#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Predict with Direct Models.
This script directly loads the model components without using the HybridModelWithEnhancedNN class.
"""

import os
import argparse
import logging
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import torch
import joblib
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from sklearn.preprocessing import RobustScaler

# Import necessary model components
from train_enhanced_dnn_temperature import RegularizedDNN

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DirectModelsPredictor:
    """
    Predictor that directly loads the model components.
    """
    def __init__(
        self,
        temp_model_dir: str = 'models/enhanced_dnn_temperature_v2',
        growth_model_dir: str = 'models/hybrid_enhanced_nn_v1'
    ):
        """
        Initialize direct models predictor.

        Args:
            temp_model_dir: Directory containing the temperature model
            growth_model_dir: Directory containing the growth rate model
        """
        self.temp_model_dir = temp_model_dir
        self.growth_model_dir = growth_model_dir
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

        # Load models
        self._load_temp_model()
        self._load_growth_model()

        logger.info("Direct models predictor initialized successfully")

    def _load_temp_model(self):
        """Load temperature model."""
        logger.info(f"Loading temperature model from {self.temp_model_dir}")

        # Load feature scaler
        self.temp_feature_scaler = joblib.load(os.path.join(self.temp_model_dir, 'temperature_scaler.joblib'))
        logger.info(f"Temperature feature scaler loaded successfully")

        # Load model architecture
        # Get input dimension from scaler
        input_dim = len(self.temp_feature_scaler.mean_)

        # Create model with the same architecture
        self.temp_model = RegularizedDNN(
            input_dim=input_dim,
            hidden_dims=[512, 512, 384, 256, 128],
            dropout_rates=[0.3, 0.4, 0.4, 0.5, 0.5],
            use_batch_norm=True,
            activation='relu'
        )

        # Load weights
        self.temp_model.load_state_dict(torch.load(
            os.path.join(self.temp_model_dir, 'temperature_model.pt'),
            map_location=self.device
        ))

        # Set model to evaluation mode
        self.temp_model.eval()
        self.temp_model.to(self.device)

        logger.info(f"Temperature model loaded successfully")

    def _load_growth_model(self):
        """Load growth rate model components directly."""
        logger.info(f"Loading growth rate model from {self.growth_model_dir}")

        # Load feature scaler
        self.growth_feature_scaler = joblib.load(os.path.join(self.growth_model_dir, 'feature_scaler.joblib'))
        logger.info(f"Growth rate feature scaler loaded successfully")

        # Check if the model is using the new structure or the old structure
        if os.path.exists(os.path.join(self.growth_model_dir, 'growth_rf.joblib')):
            # New structure (retrained models)
            self.growth_rf = joblib.load(os.path.join(self.growth_model_dir, 'growth_rf.joblib'))
            logger.info(f"Random Forest model loaded successfully (new structure)")

            # Load XGBoost model
            self.growth_xgb = joblib.load(os.path.join(self.growth_model_dir, 'growth_xgb.joblib'))
            logger.info(f"XGBoost model loaded successfully (new structure)")

            # Load LightGBM model if available
            lgb_path = os.path.join(self.growth_model_dir, 'growth_lgb.joblib')
            if os.path.exists(lgb_path):
                self.growth_lgb = joblib.load(lgb_path)
                logger.info(f"LightGBM model loaded successfully (new structure)")
            else:
                # Use XGBoost as fallback
                self.growth_lgb = self.growth_xgb
                logger.info(f"LightGBM model not found, using XGBoost as fallback")
        else:
            # Old structure
            rf_dir = os.path.join(self.growth_model_dir, 'rf_models')
            self.growth_rf = joblib.load(os.path.join(rf_dir, 'rf_growth_model.joblib'))
            logger.info(f"Random Forest model loaded successfully (old structure)")

            # Load XGBoost model
            xgb_dir = os.path.join(self.growth_model_dir, 'xgb_models')
            self.growth_xgb = joblib.load(os.path.join(xgb_dir, 'xgb_growth_model.joblib'))
            logger.info(f"XGBoost model loaded successfully (old structure)")

            # Load LightGBM model if available
            lgb_dir = os.path.join(self.growth_model_dir, 'lgb_models')
            if os.path.exists(os.path.join(lgb_dir, 'lgb_growth_model.joblib')):
                self.growth_lgb = joblib.load(os.path.join(lgb_dir, 'lgb_growth_model.joblib'))
                logger.info(f"LightGBM model loaded successfully (old structure)")
            else:
                # Use XGBoost as fallback
                self.growth_lgb = self.growth_xgb
                logger.info(f"LightGBM model not found, using XGBoost as fallback")

            # Load neural network model if available
            nn_dir = os.path.join(self.growth_model_dir, 'nn_models')
            if os.path.exists(os.path.join(nn_dir, 'enhanced_nn_model.pth')):
                # Load NN model architecture
                nn_hyperparams = joblib.load(os.path.join(nn_dir, 'hyperparams.joblib'))

                # Try to load the model directly
                try:
                    # Create NN model
                    from train_enhanced_dnn_temperature import RegularizedDNN
                    self.growth_nn = RegularizedDNN(
                        input_dim=nn_hyperparams.get('input_dim', 150),
                        hidden_dims=nn_hyperparams.get('hidden_dims', [512, 512, 384, 256, 128]),
                        dropout_rates=nn_hyperparams.get('dropout_rates', [0.3, 0.4, 0.4, 0.5, 0.5]),
                        use_batch_norm=nn_hyperparams.get('use_batch_norm', True),
                        activation=nn_hyperparams.get('activation', 'relu')
                    )

                    # Load weights
                    self.growth_nn.load_state_dict(torch.load(
                        os.path.join(nn_dir, 'enhanced_nn_model.pth'),
                        map_location=self.device
                    ))
                except Exception as e:
                    logger.warning(f"Failed to load neural network model: {e}")
                    logger.warning("Using a custom model loader as fallback")

                    # Create a custom model for the original hybrid model
                    from torch import nn

                    class HybridNN(nn.Module):
                        def __init__(self, input_dim=112):
                            super(HybridNN, self).__init__()
                            self.feature_extractor = nn.Sequential(
                                nn.Linear(input_dim, 512),
                                nn.BatchNorm1d(512),
                                nn.ReLU(),
                                nn.Dropout(0.3),
                                nn.Linear(512, 512),
                                nn.BatchNorm1d(512),
                                nn.ReLU(),
                                nn.Dropout(0.4),
                                nn.Linear(512, 384),
                                nn.BatchNorm1d(384),
                                nn.ReLU(),
                                nn.Dropout(0.4),
                                nn.Linear(384, 256),
                                nn.BatchNorm1d(256),
                                nn.ReLU(),
                                nn.Dropout(0.5),
                                nn.Linear(256, 128),
                                nn.BatchNorm1d(128),
                                nn.ReLU(),
                                nn.Dropout(0.5)
                            )
                            self.growth_output = nn.Linear(128, 1)
                            self.temp_output = nn.Linear(128, 1)

                        def forward(self, x):
                            features = self.feature_extractor(x)
                            growth_pred = self.growth_output(features)
                            return growth_pred

                    # Create the model
                    self.growth_nn = HybridNN()

                    # Load weights
                    self.growth_nn.load_state_dict(torch.load(
                        os.path.join(nn_dir, 'enhanced_nn_model.pth'),
                        map_location=self.device
                    ))

                # Set model to evaluation mode
                self.growth_nn.eval()
                self.growth_nn.to(self.device)

                # Load NN feature scaler
                self.growth_nn_scaler = joblib.load(os.path.join(nn_dir, 'feature_scaler.joblib'))

                logger.info(f"Neural network model loaded successfully (old structure)")
                self.has_nn_model = True
            else:
                logger.info(f"Neural network model not found")
                self.has_nn_model = False

        # Load ensemble weights
        try:
            weights = torch.load(
                os.path.join(self.growth_model_dir, 'growth_weights.pth'),
                map_location=self.device
            )

            # If we have a neural network model but the weights don't include it
            if self.has_nn_model and len(weights) == 3:
                # Adjust weights to include NN (reduce other weights by 25%)
                adjusted_weights = weights * 0.75
                # Add NN weight (25% of total)
                self.growth_weights = torch.cat([adjusted_weights, torch.tensor([0.25], device=self.device)])
                logger.info(f"Adjusted ensemble weights to include NN: {self.growth_weights.cpu().numpy()}")
            else:
                self.growth_weights = weights
                logger.info(f"Ensemble weights loaded successfully: {self.growth_weights.cpu().numpy()}")
        except:
            # Try loading as joblib
            try:
                weights = joblib.load(os.path.join(self.growth_model_dir, 'growth_weights.joblib'))

                # If we have a neural network model but the weights don't include it
                if self.has_nn_model and len(weights) == 3:
                    # Convert to tensor
                    weights_tensor = torch.tensor(weights, device=self.device)
                    # Adjust weights to include NN (reduce other weights by 25%)
                    adjusted_weights = weights_tensor * 0.75
                    # Add NN weight (25% of total)
                    self.growth_weights = torch.cat([adjusted_weights, torch.tensor([0.25], device=self.device)])
                    logger.info(f"Adjusted ensemble weights to include NN: {self.growth_weights.cpu().numpy()}")
                else:
                    self.growth_weights = weights
                    logger.info(f"Ensemble weights loaded successfully (joblib): {self.growth_weights}")
            except:
                # Use adjusted weights as fallback
                if self.has_nn_model:
                    # Add a fourth weight for the neural network
                    original_weights = torch.tensor([0.33, 0.33, 0.34], device=self.device)
                    # Adjust weights to include NN (reduce other weights by 25%)
                    adjusted_weights = original_weights * 0.75
                    # Add NN weight (25% of total)
                    self.growth_weights = torch.cat([adjusted_weights, torch.tensor([0.25], device=self.device)])
                else:
                    self.growth_weights = torch.tensor([0.33, 0.33, 0.34], device=self.device)
                logger.info(f"Ensemble weights not found, using adjusted weights: {self.growth_weights.cpu().numpy()}")

        # Load transform info
        self.transform_info = joblib.load(os.path.join(self.growth_model_dir, 'transform_info.joblib'))
        logger.info(f"Transform info loaded successfully: {self.transform_info}")

        logger.info(f"Growth rate model components loaded successfully")

    def predict_temperature(self, X: pd.DataFrame) -> np.ndarray:
        """
        Generate temperature predictions.

        Args:
            X: Features DataFrame

        Returns:
            Temperature predictions
        """
        # Handle feature mismatch
        if hasattr(self.temp_feature_scaler, 'feature_names_in_'):
            # Filter features to match training data
            feature_names = self.temp_feature_scaler.feature_names_in_
            common_cols = [col for col in X.columns if col in feature_names]

            # Check if we have all required features
            if len(common_cols) < len(feature_names):
                logger.warning(f"Missing {len(feature_names) - len(common_cols)} features required by the model")
                # Use only available features
                X_filtered = X[common_cols]
                # Fill missing features with zeros
                missing_cols = [col for col in feature_names if col not in common_cols]
                for col in missing_cols:
                    X_filtered[col] = 0
                # Reorder columns to match the order expected by the model
                X_filtered = X_filtered[feature_names]
            else:
                # Use only the features the model was trained on, in the correct order
                X_filtered = X[feature_names]
        else:
            # If no feature names, check dimensions
            expected_features = self.temp_feature_scaler.mean_.shape[0]
            if X.shape[1] != expected_features:
                logger.warning(f"Feature count mismatch: got {X.shape[1]}, expected {expected_features}")
                # If too many features, truncate
                if X.shape[1] > expected_features:
                    X_filtered = X.iloc[:, :expected_features]
                else:
                    # If too few features, pad with zeros
                    X_filtered = pd.DataFrame(np.zeros((X.shape[0], expected_features)), index=X.index)
                    X_filtered.iloc[:, :X.shape[1]] = X.values
            else:
                X_filtered = X

        # Scale features
        X_scaled = self.temp_feature_scaler.transform(X_filtered.values)

        # Convert to tensor
        X_tensor = torch.tensor(X_scaled, dtype=torch.float32).to(self.device)

        # Generate predictions
        with torch.no_grad():
            temp_pred = self.temp_model(X_tensor)
            temp_pred = temp_pred.cpu().numpy().flatten()

        logger.info(f"Temperature predictions - Min: {temp_pred.min():.4f}, Max: {temp_pred.max():.4f}, Mean: {temp_pred.mean():.4f}")

        return temp_pred

    def predict_growth(self, X: pd.DataFrame) -> np.ndarray:
        """
        Generate growth rate predictions.

        Args:
            X: Features DataFrame

        Returns:
            Growth rate predictions
        """
        # Handle feature mismatch
        if hasattr(self.growth_feature_scaler, 'feature_names_in_'):
            # Filter features to match training data
            feature_names = self.growth_feature_scaler.feature_names_in_
            common_cols = [col for col in X.columns if col in feature_names]

            # Check if we have all required features
            if len(common_cols) < len(feature_names):
                logger.warning(f"Missing {len(feature_names) - len(common_cols)} features required by the model")
                # Use only available features
                X_filtered = X[common_cols]
                # Fill missing features with zeros
                missing_cols = [col for col in feature_names if col not in common_cols]
                for col in missing_cols:
                    X_filtered[col] = 0
                # Reorder columns to match the order expected by the model
                X_filtered = X_filtered[feature_names]
            else:
                # Use only the features the model was trained on, in the correct order
                X_filtered = X[feature_names]
        else:
            # If no feature names, check dimensions
            expected_features = self.growth_feature_scaler.mean_.shape[0]
            if X.shape[1] != expected_features:
                logger.warning(f"Feature count mismatch: got {X.shape[1]}, expected {expected_features}")
                # If too many features, truncate
                if X.shape[1] > expected_features:
                    X_filtered = X.iloc[:, :expected_features]
                else:
                    # If too few features, pad with zeros
                    X_filtered = pd.DataFrame(np.zeros((X.shape[0], expected_features)), index=X.index)
                    X_filtered.iloc[:, :X.shape[1]] = X.values
            else:
                X_filtered = X

        # Scale features
        X_scaled = self.growth_feature_scaler.transform(X_filtered.values)

        # Get predictions from Random Forest
        rf_pred = self.growth_rf.predict(X_scaled)
        logger.info(f"RF predictions - Min: {rf_pred.min():.4f}, Max: {rf_pred.max():.4f}, Mean: {rf_pred.mean():.4f}")

        # Get predictions from XGBoost
        xgb_pred = self.growth_xgb.predict(X_scaled)
        logger.info(f"XGB predictions - Min: {xgb_pred.min():.4f}, Max: {xgb_pred.max():.4f}, Mean: {xgb_pred.mean():.4f}")

        # Get predictions from LightGBM
        lgb_pred = self.growth_lgb.predict(X_scaled)
        logger.info(f"LGB predictions - Min: {lgb_pred.min():.4f}, Max: {lgb_pred.max():.4f}, Mean: {lgb_pred.mean():.4f}")

        # Get predictions from Neural Network if available
        if hasattr(self, 'has_nn_model') and self.has_nn_model:
            # Scale features for NN
            X_nn_scaled = self.growth_nn_scaler.transform(X_filtered.values)

            # Convert to tensor
            X_nn_tensor = torch.tensor(X_nn_scaled, dtype=torch.float32).to(self.device)

            # Generate predictions
            with torch.no_grad():
                nn_pred = self.growth_nn(X_nn_tensor)
                nn_pred = nn_pred.cpu().numpy().flatten()

            logger.info(f"NN predictions - Min: {nn_pred.min():.4f}, Max: {nn_pred.max():.4f}, Mean: {nn_pred.mean():.4f}")

        # Combine predictions using ensemble weights
        if isinstance(self.growth_weights, torch.Tensor):
            weights = torch.softmax(self.growth_weights, dim=0).cpu().numpy()
        else:
            # If weights are already numpy array
            weights = self.growth_weights / np.sum(self.growth_weights)

        if hasattr(self, 'has_nn_model') and self.has_nn_model:
            logger.info(f"Normalized weights: RF={weights[0]:.4f}, XGB={weights[1]:.4f}, LGB={weights[2]:.4f}, NN={weights[3]:.4f}")

            combined_pred = (
                weights[0] * rf_pred +
                weights[1] * xgb_pred +
                weights[2] * lgb_pred +
                weights[3] * nn_pred
            )
        else:
            logger.info(f"Normalized weights: RF={weights[0]:.4f}, XGB={weights[1]:.4f}, LGB={weights[2]:.4f}")

            combined_pred = (
                weights[0] * rf_pred +
                weights[1] * xgb_pred +
                weights[2] * lgb_pred
            )

        logger.info(f"Combined predictions (scaled) - Min: {combined_pred.min():.4f}, Max: {combined_pred.max():.4f}, Mean: {combined_pred.mean():.4f}")

        # The model was trained on sqrt-transformed and RobustScaler-transformed growth rate
        # We need to convert back to the original space

        # First, inverse transform the RobustScaler
        combined_pred_reshaped = combined_pred.reshape(-1, 1)

        # Check if transform_info contains a growth_scaler
        if 'growth_scaler' in self.transform_info:
            # Use the scaler from transform_info
            combined_pred_unscaled = self.transform_info['growth_scaler'].inverse_transform(combined_pred_reshaped).flatten()
        else:
            # Create a default scaler
            scaler = RobustScaler()
            # Fit the scaler on a range of values that might be encountered
            y_growth_sqrt = np.sqrt(np.linspace(0.01, 10, 1000)).reshape(-1, 1)
            scaler.fit(y_growth_sqrt)
            combined_pred_unscaled = scaler.inverse_transform(combined_pred_reshaped).flatten()

        logger.info(f"Combined predictions (unscaled) - Min: {combined_pred_unscaled.min():.4f}, Max: {combined_pred_unscaled.max():.4f}, Mean: {combined_pred_unscaled.mean():.4f}")

        # Then, inverse transform the sqrt transformation
        if self.transform_info.get('growth_sqrt_transform', False):
            combined_pred_orig = combined_pred_unscaled ** 2
        else:
            combined_pred_orig = combined_pred_unscaled

        # Ensure predictions are non-negative
        combined_pred_orig = np.maximum(combined_pred_orig, 0)

        logger.info(f"Final predictions - Min: {combined_pred_orig.min():.4f}, Max: {combined_pred_orig.max():.4f}, Mean: {combined_pred_orig.mean():.4f}")

        return combined_pred_orig

    def predict(self, X: pd.DataFrame, target: str = 'both'):
        """
        Generate predictions for the specified target.

        Args:
            X: Features DataFrame
            target: Prediction target ('growth', 'temperature', or 'both')

        Returns:
            Predictions for the specified target
        """
        if target == 'growth':
            return self.predict_growth(X)
        elif target == 'temperature':
            return self.predict_temperature(X)
        elif target == 'both':
            growth_pred = self.predict_growth(X)
            temp_pred = self.predict_temperature(X)
            return growth_pred, temp_pred
        else:
            raise ValueError(f"Invalid target: {target}. Must be 'growth', 'temperature', or 'both'.")

    def evaluate(self, X: pd.DataFrame, y_growth=None, y_temp=None, target: str = 'both'):
        """
        Evaluate model on data.

        Args:
            X: Features DataFrame
            y_growth: Growth rate targets (optional)
            y_temp: Temperature targets (optional)
            target: Prediction target ('growth', 'temperature', or 'both')

        Returns:
            Dictionary of evaluation metrics
        """
        results = {}

        if target in ['growth', 'both'] and y_growth is not None:
            # Generate growth rate predictions
            y_growth_pred = self.predict_growth(X)

            # Calculate metrics
            growth_metrics = {
                'R2': r2_score(y_growth, y_growth_pred),
                'RMSE': np.sqrt(mean_squared_error(y_growth, y_growth_pred)),
                'MAE': mean_absolute_error(y_growth, y_growth_pred)
            }

            results['growth_rate'] = growth_metrics

            logger.info(f"Growth Rate: R²={growth_metrics['R2']:.4f}, RMSE={growth_metrics['RMSE']:.4f}, MAE={growth_metrics['MAE']:.4f}")

        if target in ['temperature', 'both'] and y_temp is not None:
            # Generate temperature predictions
            y_temp_pred = self.predict_temperature(X)

            # Calculate metrics
            temp_metrics = {
                'R2': r2_score(y_temp, y_temp_pred),
                'RMSE': np.sqrt(mean_squared_error(y_temp, y_temp_pred)),
                'MAE': mean_absolute_error(y_temp, y_temp_pred)
            }

            results['temperature'] = temp_metrics

            logger.info(f"Temperature: R²={temp_metrics['R2']:.4f}, RMSE={temp_metrics['RMSE']:.4f}, MAE={temp_metrics['MAE']:.4f}")

        if target == 'both' and 'growth_rate' in results and 'temperature' in results:
            # Calculate overall metrics (average R2)
            overall_metrics = {
                'R2': (results['growth_rate']['R2'] + results['temperature']['R2']) / 2,
                'RMSE': (results['growth_rate']['RMSE'] + results['temperature']['RMSE']) / 2,
                'MAE': (results['growth_rate']['MAE'] + results['temperature']['MAE']) / 2
            }

            results['overall'] = overall_metrics

            logger.info(f"Overall: R²={overall_metrics['R2']:.4f}, RMSE={overall_metrics['RMSE']:.4f}, MAE={overall_metrics['MAE']:.4f}")

        return results

def load_data(feature_file: str, metadata_file: str) -> tuple:
    """
    Load feature and metadata files.

    Args:
        feature_file: Path to feature file
        metadata_file: Path to metadata file

    Returns:
        Tuple of (features, metadata)
    """
    logger.info(f"Loading features from {feature_file}")
    features = pd.read_csv(feature_file, sep='\t', index_col='genome_id')

    logger.info(f"Loading metadata from {metadata_file}")
    metadata = pd.read_csv(metadata_file, sep='\t', index_col='genome_id')

    # Get common samples
    common_indices = features.index.intersection(metadata.index)
    logger.info(f"Found {len(common_indices)} common samples")

    # Filter to common samples
    features = features.loc[common_indices]
    metadata = metadata.loc[common_indices]

    # Check for NaN values in features
    nan_count = features.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in features. Filling with 0.")
        features = features.fillna(0)

    # Check for infinite values
    inf_count = np.isinf(features.values).sum()
    if inf_count > 0:
        logger.warning(f"Found {inf_count} infinite values in features. Replacing with max/min values.")
        features = features.replace([np.inf, -np.inf], np.nan)
        features = features.fillna(features.max())

    # Check for NaN values in metadata
    nan_count = metadata.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in metadata. Filling with 0.")
        metadata = metadata.fillna(0)

    return features, metadata

def main():
    parser = argparse.ArgumentParser(description="Predict with Direct Models")
    parser.add_argument("--feature_file", required=True, help="Path to feature file")
    parser.add_argument("--metadata_file", required=True, help="Path to metadata file")
    parser.add_argument("--output_dir", default="predictions", help="Directory to save predictions")
    parser.add_argument("--temp_model_dir", default="models/enhanced_dnn_temperature_v2", help="Directory containing the temperature model")
    parser.add_argument("--growth_model_dir", default="models/hybrid_enhanced_nn_v1", help="Directory containing the growth rate model")
    parser.add_argument("--target", default="both", choices=["growth", "temperature", "both"], help="Prediction target")
    args = parser.parse_args()

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Load data
    features, metadata = load_data(args.feature_file, args.metadata_file)

    # Extract targets
    y_growth = metadata['growth_rate']
    y_temp = metadata['optimal_temperature']

    # Initialize predictor
    predictor = DirectModelsPredictor(
        temp_model_dir=args.temp_model_dir,
        growth_model_dir=args.growth_model_dir
    )

    # Generate predictions
    if args.target == 'growth':
        y_growth_pred = predictor.predict(features, target='growth')

        # Save predictions
        pd.DataFrame({
            'genome_id': features.index,
            'actual_growth': y_growth,
            'predicted_growth': y_growth_pred
        }).to_csv(os.path.join(args.output_dir, 'growth_predictions.tsv'), sep='\t', index=False)

        # Plot predictions
        plt.figure(figsize=(10, 6))
        plt.scatter(y_growth, y_growth_pred, alpha=0.5)
        plt.plot([min(y_growth), max(y_growth)], [min(y_growth), max(y_growth)], 'r--')
        plt.xlabel('Actual Growth Rate')
        plt.ylabel('Predicted Growth Rate')
        plt.title('Growth Rate Predictions')
        plt.tight_layout()
        plt.savefig(os.path.join(args.output_dir, 'growth_predictions.png'))

        # Evaluate model
        metrics = predictor.evaluate(features, y_growth=y_growth, target='growth')

        # Save metrics
        pd.DataFrame({
            'metric': list(metrics['growth_rate'].keys()),
            'value': list(metrics['growth_rate'].values())
        }).to_csv(os.path.join(args.output_dir, 'growth_metrics.tsv'), sep='\t', index=False)

    elif args.target == 'temperature':
        y_temp_pred = predictor.predict(features, target='temperature')

        # Save predictions
        pd.DataFrame({
            'genome_id': features.index,
            'actual_temp': y_temp,
            'predicted_temp': y_temp_pred
        }).to_csv(os.path.join(args.output_dir, 'temperature_predictions.tsv'), sep='\t', index=False)

        # Plot predictions
        plt.figure(figsize=(10, 6))
        plt.scatter(y_temp, y_temp_pred, alpha=0.5)
        plt.plot([min(y_temp), max(y_temp)], [min(y_temp), max(y_temp)], 'r--')
        plt.xlabel('Actual Temperature')
        plt.ylabel('Predicted Temperature')
        plt.title('Temperature Predictions')
        plt.tight_layout()
        plt.savefig(os.path.join(args.output_dir, 'temperature_predictions.png'))

        # Evaluate model
        metrics = predictor.evaluate(features, y_temp=y_temp, target='temperature')

        # Save metrics
        pd.DataFrame({
            'metric': list(metrics['temperature'].keys()),
            'value': list(metrics['temperature'].values())
        }).to_csv(os.path.join(args.output_dir, 'temperature_metrics.tsv'), sep='\t', index=False)

    else:  # both
        y_growth_pred, y_temp_pred = predictor.predict(features, target='both')

        # Save predictions
        pd.DataFrame({
            'genome_id': features.index,
            'actual_growth': y_growth,
            'predicted_growth': y_growth_pred,
            'actual_temp': y_temp,
            'predicted_temp': y_temp_pred
        }).to_csv(os.path.join(args.output_dir, 'predictions.tsv'), sep='\t', index=False)

        # Plot predictions
        plt.figure(figsize=(12, 5))

        plt.subplot(1, 2, 1)
        plt.scatter(y_growth, y_growth_pred, alpha=0.5)
        plt.plot([min(y_growth), max(y_growth)], [min(y_growth), max(y_growth)], 'r--')
        plt.xlabel('Actual Growth Rate')
        plt.ylabel('Predicted Growth Rate')
        plt.title('Growth Rate Predictions')

        plt.subplot(1, 2, 2)
        plt.scatter(y_temp, y_temp_pred, alpha=0.5)
        plt.plot([min(y_temp), max(y_temp)], [min(y_temp), max(y_temp)], 'r--')
        plt.xlabel('Actual Temperature')
        plt.ylabel('Predicted Temperature')
        plt.title('Temperature Predictions')

        plt.tight_layout()
        plt.savefig(os.path.join(args.output_dir, 'predictions.png'))

        # Evaluate model
        metrics = predictor.evaluate(features, y_growth=y_growth, y_temp=y_temp, target='both')

        # Save metrics
        pd.DataFrame({
            'metric': ['R2', 'RMSE', 'MAE'],
            'growth_rate': [metrics['growth_rate']['R2'], metrics['growth_rate']['RMSE'], metrics['growth_rate']['MAE']],
            'temperature': [metrics['temperature']['R2'], metrics['temperature']['RMSE'], metrics['temperature']['MAE']],
            'overall': [metrics['overall']['R2'], metrics['overall']['RMSE'], metrics['overall']['MAE']]
        }).to_csv(os.path.join(args.output_dir, 'metrics.tsv'), sep='\t', index=False)

    logger.info(f"Predictions saved to {args.output_dir}")

if __name__ == "__main__":
    main()
