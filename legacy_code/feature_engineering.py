#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Feature Engineering for Temperature Prediction.

This module implements advanced feature engineering techniques specifically
designed to improve temperature prediction in the DeepMu model.
"""

import os
import argparse
import logging
import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestRegressor
from sklearn.feature_selection import mutual_info_regression
from sklearn.preprocessing import StandardScaler
from typing import Tuple, List, Dict, Optional, Union

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_interaction_features(
    X: pd.DataFrame,
    y: Optional[pd.Series] = None,
    top_k: int = 50,
    method: str = 'importance',
    handle_outliers: bool = True
) -> pd.DataFrame:
    """
    Create interaction features between the most important features.

    Args:
        X: DataFrame containing the original features
        y: Target variable (optional, used for feature importance)
        top_k: Number of top features to consider for interactions
        method: Method to select top features ('importance', 'variance', 'correlation')

    Returns:
        DataFrame with original and interaction features
    """
    logger.info(f"Creating interaction features using {method} method with top {top_k} features")

    # Preprocess data if handling outliers
    if handle_outliers:
        X_clean = X.copy()
        # Replace infinities with NaN
        X_clean = X_clean.replace([np.inf, -np.inf], np.nan)
        # Replace NaNs with column medians
        X_clean = X_clean.fillna(X_clean.median())
        # Clip extreme values (beyond 5 std from mean) for each column
        for col in X_clean.columns:
            mean_val = X_clean[col].mean()
            std_val = X_clean[col].std()
            if std_val > 0:  # Avoid division by zero
                X_clean[col] = X_clean[col].clip(mean_val - 5 * std_val, mean_val + 5 * std_val)
    else:
        X_clean = X

    # Get top features based on specified method
    if method == 'importance' and y is not None:
        # Use Random Forest feature importance
        rf = RandomForestRegressor(n_estimators=50, max_depth=10, random_state=42, n_jobs=-1)
        try:
            rf.fit(X_clean, y)
            feature_importances = pd.Series(rf.feature_importances_, index=X_clean.columns)
            top_features = feature_importances.nlargest(top_k).index.tolist()
            logger.info(f"Selected top {len(top_features)} features based on Random Forest importance")
        except Exception as e:
            logger.warning(f"Error fitting Random Forest for feature importance: {e}")
            logger.warning("Falling back to variance-based feature selection")
            feature_variances = X_clean.var()
            top_features = feature_variances.nlargest(top_k).index.tolist()
            logger.info(f"Selected top {len(top_features)} features based on variance (fallback)")

    elif method == 'variance':
        # Use variance
        feature_variances = X_clean.var()
        top_features = feature_variances.nlargest(top_k).index.tolist()
        logger.info(f"Selected top {len(top_features)} features based on variance")

    elif method == 'correlation' and y is not None:
        # Use correlation with target
        correlations = X_clean.corrwith(y).abs()
        top_features = correlations.nlargest(top_k).index.tolist()
        logger.info(f"Selected top {len(top_features)} features based on correlation with target")

    else:
        # Default to variance if method is not recognized or y is not provided
        feature_variances = X_clean.var()
        top_features = feature_variances.nlargest(top_k).index.tolist()
        logger.info(f"Selected top {len(top_features)} features based on variance (default)")

    # Create interaction features
    X_interactions = X.copy()
    interaction_count = 0

    # Store interactions in a dictionary to add all at once (avoid fragmentation)
    new_interactions = {}

    # Pairwise products of top features
    for i in range(len(top_features)):
        for j in range(i+1, len(top_features)):
            feat_i = top_features[i]
            feat_j = top_features[j]
            interaction_name = f"interaction_{feat_i}_{feat_j}"

            # Get feature values
            x_i = X_clean[feat_i].values
            x_j = X_clean[feat_j].values

            # Create interaction feature
            interaction = x_i * x_j

            # Final check for any infinities or NaNs
            interaction = np.nan_to_num(interaction, nan=0.0, posinf=0.0, neginf=0.0)

            # Store in dictionary
            new_interactions[interaction_name] = interaction
            interaction_count += 1

            # Limit the number of interactions to avoid explosion
            if interaction_count >= 1000:
                logger.info(f"Reached maximum number of interaction features (1000)")
                break

        if interaction_count >= 1000:
            break

    # Add all interactions at once
    for feat_name, feat_values in new_interactions.items():
        X_interactions[feat_name] = feat_values

    logger.info(f"Created {interaction_count} interaction features")
    logger.info(f"Total features after adding interactions: {X_interactions.shape[1]}")

    return X_interactions

def create_nonlinear_features(
    X: pd.DataFrame,
    y: Optional[pd.Series] = None,
    top_k: int = 50,
    transformations: List[str] = ['square', 'sqrt', 'log', 'inverse'],
    handle_outliers: bool = True
) -> pd.DataFrame:
    """
    Create nonlinear transformations of important features.

    Args:
        X: DataFrame containing the original features
        y: Target variable (optional, used for feature importance)
        top_k: Number of top features to transform
        transformations: List of transformations to apply

    Returns:
        DataFrame with original and nonlinear features
    """
    logger.info(f"Creating nonlinear features with {transformations} transformations for top {top_k} features")

    # Get top features based on importance if y is provided, otherwise use variance
    if y is not None:
        # Use Random Forest feature importance
        rf = RandomForestRegressor(n_estimators=50, max_depth=10, random_state=42, n_jobs=-1)
        rf.fit(X, y)
        feature_importances = pd.Series(rf.feature_importances_, index=X.columns)
        top_features = feature_importances.nlargest(top_k).index.tolist()
        logger.info(f"Selected top {len(top_features)} features based on Random Forest importance")
    else:
        # Use variance
        feature_variances = X.var()
        top_features = feature_variances.nlargest(top_k).index.tolist()
        logger.info(f"Selected top {len(top_features)} features based on variance")

    # Create nonlinear features
    X_nonlinear = X.copy()
    nonlinear_count = 0

    # Create a copy of the dataframe to avoid fragmentation
    new_features = {}

    for feat in top_features:
        # Get feature values
        x = X[feat].values

        # Handle outliers if requested
        if handle_outliers:
            # Replace infinities with NaN
            x = np.nan_to_num(x, nan=np.nan, posinf=np.nan, neginf=np.nan)

            # Replace NaNs with median
            median_val = np.nanmedian(x)
            x = np.nan_to_num(x, nan=median_val)

            # Clip extreme values (beyond 5 std from mean)
            mean_val = np.mean(x)
            std_val = np.std(x)
            if std_val > 0:
                x = np.clip(x, mean_val - 5 * std_val, mean_val + 5 * std_val)

        # Apply transformations
        for transform in transformations:
            if transform == 'square':
                # Square transformation
                new_features[f"{feat}_squared"] = x ** 2
                nonlinear_count += 1

            elif transform == 'sqrt':
                # Square root transformation (with handling of negative values)
                new_features[f"{feat}_sqrt"] = np.sqrt(np.abs(x)) * np.sign(x)
                nonlinear_count += 1

            elif transform == 'log':
                # Log transformation (with handling of zero/negative values)
                min_val = np.min(x)
                if min_val <= 0:
                    offset = abs(min_val) + 1e-5
                    new_features[f"{feat}_log"] = np.log(x + offset)
                else:
                    new_features[f"{feat}_log"] = np.log(x)
                nonlinear_count += 1

            elif transform == 'inverse':
                # Inverse transformation (with handling of zero values)
                new_features[f"{feat}_inverse"] = 1.0 / (np.abs(x) + 1e-5) * np.sign(x)
                nonlinear_count += 1

            elif transform == 'exp':
                # Exponential transformation (with scaling to avoid overflow)
                scaled_x = (x - np.mean(x)) / (np.std(x) + 1e-8)
                new_features[f"{feat}_exp"] = np.exp(np.clip(scaled_x, -5, 5))
                nonlinear_count += 1

    # Add all new features at once to avoid fragmentation
    for feat_name, feat_values in new_features.items():
        # Final check for any remaining infinities or NaNs
        feat_values = np.nan_to_num(feat_values, nan=0.0, posinf=0.0, neginf=0.0)
        X_nonlinear[feat_name] = feat_values

    logger.info(f"Created {nonlinear_count} nonlinear features")
    logger.info(f"Total features after adding nonlinear transformations: {X_nonlinear.shape[1]}")

    return X_nonlinear

def apply_feature_engineering(
    X: pd.DataFrame,
    y: Optional[pd.Series] = None,
    use_interactions: bool = True,
    use_nonlinear: bool = True,
    interaction_top_k: int = 50,
    nonlinear_top_k: int = 50,
    nonlinear_transformations: List[str] = ['square', 'sqrt', 'log', 'inverse'],
    handle_outliers: bool = True
) -> pd.DataFrame:
    """
    Apply comprehensive feature engineering for temperature prediction.

    Args:
        X: DataFrame containing the original features
        y: Target variable (optional, used for feature importance)
        use_interactions: Whether to create interaction features
        use_nonlinear: Whether to create nonlinear features
        interaction_top_k: Number of top features to consider for interactions
        nonlinear_top_k: Number of top features to transform
        nonlinear_transformations: List of transformations to apply

    Returns:
        DataFrame with engineered features
    """
    logger.info(f"Applying feature engineering to {X.shape[1]} features")

    # Start with original features
    X_engineered = X.copy()

    # Apply nonlinear transformations
    if use_nonlinear:
        X_engineered = create_nonlinear_features(
            X_engineered,
            y=y,
            top_k=nonlinear_top_k,
            transformations=nonlinear_transformations,
            handle_outliers=handle_outliers
        )

    # Apply interaction features
    if use_interactions:
        X_engineered = create_interaction_features(
            X_engineered,
            y=y,
            top_k=interaction_top_k,
            handle_outliers=handle_outliers
        )

    logger.info(f"Feature engineering complete. Original features: {X.shape[1]}, Engineered features: {X_engineered.shape[1]}")

    return X_engineered

def main():
    """Command line interface for feature engineering."""
    parser = argparse.ArgumentParser(description='Feature Engineering for Temperature Prediction')
    parser.add_argument('--feature-file', type=str, required=True, help='Path to feature file')
    parser.add_argument('--metadata-file', type=str, required=True, help='Path to metadata file')
    parser.add_argument('--output-file', type=str, required=True, help='Path to output file')
    parser.add_argument('--use-interactions', action='store_true', help='Create interaction features')
    parser.add_argument('--use-nonlinear', action='store_true', help='Create nonlinear features')
    parser.add_argument('--interaction-top-k', type=int, default=50, help='Number of top features for interactions')
    parser.add_argument('--nonlinear-top-k', type=int, default=50, help='Number of top features for nonlinear transformations')
    parser.add_argument('--handle-outliers', action='store_true', help='Handle outliers and extreme values')

    args = parser.parse_args()

    # Load data
    logger.info(f"Loading features from {args.feature_file}")
    features = pd.read_csv(args.feature_file, sep='\t', index_col='genome_id')

    logger.info(f"Loading metadata from {args.metadata_file}")
    metadata = pd.read_csv(args.metadata_file, sep='\t', index_col='genome_id')

    # Get common indices
    common_indices = features.index.intersection(metadata.index)
    features = features.loc[common_indices]
    metadata = metadata.loc[common_indices]

    logger.info(f"Found {len(common_indices)} genomes with both features and metadata")

    # Apply feature engineering
    engineered_features = apply_feature_engineering(
        features,
        y=metadata['optimal_temperature'],
        use_interactions=args.use_interactions,
        use_nonlinear=args.use_nonlinear,
        interaction_top_k=args.interaction_top_k,
        nonlinear_top_k=args.nonlinear_top_k,
        handle_outliers=args.handle_outliers
    )

    # Save engineered features
    logger.info(f"Saving engineered features to {args.output_file}")
    engineered_features.to_csv(args.output_file, sep='\t')

    logger.info(f"Feature engineering complete. Original features: {features.shape[1]}, Engineered features: {engineered_features.shape[1]}")

if __name__ == '__main__':
    main()
