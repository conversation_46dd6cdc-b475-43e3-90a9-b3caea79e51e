#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Hybrid model for microbial growth rate and optimal temperature prediction.
Combines stacking Random Forest approach for growth rate with DNN for temperature.
"""

import os
import argparse
import logging
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Union, Optional
from sklearn.model_selection import train_test_split, KFold
from sklearn.preprocessing import StandardScaler, LabelEncoder, OneHotEncoder
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from sklearn.inspection import permutation_importance
import xgboost as xgb
import lightgbm as lgb
from datetime import datetime
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
from torch.optim.lr_scheduler import OneCycleLR
import shap
import joblib
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Check GPU availability
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
logger.info(f"Using device: {device}")

# Define the DNN model for optimal temperature prediction
class DNNModel(nn.Module):
    def __init__(self, input_dim: int, hidden_dims: List[int], dropout_rates: List[float]):
        super(DNNModel, self).__init__()
        
        layers = []
        prev_dim = input_dim
        
        for i, (hdim, drop_rate) in enumerate(zip(hidden_dims, dropout_rates)):
            layers.append(nn.Linear(prev_dim, hdim))
            layers.append(nn.BatchNorm1d(hdim))
            layers.append(nn.LeakyReLU())
            layers.append(nn.Dropout(drop_rate))
            prev_dim = hdim
        
        # Output layer
        layers.append(nn.Linear(prev_dim, 1))
        
        self.model = nn.Sequential(*layers)
    
    def forward(self, x):
        return self.model(x)

# Define the feature selection function based on random forest importance
def select_features_by_importance(X: pd.DataFrame, y: pd.Series, n_features: int = 50) -> List[str]:
    """Select features based on random forest importance"""
    # Create a copy of features to avoid modifying the original
    X_copy = X.copy()
    
    # Encode categorical features
    categorical_cols = X_copy.select_dtypes(include=['object', 'category']).columns
    for col in categorical_cols:
        le = LabelEncoder()
        X_copy[col] = le.fit_transform(X_copy[col].astype(str))
    
    # Train a Random Forest model to get feature importance
    rf = RandomForestRegressor(n_estimators=100, random_state=42, n_jobs=-1)
    rf.fit(X_copy, y)
    
    # Get feature importance scores
    importance_scores = pd.DataFrame({
        'feature': X_copy.columns,
        'importance': rf.feature_importances_
    })
    
    # Sort features by importance
    importance_scores = importance_scores.sort_values('importance', ascending=False)
    
    # Select top n_features
    selected_features = importance_scores.head(n_features)['feature'].tolist()
    
    return selected_features

# Define function to select features by variance
def select_features_by_variance(X: pd.DataFrame, n_features: int = 100) -> List[str]:
    """Select features based on variance"""
    # Get numeric columns
    numeric_cols = X.select_dtypes(include=[np.number]).columns
    
    # Calculate variance for each feature
    feature_variances = X[numeric_cols].var().sort_values(ascending=False)
    
    # Select top features
    top_features = feature_variances.index[:n_features].tolist()
    
    # Get non-numeric columns
    non_numeric_cols = X.select_dtypes(exclude=[np.number]).columns.tolist()
    
    # Combine numeric and non-numeric columns
    return top_features + non_numeric_cols

def load_data(feature_file: str, metadata_file: str) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    Load feature and metadata files
    """
    features = pd.read_csv(feature_file, sep='\t', index_col=0)
    metadata = pd.read_csv(metadata_file, sep='\t', index_col=0)
    
    # Fill NaN values
    features = features.fillna(0)
    metadata = metadata.fillna(0)

    # Convert all numeric columns to float64 to avoid type incompatibility
    numeric_columns = features.select_dtypes(include=[np.number]).columns
    features[numeric_columns] = features[numeric_columns].astype(np.float64)
    
    return features, metadata

def prepare_data(features: pd.DataFrame, 
                 metadata: pd.DataFrame, 
                 target: str,
                 n_features: int = 50, 
                 test_size: float = 0.2,
                 val_size: float = 0.1) -> Dict:
    """
    Prepare data for training including feature selection and splitting
    """
    # Target variable
    y = metadata[target].copy()
    
    # Select features based on importance
    selected_features = select_features_by_importance(features, y, n_features)
    X = features[selected_features].copy()
    
    # Split into train, validation, and test sets
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=test_size, random_state=42
    )
    
    # Further split training set to create validation set
    if val_size > 0:
        val_size_adj = val_size / (1 - test_size)  # Adjust validation size
        X_train, X_val, y_train, y_val = train_test_split(
            X_train, y_train, test_size=val_size_adj, random_state=42
        )
    else:
        X_val, y_val = X_train.copy(), y_train.copy()
    
    # Prepare data dictionary
    data = {
        'X_train': X_train,
        'y_train': y_train,
        'X_val': X_val,
        'y_val': y_val,
        'X_test': X_test,
        'y_test': y_test,
        'selected_features': selected_features
    }
    
    return data

# Stacking RF model for growth rate
class StackingRFModel:
    def __init__(self, rf_params=None, xgb_params=None, lgb_params=None):
        # Set default parameters if not provided
        self.rf_params = rf_params or {
            'n_estimators': 300,
            'max_depth': 15,
            'min_samples_split': 5,
            'min_samples_leaf': 2,
            'n_jobs': -1,
            'random_state': 42
        }
        
        self.xgb_params = xgb_params or {
            'n_estimators': 300,
            'learning_rate': 0.05,
            'max_depth': 6,
            'subsample': 0.8,
            'colsample_bytree': 0.8,
            'random_state': 42
        }
        
        self.lgb_params = lgb_params or {
            'n_estimators': 300,
            'learning_rate': 0.05,
            'num_leaves': 31,
            'subsample': 0.8,
            'colsample_bytree': 0.8,
            'random_state': 42
        }
        
        # Initialize base models
        self.rf_model = RandomForestRegressor(**self.rf_params)
        self.xgb_model = xgb.XGBRegressor(**self.xgb_params)
        self.lgb_model = lgb.LGBMRegressor(**self.lgb_params)
        
        # Initialize meta-regressor
        self.meta_model = xgb.XGBRegressor(
            n_estimators=100,
            learning_rate=0.03,
            max_depth=4,
            random_state=42
        )
        
        # For storing feature importances
        self.feature_importances = {}
        
    def fit(self, X_train, y_train, X_val, y_val):
        """Train base models and meta-regressor"""
        logger.info("Training base models...")
        
        # Train base models
        self.rf_model.fit(X_train, y_train)
        self.xgb_model.fit(X_train, y_train)
        self.lgb_model.fit(X_train, y_train)
        
        # Generate predictions from base models
        rf_train_preds = self.rf_model.predict(X_train).reshape(-1, 1)
        xgb_train_preds = self.xgb_model.predict(X_train).reshape(-1, 1)
        lgb_train_preds = self.lgb_model.predict(X_train).reshape(-1, 1)
        
        rf_val_preds = self.rf_model.predict(X_val).reshape(-1, 1)
        xgb_val_preds = self.xgb_model.predict(X_val).reshape(-1, 1)
        lgb_val_preds = self.lgb_model.predict(X_val).reshape(-1, 1)
        
        # Create meta-features
        meta_train_features = np.hstack([rf_train_preds, xgb_train_preds, lgb_train_preds])
        meta_val_features = np.hstack([rf_val_preds, xgb_val_preds, lgb_val_preds])
        
        # Add original features for bidirectional learning
        meta_train_features = np.hstack([meta_train_features, X_train.values])
        meta_val_features = np.hstack([meta_val_features, X_val.values])
        
        # Train meta-regressor
        logger.info("Training meta-regressor...")
        
        # For different versions of XGBoost, try without early_stopping_rounds
        try:
            # Try with eval_metric and callbacks
            from xgboost.callback import EarlyStopping
            
            self.meta_model.fit(
                meta_train_features, 
                y_train,
                eval_set=[(meta_val_features, y_val)],
                eval_metric='rmse',
                callbacks=[EarlyStopping(rounds=20)],
                verbose=False
            )
        except (TypeError, ImportError):
            try:
                # Try with eval_metric parameter only
                self.meta_model.fit(
                    meta_train_features, 
                    y_train,
                    eval_set=[(meta_val_features, y_val)],
                    eval_metric='rmse',
                    verbose=False
                )
            except TypeError:
                # Fallback to basic fit without evaluation set
                logger.warning("XGBoost early stopping not available, training without validation")
                self.meta_model.fit(meta_train_features, y_train)
        
        # Store feature importances
        self.feature_importances = {
            'rf': pd.Series(self.rf_model.feature_importances_, index=X_train.columns),
            'xgb': pd.Series(self.xgb_model.feature_importances_, index=X_train.columns),
            'lgb': pd.Series(self.lgb_model.feature_importances_, index=X_train.columns)
        }
        
        logger.info("Stacking model training completed")
        
    def predict(self, X):
        """Generate predictions"""
        # Generate predictions from base models
        rf_preds = self.rf_model.predict(X).reshape(-1, 1)
        xgb_preds = self.xgb_model.predict(X).reshape(-1, 1)
        lgb_preds = self.lgb_model.predict(X).reshape(-1, 1)
        
        # Create meta-features
        meta_features = np.hstack([rf_preds, xgb_preds, lgb_preds, X.values])
        
        # Generate final predictions
        return self.meta_model.predict(meta_features)
    
    def save(self, output_dir):
        """Save all models"""
        os.makedirs(output_dir, exist_ok=True)
        
        joblib.dump(self.rf_model, os.path.join(output_dir, "rf_model.joblib"))
        joblib.dump(self.xgb_model, os.path.join(output_dir, "xgb_model.joblib"))
        joblib.dump(self.lgb_model, os.path.join(output_dir, "lgb_model.joblib"))
        joblib.dump(self.meta_model, os.path.join(output_dir, "meta_model.joblib"))
        
        # Save feature importances
        pd.DataFrame(self.feature_importances).to_csv(
            os.path.join(output_dir, "feature_importances.csv")
        )
    
    def load(self, output_dir):
        """Load all models"""
        self.rf_model = joblib.load(os.path.join(output_dir, "rf_model.joblib"))
        self.xgb_model = joblib.load(os.path.join(output_dir, "xgb_model.joblib"))
        self.lgb_model = joblib.load(os.path.join(output_dir, "lgb_model.joblib"))
        self.meta_model = joblib.load(os.path.join(output_dir, "meta_model.joblib"))
        
        # Load feature importances
        self.feature_importances = pd.read_csv(
            os.path.join(output_dir, "feature_importances.csv"), 
            index_col=0
        ).to_dict()

def train_dnn_temperature_model(data_dict, hidden_dims, dropout_rates, batch_size=64, 
                               learning_rate=0.001, epochs=150, output_dir="models"):
    """Train DNN model for optimal temperature prediction with one-cycle learning"""
    # Prepare training data
    X_train, y_train = data_dict['X_train'], data_dict['y_train']
    X_val, y_val = data_dict['X_val'], data_dict['y_val']
    
    # Scale features
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_val_scaled = scaler.transform(X_val)
    
    # Convert to PyTorch tensors
    X_train_tensor = torch.FloatTensor(X_train_scaled)
    y_train_tensor = torch.FloatTensor(y_train.values).reshape(-1, 1)
    X_val_tensor = torch.FloatTensor(X_val_scaled)
    y_val_tensor = torch.FloatTensor(y_val.values).reshape(-1, 1)
    
    # Create datasets and dataloaders
    train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
    val_dataset = TensorDataset(X_val_tensor, y_val_tensor)
    
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
    
    # Initialize model
    model = DNNModel(
        input_dim=X_train.shape[1],
        hidden_dims=hidden_dims,
        dropout_rates=dropout_rates
    ).to(device)
    
    # Loss function and optimizer
    criterion = nn.MSELoss()
    optimizer = optim.Adam(model.parameters(), lr=learning_rate, weight_decay=1e-5)
    
    # Learning rate scheduler with one-cycle policy
    scheduler = OneCycleLR(
        optimizer,
        max_lr=learning_rate,
        steps_per_epoch=len(train_loader),
        epochs=epochs,
        pct_start=0.3
    )
    
    # Training loop
    best_val_loss = float('inf')
    best_model_state = None
    history = {
        'train_loss': [],
        'val_loss': [],
        'val_r2': []
    }
    
    logger.info(f"Starting DNN training for {epochs} epochs")
    for epoch in range(epochs):
        model.train()
        train_loss = 0.0
        
        for inputs, targets in train_loader:
            inputs, targets = inputs.to(device), targets.to(device)
            
            # Zero gradients
            optimizer.zero_grad()
            
            # Forward pass
            outputs = model(inputs)
            loss = criterion(outputs, targets)
            
            # Backward pass and optimize
            loss.backward()
            
            # Apply gradient clipping
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            optimizer.step()
            scheduler.step()
            
            train_loss += loss.item() * inputs.size(0)
        
        # Calculate average loss for the epoch
        train_loss = train_loss / len(train_loader.dataset)
        history['train_loss'].append(train_loss)
        
        # Validation
        model.eval()
        val_loss = 0.0
        all_preds = []
        all_targets = []
        
        with torch.no_grad():
            for inputs, targets in val_loader:
                inputs, targets = inputs.to(device), targets.to(device)
                outputs = model(inputs)
                loss = criterion(outputs, targets)
                val_loss += loss.item() * inputs.size(0)
                
                # Collect predictions and targets for R² calculation
                all_preds.extend(outputs.cpu().numpy())
                all_targets.extend(targets.cpu().numpy())
        
        val_loss = val_loss / len(val_loader.dataset)
        history['val_loss'].append(val_loss)
        
        # Calculate R² score
        val_r2 = r2_score(all_targets, all_preds)
        history['val_r2'].append(val_r2)
        
        # Print progress
        if (epoch + 1) % 10 == 0:
            logger.info(f'Epoch {epoch+1}/{epochs} - Train Loss: {train_loss:.4f} - '
                      f'Val Loss: {val_loss:.4f} - Val R²: {val_r2:.4f}')
        
        # Save best model
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            best_model_state = model.state_dict().copy()
    
    # Load best model
    model.load_state_dict(best_model_state)
    
    # Save model and artifacts
    os.makedirs(output_dir, exist_ok=True)
    torch.save(model.state_dict(), os.path.join(output_dir, 'temperature_dnn_model.pt'))
    torch.save(scaler, os.path.join(output_dir, 'temperature_scaler.pt'))
    
    # Save training history
    pd.DataFrame(history).to_csv(os.path.join(output_dir, 'temperature_training_history.csv'))
    
    # Evaluate on validation set
    model.eval()
    with torch.no_grad():
        val_preds = model(X_val_tensor.to(device)).cpu().numpy().flatten()
    
    val_r2 = r2_score(y_val, val_preds)
    val_rmse = np.sqrt(mean_squared_error(y_val, val_preds))
    val_mae = mean_absolute_error(y_val, val_preds)
    
    logger.info(f"Validation metrics - R²: {val_r2:.4f}, RMSE: {val_rmse:.4f}, MAE: {val_mae:.4f}")
    
    return {
        'model': model,
        'scaler': scaler,
        'history': history,
        'val_metrics': {
            'r2': val_r2,
            'rmse': val_rmse,
            'mae': val_mae
        },
        'feature_names': data_dict['selected_features']
    }

def evaluate_models(growth_model, temp_model, data_dict, output_dir):
    """Evaluate both models on test data and create visualizations"""
    # Get test data for each model with the correct features
    growth_test_data = data_dict['growth']['X_test']
    temp_test_data = data_dict['temp']['X_test']
    
    y_growth_test = data_dict['growth']['y_test']
    y_temp_test = data_dict['temp']['y_test']
    
    # Predictions for growth rate
    growth_preds = growth_model.predict(growth_test_data)
    
    # Predictions for temperature
    # Get the scaler and model from the temp_model dict
    scaler = temp_model['scaler']
    model = temp_model['model']
    
    # Scale the temperature test data
    X_test_scaled = scaler.transform(temp_test_data)
    X_test_tensor = torch.FloatTensor(X_test_scaled).to(device)
    
    # Make predictions
    model.eval()
    with torch.no_grad():
        temp_preds = model(X_test_tensor).cpu().numpy().flatten()
    
    # Calculate metrics
    growth_r2 = r2_score(y_growth_test, growth_preds)
    growth_rmse = np.sqrt(mean_squared_error(y_growth_test, growth_preds))
    growth_mae = mean_absolute_error(y_growth_test, growth_preds)
    
    temp_r2 = r2_score(y_temp_test, temp_preds)
    temp_rmse = np.sqrt(mean_squared_error(y_temp_test, temp_preds))
    temp_mae = mean_absolute_error(y_temp_test, temp_preds)
    
    # Log results
    logger.info(f"Growth Rate - Test R²: {growth_r2:.4f}, RMSE: {growth_rmse:.4f}, MAE: {growth_mae:.4f}")
    logger.info(f"Optimal Temperature - Test R²: {temp_r2:.4f}, RMSE: {temp_rmse:.4f}, MAE: {temp_mae:.4f}")
    
    # Create result dictionary
    results = {
        'growth_rate': {
            'r2': growth_r2,
            'rmse': growth_rmse,
            'mae': growth_mae,
            'predictions': growth_preds,
            'actual': y_growth_test
        },
        'temperature': {
            'r2': temp_r2,
            'rmse': temp_rmse,
            'mae': temp_mae,
            'predictions': temp_preds,
            'actual': y_temp_test
        }
    }
    
    # Create visualization directory
    viz_dir = os.path.join(output_dir, 'visualizations')
    os.makedirs(viz_dir, exist_ok=True)
    
    # Create scatter plots
    plt.figure(figsize=(10, 5))
    
    plt.subplot(1, 2, 1)
    plt.scatter(y_growth_test, growth_preds, alpha=0.5)
    plt.plot([y_growth_test.min(), y_growth_test.max()], 
             [y_growth_test.min(), y_growth_test.max()], 
             'r--')
    plt.xlabel('Actual Growth Rate')
    plt.ylabel('Predicted Growth Rate')
    plt.title(f'Growth Rate Predictions (R² = {growth_r2:.4f})')
    
    plt.subplot(1, 2, 2)
    plt.scatter(y_temp_test, temp_preds, alpha=0.5)
    plt.plot([y_temp_test.min(), y_temp_test.max()], 
             [y_temp_test.min(), y_temp_test.max()], 
             'r--')
    plt.xlabel('Actual Optimal Temperature')
    plt.ylabel('Predicted Optimal Temperature')
    plt.title(f'Temperature Predictions (R² = {temp_r2:.4f})')
    
    plt.tight_layout()
    plt.savefig(os.path.join(viz_dir, 'prediction_scatter.png'))
    
    # Create feature importance plots
    # For growth model (feature importance from base models)
    growth_importance = pd.DataFrame(growth_model.feature_importances)
    growth_importance_avg = growth_importance.mean(axis=1).sort_values(ascending=False).head(20)
    
    plt.figure(figsize=(12, 6))
    growth_importance_avg.plot(kind='barh')
    plt.title('Top 20 Features for Growth Rate Prediction')
    plt.tight_layout()
    plt.savefig(os.path.join(viz_dir, 'growth_feature_importance.png'))
    
    # Save results
    with open(os.path.join(output_dir, 'test_results.txt'), 'w') as f:
        f.write(f"Growth Rate - Test R²: {growth_r2:.4f}, RMSE: {growth_rmse:.4f}, MAE: {growth_mae:.4f}\n")
        f.write(f"Optimal Temperature - Test R²: {temp_r2:.4f}, RMSE: {temp_rmse:.4f}, MAE: {temp_mae:.4f}\n")
    
    return results

def main():
    parser = argparse.ArgumentParser(description="Hybrid model for microbial growth and temperature prediction")
    parser.add_argument("--feature_file", required=True, help="Path to feature file")
    parser.add_argument("--metadata_file", required=True, help="Path to metadata file")
    parser.add_argument("--output_dir", default="hybrid_model", help="Output directory")
    parser.add_argument("--growth_features", type=int, default=100, help="Number of features for growth rate model")
    parser.add_argument("--temp_features", type=int, default=100, help="Number of features for temperature model")
    parser.add_argument("--batch_size", type=int, default=64, help="Batch size for DNN training")
    parser.add_argument("--epochs", type=int, default=150, help="Number of epochs for DNN training")
    parser.add_argument("--learning_rate", type=float, default=0.001, help="Learning rate for DNN")
    parser.add_argument("--rf_estimators", type=int, default=300, help="Number of estimators for Random Forest")
    
    args = parser.parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Load data
    logger.info("Loading data...")
    features, metadata = load_data(args.feature_file, args.metadata_file)
    
    # Prepare data for growth rate prediction
    logger.info("Preparing data for growth rate prediction...")
    growth_data = prepare_data(
        features=features,
        metadata=metadata,
        target='growth_rate',
        n_features=args.growth_features
    )
    
    # Prepare data for temperature prediction
    logger.info("Preparing data for temperature prediction...")
    temp_data = prepare_data(
        features=features,
        metadata=metadata,
        target='optimal_temperature',
        n_features=args.temp_features
    )
    
    # Create data dictionary
    data_dict = {
        'growth': growth_data,
        'temp': temp_data
    }
    
    # Train growth rate model (Stacking RF)
    logger.info("Training growth rate model...")
    growth_model = StackingRFModel(
        rf_params={'n_estimators': args.rf_estimators},
        xgb_params={'n_estimators': args.rf_estimators},
        lgb_params={'n_estimators': args.rf_estimators}
    )
    
    growth_model.fit(
        growth_data['X_train'], 
        growth_data['y_train'], 
        growth_data['X_val'], 
        growth_data['y_val']
    )
    
    # Save growth model
    growth_model.save(os.path.join(args.output_dir, "growth_model"))
    
    # Train temperature model using RandomForest instead of DNN since DNN is not performing well
    logger.info("Training temperature model using Random Forest...")
    
    temp_rf = RandomForestRegressor(
        n_estimators=500,
        max_depth=20,
        min_samples_split=5,
        min_samples_leaf=2,
        random_state=42,
        n_jobs=-1
    )
    
    # Fit the model
    temp_rf.fit(temp_data['X_train'], temp_data['y_train'])
    
    # Evaluate on validation set
    temp_val_preds = temp_rf.predict(temp_data['X_val'])
    temp_val_r2 = r2_score(temp_data['y_val'], temp_val_preds)
    temp_val_rmse = np.sqrt(mean_squared_error(temp_data['y_val'], temp_val_preds))
    
    logger.info(f"Temperature RF Validation - R²: {temp_val_r2:.4f}, RMSE: {temp_val_rmse:.4f}")
    
    # Create a mock temp_model structure similar to what the DNN would return
    temp_model = {
        'model': temp_rf,
        'scaler': None,  # Not needed for RF
        'val_metrics': {
            'r2': temp_val_r2,
            'rmse': temp_val_rmse
        },
        'feature_names': temp_data['selected_features']
    }
    
    # Save the RF model
    temp_model_dir = os.path.join(args.output_dir, "temp_model")
    os.makedirs(temp_model_dir, exist_ok=True)
    joblib.dump(temp_rf, os.path.join(temp_model_dir, "temperature_rf_model.joblib"))
    
    # Save feature importances
    temp_importances = pd.Series(temp_rf.feature_importances_, index=temp_data['X_train'].columns)
    temp_importances.sort_values(ascending=False).to_csv(
        os.path.join(temp_model_dir, "temperature_feature_importances.csv")
    )
    
    # Modify the evaluate_models function to handle the RandomForest temperature model
    def evaluate_rf_models(growth_model, temp_rf_model, data_dict, output_dir):
        # Get test data
        growth_test_data = data_dict['growth']['X_test']
        temp_test_data = data_dict['temp']['X_test']
        
        y_growth_test = data_dict['growth']['y_test']
        y_temp_test = data_dict['temp']['y_test']
        
        # Predictions
        growth_preds = growth_model.predict(growth_test_data)
        temp_preds = temp_rf_model['model'].predict(temp_test_data)
        
        # Calculate metrics
        growth_r2 = r2_score(y_growth_test, growth_preds)
        growth_rmse = np.sqrt(mean_squared_error(y_growth_test, growth_preds))
        growth_mae = mean_absolute_error(y_growth_test, growth_preds)
        
        temp_r2 = r2_score(y_temp_test, temp_preds)
        temp_rmse = np.sqrt(mean_squared_error(y_temp_test, temp_preds))
        temp_mae = mean_absolute_error(y_temp_test, temp_preds)
        
        # Log results
        logger.info(f"Growth Rate - Test R²: {growth_r2:.4f}, RMSE: {growth_rmse:.4f}, MAE: {growth_mae:.4f}")
        logger.info(f"Optimal Temperature - Test R²: {temp_r2:.4f}, RMSE: {temp_rmse:.4f}, MAE: {temp_mae:.4f}")
        
        # Create result dictionary
        results = {
            'growth_rate': {
                'r2': growth_r2,
                'rmse': growth_rmse,
                'mae': growth_mae,
                'predictions': growth_preds,
                'actual': y_growth_test
            },
            'temperature': {
                'r2': temp_r2,
                'rmse': temp_rmse,
                'mae': temp_mae,
                'predictions': temp_preds,
                'actual': y_temp_test
            }
        }
        
        # Create visualization directory
        viz_dir = os.path.join(output_dir, 'visualizations')
        os.makedirs(viz_dir, exist_ok=True)
        
        # Create scatter plots
        plt.figure(figsize=(10, 5))
        
        plt.subplot(1, 2, 1)
        plt.scatter(y_growth_test, growth_preds, alpha=0.5)
        plt.plot([y_growth_test.min(), y_growth_test.max()], 
                [y_growth_test.min(), y_growth_test.max()], 
                'r--')
        plt.xlabel('Actual Growth Rate')
        plt.ylabel('Predicted Growth Rate')
        plt.title(f'Growth Rate Predictions (R² = {growth_r2:.4f})')
        
        plt.subplot(1, 2, 2)
        plt.scatter(y_temp_test, temp_preds, alpha=0.5)
        plt.plot([y_temp_test.min(), y_temp_test.max()], 
                [y_temp_test.min(), y_temp_test.max()], 
                'r--')
        plt.xlabel('Actual Optimal Temperature')
        plt.ylabel('Predicted Optimal Temperature')
        plt.title(f'Temperature Predictions (R² = {temp_r2:.4f})')
        
        plt.tight_layout()
        plt.savefig(os.path.join(viz_dir, 'prediction_scatter.png'))
        
        # Create feature importance plots for both models
        # Growth rate
        growth_importance = pd.DataFrame(growth_model.feature_importances)
        growth_importance_avg = growth_importance.mean(axis=1).sort_values(ascending=False).head(20)
        
        plt.figure(figsize=(12, 6))
        growth_importance_avg.plot(kind='barh')
        plt.title('Top 20 Features for Growth Rate Prediction')
        plt.tight_layout()
        plt.savefig(os.path.join(viz_dir, 'growth_feature_importance.png'))
        
        # Temperature
        temp_importance = pd.Series(
            temp_rf_model['model'].feature_importances_, 
            index=temp_test_data.columns
        ).sort_values(ascending=False).head(20)
        
        plt.figure(figsize=(12, 6))
        temp_importance.plot(kind='barh')
        plt.title('Top 20 Features for Temperature Prediction')
        plt.tight_layout()
        plt.savefig(os.path.join(viz_dir, 'temp_feature_importance.png'))
        
        # Save results
        with open(os.path.join(output_dir, 'test_results.txt'), 'w') as f:
            f.write(f"Growth Rate - Test R²: {growth_r2:.4f}, RMSE: {growth_rmse:.4f}, MAE: {growth_mae:.4f}\n")
            f.write(f"Optimal Temperature - Test R²: {temp_r2:.4f}, RMSE: {temp_rmse:.4f}, MAE: {temp_mae:.4f}\n")
        
        return results
    
    # Evaluate models
    logger.info("Evaluating models...")
    results = evaluate_rf_models(
        growth_model=growth_model, 
        temp_rf_model=temp_model, 
        data_dict=data_dict,
        output_dir=args.output_dir
    )
    
    logger.info(f"Model training and evaluation completed. Results saved to {args.output_dir}")

if __name__ == "__main__":
    main() 