#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Hybrid Growth Rate Model with Neural Network Component.
This module extends the FinalGrowthRateModel with a neural network component
and learnable ensemble weights.
"""

import os
import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Tuple, Optional
import joblib
import torch
import torch.nn as nn
import torch.optim as optim
from sklearn.preprocessing import RobustScaler
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error

from final_growth_rate_model import FinalGrowthRateModel
from neural_network_component import NeuralNetworkComponent

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class HybridGrowthRateModel:
    """
    Hybrid Growth Rate Model that combines ensemble methods with a neural network.
    """
    def __init__(
        self,
        nn_hidden_dim: int = 128,
        nn_dropout: float = 0.3,
        nn_lr: float = 0.001,
        nn_batch_size: int = 64,
        nn_epochs: int = 100,
        nn_patience: int = 10,
        ensemble_weight_lr: float = 0.01,
        ensemble_weight_epochs: int = 50
    ):
        """
        Initialize hybrid model.
        
        Args:
            nn_hidden_dim: Size of hidden layers in neural network
            nn_dropout: Dropout rate for neural network
            nn_lr: Learning rate for neural network
            nn_batch_size: Batch size for neural network training
            nn_epochs: Maximum number of epochs for neural network
            nn_patience: Patience for early stopping in neural network
            ensemble_weight_lr: Learning rate for ensemble weight optimization
            ensemble_weight_epochs: Number of epochs for ensemble weight optimization
        """
        self.nn_hidden_dim = nn_hidden_dim
        self.nn_dropout = nn_dropout
        self.nn_lr = nn_lr
        self.nn_batch_size = nn_batch_size
        self.nn_epochs = nn_epochs
        self.nn_patience = nn_patience
        self.ensemble_weight_lr = ensemble_weight_lr
        self.ensemble_weight_epochs = ensemble_weight_epochs
        
        # Initialize components
        self.ensemble_model = FinalGrowthRateModel()
        self.nn_model = NeuralNetworkComponent(
            hidden_dim=nn_hidden_dim,
            dropout=nn_dropout,
            lr=nn_lr,
            batch_size=nn_batch_size,
            epochs=nn_epochs,
            patience=nn_patience
        )
        
        # Initialize ensemble weights
        self.ensemble_weights = nn.Parameter(torch.tensor([0.5, 0.5], dtype=torch.float32))
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # Initialize feature filtering parameters
        self.variance_percentile = 25  # Remove bottom 25% by variance
        
    def filter_low_variance_features(self, X: pd.DataFrame, percentile: float = 25) -> pd.DataFrame:
        """
        Filter out low-variance features.
        
        Args:
            X: Feature DataFrame
            percentile: Percentile threshold for variance filtering
            
        Returns:
            Filtered feature DataFrame
        """
        # Calculate variance for each feature
        variances = X.var()
        
        # Determine threshold
        threshold = np.percentile(variances, percentile)
        
        # Select high-variance features
        high_var_cols = variances[variances > threshold].index.tolist()
        
        logger.info(f"Removed {X.shape[1] - len(high_var_cols)} low-variance features (below {percentile}th percentile)")
        
        return X[high_var_cols]
    
    def fit(
        self, 
        X_train: pd.DataFrame, 
        y_train: pd.Series, 
        X_val: pd.DataFrame, 
        y_val: pd.Series,
        n_features: int = 150,
        use_interactions: bool = True,
        output_dir: Optional[str] = None
    ) -> None:
        """
        Fit hybrid model to data.
        
        Args:
            X_train: Training features
            y_train: Training targets
            X_val: Validation features
            y_val: Validation targets
            n_features: Number of features to select
            use_interactions: Whether to use interaction features
            output_dir: Directory to save outputs
        """
        # Step 1: Apply more aggressive variance filtering
        X_train_filtered = self.filter_low_variance_features(X_train, self.variance_percentile)
        X_val_filtered = X_val[X_train_filtered.columns]
        
        # Step 2: Train ensemble model
        logger.info("Training ensemble model component...")
        self.ensemble_model.fit(
            X_train_filtered, 
            y_train, 
            X_val_filtered, 
            y_val,
            n_features=n_features,
            use_interactions=use_interactions
        )
        
        # Step 3: Train neural network model
        logger.info("Training neural network component...")
        self.nn_model.fit(X_train_filtered, y_train, X_val_filtered, y_val)
        
        # Step 4: Optimize ensemble weights
        logger.info("Optimizing ensemble weights...")
        self._optimize_ensemble_weights(X_val_filtered, y_val)
        
        # Save model if output_dir is provided
        if output_dir:
            self.save(output_dir)
    
    def _optimize_ensemble_weights(self, X_val: pd.DataFrame, y_val: pd.Series) -> None:
        """
        Optimize ensemble weights using validation data.
        
        Args:
            X_val: Validation features
            y_val: Validation targets
        """
        # Get predictions from both models
        ensemble_preds = self.ensemble_model.predict(X_val)
        nn_preds = self.nn_model.predict(X_val)
        
        # Convert to tensors
        ensemble_preds_tensor = torch.tensor(ensemble_preds, dtype=torch.float32).to(self.device)
        nn_preds_tensor = torch.tensor(nn_preds, dtype=torch.float32).to(self.device)
        y_val_tensor = torch.tensor(y_val.values, dtype=torch.float32).to(self.device)
        
        # Initialize weights parameter
        weights = nn.Parameter(torch.tensor([0.5, 0.5], dtype=torch.float32).to(self.device))
        
        # Initialize optimizer
        optimizer = optim.Adam([weights], lr=self.ensemble_weight_lr)
        
        # Initialize loss function
        criterion = nn.MSELoss()
        
        # Training loop
        best_loss = float('inf')
        best_weights = weights.clone()
        
        for epoch in range(self.ensemble_weight_epochs):
            # Zero gradients
            optimizer.zero_grad()
            
            # Apply softmax to ensure weights sum to 1
            normalized_weights = torch.softmax(weights, dim=0)
            
            # Combine predictions
            combined_preds = (
                normalized_weights[0] * ensemble_preds_tensor + 
                normalized_weights[1] * nn_preds_tensor
            )
            
            # Calculate loss
            loss = criterion(combined_preds, y_val_tensor)
            
            # Backward pass and optimization
            loss.backward()
            optimizer.step()
            
            # Track best weights
            if loss.item() < best_loss:
                best_loss = loss.item()
                best_weights = normalized_weights.clone().detach()
            
            if (epoch + 1) % 10 == 0:
                logger.info(f"Epoch {epoch+1}/{self.ensemble_weight_epochs}: Loss: {loss.item():.6f}, Weights: {normalized_weights.cpu().detach().numpy()}")
        
        # Set final weights
        self.ensemble_weights = best_weights
        logger.info(f"Final ensemble weights: {self.ensemble_weights.cpu().numpy()}")
        
        # Calculate R² with optimized weights
        combined_preds = (
            self.ensemble_weights[0].item() * ensemble_preds + 
            self.ensemble_weights[1].item() * nn_preds
        )
        r2 = r2_score(y_val, combined_preds)
        logger.info(f"Validation R² with optimized weights: {r2:.4f}")
    
    def predict(self, X: pd.DataFrame) -> np.ndarray:
        """
        Generate predictions.
        
        Args:
            X: Features
            
        Returns:
            Predictions
        """
        # Filter features to match training data
        if hasattr(self, 'ensemble_model') and hasattr(self.ensemble_model, 'feature_names'):
            common_cols = [col for col in self.ensemble_model.feature_names if col in X.columns]
            X = X[common_cols]
        
        # Get predictions from both models
        ensemble_preds = self.ensemble_model.predict(X)
        nn_preds = self.nn_model.predict(X)
        
        # Combine predictions using learned weights
        weights = self.ensemble_weights.cpu().numpy()
        combined_preds = weights[0] * ensemble_preds + weights[1] * nn_preds
        
        return combined_preds
    
    def evaluate(self, X: pd.DataFrame, y: pd.Series) -> Dict[str, float]:
        """
        Evaluate model on data.
        
        Args:
            X: Features
            y: Target
            
        Returns:
            Dictionary of evaluation metrics
        """
        # Generate predictions
        y_pred = self.predict(X)
        
        # Calculate metrics
        metrics = {
            'R2': r2_score(y, y_pred),
            'RMSE': np.sqrt(mean_squared_error(y, y_pred)),
            'MAE': mean_absolute_error(y, y_pred)
        }
        
        # Log metrics
        logger.info(f"Evaluation metrics - R²: {metrics['R2']:.4f}, RMSE: {metrics['RMSE']:.4f}, MAE: {metrics['MAE']:.4f}")
        logger.info("Note: RMSE and MAE are in scaled units if target scaling was applied")
        
        return metrics
    
    def save(self, output_dir: str) -> None:
        """
        Save model to disk.
        
        Args:
            output_dir: Directory to save model
        """
        os.makedirs(output_dir, exist_ok=True)
        
        # Save ensemble model
        ensemble_dir = os.path.join(output_dir, 'ensemble_model')
        os.makedirs(ensemble_dir, exist_ok=True)
        self.ensemble_model.save(ensemble_dir)
        
        # Save neural network model
        nn_dir = os.path.join(output_dir, 'nn_model')
        os.makedirs(nn_dir, exist_ok=True)
        self.nn_model.save(nn_dir)
        
        # Save ensemble weights
        weights_path = os.path.join(output_dir, 'ensemble_weights.pth')
        torch.save(self.ensemble_weights, weights_path)
        
        # Save model configuration
        config = {
            'nn_hidden_dim': self.nn_hidden_dim,
            'nn_dropout': self.nn_dropout,
            'nn_lr': self.nn_lr,
            'nn_batch_size': self.nn_batch_size,
            'nn_epochs': self.nn_epochs,
            'nn_patience': self.nn_patience,
            'ensemble_weight_lr': self.ensemble_weight_lr,
            'ensemble_weight_epochs': self.ensemble_weight_epochs,
            'variance_percentile': self.variance_percentile
        }
        joblib.dump(config, os.path.join(output_dir, 'config.joblib'))
        
        logger.info(f"Hybrid model saved to {output_dir}")
    
    def load(self, input_dir: str) -> 'HybridGrowthRateModel':
        """
        Load model from disk.
        
        Args:
            input_dir: Directory to load model from
            
        Returns:
            Self
        """
        # Load ensemble model
        ensemble_dir = os.path.join(input_dir, 'ensemble_model')
        self.ensemble_model.load(ensemble_dir)
        
        # Load neural network model
        nn_dir = os.path.join(input_dir, 'nn_model')
        self.nn_model.load(nn_dir)
        
        # Load ensemble weights
        weights_path = os.path.join(input_dir, 'ensemble_weights.pth')
        self.ensemble_weights = torch.load(weights_path, map_location=self.device)
        
        # Load model configuration
        config = joblib.load(os.path.join(input_dir, 'config.joblib'))
        self.nn_hidden_dim = config['nn_hidden_dim']
        self.nn_dropout = config['nn_dropout']
        self.nn_lr = config['nn_lr']
        self.nn_batch_size = config['nn_batch_size']
        self.nn_epochs = config['nn_epochs']
        self.nn_patience = config['nn_patience']
        self.ensemble_weight_lr = config['ensemble_weight_lr']
        self.ensemble_weight_epochs = config['ensemble_weight_epochs']
        self.variance_percentile = config['variance_percentile']
        
        logger.info(f"Hybrid model loaded from {input_dir}")
        
        return self
