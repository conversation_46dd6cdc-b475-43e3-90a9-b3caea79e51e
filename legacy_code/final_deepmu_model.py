#!/usr/bin/env python3
# DeepMu Final Model: Combined RL training with calibration
# This script trains the reinforcement learning temperature model
# and applies appropriate calibrations to both growth rate and
# temperature predictions

import os
import argparse
import numpy as np
import pandas as pd
import torch
import matplotlib.pyplot as plt
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LinearRegression
import joblib
import logging
import time
import shutil

# Import the FinalHybridModel for growth rate prediction
from final_hybrid_model import FinalHybridModel, GrowthRateModel

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description="DeepMu Final Model Training and Calibration")
    
    # Data arguments
    parser.add_argument("--feature_file", type=str, default="training_data/combined_features.tsv",
                      help="Path to feature file")
    parser.add_argument("--metadata_file", type=str, default="training_data/metadata.tsv",
                      help="Path to metadata file")
    parser.add_argument("--output_dir", type=str, default="models/final_model",
                      help="Output directory for models and results")
    parser.add_argument("--test_size", type=float, default=0.15,
                      help="Proportion of data to use for testing")
    parser.add_argument("--val_size", type=float, default=0.15,
                      help="Proportion of training data to use for validation")
    
    # Training arguments
    parser.add_argument("--n_growth_features", type=int, default=250,
                      help="Number of growth rate features to use")
    parser.add_argument("--n_temp_features", type=int, default=700,
                      help="Number of temperature features to use")
    parser.add_argument("--seed", type=int, default=42,
                      help="Random seed for reproducibility")
    parser.add_argument("--batch_size", type=int, default=64,
                      help="Batch size for training")
    parser.add_argument("--epochs", type=int, default=400,
                      help="Number of epochs for training")
    
    # RL temperature model arguments
    parser.add_argument("--rl_learning_rate", type=float, default=0.001,
                      help="Learning rate for RL model")
    parser.add_argument("--rl_hidden_size", type=int, default=256,
                      help="Hidden size for RL temperature model")
    parser.add_argument("--rl_attention_heads", type=int, default=4,
                      help="Number of attention heads in RL model")
    
    # Growth model arguments
    parser.add_argument("--use_hybrid_growth", type=bool, default=True,
                     help="Use hybrid model for growth rate prediction")
    
    # Mode arguments
    parser.add_argument("--train_only", action="store_true",
                      help="Only train the model, don't evaluate")
    parser.add_argument("--predict_only", action="store_true",
                      help="Only predict using existing model")
    parser.add_argument("--input_data", type=str, default=None,
                      help="Input data for prediction (when using --predict_only)")
    parser.add_argument("--model_dir", type=str, default=None,
                      help="Directory with model files for prediction")
    
    return parser.parse_args()

def load_data(feature_file, metadata_file, n_growth_features=250, n_temp_features=700):
    """Load and preprocess data"""
    logger.info(f"Loading data from {feature_file} and {metadata_file}")
    
    # Load feature data
    feature_df = pd.read_csv(feature_file, sep='\t')
    metadata_df = pd.read_csv(metadata_file, sep='\t')
    
    # Merge features and metadata
    merged_df = pd.merge(feature_df, metadata_df, on='genome_id')
    logger.info(f"Loaded {len(merged_df)} samples after merging")
    
    # Extract genome IDs and features
    genome_ids = merged_df['genome_id'].values
    features = merged_df.drop(['genome_id'], axis=1)
    
    # Get feature columns (excluding targets and metadata)
    feature_cols = [col for col in features.columns 
                   if col not in ['growth_rate', 'optimal_temperature', 'temperature_range']]
    
    # Select relevant features based on importance (feature selection done separately)
    growth_features = feature_cols[:n_growth_features]
    temp_features = feature_cols[:n_temp_features]
    
    # Extract targets
    growth_rates = merged_df['growth_rate'].values
    optimal_temps = merged_df['optimal_temperature'].values
    
    logger.info(f"Using {len(growth_features)} growth features and {len(temp_features)} temperature features")
    
    return {
        'genome_ids': genome_ids,
        'feature_df': merged_df[feature_cols], 
        'growth_features': growth_features,
        'temp_features': temp_features,
        'growth_rates': growth_rates,
        'optimal_temps': optimal_temps,
        'full_df': merged_df
    }

def split_data(data, test_size=0.15, val_size=0.15, seed=42):
    """Split data into train, validation, and test sets"""
    feature_df = data['feature_df']
    growth_rates = data['growth_rates']
    optimal_temps = data['optimal_temps']
    genome_ids = data['genome_ids']
    
    # First split: separate test set
    (X_train_val, X_test, 
     y_growth_train_val, y_growth_test,
     y_temp_train_val, y_temp_test,
     ids_train_val, ids_test) = train_test_split(
        feature_df, growth_rates, optimal_temps, genome_ids,
        test_size=test_size, random_state=seed
    )
    
    # Second split: separate validation set from training set
    val_size_adjusted = val_size / (1 - test_size)  # Adjust val_size relative to train_val size
    
    (X_train, X_val, 
     y_growth_train, y_growth_val,
     y_temp_train, y_temp_val,
     ids_train, ids_val) = train_test_split(
        X_train_val, y_growth_train_val, y_temp_train_val, ids_train_val,
        test_size=val_size_adjusted, random_state=seed
    )
    
    logger.info(f"Data split: {len(X_train)} train, {len(X_val)} validation, {len(X_test)} test samples")
    
    return {
        'X_train': X_train, 'X_val': X_val, 'X_test': X_test,
        'y_growth_train': y_growth_train, 'y_growth_val': y_growth_val, 'y_growth_test': y_growth_test,
        'y_temp_train': y_temp_train, 'y_temp_val': y_temp_val, 'y_temp_test': y_temp_test,
        'ids_train': ids_train, 'ids_val': ids_val, 'ids_test': ids_test
    }

def train_models(split_data, data, args):
    """Train the RL temperature model and growth rate model"""
    # Import here to avoid importing if only running prediction
    from temperature_rl_trainer import TemperatureRLTrainer
    from temperature_rl_agent import TemperatureRLAgent, VerifiableRewardFunction
    
    # Create necessary directories
    temp_model_dir = os.path.join(args.output_dir, 'temp_model')
    growth_model_dir = os.path.join(args.output_dir, 'growth_model')
    os.makedirs(temp_model_dir, exist_ok=True)
    os.makedirs(growth_model_dir, exist_ok=True)
    
    # Extract features and targets
    X_train, X_val = split_data['X_train'], split_data['X_val']
    y_temp_train, y_temp_val = split_data['y_temp_train'], split_data['y_temp_val']
    y_growth_train, y_growth_val = split_data['y_growth_train'], split_data['y_growth_val']
    
    temp_features = data['temp_features']
    growth_features = data['growth_features']
    
    # Train temperature model using RL
    logger.info("Training temperature model with RL...")
    
    # Prepare feature data for temperature model
    X_train_temp = X_train[temp_features].values
    X_val_temp = X_val[temp_features].values
    
    # Define input shape for RL model
    input_dim = len(temp_features)
    
    # Create RL agent for temperature prediction
    temp_rl_agent = TemperatureRLAgent(
        input_dim=input_dim,
        hidden_dim=args.rl_hidden_size,
        num_heads=args.rl_attention_heads
    )
    
    # Create reward function
    reward_function = VerifiableRewardFunction()
    
    # Setup trainer
    temp_rl_trainer = TemperatureRLTrainer(
        agent=temp_rl_agent,
        reward_function=reward_function,
        learning_rate=args.rl_learning_rate
    )
    
    # Train the model
    temp_rl_trainer.train(
        train_features=X_train_temp,
        train_targets=y_temp_train,
        val_features=X_val_temp, 
        val_targets=y_temp_val,
        batch_size=args.batch_size,
        num_epochs=args.epochs
    )
    
    # Save temperature model
    temp_model_path = os.path.join(temp_model_dir, 'temperature_rl_model.pt')
    torch.save(temp_rl_agent.state_dict(), temp_model_path)
    
    # Train growth rate model - use either hybrid model or simple model
    if args.use_hybrid_growth:
        logger.info("Training growth rate model using FinalHybridModel...")
        
        # Create a standalone GrowthRateModel for superior performance
        growth_model = GrowthRateModel(output_dir=growth_model_dir)
        
        # Prepare data for growth rate model
        X_train_growth = X_train[growth_features]
        X_val_growth = X_val[growth_features]
        
        # Train growth rate model 
        growth_model.fit(
            X_train=X_train_growth,
            y_train=y_growth_train,
            X_val=X_val_growth,
            y_val=y_growth_val
        )
        
        # Save growth model (handled internally by GrowthRateModel)
        growth_model_path = growth_model_dir
        
    else:
        logger.info("Training simple growth rate model (Linear Regression)...")
        
        # Use a simpler model for growth rate (Linear Regression)
        X_train_growth = X_train[growth_features].values
        growth_model = LinearRegression()
        growth_model.fit(X_train_growth, y_growth_train)
        
        # Save growth model
        growth_model_path = os.path.join(growth_model_dir, 'growth_model.joblib')
        joblib.dump(growth_model, growth_model_path)
    
    logger.info("Models trained and saved successfully")
    
    # Return the trained models
    return {
        'temp_rl_agent': temp_rl_agent,
        'growth_model': growth_model,
        'temp_model_path': temp_model_path,
        'growth_model_path': growth_model_path,
        'use_hybrid_growth': args.use_hybrid_growth
    }

def calibrate_models(models, split_data, data, args):
    """Calibrate the trained models using appropriate methods"""
    logger.info("Calibrating models...")
    
    # Extract data
    X_train, X_val, X_test = split_data['X_train'], split_data['X_val'], split_data['X_test']
    y_temp_train, y_temp_val, y_temp_test = split_data['y_temp_train'], split_data['y_temp_val'], split_data['y_temp_test']
    y_growth_train, y_growth_val, y_growth_test = split_data['y_growth_train'], split_data['y_growth_val'], split_data['y_growth_test']
    
    temp_features = data['temp_features']
    growth_features = data['growth_features']
    
    # Extract models
    temp_rl_agent = models['temp_rl_agent']
    growth_model = models['growth_model']
    use_hybrid_growth = models.get('use_hybrid_growth', False)
    
    # Get predictions on training data
    X_train_temp = X_train[temp_features].values
    X_train_growth = X_train[growth_features]
    
    # Generate predictions for temperature
    temp_rl_agent.eval()
    with torch.no_grad():
        temp_train_preds, _ = temp_rl_agent(torch.FloatTensor(X_train_temp))
        temp_train_preds = temp_train_preds.numpy()
    
    # Generate predictions for growth rate
    if use_hybrid_growth:
        growth_train_preds = growth_model.predict(X_train_growth)
    else:
        growth_train_preds = growth_model.predict(X_train_growth.values)
    
    # Calibrate temperature model using linear regression
    logger.info("Calibrating temperature model with linear regression...")
    temp_calibration_model = LinearRegression()
    temp_calibration_model.fit(
        temp_train_preds.reshape(-1, 1),
        y_temp_train
    )
    
    # Save temperature calibration model
    temp_calibration_path = os.path.join(args.output_dir, 'temp_model', 'temp_calibration.joblib')
    joblib.dump(temp_calibration_model, temp_calibration_path)
    
    # For hybrid growth model, calibration may not be necessary due to its high accuracy
    # But we'll still save a reference distribution for potential quantile mapping
    logger.info("Calibrating growth rate model...")
    
    # Sort training growth rates for quantile mapping reference
    actual_sorted_growth = np.sort(y_growth_train)
    
    # Save growth quantile mapping reference distribution
    growth_mapping_path = os.path.join(args.output_dir, 'growth_model', 'growth_quantile_mapping.npy')
    np.save(growth_mapping_path, actual_sorted_growth)
    
    # Return calibration models
    return {
        'temp_calibration_model': temp_calibration_model,
        'actual_sorted_growth': actual_sorted_growth,
        'temp_calibration_path': temp_calibration_path,
        'growth_mapping_path': growth_mapping_path
    }

def apply_calibration(raw_predictions, calibration_models, target_type, use_hybrid_growth=False):
    """Apply calibration to raw predictions"""
    if target_type == 'temperature':
        # Apply linear regression calibration for temperature
        temp_calibration_model = calibration_models['temp_calibration_model']
        return temp_calibration_model.predict(raw_predictions.reshape(-1, 1))
    elif target_type == 'growth':
        # For hybrid growth model with high accuracy, may not need full calibration
        if use_hybrid_growth:
            # Apply minimal calibration if needed or just return predictions
            return raw_predictions
        else:
            # Apply quantile mapping for simple growth rate model
            actual_sorted_growth = calibration_models['actual_sorted_growth']
            
            # Calculate percentile ranks of predictions
            pred_ranks = pd.Series(raw_predictions).rank(pct=True).values
            
            # Map to the corresponding percentile in reference distribution
            calibrated_preds = np.zeros_like(pred_ranks)
            for i, rank in enumerate(pred_ranks):
                idx = int(rank * (len(actual_sorted_growth) - 1))
                calibrated_preds[i] = actual_sorted_growth[idx]
                
            return calibrated_preds
    else:
        raise ValueError(f"Unknown target type: {target_type}")

def evaluate_models(models, calibration_models, split_data, data, args):
    """Evaluate models on train, validation and test sets"""
    logger.info("Evaluating models...")
    
    # Extract data
    X_train, X_val, X_test = split_data['X_train'], split_data['X_val'], split_data['X_test']
    y_temp_train, y_temp_val, y_temp_test = split_data['y_temp_train'], split_data['y_temp_val'], split_data['y_temp_test']
    y_growth_train, y_growth_val, y_growth_test = split_data['y_growth_train'], split_data['y_growth_val'], split_data['y_growth_test']
    
    temp_features = data['temp_features']
    growth_features = data['growth_features']
    
    # Extract models
    temp_rl_agent = models['temp_rl_agent']
    growth_model = models['growth_model']
    use_hybrid_growth = models.get('use_hybrid_growth', False)
    
    # Set up results dictionary
    results = {
        'train': {}, 'val': {}, 'test': {},
        'calibrated_train': {}, 'calibrated_val': {}, 'calibrated_test': {}
    }
    
    # Function to evaluate a dataset
    def evaluate_dataset(X_data, y_temp_data, y_growth_data, dataset_name):
        # Prepare data
        X_temp = X_data[temp_features].values
        X_growth = X_data[growth_features]
        
        # Get temperature predictions
        temp_rl_agent.eval()
        with torch.no_grad():
            temp_preds, _ = temp_rl_agent(torch.FloatTensor(X_temp))
            temp_preds = temp_preds.numpy()
        
        # Get growth predictions
        if use_hybrid_growth:
            growth_preds = growth_model.predict(X_growth)
        else:
            growth_preds = growth_model.predict(X_growth.values)
        
        # Calculate raw metrics
        temp_r2 = r2_score(y_temp_data, temp_preds)
        temp_rmse = np.sqrt(mean_squared_error(y_temp_data, temp_preds))
        temp_mae = mean_absolute_error(y_temp_data, temp_preds)
        
        growth_r2 = r2_score(y_growth_data, growth_preds)
        growth_rmse = np.sqrt(mean_squared_error(y_growth_data, growth_preds))
        growth_mae = mean_absolute_error(y_growth_data, growth_preds)
        
        # Spearman correlation (rank correlation)
        growth_spearman = pd.Series(growth_preds).corr(pd.Series(y_growth_data), method='spearman')
        temp_spearman = pd.Series(temp_preds).corr(pd.Series(y_temp_data), method='spearman')
        
        # Store raw metrics
        results[dataset_name]['temp_r2'] = temp_r2
        results[dataset_name]['temp_rmse'] = temp_rmse
        results[dataset_name]['temp_mae'] = temp_mae
        results[dataset_name]['temp_spearman'] = temp_spearman
        
        results[dataset_name]['growth_r2'] = growth_r2
        results[dataset_name]['growth_rmse'] = growth_rmse
        results[dataset_name]['growth_mae'] = growth_mae
        results[dataset_name]['growth_spearman'] = growth_spearman
        
        # Apply calibration
        calibrated_temp = apply_calibration(temp_preds, calibration_models, 'temperature')
        calibrated_growth = apply_calibration(growth_preds, calibration_models, 'growth', use_hybrid_growth)
        
        # Calculate calibrated metrics
        cal_temp_r2 = r2_score(y_temp_data, calibrated_temp)
        cal_temp_rmse = np.sqrt(mean_squared_error(y_temp_data, calibrated_temp))
        cal_temp_mae = mean_absolute_error(y_temp_data, calibrated_temp)
        
        cal_growth_r2 = r2_score(y_growth_data, calibrated_growth)
        cal_growth_rmse = np.sqrt(mean_squared_error(y_growth_data, calibrated_growth))
        cal_growth_mae = mean_absolute_error(y_growth_data, calibrated_growth)
        
        # Store calibrated metrics
        dataset_name_cal = f"calibrated_{dataset_name}"
        results[dataset_name_cal]['temp_r2'] = cal_temp_r2
        results[dataset_name_cal]['temp_rmse'] = cal_temp_rmse
        results[dataset_name_cal]['temp_mae'] = cal_temp_mae
        results[dataset_name_cal]['temp_spearman'] = temp_spearman  # Same as raw
        
        results[dataset_name_cal]['growth_r2'] = cal_growth_r2
        results[dataset_name_cal]['growth_rmse'] = cal_growth_rmse
        results[dataset_name_cal]['growth_mae'] = cal_growth_mae
        results[dataset_name_cal]['growth_spearman'] = growth_spearman  # Same as raw
        
        return {
            'temp_preds': temp_preds,
            'growth_preds': growth_preds,
            'calibrated_temp': calibrated_temp,
            'calibrated_growth': calibrated_growth
        }
    
    # Evaluate on train, validation, and test sets
    train_preds = evaluate_dataset(X_train, y_temp_train, y_growth_train, 'train')
    val_preds = evaluate_dataset(X_val, y_temp_val, y_growth_val, 'val')
    test_preds = evaluate_dataset(X_test, y_temp_test, y_growth_test, 'test')
    
    # Save results
    results_path = os.path.join(args.output_dir, 'evaluation_results.joblib')
    joblib.dump(results, results_path)
    
    # Log results
    logger.info("Evaluation results:")
    for dataset in ['train', 'val', 'test']:
        logger.info(f"\n{dataset.upper()} SET RESULTS (RAW):")
        logger.info(f"Temperature: R²={results[dataset]['temp_r2']:.4f}, RMSE={results[dataset]['temp_rmse']:.4f}, Spearman={results[dataset]['temp_spearman']:.4f}")
        logger.info(f"Growth Rate: R²={results[dataset]['growth_r2']:.4f}, RMSE={results[dataset]['growth_rmse']:.4f}, Spearman={results[dataset]['growth_spearman']:.4f}")
        
        calibrated_dataset = f"calibrated_{dataset}"
        logger.info(f"\n{dataset.upper()} SET RESULTS (CALIBRATED):")
        logger.info(f"Temperature: R²={results[calibrated_dataset]['temp_r2']:.4f}, RMSE={results[calibrated_dataset]['temp_rmse']:.4f}")
        logger.info(f"Growth Rate: R²={results[calibrated_dataset]['growth_r2']:.4f}, RMSE={results[calibrated_dataset]['growth_rmse']:.4f}")
    
    # Generate and save visualizations
    create_evaluation_plots(
        X_test, y_temp_test, y_growth_test, 
        test_preds['temp_preds'], test_preds['growth_preds'],
        test_preds['calibrated_temp'], test_preds['calibrated_growth'],
        args.output_dir
    )
    
    return results

def create_evaluation_plots(X_test, y_temp_test, y_growth_test, temp_preds, growth_preds, 
                           cal_temp_preds, cal_growth_preds, output_dir):
    """Create evaluation visualizations"""
    plot_dir = os.path.join(output_dir, 'plots')
    os.makedirs(plot_dir, exist_ok=True)
    
    # Temperature scatter plots (raw vs calibrated)
    plt.figure(figsize=(12, 6))
    
    # Raw temperature predictions
    plt.subplot(1, 2, 1)
    plt.scatter(y_temp_test, temp_preds, alpha=0.5)
    plt.xlabel('Actual Optimal Temperature')
    plt.ylabel('Predicted Temperature (Raw)')
    plt.title(f'Raw Temperature Predictions\nR²: {r2_score(y_temp_test, temp_preds):.4f}')
    plt.grid(alpha=0.3)
    
    # Add perfect prediction line
    min_val = min(min(y_temp_test), min(temp_preds))
    max_val = max(max(y_temp_test), max(temp_preds))
    plt.plot([min_val, max_val], [min_val, max_val], 'r--')
    
    # Calibrated temperature predictions
    plt.subplot(1, 2, 2)
    plt.scatter(y_temp_test, cal_temp_preds, alpha=0.5)
    plt.xlabel('Actual Optimal Temperature')
    plt.ylabel('Predicted Temperature (Calibrated)')
    plt.title(f'Calibrated Temperature Predictions\nR²: {r2_score(y_temp_test, cal_temp_preds):.4f}')
    plt.grid(alpha=0.3)
    
    # Add perfect prediction line
    min_val = min(min(y_temp_test), min(cal_temp_preds))
    max_val = max(max(y_temp_test), max(cal_temp_preds))
    plt.plot([min_val, max_val], [min_val, max_val], 'r--')
    
    plt.tight_layout()
    plt.savefig(os.path.join(plot_dir, 'temperature_predictions.png'), dpi=300)
    plt.close()
    
    # Growth rate scatter plots (raw vs calibrated)
    plt.figure(figsize=(12, 6))
    
    # Raw growth predictions
    plt.subplot(1, 2, 1)
    plt.scatter(y_growth_test, growth_preds, alpha=0.5)
    plt.xlabel('Actual Growth Rate')
    plt.ylabel('Predicted Growth Rate (Raw)')
    plt.title(f'Raw Growth Rate Predictions\nR²: {r2_score(y_growth_test, growth_preds):.4f}')
    plt.grid(alpha=0.3)
    
    # Calibrated growth predictions
    plt.subplot(1, 2, 2)
    plt.scatter(y_growth_test, cal_growth_preds, alpha=0.5)
    plt.xlabel('Actual Growth Rate')
    plt.ylabel('Predicted Growth Rate (Calibrated)')
    plt.title(f'Calibrated Growth Rate Predictions\nR²: {r2_score(y_growth_test, cal_growth_preds):.4f}')
    plt.grid(alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(os.path.join(plot_dir, 'growth_rate_predictions.png'), dpi=300)
    plt.close()
    
    # Create log-space visualization for growth rate
    plt.figure(figsize=(12, 6))
    
    # Handle zeros and very small values
    offset = 1e-10
    
    # Take log of actual growth rates
    log_actual = np.log10(y_growth_test + offset)
    
    # Take log of raw predicted growth rates
    log_raw_preds = np.log10(np.abs(growth_preds) + offset)
    
    # Take log of calibrated growth rates
    log_cal_preds = np.log10(cal_growth_preds + offset)
    
    # Raw predictions in log space
    plt.subplot(1, 2, 1)
    plt.scatter(log_actual, log_raw_preds, alpha=0.5)
    plt.xlabel('Log10(Actual Growth Rate)')
    plt.ylabel('Log10(Predicted Growth Rate) - Raw')
    plt.title('Log-Space Growth Rate - Raw')
    plt.grid(alpha=0.3)
    
    # Calibrated predictions in log space
    plt.subplot(1, 2, 2)
    plt.scatter(log_actual, log_cal_preds, alpha=0.5)
    plt.xlabel('Log10(Actual Growth Rate)')
    plt.ylabel('Log10(Predicted Growth Rate) - Calibrated')
    plt.title('Log-Space Growth Rate - Calibrated')
    plt.grid(alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(os.path.join(plot_dir, 'growth_rate_log_space.png'), dpi=300)
    plt.close()
    
    # Create rank-based visualizations
    plt.figure(figsize=(12, 6))
    
    # Temperature ranks
    plt.subplot(1, 2, 1)
    temp_actual_ranks = pd.Series(y_temp_test).rank(pct=True)
    temp_pred_ranks = pd.Series(temp_preds).rank(pct=True)
    
    plt.scatter(temp_actual_ranks, temp_pred_ranks, alpha=0.5)
    plt.plot([0, 1], [0, 1], 'r--')
    plt.xlabel('Actual Temperature Percentile')
    plt.ylabel('Predicted Temperature Percentile')
    plt.title(f'Temperature Rank Correlation\nSpearman: {temp_actual_ranks.corr(temp_pred_ranks, method="spearman"):.4f}')
    plt.grid(alpha=0.3)
    
    # Growth rate ranks
    plt.subplot(1, 2, 2)
    growth_actual_ranks = pd.Series(y_growth_test).rank(pct=True)
    growth_pred_ranks = pd.Series(growth_preds).rank(pct=True)
    
    plt.scatter(growth_actual_ranks, growth_pred_ranks, alpha=0.5)
    plt.plot([0, 1], [0, 1], 'r--')
    plt.xlabel('Actual Growth Rate Percentile')
    plt.ylabel('Predicted Growth Rate Percentile')
    plt.title(f'Growth Rate Rank Correlation\nSpearman: {growth_actual_ranks.corr(growth_pred_ranks, method="spearman"):.4f}')
    plt.grid(alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(os.path.join(plot_dir, 'rank_correlations.png'), dpi=300)
    plt.close()

def predict_with_models(models, calibration_models, input_data, args):
    """Make predictions using trained and calibrated models"""
    logger.info(f"Making predictions on {input_data}...")
    
    # Load input features
    feature_df = pd.read_csv(input_data, sep='\t')
    
    # Extract genome IDs
    genome_ids = feature_df['genome_id'].values
    
    # Extract models
    temp_rl_agent = models['temp_rl_agent']
    growth_model = models['growth_model']
    use_hybrid_growth = models.get('use_hybrid_growth', False)
    
    # Get feature lists
    temp_features_file = os.path.join(args.model_dir, 'temp_model', 'temp_features.txt')
    growth_features_file = os.path.join(args.model_dir, 'growth_model', 'growth_features.txt')
    
    with open(temp_features_file, 'r') as f:
        temp_features = [line.strip() for line in f]
    
    with open(growth_features_file, 'r') as f:
        growth_features = [line.strip() for line in f]
    
    # Prepare feature data
    X_temp = feature_df[temp_features].values
    X_growth = feature_df[growth_features]
    
    # Get temperature predictions
    temp_rl_agent.eval()
    with torch.no_grad():
        temp_preds, _ = temp_rl_agent(torch.FloatTensor(X_temp))
        temp_preds = temp_preds.numpy()
    
    # Get growth predictions based on model type
    if use_hybrid_growth:
        growth_preds = growth_model.predict(X_growth)
    else:
        growth_preds = growth_model.predict(X_growth.values)
    
    # Apply calibrations
    calibrated_temp = apply_calibration(temp_preds, calibration_models, 'temperature')
    calibrated_growth = apply_calibration(growth_preds, calibration_models, 'growth', use_hybrid_growth)
    
    # Create predictions dataframe
    predictions_df = pd.DataFrame({
        'genome_id': genome_ids,
        'growth_rate': calibrated_growth,
        'optimal_temperature': calibrated_temp
    })
    
    # Save predictions
    output_file = os.path.join(args.output_dir, 'predictions.tsv')
    predictions_df.to_csv(output_file, sep='\t', index=False)
    
    logger.info(f"Predictions saved to {output_file}")
    
    return predictions_df

def load_trained_models(model_dir):
    """Load trained models from the specified directory"""
    logger.info(f"Loading trained models from {model_dir}...")
    
    # Check if model directory exists
    if not os.path.exists(model_dir):
        raise ValueError(f"Model directory {model_dir} does not exist")
    
    # Check if using hybrid growth model
    use_hybrid_growth = os.path.exists(os.path.join(model_dir, 'growth_model', 'growth_ensemble_weights.npy'))
    
    # Import temperature RL model class
    from temperature_rl_agent import TemperatureRLAgent
    
    # Load temperature features
    temp_features_file = os.path.join(model_dir, 'temp_model', 'temp_features.txt')
    with open(temp_features_file, 'r') as f:
        temp_features = [line.strip() for line in f]
    
    # Create and load temperature model
    input_dim = len(temp_features)
    temp_rl_agent = TemperatureRLAgent(input_dim=input_dim)
    temp_model_path = os.path.join(model_dir, 'temp_model', 'temperature_rl_model.pt')
    temp_rl_agent.load_state_dict(torch.load(temp_model_path))
    temp_rl_agent.eval()
    
    # Load appropriate growth model based on type
    if use_hybrid_growth:
        # Load the GrowthRateModel
        from final_hybrid_model import GrowthRateModel
        growth_model = GrowthRateModel()
        growth_model.load(os.path.join(model_dir, 'growth_model'))
        growth_model_path = os.path.join(model_dir, 'growth_model')
    else:
        # Load the simple growth model
        growth_model_path = os.path.join(model_dir, 'growth_model', 'growth_model.joblib')
        growth_model = joblib.load(growth_model_path)
    
    # Load calibration models
    temp_calibration_path = os.path.join(model_dir, 'temp_model', 'temp_calibration.joblib')
    temp_calibration_model = joblib.load(temp_calibration_path)
    
    growth_mapping_path = os.path.join(model_dir, 'growth_model', 'growth_quantile_mapping.npy')
    actual_sorted_growth = np.load(growth_mapping_path)
    
    # Create models dictionary
    models = {
        'temp_rl_agent': temp_rl_agent,
        'growth_model': growth_model,
        'temp_model_path': temp_model_path,
        'growth_model_path': growth_model_path,
        'use_hybrid_growth': use_hybrid_growth
    }
    
    # Create calibration models dictionary
    calibration_models = {
        'temp_calibration_model': temp_calibration_model,
        'actual_sorted_growth': actual_sorted_growth,
        'temp_calibration_path': temp_calibration_path,
        'growth_mapping_path': growth_mapping_path
    }
    
    logger.info("Models loaded successfully")
    
    return models, calibration_models

def save_feature_lists(data, output_dir):
    """Save feature lists to text files"""
    # Save temperature features
    temp_features_file = os.path.join(output_dir, 'temp_model', 'temp_features.txt')
    with open(temp_features_file, 'w') as f:
        for feature in data['temp_features']:
            f.write(f"{feature}\n")
    
    # Save growth features
    growth_features_file = os.path.join(output_dir, 'growth_model', 'growth_features.txt')
    with open(growth_features_file, 'w') as f:
        for feature in data['growth_features']:
            f.write(f"{feature}\n")

def main():
    # Parse command line arguments
    args = parse_args()
    
    # Set random seeds for reproducibility
    np.random.seed(args.seed)
    torch.manual_seed(args.seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(args.seed)
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Run in prediction mode if specified
    if args.predict_only:
        if args.model_dir is None or args.input_data is None:
            raise ValueError("--model_dir and --input_data must be specified when using --predict_only")
        
        # Load trained models
        models, calibration_models = load_trained_models(args.model_dir)
        
        # Make predictions
        predict_with_models(models, calibration_models, args.input_data, args)
        
        return
    
    # Load and preprocess data
    data = load_data(args.feature_file, args.metadata_file, 
                     args.n_growth_features, args.n_temp_features)
    
    # Save feature lists
    save_feature_lists(data, args.output_dir)
    
    # Split data into train, validation, and test sets
    split_data_dict = split_data(data, args.test_size, args.val_size, args.seed)
    
    # Train models
    models = train_models(split_data_dict, data, args)
    
    # Calibrate models
    calibration_models = calibrate_models(models, split_data_dict, data, args)
    
    # Exit if train_only flag is set
    if args.train_only:
        logger.info("Train only mode - skipping evaluation")
        return
    
    # Evaluate models and generate visualizations
    evaluate_models(models, calibration_models, split_data_dict, data, args)
    
    logger.info(f"Model training, calibration, and evaluation complete. Results saved to {args.output_dir}")
    
if __name__ == "__main__":
    start_time = time.time()
    main()
    elapsed_time = time.time() - start_time
    logger.info(f"Total execution time: {elapsed_time:.2f} seconds") 