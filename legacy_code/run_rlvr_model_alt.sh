#!/bin/bash
# Run script for training the RLVR model with alternative hyperparameters

# Make script executable
chmod +x train_rlvr_model.py

# Create output directories
mkdir -p models/rlvr_model_alt
mkdir -p metrics/rlvr_model_alt

# Run training script with alternative hyperparameters
python train_rlvr_model.py \
    --feature-file training_data/combined_features.tsv \
    --metadata training_data/metadata.tsv \
    --output-dir models/rlvr_model_alt \
    --metrics-dir metrics/rlvr_model_alt \
    --hidden-dim 384 \
    --num-heads 6 \
    --num-layers 4 \
    --dropout 0.25 \
    --use-layer-norm \
    --use-residual \
    --use-value-head \
    --activation gelu \
    --alpha 0.5 \
    --beta 0.3 \
    --gamma 0.2 \
    --accuracy-scale 0.8 \
    --lr 1e-3 \
    --weight-decay 5e-5 \
    --batch-size 128 \
    --epochs 150 \
    --early-stopping-patience 15 \
    --entropy-coef 0.02 \
    --value-loss-coef 0.4 \
    --max-grad-norm 1.0 \
    --seed 123 \
    --feature-selection \
    --n-features 75
