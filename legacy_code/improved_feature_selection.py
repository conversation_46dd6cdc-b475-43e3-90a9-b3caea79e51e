#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Improved feature selection module for microbial growth rate and optimal temperature prediction.
This module provides advanced feature selection techniques to improve model performance.
"""

import numpy as np
import pandas as pd
import logging
from typing import List, Dict, Tuple, Union, Optional
from sklearn.ensemble import RandomForestRegressor
from sklearn.feature_selection import SelectFromModel, RFE, VarianceThreshold, mutual_info_regression
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import cross_val_score
from sklearn.metrics import r2_score
import matplotlib.pyplot as plt
import seaborn as sns
import os

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def encode_categorical_features(X: pd.DataFrame) -> pd.DataFrame:
    """
    Encode categorical features using LabelEncoder.
    
    Args:
        X: DataFrame containing features
        
    Returns:
        DataFrame with encoded categorical features
    """
    X_encoded = X.copy()
    categorical_cols = X_encoded.select_dtypes(include=['object', 'category']).columns
    
    for col in categorical_cols:
        le = LabelEncoder()
        X_encoded[col] = le.fit_transform(X_encoded[col].astype(str))
    
    return X_encoded

def select_by_variance(X: pd.DataFrame, threshold: float = 0.01) -> List[str]:
    """
    Select features based on variance threshold.
    
    Args:
        X: DataFrame containing features
        threshold: Variance threshold for feature selection
        
    Returns:
        List of selected feature names
    """
    # Get numeric columns
    numeric_cols = X.select_dtypes(include=[np.number]).columns
    X_numeric = X[numeric_cols]
    
    # Apply variance threshold
    selector = VarianceThreshold(threshold=threshold)
    selector.fit(X_numeric)
    
    # Get selected feature indices
    selected_indices = selector.get_support(indices=True)
    selected_features = X_numeric.columns[selected_indices].tolist()
    
    # Get non-numeric columns
    non_numeric_cols = X.select_dtypes(exclude=[np.number]).columns.tolist()
    
    logger.info(f"Selected {len(selected_features)} features by variance threshold")
    
    return selected_features + non_numeric_cols

def select_by_importance(X: pd.DataFrame, y: pd.Series, n_features: int = 100) -> List[str]:
    """
    Select features based on random forest importance.
    
    Args:
        X: DataFrame containing features
        y: Target variable
        n_features: Number of features to select
        
    Returns:
        List of selected feature names
    """
    # Encode categorical features
    X_encoded = encode_categorical_features(X)
    
    # Train a Random Forest model
    rf = RandomForestRegressor(n_estimators=100, random_state=42, n_jobs=-1)
    rf.fit(X_encoded, y)
    
    # Get feature importance scores
    importance_scores = pd.DataFrame({
        'feature': X_encoded.columns,
        'importance': rf.feature_importances_
    })
    
    # Sort features by importance
    importance_scores = importance_scores.sort_values('importance', ascending=False)
    
    # Select top n_features
    selected_features = importance_scores.head(n_features)['feature'].tolist()
    
    logger.info(f"Selected {len(selected_features)} features by importance")
    
    return selected_features

def select_by_rfe(X: pd.DataFrame, y: pd.Series, n_features: int = 100, step: float = 0.3) -> List[str]:
    """
    Select features using Recursive Feature Elimination (RFE).
    
    Args:
        X: DataFrame containing features
        y: Target variable
        n_features: Number of features to select
        step: Step size for RFE (fraction of features to remove at each iteration)
        
    Returns:
        List of selected feature names
    """
    # Encode categorical features
    X_encoded = encode_categorical_features(X)
    
    # Calculate step size
    step_size = max(1, int(X_encoded.shape[1] * step))
    
    # Create RFE with Random Forest
    rf = RandomForestRegressor(n_estimators=100, random_state=42, n_jobs=-1)
    rfe = RFE(estimator=rf, n_features_to_select=n_features, step=step_size, verbose=0)
    
    # Fit RFE
    rfe.fit(X_encoded, y)
    
    # Get selected feature indices
    selected_indices = rfe.get_support(indices=True)
    selected_features = X_encoded.columns[selected_indices].tolist()
    
    logger.info(f"Selected {len(selected_features)} features by RFE")
    
    return selected_features

def select_by_mutual_info(X: pd.DataFrame, y: pd.Series, n_features: int = 100) -> List[str]:
    """
    Select features using mutual information.
    
    Args:
        X: DataFrame containing features
        y: Target variable
        n_features: Number of features to select
        
    Returns:
        List of selected feature names
    """
    # Encode categorical features
    X_encoded = encode_categorical_features(X)
    
    # Calculate mutual information
    mi_scores = mutual_info_regression(X_encoded, y, random_state=42)
    
    # Create DataFrame with scores
    mi_df = pd.DataFrame({
        'feature': X_encoded.columns,
        'mi_score': mi_scores
    })
    
    # Sort by mutual information score
    mi_df = mi_df.sort_values('mi_score', ascending=False)
    
    # Select top n_features
    selected_features = mi_df.head(n_features)['feature'].tolist()
    
    logger.info(f"Selected {len(selected_features)} features by mutual information")
    
    return selected_features

def evaluate_feature_sets(X: pd.DataFrame, y: pd.Series, feature_sets: Dict[str, List[str]], 
                         cv: int = 5, output_dir: Optional[str] = None) -> Dict[str, float]:
    """
    Evaluate different feature sets using cross-validation.
    
    Args:
        X: DataFrame containing features
        y: Target variable
        feature_sets: Dictionary mapping method names to lists of selected features
        cv: Number of cross-validation folds
        output_dir: Directory to save evaluation results
        
    Returns:
        Dictionary mapping method names to mean R² scores
    """
    results = {}
    
    for method, features in feature_sets.items():
        # Get selected features
        X_selected = X[features]
        
        # Encode categorical features
        X_selected = encode_categorical_features(X_selected)
        
        # Evaluate using Random Forest with cross-validation
        rf = RandomForestRegressor(n_estimators=100, random_state=42, n_jobs=-1)
        cv_scores = cross_val_score(rf, X_selected, y, cv=cv, scoring='r2')
        
        # Store results
        mean_r2 = cv_scores.mean()
        results[method] = mean_r2
        
        logger.info(f"Method: {method}, Mean R²: {mean_r2:.4f}, Features: {len(features)}")
    
    # Create visualization if output_dir is provided
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
        
        # Create bar plot
        plt.figure(figsize=(10, 6))
        methods = list(results.keys())
        scores = [results[method] for method in methods]
        feature_counts = [len(feature_sets[method]) for method in methods]
        
        # Create DataFrame for plotting
        plot_df = pd.DataFrame({
            'Method': methods,
            'R²': scores,
            'Feature Count': feature_counts
        })
        
        # Sort by R² score
        plot_df = plot_df.sort_values('R²', ascending=False)
        
        # Create bar plot
        sns.barplot(x='R²', y='Method', data=plot_df)
        plt.title('Feature Selection Method Comparison')
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'feature_selection_comparison.png'))
        
        # Save results to CSV
        plot_df.to_csv(os.path.join(output_dir, 'feature_selection_results.csv'), index=False)
    
    return results

def select_optimal_features(X: pd.DataFrame, y: pd.Series, target_name: str, 
                           n_features: int = 100, output_dir: Optional[str] = None) -> List[str]:
    """
    Select optimal features using multiple methods and evaluation.
    
    Args:
        X: DataFrame containing features
        y: Target variable
        target_name: Name of the target variable (for logging and output)
        n_features: Number of features to select
        output_dir: Directory to save evaluation results
        
    Returns:
        List of selected feature names
    """
    logger.info(f"Selecting optimal features for {target_name}...")
    
    # Apply different feature selection methods
    variance_features = select_by_variance(X)
    importance_features = select_by_importance(X, y, n_features)
    rfe_features = select_by_rfe(X, y, n_features)
    mi_features = select_by_mutual_info(X, y, n_features)
    
    # Create feature sets dictionary
    feature_sets = {
        'Variance': variance_features,
        'Importance': importance_features,
        'RFE': rfe_features,
        'Mutual Info': mi_features
    }
    
    # Create output directory for this target
    target_output_dir = None
    if output_dir:
        target_output_dir = os.path.join(output_dir, f"{target_name}_features")
        os.makedirs(target_output_dir, exist_ok=True)
    
    # Evaluate feature sets
    results = evaluate_feature_sets(X, y, feature_sets, output_dir=target_output_dir)
    
    # Get best method
    best_method = max(results, key=results.get)
    best_features = feature_sets[best_method]
    
    logger.info(f"Best feature selection method for {target_name}: {best_method} (R²: {results[best_method]:.4f})")
    logger.info(f"Selected {len(best_features)} features for {target_name}")
    
    # Save selected features if output_dir is provided
    if target_output_dir:
        with open(os.path.join(target_output_dir, 'selected_features.txt'), 'w') as f:
            for feature in best_features:
                f.write(f"{feature}\n")
    
    return best_features

def get_feature_importance(X: pd.DataFrame, y: pd.Series, selected_features: List[str], 
                          output_dir: Optional[str] = None, target_name: str = "target") -> pd.DataFrame:
    """
    Get feature importance for selected features.
    
    Args:
        X: DataFrame containing features
        y: Target variable
        selected_features: List of selected feature names
        output_dir: Directory to save feature importance
        target_name: Name of the target variable (for output)
        
    Returns:
        DataFrame with feature importance
    """
    # Get selected features
    X_selected = X[selected_features]
    
    # Encode categorical features
    X_selected = encode_categorical_features(X_selected)
    
    # Train Random Forest
    rf = RandomForestRegressor(n_estimators=200, random_state=42, n_jobs=-1)
    rf.fit(X_selected, y)
    
    # Get feature importance
    importance_df = pd.DataFrame({
        'Feature': selected_features,
        'Importance': rf.feature_importances_
    })
    
    # Sort by importance
    importance_df = importance_df.sort_values('Importance', ascending=False)
    
    # Save feature importance if output_dir is provided
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
        
        # Save to CSV
        importance_df.to_csv(os.path.join(output_dir, f"{target_name}_feature_importance.csv"), index=False)
        
        # Create bar plot for top 20 features
        plt.figure(figsize=(12, 8))
        sns.barplot(x='Importance', y='Feature', data=importance_df.head(20))
        plt.title(f'Top 20 Features for {target_name}')
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, f"{target_name}_feature_importance.png"))
    
    return importance_df

if __name__ == "__main__":
    # This module is not meant to be run directly
    logger.info("This module provides feature selection functions for the hybrid model.")
