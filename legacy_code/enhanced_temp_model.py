#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Enhanced Temperature Model for DeepMu.

This module implements an improved version of the temperature prediction component
with increased regularization and feature transformations.
"""

import os
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import TensorDataset, DataLoader
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import RobustScaler, PowerTransformer
import joblib
import logging
import copy
import matplotlib.pyplot as plt
from typing import Dict, Tuple, Union, List
import scipy.stats as stats

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Use GPU if available
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

class EnhancedTemperatureModel(nn.Module):
    """Enhanced temperature prediction model with increased regularization."""
    
    def __init__(self, input_dim: int, hidden_dims: List[int] = [512, 384, 256, 128, 64]):
        super().__init__()
        
        # Feature normalization
        self.feature_norm = nn.LayerNorm(input_dim)
        
        # Main network with residual connections
        self.layers = nn.ModuleList()
        
        # Input layer - increased dropout
        self.layers.append(nn.Linear(input_dim, hidden_dims[0]))
        self.layers.append(nn.BatchNorm1d(hidden_dims[0]))
        self.layers.append(nn.ReLU())
        self.layers.append(nn.Dropout(0.3))  # Increased from 0.2
        
        # Hidden layers with residual connections - increased dropout
        for i in range(1, len(hidden_dims)):
            # Add linear layer
            self.layers.append(nn.Linear(hidden_dims[i-1], hidden_dims[i]))
            self.layers.append(nn.BatchNorm1d(hidden_dims[i]))
            self.layers.append(nn.ReLU())
            self.layers.append(nn.Dropout(0.4))  # Increased from 0.3
            
        # Output layer
        self.output_layer = nn.Linear(hidden_dims[-1], 1)
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Forward pass through the model."""
        # Normalize input features
        x = self.feature_norm(x)
        
        # Process through main network with residual connections
        prev_block_output = None
        
        for i in range(0, len(self.layers), 4):
            if i + 3 < len(self.layers):  # Ensure we have a complete block
                # Store input to this block for residual connection
                block_input = x
                
                # Apply the block (linear + batch norm + activation + dropout)
                x = self.layers[i](x)       # Linear
                x = self.layers[i+1](x)     # BatchNorm
                x = self.layers[i+2](x)     # ReLU
                x = self.layers[i+3](x)     # Dropout
                
                # Add residual connection if dimensions match
                if prev_block_output is not None and prev_block_output.shape == x.shape:
                    x = x + prev_block_output
                
                # Store this block's output for the next residual connection
                prev_block_output = x
        
        # Final output layer
        return self.output_layer(x)

class EnhancedTemperatureTrainer:
    """Trainer class for the enhanced temperature prediction model."""
    
    def __init__(self, output_dir: str = "models/enhanced_temp_model"):
        """Initialize the trainer."""
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        
        # Create plots directory
        self.plots_dir = os.path.join(output_dir, "plots")
        os.makedirs(self.plots_dir, exist_ok=True)
        
        # Set up logging to file
        file_handler = logging.FileHandler(os.path.join(output_dir, "training.log"))
        file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
        logger.addHandler(file_handler)
        
        # Initialize model and components to None
        self.model = None
        self.scaler = None
        self.feature_indices = None
        self.feature_transformers = {}
        
        # Training history
        self.history = {
            "train_loss": [],
            "val_loss": [],
            "train_r2": [],
            "val_r2": []
        }
    
    def load_data(self, feature_file: str, metadata_file: str, n_features: int = 800) -> Tuple[pd.DataFrame, pd.Series]:
        """Load and prepare data."""
        logger.info(f"Loading features from {feature_file}")
        features = pd.read_csv(feature_file, sep='\t', index_col='genome_id')
        
        logger.info(f"Loading metadata from {metadata_file}")
        metadata = pd.read_csv(metadata_file, sep='\t', index_col='genome_id')
        
        # Get common samples
        common_indices = features.index.intersection(metadata.index)
        logger.info(f"Found {len(common_indices)} common samples")
        
        features = features.loc[common_indices]
        metadata = metadata.loc[common_indices]
        
        # Handle missing values
        features = features.fillna(0)
        
        # Select top n_features with highest variance if needed
        if n_features and features.shape[1] > n_features:
            logger.info(f"Selecting top {n_features} features by variance")
            feature_variance = features.var().sort_values(ascending=False)
            selected_features = feature_variance.index[:n_features].tolist()
            self.feature_indices = selected_features
            return features[selected_features], metadata['optimal_temperature']
        else:
            self.feature_indices = features.columns.tolist()
            return features, metadata['optimal_temperature']
    
    def _is_non_gaussian(self, feature_values, p_threshold=0.05):
        """Test if a feature follows Gaussian distribution using Shapiro-Wilk test."""
        # Sample the feature if too large (Shapiro-Wilk limited to 5000 samples)
        if len(feature_values) > 5000:
            feature_sample = np.random.choice(feature_values, 5000, replace=False)
        else:
            feature_sample = feature_values
            
        # Clean data for the test
        feature_sample = feature_sample[~np.isnan(feature_sample)]
        feature_sample = feature_sample[~np.isinf(feature_sample)]
        
        if len(feature_sample) < 3:
            return False  # Not enough data to test
        
        try:
            # Shapiro-Wilk test for normality
            _, p_value = stats.shapiro(feature_sample)
            return p_value < p_threshold
        except Exception:
            # If test fails, assume non-Gaussian
            return True
    
    def _apply_transformations(self, features: pd.DataFrame, fit: bool = True) -> pd.DataFrame:
        """Apply appropriate transformations to non-Gaussian features."""
        transformed_features = features.copy()
        
        if fit:
            logger.info("Analyzing feature distributions and applying transformations")
            for column in features.columns:
                feature_values = features[column].values
                
                # Check for non-Gaussian distribution
                if self._is_non_gaussian(feature_values):
                    # Check if data is positive for log transform
                    if np.all(feature_values > 0):
                        logger.info(f"Applying log10 transformation to non-Gaussian feature: {column}")
                        transformer = "log10"
                        transformed_features[column] = np.log10(feature_values)
                    else:
                        # For features with non-positive values, use power transform
                        logger.info(f"Applying power transformation to non-Gaussian feature: {column}")
                        transformer = PowerTransformer(method='yeo-johnson')
                        transformed_values = transformer.fit_transform(feature_values.reshape(-1, 1))
                        transformed_features[column] = transformed_values.flatten()
                    
                    # Store the transformer
                    self.feature_transformers[column] = transformer
        else:
            # Apply saved transformations to new data
            for column, transformer in self.feature_transformers.items():
                if column in features.columns:
                    if transformer == "log10":
                        # Ensure positive values for log transform
                        values = features[column].values
                        values = np.maximum(values, 1e-10)
                        transformed_features[column] = np.log10(values)
                    else:
                        transformed_features[column] = transformer.transform(
                            features[column].values.reshape(-1, 1)).flatten()
        
        return transformed_features
    
    def _visualize_feature_distributions(self, features_before, features_after, n_samples=10):
        """Visualize distributions of selected features before and after transformation."""
        # Select random features that were transformed
        transformed_columns = list(self.feature_transformers.keys())
        
        if len(transformed_columns) > 0:
            sample_columns = np.random.choice(
                transformed_columns, 
                size=min(n_samples, len(transformed_columns)), 
                replace=False
            )
            
            for i, column in enumerate(sample_columns):
                plt.figure(figsize=(12, 5))
                
                # Before transformation
                plt.subplot(1, 2, 1)
                plt.hist(features_before[column], bins=30, alpha=0.7)
                plt.title(f"Before: {column}")
                plt.xlabel("Value")
                plt.ylabel("Frequency")
                
                # After transformation
                plt.subplot(1, 2, 2)
                plt.hist(features_after[column], bins=30, alpha=0.7)
                plt.title(f"After: {column}")
                plt.xlabel("Value")
                plt.ylabel("Frequency")
                
                plt.tight_layout()
                plt.savefig(os.path.join(self.plots_dir, f"feature_transform_{i}_{column.replace('/', '_')}.png"))
                plt.close()
            
            logger.info(f"Feature distribution visualizations saved to {self.plots_dir}")
    
    def prepare_data_loaders(self, features: pd.DataFrame, temperatures: pd.Series, 
                            batch_size: int = 64) -> Dict:
        """Split data and create data loaders with feature transformations."""
        # Split data
        X_train, X_val, y_train, y_val = train_test_split(
            features, temperatures, test_size=0.2, random_state=42
        )
        
        # Apply feature transformations
        X_train_before = X_train.copy()
        X_train = self._apply_transformations(X_train, fit=True)
        
        # Visualize transformations for a few features
        self._visualize_feature_distributions(X_train_before, X_train)
        
        # Apply same transformations to validation set
        X_val = self._apply_transformations(X_val, fit=False)
        
        # Scale features
        self.scaler = RobustScaler()
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_val_scaled = self.scaler.transform(X_val)
        
        # Create datasets
        train_dataset = TensorDataset(
            torch.FloatTensor(X_train_scaled).to(device),
            torch.FloatTensor(y_train.values.reshape(-1, 1)).to(device)
        )
        
        val_dataset = TensorDataset(
            torch.FloatTensor(X_val_scaled).to(device),
            torch.FloatTensor(y_val.values.reshape(-1, 1)).to(device)
        )
        
        # Create data loaders
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size=batch_size*2)
        
        return {
            "train_loader": train_loader,
            "val_loader": val_loader,
            "X_train": X_train,
            "X_val": X_val,
            "y_train": y_train,
            "y_val": y_val,
            "X_train_scaled": X_train_scaled,
            "X_val_scaled": X_val_scaled
        }
    
    def train(self, features: pd.DataFrame, temperatures: pd.Series, 
              epochs: int = 200, lr: float = 0.001, batch_size: int = 64, 
              patience: int = 30, weight_decay: float = 5e-6) -> None:
        """Train the model with increased regularization."""
        # Prepare data
        data = self.prepare_data_loaders(features, temperatures, batch_size)
        train_loader = data["train_loader"]
        val_loader = data["val_loader"]
        
        # Initialize model
        input_dim = data["X_train"].shape[1]
        self.model = EnhancedTemperatureModel(input_dim=input_dim).to(device)
        
        # Create optimizer with increased weight decay
        optimizer = optim.AdamW(
            self.model.parameters(),
            lr=lr,
            weight_decay=weight_decay,  # Increased from 1e-6
            eps=1e-8
        )
        
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, mode='min', factor=0.5, patience=5, verbose=True
        )
        
        # Set up early stopping
        early_stopping_counter = 0
        best_val_loss = float('inf')
        best_model_state = None
        
        # Training loop
        for epoch in range(epochs):
            # Training phase
            self.model.train()
            train_loss = 0.0
            
            for X_batch, y_batch in train_loader:
                optimizer.zero_grad()
                
                # Forward pass
                y_pred = self.model(X_batch)
                loss = F.mse_loss(y_pred, y_batch)
                
                # Backward pass
                loss.backward()
                
                # Gradient clipping to prevent exploding gradients
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                
                optimizer.step()
                train_loss += loss.item() * len(y_batch)
            
            train_loss /= len(train_loader.dataset)
            
            # Validation phase
            self.model.eval()
            val_loss = 0.0
            val_preds = []
            val_targets = []
            
            with torch.no_grad():
                for X_batch, y_batch in val_loader:
                    # Forward pass
                    y_pred = self.model(X_batch)
                    loss = F.mse_loss(y_pred, y_batch)
                    
                    val_loss += loss.item() * len(y_batch)
                    
                    # Store predictions and targets for metrics calculation
                    val_preds.extend(y_pred.cpu().numpy().flatten())
                    val_targets.extend(y_batch.cpu().numpy().flatten())
            
            val_loss /= len(val_loader.dataset)
            
            # Update scheduler
            scheduler.step(val_loss)
            
            # Calculate R² metrics
            train_preds = []
            train_targets = []
            
            self.model.eval()
            with torch.no_grad():
                for X_batch, y_batch in train_loader:
                    y_pred = self.model(X_batch)
                    train_preds.extend(y_pred.cpu().numpy().flatten())
                    train_targets.extend(y_batch.cpu().numpy().flatten())
            
            train_r2 = r2_score(train_targets, train_preds)
            val_r2 = r2_score(val_targets, val_preds)
            
            # Store metrics in history
            self.history["train_loss"].append(train_loss)
            self.history["val_loss"].append(val_loss)
            self.history["train_r2"].append(train_r2)
            self.history["val_r2"].append(val_r2)
            
            # Log progress every 10 epochs
            if (epoch + 1) % 10 == 0:
                logger.info(f"Epoch {epoch+1}/{epochs}")
                logger.info(f"  Train Loss: {train_loss:.6f}, Train R²: {train_r2:.6f}")
                logger.info(f"  Val Loss: {val_loss:.6f}, Val R²: {val_r2:.6f}")
                logger.info(f"  Current LR: {optimizer.param_groups[0]['lr']:.6f}")
            
            # Check for improvement
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                best_model_state = copy.deepcopy(self.model.state_dict())
                early_stopping_counter = 0
                logger.info(f"  Validation loss improved to {val_loss:.6f}")
            else:
                early_stopping_counter += 1
                
                if early_stopping_counter >= patience:
                    logger.info(f"Early stopping triggered after {epoch+1} epochs")
                    break
        
        # Plot training history
        self._plot_training_history()
        
        # Load best model
        self.model.load_state_dict(best_model_state)
        
        # Final evaluation
        self.evaluate(data["X_train"], data["y_train"], data["X_val"], data["y_val"])
        
        # Save model and artifacts
        self.save()
    
    def _plot_training_history(self):
        """Plot training and validation metrics over epochs."""
        epochs = range(1, len(self.history["train_loss"]) + 1)
        
        # Plot loss
        plt.figure(figsize=(10, 5))
        plt.subplot(1, 2, 1)
        plt.plot(epochs, self.history["train_loss"], 'b-', label='Training Loss')
        plt.plot(epochs, self.history["val_loss"], 'r-', label='Validation Loss')
        plt.title('Loss Over Epochs')
        plt.xlabel('Epochs')
        plt.ylabel('Loss')
        plt.legend()
        
        # Plot R²
        plt.subplot(1, 2, 2)
        plt.plot(epochs, self.history["train_r2"], 'b-', label='Training R²')
        plt.plot(epochs, self.history["val_r2"], 'r-', label='Validation R²')
        plt.title('R² Over Epochs')
        plt.xlabel('Epochs')
        plt.ylabel('R²')
        plt.legend()
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.plots_dir, 'training_history.png'))
        plt.close()
        
        logger.info(f"Training history plot saved to {os.path.join(self.plots_dir, 'training_history.png')}")
    
    def evaluate(self, X_train: pd.DataFrame, y_train: pd.Series, 
                X_val: pd.DataFrame, y_val: pd.Series) -> Dict:
        """Evaluate the model on training and validation data."""
        # Ensure model is in evaluation mode
        self.model.eval()
        
        # Scale data
        X_train_scaled = self.scaler.transform(X_train)
        X_val_scaled = self.scaler.transform(X_val)
        
        # Convert to tensors
        X_train_tensor = torch.FloatTensor(X_train_scaled).to(device)
        X_val_tensor = torch.FloatTensor(X_val_scaled).to(device)
        
        # Get predictions
        with torch.no_grad():
            train_preds = self.model(X_train_tensor).cpu().numpy().flatten()
            val_preds = self.model(X_val_tensor).cpu().numpy().flatten()
        
        # Calculate metrics
        train_metrics = self._calculate_metrics(y_train.values, train_preds)
        val_metrics = self._calculate_metrics(y_val.values, val_preds)
        
        # Log results
        logger.info("Final Evaluation Results:")
        logger.info("Training Set:")
        logger.info(f"  R²: {train_metrics['r2']:.6f}")
        logger.info(f"  RMSE: {train_metrics['rmse']:.6f}")
        logger.info(f"  MAE: {train_metrics['mae']:.6f}")
        
        logger.info("Validation Set:")
        logger.info(f"  R²: {val_metrics['r2']:.6f}")
        logger.info(f"  RMSE: {val_metrics['rmse']:.6f}")
        logger.info(f"  MAE: {val_metrics['mae']:.6f}")
        
        # Plot prediction results
        self._plot_prediction_results(y_train, train_preds, y_val, val_preds)
        
        return {
            "train": train_metrics,
            "val": val_metrics
        }
    
    def _plot_prediction_results(self, y_train, train_preds, y_val, val_preds):
        """Create visualizations of model performance."""
        # Create figure with two subplots
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 7))
        
        # Training set results
        train_r2 = r2_score(y_train, train_preds)
        train_rmse = np.sqrt(mean_squared_error(y_train, train_preds))
        
        ax1.scatter(y_train, train_preds, alpha=0.5, c='blue')
        ax1.plot([0, 100], [0, 100], 'r--')  # Perfect prediction line
        ax1.set_xlabel('True Temperature (°C)')
        ax1.set_ylabel('Predicted Temperature (°C)')
        ax1.set_title(f'Training Set (R² = {train_r2:.4f}, RMSE = {train_rmse:.2f}°C)')
        ax1.set_xlim(0, 100)
        ax1.set_ylim(0, 100)
        
        # Validation set results
        val_r2 = r2_score(y_val, val_preds)
        val_rmse = np.sqrt(mean_squared_error(y_val, val_preds))
        
        ax2.scatter(y_val, val_preds, alpha=0.5, c='green')
        ax2.plot([0, 100], [0, 100], 'r--')  # Perfect prediction line
        ax2.set_xlabel('True Temperature (°C)')
        ax2.set_ylabel('Predicted Temperature (°C)')
        ax2.set_title(f'Validation Set (R² = {val_r2:.4f}, RMSE = {val_rmse:.2f}°C)')
        ax2.set_xlim(0, 100)
        ax2.set_ylim(0, 100)
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.plots_dir, 'prediction_results.png'))
        plt.close()
        
        # Create error distribution plot
        plt.figure(figsize=(10, 6))
        
        # Calculate errors
        train_errors = y_train - train_preds
        val_errors = y_val - val_preds
        
        # Plot error distributions
        sns.histplot(train_errors, kde=True, label='Training', alpha=0.5, color='blue')
        sns.histplot(val_errors, kde=True, label='Validation', alpha=0.5, color='green')
        
        plt.axvline(x=0, color='r', linestyle='--')
        plt.title('Error Distribution (True - Predicted)')
        plt.xlabel('Error (°C)')
        plt.ylabel('Frequency')
        plt.legend()
        plt.savefig(os.path.join(self.plots_dir, 'error_distribution.png'))
        plt.close()
        
        # Create error by temperature plot
        plt.figure(figsize=(10, 6))
        plt.scatter(y_val, val_errors, alpha=0.5, c='green')
        plt.axhline(y=0, color='r', linestyle='--')
        plt.title('Prediction Error vs. True Temperature')
        plt.xlabel('True Temperature (°C)')
        plt.ylabel('Error (°C)')
        plt.savefig(os.path.join(self.plots_dir, 'error_by_temperature.png'))
        plt.close()
        
        logger.info(f"Prediction analysis plots saved to {self.plots_dir}")
    
    def _calculate_metrics(self, y_true: np.ndarray, y_pred: np.ndarray) -> Dict:
        """Calculate evaluation metrics with consistent methodology."""
        # Ensure 1D arrays
        y_true = np.array(y_true).flatten()
        y_pred = np.array(y_pred).flatten()
        
        # Calculate metrics using scikit-learn
        r2 = r2_score(y_true, y_pred)
        rmse = np.sqrt(mean_squared_error(y_true, y_pred))
        mae = mean_absolute_error(y_true, y_pred)
        
        # Calculate R² manually for verification
        y_mean = np.mean(y_true)
        ss_total = np.sum((y_true - y_mean) ** 2)
        ss_residual = np.sum((y_true - y_pred) ** 2)
        manual_r2 = 1 - (ss_residual / ss_total) if ss_total > 0 else 0.0
        
        # Check for discrepancy
        if abs(r2 - manual_r2) > 0.001:
            logger.warning(f"R² calculation discrepancy: sklearn={r2:.6f}, manual={manual_r2:.6f}")
            
        return {
            "r2": r2,
            "rmse": rmse,
            "mae": mae,
            "manual_r2": manual_r2
        }
    
    def predict(self, features: pd.DataFrame) -> np.ndarray:
        """Make predictions with the trained model."""
        if self.model is None:
            raise ValueError("Model not trained yet")
        
        # Apply feature selection if needed
        if self.feature_indices:
            features = features[self.feature_indices]
        
        # Apply feature transformations
        features = self._apply_transformations(features, fit=False)
        
        # Scale features
        X_scaled = self.scaler.transform(features)
        
        # Convert to tensor
        X_tensor = torch.FloatTensor(X_scaled).to(device)
        
        # Get predictions
        self.model.eval()
        with torch.no_grad():
            predictions = self.model(X_tensor).cpu().numpy().flatten()
        
        return predictions
    
    def save(self) -> None:
        """Save model and artifacts."""
        # Save model state
        torch.save(self.model.state_dict(), os.path.join(self.output_dir, "model.pt"))
        
        # Save scaler
        joblib.dump(self.scaler, os.path.join(self.output_dir, "scaler.pkl"))
        
        # Save feature indices
        if self.feature_indices:
            joblib.dump(self.feature_indices, os.path.join(self.output_dir, "feature_indices.pkl"))
        
        # Save feature transformers
        joblib.dump(self.feature_transformers, os.path.join(self.output_dir, "feature_transformers.pkl"))
        
        # Save model architecture info
        input_dim = None
        if self.model:
            # Find the first Linear layer to get its input dimension
            for module in self.model.modules():
                if isinstance(module, nn.Linear):
                    input_dim = module.in_features
                    break
            else:
                # Fallback if no Linear layer is found
                input_dim = len(self.feature_indices) if self.feature_indices else None
        
        model_info = {
            "input_dim": input_dim,
            "history": self.history,
            "num_transformed_features": len(self.feature_transformers)
        }
        joblib.dump(model_info, os.path.join(self.output_dir, "model_info.pkl"))
        
        logger.info(f"Model and artifacts saved to {self.output_dir}")
    
    def load(self, model_dir: str) -> None:
        """Load a trained model."""
        # Load model info
        model_info = joblib.load(os.path.join(model_dir, "model_info.pkl"))
        
        # Initialize model
        self.model = EnhancedTemperatureModel(input_dim=model_info["input_dim"]).to(device)
        
        # Load model weights
        self.model.load_state_dict(torch.load(os.path.join(model_dir, "model.pt")))
        
        # Load scaler
        self.scaler = joblib.load(os.path.join(model_dir, "scaler.pkl"))
        
        # Load feature transformers
        try:
            self.feature_transformers = joblib.load(os.path.join(model_dir, "feature_transformers.pkl"))
        except:
            self.feature_transformers = {}
        
        # Load feature indices
        try:
            self.feature_indices = joblib.load(os.path.join(model_dir, "feature_indices.pkl"))
        except:
            self.feature_indices = None
        
        logger.info(f"Model loaded from {model_dir}")

def main():
    """Main function to train the model."""
    # Create trainer
    trainer = EnhancedTemperatureTrainer(output_dir="models/enhanced_temp_model")
    
    # Load data
    features, temperatures = trainer.load_data(
        "training_data/combined_features.tsv",
        "training_data/metadata.tsv",
        n_features=800
    )
    
    logger.info(f"Training model with {features.shape[1]} features and {len(temperatures)} samples")
    
    # Train model with increased regularization
    trainer.train(
        features,
        temperatures,
        epochs=200,
        lr=0.001,
        batch_size=64,
        patience=30,
        weight_decay=5e-6  # Increased regularization
    )
    
    logger.info("Training complete!")

if __name__ == "__main__":
    import matplotlib
    matplotlib.use('Agg')  # Use non-interactive backend
    from matplotlib import pyplot as plt
    import seaborn as sns
    sns.set(style='whitegrid', font_scale=1.2)
    
    main() 