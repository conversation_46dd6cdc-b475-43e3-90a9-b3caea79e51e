#!/usr/bin/env python
"""
Train a hybrid model that combines Random Forest for growth rate prediction
and Transformer for optimal temperature prediction.

This script trains a hybrid model that leverages the strengths of both approaches:
- Random Forest excels at tabular data and regression tasks like growth rate prediction
- Transformer architecture has shown superior performance for temperature prediction

The hybrid model handles:
1. Feature preprocessing and categorization
2. Training separate models for each prediction task
3. Evaluation and visualization of results
4. Saving models and metrics for later use
"""

import os
import sys
import json
import argparse
import logging
import numpy as np
import pandas as pd
import torch
from deepmu.models.rlvr_agent import RLVRAgent, VerifiableRewardFunction, RLVRTrainer
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from typing import Dict, List, Tuple, Optional, Union, Any

from deepmu.models.hybrid_rf_transformer import HybridRFTransformer, HybridRFTransformerTrainer
from deepmu.utils.logging import get_logger

# Set up logging
logger = get_logger()


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Train a hybrid RF-Transformer model')

    # Input data
    parser.add_argument('--feature-file', type=str, required=True,
                        help='Path to combined feature file (TSV)')
    parser.add_argument('--metadata', type=str, required=True,
                        help='Path to metadata file (TSV)')

    # Output
    parser.add_argument('--output-dir', type=str, required=True,
                        help='Directory to save model and results')
    parser.add_argument('--metrics-dir', type=str, default=None,
                        help='Directory to save metrics and plots')

    # Training parameters
    parser.add_argument('--batch-size', type=int, default=32,
                        help='Batch size for transformer training')
    parser.add_argument('--epochs', type=int, default=100,
                        help='Number of epochs for transformer training')
    parser.add_argument('--learning-rate', type=float, default=0.001,
                        help='Learning rate for transformer optimizer')
    parser.add_argument('--weight-decay', type=float, default=1e-5,
                        help='Weight decay for regularization')
    parser.add_argument('--patience', type=int, default=10,
                        help='Patience for early stopping')
    parser.add_argument('--test-size', type=float, default=0.2,
                        help='Test set size')
    parser.add_argument('--random-state', type=int, default=42,
                        help='Random state for reproducibility')

    # Random Forest parameters
    parser.add_argument('--rf-n-estimators', type=int, default=200,
                        help='Number of trees in random forest')
    parser.add_argument('--rf-max-depth', type=int, default=20,
                        help='Maximum depth of trees in random forest')
    parser.add_argument('--rf-min-samples-split', type=int, default=2,
                        help='Minimum samples required to split a node')
    parser.add_argument('--rf-min-samples-leaf', type=int, default=1,
                        help='Minimum samples required at a leaf node')

    # Transformer parameters
    parser.add_argument('--transformer-hidden-dims', type=int, nargs='+', default=[256, 128, 64],
                        help='Hidden layer dimensions for transformer')
    parser.add_argument('--transformer-dropout-rates', type=float, nargs='+', default=[0.2, 0.3, 0.4],
                        help='Dropout rates for transformer')
    parser.add_argument('--transformer-heads', type=int, default=4,
                        help='Number of attention heads in transformer')
    parser.add_argument('--transformer-layers', type=int, default=2,
                         help='Number of transformer layers')

    # RL Training Parameters
    parser.add_argument('--rl-iterations', type=int, default=1000,
                        help='Number of RL training iterations')
    parser.add_argument('--rl-learning-rate', type=float, default=1e-4,
                        help='Learning rate for RL agent')
    parser.add_argument('--rl-log-interval', type=int, default=100,
                        help='Logging interval for RL training')
    parser.add_argument('--no-batch-norm', action='store_true',
                        help='Disable batch normalization')
    parser.add_argument('--no-residual', action='store_true',
                        help='Disable residual connections')
    parser.add_argument('--no-positional-encoding', action='store_true',
                        help='Disable positional encoding in transformer')

    return parser.parse_args()


def load_data(feature_file: str, metadata_file: str) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """Load feature and metadata files.

    Args:
        feature_file: Path to feature file (TSV)
        metadata_file: Path to metadata file (TSV)

    Returns:
        Tuple of (features_df, metadata_df)
    """
    logger.info(f"Loading features from {feature_file}")
    features_df = pd.read_csv(feature_file, sep='\t')
    if 'genome_id' in features_df.columns:
        features_df = features_df.set_index('genome_id')

    logger.info(f"Loading metadata from {metadata_file}")
    metadata_df = pd.read_csv(metadata_file, sep='\t')
    if 'genome_id' in metadata_df.columns:
        metadata_df = metadata_df.set_index('genome_id')

    # Get common genomes
    common_genomes = list(set(features_df.index) & set(metadata_df.index))
    logger.info(f"Found {len(common_genomes)} common genomes")

    # Filter to common genomes
    features_df = features_df.loc[common_genomes]
    metadata_df = metadata_df.loc[common_genomes]

    # Check for NaN values in metadata
    nan_count_growth = metadata_df['growth_rate'].isna().sum()
    nan_count_temp = metadata_df['optimal_temperature'].isna().sum()

    if nan_count_growth > 0 or nan_count_temp > 0:
        logger.warning(f"Found {nan_count_growth} NaN values in growth_rate and {nan_count_temp} in optimal_temperature")
        logger.warning("Removing rows with NaN target values")
        metadata_df = metadata_df.dropna(subset=['growth_rate', 'optimal_temperature'])
        # Update common genomes
        common_genomes = list(set(features_df.index) & set(metadata_df.index))
        features_df = features_df.loc[common_genomes]
        metadata_df = metadata_df.loc[common_genomes]
        logger.info(f"After removing NaN values, {len(common_genomes)} common genomes remain")

    # Check for NaN values in features
    nan_count = features_df.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in features. Filling with zeros.")
        features_df = features_df.fillna(0)

    # Check for infinite values in features
    inf_count = np.isinf(features_df).sum().sum()
    if inf_count > 0:
        logger.warning(f"Found {inf_count} infinite values in features. Replacing with zeros.")
        features_df = features_df.replace([np.inf, -np.inf], 0)

    return features_df, metadata_df


def categorize_features(features_df: pd.DataFrame) -> Dict[str, List[str]]:
    """Categorize features into different types.

    Args:
        features_df: DataFrame with features

    Returns:
        Dictionary mapping feature types to lists of feature names
    """
    feature_categories = {
        'codon': [],
        'aa': [],
        'genomic': [],
        'rna': [],
        'pi': [],
        'taxonomy': [],
        'pathway': []
    }

    # Print a few column names to help with debugging
    logger.info(f"Sample feature names: {list(features_df.columns[:10])}")
    logger.info(f"Total features: {len(features_df.columns)}")

    # Categorize features based on prefixes and keywords
    for feature in features_df.columns:
        # Convert to lowercase for case-insensitive matching
        feature_lower = feature.lower()

        # Codon features - updated to include renamed features
        if any(keyword in feature_lower for keyword in [
            'cub', 'cpb', 'cai', 'enc', 'rscu', 'codon', 'heg', 'bg', 'delta',
            'deviation', 'divergence', 'composite', 'context_bias'
        ]):
            feature_categories['codon'].append(feature)

        # Amino acid features
        elif any(keyword in feature_lower for keyword in [
            'aa_', 'amino', 'protein', 'arsc', 'nitrogen', 'carbon', 'c_arsc', 'n_arsc'
        ]):
            feature_categories['aa'].append(feature)

        # Genomic features (including breakpoint features)
        elif any(keyword in feature_lower for keyword in [
            'genome', 'gc_', 'gc1', 'gc2', 'gc3', 'dinuc', 'size', 'length', 'skew',
            '_change_points', '_sign_changes', '_mean', '_std', '_min', '_max', '_range',
            'breakpoint', 'freq_'
        ]):
            feature_categories['genomic'].append(feature)

        # RNA features
        elif any(keyword in feature_lower for keyword in [
            'trna', 'rrna', 'tai', 'rna', 'rrna_count', 'trna_count'
        ]):
            feature_categories['rna'].append(feature)

        # Protein pI features
        elif any(keyword in feature_lower for keyword in [
            'pi_', 'isoelectric', 'ph', 'pi_mean', 'pi_median', 'pi_std'
        ]):
            feature_categories['pi'].append(feature)

        # Taxonomy features
        elif any(keyword in feature_lower for keyword in [
            'phylum', 'class', 'order', 'family', 'genus', 'taxid', 'taxonomy'
        ]):
            feature_categories['taxonomy'].append(feature)

        # Pathway features - updated to include pathway completeness and metabolism features
        elif any(keyword in feature_lower for keyword in [
            'pathway', 'kegg', 'ko_', 'module', 'completeness', 'metabolism_',
            'functional_', 'pathway_completeness_', 'metabolic_capacity'
        ]) or feature.startswith('ko'):
            feature_categories['pathway'].append(feature)

        # Default to codon features if not categorized
        else:
            # Check if it's likely a numeric feature (not an ID or metadata)
            if feature != 'genome_id' and not feature.endswith('_id'):
                logger.info(f"Uncategorized feature: {feature}")
                feature_categories['codon'].append(feature)

    # Log feature counts
    for category, features in feature_categories.items():
        logger.info(f"Found {len(features)} {category} features")
        if len(features) > 0:
            logger.info(f"Sample {category} features: {features[:5]}")

    return feature_categories


def prepare_data(
    features_df: pd.DataFrame,
    metadata_df: pd.DataFrame,
    feature_categories: Dict[str, List[str]],
    test_size: float = 0.2,
    random_state: int = 42
) -> Dict[str, Dict[str, np.ndarray]]:
    """Prepare data for training.

    Args:
        features_df: Feature DataFrame
        metadata_df: Metadata DataFrame
        feature_categories: Dictionary mapping feature types to lists of feature names
        test_size: Test set size
        random_state: Random state for reproducibility

    Returns:
        Dictionary with train and test data for each feature type and target
    """
    # Get target variables
    y_growth = metadata_df['growth_rate'].values
    y_temp = metadata_df['optimal_temperature'].values

    # Split data
    indices = np.arange(len(features_df))
    train_indices, test_indices = train_test_split(
        indices, test_size=test_size, random_state=random_state
    )

    logger.info(f"Training set size: {len(train_indices)}")
    logger.info(f"Test set size: {len(test_indices)}")

    # Prepare data dictionary
    data = {
        'train': {
            'features': {},
            'targets': {}
        },
        'test': {
            'features': {},
            'targets': {}
        }
    }

    # Process each feature category
    for category, feature_names in feature_categories.items():
        if not feature_names:
            continue

        try:
            # Extract features
            X_category = features_df[feature_names].values

            # Split into train and test
            X_train = X_category[train_indices]
            X_test = X_category[test_indices]

            # Check for NaN or infinite values
            if np.isnan(X_train).any() or np.isnan(X_test).any():
                logger.warning(f"Found NaN values in {category} features. Filling with zeros.")
                X_train = np.nan_to_num(X_train, nan=0.0)
                X_test = np.nan_to_num(X_test, nan=0.0)

            if np.isinf(X_train).any() or np.isinf(X_test).any():
                logger.warning(f"Found infinite values in {category} features. Replacing with zeros.")
                X_train = np.nan_to_num(X_train, posinf=0.0, neginf=0.0)
                X_test = np.nan_to_num(X_test, posinf=0.0, neginf=0.0)

            # Scale features with error handling
            try:
                scaler = StandardScaler()
                X_train_scaled = scaler.fit_transform(X_train)
                X_test_scaled = scaler.transform(X_test)

                # Check for NaN or infinite values after scaling
                if np.isnan(X_train_scaled).any() or np.isnan(X_test_scaled).any():
                    logger.warning(f"Scaling produced NaN values in {category} features. Using robust scaling.")
                    # If standard scaling fails, try a more robust approach
                    X_train_scaled = np.nan_to_num(X_train_scaled, nan=0.0)
                    X_test_scaled = np.nan_to_num(X_test_scaled, nan=0.0)

                if np.isinf(X_train_scaled).any() or np.isinf(X_test_scaled).any():
                    logger.warning(f"Scaling produced infinite values in {category} features. Using robust scaling.")
                    X_train_scaled = np.nan_to_num(X_train_scaled, posinf=0.0, neginf=0.0)
                    X_test_scaled = np.nan_to_num(X_test_scaled, posinf=0.0, neginf=0.0)

            except Exception as e:
                logger.warning(f"Error during scaling of {category} features: {str(e)}. Using unscaled data.")
                # If scaling fails completely, use the original data
                X_train_scaled = X_train
                X_test_scaled = X_test

            # Store in data dictionary
            data['train']['features'][category] = X_train_scaled
            data['test']['features'][category] = X_test_scaled

        except Exception as e:
            logger.error(f"Error processing {category} features: {str(e)}. Skipping this category.")
            continue

    # Add targets
    data['train']['targets']['growth_rate'] = y_growth[train_indices]
    data['train']['targets']['temperature'] = y_temp[train_indices]
    data['test']['targets']['growth_rate'] = y_growth[test_indices]
    data['test']['targets']['temperature'] = y_temp[test_indices]

    return data


def plot_feature_importance(importance_df: pd.DataFrame, output_dir: str, top_n: int = 20):
    """Plot feature importance from Random Forest.

    Args:
        importance_df: DataFrame with feature importance
        output_dir: Directory to save plot
        top_n: Number of top features to show
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Get top N features
    top_features = importance_df.head(top_n)

    # Plot feature importance
    plt.figure(figsize=(14, 10))
    bars = plt.barh(top_features['feature'], top_features['importance'], color='skyblue')
    plt.xlabel('Importance', fontsize=12)
    plt.ylabel('Feature', fontsize=12)
    plt.title(f'Top {top_n} Feature Importance for Growth Rate Prediction', fontsize=14)
    plt.grid(True, alpha=0.3, axis='x')

    # Add values to bars
    for bar in bars:
        width = bar.get_width()
        plt.text(width + 0.002, bar.get_y() + bar.get_height()/2,
                 f'{width:.4f}', ha='left', va='center', fontsize=10)

    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'feature_importance.png'), dpi=300)
    plt.close()

    # Plot feature importance by category
    try:
        # Extract category from feature name
        def get_category(feature_name):
            feature_lower = feature_name.lower()
            if any(keyword in feature_lower for keyword in ['cub', 'cpb', 'cai', 'enc', 'rscu', 'codon', 'heg', 'bg', 'delta', 'deviation', 'divergence']):
                return 'Codon'
            elif any(keyword in feature_lower for keyword in ['aa_', 'amino', 'protein', 'arsc']):
                return 'Amino Acid'
            elif any(keyword in feature_lower for keyword in ['genome', 'gc_', 'dinuc', 'skew', 'breakpoint']):
                return 'Genomic'
            elif any(keyword in feature_lower for keyword in ['trna', 'rrna', 'tai']):
                return 'RNA'
            elif any(keyword in feature_lower for keyword in ['pi_', 'isoelectric']):
                return 'Protein pI'
            elif any(keyword in feature_lower for keyword in ['phylum', 'class', 'order', 'family', 'genus', 'taxid']):
                return 'Taxonomy'
            elif any(keyword in feature_lower for keyword in ['pathway', 'kegg', 'ko_', 'module', 'completeness', 'metabolism_', 'functional_', 'metabolic_capacity']) or feature_name.startswith('ko'):
                return 'Pathway'
            else:
                return 'Other'

        # Add category to importance DataFrame
        importance_df['category'] = importance_df['feature'].apply(get_category)

        # Group by category and sum importance
        category_importance = importance_df.groupby('category')['importance'].sum().reset_index()
        category_importance = category_importance.sort_values('importance', ascending=False)

        # Plot category importance
        plt.figure(figsize=(12, 8))
        bars = plt.bar(category_importance['category'], category_importance['importance'], color='lightgreen')
        plt.xlabel('Feature Category', fontsize=12)
        plt.ylabel('Total Importance', fontsize=12)
        plt.title('Feature Importance by Category', fontsize=14)
        plt.xticks(rotation=45, ha='right')
        plt.grid(True, alpha=0.3, axis='y')

        # Add values to bars
        for bar in bars:
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2, height + 0.01,
                     f'{height:.4f}', ha='center', va='bottom', fontsize=10)

        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'category_importance.png'), dpi=300)
        plt.close()

        # Save category importance to TSV
        category_importance.to_csv(os.path.join(output_dir, 'category_importance.tsv'), sep='\t', index=False)

        # Plot top features by category
        for category in category_importance['category']:
            cat_features = importance_df[importance_df['category'] == category].head(10)
            if len(cat_features) > 0:
                plt.figure(figsize=(12, 8))
                bars = plt.barh(cat_features['feature'], cat_features['importance'], color='lightblue')
                plt.xlabel('Importance', fontsize=12)
                plt.ylabel('Feature', fontsize=12)
                plt.title(f'Top Features in {category} Category', fontsize=14)
                plt.grid(True, alpha=0.3, axis='x')

                # Add values to bars
                for bar in bars:
                    width = bar.get_width()
                    plt.text(width + 0.002, bar.get_y() + bar.get_height()/2,
                             f'{width:.4f}', ha='left', va='center', fontsize=10)

                plt.tight_layout()
                plt.savefig(os.path.join(output_dir, f'{category.lower()}_importance.png'), dpi=300)
                plt.close()

                # Save category features to TSV
                cat_features.to_csv(os.path.join(output_dir, f'{category.lower()}_features.tsv'), sep='\t', index=False)

    except Exception as e:
        logger.warning(f"Error creating category-based feature importance plots: {str(e)}")


def plot_training_history(history: Dict[str, List[float]], output_dir: str):
    """Plot transformer training history.

    Args:
        history: Dictionary with training history
        output_dir: Directory to save plots
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Plot loss
    plt.figure(figsize=(12, 8))
    plt.plot(history['loss'], label='Train Loss', linewidth=2)
    plt.plot(history['val_loss'], label='Validation Loss', linewidth=2)
    plt.xlabel('Epoch', fontsize=12)
    plt.ylabel('Loss', fontsize=12)
    plt.title('Training and Validation Loss', fontsize=14)
    plt.legend(fontsize=12)
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'loss.png'), dpi=300)
    plt.close()

    # Plot R-squared if available
    if 'val_r2' in history:
        plt.figure(figsize=(12, 8))
        plt.plot(history['val_r2'], label='Temperature R²', linewidth=2)
        if 'r2' in history:
            plt.plot(history['r2'], label='Train R²', linewidth=2)
        plt.xlabel('Epoch', fontsize=12)
        plt.ylabel('R²', fontsize=12)
        plt.title('R-squared', fontsize=14)
        plt.legend(fontsize=12)
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'r2.png'), dpi=300)
        plt.close()

    # Plot RMSE if available
    if 'val_rmse' in history:
        plt.figure(figsize=(12, 8))
        plt.plot(history['val_rmse'], label='Validation RMSE', linewidth=2)
        if 'rmse' in history:
            plt.plot(history['rmse'], label='Train RMSE', linewidth=2)
        plt.xlabel('Epoch', fontsize=12)
        plt.ylabel('RMSE', fontsize=12)
        plt.title('Root Mean Squared Error', fontsize=14)
        plt.legend(fontsize=12)
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'rmse.png'), dpi=300)
        plt.close()

    # Save history as CSV, TSV, and JSON
    history_df = pd.DataFrame(history)
    history_df.to_csv(os.path.join(output_dir, 'training_history.csv'), index=False)
    history_df.to_csv(os.path.join(output_dir, 'training_history.tsv'), sep='\t', index=False)

    # Save history as JSON for easier parsing
    with open(os.path.join(output_dir, 'training_history.json'), 'w') as f:
        json.dump(history, f, indent=2)


def save_metrics_to_tsv(metrics: Dict[str, float], output_file: str):
    """Save metrics to a TSV file.

    Args:
        metrics: Dictionary with metrics
        output_file: Path to output file
    """
    metrics_df = pd.DataFrame({
        'metric': list(metrics.keys()),
        'value': list(metrics.values())
    })

    metrics_df.to_csv(output_file, sep='\t', index=False)

    # Also save as JSON for easier parsing
    json_file = output_file.replace('.tsv', '.json')
    with open(json_file, 'w') as f:
        json.dump(metrics, f, indent=2)


def main():
    """Main function."""
    # Parse arguments
    args = parse_args()

    # Create output directories
    os.makedirs(args.output_dir, exist_ok=True)

    metrics_dir = args.metrics_dir or os.path.join(args.output_dir, 'metrics')
    os.makedirs(metrics_dir, exist_ok=True)

    # Load data
    features_df, metadata_df = load_data(args.feature_file, args.metadata)

    # Categorize features
    categorized_features = categorize_features(features_df)

    # Calculate feature dimensions from categorized features
    feature_dims = {category: len(features) for category, features in categorized_features.items()}
    
    # Create model
    model = HybridRFTransformer(
        feature_dims=feature_dims,
        transformer_hidden_dims=args.transformer_hidden_dims,
        transformer_dropout_rates=args.transformer_dropout_rates,
        transformer_heads=args.transformer_heads,
        transformer_layers=args.transformer_layers,
        rf_n_estimators=args.rf_n_estimators,
        rf_max_depth=args.rf_max_depth,
        rf_min_samples_split=args.rf_min_samples_split,
        rf_min_samples_leaf=args.rf_min_samples_leaf
    )

    # Create model
    model = HybridRFTransformer(
        feature_dims=feature_dims,
        transformer_hidden_dims=args.transformer_hidden_dims,
        transformer_dropout_rates=args.transformer_dropout_rates,
        transformer_heads=args.transformer_heads,
        transformer_layers=args.transformer_layers,
        rf_n_estimators=args.rf_n_estimators,
        rf_max_depth=args.rf_max_depth,
        rf_min_samples_split=args.rf_min_samples_split,
        rf_min_samples_leaf=args.rf_min_samples_leaf
    )

    # Initialize RL agent
    rl_agent = RLVRAgent(feature_dims={
        'codon': 64,
        'aa': 20,
        'genomic': 128,
        'taxonomy': 32
    })
    reward_fn = VerifiableRewardFunction(model.rf_model)
    rl_trainer = RLVRTrainer(rl_agent, reward_fn, lr=args.rl_learning_rate)

    # Create output directories
    os.makedirs(args.output_dir, exist_ok=True)

    metrics_dir = args.metrics_dir or os.path.join(args.output_dir, 'metrics')
    os.makedirs(metrics_dir, exist_ok=True)

    # Log all arguments
    logger.info("Training with the following parameters:")
    for arg, value in vars(args).items():
        logger.info(f"  {arg}: {value}")

    # Load data
    features_df, metadata_df = load_data(args.feature_file, args.metadata)

    # Categorize features
    categorized_features = categorize_features(features_df)

    # Prepare data
    data = prepare_data(
        features_df,
        metadata_df,
        feature_categories,
        args.test_size,
        args.random_state
    )

    # Get feature dimensions
    feature_dims = {
        category: features.shape[1]
        for category, features in data['train']['features'].items()
    }

    # Log feature dimensions
    logger.info("Feature dimensions:")
    for category, dim in feature_dims.items():
        logger.info(f"  {category}: {dim}")

    # Create model
    model = HybridRFTransformer(
        feature_dims=feature_dims,
        transformer_hidden_dims=args.transformer_hidden_dims,
        transformer_dropout_rates=args.transformer_dropout_rates,
        transformer_heads=args.transformer_heads,
        transformer_layers=args.transformer_layers,
        use_batch_norm=not args.no_batch_norm,
        use_residual=not args.no_residual,
        use_positional_encoding=not args.no_positional_encoding,
        rf_n_estimators=args.rf_n_estimators,
        rf_max_depth=args.rf_max_depth,
        rf_min_samples_split=args.rf_min_samples_split,
        rf_min_samples_leaf=args.rf_min_samples_leaf
    )

    # Log model architecture
    logger.info(f"Random Forest parameters:")
    logger.info(f"  n_estimators: {args.rf_n_estimators}")
    logger.info(f"  max_depth: {args.rf_max_depth}")
    logger.info(f"  min_samples_split: {args.rf_min_samples_split}")
    logger.info(f"  min_samples_leaf: {args.rf_min_samples_leaf}")

    logger.info(f"Transformer parameters:")
    logger.info(f"  hidden_dims: {args.transformer_hidden_dims}")
    logger.info(f"  dropout_rates: {args.transformer_dropout_rates}")
    logger.info(f"  heads: {args.transformer_heads}")
    logger.info(f"  layers: {args.transformer_layers}")
    logger.info(f"  batch_norm: {not args.no_batch_norm}")
    logger.info(f"  residual: {not args.no_residual}")
    logger.info(f"  positional_encoding: {not args.no_positional_encoding}")

    # Create trainer
    trainer = HybridRFTransformerTrainer(
        model=model,
        learning_rate=args.learning_rate,
        weight_decay=args.weight_decay
    )

    # Set up parameters
    rf_params = {
        'n_estimators': args.rf_n_estimators,
        'max_depth': args.rf_max_depth,
        'min_samples_split': args.rf_min_samples_split,
        'min_samples_leaf': args.rf_min_samples_leaf
    }

    transformer_params = {
        'batch_size': args.batch_size,
        'epochs': args.epochs,
        'patience': args.patience
    }

    # Train model
    # RL Training Loop
    logger.info('Starting RL training')
    for iteration in range(args.rl_iterations):
        # Get model predictions for RL state
        with torch.no_grad():
            val_preds = model.transformer_model(val_features)
        
        # Update RL agent
        rl_loss = rl_trainer.train_step(val_features, val_targets['growth_rate'])
        
        # Log RL metrics
        if iteration % args.rl_log_interval == 0:
            logger.info(f'RL Iteration {iteration}: Loss={rl_loss:.4f}')

    # Save RL agent
    torch.save(rl_agent.state_dict(), os.path.join(args.output_dir, 'rl_agent.pth'))

    logger.info("Starting model training...")
    results = trainer.train(
        data=data,
        rf_params=rf_params,
        transformer_params=transformer_params,
        output_dir=args.output_dir
    )
    logger.info("Model training completed.")

    # Get feature importance
    feature_names = []
    for category, names in feature_categories.items():
        feature_names.extend(names)

    importance_df = model.get_feature_importance(feature_names)

    # Save feature importance
    importance_file = os.path.join(metrics_dir, 'feature_importance.tsv')
    importance_df.to_csv(importance_file, sep='\t', index=False)

    # Save top 50 features
    top_features_file = os.path.join(metrics_dir, 'top_features.tsv')
    importance_df.head(50).to_csv(top_features_file, sep='\t', index=False)

    # Plot feature importance
    logger.info("Generating feature importance plots...")
    plot_feature_importance(importance_df, metrics_dir)

    # Plot training history
    if 'transformer_history' in results:
        logger.info("Generating training history plots...")
        plot_training_history(results['transformer_history'], metrics_dir)

    # Save final metrics
    metrics_file = os.path.join(metrics_dir, 'metrics.tsv')
    save_metrics_to_tsv(model.get_metrics(), metrics_file)

    # Save model configuration
    config = {
        'feature_dims': {k: int(v) for k, v in feature_dims.items()},
        'transformer_hidden_dims': args.transformer_hidden_dims,
        'transformer_dropout_rates': args.transformer_dropout_rates,
        'transformer_heads': args.transformer_heads,
        'transformer_layers': args.transformer_layers,
        'use_batch_norm': not args.no_batch_norm,
        'use_residual': not args.no_residual,
        'use_positional_encoding': not args.no_positional_encoding,
        'rf_n_estimators': args.rf_n_estimators,
        'rf_max_depth': args.rf_max_depth,
        'rf_min_samples_split': args.rf_min_samples_split,
        'rf_min_samples_leaf': args.rf_min_samples_leaf,
        'learning_rate': args.learning_rate,
        'weight_decay': args.weight_decay,
        'batch_size': args.batch_size,
        'epochs': args.epochs,
        'patience': args.patience
    }

    with open(os.path.join(args.output_dir, 'model_config.json'), 'w') as f:
        json.dump(config, f, indent=2, default=str)

    # Log final metrics
    logger.info("Final metrics:")
    for metric, value in model.get_metrics().items():
        logger.info(f"  {metric}: {value:.4f}")

    logger.info(f"Training complete. Models saved to {args.output_dir}")
    logger.info(f"Metrics and plots saved to {metrics_dir}")


if __name__ == '__main__':
    main()
