#!/bin/bash

# Run the hybrid tabular model with feature selection and ensemble architecture

# Activate virtual environment if needed
# source venv/bin/activate

# Set output directory
OUTPUT_DIR="models/hybrid_tabular"

# Make directory
mkdir -p ${OUTPUT_DIR}

# Run the model
python hybrid_tabular_model.py \
  --feature_file ./training_data/combined_features.tsv \
  --metadata_file ./training_data/metadata.tsv \
  --output_dir ${OUTPUT_DIR} \
  --n_features 500 \
  --batch_size 64 \
  --epochs 100 \
  --lr 0.001 \
  --rf_trees 200 \
  --hidden_dim 128 \
  --dropout 0.3

# Make script executable: chmod +x run_hybrid_tabular_model.sh 