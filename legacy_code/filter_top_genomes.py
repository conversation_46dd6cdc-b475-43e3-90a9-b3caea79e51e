#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Filter the dataset to the top 2000 genomes with the most reliable temperature data.
"""

import os
import argparse
import logging
import numpy as np
import pandas as pd
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def filter_top_genomes(feature_file, metadata_file, output_dir, n_top=2000):
    """
    Filter the dataset to the top n genomes with the most reliable temperature data.
    
    Args:
        feature_file: Path to the combined feature file (TSV)
        metadata_file: Path to the metadata file (TSV)
        output_dir: Directory to save the filtered dataset
        n_top: Number of top genomes to keep
        
    Returns:
        Tuple of (filtered_features_path, filtered_metadata_path)
    """
    logger.info(f"Loading features from {feature_file}")
    features_df = pd.read_csv(feature_file, sep='\t', index_col='genome_id')
    
    logger.info(f"Loading metadata from {metadata_file}")
    metadata_df = pd.read_csv(metadata_file, sep='\t', index_col='genome_id')
    
    # Get common indices
    common_indices = features_df.index.intersection(metadata_df.index)
    features_df = features_df.loc[common_indices]
    metadata_df = metadata_df.loc[common_indices]
    
    logger.info(f"Found {len(common_indices)} genomes with both features and metadata")
    
    # Calculate temperature reliability score
    # We'll use a heuristic based on the kingdom and the temperature value
    # Bacteria and archaea with temperatures between 20-80°C are more likely to be reliable
    
    # Create a reliability score
    temp_values = metadata_df['optimal_temperature'].values
    
    # Higher score for temperatures in the middle range (30-70°C)
    temp_score = np.exp(-0.01 * (temp_values - 50)**2)
    
    # Higher score for bacteria and archaea (they have more reliable temperature data)
    kingdom_score = metadata_df['kingdom'].map({'bac': 1.0, 'arc': 1.0, 'euk': 0.8, 'vir': 0.6}).fillna(0.7)
    
    # Combine scores
    reliability_score = temp_score * kingdom_score
    
    # Add score to metadata
    metadata_df['reliability_score'] = reliability_score
    
    # Sort by reliability score
    sorted_metadata = metadata_df.sort_values('reliability_score', ascending=False)
    
    # Take top n genomes
    top_genomes = sorted_metadata.index[:n_top]
    
    logger.info(f"Selected top {len(top_genomes)} genomes based on temperature reliability score")
    
    # Filter features and metadata
    filtered_features = features_df.loc[top_genomes]
    filtered_metadata = metadata_df.loc[top_genomes]
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Save filtered datasets
    filtered_features_path = os.path.join(output_dir, 'top_genomes_features.tsv')
    filtered_metadata_path = os.path.join(output_dir, 'top_genomes_metadata.tsv')
    
    filtered_features.to_csv(filtered_features_path, sep='\t')
    filtered_metadata.to_csv(filtered_metadata_path, sep='\t')
    
    logger.info(f"Saved filtered features to {filtered_features_path}")
    logger.info(f"Saved filtered metadata to {filtered_metadata_path}")
    
    return filtered_features_path, filtered_metadata_path

def main():
    parser = argparse.ArgumentParser(description='Filter dataset to top genomes with reliable temperature data')
    parser.add_argument('--feature-file', type=str, default='./training_data/combined_features.tsv',
                        help='Path to the combined feature file (TSV)')
    parser.add_argument('--metadata-file', type=str, default='./training_data/metadata.tsv',
                        help='Path to the metadata file (TSV)')
    parser.add_argument('--output-dir', type=str, default='./training_data/filtered',
                        help='Directory to save the filtered dataset')
    parser.add_argument('--n-top', type=int, default=2000,
                        help='Number of top genomes to keep')
    
    args = parser.parse_args()
    
    try:
        filter_top_genomes(args.feature_file, args.metadata_file, args.output_dir, args.n_top)
        logger.info("Filtering completed successfully")
        return 0
    
    except Exception as e:
        logger.error(f"Error filtering dataset: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == '__main__':
    main()
