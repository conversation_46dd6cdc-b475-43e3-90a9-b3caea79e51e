#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Combined Hybrid Model for DeepMu.

This script integrates the enhanced temperature model with the growth rate prediction
component to create a complete hybrid model for microbial phenotype prediction.
"""

import os
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import TensorDataset, DataLoader
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import RobustScaler
import joblib
import logging
import argparse
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, Tuple, List, Union
import sys
import time

# Import our enhanced temperature model
from enhanced_temp_model import EnhancedTemperatureModel, EnhancedTemperatureTrainer

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Use GPU if available
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

class GrowthRateModel(nn.Module):
    """Neural network model for growth rate prediction."""
    
    def __init__(self, input_dim: int, hidden_dims: List[int] = [128, 64, 32]):
        super().__init__()
        
        # Feature normalization
        self.feature_norm = nn.LayerNorm(input_dim)
        
        # Main network with residual connections
        self.layers = nn.ModuleList()
        
        # Input layer
        self.layers.append(nn.Linear(input_dim, hidden_dims[0]))
        self.layers.append(nn.BatchNorm1d(hidden_dims[0]))
        self.layers.append(nn.ReLU())
        self.layers.append(nn.Dropout(0.2))
        
        # Hidden layers with residual connections
        for i in range(1, len(hidden_dims)):
            # Add linear layer
            self.layers.append(nn.Linear(hidden_dims[i-1], hidden_dims[i]))
            self.layers.append(nn.BatchNorm1d(hidden_dims[i]))
            self.layers.append(nn.ReLU())
            self.layers.append(nn.Dropout(0.2))
        
        # Output layer
        self.output_layer = nn.Linear(hidden_dims[-1], 1)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Forward pass through the model."""
        # Normalize input features
        x = self.feature_norm(x)
        
        # Process through main network with residual connections
        prev_block_output = None
        
        for i in range(0, len(self.layers), 4):
            if i + 3 < len(self.layers):  # Ensure we have a complete block
                # Store input to this block for residual connection
                block_input = x
                
                # Apply the block (linear + batch norm + activation + dropout)
                x = self.layers[i](x)       # Linear
                x = self.layers[i+1](x)     # BatchNorm
                x = self.layers[i+2](x)     # ReLU
                x = self.layers[i+3](x)     # Dropout
                
                # Add residual connection if dimensions match
                if prev_block_output is not None and prev_block_output.shape == x.shape:
                    x = x + prev_block_output
                
                # Store this block's output for the next residual connection
                prev_block_output = x
        
        # Final output layer
        return self.output_layer(x)

class HybridModel:
    """Combined hybrid model for growth rate and temperature prediction."""
    
    def __init__(self, 
                output_dir: str = "models/hybrid_model",
                growth_n_features: int = 250,
                temp_n_features: int = 800):
        """Initialize the hybrid model."""
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        
        # Create plots directory
        self.plots_dir = os.path.join(output_dir, "plots")
        os.makedirs(self.plots_dir, exist_ok=True)
        
        # Set up logging to file
        file_handler = logging.FileHandler(os.path.join(output_dir, "training.log"))
        file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
        logger.addHandler(file_handler)
        
        # Initialize component models
        self.growth_model = None
        self.growth_scaler = None
        self.growth_feature_indices = None
        self.growth_n_features = growth_n_features
        
        # Temperature model will be handled by EnhancedTemperatureTrainer
        self.temp_trainer = EnhancedTemperatureTrainer(
            output_dir=os.path.join(output_dir, "temp_model")
        )
        self.temp_n_features = temp_n_features
        
    def load_data(self, feature_file: str, metadata_file: str) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """Load and prepare data."""
        logger.info(f"Loading features from {feature_file}")
        features = pd.read_csv(feature_file, sep='\t', index_col='genome_id')
        
        logger.info(f"Loading metadata from {metadata_file}")
        metadata = pd.read_csv(metadata_file, sep='\t', index_col='genome_id')
        
        # Get common samples
        common_indices = features.index.intersection(metadata.index)
        logger.info(f"Found {len(common_indices)} common samples")
        
        features = features.loc[common_indices]
        metadata = metadata.loc[common_indices]
        
        # Handle missing values
        features = features.fillna(0)
        
        return features, metadata
    
    def prepare_growth_features(self, features: pd.DataFrame) -> pd.DataFrame:
        """Select top features by variance for growth rate prediction."""
        if self.growth_n_features and features.shape[1] > self.growth_n_features:
            logger.info(f"Selecting top {self.growth_n_features} features by variance for growth rate model")
            feature_variance = features.var().sort_values(ascending=False)
            selected_features = feature_variance.index[:self.growth_n_features].tolist()
            self.growth_feature_indices = selected_features
            return features[selected_features]
        else:
            self.growth_feature_indices = features.columns.tolist()
            return features
    
    def prepare_growth_data_loaders(self, features: pd.DataFrame, growth_rates: pd.Series, 
                                   batch_size: int = 32) -> Dict:
        """Split growth rate data and create data loaders."""
        # Split data
        X_train, X_val, y_train, y_val = train_test_split(
            features, growth_rates, test_size=0.2, random_state=42
        )
        
        # Scale features
        self.growth_scaler = RobustScaler()
        X_train_scaled = self.growth_scaler.fit_transform(X_train)
        X_val_scaled = self.growth_scaler.transform(X_val)
        
        # Create datasets
        train_dataset = TensorDataset(
            torch.FloatTensor(X_train_scaled).to(device),
            torch.FloatTensor(y_train.values.reshape(-1, 1)).to(device)
        )
        
        val_dataset = TensorDataset(
            torch.FloatTensor(X_val_scaled).to(device),
            torch.FloatTensor(y_val.values.reshape(-1, 1)).to(device)
        )
        
        # Create data loaders
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size=batch_size*2)
        
        return {
            "train_loader": train_loader,
            "val_loader": val_loader,
            "X_train": X_train,
            "X_val": X_val,
            "y_train": y_train,
            "y_val": y_val,
            "X_train_scaled": X_train_scaled,
            "X_val_scaled": X_val_scaled
        }
    
    def train_growth_model(self, features: pd.DataFrame, growth_rates: pd.Series,
                          epochs: int = 200, lr: float = 0.001, batch_size: int = 32,
                          patience: int = 30) -> None:
        """Train the growth rate prediction model."""
        # Select features for growth rate model
        growth_features = self.prepare_growth_features(features)
        
        # Prepare data
        logger.info("Preparing data for growth rate model")
        data = self.prepare_growth_data_loaders(growth_features, growth_rates, batch_size)
        train_loader = data["train_loader"]
        val_loader = data["val_loader"]
        
        # Initialize model
        input_dim = data["X_train"].shape[1]
        logger.info(f"Initializing growth model with input dimension: {input_dim}")
        self.growth_model = GrowthRateModel(input_dim=input_dim).to(device)
        
        # Create optimizer
        optimizer = optim.Adam(self.growth_model.parameters(), lr=lr)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, mode='min', factor=0.5, patience=5
        )
        
        # Set up early stopping
        early_stopping_counter = 0
        best_val_loss = float('inf')
        best_model_state = None
        
        # Training history
        history = {
            "train_loss": [],
            "val_loss": [],
            "train_r2": [],
            "val_r2": []
        }
        
        # Training loop
        for epoch in range(epochs):
            # Training phase
            self.growth_model.train()
            train_loss = 0.0
            
            for X_batch, y_batch in train_loader:
                optimizer.zero_grad()
                
                # Forward pass
                y_pred = self.growth_model(X_batch)
                loss = F.mse_loss(y_pred, y_batch)
                
                # Backward pass
                loss.backward()
                optimizer.step()
                train_loss += loss.item() * len(y_batch)
            
            train_loss /= len(train_loader.dataset)
            
            # Validation phase
            self.growth_model.eval()
            val_loss = 0.0
            val_preds = []
            val_targets = []
            
            with torch.no_grad():
                for X_batch, y_batch in val_loader:
                    # Forward pass
                    y_pred = self.growth_model(X_batch)
                    loss = F.mse_loss(y_pred, y_batch)
                    
                    val_loss += loss.item() * len(y_batch)
                    
                    # Store predictions and targets for metrics calculation
                    val_preds.extend(y_pred.cpu().numpy().flatten())
                    val_targets.extend(y_batch.cpu().numpy().flatten())
            
            val_loss /= len(val_loader.dataset)
            
            # Update scheduler
            scheduler.step(val_loss)
            
            # Calculate R² metrics
            train_preds = []
            train_targets = []
            
            self.growth_model.eval()
            with torch.no_grad():
                for X_batch, y_batch in train_loader:
                    y_pred = self.growth_model(X_batch)
                    train_preds.extend(y_pred.cpu().numpy().flatten())
                    train_targets.extend(y_batch.cpu().numpy().flatten())
            
            train_r2 = r2_score(train_targets, train_preds)
            val_r2 = r2_score(val_targets, val_preds)
            
            # Store metrics in history
            history["train_loss"].append(train_loss)
            history["val_loss"].append(val_loss)
            history["train_r2"].append(train_r2)
            history["val_r2"].append(val_r2)
            
            # Log progress every 10 epochs
            if (epoch + 1) % 10 == 0:
                logger.info(f"Epoch {epoch+1}/{epochs}")
                logger.info(f"  Train Loss: {train_loss:.6f}, Train R²: {train_r2:.6f}")
                logger.info(f"  Val Loss: {val_loss:.6f}, Val R²: {val_r2:.6f}")
                logger.info(f"  Current LR: {optimizer.param_groups[0]['lr']:.6f}")
            
            # Check for improvement
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                best_model_state = self.growth_model.state_dict().copy()
                early_stopping_counter = 0
                logger.info(f"  Validation loss improved to {val_loss:.6f}")
            else:
                early_stopping_counter += 1
                
                if early_stopping_counter >= patience:
                    logger.info(f"Early stopping triggered after {epoch+1} epochs")
                    break
        
        # Load best model
        self.growth_model.load_state_dict(best_model_state)
        
        # Plot training history
        self._plot_growth_training_history(history)
        
        # Final evaluation
        self._evaluate_growth_model(data["X_train"], data["y_train"], data["X_val"], data["y_val"])
        
        # Save model and artifacts
        self._save_growth_model()
    
    def _plot_growth_training_history(self, history: Dict):
        """Plot training and validation metrics over epochs for growth rate model."""
        epochs = range(1, len(history["train_loss"]) + 1)
        
        # Plot loss
        plt.figure(figsize=(10, 5))
        plt.subplot(1, 2, 1)
        plt.plot(epochs, history["train_loss"], 'b-', label='Training Loss')
        plt.plot(epochs, history["val_loss"], 'r-', label='Validation Loss')
        plt.title('Growth Rate Model Loss')
        plt.xlabel('Epochs')
        plt.ylabel('Loss')
        plt.legend()
        
        # Plot R²
        plt.subplot(1, 2, 2)
        plt.plot(epochs, history["train_r2"], 'b-', label='Training R²')
        plt.plot(epochs, history["val_r2"], 'r-', label='Validation R²')
        plt.title('Growth Rate Model R²')
        plt.xlabel('Epochs')
        plt.ylabel('R²')
        plt.legend()
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.plots_dir, 'growth_training_history.png'))
        plt.close()
        
        logger.info(f"Growth model training history plot saved to {os.path.join(self.plots_dir, 'growth_training_history.png')}")
    
    def _evaluate_growth_model(self, X_train, y_train, X_val, y_val):
        """Evaluate the growth rate model on training and validation data."""
        # Ensure model is in evaluation mode
        self.growth_model.eval()
        
        # Scale data
        X_train_scaled = self.growth_scaler.transform(X_train)
        X_val_scaled = self.growth_scaler.transform(X_val)
        
        # Convert to tensors
        X_train_tensor = torch.FloatTensor(X_train_scaled).to(device)
        X_val_tensor = torch.FloatTensor(X_val_scaled).to(device)
        
        # Get predictions
        with torch.no_grad():
            train_preds = self.growth_model(X_train_tensor).cpu().numpy().flatten()
            val_preds = self.growth_model(X_val_tensor).cpu().numpy().flatten()
        
        # Calculate metrics
        train_r2 = r2_score(y_train.values, train_preds)
        train_rmse = np.sqrt(mean_squared_error(y_train.values, train_preds))
        train_mae = mean_absolute_error(y_train.values, train_preds)
        
        val_r2 = r2_score(y_val.values, val_preds)
        val_rmse = np.sqrt(mean_squared_error(y_val.values, val_preds))
        val_mae = mean_absolute_error(y_val.values, val_preds)
        
        # Log results
        logger.info("Growth Model Final Evaluation Results:")
        logger.info("Training Set:")
        logger.info(f"  R²: {train_r2:.6f}")
        logger.info(f"  RMSE: {train_rmse:.6f}")
        logger.info(f"  MAE: {train_mae:.6f}")
        
        logger.info("Validation Set:")
        logger.info(f"  R²: {val_r2:.6f}")
        logger.info(f"  RMSE: {val_rmse:.6f}")
        logger.info(f"  MAE: {val_mae:.6f}")
        
        # Create visualization
        plt.figure(figsize=(12, 5))
        
        # Training set
        plt.subplot(1, 2, 1)
        plt.scatter(y_train, train_preds, alpha=0.5)
        min_val = min(y_train.min(), train_preds.min())
        max_val = max(y_train.max(), train_preds.max())
        plt.plot([min_val, max_val], [min_val, max_val], 'r--')
        plt.xlabel('True Growth Rate')
        plt.ylabel('Predicted Growth Rate')
        plt.title(f'Training Set (R² = {train_r2:.4f})')
        
        # Validation set
        plt.subplot(1, 2, 2)
        plt.scatter(y_val, val_preds, alpha=0.5)
        min_val = min(y_val.min(), val_preds.min())
        max_val = max(y_val.max(), val_preds.max())
        plt.plot([min_val, max_val], [min_val, max_val], 'r--')
        plt.xlabel('True Growth Rate')
        plt.ylabel('Predicted Growth Rate')
        plt.title(f'Validation Set (R² = {val_r2:.4f})')
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.plots_dir, 'growth_prediction_results.png'))
        plt.close()
        
        # Error distribution plot
        plt.figure(figsize=(10, 6))
        train_errors = y_train - train_preds
        val_errors = y_val - val_preds
        
        plt.hist(train_errors, alpha=0.5, bins=30, label='Training')
        plt.hist(val_errors, alpha=0.5, bins=30, label='Validation')
        plt.axvline(x=0, color='r', linestyle='--')
        plt.xlabel('Error (True - Predicted)')
        plt.ylabel('Frequency')
        plt.title('Growth Rate Error Distribution')
        plt.legend()
        plt.savefig(os.path.join(self.plots_dir, 'growth_error_distribution.png'))
        plt.close()
    
    def _save_growth_model(self):
        """Save growth rate model and artifacts."""
        growth_model_dir = os.path.join(self.output_dir, "growth_model")
        os.makedirs(growth_model_dir, exist_ok=True)
        
        # Save model state
        torch.save(self.growth_model.state_dict(), os.path.join(growth_model_dir, "model.pt"))
        
        # Save scaler
        joblib.dump(self.growth_scaler, os.path.join(growth_model_dir, "scaler.pkl"))
        
        # Save feature indices
        if self.growth_feature_indices:
            joblib.dump(self.growth_feature_indices, os.path.join(growth_model_dir, "feature_indices.pkl"))
        
        # Save model info
        model_info = {
            "input_dim": len(self.growth_feature_indices) if self.growth_feature_indices else None,
        }
        joblib.dump(model_info, os.path.join(growth_model_dir, "model_info.pkl"))
        
        logger.info(f"Growth model and artifacts saved to {growth_model_dir}")
    
    def load_growth_model(self, model_dir: str):
        """Load a trained growth rate model."""
        # Load model info
        model_info = joblib.load(os.path.join(model_dir, "model_info.pkl"))
        
        # Initialize model
        self.growth_model = GrowthRateModel(input_dim=model_info["input_dim"]).to(device)
        
        # Load model weights
        self.growth_model.load_state_dict(torch.load(os.path.join(model_dir, "model.pt")))
        
        # Load scaler
        self.growth_scaler = joblib.load(os.path.join(model_dir, "scaler.pkl"))
        
        # Load feature indices
        try:
            self.growth_feature_indices = joblib.load(os.path.join(model_dir, "feature_indices.pkl"))
        except:
            self.growth_feature_indices = None
        
        logger.info(f"Growth model loaded from {model_dir}")
    
    def train(self, features: pd.DataFrame, metadata: pd.DataFrame,
              train_growth: bool = True, train_temp: bool = True,
              batch_size: int = 32, patience: int = 30):
        """Train both growth rate and temperature models."""
        start_time = time.time()
        
        # Train growth rate model
        if train_growth:
            logger.info("Training growth rate model...")
            self.train_growth_model(
                features, 
                metadata['growth_rate'],
                batch_size=batch_size,
                patience=patience
            )
        
        # Train temperature model
        if train_temp:
            logger.info("Training temperature model...")
            
            # Instead of using load_data which expects file paths, select features here
            # and pass directly to train method
            if self.temp_n_features and features.shape[1] > self.temp_n_features:
                logger.info(f"Selecting top {self.temp_n_features} features by variance for temperature model")
                feature_variance = features.var().sort_values(ascending=False)
                selected_features = feature_variance.index[:self.temp_n_features].tolist()
                temp_features = features[selected_features]
            else:
                temp_features = features
                
            temperatures = metadata['optimal_temperature']
            
            # Train temperature model with the prepared data
            self.temp_trainer.train(
                temp_features,
                temperatures,
                batch_size=batch_size,
                patience=patience
            )
        
        # Log total training time
        total_time = time.time() - start_time
        logger.info(f"Total training time: {total_time:.2f} seconds ({total_time/60:.2f} minutes)")
    
    def predict_growth_rate(self, features: pd.DataFrame) -> np.ndarray:
        """Make growth rate predictions with the trained model."""
        if self.growth_model is None:
            raise ValueError("Growth rate model not trained yet")
        
        # Apply feature selection if needed
        if self.growth_feature_indices:
            features = features[self.growth_feature_indices]
        
        # Scale features
        X_scaled = self.growth_scaler.transform(features)
        
        # Convert to tensor
        X_tensor = torch.FloatTensor(X_scaled).to(device)
        
        # Get predictions
        self.growth_model.eval()
        with torch.no_grad():
            predictions = self.growth_model(X_tensor).cpu().numpy().flatten()
        
        return predictions
    
    def predict_temperature(self, features: pd.DataFrame) -> np.ndarray:
        """Make temperature predictions using the enhanced temperature model."""
        # Apply feature selection if needed based on what was selected during training
        if self.temp_n_features and features.shape[1] > self.temp_n_features:
            # Get the feature indices from the temperature trainer
            if hasattr(self.temp_trainer, 'feature_indices') and self.temp_trainer.feature_indices:
                features = features[self.temp_trainer.feature_indices]
            else:
                # If no explicit indices saved, select top features by variance
                feature_variance = features.var().sort_values(ascending=False)
                selected_features = feature_variance.index[:self.temp_n_features].tolist()
                features = features[selected_features]
                
        return self.temp_trainer.predict(features)
    
    def predict(self, features: pd.DataFrame) -> Dict[str, np.ndarray]:
        """Make predictions for both growth rate and temperature."""
        growth_predictions = self.predict_growth_rate(features)
        temp_predictions = self.predict_temperature(features)
        
        return {
            "growth_rate": growth_predictions,
            "optimal_temperature": temp_predictions
        }

def main():
    """Main function to run the model."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Train and evaluate the hybrid model")
    parser.add_argument("--features", type=str, default="training_data/combined_features.tsv",
                      help="Path to feature file (TSV format)")
    parser.add_argument("--metadata", type=str, default="training_data/metadata.tsv",
                      help="Path to metadata file (TSV format)")
    parser.add_argument("--output-dir", type=str, default="models/hybrid_model",
                      help="Directory to save model outputs")
    parser.add_argument("--growth-features", type=int, default=250,
                      help="Number of features to use for growth rate prediction")
    parser.add_argument("--temp-features", type=int, default=800,
                      help="Number of features to use for temperature prediction")
    parser.add_argument("--train-growth", action="store_true", default=True,
                      help="Train the growth rate model")
    parser.add_argument("--train-temp", action="store_true", default=True,
                      help="Train the temperature model")
    parser.add_argument("--batch-size", type=int, default=32,
                      help="Batch size for training")
    parser.add_argument("--patience", type=int, default=30,
                      help="Early stopping patience")
    
    args = parser.parse_args()
    
    # Initialize hybrid model
    model = HybridModel(
        output_dir=args.output_dir,
        growth_n_features=args.growth_features,
        temp_n_features=args.temp_features
    )
    
    # Load data
    features, metadata = model.load_data(args.features, args.metadata)
    
    # Train model
    model.train(
        features, 
        metadata,
        train_growth=args.train_growth,
        train_temp=args.train_temp,
        batch_size=args.batch_size,
        patience=args.patience
    )
    
    logger.info("Training complete!")

if __name__ == "__main__":
    main()