#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Run Hybrid Model with Optimally Integrated Components v4.
This script trains a hybrid model that combines the best of both worlds:
1. The successful LightGBM+ensemble approach for growth rate prediction from the original hybrid model
2. The highly successful temperature prediction architecture from the standalone temperature model
"""

import os
import argparse
import logging
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
import joblib
from typing import Dict, List, Tuple, Optional

# Import only necessary functions
from train_hybrid_with_enhanced_nn import load_data, evaluate_model

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import the required components
from hybrid_model_with_enhanced_nn import FeatureDataset, HybridModelWithEnhancedNN

class ResidualBlock(nn.Module):
    """
    Residual block with batch normalization and dropout.
    """
    def __init__(self, dim, dropout_rate=0.3):
        super(ResidualBlock, self).__init__()
        self.block = nn.Sequential(
            nn.Linear(dim, dim),
            nn.BatchNorm1d(dim),
            nn.LeakyReLU(0.1),
            nn.Dropout(dropout_rate),
            nn.Linear(dim, dim),
            nn.BatchNorm1d(dim)
        )
        self.activation = nn.LeakyReLU(0.1)
        
    def forward(self, x):
        residual = x
        out = self.block(x)
        out += residual  # Add residual connection
        return self.activation(out)

class AdvancedRegularizedDNN(nn.Module):
    """
    Advanced Regularized DNN with residual connections and optimized architecture.
    This is based on the successful standalone temperature model.
    """
    def __init__(self, input_dim, hidden_dims=[512, 512, 512, 384, 256, 128], 
                 dropout_rates=[0.2, 0.3, 0.3, 0.4, 0.5, 0.5], use_residual=True):
        super(AdvancedRegularizedDNN, self).__init__()
        
        # Input layer
        layers = [nn.Linear(input_dim, hidden_dims[0])]
        layers.append(nn.BatchNorm1d(hidden_dims[0]))
        layers.append(nn.LeakyReLU(0.1))
        layers.append(nn.Dropout(dropout_rates[0]))
        
        # Hidden layers with residual connections
        prev_dim = hidden_dims[0]
        for i in range(1, len(hidden_dims)):
            if use_residual and prev_dim == hidden_dims[i]:
                # Add residual block if dimensions match
                layers.append(ResidualBlock(hidden_dims[i], dropout_rates[i]))
            else:
                # Regular layer if dimensions don't match
                layers.append(nn.Linear(prev_dim, hidden_dims[i]))
                layers.append(nn.BatchNorm1d(hidden_dims[i]))
                layers.append(nn.LeakyReLU(0.1))
                layers.append(nn.Dropout(dropout_rates[i]))
            
            prev_dim = hidden_dims[i]
        
        # Feature extractor
        self.feature_extractor = nn.Sequential(*layers)
        
        # Output layer
        self.output_layer = nn.Linear(prev_dim, 1)
        
        # Initialize weights
        self._init_weights()
    
    def _init_weights(self):
        """Initialize weights with improved initialization."""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                # Use Xavier/Glorot initialization with gain for LeakyReLU
                nn.init.xavier_uniform_(m.weight, gain=nn.init.calculate_gain('leaky_relu', 0.1))
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
    
    def forward(self, x):
        features = self.feature_extractor(x)
        output = self.output_layer(features)
        return output.squeeze()

def prepare_data_optimized(
    features: pd.DataFrame, 
    metadata: pd.DataFrame,
    n_features: int = 250, 
    output_dir: Optional[str] = None
) -> Tuple:
    """
    Prepare data with highly optimized processing for both growth rate and temperature prediction.
    
    Args:
        features: Feature DataFrame
        metadata: Metadata DataFrame
        n_features: Number of features to use
        output_dir: Directory to save outputs
        
    Returns:
        Tuple containing processed datasets and transformation info
    """
    logger.info("Preparing data with optimal processing for both growth rate and temperature prediction...")

    # Extract targets
    y_growth_original = metadata['growth_rate']
    y_temp_original = metadata['optimal_temperature']
    
    # Apply square root transformation only to growth rate
    logger.info("Applying square root transformation to growth rate only")
    y_growth = np.sqrt(y_growth_original)
    y_temp = y_temp_original  # Keep temperature in original scale
    
    # Create bins for stratification based on temperature (more important for our focus)
    n_bins = 10
    y_binned = pd.qcut(y_temp, n_bins, labels=False, duplicates='drop')
    
    # First split data into train+val and test sets with stratification on temperature
    X_train_val, X_test, y_growth_train_val, y_growth_test, y_temp_train_val, y_temp_test, y_binned_train_val, _ = train_test_split(
        features, y_growth, y_temp, y_binned, test_size=0.2, random_state=42, shuffle=True, stratify=y_binned
    )

    # Then split train+val into train and validation sets with stratification
    X_train, X_val, y_growth_train, y_growth_val, y_temp_train, y_temp_val = train_test_split(
        X_train_val, y_growth_train_val, y_temp_train_val, test_size=0.25, random_state=42, 
        shuffle=True, stratify=y_binned_train_val
    )

    logger.info(f"Data split: Train={X_train.shape[0]} samples, Validation={X_val.shape[0]} samples, Test={X_test.shape[0]} samples")
    
    # Apply target scaling using RobustScaler for both targets
    growth_scaler = RobustScaler()
    temp_scaler = RobustScaler()
    
    # Reshape for scaler
    y_growth_train_reshaped = y_growth_train.values.reshape(-1, 1)
    y_growth_val_reshaped = y_growth_val.values.reshape(-1, 1)
    y_growth_test_reshaped = y_growth_test.values.reshape(-1, 1)
    
    y_temp_train_reshaped = y_temp_train.values.reshape(-1, 1)
    y_temp_val_reshaped = y_temp_val.values.reshape(-1, 1)
    y_temp_test_reshaped = y_temp_test.values.reshape(-1, 1)
    
    # Fit scalers on training data only
    growth_scaler.fit(y_growth_train_reshaped)
    temp_scaler.fit(y_temp_train_reshaped)
    
    # Transform all sets
    y_growth_train_scaled = growth_scaler.transform(y_growth_train_reshaped).flatten()
    y_growth_val_scaled = growth_scaler.transform(y_growth_val_reshaped).flatten()
    y_growth_test_scaled = growth_scaler.transform(y_growth_test_reshaped).flatten()
    
    y_temp_train_scaled = temp_scaler.transform(y_temp_train_reshaped).flatten()
    y_temp_val_scaled = temp_scaler.transform(y_temp_val_reshaped).flatten()
    y_temp_test_scaled = temp_scaler.transform(y_temp_test_reshaped).flatten()
    
    # Convert back to pandas Series with original indices
    y_growth_train = pd.Series(y_growth_train_scaled, index=y_growth_train.index)
    y_growth_val = pd.Series(y_growth_val_scaled, index=y_growth_val.index)
    y_growth_test = pd.Series(y_growth_test_scaled, index=y_growth_test.index)
    
    y_temp_train = pd.Series(y_temp_train_scaled, index=y_temp_train.index)
    y_temp_val = pd.Series(y_temp_val_scaled, index=y_temp_val.index)
    y_temp_test = pd.Series(y_temp_test_scaled, index=y_temp_test.index)
    
    logger.info("Applied target scaling using RobustScaler for both targets")
    
    # Feature selection - prioritize a balanced mix for both tasks
    # Priority features for temperature
    temp_priority_features = [col for col in features.columns if 'GC' in col]
    temp_priority_features.extend([col for col in features.columns if 'temp' in col.lower()])
    temp_priority_features.extend([col for col in features.columns if 'amino' in col.lower()])
    
    # Priority features for growth rate (codon usage, tRNA-related features)
    growth_priority_features = [col for col in features.columns if 'codon' in col.lower()]
    growth_priority_features.extend([col for col in features.columns if 'tRNA' in col])
    growth_priority_features.extend([col for col in features.columns if 'rRNA' in col])
    
    # Combine priority features and deduplicate
    priority_features = list(set(temp_priority_features + growth_priority_features))
    
    # Add remaining features up to n_features
    remaining_features = [col for col in features.columns if col not in priority_features]
    selected_features = priority_features + remaining_features
    selected_features = selected_features[:n_features]
    
    logger.info(f"Selected {len(selected_features)} features with balanced priority for both growth rate and temperature")
    
    # Filter to selected features
    X_train = X_train[selected_features]
    X_val = X_val[selected_features]
    X_test = X_test[selected_features]

    # Store transformation information
    transform_info = {
        'growth_scaler': growth_scaler,
        'temp_scaler': temp_scaler,
        'growth_sqrt_transform': True,
        'temp_sqrt_transform': False
    }
    
    # Save transformation info if output_dir is provided
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
        joblib.dump(transform_info, os.path.join(output_dir, 'transform_info.joblib'))
        joblib.dump(selected_features, os.path.join(output_dir, 'selected_features.joblib'))
        logger.info(f"Saved transformation info to {output_dir}")

    return (
        X_train, y_growth_train, y_temp_train, 
        X_val, y_growth_val, y_temp_val, 
        X_test, y_growth_test, y_temp_test, 
        selected_features, transform_info
    )

class EarlyStopping:
    """Early stopping to prevent overfitting"""
    def __init__(self, patience=15, min_delta=0, restore_best_weights=True):
        self.patience = patience
        self.min_delta = min_delta
        self.restore_best_weights = restore_best_weights
        self.best_loss = float('inf')
        self.counter = 0
        self.best_weights = None
        self.early_stop = False
        
    def __call__(self, val_loss, model):
        if val_loss < self.best_loss - self.min_delta:
            self.best_loss = val_loss
            self.counter = 0
            if self.restore_best_weights:
                self.best_weights = model.state_dict().copy()
        else:
            self.counter += 1
            if self.counter >= self.patience:
                self.early_stop = True
                if self.restore_best_weights and self.best_weights is not None:
                    model.load_state_dict(self.best_weights)
                return True
        return False
    
    def restore_weights(self, model):
        if self.restore_best_weights and self.best_weights is not None:
            model.load_state_dict(self.best_weights)

class OptimallyIntegratedHybridModel(HybridModelWithEnhancedNN):
    """
    Hybrid model with optimally integrated components for growth rate and temperature prediction.
    Combines the best of both existing approaches.
    """
    
    def __init__(self, **kwargs):
        super(OptimallyIntegratedHybridModel, self).__init__(**kwargs)
    
    def _train_temperature_model(
        self,
        X_train_scaled: np.ndarray,
        y_temp_train: np.ndarray,
        X_val_scaled: np.ndarray,
        y_temp_val: np.ndarray,
        output_dir: Optional[str] = None
    ) -> None:
        """
        Train the advanced temperature model with integrated optimal components from standalone temperature model.
        
        Args:
            X_train_scaled: Scaled training features
            y_temp_train: Training temperature targets
            X_val_scaled: Scaled validation features
            y_temp_val: Validation temperature targets
            output_dir: Directory to save outputs
        """
        # Create datasets and dataloaders
        train_dataset = FeatureDataset(X_train_scaled, y_temp_train)
        val_dataset = FeatureDataset(X_val_scaled, y_temp_val)
        
        # Use carefully tuned batch sizes
        train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size=64, shuffle=False)
        
        # Initialize model with the advanced architecture from the successful standalone temperature model
        input_dim = X_train_scaled.shape[1]
        
        # Create an advanced model with optimal architecture
        self.temp_model = AdvancedRegularizedDNN(
            input_dim=input_dim,
            hidden_dims=[512, 512, 512, 384, 256, 128],  # Optimal architecture from standalone model
            dropout_rates=[0.2, 0.25, 0.3, 0.35, 0.4, 0.5],  # Carefully tuned dropout rates
            use_residual=True  # Enable residual connections
        ).to(self.device)
        
        # Use the optimizer setup from the successful standalone model
        optimizer = optim.AdamW(
            self.temp_model.parameters(), 
            lr=0.0005,  # Lower initial learning rate
            weight_decay=5e-5,  # Carefully tuned weight decay
            betas=(0.9, 0.999)
        )
        
        # Use the OneCycleLR scheduler that works well for this task
        scheduler = optim.lr_scheduler.OneCycleLR(
            optimizer,
            max_lr=0.005,  # Peak learning rate
            epochs=self.temp_nn_epochs,
            steps_per_epoch=len(train_loader),
            pct_start=0.3,  # 30% warmup
            anneal_strategy='cos',
            div_factor=25.0,
            final_div_factor=10000.0
        )
        
        # Use combined loss function that performed well in v2
        mse_criterion = nn.MSELoss()
        l1_criterion = nn.L1Loss()
        
        # Advanced early stopping mechanism
        early_stopping = EarlyStopping(patience=self.temp_nn_patience, restore_best_weights=True)
        
        # Training loop
        logger.info(f"Beginning temperature model training for {self.temp_nn_epochs} epochs...")
        
        for epoch in range(self.temp_nn_epochs):
            # Training phase
            self.temp_model.train()
            train_loss = 0.0
            
            for features, targets in train_loader:
                features, targets = features.to(self.device), targets.to(self.device)
                
                # Zero gradients
                optimizer.zero_grad()
                
                # Forward pass
                outputs = self.temp_model(features)
                
                # Combined loss with optimal 0.85/0.15 ratio from successful v2 model
                mse_loss = mse_criterion(outputs, targets)
                l1_loss = l1_criterion(outputs, targets)
                loss = 0.85 * mse_loss + 0.15 * l1_loss
                
                # Add L1 regularization with carefully tuned parameter
                l1_lambda = 5e-6
                if l1_lambda > 0:
                    l1_norm = sum(p.abs().sum() for p in self.temp_model.parameters())
                    loss += l1_lambda * l1_norm
                
                # Backward pass
                loss.backward()
                
                # Gradient clipping with optimal value
                torch.nn.utils.clip_grad_norm_(self.temp_model.parameters(), 0.5)
                
                # Update weights
                optimizer.step()
                
                # Update learning rate
                scheduler.step()
                
                # Add batch loss
                train_loss += loss.item() * features.size(0)
            
            # Calculate average loss
            train_loss /= len(train_loader.dataset)
            
            # Validation phase
            self.temp_model.eval()
            val_loss = 0.0
            predictions = []
            actuals = []
            
            with torch.no_grad():
                for features, targets in val_loader:
                    features, targets = features.to(self.device), targets.to(self.device)
                    
                    # Forward pass
                    outputs = self.temp_model(features)
                    
                    # Calculate loss (same combined loss)
                    mse_loss = mse_criterion(outputs, targets)
                    l1_loss = l1_criterion(outputs, targets)
                    loss = 0.85 * mse_loss + 0.15 * l1_loss
                    
                    # Add batch loss
                    val_loss += loss.item() * features.size(0)
                    
                    # Store predictions and targets for metrics
                    predictions.extend(outputs.cpu().numpy())
                    actuals.extend(targets.cpu().numpy())
            
            # Calculate average loss and metrics
            val_loss /= len(val_loader.dataset)
            
            # Calculate metrics
            val_rmse = np.sqrt(mean_squared_error(actuals, predictions))
            val_r2 = r2_score(actuals, predictions)
            val_mae = mean_absolute_error(actuals, predictions)
            
            # Log metrics at appropriate intervals
            if (epoch + 1) % 10 == 0 or epoch == 0:
                logger.info(f"Epoch {epoch+1}/{self.temp_nn_epochs} - "
                           f"Train Loss: {train_loss:.4f}, "
                           f"Val Loss: {val_loss:.4f}, "
                           f"Val RMSE: {val_rmse:.4f}, "
                           f"Val R²: {val_r2:.4f}, "
                           f"Val MAE: {val_mae:.4f}")
            
            # Check for early stopping
            if early_stopping(val_loss, self.temp_model):
                logger.info(f"Early stopping at epoch {epoch+1}")
                break
        
        # Ensure best weights are loaded
        early_stopping.restore_weights(self.temp_model)
        
        # Calculate final metrics
        self.temp_model.eval()
        predictions = []
        actuals = []
        
        with torch.no_grad():
            for features, targets in val_loader:
                features, targets = features.to(self.device), targets.to(self.device)
                outputs = self.temp_model(features)
                predictions.extend(outputs.cpu().numpy())
                actuals.extend(targets.cpu().numpy())
        
        final_rmse = np.sqrt(mean_squared_error(actuals, predictions))
        final_r2 = r2_score(actuals, predictions)
        final_mae = mean_absolute_error(actuals, predictions)
        
        logger.info(f"Final Temperature Model - "
                   f"Val RMSE: {final_rmse:.4f}, "
                   f"Val R²: {final_r2:.4f}, "
                   f"Val MAE: {final_mae:.4f}")

def main():
    """Main function."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Train hybrid model with optimally integrated components (v4)')
    parser.add_argument('--feature_file', type=str, required=True, help='Path to feature file (TSV)')
    parser.add_argument('--metadata_file', type=str, required=True, help='Path to metadata file (TSV)')
    parser.add_argument('--output_dir', type=str, required=True, help='Directory to save outputs')
    parser.add_argument('--n_features', type=int, default=250, help='Number of features to use')
    
    # Enhanced parameters with optimal values
    parser.add_argument('--temp_nn_epochs', type=int, default=250,
                       help='Maximum number of epochs for temperature neural network')
    parser.add_argument('--temp_nn_patience', type=int, default=25,
                       help='Early stopping patience for temperature neural network')
    parser.add_argument('--seed', type=int, default=42, help='Random seed')
    
    args = parser.parse_args()
    
    # Set random seeds for reproducibility
    np.random.seed(args.seed)
    torch.manual_seed(args.seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(args.seed)
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Load data
    features, metadata = load_data(args.feature_file, args.metadata_file)
    
    # Prepare data with optimized processing
    (X_train, y_growth_train, y_temp_train, 
     X_val, y_growth_val, y_temp_val, 
     X_test, y_growth_test, y_temp_test, 
     selected_features, transform_info) = prepare_data_optimized(
        features, metadata, args.n_features, args.output_dir
    )
    
    # Log dataset information
    logger.info(f"Training set: {X_train.shape[0]} samples, {X_train.shape[1]} features")
    logger.info(f"Validation set: {X_val.shape[0]} samples, {X_val.shape[1]} features")
    logger.info(f"Test set: {X_test.shape[0]} samples, {X_test.shape[1]} features")
    
    # Initialize the optimally integrated hybrid model
    model = OptimallyIntegratedHybridModel(
        # Growth rate ensemble parameters - keep best configuration
        growth_ensemble_weight_lr=0.01,
        growth_ensemble_weight_epochs=50,
        
        # Temperature neural network parameters - optimally tuned
        temp_nn_hidden_dims=[512, 512, 512, 384, 256, 128],
        temp_nn_dropout_rates=[0.2, 0.25, 0.3, 0.35, 0.4, 0.5],
        temp_nn_lr=0.0005,
        temp_nn_batch_size=32,
        temp_nn_epochs=args.temp_nn_epochs,
        temp_nn_patience=args.temp_nn_patience,
        
        # General parameters
        variance_percentile=25
    )
    
    # Fit model
    model.fit(
        X_train, 
        y_growth_train, 
        y_temp_train,
        X_val, 
        y_growth_val, 
        y_temp_val,
        args.output_dir
    )
    
    # Evaluate model
    metrics = evaluate_model(
        model, X_test, y_growth_test, y_temp_test,
        transform_info, args.output_dir
    )
    
    # Print final metrics
    logger.info("Hybrid model with optimally integrated components (v4) - training and evaluation completed")
    logger.info(f"Final metrics (scaled) - Overall R²: {metrics['scaled']['overall']['r2']:.4f}")
    logger.info(f"Final metrics (scaled) - Growth Rate R²: {metrics['scaled']['growth_rate']['r2']:.4f}")
    logger.info(f"Final metrics (scaled) - Temperature R²: {metrics['scaled']['temperature']['r2']:.4f}")
    
    logger.info(f"Final metrics (original) - Overall R²: {metrics['original']['overall']['r2']:.4f}")
    logger.info(f"Final metrics (original) - Growth Rate R²: {metrics['original']['growth_rate']['r2']:.4f}")
    logger.info(f"Final metrics (original) - Temperature R²: {metrics['original']['temperature']['r2']:.4f}")
    
    # Save command line arguments
    with open(os.path.join(args.output_dir, 'args.txt'), 'w') as f:
        for arg, value in vars(args).items():
            f.write(f"{arg}: {value}\n")
    
    logger.info(f"Model and results saved to {args.output_dir}")
    logger.info("Done!")

if __name__ == "__main__":
    main() 