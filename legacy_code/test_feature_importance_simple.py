import torch
import numpy as np
import matplotlib.pyplot as plt
from deepmu.models.tabnet_model import TabNetEncoder

def test_encoder_masks():
    """Test that TabNetEncoder correctly generates and stores masks."""
    # Parameters
    input_dim = 10
    n_d = 4
    n_a = 4
    n_steps = 3
    
    # Create a simple encoder
    encoder = TabNetEncoder(
        input_dim=input_dim,
        output_dim=1,
        n_d=n_d,
        n_a=n_a,
        n_steps=n_steps,
        gamma=1.3,
        n_independent=1,
        n_shared=1,
        virtual_batch_size=32,
        momentum=0.02,
        mask_type="sparsemax"
    )
    
    # Create a simple input
    x = torch.randn(10, input_dim)  # 10 samples, input_dim features
    
    # Forward pass
    encoder.eval()
    with torch.no_grad():
        output, sparse_loss = encoder(x)
    
    # Check if masks are stored
    if hasattr(encoder, 'masks') and len(encoder.masks) > 0:
        print(f"Number of masks: {len(encoder.masks)}")
        for i, mask in enumerate(encoder.masks):
            print(f"Mask {i+1} shape: {mask.shape}")
            print(f"Mask {i+1} sum: {mask.sum().item()}")
            print(f"Mask {i+1} min/max: {mask.min().item():.4f}/{mask.max().item():.4f}")
        return True
    else:
        print("No masks found!")
        return False

if __name__ == '__main__':
    success = test_encoder_masks()
    if success:
        print("\nSuccess! TabNetEncoder correctly stores masks.")
    else:
        print("\nTest failed! TabNetEncoder does not properly store masks.") 