#!/bin/bash
# Run script for training RL-based temperature prediction model

# Create output directories
mkdir -p models/temperature_rl_model
mkdir -p metrics/temperature_rl_model

# Make training script executable
chmod +x train_rl_temperature.py

# Run training script with optimized parameters
python train_rl_temperature.py \
  --feature-file training_data/combined_features.tsv \
  --metadata training_data/metadata.tsv \
  --output-dir models/temperature_rl_model \
  --metrics-dir metrics/temperature_rl_model \
  --hidden-dim 384 \
  --num-heads 6 \
  --num-layers 3 \
  --dropout 0.25 \
  --use-layer-norm \
  --use-residual \
  --use-value-head \
  --activation gelu \
  --alpha 0.7 \
  --beta 0.2 \
  --gamma 0.1 \
  --accuracy-scale 1.0 \
  --lr 5e-5 \
  --weight-decay 1e-5 \
  --batch-size 128 \
  --epochs 300 \
  --early-stopping-patience 25 \
  --entropy-coef 0.01 \
  --value-loss-coef 0.5 \
  --max-grad-norm 1.0 \
  --seed 42

echo "Training complete. Results saved to models/temperature_rl_model and metrics/temperature_rl_model" 