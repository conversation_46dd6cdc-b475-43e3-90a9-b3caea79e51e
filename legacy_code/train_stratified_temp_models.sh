#!/bin/bash

# DeepMu: Training Script with Temperature Stratification
# This script trains separate models for different temperature categories:
# - Psychrophiles: -20°C to 20°C (optimal ~15°C or lower)
# - Mesophiles: 20°C to 45°C (optimal ~37°C)
# - Thermophiles: 50°C to 80°C (optimal ~60-70°C)
# - Hyperthermophiles: 80°C and above (optimal ~80°C or higher)

# Set a clean output directory
OUTPUT_DIR="models/deepmu_stratified_temp"

# Create output directory
mkdir -p "$OUTPUT_DIR"

# Define color codes for output formatting
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Display initial message
echo -e "${BLUE}${BOLD}========================================================${NC}"
echo -e "${BLUE}${BOLD}   DeepMu - Temperature Stratified Models   ${NC}"
echo -e "${BLUE}${BOLD}========================================================${NC}"
echo -e "${GREEN}This script trains separate models for different temperature categories:${NC}"
echo -e "  1. ${YELLOW}Psychrophiles: -20°C to 20°C (optimal ~15°C or lower)${NC}"
echo -e "  2. ${YELLOW}Mesophiles: 20°C to 45°C (optimal ~37°C)${NC}"
echo -e "  3. ${YELLOW}Thermophiles: 45°C to 80°C (optimal ~60-70°C)${NC}"
echo -e "  4. ${YELLOW}Hyperthermophiles: 80°C and above (optimal ~80°C or higher)${NC}"
echo -e ""
echo -e "${YELLOW}Output will be saved to:${NC} ${OUTPUT_DIR}"
echo ""

# Check if prerequisites are available
echo -e "${YELLOW}Checking prerequisites...${NC}"
if [ ! -f "./training_data/combined_features.tsv" ]; then
    echo -e "${RED}Error: Feature file not found. Please ensure './training_data/combined_features.tsv' exists.${NC}"
    exit 1
fi

if [ ! -f "./training_data/metadata.tsv" ]; then
    echo -e "${RED}Error: Metadata file not found. Please ensure './training_data/metadata.tsv' exists.${NC}"
    exit 1
fi

echo -e "${GREEN}All prerequisites found.${NC}"
echo ""

# Step 1: Create stratified datasets
echo -e "${YELLOW}Step 1: Creating stratified datasets based on temperature categories...${NC}"
python temperature_stratification.py \
    --feature-file "./training_data/combined_features.tsv" \
    --metadata-file "./training_data/metadata.tsv" \
    --output-dir "$OUTPUT_DIR/stratified_data" \
    --reliability-threshold 0.6

# Check if stratification was successful
if [ $? -eq 0 ]; then
    echo -e "${GREEN}Dataset stratification completed. Using stratified datasets for training.${NC}"
else
    echo -e "${RED}Error: Dataset stratification failed. Exiting.${NC}"
    exit 1
fi

# Set common parameters for all models
HIDDEN_DIMS="768,768,512,512,384,256,128"  # Deeper architecture
DROPOUT_RATES="0.2,0.25,0.3,0.35,0.4,0.45,0.5"  # Progressive dropout
MAX_EPOCHS=800  # More epochs for complete training
PATIENCE=80  # Increased patience for more stable convergence
BATCH_SIZE=32  # Smaller batch size for better generalization
N_GROWTH_FEATURES=150  # Fewer features for growth rate
N_TEMP_FEATURES=1000  # More features for temperature
L1_LAMBDA="1e-6"  # Reduced L1 regularization
WEIGHT_DECAY="1e-6"  # Reduced weight decay
LEARNING_RATE="0.0005"  # Lower learning rate for better convergence

# Set environment variables for deterministic behavior
export PYTHONHASHSEED=42
export TORCH_CUDNN_DETERMINISTIC=1
export TORCH_DETERMINISTIC=1
export CUBLAS_WORKSPACE_CONFIG=:4096:8

# Step 2: Train models for each temperature category
echo -e "${YELLOW}Step 2: Training models for each temperature category...${NC}"

# Function to train a model for a specific category
train_category_model() {
    local category=$1
    local features_file="$OUTPUT_DIR/stratified_data/$category/features.tsv"
    local metadata_file="$OUTPUT_DIR/stratified_data/$category/metadata.tsv"
    local category_output_dir="$OUTPUT_DIR/$category"
    
    # Check if category has enough data
    local sample_count=$(wc -l < "$features_file")
    sample_count=$((sample_count - 1))  # Subtract header line
    
    if [ $sample_count -lt 100 ]; then
        echo -e "${YELLOW}Warning: Not enough data for $category (only $sample_count samples). Skipping.${NC}"
        return 0
    fi
    
    echo -e "\n${BLUE}${BOLD}========================================================${NC}"
    echo -e "${BLUE}${BOLD}   Training Model for ${category^} (${sample_count} samples)   ${NC}"
    echo -e "${BLUE}${BOLD}========================================================${NC}"
    
    # Create category output directory
    mkdir -p "$category_output_dir"
    
    # Adjust batch size for small datasets
    local adjusted_batch_size=$BATCH_SIZE
    if [ $sample_count -lt 500 ]; then
        adjusted_batch_size=16
    fi
    if [ $sample_count -lt 200 ]; then
        adjusted_batch_size=8
    fi
    
    # Run the Python script with optimized parameters
    python final_hybrid_model.py \
        --feature_file "$features_file" \
        --metadata_file "$metadata_file" \
        --output_dir "$category_output_dir" \
        --n_growth_features $N_GROWTH_FEATURES \
        --n_temp_features $N_TEMP_FEATURES \
        --seed 42 \
        --lr $LEARNING_RATE \
        --batch_size $adjusted_batch_size \
        --patience $PATIENCE \
        --epochs $MAX_EPOCHS \
        --l1_lambda $L1_LAMBDA \
        --weight_decay $WEIGHT_DECAY \
        --mse_l1_ratio 0.95 \
        --hidden_dims "$HIDDEN_DIMS" \
        --dropout_rates "$DROPOUT_RATES" \
        --activation "leaky_relu" \
        --use_batch_norm \
        --use_residual \
        --use_attention \
        --use_one_cycle_lr \
        --save_checkpoints \
        --missing_threshold 0.5 \
        --temp_transform "none" \
        --growth_transform "log2" \
        --verbose 2>&1 | tee "${category_output_dir}/training.log"
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}Training completed successfully for ${category^}.${NC}"
        return 0
    else
        echo -e "${RED}Error: Training failed for ${category^}.${NC}"
        return 1
    fi
}

# Train models for each category
categories=("psychrophile" "mesophile" "thermophile" "hyperthermophile")
success=true

for category in "${categories[@]}"; do
    train_category_model "$category"
    if [ $? -ne 0 ]; then
        success=false
    fi
done

# Step 3: Train a combined model with all reliable data
echo -e "\n${BLUE}${BOLD}========================================================${NC}"
echo -e "${BLUE}${BOLD}   Training Combined Model with All Reliable Data   ${NC}"
echo -e "${BLUE}${BOLD}========================================================${NC}"

# Create combined output directory
mkdir -p "$OUTPUT_DIR/combined"

# Run the Python script with optimized parameters
python final_hybrid_model.py \
    --feature_file "$OUTPUT_DIR/stratified_data/combined_features.tsv" \
    --metadata_file "$OUTPUT_DIR/stratified_data/combined_metadata.tsv" \
    --output_dir "$OUTPUT_DIR/combined" \
    --n_growth_features $N_GROWTH_FEATURES \
    --n_temp_features $N_TEMP_FEATURES \
    --seed 42 \
    --lr $LEARNING_RATE \
    --batch_size $BATCH_SIZE \
    --patience $PATIENCE \
    --epochs $MAX_EPOCHS \
    --l1_lambda $L1_LAMBDA \
    --weight_decay $WEIGHT_DECAY \
    --mse_l1_ratio 0.95 \
    --hidden_dims "$HIDDEN_DIMS" \
    --dropout_rates "$DROPOUT_RATES" \
    --activation "leaky_relu" \
    --use_batch_norm \
    --use_residual \
    --use_attention \
    --use_one_cycle_lr \
    --save_checkpoints \
    --missing_threshold 0.5 \
    --temp_transform "none" \
    --growth_transform "log2" \
    --verbose 2>&1 | tee "${OUTPUT_DIR}/combined/training.log"

if [ $? -eq 0 ]; then
    echo -e "${GREEN}Training completed successfully for combined model.${NC}"
else
    echo -e "${RED}Error: Training failed for combined model.${NC}"
    success=false
fi

# Step 4: Create a model selector script
echo -e "${YELLOW}Step 4: Creating model selector script...${NC}"

cat > "$OUTPUT_DIR/select_model.py" << 'EOF'
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Model Selector for Temperature-Stratified Models.

This script selects the appropriate model based on the input temperature range.
"""

import os
import sys
import argparse
import logging
import numpy as np
import pandas as pd
import torch
import pickle
from typing import Dict, Any, Optional, Tuple

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Define temperature categories
TEMP_CATEGORIES = {
    'psychrophile': (-20, 20),
    'mesophile': (20, 45),
    'thermophile': (45, 80),
    'hyperthermophile': (80, 120)
}

def select_model_for_temperature(temperature: float) -> str:
    """
    Select the appropriate model based on the input temperature.
    
    Args:
        temperature: Input temperature value
        
    Returns:
        Category name for the selected model
    """
    if temperature <= TEMP_CATEGORIES['psychrophile'][1]:
        return 'psychrophile'
    elif temperature <= TEMP_CATEGORIES['mesophile'][1]:
        return 'mesophile'
    elif temperature <= TEMP_CATEGORIES['thermophile'][1]:
        return 'thermophile'
    else:
        return 'hyperthermophile'

def load_model(model_dir: str, category: str) -> Dict[str, Any]:
    """
    Load the model for the specified category.
    
    Args:
        model_dir: Base directory containing all models
        category: Category name
        
    Returns:
        Dictionary containing the loaded model
    """
    category_dir = os.path.join(model_dir, category)
    model_path = os.path.join(category_dir, 'model.pkl')
    
    if not os.path.exists(model_path):
        logger.warning(f"Model for {category} not found at {model_path}")
        logger.warning(f"Falling back to combined model")
        category_dir = os.path.join(model_dir, 'combined')
        model_path = os.path.join(category_dir, 'model.pkl')
    
    logger.info(f"Loading model from {model_path}")
    with open(model_path, 'rb') as f:
        model = pickle.load(f)
    
    return model

def predict_with_stratified_models(
    model_dir: str,
    features: pd.DataFrame,
    temperatures: Optional[np.ndarray] = None
) -> Tuple[np.ndarray, np.ndarray]:
    """
    Make predictions using temperature-stratified models.
    
    Args:
        model_dir: Base directory containing all models
        features: DataFrame containing input features
        temperatures: Optional array of temperatures for model selection
        
    Returns:
        Tuple of (temperature_predictions, growth_rate_predictions)
    """
    # If temperatures are not provided, use the combined model
    if temperatures is None:
        logger.info("No temperatures provided, using combined model for all predictions")
        model = load_model(model_dir, 'combined')
        temp_preds, growth_preds = model.predict(features)
        return temp_preds, growth_preds
    
    # Select models based on input temperatures
    temp_preds = np.zeros(len(temperatures))
    growth_preds = np.zeros(len(temperatures))
    
    # Group samples by category
    category_indices = {}
    for category in TEMP_CATEGORIES.keys():
        category_indices[category] = []
    
    for i, temp in enumerate(temperatures):
        category = select_model_for_temperature(temp)
        category_indices[category].append(i)
    
    # Make predictions for each category
    for category, indices in category_indices.items():
        if not indices:
            continue
        
        logger.info(f"Making predictions for {len(indices)} {category} samples")
        model = load_model(model_dir, category)
        
        category_features = features.iloc[indices]
        category_temp_preds, category_growth_preds = model.predict(category_features)
        
        temp_preds[indices] = category_temp_preds
        growth_preds[indices] = category_growth_preds
    
    return temp_preds, growth_preds

def main():
    parser = argparse.ArgumentParser(description='Model Selector for Temperature-Stratified Models')
    parser.add_argument('--model-dir', type=str, required=True, help='Base directory containing all models')
    parser.add_argument('--feature-file', type=str, required=True, help='Path to feature file')
    parser.add_argument('--output-file', type=str, required=True, help='Path to output file')
    parser.add_argument('--temperature-file', type=str, help='Path to file with known temperatures (optional)')
    
    args = parser.parse_args()
    
    try:
        # Load features
        logger.info(f"Loading features from {args.feature_file}")
        features = pd.read_csv(args.feature_file, sep='\t', index_col='genome_id')
        
        # Load temperatures if provided
        temperatures = None
        if args.temperature_file:
            logger.info(f"Loading temperatures from {args.temperature_file}")
            temp_df = pd.read_csv(args.temperature_file, sep='\t', index_col='genome_id')
            if 'optimal_temperature' in temp_df.columns:
                temperatures = temp_df.loc[features.index, 'optimal_temperature'].values
        
        # Make predictions
        temp_preds, growth_preds = predict_with_stratified_models(
            args.model_dir,
            features,
            temperatures
        )
        
        # Save predictions
        results_df = pd.DataFrame(index=features.index)
        results_df['predicted_temperature'] = temp_preds
        results_df['predicted_growth_rate'] = growth_preds
        
        if temperatures is not None:
            results_df['true_temperature'] = temperatures
        
        logger.info(f"Saving predictions to {args.output_file}")
        results_df.to_csv(args.output_file, sep='\t')
        
        logger.info("Prediction completed successfully")
        return 0
    
    except Exception as e:
        logger.error(f"Error making predictions: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == '__main__':
    main()
EOF

chmod +x "$OUTPUT_DIR/select_model.py"
echo -e "${GREEN}Created model selector script at $OUTPUT_DIR/select_model.py${NC}"

# Final status
if $success; then
    echo ""
    echo -e "${GREEN}${BOLD}========================================================${NC}"
    echo -e "${GREEN}${BOLD}   All training completed successfully!   ${NC}"
    echo -e "${GREEN}${BOLD}========================================================${NC}"
    echo ""
    echo -e "${GREEN}Models saved to:${NC} ${OUTPUT_DIR}"
    echo -e "${YELLOW}To use the stratified models, run:${NC}"
    echo -e "  python $OUTPUT_DIR/select_model.py --model-dir $OUTPUT_DIR --feature-file <features.tsv> --output-file <predictions.tsv>"
else
    echo ""
    echo -e "${RED}${BOLD}========================================================${NC}"
    echo -e "${RED}${BOLD}   Some training tasks failed!   ${NC}"
    echo -e "${RED}${BOLD}========================================================${NC}"
    echo ""
    echo -e "${RED}Please check the error messages above for details.${NC}"
fi

echo ""
echo -e "${GREEN}Done.${NC}"
