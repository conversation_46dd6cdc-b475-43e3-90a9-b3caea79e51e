#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Improved Hybrid Multi-Task Model for Growth Rate and Temperature Prediction.
This module provides an enhanced hybrid model that combines ensemble methods with
an advanced neural network to predict both growth rate and optimal temperature.
"""

import os
import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Tuple, Optional
import joblib
import torch
import torch.nn as nn
import torch.optim as optim
from sklearn.preprocessing import RobustScaler, StandardScaler
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from sklearn.ensemble import RandomForestRegressor
import xgboost as xgb
import lightgbm as lgb

from advanced_multi_task_nn import AdvancedMultiTaskNeuralNetwork

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ImprovedHybridMultiTaskModel:
    """
    Improved Hybrid Multi-Task Model that combines ensemble methods with an advanced neural network
    to predict both growth rate and optimal temperature.
    """
    def __init__(
        self,
        nn_hidden_dim: int = 256,
        nn_num_blocks: int = 3,
        nn_dropout: float = 0.3,
        nn_l2_reg: float = 1e-5,
        nn_lr: float = 0.001,
        nn_batch_size: int = 64,
        nn_epochs: int = 100,
        nn_patience: int = 15,
        nn_growth_weight: float = 0.5,
        nn_use_swa: bool = True,
        ensemble_weight_lr: float = 0.01,
        ensemble_weight_epochs: int = 50,
        variance_percentile: int = 25
    ):
        """
        Initialize improved hybrid multi-task model.
        
        Args:
            nn_hidden_dim: Size of hidden layers in neural network
            nn_num_blocks: Number of residual blocks in neural network
            nn_dropout: Dropout rate for neural network
            nn_l2_reg: L2 regularization strength for neural network
            nn_lr: Learning rate for neural network
            nn_batch_size: Batch size for neural network training
            nn_epochs: Maximum number of epochs for neural network
            nn_patience: Patience for early stopping in neural network
            nn_growth_weight: Weight for growth rate loss in neural network
            nn_use_swa: Whether to use Stochastic Weight Averaging for neural network
            ensemble_weight_lr: Learning rate for ensemble weight optimization
            ensemble_weight_epochs: Number of epochs for ensemble weight optimization
            variance_percentile: Percentile threshold for variance filtering
        """
        self.nn_hidden_dim = nn_hidden_dim
        self.nn_num_blocks = nn_num_blocks
        self.nn_dropout = nn_dropout
        self.nn_l2_reg = nn_l2_reg
        self.nn_lr = nn_lr
        self.nn_batch_size = nn_batch_size
        self.nn_epochs = nn_epochs
        self.nn_patience = nn_patience
        self.nn_growth_weight = nn_growth_weight
        self.nn_use_swa = nn_use_swa
        self.ensemble_weight_lr = ensemble_weight_lr
        self.ensemble_weight_epochs = ensemble_weight_epochs
        self.variance_percentile = variance_percentile
        
        # Initialize neural network component
        self.nn_model = AdvancedMultiTaskNeuralNetwork(
            hidden_dim=nn_hidden_dim,
            num_blocks=nn_num_blocks,
            dropout=nn_dropout,
            l2_reg=nn_l2_reg,
            lr=nn_lr,
            batch_size=nn_batch_size,
            epochs=nn_epochs,
            patience=nn_patience,
            growth_weight=nn_growth_weight,
            use_swa=nn_use_swa
        )
        
        # Initialize ensemble models for growth rate
        self.growth_rf = RandomForestRegressor(
            n_estimators=200,
            max_depth=15,
            min_samples_split=5,
            min_samples_leaf=2,
            max_features='sqrt',
            bootstrap=True,
            n_jobs=-1,
            random_state=42
        )
        
        self.growth_xgb = xgb.XGBRegressor(
            n_estimators=200,
            max_depth=6,
            learning_rate=0.05,
            subsample=0.8,
            colsample_bytree=0.8,
            reg_alpha=0.01,
            reg_lambda=1.0,
            n_jobs=-1,
            random_state=42
        )
        
        self.growth_lgb = lgb.LGBMRegressor(
            n_estimators=200,
            max_depth=10,
            learning_rate=0.05,
            subsample=0.8,
            colsample_bytree=0.8,
            reg_alpha=0.01,
            reg_lambda=1.0,
            n_jobs=-1,
            random_state=42
        )
        
        # Initialize ensemble models for temperature
        self.temp_rf = RandomForestRegressor(
            n_estimators=200,
            max_depth=15,
            min_samples_split=5,
            min_samples_leaf=2,
            max_features='sqrt',
            bootstrap=True,
            n_jobs=-1,
            random_state=42
        )
        
        self.temp_xgb = xgb.XGBRegressor(
            n_estimators=200,
            max_depth=6,
            learning_rate=0.05,
            subsample=0.8,
            colsample_bytree=0.8,
            reg_alpha=0.01,
            reg_lambda=1.0,
            n_jobs=-1,
            random_state=42
        )
        
        self.temp_lgb = lgb.LGBMRegressor(
            n_estimators=200,
            max_depth=10,
            learning_rate=0.05,
            subsample=0.8,
            colsample_bytree=0.8,
            reg_alpha=0.01,
            reg_lambda=1.0,
            n_jobs=-1,
            random_state=42
        )
        
        # Initialize ensemble weights
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # Weights for growth rate (neural network, RF, XGB, LGB)
        self.growth_weights = nn.Parameter(torch.tensor([0.25, 0.25, 0.25, 0.25], dtype=torch.float32))
        
        # Weights for temperature (neural network, RF, XGB, LGB)
        self.temp_weights = nn.Parameter(torch.tensor([0.25, 0.25, 0.25, 0.25], dtype=torch.float32))
        
        # Initialize feature scalers
        self.feature_scaler = StandardScaler()
        
    def filter_low_variance_features(self, X: pd.DataFrame, percentile: float = 25) -> pd.DataFrame:
        """
        Filter out low-variance features.
        
        Args:
            X: Feature DataFrame
            percentile: Percentile threshold for variance filtering
            
        Returns:
            Filtered feature DataFrame
        """
        # Calculate variance for each feature
        variances = X.var()
        
        # Determine threshold
        threshold = np.percentile(variances, percentile)
        
        # Select high-variance features
        high_var_cols = variances[variances > threshold].index.tolist()
        
        logger.info(f"Removed {X.shape[1] - len(high_var_cols)} low-variance features (below {percentile}th percentile)")
        
        return X[high_var_cols]
    
    def fit(
        self, 
        X_train: pd.DataFrame, 
        y_growth_train: pd.Series, 
        y_temp_train: pd.Series,
        X_val: pd.DataFrame, 
        y_growth_val: pd.Series, 
        y_temp_val: pd.Series,
        output_dir: Optional[str] = None
    ) -> None:
        """
        Fit improved hybrid multi-task model to data.
        
        Args:
            X_train: Training features
            y_growth_train: Training growth rate targets
            y_temp_train: Training temperature targets
            X_val: Validation features
            y_growth_val: Validation growth rate targets
            y_temp_val: Validation temperature targets
            output_dir: Directory to save outputs
        """
        # Step 1: Apply variance filtering
        X_train_filtered = self.filter_low_variance_features(X_train, self.variance_percentile)
        X_val_filtered = X_val[X_train_filtered.columns]
        
        # Step 2: Scale features
        X_train_scaled = self.feature_scaler.fit_transform(X_train_filtered)
        X_val_scaled = self.feature_scaler.transform(X_val_filtered)
        
        # Convert back to DataFrame
        X_train_scaled_df = pd.DataFrame(
            X_train_scaled, 
            columns=X_train_filtered.columns,
            index=X_train_filtered.index
        )
        
        X_val_scaled_df = pd.DataFrame(
            X_val_scaled, 
            columns=X_val_filtered.columns,
            index=X_val_filtered.index
        )
        
        # Step 3: Train neural network model
        logger.info("Training advanced neural network component...")
        self.nn_model.fit(
            X_train_scaled_df, 
            y_growth_train, 
            y_temp_train,
            X_val_scaled_df, 
            y_growth_val, 
            y_temp_val
        )
        
        # Step 4: Train ensemble models for growth rate
        logger.info("Training ensemble models for growth rate...")
        self.growth_rf.fit(X_train_scaled, y_growth_train)
        self.growth_xgb.fit(X_train_scaled, y_growth_train)
        self.growth_lgb.fit(X_train_scaled, y_growth_train)
        
        # Step 5: Train ensemble models for temperature
        logger.info("Training ensemble models for temperature...")
        self.temp_rf.fit(X_train_scaled, y_temp_train)
        self.temp_xgb.fit(X_train_scaled, y_temp_train)
        self.temp_lgb.fit(X_train_scaled, y_temp_train)
        
        # Step 6: Optimize ensemble weights
        logger.info("Optimizing ensemble weights...")
        self._optimize_ensemble_weights(
            X_val_scaled_df, 
            y_growth_val, 
            y_temp_val
        )
        
        # Save model if output_dir is provided
        if output_dir:
            self.save(output_dir)
    
    def _optimize_ensemble_weights(
        self, 
        X_val: pd.DataFrame, 
        y_growth_val: pd.Series, 
        y_temp_val: pd.Series
    ) -> None:
        """
        Optimize ensemble weights using validation data.
        
        Args:
            X_val: Validation features
            y_growth_val: Validation growth rate targets
            y_temp_val: Validation temperature targets
        """
        # Get predictions from all models
        # Neural network predictions
        nn_growth_pred, nn_temp_pred = self.nn_model.predict(X_val)
        
        # Convert to numpy arrays
        X_val_np = X_val.values
        
        # Ensemble predictions for growth rate
        rf_growth_pred = self.growth_rf.predict(X_val_np)
        xgb_growth_pred = self.growth_xgb.predict(X_val_np)
        lgb_growth_pred = self.growth_lgb.predict(X_val_np)
        
        # Ensemble predictions for temperature
        rf_temp_pred = self.temp_rf.predict(X_val_np)
        xgb_temp_pred = self.temp_xgb.predict(X_val_np)
        lgb_temp_pred = self.temp_lgb.predict(X_val_np)
        
        # Convert to tensors
        nn_growth_tensor = torch.tensor(nn_growth_pred, dtype=torch.float32).to(self.device)
        rf_growth_tensor = torch.tensor(rf_growth_pred, dtype=torch.float32).to(self.device)
        xgb_growth_tensor = torch.tensor(xgb_growth_pred, dtype=torch.float32).to(self.device)
        lgb_growth_tensor = torch.tensor(lgb_growth_pred, dtype=torch.float32).to(self.device)
        
        nn_temp_tensor = torch.tensor(nn_temp_pred, dtype=torch.float32).to(self.device)
        rf_temp_tensor = torch.tensor(rf_temp_pred, dtype=torch.float32).to(self.device)
        xgb_temp_tensor = torch.tensor(xgb_temp_pred, dtype=torch.float32).to(self.device)
        lgb_temp_tensor = torch.tensor(lgb_temp_pred, dtype=torch.float32).to(self.device)
        
        y_growth_val_tensor = torch.tensor(y_growth_val.values, dtype=torch.float32).to(self.device)
        y_temp_val_tensor = torch.tensor(y_temp_val.values, dtype=torch.float32).to(self.device)
        
        # Initialize weights parameters
        growth_weights = nn.Parameter(torch.tensor([0.25, 0.25, 0.25, 0.25], dtype=torch.float32).to(self.device))
        temp_weights = nn.Parameter(torch.tensor([0.25, 0.25, 0.25, 0.25], dtype=torch.float32).to(self.device))
        
        # Initialize optimizer
        optimizer = optim.Adam([growth_weights, temp_weights], lr=self.ensemble_weight_lr)
        
        # Initialize loss function
        criterion = nn.MSELoss()
        
        # Training loop
        best_loss = float('inf')
        best_growth_weights = growth_weights.clone()
        best_temp_weights = temp_weights.clone()
        
        for epoch in range(self.ensemble_weight_epochs):
            # Zero gradients
            optimizer.zero_grad()
            
            # Apply softmax to ensure weights sum to 1
            normalized_growth_weights = torch.softmax(growth_weights, dim=0)
            normalized_temp_weights = torch.softmax(temp_weights, dim=0)
            
            # Combine predictions for growth rate
            combined_growth_pred = (
                normalized_growth_weights[0] * nn_growth_tensor + 
                normalized_growth_weights[1] * rf_growth_tensor +
                normalized_growth_weights[2] * xgb_growth_tensor +
                normalized_growth_weights[3] * lgb_growth_tensor
            )
            
            # Combine predictions for temperature
            combined_temp_pred = (
                normalized_temp_weights[0] * nn_temp_tensor + 
                normalized_temp_weights[1] * rf_temp_tensor +
                normalized_temp_weights[2] * xgb_temp_tensor +
                normalized_temp_weights[3] * lgb_temp_tensor
            )
            
            # Calculate losses
            growth_loss = criterion(combined_growth_pred, y_growth_val_tensor)
            temp_loss = criterion(combined_temp_pred, y_temp_val_tensor)
            
            # Combined loss with equal weighting
            loss = 0.5 * growth_loss + 0.5 * temp_loss
            
            # Backward pass and optimization
            loss.backward()
            optimizer.step()
            
            # Track best weights
            if loss.item() < best_loss:
                best_loss = loss.item()
                best_growth_weights = normalized_growth_weights.clone().detach()
                best_temp_weights = normalized_temp_weights.clone().detach()
            
            if (epoch + 1) % 10 == 0:
                logger.info(f"Epoch {epoch+1}/{self.ensemble_weight_epochs}: Loss: {loss.item():.6f}")
                logger.info(f"Growth weights: {normalized_growth_weights.cpu().detach().numpy()}")
                logger.info(f"Temperature weights: {normalized_temp_weights.cpu().detach().numpy()}")
        
        # Set final weights
        self.growth_weights = best_growth_weights
        self.temp_weights = best_temp_weights
        
        logger.info(f"Final growth weights: {self.growth_weights.cpu().numpy()}")
        logger.info(f"Final temperature weights: {self.temp_weights.cpu().numpy()}")
        
        # Calculate R² with optimized weights
        combined_growth_pred = (
            self.growth_weights[0].item() * nn_growth_pred + 
            self.growth_weights[1].item() * rf_growth_pred +
            self.growth_weights[2].item() * xgb_growth_pred +
            self.growth_weights[3].item() * lgb_growth_pred
        )
        
        combined_temp_pred = (
            self.temp_weights[0].item() * nn_temp_pred + 
            self.temp_weights[1].item() * rf_temp_pred +
            self.temp_weights[2].item() * xgb_temp_pred +
            self.temp_weights[3].item() * lgb_temp_pred
        )
        
        growth_r2 = r2_score(y_growth_val, combined_growth_pred)
        temp_r2 = r2_score(y_temp_val, combined_temp_pred)
        overall_r2 = (growth_r2 + temp_r2) / 2
        
        logger.info(f"Validation R² with optimized weights - Growth: {growth_r2:.4f}, Temperature: {temp_r2:.4f}, Overall: {overall_r2:.4f}")
    
    def predict(self, X: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """
        Generate predictions for both growth rate and temperature.
        
        Args:
            X: Features
            
        Returns:
            Tuple of (growth_rate_predictions, temperature_predictions)
        """
        # Filter features to match training data
        common_cols = [col for col in X.columns if col in self.feature_scaler.feature_names_in_]
        X_filtered = X[common_cols]
        
        # Scale features
        X_scaled = self.feature_scaler.transform(X_filtered)
        
        # Convert to DataFrame for neural network
        X_scaled_df = pd.DataFrame(
            X_scaled, 
            columns=X_filtered.columns,
            index=X_filtered.index
        )
        
        # Get predictions from neural network
        nn_growth_pred, nn_temp_pred = self.nn_model.predict(X_scaled_df)
        
        # Get predictions from ensemble models
        rf_growth_pred = self.growth_rf.predict(X_scaled)
        xgb_growth_pred = self.growth_xgb.predict(X_scaled)
        lgb_growth_pred = self.growth_lgb.predict(X_scaled)
        
        rf_temp_pred = self.temp_rf.predict(X_scaled)
        xgb_temp_pred = self.temp_xgb.predict(X_scaled)
        lgb_temp_pred = self.temp_lgb.predict(X_scaled)
        
        # Combine predictions using learned weights
        growth_weights = self.growth_weights.cpu().numpy()
        temp_weights = self.temp_weights.cpu().numpy()
        
        combined_growth_pred = (
            growth_weights[0] * nn_growth_pred + 
            growth_weights[1] * rf_growth_pred +
            growth_weights[2] * xgb_growth_pred +
            growth_weights[3] * lgb_growth_pred
        )
        
        combined_temp_pred = (
            temp_weights[0] * nn_temp_pred + 
            temp_weights[1] * rf_temp_pred +
            temp_weights[2] * xgb_temp_pred +
            temp_weights[3] * lgb_temp_pred
        )
        
        return combined_growth_pred, combined_temp_pred
    
    def predict_growth(self, X: pd.DataFrame) -> np.ndarray:
        """
        Generate growth rate predictions only.
        
        Args:
            X: Features
            
        Returns:
            Growth rate predictions
        """
        growth_pred, _ = self.predict(X)
        return growth_pred
    
    def predict_temperature(self, X: pd.DataFrame) -> np.ndarray:
        """
        Generate temperature predictions only.
        
        Args:
            X: Features
            
        Returns:
            Temperature predictions
        """
        _, temp_pred = self.predict(X)
        return temp_pred
    
    def evaluate(
        self, 
        X: pd.DataFrame, 
        y_growth: pd.Series, 
        y_temp: pd.Series
    ) -> Dict[str, Dict[str, float]]:
        """
        Evaluate model on data.
        
        Args:
            X: Features
            y_growth: Growth rate targets
            y_temp: Temperature targets
            
        Returns:
            Dictionary of evaluation metrics for each task
        """
        # Generate predictions
        y_growth_pred, y_temp_pred = self.predict(X)
        
        # Calculate metrics for growth rate
        growth_metrics = {
            'R2': r2_score(y_growth, y_growth_pred),
            'RMSE': np.sqrt(mean_squared_error(y_growth, y_growth_pred)),
            'MAE': mean_absolute_error(y_growth, y_growth_pred)
        }
        
        # Calculate metrics for temperature
        temp_metrics = {
            'R2': r2_score(y_temp, y_temp_pred),
            'RMSE': np.sqrt(mean_squared_error(y_temp, y_temp_pred)),
            'MAE': mean_absolute_error(y_temp, y_temp_pred)
        }
        
        # Calculate overall metrics (average R2)
        overall_metrics = {
            'R2': (growth_metrics['R2'] + temp_metrics['R2']) / 2,
            'RMSE': (growth_metrics['RMSE'] + temp_metrics['RMSE']) / 2,
            'MAE': (growth_metrics['MAE'] + temp_metrics['MAE']) / 2
        }
        
        # Log metrics
        logger.info(f"Evaluation metrics - Growth Rate: R²={growth_metrics['R2']:.4f}, RMSE={growth_metrics['RMSE']:.4f}, MAE={growth_metrics['MAE']:.4f}")
        logger.info(f"Evaluation metrics - Temperature: R²={temp_metrics['R2']:.4f}, RMSE={temp_metrics['RMSE']:.4f}, MAE={temp_metrics['MAE']:.4f}")
        logger.info(f"Evaluation metrics - Overall: R²={overall_metrics['R2']:.4f}, RMSE={overall_metrics['RMSE']:.4f}, MAE={overall_metrics['MAE']:.4f}")
        
        return {
            'growth_rate': growth_metrics,
            'temperature': temp_metrics,
            'overall': overall_metrics
        }
    
    def save(self, output_dir: str) -> None:
        """
        Save model to disk.
        
        Args:
            output_dir: Directory to save model
        """
        os.makedirs(output_dir, exist_ok=True)
        
        # Save neural network model
        nn_dir = os.path.join(output_dir, 'nn_model')
        os.makedirs(nn_dir, exist_ok=True)
        self.nn_model.save(nn_dir)
        
        # Save ensemble models for growth rate
        growth_dir = os.path.join(output_dir, 'growth_models')
        os.makedirs(growth_dir, exist_ok=True)
        joblib.dump(self.growth_rf, os.path.join(growth_dir, 'rf_model.joblib'))
        joblib.dump(self.growth_xgb, os.path.join(growth_dir, 'xgb_model.joblib'))
        joblib.dump(self.growth_lgb, os.path.join(growth_dir, 'lgb_model.joblib'))
        
        # Save ensemble models for temperature
        temp_dir = os.path.join(output_dir, 'temp_models')
        os.makedirs(temp_dir, exist_ok=True)
        joblib.dump(self.temp_rf, os.path.join(temp_dir, 'rf_model.joblib'))
        joblib.dump(self.temp_xgb, os.path.join(temp_dir, 'xgb_model.joblib'))
        joblib.dump(self.temp_lgb, os.path.join(temp_dir, 'lgb_model.joblib'))
        
        # Save feature scaler
        joblib.dump(self.feature_scaler, os.path.join(output_dir, 'feature_scaler.joblib'))
        
        # Save ensemble weights
        torch.save(self.growth_weights, os.path.join(output_dir, 'growth_weights.pth'))
        torch.save(self.temp_weights, os.path.join(output_dir, 'temp_weights.pth'))
        
        # Save model configuration
        config = {
            'nn_hidden_dim': self.nn_hidden_dim,
            'nn_num_blocks': self.nn_num_blocks,
            'nn_dropout': self.nn_dropout,
            'nn_l2_reg': self.nn_l2_reg,
            'nn_lr': self.nn_lr,
            'nn_batch_size': self.nn_batch_size,
            'nn_epochs': self.nn_epochs,
            'nn_patience': self.nn_patience,
            'nn_growth_weight': self.nn_growth_weight,
            'nn_use_swa': self.nn_use_swa,
            'ensemble_weight_lr': self.ensemble_weight_lr,
            'ensemble_weight_epochs': self.ensemble_weight_epochs,
            'variance_percentile': self.variance_percentile
        }
        joblib.dump(config, os.path.join(output_dir, 'config.joblib'))
        
        logger.info(f"Improved hybrid multi-task model saved to {output_dir}")
    
    def load(self, input_dir: str) -> None:
        """
        Load model from disk.
        
        Args:
            input_dir: Directory to load model from
        """
        # Load neural network model
        nn_dir = os.path.join(input_dir, 'nn_model')
        self.nn_model.load(nn_dir)
        
        # Load ensemble models for growth rate
        growth_dir = os.path.join(input_dir, 'growth_models')
        self.growth_rf = joblib.load(os.path.join(growth_dir, 'rf_model.joblib'))
        self.growth_xgb = joblib.load(os.path.join(growth_dir, 'xgb_model.joblib'))
        self.growth_lgb = joblib.load(os.path.join(growth_dir, 'lgb_model.joblib'))
        
        # Load ensemble models for temperature
        temp_dir = os.path.join(input_dir, 'temp_models')
        self.temp_rf = joblib.load(os.path.join(temp_dir, 'rf_model.joblib'))
        self.temp_xgb = joblib.load(os.path.join(temp_dir, 'xgb_model.joblib'))
        self.temp_lgb = joblib.load(os.path.join(temp_dir, 'lgb_model.joblib'))
        
        # Load feature scaler
        self.feature_scaler = joblib.load(os.path.join(input_dir, 'feature_scaler.joblib'))
        
        # Load ensemble weights
        self.growth_weights = torch.load(os.path.join(input_dir, 'growth_weights.pth'), map_location=self.device)
        self.temp_weights = torch.load(os.path.join(input_dir, 'temp_weights.pth'), map_location=self.device)
        
        # Load model configuration
        config = joblib.load(os.path.join(input_dir, 'config.joblib'))
        self.nn_hidden_dim = config['nn_hidden_dim']
        self.nn_num_blocks = config['nn_num_blocks']
        self.nn_dropout = config['nn_dropout']
        self.nn_l2_reg = config['nn_l2_reg']
        self.nn_lr = config['nn_lr']
        self.nn_batch_size = config['nn_batch_size']
        self.nn_epochs = config['nn_epochs']
        self.nn_patience = config['nn_patience']
        self.nn_growth_weight = config['nn_growth_weight']
        self.nn_use_swa = config['nn_use_swa']
        self.ensemble_weight_lr = config['ensemble_weight_lr']
        self.ensemble_weight_epochs = config['ensemble_weight_epochs']
        self.variance_percentile = config['variance_percentile']
        
        logger.info(f"Improved hybrid multi-task model loaded from {input_dir}")
