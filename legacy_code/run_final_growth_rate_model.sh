#!/bin/bash

# Run final growth rate model for microbial growth rate prediction

# Set paths
FEATURE_FILE="training_data/combined_features.tsv"
METADATA_FILE="training_data/metadata.tsv"
OUTPUT_DIR="models/final_growth_rate"

# Set parameters
N_FEATURES=150
OPTIMIZE_HYPERPARAMS=true
USE_INTERACTIONS=true

# Create output directory
mkdir -p $OUTPUT_DIR

# Run final growth rate model
python train_final_growth_rate_model.py \
  --feature_file $FEATURE_FILE \
  --metadata_file $METADATA_FILE \
  --output_dir $OUTPUT_DIR \
  --n_features $N_FEATURES \
  $([ "$OPTIMIZE_HYPERPARAMS" = true ] && echo "--optimize_hyperparams") \
  $([ "$USE_INTERACTIONS" = true ] && echo "--use_interactions")

echo "Final growth rate model training and evaluation completed"
