#!/bin/bash

# DeepMu Enhanced Training Script
# Uses the hybrid approach with:
# - DNN for growth rate prediction (with log2 transformation)
# - Random Forest for temperature prediction (with NO transformation)
# - Stratified train-validation-test splitting (7:2:1 ratio)

# Define color codes for output formatting
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Display initial message
echo -e "${BLUE}${BOLD}========================================================${NC}"
echo -e "${BLUE}${BOLD}   DeepMu Enhanced Model Training                       ${NC}"
echo -e "${BLUE}${BOLD}   Hybrid Model with Stratified Splitting:              ${NC}"
echo -e "${BLUE}${BOLD}   - DNN for growth rate (log2 transform)               ${NC}"
echo -e "${BLUE}${BOLD}   - Random Forest for temperature (no transform)       ${NC}"
echo -e "${BLUE}${BOLD}   - Stratified train-val-test split (7:2:1)            ${NC}"
echo -e "${BLUE}${BOLD}========================================================${NC}"

# Set output directory
OUTPUT_DIR="models/deepmu_ensemble_final"

# Create output directory
mkdir -p "$OUTPUT_DIR"

# Run the enhanced model training script
python rf_temp_hybrid_model.py \
    --feature_file="./training_data/combined_features.tsv" \
    --metadata_file="./training_data/metadata.tsv" \
    --output_dir=$OUTPUT_DIR \
    --n_growth_features=250 \
    --n_temp_features=800 \
    --use_rf_temp_model \
    --rf_n_estimators=300 \
    --rf_max_depth=20 \
    --rf_min_samples_leaf=2 \
    --temp_transform=none \
    --growth_transform=log2 \
    --batch_size=64 \
    --epochs=300 \
    --patience=40 \
    --hidden_dims="640,512,384,256,192" \
    --dropout_rates="0.2,0.25,0.3,0.35,0.4" \
    --lr=0.0008 \
    --weight_decay=2e-5 \
    --use_batch_norm \
    --use_residual \
    --verbose

if [ $? -eq 0 ]; then
    echo -e "\n${GREEN}${BOLD}========================================================${NC}"
    echo -e "${GREEN}${BOLD}   Training completed successfully!                       ${NC}"
    echo -e "${GREEN}${BOLD}========================================================${NC}"
    echo -e "\n${GREEN}Final DeepMu ensemble model saved to:${NC} $OUTPUT_DIR"
    echo -e "\n${YELLOW}This model uses:${NC}"
    echo -e "  - ${GREEN}DNN for growth rate prediction (with log2 transformation)${NC}"
    echo -e "  - ${GREEN}Random Forest for temperature prediction (with NO transformation)${NC}"
    echo -e "  - ${GREEN}Stratified train-validation-test splitting (7:2:1 ratio)${NC}"
else
    echo -e "\n${RED}${BOLD}========================================================${NC}"
    echo -e "${RED}${BOLD}   Training failed!                                     ${NC}"
    echo -e "${RED}${BOLD}========================================================${NC}"
    echo -e "\n${RED}Please check the error messages above for details.${NC}"
fi

echo -e "\n${GREEN}Done.${NC}"
