#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Stratified Data Splitting for DeepMu.

This module implements stratified train-validation-test splitting with a 7:2:1 ratio
for temperature and growth rate targets separately.
"""

import os
import numpy as np
import pandas as pd
import logging
from sklearn.model_selection import train_test_split
from typing import Dict, Tuple, List, Optional, Union

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_bins(values: np.ndarray, n_bins: int = 10) -> np.ndarray:
    """
    Create bins for stratification of continuous values.
    
    Args:
        values: Array of continuous values
        n_bins: Number of bins to create
        
    Returns:
        Array of bin indices for each value
    """
    # Create bins using quantiles to ensure equal number of samples in each bin
    bins = pd.qcut(values, q=n_bins, labels=False, duplicates='drop')
    
    # Handle NaN values if any
    if np.isnan(bins).any():
        bins = np.nan_to_num(bins, nan=0).astype(int)
    
    return bins

def stratified_train_val_test_split(
    features: pd.DataFrame,
    growth_rate: pd.Series,
    temperature: pd.Series,
    train_ratio: float = 0.7,
    val_ratio: float = 0.2,
    test_ratio: float = 0.1,
    n_bins: int = 10,
    random_state: int = 42
) -> Dict[str, Dict[str, Union[pd.DataFrame, pd.Series]]]:
    """
    Perform stratified train-validation-test splitting for both targets.
    
    Args:
        features: Feature DataFrame
        growth_rate: Growth rate Series
        temperature: Temperature Series
        train_ratio: Ratio of training data
        val_ratio: Ratio of validation data
        test_ratio: Ratio of test data
        n_bins: Number of bins for stratification
        random_state: Random state for reproducibility
        
    Returns:
        Dictionary containing train, validation, and test data for both targets
    """
    # Validate ratios
    if abs(train_ratio + val_ratio + test_ratio - 1.0) > 1e-6:
        raise ValueError(f"Ratios must sum to 1.0: {train_ratio} + {val_ratio} + {test_ratio} = {train_ratio + val_ratio + test_ratio}")
    
    # Create bins for stratification
    logger.info("Creating bins for stratified splitting")
    growth_bins = create_bins(growth_rate.values, n_bins=n_bins)
    temp_bins = create_bins(temperature.values, n_bins=n_bins)
    
    # Log bin distribution
    logger.info(f"Growth rate bin distribution: {np.bincount(growth_bins)}")
    logger.info(f"Temperature bin distribution: {np.bincount(temp_bins)}")
    
    # Create separate splits for growth rate and temperature
    logger.info("Performing stratified splitting for growth rate")
    
    # First split for growth rate: train+val vs test
    growth_test_ratio = test_ratio / (train_ratio + val_ratio + test_ratio)
    X_trainval_growth, X_test_growth, y_trainval_growth, y_test_growth, growth_bins_trainval, _ = train_test_split(
        features, growth_rate, growth_bins, 
        test_size=growth_test_ratio, 
        random_state=random_state, 
        stratify=growth_bins
    )
    
    # Second split for growth rate: train vs val
    growth_val_ratio = val_ratio / (train_ratio + val_ratio)
    X_train_growth, X_val_growth, y_train_growth, y_val_growth = train_test_split(
        X_trainval_growth, y_trainval_growth, 
        test_size=growth_val_ratio, 
        random_state=random_state, 
        stratify=growth_bins_trainval
    )
    
    logger.info("Performing stratified splitting for temperature")
    
    # First split for temperature: train+val vs test
    temp_test_ratio = test_ratio / (train_ratio + val_ratio + test_ratio)
    X_trainval_temp, X_test_temp, y_trainval_temp, y_test_temp, temp_bins_trainval, _ = train_test_split(
        features, temperature, temp_bins, 
        test_size=temp_test_ratio, 
        random_state=random_state, 
        stratify=temp_bins
    )
    
    # Second split for temperature: train vs val
    temp_val_ratio = val_ratio / (train_ratio + val_ratio)
    X_train_temp, X_val_temp, y_train_temp, y_val_temp = train_test_split(
        X_trainval_temp, y_trainval_temp, 
        test_size=temp_val_ratio, 
        random_state=random_state, 
        stratify=temp_bins_trainval
    )
    
    # Log split sizes
    logger.info(f"Growth rate split - Train: {len(X_train_growth)}, Val: {len(X_val_growth)}, Test: {len(X_test_growth)}")
    logger.info(f"Temperature split - Train: {len(X_train_temp)}, Val: {len(X_val_temp)}, Test: {len(X_test_temp)}")
    
    # Log target distributions
    logger.info(f"Growth rate range - Train: [{y_train_growth.min():.4f}, {y_train_growth.max():.4f}], Val: [{y_val_growth.min():.4f}, {y_val_growth.max():.4f}], Test: [{y_test_growth.min():.4f}, {y_test_growth.max():.4f}]")
    logger.info(f"Temperature range - Train: [{y_train_temp.min():.4f}, {y_train_temp.max():.4f}], Val: [{y_val_temp.min():.4f}, {y_val_temp.max():.4f}], Test: [{y_test_temp.min():.4f}, {y_test_temp.max():.4f}]")
    
    # Create result dictionary
    result = {
        'growth': {
            'train': {
                'X': X_train_growth,
                'y': y_train_growth
            },
            'val': {
                'X': X_val_growth,
                'y': y_val_growth
            },
            'test': {
                'X': X_test_growth,
                'y': y_test_growth
            }
        },
        'temp': {
            'train': {
                'X': X_train_temp,
                'y': y_train_temp
            },
            'val': {
                'X': X_val_temp,
                'y': y_val_temp
            },
            'test': {
                'X': X_test_temp,
                'y': y_test_temp
            }
        }
    }
    
    return result

def load_and_split_data(
    feature_file: str,
    metadata_file: str,
    train_ratio: float = 0.7,
    val_ratio: float = 0.2,
    test_ratio: float = 0.1,
    n_bins: int = 10,
    random_state: int = 42,
    missing_threshold: float = 0.5
) -> Dict[str, Dict[str, Dict[str, Union[pd.DataFrame, pd.Series]]]]:
    """
    Load data and perform stratified train-validation-test splitting.
    
    Args:
        feature_file: Path to feature file
        metadata_file: Path to metadata file
        train_ratio: Ratio of training data
        val_ratio: Ratio of validation data
        test_ratio: Ratio of test data
        n_bins: Number of bins for stratification
        random_state: Random state for reproducibility
        missing_threshold: Maximum allowable percentage of missing values in features
        
    Returns:
        Dictionary containing train, validation, and test data for both targets
    """
    # Load features
    logger.info(f"Loading features from {feature_file}")
    features = pd.read_csv(feature_file, sep='\t', index_col='genome_id')
    
    # Load metadata
    logger.info(f"Loading metadata from {metadata_file}")
    metadata = pd.read_csv(metadata_file, sep='\t', index_col='genome_id')
    
    # Get common samples
    common_indices = features.index.intersection(metadata.index)
    logger.info(f"Found {len(common_indices)} common samples")
    
    # Filter to common samples
    features = features.loc[common_indices]
    metadata = metadata.loc[common_indices]
    
    # Filter out features with high missing values
    initial_feature_count = features.shape[1]
    missing_ratio = features.isna().mean()
    features_to_keep = missing_ratio[missing_ratio <= missing_threshold].index.tolist()
    filtered_feature_count = len(features_to_keep)
    removed_count = initial_feature_count - filtered_feature_count
    
    if removed_count > 0:
        logger.info(f"Removed {removed_count} features with >{missing_threshold*100:.1f}% missing values")
        logger.info(f"Retained {filtered_feature_count} features out of {initial_feature_count}")
        features = features[features_to_keep]
    else:
        logger.info(f"No features exceeded the missing value threshold of {missing_threshold*100:.1f}%")
    
    # Fill remaining missing values with 0
    features = features.fillna(0)
    
    # Extract targets
    growth_rate = metadata['growth_rate']
    temperature = metadata['optimal_temperature']
    
    # Perform stratified splitting
    split_data = stratified_train_val_test_split(
        features=features,
        growth_rate=growth_rate,
        temperature=temperature,
        train_ratio=train_ratio,
        val_ratio=val_ratio,
        test_ratio=test_ratio,
        n_bins=n_bins,
        random_state=random_state
    )
    
    return split_data

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Stratified Data Splitting for DeepMu")
    parser.add_argument("--feature_file", type=str, required=True, help="Path to feature file")
    parser.add_argument("--metadata_file", type=str, required=True, help="Path to metadata file")
    parser.add_argument("--output_dir", type=str, required=True, help="Directory to save split data")
    parser.add_argument("--train_ratio", type=float, default=0.7, help="Ratio of training data")
    parser.add_argument("--val_ratio", type=float, default=0.2, help="Ratio of validation data")
    parser.add_argument("--test_ratio", type=float, default=0.1, help="Ratio of test data")
    parser.add_argument("--n_bins", type=int, default=10, help="Number of bins for stratification")
    parser.add_argument("--random_state", type=int, default=42, help="Random state for reproducibility")
    parser.add_argument("--missing_threshold", type=float, default=0.5, help="Maximum allowable percentage of missing values in features")
    
    args = parser.parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Load and split data
    split_data = load_and_split_data(
        feature_file=args.feature_file,
        metadata_file=args.metadata_file,
        train_ratio=args.train_ratio,
        val_ratio=args.val_ratio,
        test_ratio=args.test_ratio,
        n_bins=args.n_bins,
        random_state=args.random_state,
        missing_threshold=args.missing_threshold
    )
    
    # Save split data
    for target in ['growth', 'temp']:
        for split in ['train', 'val', 'test']:
            # Create target directory
            target_dir = os.path.join(args.output_dir, target, split)
            os.makedirs(target_dir, exist_ok=True)
            
            # Save features
            split_data[target][split]['X'].to_csv(os.path.join(target_dir, 'features.tsv'), sep='\t')
            
            # Save targets
            split_data[target][split]['y'].to_csv(os.path.join(target_dir, 'targets.tsv'), sep='\t')
    
    logger.info(f"Split data saved to {args.output_dir}")
