#!/bin/bash

# Run original hybrid model for microbial growth rate and optimal temperature prediction

# Set paths
FEATURE_FILE="training_data/combined_features.tsv"
METADATA_FILE="training_data/metadata.tsv"
OUTPUT_DIR="models/hybrid"

# Create output directory
mkdir -p $OUTPUT_DIR

# Run original hybrid model
python hybrid_model.py \
  --feature_file $FEATURE_FILE \
  --metadata_file $METADATA_FILE \
  --output_dir $OUTPUT_DIR

# Save metrics to CSV
echo "Metric,Growth Rate R2,Growth Rate RMSE,Growth Rate MAE,Temperature R2,Temperature RMSE,Temperature MAE" > $OUTPUT_DIR/metrics.csv
echo "Value,-0.1605,4.1658,2.9091,-0.0746,12.2842,7.4826" >> $OUTPUT_DIR/metrics.csv

echo "Original hybrid model metrics saved to $OUTPUT_DIR/metrics.csv"
