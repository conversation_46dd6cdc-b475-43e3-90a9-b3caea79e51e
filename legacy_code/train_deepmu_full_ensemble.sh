#!/bin/bash

# DeepMu Full Ensemble Training Script
# Implements comprehensive ensemble approach with Optuna optimization:
# - Random Forest models for both temperature and growth rate
# - DNN models for both temperature and growth rate
# - Ensemble models combining both approaches
# - Stratified train-validation-test splitting (7:2:1 ratio)

# Define color codes for output formatting
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "\n${BLUE}${BOLD}========================================================${NC}"
    echo -e "${BLUE}${BOLD}   $1${NC}"
    echo -e "${BLUE}${BOLD}========================================================${NC}"
}

# Display initial message
echo -e "${BLUE}${BOLD}========================================================${NC}"
echo -e "${BLUE}${BOLD}   DeepMu Full Ensemble Training with Optuna             ${NC}"
echo -e "${BLUE}${BOLD}   Comprehensive Ensemble Approach:                     ${NC}"
echo -e "${BLUE}${BOLD}   - Random Forest models with Optuna optimization      ${NC}"
echo -e "${BLUE}${BOLD}   - DNN models with Optuna optimization                ${NC}"
echo -e "${BLUE}${BOLD}   - Ensemble models combining both approaches          ${NC}"
echo -e "${BLUE}${BOLD}   - Stratified train-val-test split (7:2:1)            ${NC}"
echo -e "${BLUE}${BOLD}========================================================${NC}"

# Set paths
FEATURE_FILE="./training_data/combined_features.tsv"
METADATA_FILE="./training_data/metadata.tsv"
PREPROCESSED_DIR="./preprocessed_data"
MODEL_DIR="./models"

# Create directories
mkdir -p $PREPROCESSED_DIR
mkdir -p $MODEL_DIR

# Step 1: Preprocess features
echo "Preprocessing features..."
python preprocess_features.py \
    --feature_file $FEATURE_FILE \
    --metadata_file $METADATA_FILE \
    --output_dir $PREPROCESSED_DIR \
    --n_temp_features 250 \
    --n_growth_features 250

# Step 2: Train temperature models
echo "Training temperature models..."

# Random Forest
python optimize_rf_temp.py \
    --feature_file $PREPROCESSED_DIR/X_temp.npy \
    --target_file $PREPROCESSED_DIR/y_temp.npy \
    --output_dir $MODEL_DIR

# DNN
python optimize_dnn_temp.py \
    --feature_file $PREPROCESSED_DIR/X_temp.npy \
    --target_file $PREPROCESSED_DIR/y_temp.npy \
    --output_dir $MODEL_DIR

# Step 3: Train growth rate models
echo "Training growth rate models..."

# Random Forest
python optimize_rf_growth.py \
    --feature_file $PREPROCESSED_DIR/X_growth.npy \
    --target_file $PREPROCESSED_DIR/y_growth.npy \
    --output_dir $MODEL_DIR

# DNN
python optimize_dnn_growth.py \
    --feature_file $PREPROCESSED_DIR/X_growth.npy \
    --target_file $PREPROCESSED_DIR/y_growth.npy \
    --output_dir $MODEL_DIR

echo "Training complete!"

# Step 4: Create ensemble models
print_step "Step 4: Creating Ensemble Models"

# Create ensemble for temperature prediction
print_status "Creating ensemble model for temperature prediction..."
python create_ensemble_models.py \
    --feature_file $FEATURE_FILE \
    --metadata_file $METADATA_FILE \
    --output_dir "$MODEL_DIR/ensemble_models" \
    --n_temp_features 250 \
    --n_growth_features 250

if [ $? -ne 0 ]; then
    print_error "Failed to create ensemble models"
    exit 1
fi

print_status "Ensemble models created successfully!"

# Step 5: Evaluate all models
print_step "Step 5: Evaluating All Models"

print_status "Evaluating all models..."
python evaluate_models.py \
    --feature_file="./training_data/combined_features.tsv" \
    --metadata_file="./training_data/metadata.tsv" \
    --rf_temp_model="$MODEL_DIR/temperature_rf_model.pkl" \
    --rf_growth_model="$MODEL_DIR/growth_rate_rf_model.pkl" \
    --dnn_temp_model="$MODEL_DIR/temperature_model.pth" \
    --dnn_growth_model="$MODEL_DIR/growth_rate_model.pth" \
    --ensemble_temp_model="$MODEL_DIR/ensemble_models/temperature_ensemble.pkl" \
    --ensemble_growth_model="$MODEL_DIR/ensemble_models/growth_ensemble.pkl" \
    --output_dir="$MODEL_DIR/evaluation" \
    --verbose

if [ $? -ne 0 ]; then
    print_error "Failed to evaluate models"
    exit 1
fi

print_status "Model evaluation completed successfully!"

# Final summary
echo -e "\n${GREEN}${BOLD}========================================================${NC}"
echo -e "${GREEN}${BOLD}   Training and evaluation completed successfully!        ${NC}"
echo -e "${GREEN}${BOLD}========================================================${NC}"
echo -e "\n${GREEN}Final DeepMu ensemble models saved to:${NC} $MODEL_DIR"
echo -e "\n${YELLOW}Model Training Summary:${NC}"
echo -e "  - ${GREEN}Random Forest models (with Optuna optimization):${NC}"
echo -e "    * Temperature prediction model"
echo -e "    * Growth rate prediction model"
echo -e "  - ${GREEN}DNN models (with Optuna optimization):${NC}"
echo -e "    * Temperature prediction model"
echo -e "    * Growth rate prediction model"
echo -e "  - ${GREEN}Ensemble models:${NC}"
echo -e "    * Temperature prediction ensemble"
echo -e "    * Growth rate prediction ensemble"
echo -e "\n${YELLOW}Results and Evaluation:${NC}"
echo -e "  - Comprehensive evaluation results available in: $MODEL_DIR/evaluation"
echo -e "  - Model comparison plots and metrics saved"
echo -e "  - Detailed performance reports generated"
echo -e "  - Optuna optimization studies saved in: $MODEL_DIR/optuna_studies"

echo -e "\n${GREEN}Done.${NC}"
