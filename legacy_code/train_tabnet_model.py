#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Training script for TabNet model for growth rate prediction.

This script trains a TabNet model which combines the high performance of neural networks
with the interpretability of decision trees for tabular data.
"""

import os
import sys
import argparse
import json
import numpy as np
import pandas as pd
import torch
import matplotlib.pyplot as plt
import seaborn as sns
import logging
from pathlib import Path
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from scipy.stats import spearmanr, pearsonr
import sklearn
import scipy.stats
from sklearn.feature_selection import mutual_info_regression
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import Lasso
from sklearn.ensemble import GradientBoostingRegressor
from torch.utils.data import TensorDataset, DataLoader
import torch.nn.functional as F
import torch.optim as optim

# Add parent directory to path to import deepmu modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '.')))

from deepmu.models.tabnet_model import TabNetModel, TabNetTrainer
from deepmu.utils.logging import get_logger

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('tabnet_training.log')
    ]
)
logger = get_logger()

def load_data(feature_file, metadata_file):
    """
    Load feature and metadata files.

    Args:
        feature_file: Path to feature file (TSV format)
        metadata_file: Path to metadata file (TSV format)

    Returns:
        Tuple of (features_df, metadata_df)
    """
    logger.info(f"Loading features from {feature_file}")
    features_df = pd.read_csv(feature_file, sep='\t', index_col=0)

    logger.info(f"Loading metadata from {metadata_file}")
    metadata_df = pd.read_csv(metadata_file, sep='\t', index_col=0)

    # Align features and metadata
    common_samples = features_df.index.intersection(metadata_df.index)
    features_df = features_df.loc[common_samples]
    metadata_df = metadata_df.loc[common_samples]

    logger.info(f"Loaded {len(features_df)} samples with {len(features_df.columns)} features")

    return features_df, metadata_df

def prepare_data(feature_file, metadata_file):
    """
    Prepare data for training.
    
    Args:
        feature_file: Path to the feature file
        metadata_file: Path to the metadata file
        
    Returns:
        X: Feature matrix
        y: Target labels
        feature_names: List of feature names
    """
    # Load features
    logger.info(f"Loading features from {feature_file}")
    features = pd.read_csv(feature_file, sep='\t', index_col=0)
    
    # Load metadata
    logger.info(f"Loading metadata from {metadata_file}")
    metadata = pd.read_csv(metadata_file, sep='\t', index_col=0)
    
    # Align features and metadata
    common_samples = features.index.intersection(metadata.index)
    features = features.loc[common_samples]
    metadata = metadata.loc[common_samples]
    
    logger.info(f"Loaded {len(features)} samples with {len(features.columns)} features")
    
    # Extract features and labels
    X = features.values
    y = metadata['label'].values
    
    # Get feature names
    feature_names = features.columns.tolist()
    
    return X, y, feature_names

def select_features(data, n_features=50):
    """
    Select top features using an ensemble of feature importance methods.
    
    Args:
        data: Dictionary with train, val, and test data
        n_features: Number of features to select
        
    Returns:
        Updated data dictionary with selected features
    """
    logger.info(f"Selecting top {n_features} features using ensemble method")
    
    X_train = data['train']['features']['all_features']
    y_train = data['train']['targets']
    feature_names = data['feature_names']
    
    # 1. Random Forest feature importance
    rf = RandomForestRegressor(n_estimators=100, random_state=42, n_jobs=-1)
    rf.fit(X_train, y_train)
    rf_importance = rf.feature_importances_
    
    # 2. Mutual Information
    mi_scores = mutual_info_regression(X_train, y_train, random_state=42)
    
    # 3. Lasso regression coefficients
    lasso = Lasso(alpha=0.1, random_state=42)
    lasso.fit(X_train, y_train)
    lasso_importance = np.abs(lasso.coef_)
    
    # 4. Gradient Boosting feature importance
    gb = GradientBoostingRegressor(n_estimators=100, random_state=42)
    gb.fit(X_train, y_train)
    gb_importance = gb.feature_importances_
    
    # Combine importance scores
    # Normalize each importance score
    rf_importance = rf_importance / rf_importance.sum()
    mi_scores = mi_scores / mi_scores.sum()
    lasso_importance = lasso_importance / lasso_importance.sum()
    gb_importance = gb_importance / gb_importance.sum()
    
    # Weighted average of importance scores
    combined_importance = (
        0.3 * rf_importance +
        0.3 * mi_scores +
        0.2 * lasso_importance +
        0.2 * gb_importance
    )
    
    # Get top features
    top_indices = np.argsort(combined_importance)[-n_features:]
    top_features = [feature_names[i] for i in top_indices]
    
    # Create new feature arrays
    X_train_selected = X_train[:, top_indices]
    X_val_selected = data['val']['features']['all_features'][:, top_indices]
    X_test_selected = data['test']['features']['all_features'][:, top_indices]
    
    # Add selected features to data dictionary
    data['train']['features']['selected'] = X_train_selected
    data['val']['features']['selected'] = X_val_selected
    data['test']['features']['selected'] = X_test_selected
    data['selected_features'] = top_features
    data['selected_indices'] = top_indices.tolist()
    
    # Log feature importance
    importance_df = pd.DataFrame({
        'feature': top_features,
        'importance': combined_importance[top_indices]
    }).sort_values('importance', ascending=False)
    
    logger.info("Top 10 most important features:")
    for _, row in importance_df.head(10).iterrows():
        logger.info(f"  {row['feature']}: {row['importance']:.4f}")
    
    return data

def train_tabnet_model(data, output_dir, metrics_dir, feature_set='all_features', 
                      n_d=16, n_a=16, n_steps=5, gamma=1.5, n_independent=3, 
                      n_shared=3, batch_size=256, max_epochs=200, patience=20,
                      learning_rate=0.02, weight_decay=1e-5, scheduler_gamma=0.95):
    """
    Train a TabNet model with improved training loop and evaluation.
    
    Args:
        data: Dictionary with train, val, and test data
        output_dir: Directory to save the model
        metrics_dir: Directory to save metrics and plots
        feature_set: Which feature set to use ('all_features' or 'selected')
        n_d: Width of the decision prediction layer
        n_a: Width of the attention embedding for each mask
        n_steps: Number of steps in the architecture
        gamma: Scaling factor for attention updates
        n_independent: Number of independent GLU layers
        n_shared: Number of shared GLU layers
        batch_size: Batch size for training
        max_epochs: Maximum number of epochs
        patience: Number of epochs to wait before early stopping
        learning_rate: Initial learning rate
        weight_decay: Weight decay for optimizer
        scheduler_gamma: Gamma for learning rate scheduler
    """
    logger.info("Training TabNet model")
    logger.info(f"Using feature set: {feature_set}")
    
    # Get data
    X_train = data['train']['features'][feature_set]
    y_train = data['train']['targets']
    X_val = data['val']['features'][feature_set]
    y_val = data['val']['targets']
    X_test = data['test']['features'][feature_set]
    y_test = data['test']['targets']
    
    # Log target distribution
    logger.info(f"Target distribution: min={y_train.min():.4f}, max={y_train.max():.4f}, "
               f"mean={y_train.mean():.4f}, median={np.median(y_train):.4f}")
    logger.info(f"  10th percentile: {np.percentile(y_train, 10):.4f}")
    logger.info(f"  25th percentile: {np.percentile(y_train, 25):.4f}")
    logger.info(f"  50th percentile: {np.percentile(y_train, 50):.4f}")
    logger.info(f"  75th percentile: {np.percentile(y_train, 75):.4f}")
    logger.info(f"  90th percentile: {np.percentile(y_train, 90):.4f}")
    logger.info(f"  95th percentile: {np.percentile(y_train, 95):.4f}")
    logger.info(f"  99th percentile: {np.percentile(y_train, 99):.4f}")
    
    # Set Huber loss delta to IQR/2 for robust regression
    iqr = np.percentile(y_train, 75) - np.percentile(y_train, 25)
    huber_delta = iqr / 2
    logger.info(f"Setting Huber delta to {huber_delta:.4f} (IQR/2)")
    
    # Convert to PyTorch tensors
    X_train = torch.FloatTensor(X_train)
    y_train = torch.FloatTensor(y_train).view(-1)  # Ensure target is 1D
    X_val = torch.FloatTensor(X_val)
    y_val = torch.FloatTensor(y_val).view(-1)  # Ensure target is 1D
    X_test = torch.FloatTensor(X_test)
    y_test = torch.FloatTensor(y_test).view(-1)  # Ensure target is 1D
    
    # Create datasets
    train_dataset = TensorDataset(X_train, y_train)
    val_dataset = TensorDataset(X_val, y_val)
    test_dataset = TensorDataset(X_test, y_test)
    
    # Create data loaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=4
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=4
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=4
    )
    
    # Initialize model
    input_dim = X_train.shape[1]
    model = TabNetModel(
        input_dim=input_dim,
        output_dim=1,
        n_d=n_d,
        n_a=n_a,
        n_steps=n_steps,
        gamma=gamma,
        n_independent=n_independent,
        n_shared=n_shared,
        virtual_batch_size=batch_size,
        momentum=0.02,
        mask_type="sparsemax"
    )
    
    # Move model to GPU if available
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)
    
    # Initialize optimizer and scheduler
    optimizer = optim.Adam(model.parameters(), lr=learning_rate, weight_decay=weight_decay)
    scheduler = optim.lr_scheduler.ExponentialLR(optimizer, gamma=scheduler_gamma)
    
    # Training loop
    best_val_loss = float('inf')
    patience_counter = 0
    train_losses = []
    val_losses = []
    
    for epoch in range(max_epochs):
        # Training
        model.train()
        train_loss = 0
        for batch_X, batch_y in train_loader:
            batch_X = batch_X.to(device)
            batch_y = batch_y.to(device)
            
            optimizer.zero_grad()
            output = model(batch_X)
            loss = F.huber_loss(output, batch_y, delta=huber_delta)
            loss.backward()
            
            # Gradient clipping
            torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
            
            optimizer.step()
            train_loss += loss.item()
        
        train_loss /= len(train_loader)
        train_losses.append(train_loss)
        
        # Validation
        model.eval()
        val_loss = 0
        with torch.no_grad():
            for batch_X, batch_y in val_loader:
                batch_X = batch_X.to(device)
                batch_y = batch_y.to(device)
                output = model(batch_X)
                loss = F.huber_loss(output, batch_y, delta=huber_delta)
                val_loss += loss.item()
        
        val_loss /= len(val_loader)
        val_losses.append(val_loss)
        
        # Update learning rate
        scheduler.step()
        
        # Early stopping
        if val_loss < best_val_loss - min_delta:
            best_val_loss = val_loss
            patience_counter = 0
            torch.save({
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'epoch': epoch,
                'best_val_loss': best_val_loss
            }, os.path.join(output_dir, 'tabnet_model.pt'))
        else:
            patience_counter += 1
            if patience_counter >= patience:
                logger.info("Early stopping triggered")
                break
        
        # Log progress
        if (epoch + 1) % 10 == 0:
            logger.info(f"Epoch {epoch+1}/{max_epochs}, Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}")
    
    # Load best model
    checkpoint = torch.load(os.path.join(output_dir, 'tabnet_model.pt'))
    model.load_state_dict(checkpoint['model_state_dict'])
    optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
    
    # Plot training metrics
    plt.figure(figsize=(10, 6))
    plt.plot(train_losses, label='Training Loss')
    plt.plot(val_losses, label='Validation Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('Training and Validation Loss')
    plt.legend()
    plt.savefig(os.path.join(metrics_dir, 'training_metrics.png'))
    plt.close()
    
    # Evaluate model
    logger.info("Evaluating TabNet model")
    model.eval()
    y_pred = []
    y_true = []
    
    with torch.no_grad():
        for batch_X, batch_y in test_loader:
            batch_X = batch_X.to(device)
            output = model(batch_X)
            y_pred.extend(output.cpu().numpy())
            y_true.extend(batch_y.numpy())
    
    y_pred = np.array(y_pred)
    y_true = np.array(y_true)
    
    # Calculate metrics
    r2 = r2_score(y_true, y_pred)
    rmse = np.sqrt(mean_squared_error(y_true, y_pred))
    mae = mean_absolute_error(y_true, y_pred)
    spearman_corr = spearmanr(y_true, y_pred)[0]
    pearson_corr = pearsonr(y_true, y_pred)[0]
    
    logger.info("Test metrics:")
    logger.info(f"  R²: {r2:.4f}")
    logger.info(f"  RMSE: {rmse:.4f}")
    logger.info(f"  MAE: {mae:.4f}")
    logger.info(f"  Spearman's rank correlation: {spearman_corr:.4f}")
    logger.info(f"  Pearson correlation: {pearson_corr:.4f}")
    
    # Get feature importance
    feature_importance = model.get_feature_importance(torch.FloatTensor(X_test).to(device))
    feature_names = data['feature_names']
    
    # Save feature importance
    importance_df = pd.DataFrame({
        'feature': feature_names,
        'importance': feature_importance
    }).sort_values('importance', ascending=False)
    importance_df.to_csv(os.path.join(metrics_dir, 'feature_importance.tsv'), sep='\t', index=False)
    
    # Plot feature importance
    plt.figure(figsize=(12, 6))
    sns.barplot(data=importance_df.head(20), x='importance', y='feature')
    plt.title('Top 20 Most Important Features')
    plt.tight_layout()
    plt.savefig(os.path.join(metrics_dir, 'feature_importance.png'))
    plt.close()
    
    # Save model configuration
    config = {
        'input_dim': input_dim,
        'n_d': n_d,
        'n_a': n_a,
        'n_steps': n_steps,
        'gamma': gamma,
        'n_independent': n_independent,
        'n_shared': n_shared,
        'batch_size': batch_size,
        'learning_rate': learning_rate,
        'weight_decay': weight_decay,
        'scheduler_gamma': scheduler_gamma,
        'huber_delta': huber_delta
    }
    
    with open(os.path.join(output_dir, 'config.json'), 'w') as f:
        json.dump(config, f, indent=4)
    
    logger.info("Training complete")

def evaluate_model(model, data, metrics_dir):
    """
    Evaluate the trained TabNet model and calculate feature importance.
    
    Args:
        model: Trained TabNet model
        data: Dictionary containing the data splits
        metrics_dir: Directory to save metrics and plots
        
    Returns:
        Dictionary of test metrics
    """
    logger.info("Evaluating TabNet model")
    
    # Determine feature set to use
    feature_set = 'selected' if 'selected' in data['test']['features'] else 'all_features'
    
    # Get test data
    X_test = {feature_set: data['test']['features'][feature_set]}
    y_test = data['test']['targets']
    
    # Get feature names
    if 'selected_features' in data:
        feature_names = data['selected_features']
    else:
        feature_names = data['feature_names']
    
    # Convert to torch tensors if needed
    test_features = {}
    for key, value in X_test.items():
        if not isinstance(value, torch.Tensor):
            test_features[key] = torch.FloatTensor(value)
        else:
            test_features[key] = value
    
    if not isinstance(y_test, torch.Tensor):
        test_targets = torch.FloatTensor(y_test).reshape(-1, 1)
    else:
        test_targets = y_test.reshape(-1, 1)
    
    # Set model to evaluation mode
    model.eval()
    
    # Make predictions on test set
    with torch.no_grad():
        predictions, _ = model(test_features[feature_set])
    
    # Calculate evaluation metrics
    metrics = {}
    metrics["r2"] = sklearn.metrics.r2_score(test_targets.numpy(), predictions.numpy())
    metrics["rmse"] = np.sqrt(sklearn.metrics.mean_squared_error(test_targets.numpy(), predictions.numpy()))
    metrics["mae"] = sklearn.metrics.mean_absolute_error(test_targets.numpy(), predictions.numpy())
    metrics["spearman"] = scipy.stats.spearmanr(test_targets.numpy().flatten(), predictions.numpy().flatten())[0]
    metrics["pearson"] = scipy.stats.pearsonr(test_targets.numpy().flatten(), predictions.numpy().flatten())[0]
    
    # Log metrics
    logger.info("Test metrics:")
    logger.info(f"  R²: {metrics['r2']:.4f}")
    logger.info(f"  RMSE: {metrics['rmse']:.4f}")
    logger.info(f"  MAE: {metrics['mae']:.4f}")
    logger.info(f"  Spearman's rank correlation: {metrics['spearman']:.4f}")
    logger.info(f"  Pearson correlation: {metrics['pearson']:.4f}")
    
    # Plot predictions vs. actual
    plt.figure(figsize=(10, 6))
    plt.scatter(test_targets.numpy(), predictions.numpy(), alpha=0.5)
    plt.plot([test_targets.min(), test_targets.max()], [test_targets.min(), test_targets.max()], 'r--')
    plt.xlabel("Actual growth rate")
    plt.ylabel("Predicted growth rate")
    plt.title("TabNet Model Predictions vs. Actual")
    plt.tight_layout()
    plt.savefig(Path(metrics_dir) / "predictions.png")
    plt.close()
    
    # Save metrics to JSON
    metrics_path = Path(metrics_dir) / "test_metrics.json"
    # Convert numpy types to standard Python types for JSON serialization
    json_metrics = {k: float(v) for k, v in metrics.items()}
    with open(metrics_path, 'w') as f:
        json.dump(json_metrics, f, indent=2)
    
    # Calculate feature importance using improved ensemble method
    try:
        # Use the feature_importance_ensemble method for more reliable results
        feature_importance = model.feature_importance_ensemble(test_features[feature_set])
        
        if feature_importance is not None and len(feature_importance) > 0:
            # Create DataFrame
            importance_df = pd.DataFrame({
                'feature': feature_names,
                'importance': feature_importance
            })
            
            # Sort by importance
            importance_df = importance_df.sort_values('importance', ascending=False)
            
            # Plot feature importance (top 20)
            top_n = min(20, len(feature_names))
            plt.figure(figsize=(12, 8))
            plt.barh(range(top_n), importance_df['importance'].values[:top_n], align='center')
            plt.yticks(range(top_n), importance_df['feature'].values[:top_n])
            plt.xlabel('Importance')
            plt.title('Feature Importance (Top 20)')
            plt.tight_layout()
            plot_path = Path(metrics_dir) / 'feature_importance.png'
            plt.savefig(plot_path)
            plt.close()
            
            # Save feature importance to TSV
            importance_path = Path(metrics_dir) / 'feature_importance.tsv'
            importance_df.to_csv(importance_path, sep='\t', index=False)
            logger.info(f"Feature importance saved to {importance_path}")
    except Exception as e:
        logger.warning(f"Error calculating feature importance: {str(e)}")
    
    return metrics

def plot_training_metrics(metrics, metrics_dir):
    """
    Plot training metrics.
    
    Args:
        metrics: Dictionary of training metrics
        metrics_dir: Directory to save plots
    """
    logger.info("Plotting training metrics")
    
    # Create metrics directory if it doesn't exist
    os.makedirs(metrics_dir, exist_ok=True)
    
    # Plot loss curves
    plt.figure(figsize=(10, 6))
    plt.plot(metrics['loss'], label='Train Loss')
    if metrics['val_loss'] is not None:
        plt.plot(metrics['val_loss'], label='Validation Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('Training and Validation Loss')
    plt.legend()
    plt.grid(True)
    plt.savefig(os.path.join(metrics_dir, 'loss_curves.png'))
    plt.close()
    
    # Plot additional metrics if available
    for metric_name in metrics:
        if metric_name not in ['loss', 'val_loss'] and not metric_name.startswith('val_'):
            plt.figure(figsize=(10, 6))
            plt.plot(metrics[metric_name], label=f'Train {metric_name}')
            if f'val_{metric_name}' in metrics:
                plt.plot(metrics[f'val_{metric_name}'], label=f'Validation {metric_name}')
            plt.xlabel('Epoch')
            plt.ylabel(metric_name)
            plt.title(f'Training and Validation {metric_name}')
            plt.legend()
            plt.grid(True)
            plt.savefig(os.path.join(metrics_dir, f'{metric_name}_curves.png'))
            plt.close()

def save_model_and_config(_, trainer, args, output_dir):
    """
    Save the trained model and configuration.

    Args:
        _: Trained TabNet model (not used directly as it's saved via trainer)
        trainer: TabNet trainer
        args: Command line arguments
        output_dir: Directory to save model and configuration
    """
    logger.info("Saving model and configuration")

    # Create output directory
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)

    # Save model
    model_path = output_dir / 'tabnet_model.pt'
    trainer.save_model(str(model_path))

    # Save configuration
    config = vars(args)
    config_path = output_dir / 'config.json'
    with open(config_path, 'w') as f:
        json.dump(config, f, indent=2)

    logger.info(f"Model saved to {model_path}")
    logger.info(f"Configuration saved to {config_path}")

def test_feature_importance_simplified():
    """
    Simplified test for feature importance.
    
    This doesn't rely on the actual TabNet model internals, just verifies the feature_importance interface.
    """
    import torch
    import numpy as np
    import matplotlib.pyplot as plt
    from torch import nn
    
    # Create mock feature importance data
    n_features = 20
    feature_names = [f'Feature {i}' for i in range(n_features)]
    
    # Generate random feature importance scores
    feature_importance = np.random.random(n_features)
    feature_importance = feature_importance / feature_importance.sum()  # Normalize
    
    # Create DataFrame
    import pandas as pd
    importance_df = pd.DataFrame({
        'feature': feature_names,
        'importance': feature_importance
    })
    
    # Sort by importance
    importance_df = importance_df.sort_values('importance', ascending=False)
    
    # Plot top features
    plt.figure(figsize=(10, 6))
    plt.barh(range(10), importance_df['importance'].values[:10], align='center')
    plt.yticks(range(10), importance_df['feature'].values[:10])
    plt.xlabel('Importance')
    plt.title('Feature Importance Test')
    plt.tight_layout()
    plt.savefig('feature_importance_test.png')
    plt.close()
    
    print("Feature importance values:", feature_importance)
    print("Feature importance test successful!")
    return True

def main():
    """Main function."""
    # Parse arguments
    parser = argparse.ArgumentParser(description='Train TabNet model for growth rate prediction')

    # Input/output arguments
    parser.add_argument('--feature-file', type=str, required=False,
                        help='Path to feature file (TSV format)')
    parser.add_argument('--metadata', type=str, required=False,
                        help='Path to metadata file (TSV format)')
    parser.add_argument('--output-dir', type=str, default='models/tabnet_model',
                        help='Directory to save model and results')
    parser.add_argument('--metrics-dir', type=str, default='metrics/tabnet_model',
                        help='Directory to save metrics and plots')

    # Model parameters
    parser.add_argument('--n-steps', type=int, default=3,
                        help='Number of sequential processing steps in TabNet')
    parser.add_argument('--hidden-dim', type=int, default=64,
                        help='Hidden dimension size')
    parser.add_argument('--n-shared-glu', type=int, default=2,
                        help='Number of shared GLU layers')
    parser.add_argument('--n-independent-glu', type=int, default=2,
                        help='Number of independent GLU layers')
    parser.add_argument('--virtual-batch-size', type=int, default=128,
                        help='Virtual batch size for Ghost Batch Normalization')
    parser.add_argument('--mask-type', type=str, default='sparsemax', choices=['softmax', 'sparsemax', 'entmax'],
                        help='Mask activation function type')
    # Add new parameters for the improved TabNet model
    parser.add_argument('--gamma', type=float, default=1.3,
                        help='Feature reuse coefficient for masking')
    parser.add_argument('--attention-dropout', type=float, default=0.0,
                        help='Dropout rate for attention layers')
    parser.add_argument('--prediction-dropout', type=float, default=0.0,
                        help='Dropout rate for prediction head')
    parser.add_argument('--relaxation-factor', type=float, default=1.0,
                        help='Relaxation factor for feature selection')
                        
    # Loss function parameters
    parser.add_argument('--loss-fn', type=str, default='huber', choices=['mse', 'huber'],
                        help='Loss function to use')
    parser.add_argument('--huber-delta', type=float, default=None,
                        help='Delta parameter for Huber loss (if None, will be set automatically)')
    parser.add_argument('--sample-weight', type=str, default='inverse', 
                        choices=['none', 'inverse', 'log_inverse'],
                        help='Sample weighting strategy for imbalanced data')
    parser.add_argument('--log-transform', action='store_true',
                        help='Apply log transformation to target variable')

    # Training parameters
    parser.add_argument('--lr', type=float, default=1e-3,
                        help='Learning rate')
    parser.add_argument('--weight-decay', type=float, default=1e-5,
                        help='Weight decay for regularization')
    parser.add_argument('--batch-size', type=int, default=1024,
                        help='Batch size for training')
    parser.add_argument('--epochs', type=int, default=100,
                        help='Number of epochs to train for')
    parser.add_argument('--early-stopping-patience', type=int, default=10,
                        help='Patience for early stopping')
    parser.add_argument('--seed', type=int, default=42,
                        help='Random seed')
    parser.add_argument('--test-size', type=float, default=0.2,
                        help='Fraction of data to use for testing')
    parser.add_argument('--val-size', type=float, default=0.2,
                        help='Fraction of training data to use for validation')

    # Feature selection
    parser.add_argument('--feature-selection', action='store_true',
                        help='Use feature selection')
    parser.add_argument('--n-features', type=int, default=50,
                        help='Number of features to select')

    # Test mode
    parser.add_argument('--test-feature-importance', action='store_true',
                        help='Test feature importance')

    args = parser.parse_args()
    
    # Test feature importance
    if args.test_feature_importance:
        test_feature_importance_simplified()
        return
        
    # Verify required arguments for training
    if args.feature_file is None or args.metadata is None:
        print("Error: --feature-file and --metadata arguments are required for training")
        return

    # Create output directories
    output_dir = Path(args.output_dir)
    metrics_dir = Path(args.metrics_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    metrics_dir.mkdir(parents=True, exist_ok=True)

    # Load data
    features_df, metadata_df = load_data(args.feature_file, args.metadata)

    # Prepare data
    X, y, feature_names = prepare_data(args.feature_file, args.metadata)

    # Feature selection if enabled
    if args.feature_selection:
        data = select_features(data, n_features=args.n_features)

    # Train TabNet model
    train_tabnet_model(data, args.output_dir, args.metrics_dir)

    # Evaluate model
    _ = evaluate_model(data['model'], data, args.metrics_dir)

    # Plot training metrics
    plot_training_metrics(data['training_metrics'], args.metrics_dir)

    # Save model and configuration
    save_model_and_config(data['model'], data['trainer'], args, args.output_dir)

    logger.info("Training complete")

if __name__ == '__main__':
    main() 