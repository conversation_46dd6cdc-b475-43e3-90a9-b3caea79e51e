#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Train a Deep Neural Network model for DeepMu with improved regularization techniques
to prevent overfitting and improve generalization.
"""

import os
import argparse
import logging
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader, random_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.model_selection import train_test_split
import joblib
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Define the improved neural network model with regularization
class RegularizedDNN(nn.Module):
    def __init__(self, input_dim, hidden_dims=[512, 256, 128], dropout_rates=[0.3, 0.4, 0.5], 
                 use_batch_norm=True, activation='relu'):
        super(RegularizedDNN, self).__init__()
        
        # Create layers
        layers = []
        prev_dim = input_dim
        
        # Choose activation function
        if activation == 'relu':
            act_fn = nn.ReLU()
        elif activation == 'leaky_relu':
            act_fn = nn.LeakyReLU(0.1)
        elif activation == 'elu':
            act_fn = nn.ELU()
        else:
            act_fn = nn.ReLU()
        
        # Build network with progressively increasing dropout
        for i, hidden_dim in enumerate(hidden_dims):
            # Linear layer
            layers.append(nn.Linear(prev_dim, hidden_dim))
            
            # Batch normalization (before activation)
            if use_batch_norm:
                layers.append(nn.BatchNorm1d(hidden_dim))
            
            # Activation
            layers.append(act_fn)
            
            # Dropout with increasing rate for deeper layers
            dropout_rate = dropout_rates[min(i, len(dropout_rates)-1)]
            layers.append(nn.Dropout(dropout_rate))
            
            prev_dim = hidden_dim
        
        self.feature_extractor = nn.Sequential(*layers)
        
        # Output layer with no activation or dropout
        self.output_layer = nn.Linear(prev_dim, 1)
        
        # Initialize weights with improved method
        self._init_weights()
    
    def _init_weights(self):
        """Initialize weights with improved initialization."""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                # Use Xavier/Glorot initialization for better stability
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
    
    def forward(self, x):
        features = self.feature_extractor(x)
        output = self.output_layer(features)
        return output.squeeze()
    
    def get_feature_importance(self, X, device):
        """
        Calculate feature importance using gradient-based method.
        
        Args:
            X: Input features tensor
            device: Device to run calculation on
            
        Returns:
            Dictionary mapping feature indices to importance scores
        """
        self.eval()
        X = X.to(device)
        X.requires_grad_(True)
        
        # Forward pass
        output = self(X)
        
        # Calculate gradients
        output.backward(torch.ones_like(output))
        
        # Get gradients with respect to input
        gradients = X.grad.abs()
        
        # Average gradients across samples
        importance = gradients.mean(dim=0).cpu().detach().numpy()
        
        return importance

# Define dataset class
class FeatureDataset(Dataset):
    def __init__(self, features, targets):
        self.features = torch.tensor(features, dtype=torch.float32)
        self.targets = torch.tensor(targets, dtype=torch.float32)
    
    def __len__(self):
        return len(self.features)
    
    def __getitem__(self, idx):
        return self.features[idx], self.targets[idx]

def train_epoch(model, dataloader, criterion, optimizer, device, l1_lambda=0.0, use_gradient_clipping=False, max_grad_norm=1.0):
    model.train()
    running_loss = 0.0
    
    for inputs, targets in dataloader:
        inputs, targets = inputs.to(device), targets.to(device)
        
        # Zero the parameter gradients
        optimizer.zero_grad()
        
        # Forward pass
        outputs = model(inputs)
        loss = criterion(outputs, targets)
        
        # Add L1 regularization
        if l1_lambda > 0:
            l1_norm = sum(p.abs().sum() for p in model.parameters())
            loss += l1_lambda * l1_norm
        
        # Backward pass and optimize
        loss.backward()
        
        # Gradient clipping to prevent exploding gradients
        if use_gradient_clipping:
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=max_grad_norm)
        
        optimizer.step()
        
        running_loss += loss.item() * inputs.size(0)
    
    return running_loss / len(dataloader.dataset)

def validate(model, dataloader, criterion, device):
    model.eval()
    running_loss = 0.0
    all_targets = []
    all_predictions = []
    
    with torch.no_grad():
        for inputs, targets in dataloader:
            inputs, targets = inputs.to(device), targets.to(device)
            
            # Forward pass
            outputs = model(inputs)
            loss = criterion(outputs, targets)
            
            running_loss += loss.item() * inputs.size(0)
            
            # Store predictions and targets for metrics
            all_targets.extend(targets.cpu().numpy())
            all_predictions.extend(outputs.cpu().numpy())
    
    # Calculate metrics
    val_loss = running_loss / len(dataloader.dataset)
    val_rmse = np.sqrt(mean_squared_error(all_targets, all_predictions))
    val_r2 = r2_score(all_targets, all_predictions)
    
    return val_loss, val_rmse, val_r2, all_targets, all_predictions

class EarlyStopping:
    """Early stopping to prevent overfitting"""
    def __init__(self, patience=10, min_delta=0, restore_best_weights=True):
        self.patience = patience
        self.min_delta = min_delta
        self.restore_best_weights = restore_best_weights
        self.best_score = None
        self.best_weights = None
        self.counter = 0
        self.early_stop = False
    
    def __call__(self, val_loss, model):
        score = -val_loss
        
        if self.best_score is None:
            self.best_score = score
            self.best_weights = {k: v.cpu().clone() for k, v in model.state_dict().items()}
        elif score < self.best_score + self.min_delta:
            self.counter += 1
            if self.counter >= self.patience:
                self.early_stop = True
        else:
            self.best_score = score
            self.best_weights = {k: v.cpu().clone() for k, v in model.state_dict().items()}
            self.counter = 0
        
        return self.early_stop
    
    def restore_weights(self, model):
        if self.restore_best_weights and self.best_weights is not None:
            model.load_state_dict(self.best_weights)

def load_data(feature_file, metadata_file):
    """
    Load and prepare feature and metadata files.
    
    Args:
        feature_file: Path to the combined feature file (TSV)
        metadata_file: Path to the metadata file (TSV)
        
    Returns:
        Tuple of (features_df, metadata_df)
    """
    logger.info(f"Loading features from {feature_file}")
    features_df = pd.read_csv(feature_file, sep='\t', index_col='genome_id')
    
    # Check for NaN values in features
    nan_count = features_df.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in features. Filling with 0.")
        features_df = features_df.fillna(0)
    
    # Check for infinite values
    inf_count = np.isinf(features_df.values).sum()
    if inf_count > 0:
        logger.warning(f"Found {inf_count} infinite values in features. Replacing with max/min values.")
        features_df = features_df.replace([np.inf, -np.inf], np.nan)
        features_df = features_df.fillna(features_df.max())

    logger.info(f"Loading metadata from {metadata_file}")
    metadata_df = pd.read_csv(metadata_file, sep='\t', index_col='genome_id')
    
    # Check for NaN values in metadata
    nan_count = metadata_df.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in metadata. Filling with 0.")
        metadata_df = metadata_df.fillna(0)

    # Get common genomes
    common_genomes = list(set(features_df.index) & set(metadata_df.index))
    logger.info(f"Found {len(common_genomes)} common genomes")

    # Filter to common genomes
    features_df = features_df.loc[common_genomes]
    metadata_df = metadata_df.loc[common_genomes]
    
    # Log data statistics
    logger.info(f"Feature statistics:")
    logger.info(f"  Shape: {features_df.shape}")
    logger.info(f"  Min: {features_df.min().min():.4f}")
    logger.info(f"  Max: {features_df.max().max():.4f}")
    logger.info(f"  Mean: {features_df.mean().mean():.4f}")
    logger.info(f"  Std: {features_df.std().mean():.4f}")
    
    logger.info(f"Metadata statistics:")
    logger.info(f"  Shape: {metadata_df.shape}")
    logger.info(f"  Growth rate range: {metadata_df['growth_rate'].min():.4f} to {metadata_df['growth_rate'].max():.4f}")
    logger.info(f"  Temperature range: {metadata_df['optimal_temperature'].min():.4f} to {metadata_df['optimal_temperature'].max():.4f}")

    return features_df, metadata_df

def train_model(feature_file, metadata_file, output_dir, target='growth_rate', metrics_dir=None, 
                batch_size=64, epochs=150, learning_rate=0.001, hidden_dims=[512, 256, 128], 
                dropout_rates=[0.3, 0.4, 0.5], weight_decay=1e-5, l1_lambda=1e-5,
                patience=15, activation='relu', use_batch_norm=True, validation_split=0.2,
                use_gradient_clipping=False, max_grad_norm=1.0, lr_scheduler='plateau',
                warmup_epochs=5, cyclic_lr_max=None, one_cycle_max_lr=None):
    """
    Train a regularized deep neural network model.
    
    Args:
        feature_file: Path to combined feature file (TSV)
        metadata_file: Path to metadata file (TSV)
        output_dir: Directory to save model and results
        target: Target variable to predict ('growth_rate' or 'temperature')
        metrics_dir: Directory to save metrics and plots
        batch_size: Batch size for training
        epochs: Number of epochs to train
        learning_rate: Learning rate for optimizer
        hidden_dims: List of hidden layer dimensions
        dropout_rates: List of dropout rates for each layer
        weight_decay: L2 regularization parameter
        l1_lambda: L1 regularization parameter
        patience: Early stopping patience
        activation: Activation function to use ('relu', 'leaky_relu', 'elu')
        use_batch_norm: Whether to use batch normalization
        validation_split: Proportion of data to use for validation
        use_gradient_clipping: Whether to use gradient clipping
        max_grad_norm: Maximum gradient norm for clipping
        lr_scheduler: Learning rate scheduler type ('plateau', 'cosine', 'one_cycle', 'none')
        warmup_epochs: Number of warmup epochs for learning rate schedulers
        cyclic_lr_max: Maximum learning rate for cyclic scheduler
        one_cycle_max_lr: Maximum learning rate for one-cycle scheduler
        
    Returns:
        Dictionary with training results
    """
    logger.info(f"Training regularized DNN model for {target}")
    
    # Create output directory
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Create metrics directory
    if metrics_dir is None:
        metrics_dir = Path('metrics/dnn_regularized')
    else:
        metrics_dir = Path(metrics_dir)
    metrics_dir.mkdir(parents=True, exist_ok=True)
    
    # Load and prepare data
    features_df, metadata_df = load_data(feature_file, metadata_file)
    
    # Validate input dimensions
    for dim in hidden_dims:
        if dim <= 0:
            raise ValueError(f"Invalid hidden dimension: {dim}")
    
    # Validate dropout rates
    for rate in dropout_rates:
        if not 0 <= rate < 1:
            raise ValueError(f"Invalid dropout rate: {rate}")
    
    # Get target variable
    if target == 'growth_rate':
        y = metadata_df['growth_rate'].values
        target_name = 'Growth Rate'
        logger.info(f"Growth rate statistics - Min: {y.min():.4f}, Max: {y.max():.4f}, Mean: {y.mean():.4f}")
    else:  # temperature
        y = metadata_df['optimal_temperature'].values
        target_name = 'Optimal Temperature'
        logger.info(f"Temperature statistics - Min: {y.min():.4f}, Max: {y.max():.4f}, Mean: {y.mean():.4f}")
    
    # Get feature matrix - exclude metadata columns
    X = features_df.drop(['growth_rate', 'optimal_temperature', 'taxid', 'codon_table', 'kingdom'], 
                     axis=1, errors='ignore').values
    feature_names = features_df.drop(['growth_rate', 'optimal_temperature', 'taxid', 'codon_table', 'kingdom'], 
                                 axis=1, errors='ignore').columns.tolist()
    
    # Check for NaN values in features
    if np.isnan(X).any():
        logger.warning(f"Found NaN values in feature matrix. Filling with 0.")
        X = np.nan_to_num(X, 0)
    
    # Check for infinite values
    if np.isinf(X).any():
        logger.warning(f"Found infinite values in feature matrix. Replacing with max/min values.")
        X = np.nan_to_num(X, np.nan)
        X = np.nan_to_num(X, np.nanmax(X))
    
    # Standardize features
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    # Log scaling statistics
    logger.info(f"Feature statistics after scaling:")
    logger.info(f"  Min: {X_scaled.min():.4f}")
    logger.info(f"  Max: {X_scaled.max():.4f}")
    logger.info(f"  Mean: {X_scaled.mean():.4f}")
    logger.info(f"  Std: {X_scaled.std():.4f}")
    
    # Save scaler
    scaler_path = os.path.join(output_dir, f'{target}_scaler.joblib')
    joblib.dump(scaler, scaler_path)
    logger.info(f"Saved scaler to {scaler_path}")
    
    # Split data into train and validation sets with stratification
    # For regression, we create bins for stratification
    if len(np.unique(y)) > 10:  # If it's a continuous variable with many unique values
        bins = pd.qcut(y, 5, labels=False, duplicates='drop')
    else:  # For discrete variables or those with few unique values
        bins = y  # Use the values directly for stratification
    
    X_train, X_val, y_train, y_val = train_test_split(
        X_scaled, y, test_size=validation_split, random_state=42, stratify=bins
    )
    
    # Log train/val statistics
    logger.info(f"Training set: {X_train.shape[0]} samples")
    logger.info(f"Validation set: {X_val.shape[0]} samples")
    logger.info(f"Training target statistics - Min: {y_train.min():.4f}, Max: {y_train.max():.4f}, Mean: {y_train.mean():.4f}")
    logger.info(f"Validation target statistics - Min: {y_val.min():.4f}, Max: {y_val.max():.4f}, Mean: {y_val.mean():.4f}")
    
    # Create datasets and dataloaders
    train_dataset = FeatureDataset(X_train, y_train)
    val_dataset = FeatureDataset(X_val, y_val)
    
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size)
    
    # Set device
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    logger.info(f"Using device: {device}")
    
    # Create model
    input_dim = X_scaled.shape[1]
    model = RegularizedDNN(
        input_dim=input_dim, 
        hidden_dims=hidden_dims, 
        dropout_rates=dropout_rates,
        use_batch_norm=use_batch_norm,
        activation=activation
    )
    model.to(device)
    
    # Define loss function and optimizer
    criterion = nn.MSELoss()
    optimizer = optim.AdamW(model.parameters(), lr=learning_rate, weight_decay=weight_decay)
    
    # Learning rate scheduler
    if lr_scheduler == 'plateau':
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, mode='min', factor=0.5, patience=5, verbose=True, min_lr=1e-6
        )
    elif lr_scheduler == 'cosine':
        scheduler = optim.lr_scheduler.CosineAnnealingLR(
            optimizer, T_max=epochs, eta_min=1e-6
        )
    elif lr_scheduler == 'one_cycle':
        # Calculate total steps
        steps_per_epoch = len(train_loader)
        total_steps = steps_per_epoch * epochs
        
        # Set the max_lr for one cycle
        max_lr = one_cycle_max_lr if one_cycle_max_lr is not None else learning_rate * 10
        
        logger.info(f"Setting up OneCycleLR with max_lr={max_lr}, total_steps={total_steps}")
        
        scheduler = optim.lr_scheduler.OneCycleLR(
            optimizer, 
            max_lr=max_lr, 
            total_steps=total_steps,
            pct_start=warmup_epochs/epochs,
            anneal_strategy='cos'
        )
    else:  # 'none'
        # Dummy scheduler that keeps the learning rate constant
        scheduler = optim.lr_scheduler.LambdaLR(
            optimizer, lambda epoch: 1.0
        )
    
    # Early stopping
    early_stopping = EarlyStopping(patience=patience, min_delta=0.001, restore_best_weights=True)
    
    # Training loop
    train_losses = []
    val_losses = []
    val_rmses = []
    val_r2s = []
    
    for epoch in range(epochs):
        # Train
        train_loss = train_epoch(model, train_loader, criterion, optimizer, device, l1_lambda, use_gradient_clipping, max_grad_norm)
        train_losses.append(train_loss)
        
        # Validate
        val_loss, val_rmse, val_r2, _, _ = validate(model, val_loader, criterion, device)
        val_losses.append(val_loss)
        val_rmses.append(val_rmse)
        val_r2s.append(val_r2)
        
        # Update learning rate
        if lr_scheduler == 'plateau':
            scheduler.step(val_loss)
        elif lr_scheduler in ['cosine', 'one_cycle']:
            scheduler.step()
        
        # Log progress
        logger.info(f"Epoch {epoch+1}/{epochs} - Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}, Val RMSE: {val_rmse:.4f}, Val R²: {val_r2:.4f}")
        
        # Check early stopping
        if early_stopping(val_loss, model):
            logger.info(f"Early stopping at epoch {epoch+1}")
            break
    
    # Restore best weights
    early_stopping.restore_weights(model)
    
    # Save best model
    model_path = os.path.join(output_dir, f'{target}_model.pt')
    torch.save(model.state_dict(), model_path)
    logger.info(f"Saved best model to {model_path}")
    
    # Final evaluation
    train_loss, train_rmse, train_r2, train_targets, train_preds = validate(model, train_loader, criterion, device)
    val_loss, val_rmse, val_r2, val_targets, val_preds = validate(model, val_loader, criterion, device)
    
    logger.info(f"{target} - Train: R²={train_r2:.4f}, RMSE={train_rmse:.4f}")
    logger.info(f"{target} - Val: R²={val_r2:.4f}, RMSE={val_rmse:.4f}")
    
    # Calculate feature importance
    X_tensor = torch.tensor(X_scaled[:1000], dtype=torch.float32)  # Use subset for efficiency
    importance = model.get_feature_importance(X_tensor, device)
    
    # Create feature importance dataframe
    importance_df = pd.DataFrame({
        'feature': feature_names,
        'importance': importance
    })
    importance_df = importance_df.sort_values('importance', ascending=False)
    
    # Save feature importance
    importance_path = os.path.join(metrics_dir, f'{target}_feature_importance.tsv')
    importance_df.to_csv(importance_path, sep='\t', index=False)
    logger.info(f"Saved feature importance to {importance_path}")
    
    # Identify codon-related features
    codon_features = [f for f in feature_names if any(pattern in f.lower() for pattern in
                    ['cub', 'cpb', 'cai', 'enc', 'heg_', 'bg_', 'delta_', 'rscu_',
                     'codon_bias_', 'aaai', 'aa_bias_heg_bp', 'milc'])]
    
    # Create codon feature importance dataframe
    codon_importance_df = importance_df[importance_df['feature'].isin(codon_features)]
    codon_importance_df = codon_importance_df.sort_values('importance', ascending=False)
    
    # Save codon feature importance
    codon_importance_path = os.path.join(metrics_dir, f'{target}_codon_feature_importance.tsv')
    codon_importance_df.to_csv(codon_importance_path, sep='\t', index=False)
    logger.info(f"Saved codon feature importance to {codon_importance_path}")
    
    # Log top codon features
    logger.info(f"Top 10 codon features for {target}:")
    for i, (feature, imp) in enumerate(zip(codon_importance_df['feature'].head(10), 
                                          codon_importance_df['importance'].head(10))):
        logger.info(f"{i+1}. {feature}: {imp:.6f}")
    
    # Plot training curves
    plt.figure(figsize=(12, 5))
    plt.subplot(1, 2, 1)
    plt.plot(train_losses, label='Train Loss')
    plt.plot(val_losses, label='Validation Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss (MSE)')
    plt.title('Training and Validation Loss')
    plt.legend()
    
    plt.subplot(1, 2, 2)
    plt.plot(val_rmses, label='RMSE')
    plt.plot(val_r2s, label='R²')
    plt.xlabel('Epoch')
    plt.ylabel('Metric')
    plt.title('Validation Metrics')
    plt.legend()
    
    plt.tight_layout()
    plt.savefig(os.path.join(metrics_dir, f'{target}_training_curves.png'))
    
    # Plot feature importance
    plt.figure(figsize=(12, 8))
    plt.barh(importance_df['feature'].values[:20], importance_df['importance'].values[:20])
    plt.xlabel('Importance')
    plt.ylabel('Feature')
    plt.title(f'Top 20 Features for {target_name}')
    plt.tight_layout()
    plt.savefig(os.path.join(metrics_dir, f'{target}_feature_importance.png'))
    
    # Plot codon feature importance
    plt.figure(figsize=(12, 8))
    plt.barh(codon_importance_df['feature'].values[:20], codon_importance_df['importance'].values[:20])
    plt.xlabel('Importance')
    plt.ylabel('Feature')
    plt.title(f'Top 20 Codon Features for {target_name}')
    plt.tight_layout()
    plt.savefig(os.path.join(metrics_dir, f'{target}_codon_feature_importance.png'))
    
    # Plot actual vs predicted
    plt.figure(figsize=(10, 8))
    plt.scatter(train_targets, train_preds, alpha=0.5, label='Train')
    plt.scatter(val_targets, val_preds, alpha=0.5, label='Validation')
    plt.plot([min(y), max(y)], [min(y), max(y)], 'k--')
    plt.xlabel(f'Actual {target_name}')
    plt.ylabel(f'Predicted {target_name}')
    plt.title(f'Actual vs Predicted {target_name}')
    plt.legend()
    plt.tight_layout()
    plt.savefig(os.path.join(metrics_dir, f'{target}_actual_vs_predicted.png'))
    
    # Save predictions
    pd.DataFrame({
        'actual': val_targets,
        'predicted': val_preds
    }).to_csv(os.path.join(metrics_dir, f'{target}_predictions.tsv'), sep='\t', index=False)
    
    # Save results
    results = {
        'train_loss': train_loss,
        'train_rmse': train_rmse,
        'train_r2': train_r2,
        'val_loss': val_loss,
        'val_rmse': val_rmse,
        'val_r2': val_r2
    }
    
    results_df = pd.DataFrame([results])
    results_df.to_csv(os.path.join(metrics_dir, f'{target}_results.tsv'), sep='\t', index=False)
    
    logger.info(f"Saved results to {metrics_dir}")
    
    return results

def main():
    parser = argparse.ArgumentParser(description='Train a Regularized Deep Neural Network model for DeepMu')
    parser.add_argument('--feature-file', type=str, default='./training_data/combined_features.tsv', 
                        help='Path to the combined feature file (TSV)')
    parser.add_argument('--metadata', type=str, default='./training_data/metadata.tsv', 
                        help='Path to metadata file')
    parser.add_argument('--output-dir', type=str, default='models/dnn_regularized', 
                        help='Directory to save model and results')
    parser.add_argument('--metrics-dir', type=str, default='metrics/dnn_regularized', 
                        help='Directory to save metrics and plots')
    parser.add_argument('--target', type=str, choices=['growth_rate', 'temperature', 'both'], default='both',
                        help='Target variable to predict')
    parser.add_argument('--batch-size', type=int, default=64, 
                        help='Batch size for training')
    parser.add_argument('--epochs', type=int, default=150, 
                        help='Number of epochs to train')
    parser.add_argument('--learning-rate', type=float, default=0.001, 
                        help='Learning rate for optimizer')
    parser.add_argument('--hidden-dims', type=str, default='512,256,128', 
                        help='Comma-separated list of hidden layer dimensions')
    parser.add_argument('--dropout-rates', type=str, default='0.3,0.4,0.5',
                        help='Comma-separated list of dropout rates')
    parser.add_argument('--weight-decay', type=float, default=1e-5, 
                        help='L2 regularization parameter')
    parser.add_argument('--l1-lambda', type=float, default=1e-5, 
                        help='L1 regularization parameter')
    parser.add_argument('--patience', type=int, default=15, 
                        help='Early stopping patience')
    parser.add_argument('--activation', type=str, choices=['relu', 'leaky_relu', 'elu'], default='relu',
                        help='Activation function to use')
    parser.add_argument('--use-batch-norm', action='store_true', 
                        help='Use batch normalization')
    parser.add_argument('--validation-split', type=float, default=0.2, 
                        help='Proportion of data to use for validation')
    parser.add_argument('--use-gradient-clipping', action='store_true', 
                        help='Use gradient clipping (enabled by default in train_epoch)')
    parser.add_argument('--max-grad-norm', type=float, default=1.0, 
                        help='Maximum gradient norm for clipping')
    
    # Add learning rate scheduler options
    parser.add_argument('--lr-scheduler', type=str, default='plateau',
                        choices=['plateau', 'cosine', 'one_cycle', 'none'],
                        help='Learning rate scheduler type')
    parser.add_argument('--warmup-epochs', type=int, default=5,
                        help='Number of warmup epochs for learning rate schedulers')
    parser.add_argument('--cyclic-lr-max', type=float, default=None,
                        help='Maximum learning rate for cyclic scheduler')
    parser.add_argument('--one-cycle-max-lr', type=float, default=None,
                        help='Maximum learning rate for one-cycle scheduler')
    
    args = parser.parse_args()
    
    # Parse hidden dimensions and dropout rates
    hidden_dims = [int(dim) for dim in args.hidden_dims.split(',')]
    dropout_rates = [float(rate) for rate in args.dropout_rates.split(',')]
    
    try:
        # Create output and metrics directories
        os.makedirs(args.output_dir, exist_ok=True)
        os.makedirs(args.metrics_dir, exist_ok=True)
        
        # Train models
        if args.target in ['growth_rate', 'both']:
            train_model(
                args.feature_file, 
                args.metadata, 
                args.output_dir, 
                'growth_rate', 
                args.metrics_dir,
                args.batch_size,
                args.epochs,
                args.learning_rate,
                hidden_dims,
                dropout_rates,
                args.weight_decay,
                args.l1_lambda,
                args.patience,
                args.activation,
                args.use_batch_norm,
                args.validation_split,
                args.use_gradient_clipping,
                args.max_grad_norm,
                args.lr_scheduler,
                args.warmup_epochs,
                args.cyclic_lr_max if args.cyclic_lr_max is not None else args.learning_rate * 5,
                args.one_cycle_max_lr if args.one_cycle_max_lr is not None else args.learning_rate * 10
            )
        
        if args.target in ['temperature', 'both']:
            train_model(
                args.feature_file, 
                args.metadata, 
                args.output_dir, 
                'temperature', 
                args.metrics_dir,
                args.batch_size,
                args.epochs,
                args.learning_rate,
                hidden_dims,
                dropout_rates,
                args.weight_decay,
                args.l1_lambda,
                args.patience,
                args.activation,
                args.use_batch_norm,
                args.validation_split,
                args.use_gradient_clipping,
                args.max_grad_norm,
                args.lr_scheduler,
                args.warmup_epochs,
                args.cyclic_lr_max if args.cyclic_lr_max is not None else args.learning_rate * 5,
                args.one_cycle_max_lr if args.one_cycle_max_lr is not None else args.learning_rate * 10
            )
        
        logger.info("Regularized Deep Neural Network model training completed successfully")
        return 0
    
    except Exception as e:
        logger.error(f"Error training Regularized Deep Neural Network model: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == '__main__':
    main()
