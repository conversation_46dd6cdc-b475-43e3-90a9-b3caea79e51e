#!/bin/bash


# >5000 genomes
# ------------------------------------------
## Growth metrics
# metric  value
# R2      0.8938773514032439
# RMSE    1.0359451157762432
# MAE     0.5109071474646792
# Log_R2  0.9305236691070248
# Log_RMSE        0.1720057840663965
# Log_MAE 0.1099039673205874
# python predict_with_correct_growth_model.py --feature_file training_data/combined_features.tsv --metadata_file training_data/metadata.tsv --output_dir predictions/correct_growth_model_filtered --model_dir models/hybrid_enhanced_nn_v1 --filter_outliers

## Growth metrics
# metric  value
# R2      0.8938773514032439
# RMSE    1.0359451157762432
# MAE     0.5109071474646792
# Log_R2  0.9305236691070248
# Log_RMSE        0.17200578406639652
# Log_MAE 0.10990396732058748
# Sqrt_R2 0.9235735892958078
# Sqrt_RMSE       0.22070524936967661
# Sqrt_MAE        0.13458564564344913
# python predict_with_sqrt_transform.py --feature_file training_data/combined_features.tsv --metadata_file training_data/metadata.tsv --output_dir predictions/no_sqrt_transform --model_dir models/hybrid_enhanced_nn_v1 --filter_outliers

# 
# Growth metrics - log-transform best
# Evaluation metrics - R²=0.8718, RMSE=1.4265, MAE=0.5683
# Log-scale metrics - R²=0.9312, RMSE=0.1774, MAE=0.1120
# Sqrt-scale metrics - R²=0.9196, RMSE=0.2429, MAE=0.1403
# python predict_growth_with_zscore_outliers.py --feature_file ./training_data/combined_features.tsv --metadata_file ./training_data/metadata.tsv --output_dir result_growth_with_outliers  --zscore_threshold 7 --apply_sqrt



### Temperature ensemble optimization results:
# Standard model R²: 0.8812
# Advanced model R²: 0.8534
# Ensemble R²: 0.8806
# Optimized weights: Standard=0.5000, Advanced=0.5000
### Making predictions for 5041 samples
# Evaluation metrics (transformed space):
# Growth Rate: R²=-0.0002, RMSE=7808.0452, MAE=113.1592: a inverse-transformation should be done first to evaluate, the spearman corr is excellent (98%)
# Temperature: R²=-8.5399, RMSE=36.1078, MAE=34.6089
# Overall: R²=-4.2701, RMSE=3922.0765, MAE=73.8840
# python final_hybrid_model.py --feature_file training_data/combined_features.tsv --metadata_file training_data/metadata.tsv --output_dir models/final_hybrid_rl --n_growth_features 250 --n_temp_features 700



# ------------------------------------------
# Best models 

# Hybrid model with improved LightGBM and NN
# Final metrics (scaled) - Overall R²: 0.8920
# Final metrics (scaled) - Growth Rate R²: 0.9207
# Final metrics (scaled) - Temperature R²: 0.8633
# Final metrics (original) - Overall R²: 0.8578
# Final metrics (original) - Growth Rate R²: 0.8523
# Final metrics (original) - Temperature R²: 0.8633
# python train_hybrid_with_enhanced_nn.py --feature_file training_data/combined_features.tsv --metadata_file training_data/metadata.tsv --output_dir models/hybrid_enhanced_nn_v1 --n_features 150


# Improve NN for temperature prediction
# Train - Loss: 8.9624, RMSE: 2.9937, R²: 0.9467, MAE: 1.9829
# Validation - Loss: 8.4228, RMSE: 2.9022, R²: 0.9378, MAE: 1.8971
# Test - Loss: 9.2330, RMSE: 3.0386, R²: 0.9284, MAE: 1.9749
# python evaluate_enhanced_dnn_temperature.py --model-dir models/enhanced_dnn_temperature_v2 --output-dir metrics/enhanced_dnn_temperature_v2_eval


# Improve NN for temperature prediction, merged with Hybrid model with improved LightGBM and NN
# Final metrics (scaled) - Overall R²: 0.8979
# Final metrics (scaled) - Growth Rate R²: 0.9207
# Final metrics (scaled) - Temperature R²: 0.8752
# python train_hybrid_with_enhanced_temp_dnn.py --feature_file training_data/combined_features.tsv --metadata_file training_data/metadata.tsv --output_dir models/hybrid_enhanced_temp_dnn_v1 --n_features 150
 
# predict_temperature.py: Uses the enhanced DNN model for temperature prediction (R² = 0.9421)
# predict_with_correct_growth_model.py: Uses the hybrid model with enhanced NN for growth rate prediction (R² = 0.9346)
# predict_with_specialized_models_fixed.py: Combines both models for comprehensive prediction (Overall R² = 0.9426)

# Growth Rate: R²=0.9432, RMSE=1.0336, MAE=0.3532
# Temperature: R²=0.9421, RMSE=2.9847, MAE=1.9641
# Overall: R²=0.9426, RMSE=2.0092, MAE=1.1586
# python predict_with_specialized_models_fixed.py --feature_file training_data/combined_features.tsv --metadata_file training_data/metadata.tsv --output_dir predictions/specialized_models_final

# Final Growth Rate Metrics - R²: 0.9231, RMSE: 0.2042, MAE: 0.1154
# Final Temperature Metrics - R²: 0.8128, RMSE: 0.7673, MAE: 0.4152
# Overall Metrics - R²: 0.8679, RMSE: 0.4858, MAE: 0.2653 
# python hybrid_model_v6.py --feature_file "./training_data/combined_features.tsv" --metadata_file "./training_data/metadata.tsv" --output_dir metrics_v6  --n_growth_features 200 --n_temp_features 300

# Performance Metrics for 2492 genomes 
# Growth Rate Train Metrics - R²: 0.9671, RMSE: 0.1281, MAE: 0.0828
# Growth Rate Val Metrics - R²: 0.9274, RMSE: 0.1907, MAE: 0.1225
# Growth Rate Test Metrics - R²: 0.8069, RMSE: 0.3492, MAE: 0.1397
# Temperature Train Metrics - R²: 0.9883, RMSE: 0.1952, MAE: 0.1178
# Temperature Val Metrics - R²: 0.8689, RMSE: 0.5985, MAE: 0.3577
# Temperature Test Metrics - R²: 0.8965, RMSE: 0.5761, MAE: 0.3412
# Final Growth Rate Metrics - R²: 0.8069, RMSE: 0.3492, MAE: 0.1397
# Final Temperature Metrics - R²: 0.8965, RMSE: 0.5761, MAE: 0.3412
# Overall Metrics - R²: 0.8517, RMSE: 0.4626, MAE: 0.2405
# python final_hybrid_model.py  --feature_file "./training_data/combined_features_2492.tsv" --metadata_file "./training_data/metadata_2492.tsv"  --output_dir "hybrid_v6_2492" --n_growth_features 250 --n_temp_features 400 --seed 42 --lr 0.001 --batch_size 64 --patience 30 --epochs 250 --l1_lambda 5e-6 --weight_decay 1e-6 --mse_l1_ratio 0.9 --hidden_dims "512,512,384,256,128" --dropout_rates "0.3,0.4,0.4,0.5,0.5" --activation "relu" --use_batch_norm --use_residual --use_gradient_clipping --save_checkpoints --verbose

# Performance Metrics for 5041 genomes 
# Growth Rate Train Metrics - R²: 0.0042, RMSE: 12.0233, MAE: 0.3151
# Growth Rate Val Metrics - R²: 0.8458, RMSE: 0.3189, MAE: 0.1412
# Growth Rate Test Metrics - R²: 0.9001, RMSE: 0.2353, MAE: 0.1412
# Temperature Train Metrics - R²: 0.9736, RMSE: 0.2079, MAE: 0.1414
# Temperature Val Metrics - R²: 0.8784, RMSE: 0.4764, MAE: 0.3016
# Temperature Test Metrics - R²: 0.8624, RMSE: 0.4773, MAE: 0.3147
# Final Growth Rate Metrics - R²: 0.9001, RMSE: 0.2353, MAE: 0.1412
# Final Temperature Metrics - R²: 0.8624, RMSE: 0.4773, MAE: 0.3147
# Overall Metrics - R²: 0.8812, RMSE: 0.3563, MAE: 0.2279
# python final_hybrid_model.py  --feature_file "./training_data/combined_features.tsv" --metadata_file "./training_data/metadata.tsv"  --output_dir "hybrid_v6" --n_growth_features 250 --n_temp_features 400 --seed 42 --lr 0.001 --batch_size 64 --patience 30 --epochs 250 --l1_lambda 5e-6 --weight_decay 1e-6 --mse_l1_ratio 0.9 --hidden_dims "512,512,384,256,128" --dropout_rates "0.3,0.4,0.4,0.5,0.5" --activation "relu" --use_batch_norm --use_residual --use_gradient_clipping --save_checkpoints --verbose



# ------------------------------------------
# Hybrid Models
### Performance Metrics for 2492 genomes
# Neural Network - Overall RMSE: 3.1371, R2: 0.8651
# Growth Rate - RMSE: 1.6895, R2: 0.8287
# Optimal Temperature - RMSE: 4.1023, R2: 0.9015
# Random Forest - Overall RMSE: 3.5424, R2: 0.8567
# Growth Rate - RMSE: 1.6062, R2: 0.8452
# Optimal Temperature - RMSE: 4.7453, R2: 0.8682
# Hybrid (50/50) - Overall RMSE: 3.1045, R2: 0.8754
# Growth Rate - RMSE: 1.5870, R2: 0.8489
# Optimal Temperature - RMSE: 4.0936, R2: 0.9019
# RF-Biased Hybrid (20/80) - Overall RMSE: 3.1884, R2: 0.8729
# Growth Rate - RMSE: 1.5803, R2: 0.8501
# Optimal Temperature - RMSE: 4.2230, R2: 0.8956
# Best model: Hybrid (50/50) with R² = 0.8754
# python hybrid_tabular_model.py --feature_file ./training_data/combined_features_2492.tsv --metadata_file ./training_data/metadata_2492.tsv --n_features 400 --batch_size 64 --epochs 200 --lr 0.001 --dropout 0.2 --output_dir models_hybrid_tab_2492

### Performance Metrics for 5041 genomes
# Neural Network - Overall RMSE: 4.6615, R2: 0.6180
# Growth Rate - RMSE: 4.5441, R2: 0.3840
# Optimal Temperature - RMSE: 4.7760, R2: 0.8521
# Random Forest - Overall RMSE: 4.5993, R2: 0.6304
# Growth Rate - RMSE: 4.4624, R2: 0.4059
# Optimal Temperature - RMSE: 4.7323, R2: 0.8548
# Hybrid (50/50) - Overall RMSE: 4.4692, R2: 0.6369
# Growth Rate - RMSE: 4.4733, R2: 0.4030
# Optimal Temperature - RMSE: 4.4651, R2: 0.8707
# RF-Biased Hybrid (20/80) - Overall RMSE: 4.4736, R2: 0.6376
# Growth Rate - RMSE: 4.4637, R2: 0.4056
# Optimal Temperature - RMSE: 4.4836, R2: 0.8697
# Best model: RF-Biased Hybrid (20/80) with R² = 0.6376
#python hybrid_tabular_model.py --feature_file ./training_data/combined_features.tsv --metadata_file ./training_data/metadata.tsv --n_features 400 --batch_size 64 --epochs 200 --lr 0.001 --dropout 0.2 --output_dir models_hybrid_tab


# ------------------------------------------
# Hybrid model with improved LightGBM
# Final metrics (scaled) - Overall R²: 0.8971
# Final metrics (scaled) - Growth Rate R²: 0.9194
# Final metrics (scaled) - Temperature R²: 0.8748
# Final metrics (original) - Overall R²: 0.8657
# Final metrics (original) - Growth Rate R²: 0.8566
# Final metrics (original) - Temperature R²: 0.8748
# python train_hybrid_with_improved_lgb.py --feature_file training_data/combined_features.tsv --metadata_file training_data/metadata.tsv --output_dir models/hybrid_improved_lgb_v3 --n_features 15


# ------------------------------------------
# rf model
# Growth Training MSE: 0.1904, RMSE: 0.4364, R²: 0.9854
# Growth Testing MSE: 26.6594, RMSE: 5.1633, R²: 0.3606
# Temp Training MSE: 2.3089, RMSE: 1.5195, R²: 0.9854
# Temp Testing MSE: 20.8529, RMSE: 4.5665, R²: 0.8469
# python improved_rf_model.py --feature_file "training_data/combined_features.tsv" --metadata_file "training_data/metadata.tsv" --output_dir "models/improved_rf_model"


# ------------------------------------------ 
# regularized rf model
## Growth Rate:
# Training R²: 0.9700 (excellent)
# Testing R²: 0.3685 (poor, but better than the baseline)
# Out-of-bag Score: 0.8693 (good)
# Cross-validation RMSE: 1.3429
# Selected 96 features out of 1559
## Optimal Temperature:
# Training R²: 0.9687 (excellent)
# Testing R²: 0.8415 (very good)
# Out-of-bag Score: 0.8868 (very good)
# Cross-validation RMSE: 4.2882
# Selected 79 features out of 1559
# python regularized_rf_model.py --feature_file "training_data/combined_features.tsv" --metadata_file "training_data/metadata.tsv" --output_dir "models/regularized_rf_model" 


# ------------------------------------------
# rf hybrid model
# growth r2: train 0.947, test 0.848 
# temp r2: train 0.982, test 0.869
# ./run_rf_hybrid_model.sh 

# ------------------------------------------
# # growth r2: train 0.9467, test 0.856 
# temp r2: train 0.984, test 0.875
# ./run_rf_hybrid_model_simple.sh


# ------------------------------------------
# # growth r2: train 0.948, test 0.863 
# temp r2: train 0.984, test 0.861
# ./run_rf_hybrid_model_rfe.sh

# ------------------------------------------
# growth Train R²: 1.0000, Test R²: 0.8299
# temp   Train R²: 1.0000, Test R²: 0.8702
# ./run_rf_hybrid_model_bayesian.sh
# 

# ------------------------------------------
# growth test R2: 0.8754, temp R2: 0.8699
# /home/<USER>/anaconda3/lib/python3.12/site-packages/sklearn/utils/validation.py:2739: UserWarning: X does not have valid feature names, but LGBMRegressor was fitted with feature names
#  warnings.warn(
#/home/<USER>/anaconda3/lib/python3.12/site-packages/sklearn/utils/validation.py:2739: UserWarning: X does not have valid feature names, but LGBMRegressor was fitted with feature names
#  warnings.warn(
#
# ./run_stacking_rf_bidirectional.sh


# ------------------------------------------
# baseline model
# Train R²: 0.9310, RMSE: 0.9516
# Val R²: 0.8438, RMSE: 1.4138
# Test R2: 0.3604, RMSE: 5.1640
# ./run_enhanced_rlvr_hyperparam_search.py


# ------------------------------------------
# baseline model
# Train R²: 0.9288, RMSE: 0.9664
# Val   R²: 0.8424, RMSE: 1.4198
# Test  R²: 0.3606, RMSE: 5.1633
# Val R²: 0.8384, Val RMSE: 1.4379
# ./run_enhanced_rlvr_model.sh


# ------------------------------------------
# Tab Transformer
# Growth Train Loss: 0.4663
# Growth Val Loss: 10.0436, RMSE: 3.1692, R2: 0.8538
# python train_tabtransformer.py --feature_file ./training_data/combined_features.tsv --metadata_file ./training_data/metadata.tsv --batch_size 128 --epochs 200 --lr 0.001 --output_dir tab_transformer


# ------------------------------------------
# growth: Train R²: 0.7168, Test R²: 0.6660 
# temp:   Train R²: 0.9807, Test R²: 0.8740 
# python train_stacking_rf_transformer.py --feature-file ./features/combined_features.tsv     --metadata training_data/metadata.tsv     --output-dir models/stacking_hybrid     --metrics-dir metrics/stacking_hybrid     --rf-n-estimators 300     --xgb-n-estimators 300     --lgb-n-estimators 300


# ------------------------------------------
# growth R² score: 0.8312 (83.12% of variance explained), RMSE: 1.5039, MAE: 0.7956
# ./run_tabnet_model.sh  


# ------------------------------------------
# growth_rate - Train: R²=0.9460, RMSE=0.8689
# growth_rate - Val: R²=0.3379, RMSE=5.0185
# temperature - Train: R²=0.9521, RMSE=2.7# temperature - Train: R²=0.9521, RMSE=2.7366
# temperature - Val: R²=0.8929, RMSE=3.9205
# python train_dnn_regularized.py --feature-file ./training_data/combined_features.tsv --metadata ./training_data/metadata.tsv --output-dir models/dnn_regularized --metrics-dir metrics/dnn_regularized --target both --batch-size 64 --epochs 150 --use-batch-norm


# ------------------------------------------
# # Using One-Cycle Learning Rate Schedule
# # growth: Train R2: 0.70, Test R2: 0.82
# temp: train R2: 0.93, Test R2:0.90
# python train_dnn_regularized.py --feature-file ./training_data/combined_features.tsv --hidden-dims 512,512,384,256,128 --dropout-rates 0.3,0.4,0.4,0.5,0.5 --lr-scheduler one_cycle --learning-rate 0.001 --batch-size 64 --use-batch-norm --use-gradient-clipping

# ------------------------------------------
# # Using Cosine Annealing Schedule
# # growth: Train R2: 0.58, Test R2: 0.64
# temp: train R2: 0.91, Test R2:0.80
# python train_dnn_regularized.py --feature-file ./training_data/combined_features.tsv --hidden-dims 512,256,128 --dropout-rates 0.3,0.4,0.5 --lr-scheduler cosine --learning-rate 0.001 --weight-decay 2e-5 --activation leaky_relu


# ------------------------------------------
# growth val r2 0.54
# temp val r2 0.83 
# python train_multi_branch.py --feature-file ./training_data/combined_features.tsv --metadata ./training_data/metadata.tsv --output-dir models/multi_branch  --metrics-dir metrics/multi_branch --batch-size 32 --epochs 100 --hidden-dims 256 128 64 --dropout-rates 0.2 0.3 0.4 --attention-heads 4 --fusion-layers 2


# ------------------------------------------
# train transformer
# growth_rate_r2: 0.6687
# temperature_r2: 0.7715
# python train_transformer.py     --feature-file ./training_data/combined_features.tsv     --metadata ./training_data/metadata.tsv     --output-dir models/transformer     --metrics-dir metrics/transformer     --use-gradient-clipping     --lr-scheduler one_cycle


# ------------------------------------------
# growth r2 0.777
# temp r2 0.859
# python train_transformer.py     --feature-file ./training_data/combined_features.tsv     --metadata ./training_data/metadata.tsv     --output-dir models/transformer     --metrics-dir metrics/transformer     --use-gradient-clipping     --lr-scheduler one_cycle --transformer-layers 4 --transformer-heads 8


# ------------------------------------------
# growth r2 0.695
# temp r2 0.783
# python train_transformer.py     --feature-file ./training_data/combined_features.tsv     --metadata ./training_data/metadata.tsv     --output-dir models/transformer     --metrics-dir metrics/transformer     --use-gradient-clipping     --lr-scheduler one_cycle --transformer-layers 8 --transformer-heads 16
# train combined CNN-Transformer

# ------------------------------------------
# growth r2 0.846
# temp r2 0.846
# python train_combined_model.py --feature-file ./training_data/combined_features.tsv --metadata training_data/metadata.tsv --output-dir combined_models --metrics-dir combined_metrics


# ------------------------------------------
# train improved rf with increased Regularization
# r2: train 0.764, val 0.876 
# python train_improved_rf.py   --feature-file ./training_data/combined_features.tsv   --metadata training_data/metadata.tsv   --output-dir rf_models_regularized   --max-depth 15   --min-samples-split 10   --min-samples-leaf 5

# ------------------------------------------
# growth train r2 0.84
# growth validation r2 0.86
# python train_improved_rf.py --feature-file ./training_data/combined_features.tsv --metadata training_data/metadata.tsv  --output-dir rf_models --metrics-dir rf_metrics --n-estimators 500 --max-depth 20 --min-samples-split 5 --min-samples-leaf 2

# ------------------------------------------
# growth train r2 0.85
# growth test r2 0.73
# python train_rf_kfold.py --feature-file ./training_data/combined_features.tsv --metadata training_data/metadata.tsv --output-dir rf_kfold_models --metrics-dir rf_kfold_metrics --target growth_rate --n-estimators 200 --max-depth 15 --min-samples-split 5 --min-samples-leaf 2 --n-folds 5

# ------------------------------------------
# stacking rf transformer
#
#
# python train_stacking_rf_transformer.py --feature-file ./features/combined_features.tsv --metadata training_data/metadata.tsv --output-dir models/stacking_hybrid --metrics-dir metrics/stacking_hybrid


# ------------------------------------------
# train ensemble 
:<<'COMMENT'
python train_ensemble_model.py \
  --feature-file training_data/combined_features.tsv \
  --metadata training_data/metadata.tsv \
  --transformer-model pretrained_models/transformer_model.pt \
  --output-dir results/ensemble_model \
  --metrics-dir results/ensemble_model/metrics \
  --rf-n-estimators 200 \
  --rf-max-depth 20 \
  --transformer-hidden-dims 256 128 64 \
  --transformer-dropout-rates 0.2 0.3 0.4 \
  --transformer-heads 4 \
  --transformer-layers 2 \
  --test-size 0.2 \
  --random-state 42
COMMENT
