#!/bin/bash
# Run script for training the TabNet model with improved parameters

# Set environment variables
export PYTHONPATH=.:${PYTHONPATH}

# Create necessary directories
mkdir -p models/tabnet
mkdir -p logs
mkdir -p metrics/tabnet

# Set data paths
FEATURE_FILE="training_data/combined_features.tsv"
METADATA="training_data/metadata.tsv"
OUTPUT_DIR="models/tabnet"
METRICS_DIR="metrics/tabnet"

# Use the train_tabnet_model.py script which supports combined_features.tsv and metadata.tsv
echo "Training TabNet model..."
python train_tabnet_model.py \
    --feature-file "${FEATURE_FILE}" \
    --metadata "${METADATA}" \
    --output-dir "${OUTPUT_DIR}" \
    --metrics-dir "${METRICS_DIR}" \
    --n-steps 3 \
    --hidden-dim 64 \
    --n-shared-glu 2 \
    --n-independent-glu 2 \
    --virtual-batch-size 128 \
    --gamma 1.3 \
    --mask-type "sparsemax" \
    --loss-fn "huber" \
    --lr 0.01 \
    --weight-decay 1e-5 \
    --batch-size 64 \
    --epochs 100 \
    --early-stopping-patience 10 \
    --feature-selection \
    --n-features 50 \
    --seed 42

echo "Training complete!"

:<<'COMMENT'
TabNet architecture advantages with these improvements:

1. Enhanced Interpretability: 
   - Actual sparsemax implementation for better feature selection
   - Gamma parameter (1.3) controls feature reuse between decision steps
   - Feature importance visualization capabilities

2. Improved Architecture:
   - Optimized feature dimension sizes
   - Dropout layers prevent overfitting
   - Better balance between shared and independent GLU layers

3. Better Training Dynamics:
   - Appropriate learning rate for stable training
   - Feature selection for improved model focus
   - Early stopping to prevent overfitting

4. Feature Selection:
   - Uses top 50 most important features
   - Better handling of feature interactions via proper masking

5. Loss Function Optimizations:
   - Huber loss for robustness to outliers
   - Weight decay for regularization

These improvements address the limitations in the original implementation
and should result in significantly better model performance.

COMMENT
