#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Hybrid Model with Random Forest Ensemble for Temperature Prediction.

This module integrates the Random Forest Ensemble Temperature model with
the existing growth rate prediction model to create a complete hybrid model.
"""

import os
import numpy as np
import pandas as pd
import torch
import joblib
import logging
from typing import Dict, Tuple, Union, List, Optional
import time
import argparse
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import RobustScaler

# Import the RF ensemble temperature model
from rf_ensemble_temp_model import RFEnsembleTemperatureModel

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class HybridRFEnsembleModel:
    """
    Hybrid model integrating the RF ensemble temperature model with the
    existing growth rate prediction model.
    """
    
    def __init__(self, output_dir: str = "models/hybrid_rf_ensemble"):
        """Initialize the hybrid model.
        
        Args:
            output_dir: Directory to save model outputs
        """
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        
        # Set up logging to file
        file_handler = logging.FileHandler(os.path.join(output_dir, "training.log"))
        file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
        logger.addHandler(file_handler)
        
        # Initialize model components
        self.growth_model = None
        self.temp_model = RFEnsembleTemperatureModel(output_dir=os.path.join(output_dir, "temp_model"))
        
        # Track training status
        self.is_fitted = False
    
    def load_data(self, feature_file: str, metadata_file: str, 
                 split_ratio: float = 0.2) -> Dict:
        """Load and prepare data for both model components."""
        logger.info(f"Loading features from {feature_file}")
        features = pd.read_csv(feature_file, sep='\t', index_col='genome_id')
        
        logger.info(f"Loading metadata from {metadata_file}")
        metadata = pd.read_csv(metadata_file, sep='\t', index_col='genome_id')
        
        # Get common samples
        common_indices = features.index.intersection(metadata.index)
        logger.info(f"Found {len(common_indices)} common samples")
        
        features = features.loc[common_indices]
        metadata = metadata.loc[common_indices]
        
        # Handle missing values
        features = features.fillna(0)
        
        # Prepare target variables
        growth_rates = metadata['growth_rate']
        temperatures = metadata['optimal_temperature']
        
        # Split data
        X_train, X_test, y_growth_train, y_growth_test, y_temp_train, y_temp_test = train_test_split(
            features, growth_rates, temperatures, 
            test_size=split_ratio, random_state=42
        )
        
        # Further split test into validation and test
        X_val, X_test, y_growth_val, y_growth_test, y_temp_val, y_temp_test = train_test_split(
            X_test, y_growth_test, y_temp_test, 
            test_size=0.5, random_state=42
        )
        
        logger.info(f"Training set: {X_train.shape[0]} samples")
        logger.info(f"Validation set: {X_val.shape[0]} samples")
        logger.info(f"Test set: {X_test.shape[0]} samples")
        
        # Prepare data for each model
        data = {
            "growth": {
                "X_train": X_train,
                "X_val": X_val,
                "X_test": X_test,
                "y_train": y_growth_train,
                "y_val": y_growth_val,
                "y_test": y_growth_test
            },
            "temp": {
                "X_train": X_train,
                "X_val": X_val,
                "X_test": X_test,
                "y_train": y_temp_train,
                "y_val": y_temp_val,
                "y_test": y_temp_test
            },
            "features": features,
            "metadata": metadata
        }
        
        return data
    
    def load_existing_growth_model(self, model_path: str) -> None:
        """Load an existing trained growth model."""
        logger.info(f"Loading existing growth model from {model_path}")
        
        try:
            self.growth_model = joblib.load(model_path)
            logger.info("Growth model loaded successfully")
        except Exception as e:
            logger.error(f"Error loading growth model: {str(e)}")
            raise
    
    def train(self, data: Dict, optimize_temp_hyperparams: bool = True,
             n_estimators: int = 300, max_depth: int = 20) -> None:
        """Train the hybrid model."""
        start_time = time.time()
        logger.info("Training hybrid model with RF ensemble for temperature")
        
        # Check if growth model is loaded
        if self.growth_model is None:
            logger.warning("Growth model not loaded, only temperature model will be trained")
        
        # Train temperature model
        logger.info("Training RF ensemble temperature model")
        
        # Prepare temperature training data
        temp_data = self.temp_model.prepare_training_data(
            data["temp"]["X_train"],
            data["temp"]["y_train"]
        )
        
        # Train the RF ensemble temperature model
        self.temp_model.train_models(
            temp_data,
            optimize_hp=optimize_temp_hyperparams,
            n_estimators=n_estimators,
            max_depth=max_depth,
            feature_selection=True
        )
        
        # Evaluate temperature model
        temp_metrics = self.temp_model.evaluate(
            data["temp"]["X_train"],
            data["temp"]["y_train"],
            data["temp"]["X_val"],
            data["temp"]["y_val"]
        )
        
        logger.info(f"Temperature model training completed in {time.time() - start_time:.2f} seconds")
        
        # Set fitted flag
        self.is_fitted = True
        
        # Save the model
        self.save()
    
    def evaluate(self, data: Dict) -> Dict:
        """Evaluate the complete hybrid model."""
        if not self.is_fitted:
            raise ValueError("Model not trained yet")
        
        logger.info("Evaluating hybrid model")
        
        results = {}
        
        # Evaluate temperature model on test set
        if hasattr(self.temp_model, 'is_trained') and self.temp_model.is_trained:
            logger.info("Evaluating temperature model on test set")
            
            test_temp_preds = self.temp_model.predict(data["temp"]["X_test"])
            test_temp_r2 = r2_score(data["temp"]["y_test"], test_temp_preds)
            test_temp_rmse = np.sqrt(mean_squared_error(data["temp"]["y_test"], test_temp_preds))
            test_temp_mae = mean_absolute_error(data["temp"]["y_test"], test_temp_preds)
            
            logger.info(f"Temperature Test R²: {test_temp_r2:.6f}")
            logger.info(f"Temperature Test RMSE: {test_temp_rmse:.4f}")
            logger.info(f"Temperature Test MAE: {test_temp_mae:.4f}")
            
            results["temperature"] = {
                "r2": test_temp_r2,
                "rmse": test_temp_rmse,
                "mae": test_temp_mae,
                "predictions": test_temp_preds,
                "actual": data["temp"]["y_test"]
            }
        
        # Evaluate growth model on test set if available
        if self.growth_model is not None:
            logger.info("Evaluating growth model on test set")
            
            test_growth_preds = self.growth_model.predict(data["growth"]["X_test"])
            test_growth_r2 = r2_score(data["growth"]["y_test"], test_growth_preds)
            test_growth_rmse = np.sqrt(mean_squared_error(data["growth"]["y_test"], test_growth_preds))
            test_growth_mae = mean_absolute_error(data["growth"]["y_test"], test_growth_preds)
            
            logger.info(f"Growth Test R²: {test_growth_r2:.6f}")
            logger.info(f"Growth Test RMSE: {test_growth_rmse:.6f}")
            logger.info(f"Growth Test MAE: {test_growth_mae:.6f}")
            
            results["growth"] = {
                "r2": test_growth_r2,
                "rmse": test_growth_rmse,
                "mae": test_growth_mae,
                "predictions": test_growth_preds,
                "actual": data["growth"]["y_test"]
            }
        
        # Save results
        joblib.dump(results, os.path.join(self.output_dir, "test_results.joblib"))
        
        return results
    
    def predict(self, features: pd.DataFrame) -> Dict:
        """Make predictions with the hybrid model."""
        if not self.is_fitted:
            raise ValueError("Model not trained yet")
        
        logger.info(f"Making predictions for {len(features)} samples")
        
        results = {}
        
        # Temperature predictions
        if hasattr(self.temp_model, 'is_trained') and self.temp_model.is_trained:
            logger.info("Making temperature predictions")
            temp_preds = self.temp_model.predict(features)
            results["optimal_temperature"] = temp_preds
        
        # Growth predictions if available
        if self.growth_model is not None:
            logger.info("Making growth rate predictions")
            growth_preds = self.growth_model.predict(features)
            results["growth_rate"] = growth_preds
        
        return results
    
    def save(self) -> None:
        """Save the hybrid model."""
        logger.info(f"Saving hybrid model to {self.output_dir}")
        
        # Save temperature model
        if hasattr(self.temp_model, 'is_trained') and self.temp_model.is_trained:
            self.temp_model.save()
        
        # Save growth model if available
        if self.growth_model is not None:
            joblib.dump(self.growth_model, os.path.join(self.output_dir, "growth_model.joblib"))
        
        # Save model configuration
        config = {
            "has_temp_model": hasattr(self.temp_model, 'is_trained') and self.temp_model.is_trained,
            "has_growth_model": self.growth_model is not None,
            "output_dir": self.output_dir
        }
        
        joblib.dump(config, os.path.join(self.output_dir, "model_config.joblib"))
        
        logger.info("Hybrid model saved successfully")
    
    def load(self, model_dir: str) -> None:
        """Load a trained hybrid model."""
        logger.info(f"Loading hybrid model from {model_dir}")
        
        # Load configuration
        config_path = os.path.join(model_dir, "model_config.joblib")
        if os.path.exists(config_path):
            config = joblib.load(config_path)
            logger.info(f"Model configuration: {config}")
        else:
            logger.warning("Model configuration not found")
            config = {"has_temp_model": True, "has_growth_model": True}
        
        # Update output directory
        self.output_dir = model_dir
        
        # Load temperature model
        if config.get("has_temp_model", True):
            temp_model_dir = os.path.join(model_dir, "temp_model")
            self.temp_model = RFEnsembleTemperatureModel(output_dir=temp_model_dir)
            
            try:
                self.temp_model.load(temp_model_dir)
                logger.info("Temperature model loaded successfully")
            except Exception as e:
                logger.error(f"Error loading temperature model: {str(e)}")
        
        # Load growth model if available
        if config.get("has_growth_model", True):
            growth_model_path = os.path.join(model_dir, "growth_model.joblib")
            
            if os.path.exists(growth_model_path):
                try:
                    self.growth_model = joblib.load(growth_model_path)
                    logger.info("Growth model loaded successfully")
                except Exception as e:
                    logger.error(f"Error loading growth model: {str(e)}")
            else:
                logger.warning("Growth model not found")
        
        # Set fitted flag based on temperature model
        self.is_fitted = hasattr(self.temp_model, 'is_trained') and self.temp_model.is_trained
        
        logger.info(f"Hybrid model loaded. Is fitted: {self.is_fitted}")

def main():
    """Main function to train and evaluate the hybrid model."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Train and evaluate hybrid model with RF ensemble for temperature")
    parser.add_argument("--features", type=str, required=True, help="Path to feature file")
    parser.add_argument("--metadata", type=str, required=True, help="Path to metadata file")
    parser.add_argument("--output_dir", type=str, default="models/hybrid_rf_ensemble", help="Output directory")
    parser.add_argument("--growth_model", type=str, default=None, help="Path to existing growth model")
    parser.add_argument("--optimize_hp", action="store_true", help="Optimize hyperparameters")
    parser.add_argument("--n_estimators", type=int, default=300, help="Number of estimators for RF")
    parser.add_argument("--max_depth", type=int, default=20, help="Max depth for RF")
    
    args = parser.parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Initialize model
    hybrid_model = HybridRFEnsembleModel(output_dir=args.output_dir)
    
    # Load data
    data = hybrid_model.load_data(args.features, args.metadata)
    
    # Load existing growth model if specified
    if args.growth_model is not None:
        hybrid_model.load_existing_growth_model(args.growth_model)
    
    # Train model
    hybrid_model.train(
        data,
        optimize_temp_hyperparams=args.optimize_hp,
        n_estimators=args.n_estimators,
        max_depth=args.max_depth
    )
    
    # Evaluate model
    results = hybrid_model.evaluate(data)
    
    # Log final results
    if "temperature" in results:
        logger.info(f"Final Temperature Test R²: {results['temperature']['r2']:.6f}")
    
    if "growth" in results:
        logger.info(f"Final Growth Test R²: {results['growth']['r2']:.6f}")
    
    logger.info(f"Model training and evaluation completed successfully. Results saved in {args.output_dir}")

if __name__ == "__main__":
    main() 