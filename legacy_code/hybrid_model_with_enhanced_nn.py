#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Hybrid Model with Enhanced Neural Network for Temperature Prediction.
This module provides a hybrid model that combines ensemble methods for growth rate prediction
with an enhanced neural network for temperature prediction.
"""

import os
import numpy as np
import pandas as pd
import logging
from typing import Dict, Tuple, Optional, List
import joblib
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from sklearn.ensemble import RandomForestRegressor
import xgboost as xgb
import lightgbm as lgb

from improved_lightgbm_component import ImprovedLightGBMComponent

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FeatureDataset(Dataset):
    """Dataset for tabular features."""
    def __init__(self, features, targets):
        self.features = torch.tensor(features, dtype=torch.float32)
        self.targets = torch.tensor(targets, dtype=torch.float32)
    
    def __len__(self):
        return len(self.features)
    
    def __getitem__(self, idx):
        return self.features[idx], self.targets[idx]

class RegularizedDNN(nn.Module):
    """
    Regularized Deep Neural Network with batch normalization, dropout, and gradient clipping.
    """
    def __init__(self, input_dim, hidden_dims=[512, 512, 384, 256, 128], dropout_rates=[0.3, 0.4, 0.4, 0.5, 0.5], 
                 use_batch_norm=True, activation='relu', use_residual=False):
        super(RegularizedDNN, self).__init__()
        
        # Create layers
        layers = []
        prev_dim = input_dim
        
        # Choose activation function
        if activation == 'relu':
            act_fn = nn.ReLU()
        elif activation == 'leaky_relu':
            act_fn = nn.LeakyReLU(0.1)
        elif activation == 'elu':
            act_fn = nn.ELU()
        else:
            act_fn = nn.ReLU()
        
        # Store dimensions for residual connections
        self.layer_dims = [input_dim] + hidden_dims
        self.use_residual = use_residual
        self.residual_layers = {}
        
        # Build network with progressively increasing dropout
        for i, hidden_dim in enumerate(hidden_dims):
            # Linear layer
            layers.append(nn.Linear(prev_dim, hidden_dim))
            
            # Batch normalization (before activation)
            if use_batch_norm:
                layers.append(nn.BatchNorm1d(hidden_dim))
            
            # Activation
            layers.append(act_fn)
            
            # Dropout with increasing rate for deeper layers
            dropout_rate = dropout_rates[min(i, len(dropout_rates)-1)]
            layers.append(nn.Dropout(dropout_rate))
            
            # If using residual connections and dimensions match
            if use_residual and i > 0 and hidden_dims[i-1] == hidden_dim:
                # Store the index of this layer for residual connections
                self.residual_layers[i] = len(layers) - 1
            
            prev_dim = hidden_dim
        
        self.feature_extractor = nn.Sequential(*layers)
        
        # Output layer with no activation or dropout
        self.output_layer = nn.Linear(prev_dim, 1)
        
        # Initialize weights with improved method
        self._init_weights()
    
    def _init_weights(self):
        """Initialize weights with improved initialization."""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                # Use Xavier/Glorot initialization for better stability
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
    
    def forward(self, x):
        features = self.feature_extractor(x)
        output = self.output_layer(features)
        return output.squeeze()

class HybridModelWithEnhancedNN:
    """
    Hybrid Model that combines ensemble methods for growth rate prediction
    with a specialized RegularizedDNN for temperature prediction.
    """
    def __init__(
        self,
        # Growth rate ensemble parameters
        growth_ensemble_weight_lr: float = 0.01,
        growth_ensemble_weight_epochs: int = 50,
        # Temperature neural network parameters
        temp_nn_hidden_dims: list = [512, 512, 384, 256, 128],
        temp_nn_dropout_rates: list = [0.3, 0.4, 0.4, 0.5, 0.5],
        temp_nn_lr: float = 0.001,
        temp_nn_batch_size: int = 64,
        temp_nn_epochs: int = 100,
        temp_nn_patience: int = 15,
        # General parameters
        variance_percentile: int = 25
    ):
        """
        Initialize hybrid model with enhanced neural network.
        
        Args:
            growth_ensemble_weight_lr: Learning rate for growth rate ensemble weight optimization
            growth_ensemble_weight_epochs: Number of epochs for growth rate ensemble weight optimization
            temp_nn_hidden_dims: List of hidden layer dimensions for temperature neural network
            temp_nn_dropout_rates: List of dropout rates for temperature neural network
            temp_nn_lr: Learning rate for temperature neural network
            temp_nn_batch_size: Batch size for temperature neural network training
            temp_nn_epochs: Maximum number of epochs for temperature neural network
            temp_nn_patience: Patience for early stopping in temperature neural network
            variance_percentile: Percentile threshold for variance filtering
        """
        self.growth_ensemble_weight_lr = growth_ensemble_weight_lr
        self.growth_ensemble_weight_epochs = growth_ensemble_weight_epochs
        self.temp_nn_hidden_dims = temp_nn_hidden_dims
        self.temp_nn_dropout_rates = temp_nn_dropout_rates
        self.temp_nn_lr = temp_nn_lr
        self.temp_nn_batch_size = temp_nn_batch_size
        self.temp_nn_epochs = temp_nn_epochs
        self.temp_nn_patience = temp_nn_patience
        self.variance_percentile = variance_percentile
        
        # Initialize growth rate ensemble models
        self.growth_rf = RandomForestRegressor(
            n_estimators=200,
            max_depth=15,
            min_samples_split=5,
            min_samples_leaf=2,
            max_features='sqrt',
            bootstrap=True,
            n_jobs=-1,
            random_state=42
        )
        
        self.growth_xgb = xgb.XGBRegressor(
            n_estimators=200,
            max_depth=6,
            learning_rate=0.05,
            subsample=0.8,
            colsample_bytree=0.8,
            reg_alpha=0.01,
            reg_lambda=1.0,
            n_jobs=-1,
            random_state=42
        )
        
        # Initialize improved LightGBM component for growth rate
        self.growth_lgb = ImprovedLightGBMComponent(
            n_estimators=200,
            learning_rate=0.05,
            max_depth=10,
            min_data_in_leaf=20,
            min_gain_to_split=0.1,
            reg_alpha=0.1,
            reg_lambda=1.0,
            subsample=0.8,
            colsample_bytree=0.8,
            random_state=42,
            verbose=-1
        )
        
        # Initialize model for RegularizedDNN
        self.temp_model = None
        
        # Initialize ensemble weights for growth rate
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # Weights for growth rate (RF, XGB, LGB)
        self.growth_weights = nn.Parameter(torch.tensor([0.33, 0.33, 0.34], dtype=torch.float32))
        
        # Initialize feature scalers
        self.feature_scaler = StandardScaler()
        self.temp_feature_scaler = StandardScaler()  # Separate scaler for temperature features
        
    def filter_low_variance_features(self, X: pd.DataFrame, percentile: float = 25) -> pd.DataFrame:
        """
        Filter out low-variance features.
        
        Args:
            X: Feature DataFrame
            percentile: Percentile threshold for variance filtering
            
        Returns:
            Filtered feature DataFrame
        """
        # Calculate variance for each feature
        variances = X.var()
        
        # Determine threshold
        threshold = np.percentile(variances, percentile)
        
        # Select high-variance features
        high_var_cols = variances[variances > threshold].index.tolist()
        
        logger.info(f"Removed {X.shape[1] - len(high_var_cols)} low-variance features (below {percentile}th percentile)")
        
        return X[high_var_cols]
    
    def fit(
        self, 
        X_train: pd.DataFrame, 
        y_growth_train: pd.Series, 
        y_temp_train: pd.Series,
        X_val: pd.DataFrame, 
        y_growth_val: pd.Series, 
        y_temp_val: pd.Series,
        output_dir: Optional[str] = None
    ) -> None:
        """
        Fit hybrid model to data.
        
        Args:
            X_train: Training features
            y_growth_train: Training growth rate targets
            y_temp_train: Training temperature targets
            X_val: Validation features
            y_growth_val: Validation growth rate targets
            y_temp_val: Validation temperature targets
            output_dir: Directory to save outputs
        """
        # Step 1: Apply variance filtering for growth rate models
        X_train_filtered = self.filter_low_variance_features(X_train, self.variance_percentile)
        X_val_filtered = X_val[X_train_filtered.columns]
        
        # Step 2: Scale features for growth rate models
        X_train_scaled = self.feature_scaler.fit_transform(X_train_filtered)
        X_val_scaled = self.feature_scaler.transform(X_val_filtered)
        
        # Convert back to DataFrame
        X_train_scaled_df = pd.DataFrame(
            X_train_scaled, 
            columns=X_train_filtered.columns,
            index=X_train_filtered.index
        )
        
        X_val_scaled_df = pd.DataFrame(
            X_val_scaled, 
            columns=X_val_filtered.columns,
            index=X_val_filtered.index
        )
        
        # Step 3: Train growth rate ensemble models
        logger.info("Training growth rate ensemble models...")
        
        # Train Random Forest for growth rate
        logger.info("Training Random Forest for growth rate...")
        self.growth_rf.fit(X_train_scaled, y_growth_train)
        
        # Train XGBoost for growth rate
        logger.info("Training XGBoost for growth rate...")
        self.growth_xgb.fit(X_train_scaled, y_growth_train)
        
        # Train LightGBM for growth rate
        logger.info("Training LightGBM for growth rate...")
        # Create dummy temperature targets (not used, but required by the API)
        dummy_temp = pd.Series(np.zeros(len(y_growth_train)), index=y_growth_train.index)
        dummy_temp_val = pd.Series(np.zeros(len(y_growth_val)), index=y_growth_val.index)
        
        self.growth_lgb.fit(
            X_train_scaled_df, 
            y_growth_train, 
            dummy_temp,
            X_val_scaled_df, 
            y_growth_val, 
            dummy_temp_val
        )
        
        # Step 4: Optimize ensemble weights for growth rate
        logger.info("Optimizing ensemble weights for growth rate...")
        self._optimize_growth_ensemble_weights(
            X_val_scaled_df, 
            y_growth_val
        )
        
        # Step 5: For temperature prediction, we use all features without variance filtering
        # Scale features for temperature model separately
        X_train_temp_scaled = self.temp_feature_scaler.fit_transform(X_train)
        X_val_temp_scaled = self.temp_feature_scaler.transform(X_val)
        
        # Step 6: Train the RegularizedDNN model for temperature
        logger.info("Training RegularizedDNN for temperature prediction...")
        self._train_temperature_model(
            X_train_temp_scaled,
            y_temp_train.values,
            X_val_temp_scaled,
            y_temp_val.values,
            output_dir
        )
        
        # Save model if output_dir provided
        if output_dir:
            self.save(output_dir)
            
    def _train_temperature_model(
        self,
        X_train_scaled: np.ndarray,
        y_temp_train: np.ndarray,
        X_val_scaled: np.ndarray,
        y_temp_val: np.ndarray,
        output_dir: Optional[str] = None
    ) -> None:
        """
        Train the RegularizedDNN model for temperature prediction.
        
        Args:
            X_train_scaled: Scaled training features
            y_temp_train: Training temperature targets
            X_val_scaled: Scaled validation features
            y_temp_val: Validation temperature targets
            output_dir: Directory to save outputs
        """
        # Create datasets and dataloaders
        train_dataset = FeatureDataset(X_train_scaled, y_temp_train)
        val_dataset = FeatureDataset(X_val_scaled, y_temp_val)
        
        # Use a larger batch size and more epochs for better results
        train_loader = DataLoader(train_dataset, batch_size=self.temp_nn_batch_size, shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size=self.temp_nn_batch_size, shuffle=False)
        
        # Initialize model with the optimal configuration
        input_dim = X_train_scaled.shape[1]
        self.temp_model = RegularizedDNN(
            input_dim=input_dim,
            hidden_dims=self.temp_nn_hidden_dims,
            dropout_rates=self.temp_nn_dropout_rates,
            use_batch_norm=True,
            activation='leaky_relu',  # Use leaky ReLU for better gradient flow
            use_residual=True  # Enable residual connections
        ).to(self.device)
        
        # Initialize optimizer with AdamW for better weight decay handling
        optimizer = optim.AdamW(self.temp_model.parameters(), lr=self.temp_nn_lr, weight_decay=1e-5)
        
        # Initialize learning rate scheduler with OneCycleLR
        scheduler = optim.lr_scheduler.OneCycleLR(
            optimizer,
            max_lr=self.temp_nn_lr * 10,  # 10x higher peak LR
            epochs=self.temp_nn_epochs,
            steps_per_epoch=len(train_loader),
            pct_start=0.3,  # 30% warmup
            anneal_strategy='cos',
            div_factor=25.0,
            final_div_factor=10000.0
        )
        
        # Initialize loss function
        criterion = nn.MSELoss()
        
        # Add L1 regularization parameter
        l1_lambda = 1e-5
        
        # Early stopping variables
        best_val_loss = float('inf')
        patience_counter = 0
        best_model_state = None
        
        # Training loop
        logger.info(f"Beginning temperature model training for {self.temp_nn_epochs} epochs...")
        
        for epoch in range(self.temp_nn_epochs):
            # Training phase
            self.temp_model.train()
            train_loss = 0.0
            
            for features, targets in train_loader:
                features, targets = features.to(self.device), targets.to(self.device)
                
                # Zero gradients
                optimizer.zero_grad()
                
                # Forward pass
                outputs = self.temp_model(features)
                
                # Calculate loss with L1 regularization
                loss = criterion(outputs, targets)
                if l1_lambda > 0:
                    l1_norm = sum(p.abs().sum() for p in self.temp_model.parameters())
                    loss += l1_lambda * l1_norm
                
                # Backward pass
                loss.backward()
                
                # Gradient clipping with more aggressive norm
                torch.nn.utils.clip_grad_norm_(self.temp_model.parameters(), 0.5)
                
                # Update weights
                optimizer.step()
                
                # Update learning rate
                scheduler.step()
                
                # Add batch loss
                train_loss += loss.item() * features.size(0)
            
            # Calculate average loss
            train_loss /= len(train_loader.dataset)
            
            # Validation phase
            self.temp_model.eval()
            val_loss = 0.0
            predictions = []
            actuals = []
            
            with torch.no_grad():
                for features, targets in val_loader:
                    features, targets = features.to(self.device), targets.to(self.device)
                    
                    # Forward pass
                    outputs = self.temp_model(features)
                    
                    # Calculate loss
                    loss = criterion(outputs, targets)
                    
                    # Add batch loss
                    val_loss += loss.item() * features.size(0)
                    
                    # Store predictions and targets for metrics
                    predictions.extend(outputs.cpu().numpy())
                    actuals.extend(targets.cpu().numpy())
            
            # Calculate average loss and metrics
            val_loss /= len(val_loader.dataset)
            
            # Calculate metrics
            val_rmse = np.sqrt(mean_squared_error(actuals, predictions))
            val_r2 = r2_score(actuals, predictions)
            val_mae = mean_absolute_error(actuals, predictions)
            
            # Log metrics
            if (epoch + 1) % 10 == 0 or epoch == 0:
                logger.info(f"Epoch {epoch+1}/{self.temp_nn_epochs} - "
                           f"Train Loss: {train_loss:.4f}, "
                           f"Val Loss: {val_loss:.4f}, "
                           f"Val RMSE: {val_rmse:.4f}, "
                           f"Val R²: {val_r2:.4f}, "
                           f"Val MAE: {val_mae:.4f}")
            
            # Early stopping - store best model with a higher patience
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                best_model_state = self.temp_model.state_dict().copy()
                patience_counter = 0
            else:
                patience_counter += 1
                
            if patience_counter >= self.temp_nn_patience:
                logger.info(f"Early stopping at epoch {epoch+1}")
                break
        
        # Load best model
        if best_model_state is not None:
            self.temp_model.load_state_dict(best_model_state)
            logger.info(f"Loaded best model with validation loss: {best_val_loss:.4f}")
        
        # Calculate final metrics
        self.temp_model.eval()
        predictions = []
        actuals = []
        
        with torch.no_grad():
            for features, targets in val_loader:
                features, targets = features.to(self.device), targets.to(self.device)
                outputs = self.temp_model(features)
                predictions.extend(outputs.cpu().numpy())
                actuals.extend(targets.cpu().numpy())
        
        final_rmse = np.sqrt(mean_squared_error(actuals, predictions))
        final_r2 = r2_score(actuals, predictions)
        final_mae = mean_absolute_error(actuals, predictions)
        
        logger.info(f"Final Temperature Model - "
                   f"Val RMSE: {final_rmse:.4f}, "
                   f"Val R²: {final_r2:.4f}, "
                   f"Val MAE: {final_mae:.4f}")
    
    def _optimize_growth_ensemble_weights(
        self, 
        X_val: pd.DataFrame, 
        y_growth_val: pd.Series
    ) -> None:
        """
        Optimize ensemble weights for growth rate using validation data.
        
        Args:
            X_val: Validation features
            y_growth_val: Validation growth rate targets
        """
        # Get predictions from all models
        # Random Forest predictions
        rf_growth_pred = self.growth_rf.predict(X_val.values)
        
        # XGBoost predictions
        xgb_growth_pred = self.growth_xgb.predict(X_val.values)
        
        # LightGBM predictions
        lgb_growth_pred, _ = self.growth_lgb.predict(X_val)
        
        # Convert to tensors
        rf_growth_tensor = torch.tensor(rf_growth_pred, dtype=torch.float32).to(self.device)
        xgb_growth_tensor = torch.tensor(xgb_growth_pred, dtype=torch.float32).to(self.device)
        lgb_growth_tensor = torch.tensor(lgb_growth_pred, dtype=torch.float32).to(self.device)
        
        y_growth_val_tensor = torch.tensor(y_growth_val.values, dtype=torch.float32).to(self.device)
        
        # Initialize weights parameters
        growth_weights = nn.Parameter(torch.tensor([0.33, 0.33, 0.34], dtype=torch.float32).to(self.device))
        
        # Initialize optimizer
        optimizer = optim.Adam([growth_weights], lr=self.growth_ensemble_weight_lr)
        
        # Initialize loss function
        criterion = nn.MSELoss()
        
        # Training loop
        best_loss = float('inf')
        best_growth_weights = growth_weights.clone()
        
        for epoch in range(self.growth_ensemble_weight_epochs):
            # Zero gradients
            optimizer.zero_grad()
            
            # Apply softmax to ensure weights sum to 1
            normalized_growth_weights = torch.softmax(growth_weights, dim=0)
            
            # Combine predictions for growth rate
            combined_growth_pred = (
                normalized_growth_weights[0] * rf_growth_tensor +
                normalized_growth_weights[1] * xgb_growth_tensor +
                normalized_growth_weights[2] * lgb_growth_tensor
            )
            
            # Calculate loss
            loss = criterion(combined_growth_pred, y_growth_val_tensor)
            
            # Backward pass and optimization
            loss.backward()
            optimizer.step()
            
            # Track best weights
            if loss.item() < best_loss:
                best_loss = loss.item()
                best_growth_weights = normalized_growth_weights.clone().detach()
            
            if (epoch + 1) % 10 == 0:
                logger.info(f"Epoch {epoch+1}/{self.growth_ensemble_weight_epochs}: Loss: {loss.item():.6f}")
                logger.info(f"Growth weights: {normalized_growth_weights.cpu().detach().numpy()}")
        
        # Set final weights
        self.growth_weights = best_growth_weights
        
        logger.info(f"Final growth weights: {self.growth_weights.cpu().numpy()}")
        
        # Calculate R² with optimized weights
        combined_growth_pred = (
            self.growth_weights[0].item() * rf_growth_pred + 
            self.growth_weights[1].item() * xgb_growth_pred +
            self.growth_weights[2].item() * lgb_growth_pred
        )
        
        growth_r2 = r2_score(y_growth_val, combined_growth_pred)
        
        logger.info(f"Validation R² with optimized weights - Growth: {growth_r2:.4f}")
    
    def predict(self, X: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """
        Generate predictions for both growth rate and temperature.
        
        Args:
            X: Features
            
        Returns:
            Tuple of (growth_rate_predictions, temperature_predictions)
        """
        # Scale features for growth rate
        X_filtered = X[self.feature_scaler.feature_names_in_]
        X_scaled = self.feature_scaler.transform(X_filtered)
        
        # Convert to DataFrame for LightGBM
        X_scaled_df = pd.DataFrame(
            X_scaled, 
            columns=self.feature_scaler.feature_names_in_,
            index=X.index
        )
        
        # Get predictions from ensemble models for growth rate
        growth_rf_pred = self.growth_rf.predict(X_scaled)
        growth_xgb_pred = self.growth_xgb.predict(X_scaled)
        growth_lgb_pred = self.growth_lgb.predict_growth(X_scaled_df)
        
        # Combine predictions with learned weights
        weights = torch.softmax(self.growth_weights, dim=0).detach().cpu().numpy()
        
        combined_growth_pred = (
            weights[0] * growth_rf_pred +
            weights[1] * growth_xgb_pred +
            weights[2] * growth_lgb_pred
        )
        
        # Get temperature predictions from RegularizedDNN
        temp_pred = self.predict_temperature(X)
        
        return combined_growth_pred, temp_pred
    
    def predict_growth(self, X: pd.DataFrame) -> np.ndarray:
        """
        Generate growth rate predictions only.
        
        Args:
            X: Features
            
        Returns:
            Growth rate predictions
        """
        growth_pred, _ = self.predict(X)
        return growth_pred
    
    def predict_temperature(self, X: pd.DataFrame) -> np.ndarray:
        """
        Predict temperature using the RegularizedDNN model.
        
        Args:
            X: Feature matrix
            
        Returns:
            Array of temperature predictions
        """
        # Scale features
        X_scaled = self.temp_feature_scaler.transform(X)
        
        # Convert to tensor
        X_tensor = torch.tensor(X_scaled, dtype=torch.float32).to(self.device)
        
        # Make prediction
        self.temp_model.eval()
        with torch.no_grad():
            temp_pred = self.temp_model(X_tensor).cpu().numpy()
            
        return temp_pred
    
    def evaluate(
        self, 
        X: pd.DataFrame, 
        y_growth: pd.Series, 
        y_temp: pd.Series
    ) -> Dict[str, Dict[str, float]]:
        """
        Evaluate model on data.
        
        Args:
            X: Features
            y_growth: Growth rate targets
            y_temp: Temperature targets
            
        Returns:
            Dictionary of evaluation metrics for each task
        """
        # Generate predictions
        y_growth_pred, y_temp_pred = self.predict(X)
        
        # Calculate metrics for growth rate
        growth_metrics = {
            'R2': r2_score(y_growth, y_growth_pred),
            'RMSE': np.sqrt(mean_squared_error(y_growth, y_growth_pred)),
            'MAE': mean_absolute_error(y_growth, y_growth_pred)
        }
        
        # Calculate metrics for temperature
        temp_metrics = {
            'R2': r2_score(y_temp, y_temp_pred),
            'RMSE': np.sqrt(mean_squared_error(y_temp, y_temp_pred)),
            'MAE': mean_absolute_error(y_temp, y_temp_pred)
        }
        
        # Calculate overall metrics (average R2)
        overall_metrics = {
            'R2': (growth_metrics['R2'] + temp_metrics['R2']) / 2,
            'RMSE': (growth_metrics['RMSE'] + temp_metrics['RMSE']) / 2,
            'MAE': (growth_metrics['MAE'] + temp_metrics['MAE']) / 2
        }
        
        # Log metrics
        logger.info(f"Evaluation metrics - Growth Rate: R²={growth_metrics['R2']:.4f}, RMSE={growth_metrics['RMSE']:.4f}, MAE={growth_metrics['MAE']:.4f}")
        logger.info(f"Evaluation metrics - Temperature: R²={temp_metrics['R2']:.4f}, RMSE={temp_metrics['RMSE']:.4f}, MAE={temp_metrics['MAE']:.4f}")
        logger.info(f"Evaluation metrics - Overall: R²={overall_metrics['R2']:.4f}, RMSE={overall_metrics['RMSE']:.4f}, MAE={overall_metrics['MAE']:.4f}")
        
        return {
            'growth_rate': growth_metrics,
            'temperature': temp_metrics,
            'overall': overall_metrics
        }
    
    def save(self, output_dir: str) -> None:
        """
        Save model to disk.
        
        Args:
            output_dir: Directory to save outputs
        """
        logger.info(f"Saving model to {output_dir}")
        os.makedirs(output_dir, exist_ok=True)
        
        # Save growth rate models
        joblib.dump(self.growth_rf, os.path.join(output_dir, 'growth_rf.joblib'))
        joblib.dump(self.growth_xgb, os.path.join(output_dir, 'growth_xgb.joblib'))
        
        # Save LightGBM model
        self.growth_lgb.save(os.path.join(output_dir, 'growth_lgb'))
        
        # Save temperature model
        torch.save(self.temp_model.state_dict(), os.path.join(output_dir, 'temperature_model.pt'))
        
        # Save weights, scalers and other information
        joblib.dump(self.growth_weights.detach().cpu().numpy(), os.path.join(output_dir, 'growth_weights.joblib'))
        joblib.dump(self.feature_scaler, os.path.join(output_dir, 'feature_scaler.joblib'))
        joblib.dump(self.temp_feature_scaler, os.path.join(output_dir, 'temp_feature_scaler.joblib'))
        
        # Save model configuration
        config = {
            'growth_ensemble_weight_lr': self.growth_ensemble_weight_lr,
            'growth_ensemble_weight_epochs': self.growth_ensemble_weight_epochs,
            'temp_nn_hidden_dims': self.temp_nn_hidden_dims,
            'temp_nn_dropout_rates': self.temp_nn_dropout_rates,
            'temp_nn_lr': self.temp_nn_lr,
            'temp_nn_batch_size': self.temp_nn_batch_size,
            'temp_nn_epochs': self.temp_nn_epochs,
            'temp_nn_patience': self.temp_nn_patience,
            'variance_percentile': self.variance_percentile
        }
        
        joblib.dump(config, os.path.join(output_dir, 'model_config.joblib'))
        logger.info(f"Model saved to {output_dir}")
    
    def load(self, input_dir: str) -> None:
        """
        Load model from disk.
        
        Args:
            input_dir: Directory to load model from
        """
        logger.info(f"Loading model from {input_dir}")
        
        # Load growth rate models
        self.growth_rf = joblib.load(os.path.join(input_dir, 'growth_rf.joblib'))
        self.growth_xgb = joblib.load(os.path.join(input_dir, 'growth_xgb.joblib'))
        
        # Load LightGBM model
        self.growth_lgb.load(os.path.join(input_dir, 'growth_lgb'))
        
        # Load weights and scalers
        weights = joblib.load(os.path.join(input_dir, 'growth_weights.joblib'))
        self.growth_weights = nn.Parameter(torch.tensor(weights, dtype=torch.float32))
        
        self.feature_scaler = joblib.load(os.path.join(input_dir, 'feature_scaler.joblib'))
        self.temp_feature_scaler = joblib.load(os.path.join(input_dir, 'temp_feature_scaler.joblib'))
        
        # Load model configuration
        config = joblib.load(os.path.join(input_dir, 'model_config.joblib'))
        
        self.growth_ensemble_weight_lr = config['growth_ensemble_weight_lr']
        self.growth_ensemble_weight_epochs = config['growth_ensemble_weight_epochs']
        self.temp_nn_hidden_dims = config['temp_nn_hidden_dims']
        self.temp_nn_dropout_rates = config['temp_nn_dropout_rates']
        self.temp_nn_lr = config['temp_nn_lr']
        self.temp_nn_batch_size = config['temp_nn_batch_size']
        self.temp_nn_epochs = config['temp_nn_epochs']
        self.temp_nn_patience = config['temp_nn_patience']
        self.variance_percentile = config['variance_percentile']
        
        # Initialize temperature model
        # We need to know the input dimension, so we'll infer it from the feature scaler
        input_dim = len(self.temp_feature_scaler.mean_)
        
        self.temp_model = RegularizedDNN(
            input_dim=input_dim,
            hidden_dims=self.temp_nn_hidden_dims,
            dropout_rates=self.temp_nn_dropout_rates,
            use_batch_norm=True,
            activation='relu',
            use_residual=True
        ).to(self.device)
        
        # Load temperature model state
        self.temp_model.load_state_dict(torch.load(
            os.path.join(input_dir, 'temperature_model.pt'),
            map_location=self.device
        ))
        
        logger.info(f"Model loaded from {input_dir}")
