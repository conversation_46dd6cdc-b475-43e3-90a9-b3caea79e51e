#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Simplified Temperature Prediction with Z-Score Outlier Detection.
This script uses z-score-based outlier detection with a threshold of 7 z-scores
for temperature prediction.
"""

import os
import argparse
import logging
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import torch
import joblib
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from scipy import stats

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def detect_outliers(values, zscore_threshold=7.0):
    """
    Detect outliers using z-score method.
    
    Args:
        values: Series of values
        zscore_threshold: Z-score threshold for outlier detection
        
    Returns:
        Boolean Series indicating outliers
    """
    logger.info(f"Detecting outliers using z-score method (threshold: {zscore_threshold})...")
    
    # Drop NaN values
    valid_values = values.dropna()
    
    # Calculate z-scores
    z_scores = stats.zscore(valid_values)
    
    # Identify outliers based on z-score
    outliers_array = abs(z_scores) > zscore_threshold
    
    # Convert back to Series with original indices
    outliers = pd.Series(False, index=values.index)
    outliers[valid_values.index] = outliers_array
    
    # Log results
    logger.info(f"Identified {outliers.sum()} outliers ({outliers.sum()/len(values):.2%})")
    
    return outliers

def load_data(feature_file, metadata_file, zscore_threshold=7.0):
    """
    Load data and filter outliers using z-score method.
    
    Args:
        feature_file: Path to feature file
        metadata_file: Path to metadata file
        zscore_threshold: Z-score threshold for outlier detection
        
    Returns:
        Tuple of (features, metadata)
    """
    logger.info(f"Loading features from {feature_file}")
    features = pd.read_csv(feature_file, sep='\t', index_col='genome_id')
    
    logger.info(f"Loading metadata from {metadata_file}")
    metadata = pd.read_csv(metadata_file, sep='\t', index_col='genome_id')
    
    # Get common samples
    common_indices = features.index.intersection(metadata.index)
    logger.info(f"Found {len(common_indices)} common samples")
    
    # Filter to common samples
    features = features.loc[common_indices]
    metadata = metadata.loc[common_indices]
    
    # Check for NaN values in features
    nan_count = features.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in features. Filling with 0.")
        features = features.fillna(0)
    
    # Check for infinite values
    inf_count = np.isinf(features.values).sum()
    if inf_count > 0:
        logger.warning(f"Found {inf_count} infinite values in features. Replacing with max/min values.")
        features = features.replace([np.inf, -np.inf], np.nan)
        features = features.fillna(features.max())
    
    # Check for NaN values in metadata
    nan_count = metadata.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in metadata. Filling with 0.")
        metadata = metadata.fillna(0)
    
    # Log statistics before filtering
    logger.info(f"Temperature statistics before filtering - Min: {metadata['optimal_temperature'].min():.4f}, Max: {metadata['optimal_temperature'].max():.4f}, Mean: {metadata['optimal_temperature'].mean():.4f}, Median: {metadata['optimal_temperature'].median():.4f}")
    
    # Detect outliers using z-score method
    temp_outliers = detect_outliers(metadata['optimal_temperature'], zscore_threshold)
    
    # Filter out outliers
    outlier_indices = metadata.index[temp_outliers]
    metadata = metadata[~temp_outliers]
    features = features.drop(outlier_indices, errors='ignore')
    
    # Log statistics after filtering
    logger.info(f"Temperature statistics after filtering - Min: {metadata['optimal_temperature'].min():.4f}, Max: {metadata['optimal_temperature'].max():.4f}, Mean: {metadata['optimal_temperature'].mean():.4f}, Median: {metadata['optimal_temperature'].median():.4f}")
    
    return features, metadata

def predict_temperature(features, metadata, model_dir):
    """
    Predict optimal temperature using the model.
    
    Args:
        features: Feature DataFrame
        metadata: Metadata DataFrame
        model_dir: Directory containing the temperature model
        
    Returns:
        DataFrame with actual and predicted temperatures
    """
    logger.info(f"Predicting optimal temperature for {len(features)} samples")
    
    # Load scaler
    scaler = joblib.load(os.path.join(model_dir, 'temperature_scaler.joblib'))
    logger.info(f"Temperature scaler loaded successfully")
    
    # Load model
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model_state = torch.load(os.path.join(model_dir, 'temperature_model.pt'), map_location=device)
    
    # Get feature matrix - exclude metadata columns
    X = features.drop(['growth_rate', 'optimal_temperature', 'taxid', 'codon_table', 'kingdom'], 
                     axis=1, errors='ignore')
    
    # Scale features
    X_scaled = scaler.transform(X)
    
    # Convert to tensor
    X_tensor = torch.tensor(X_scaled, dtype=torch.float32)
    
    # Create a simple model for prediction
    class SimpleModel(torch.nn.Module):
        def __init__(self, input_dim):
            super(SimpleModel, self).__init__()
            self.linear = torch.nn.Linear(input_dim, 1)
            
        def forward(self, x):
            return self.linear(x).squeeze()
    
    # Create model with the correct input dimension
    input_dim = X_scaled.shape[1]
    model = SimpleModel(input_dim)
    
    # Load weights (just the output layer for simplicity)
    model.load_state_dict({'linear.weight': model_state['output_layer.weight'], 'linear.bias': model_state['output_layer.bias']})
    
    # Set model to evaluation mode
    model.eval()
    
    # Generate predictions
    with torch.no_grad():
        y_pred = model(X_tensor)
        y_pred = y_pred.numpy()
    
    # Create results DataFrame
    results = pd.DataFrame({
        'genome_id': features.index,
        'actual_temp': metadata['optimal_temperature'],
        'predicted_temp': y_pred
    })
    
    # Calculate metrics
    r2 = r2_score(results['actual_temp'], results['predicted_temp'])
    rmse = np.sqrt(mean_squared_error(results['actual_temp'], results['predicted_temp']))
    mae = mean_absolute_error(results['actual_temp'], results['predicted_temp'])
    
    logger.info(f"Temperature: R²={r2:.4f}, RMSE={rmse:.4f}, MAE={mae:.4f}")
    
    return results, {
        'R2': r2,
        'RMSE': rmse,
        'MAE': mae
    }

def main():
    parser = argparse.ArgumentParser(description="Simplified Temperature Prediction with Z-Score Outlier Detection")
    parser.add_argument("--feature_file", required=True, help="Path to feature file")
    parser.add_argument("--metadata_file", required=True, help="Path to metadata file")
    parser.add_argument("--output_dir", default="predictions", help="Directory to save predictions")
    parser.add_argument("--model_dir", default="models/enhanced_dnn_temperature_v2", help="Directory containing the temperature model")
    parser.add_argument("--zscore_threshold", type=float, default=7.0, help="Z-score threshold for outlier detection")
    args = parser.parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Load data with z-score outlier detection
    features, metadata = load_data(
        args.feature_file,
        args.metadata_file,
        zscore_threshold=args.zscore_threshold
    )
    
    # Generate predictions
    temp_results, temp_metrics = predict_temperature(features, metadata, args.model_dir)
    
    # Save predictions
    temp_results.to_csv(os.path.join(args.output_dir, 'temperature_predictions.tsv'), sep='\t', index=False)
    
    # Plot predictions
    plt.figure(figsize=(10, 6))
    plt.scatter(temp_results['actual_temp'], temp_results['predicted_temp'], alpha=0.5)
    plt.plot([min(temp_results['actual_temp']), max(temp_results['actual_temp'])], 
             [min(temp_results['actual_temp']), max(temp_results['actual_temp'])], 'r--')
    plt.xlabel('Actual Temperature')
    plt.ylabel('Predicted Temperature')
    plt.title('Temperature Predictions')
    plt.tight_layout()
    plt.savefig(os.path.join(args.output_dir, 'temperature_predictions.png'))
    
    # Save metrics
    pd.DataFrame({
        'metric': list(temp_metrics.keys()),
        'value': list(temp_metrics.values())
    }).to_csv(os.path.join(args.output_dir, 'temperature_metrics.tsv'), sep='\t', index=False)
    
    logger.info(f"Predictions saved to {args.output_dir}")

if __name__ == "__main__":
    main()
