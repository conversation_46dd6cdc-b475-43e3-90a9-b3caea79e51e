#!/usr/bin/env python3

import os
import argparse
import numpy as np
import pandas as pd
import optuna
import torch
import torch.nn as nn
from torch.utils.data import DataLoader, TensorDataset
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, r2_score
import logging
import joblib
from sklearn.preprocessing import RobustScaler
from sklearn.feature_selection import mutual_info_regression

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class GrowthRateDNNModel(nn.Module):
    def __init__(self, input_dim, hidden_dims, dropout_rates, use_batch_norm=True, use_residual=True, use_attention=True):
        super().__init__()
        self.use_batch_norm = use_batch_norm
        self.use_residual = use_residual
        self.use_attention = use_attention
        
        # Ensure hidden dimensions are divisible by num_heads
        num_heads = 4
        hidden_dims = [dim - (dim % num_heads) for dim in hidden_dims]
        
        # Input layer
        self.input_layer = nn.Linear(input_dim, hidden_dims[0])
        if use_batch_norm:
            self.input_bn = nn.BatchNorm1d(hidden_dims[0], track_running_stats=False)
        
        # Hidden layers
        self.hidden_layers = nn.ModuleList()
        self.batch_norms = nn.ModuleList() if use_batch_norm else None
        self.dropouts = nn.ModuleList()
        
        for i in range(len(hidden_dims) - 1):
            self.hidden_layers.append(nn.Linear(hidden_dims[i], hidden_dims[i + 1]))
            if use_batch_norm:
                self.batch_norms.append(nn.BatchNorm1d(hidden_dims[i + 1], track_running_stats=False))
            self.dropouts.append(nn.Dropout(dropout_rates[i]))
        
        # Attention layer
        if use_attention:
            self.attention = nn.MultiheadAttention(hidden_dims[-1], num_heads=num_heads, batch_first=True)
        
        # Output layer
        self.output_layer = nn.Linear(hidden_dims[-1], 1)
    
    def forward(self, x):
        # Input layer
        x = self.input_layer(x)
        if self.use_batch_norm:
            # Handle batch size of 1
            if x.size(0) == 1:
                x = x.repeat(2, 1)  # Duplicate the sample
                x = self.input_bn(x)
                x = x[:1]  # Take only the first sample back
            else:
                x = self.input_bn(x)
        x = nn.ReLU()(x)
        
        # Hidden layers with residual connections
        for i, (layer, dropout) in enumerate(zip(self.hidden_layers, self.dropouts)):
            residual = x
            x = layer(x)
            if self.use_batch_norm:
                # Handle batch size of 1
                if x.size(0) == 1:
                    x = x.repeat(2, 1)  # Duplicate the sample
                    x = self.batch_norms[i](x)
                    x = x[:1]  # Take only the first sample back
                else:
                    x = self.batch_norms[i](x)
            x = nn.ReLU()(x)
            x = dropout(x)
            if self.use_residual and x.shape == residual.shape:
                x = x + residual
        
        # Attention layer
        if self.use_attention:
            x = x.unsqueeze(1)  # Add sequence dimension
            x, _ = self.attention(x, x, x)
            x = x.squeeze(1)  # Remove sequence dimension
        
        # Output layer
        x = self.output_layer(x)
        # Ensure output is always 2D (batch_size, 1)
        if x.dim() == 1:
            x = x.unsqueeze(-1)
        return x

def load_and_filter_data(feature_file, target_file):
    """
    Load preprocessed data for growth rate prediction.
    
    Args:
        feature_file: Path to the preprocessed feature file (.npy)
        target_file: Path to the preprocessed target file (.npy)
        
    Returns:
        X: Preprocessed feature matrix
        y: Preprocessed target vector
    """
    logger.info(f"Loading preprocessed data from {feature_file} and {target_file}")
    
    # Load preprocessed data
    X = np.load(feature_file)
    y = np.load(target_file)
    
    logger.info(f"Loaded data shapes - X: {X.shape}, y: {y.shape}")
    
    return X, y

def objective(trial, X_train, X_val, y_train, y_val, device):
    """Optuna objective function for DNN optimization."""
    # Define hyperparameter search space
    n_layers = trial.suggest_int('n_layers', 2, 5)
    hidden_dims = []
    dropout_rates = []
    
    # First layer dimension (ensure it's divisible by 4)
    first_dim = trial.suggest_int('first_layer_dim', 256, 1024)
    first_dim = first_dim - (first_dim % 4)  # Make divisible by 4
    hidden_dims.append(first_dim)
    
    # Subsequent layers
    for i in range(n_layers - 1):
        dim = trial.suggest_int(f'layer_{i+1}_dim', 64, hidden_dims[-1])
        dim = dim - (dim % 4)  # Make divisible by 4
        hidden_dims.append(dim)
        dropout_rates.append(trial.suggest_float(f'dropout_{i}', 0.1, 0.5))
    
    # Model hyperparameters
    use_batch_norm = trial.suggest_categorical('use_batch_norm', [True, False])
    use_residual = trial.suggest_categorical('use_residual', [True, False])
    use_attention = trial.suggest_categorical('use_attention', [True, False])
    
    # Training hyperparameters
    batch_size = trial.suggest_int('batch_size', 32, 128)  # Increased minimum batch size
    learning_rate = trial.suggest_float('learning_rate', 1e-5, 1e-2, log=True)
    weight_decay = trial.suggest_float('weight_decay', 1e-6, 1e-3, log=True)
    
    # Create model
    model = GrowthRateDNNModel(
        input_dim=X_train.shape[1],
        hidden_dims=hidden_dims,
        dropout_rates=dropout_rates,
        use_batch_norm=use_batch_norm,
        use_residual=use_residual,
        use_attention=use_attention
    ).to(device)
    
    # Create data loaders with properly shaped targets
    train_dataset = TensorDataset(
        torch.FloatTensor(X_train),
        torch.FloatTensor(y_train).unsqueeze(-1)  # Add dimension to match model output
    )
    val_dataset = TensorDataset(
        torch.FloatTensor(X_val),
        torch.FloatTensor(y_val).unsqueeze(-1)  # Add dimension to match model output
    )
    
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size)
    
    # Training setup
    optimizer = torch.optim.Adam(model.parameters(), lr=learning_rate, weight_decay=weight_decay)
    criterion = nn.MSELoss()
    
    # Training loop
    n_epochs = 50
    best_val_r2 = float('-inf')
    patience = 10
    patience_counter = 0
    
    for epoch in range(n_epochs):
        model.train()
        train_loss = 0
        for X_batch, y_batch in train_loader:
            X_batch, y_batch = X_batch.to(device), y_batch.to(device)
            
            optimizer.zero_grad()
            y_pred = model(X_batch)
            loss = criterion(y_pred, y_batch)
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
        
        # Validation
        model.eval()
        val_preds = []
        val_true = []
        with torch.no_grad():
            for X_batch, y_batch in val_loader:
                X_batch, y_batch = X_batch.to(device), y_batch.to(device)
                y_pred = model(X_batch)
                val_preds.extend(y_pred.cpu().numpy().flatten())
                val_true.extend(y_batch.cpu().numpy().flatten())
        
        # Calculate R² score
        val_r2 = r2_score(val_true, val_preds)
        
        # Early stopping
        if val_r2 > best_val_r2:
            best_val_r2 = val_r2
            patience_counter = 0
        else:
            patience_counter += 1
            if patience_counter >= patience:
                break
    
    # Return R² directly since we're maximizing
    return best_val_r2

def main():
    parser = argparse.ArgumentParser(description='Optimize DNN for growth rate prediction')
    parser.add_argument('--feature_file', type=str, required=True, help='Path to preprocessed feature file')
    parser.add_argument('--target_file', type=str, required=True, help='Path to preprocessed target file')
    parser.add_argument('--output_dir', type=str, required=True, help='Output directory for models')
    parser.add_argument('--n_trials', type=int, default=100, help='Number of optimization trials')
    parser.add_argument('--study_name', type=str, default='dnn_growth_optimization', help='Optuna study name')
    parser.add_argument('--n_jobs', type=int, default=4, help='Number of parallel jobs')
    
    args = parser.parse_args()
    
    # Set device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"Using device: {device}")
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Load preprocessed data
    X, y = load_and_filter_data(args.feature_file, args.target_file)
    
    # Split data
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    X_train, X_val, y_train, y_val = train_test_split(X_train, y_train, test_size=0.2, random_state=42)
    
    # Create Optuna study
    study = optuna.create_study(direction='maximize', study_name=args.study_name)
    
    # Run optimization
    logger.info(f"Starting optimization with {args.n_trials} trials...")
    study.optimize(lambda trial: objective(trial, X_train, X_val, y_train, y_val, device),
                  n_trials=args.n_trials,
                  n_jobs=args.n_jobs)
    
    # Get best parameters
    best_params = study.best_params
    logger.info(f"Best parameters: {best_params}")
    logger.info(f"Best validation R²: {study.best_value:.4f}")  # No need to convert back to positive
    
    # Train final model with best parameters
    logger.info("Training final model with best parameters...")
    n_layers = best_params['n_layers']
    hidden_dims = [best_params['first_layer_dim']]
    dropout_rates = []
    
    for i in range(n_layers - 1):
        hidden_dims.append(best_params[f'layer_{i+1}_dim'])
        dropout_rates.append(best_params[f'dropout_{i}'])
    
    final_model = GrowthRateDNNModel(
        input_dim=X_train.shape[1],
        hidden_dims=hidden_dims,
        dropout_rates=dropout_rates,
        use_batch_norm=best_params['use_batch_norm'],
        use_residual=best_params['use_residual'],
        use_attention=best_params['use_attention']
    ).to(device)
    
    # Create data loaders with properly shaped targets
    train_dataset = TensorDataset(
        torch.FloatTensor(X_train),
        torch.FloatTensor(y_train).unsqueeze(-1)  # Add dimension to match model output
    )
    test_dataset = TensorDataset(
        torch.FloatTensor(X_test),
        torch.FloatTensor(y_test).unsqueeze(-1)  # Add dimension to match model output
    )
    
    train_loader = DataLoader(train_dataset, batch_size=best_params['batch_size'], shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=best_params['batch_size'])
    
    # Training setup
    optimizer = torch.optim.Adam(
        final_model.parameters(),
        lr=best_params['learning_rate'],
        weight_decay=best_params['weight_decay']
    )
    criterion = nn.MSELoss()
    
    # Training loop
    n_epochs = 100
    best_test_r2 = float('-inf')
    patience = 20
    patience_counter = 0
    
    for epoch in range(n_epochs):
        # Training
        final_model.train()
        train_loss = 0
        for X_batch, y_batch in train_loader:
            X_batch, y_batch = X_batch.to(device), y_batch.to(device)
            
            optimizer.zero_grad()
            y_pred = final_model(X_batch)
            loss = criterion(y_pred, y_batch)
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
        
        # Testing
        final_model.eval()
        test_preds = []
        test_true = []
        
        with torch.no_grad():
            for X_batch, y_batch in test_loader:
                X_batch, y_batch = X_batch.to(device), y_batch.to(device)
                y_pred = final_model(X_batch)
                test_preds.extend(y_pred.cpu().numpy().flatten())
                test_true.extend(y_batch.cpu().numpy().flatten())
        
        # Calculate R² score
        test_r2 = r2_score(test_true, test_preds)
        
        # Early stopping
        if test_r2 > best_test_r2:
            best_test_r2 = test_r2
            patience_counter = 0
            # Save best model
            torch.save(final_model.state_dict(), os.path.join(args.output_dir, 'growth_rate_dnn_model.pth'))
        else:
            patience_counter += 1
            if patience_counter >= patience:
                break
    
    # Load best model and evaluate
    final_model.load_state_dict(torch.load(os.path.join(args.output_dir, 'growth_rate_dnn_model.pth')))
    final_model.eval()
    
    test_preds = np.array(test_preds)
    test_true = np.array(test_true)
    
    mse = mean_squared_error(test_true, test_preds)
    r2 = r2_score(test_true, test_preds)
    
    logger.info(f"Test MSE: {mse:.4f}")
    logger.info(f"Test R²: {r2:.4f}")
    
    # Save study
    study_path = os.path.join(args.output_dir, 'growth_rate_dnn_study.pkl')
    joblib.dump(study, study_path)
    logger.info(f"Study saved to {study_path}")

if __name__ == '__main__':
    main() 