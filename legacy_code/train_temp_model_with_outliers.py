import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.ensemble import IsolationForest
import xgboost as xgb
from sklearn.metrics import accuracy_score, mean_squared_error, r2_score, classification_report
import matplotlib.pyplot as plt
from pathlib import Path
from tqdm import tqdm
import seaborn as sns
import os
import joblib
import optuna
from optuna.trial import Trial

class ResidualBlock(nn.Module):
    def __init__(self, dim, dropout_rate=0.3):
        super(ResidualBlock, self).__init__()
        self.layers = nn.Sequential(
            nn.Linear(dim, dim),
            nn.BatchNorm1d(dim),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(dim, dim),
            nn.BatchNorm1d(dim)
        )
        self.dropout = nn.Dropout(dropout_rate)
        self.activation = nn.ReLU()
    
    def forward(self, x):
        residual = x
        out = self.layers(x)
        out = self.dropout(out)
        out += residual
        out = self.activation(out)
        return out

class FeatureEngineering(nn.Module):
    def __init__(self, input_dim):
        super(FeatureEngineering, self).__init__()
        self.bn = nn.BatchNorm1d(input_dim, track_running_stats=True)
        self.dropout = nn.Dropout(0.2)  # Increased dropout
        
        # Feature selection layer
        self.feature_selector = nn.Sequential(
            nn.Linear(input_dim, input_dim // 2),
            nn.ReLU(),
            nn.BatchNorm1d(input_dim // 2, track_running_stats=True),
            nn.Dropout(0.2)
        )
        
        # Feature interaction layer with reduced complexity
        self.interaction = nn.Sequential(
            nn.Linear(input_dim // 2, input_dim // 2),
            nn.ReLU(),
            nn.BatchNorm1d(input_dim // 2, track_running_stats=True),
            nn.Dropout(0.2)
        )
    
    def forward(self, x):
        # Handle single sample case
        if x.size(0) == 1:
            # Use running statistics for batch norm
            self.bn.eval()
            self.feature_selector[2].eval()
            self.interaction[2].eval()
        
        # Normalize input
        x = self.bn(x)
        
        # Feature selection
        x = self.feature_selector(x)
        
        # Add feature interactions
        x_interaction = self.interaction(x)
        
        # Combine original and interaction features
        x = x + x_interaction
        x = self.dropout(x)
        
        # Reset to training mode if it was single sample
        if x.size(0) == 1:
            self.bn.train()
            self.feature_selector[2].train()
            self.interaction[2].train()
        
        return x

class EnhancedMultiHeadAttention(nn.Module):
    def __init__(self, input_dim, num_heads=4, dropout=0.2):  # Increased dropout
        super(EnhancedMultiHeadAttention, self).__init__()
        assert input_dim % num_heads == 0, "input_dim must be divisible by num_heads"
        
        self.num_heads = num_heads
        self.head_dim = input_dim // num_heads
        self.scale = self.head_dim ** -0.5
        
        # Enhanced projections with layer normalization and reduced complexity
        self.q_proj = nn.Sequential(
            nn.LayerNorm(input_dim),
            nn.Linear(input_dim, input_dim),
            nn.Dropout(dropout)
        )
        self.k_proj = nn.Sequential(
            nn.LayerNorm(input_dim),
            nn.Linear(input_dim, input_dim),
            nn.Dropout(dropout)
        )
        self.v_proj = nn.Sequential(
            nn.LayerNorm(input_dim),
            nn.Linear(input_dim, input_dim),
            nn.Dropout(dropout)
        )
        
        self.out_proj = nn.Sequential(
            nn.Linear(input_dim, input_dim),
            nn.LayerNorm(input_dim),
            nn.Dropout(dropout)
        )
        
        self.dropout = nn.Dropout(dropout)
    
    def forward(self, x):
        batch_size = x.size(0)
        
        # Project queries, keys, and values
        q = self.q_proj(x).view(batch_size, -1, self.num_heads, self.head_dim).transpose(1, 2)
        k = self.k_proj(x).view(batch_size, -1, self.num_heads, self.head_dim).transpose(1, 2)
        v = self.v_proj(x).view(batch_size, -1, self.num_heads, self.head_dim).transpose(1, 2)
        
        # Compute attention scores with scaling
        scores = torch.matmul(q, k.transpose(-2, -1)) * self.scale
        
        # Apply attention mask if needed
        # scores = scores.masked_fill(mask == 0, -1e9)
        
        attn_weights = torch.softmax(scores, dim=-1)
        attn_weights = self.dropout(attn_weights)
        
        # Apply attention weights
        context = torch.matmul(attn_weights, v)
        context = context.transpose(1, 2).contiguous().view(batch_size, -1, self.num_heads * self.head_dim)
        
        # Project output with residual connection
        output = self.out_proj(context)
        return output + x

class TemperatureDNNModel(nn.Module):
    def __init__(self, input_dim, hidden_dims=[256, 256, 128, 64, 32],  # Reduced dimensions
                 dropout_rates=[0.2, 0.3, 0.3, 0.4, 0.4],  # Adjusted dropout
                 use_batch_norm=True, activation='relu', num_heads=4):
        super(TemperatureDNNModel, self).__init__()
        
        # Feature engineering layer
        self.feature_engineering = FeatureEngineering(input_dim)
        
        # Initial feature projection with reduced dimension
        self.input_projection = nn.Sequential(
            nn.Linear(input_dim // 2, hidden_dims[0]),  # Adjusted input dimension
            nn.BatchNorm1d(hidden_dims[0]),
            nn.ReLU(),
            nn.Dropout(dropout_rates[0])
        )
        
        # Ensure the first hidden dimension is divisible by num_heads
        attention_dim = (hidden_dims[0] // num_heads) * num_heads
        if attention_dim != hidden_dims[0]:
            self.dim_adjustment = nn.Linear(hidden_dims[0], attention_dim)
            hidden_dims[0] = attention_dim
        else:
            self.dim_adjustment = nn.Identity()
        
        # Enhanced multi-head attention layer
        self.attention = EnhancedMultiHeadAttention(
            input_dim=attention_dim,
            num_heads=num_heads,
            dropout=dropout_rates[0]
        )
        
        # Residual blocks with skip connections
        self.residual_blocks = nn.ModuleList([
            ResidualBlock(hidden_dims[0], dropout_rates[0])
            for _ in range(2)  # Reduced number of residual blocks
        ])
        
        # Progressive dimension reduction with skip connections
        layers = []
        prev_dim = hidden_dims[0]
        
        for i, hidden_dim in enumerate(hidden_dims[1:], 1):
            # Add skip connection if dimensions match
            if prev_dim == hidden_dim:
                layers.append(ResidualBlock(hidden_dim, dropout_rates[min(i, len(dropout_rates)-1)]))
            else:
                layers.extend([
                    nn.Linear(prev_dim, hidden_dim),
                    nn.BatchNorm1d(hidden_dim),
                    nn.ReLU(),
                    nn.Dropout(dropout_rates[min(i, len(dropout_rates)-1)])
                ])
            prev_dim = hidden_dim
        
        self.feature_extractor = nn.Sequential(*layers)
        
        # Output layer with no activation or dropout
        self.output_layer = nn.Linear(prev_dim, 1)
        
        # Initialize weights with improved method
        self._init_weights()
    
    def _init_weights(self):
        """Initialize weights with improved initialization."""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                # Use Kaiming initialization for better stability with ReLU
                nn.init.kaiming_normal_(m.weight, mode='fan_in', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
            elif isinstance(m, nn.BatchNorm1d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.LayerNorm):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
    
    def forward(self, x):
        # Feature engineering
        x = self.feature_engineering(x)
        
        # Initial projection
        x = self.input_projection(x)
        
        # Adjust dimension if needed
        x = self.dim_adjustment(x)
        
        # Add sequence dimension for attention
        x = x.unsqueeze(1)  # [batch_size, 1, hidden_dim]
        
        # Apply attention
        x = self.attention(x)
        x = x.squeeze(1)  # Remove sequence dimension
        
        # Residual blocks
        for block in self.residual_blocks:
            x = block(x)
        
        # Feature extraction
        features = self.feature_extractor(x)
        
        # Output
        output = self.output_layer(features)
        return output

class TemperatureModel(nn.Module):
    def __init__(self, input_dim, hidden_dims=[64, 32, 16], dropout_rate=0.3):
        super(TemperatureModel, self).__init__()
        
        # Input normalization
        self.input_norm = nn.BatchNorm1d(input_dim)
        
        # Feature extraction layers
        layers = []
        prev_dim = input_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.BatchNorm1d(hidden_dim),
                nn.ReLU(),
                nn.Dropout(dropout_rate)
            ])
            prev_dim = hidden_dim
        
        self.feature_extractor = nn.Sequential(*layers)
        
        # Output layer
        self.output_layer = nn.Linear(prev_dim, 1)
        
        # Initialize weights
        self._init_weights()
    
    def _init_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Linear):
                # Use Xavier initialization for better stability
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
            elif isinstance(m, nn.BatchNorm1d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
    
    def forward(self, x):
        x = self.input_norm(x)
        features = self.feature_extractor(x)
        output = self.output_layer(features)
        return output

class TemperatureEnsemble:
    def __init__(self, input_dim, n_models=5, temperature_ranges=None):
        self.n_models = n_models
        self.models = []
        self.temperature_ranges = temperature_ranges or [
            (0, 30), (20, 50), (40, 70), (60, 90), (80, 100)
        ]
        
        # Initialize models for each temperature range
        for _ in range(n_models):
            model = TemperatureModel(
                input_dim=input_dim,
                hidden_dims=[64, 32, 16],
                dropout_rate=0.3
            )
            self.models.append(model)
    
    def to(self, device):
        for model in self.models:
            model.to(device)
        return self
    
    def train(self):
        for model in self.models:
            model.train()
    
    def eval(self):
        for model in self.models:
            model.eval()
    
    def forward(self, x, temperatures):
        # Get predictions from each model
        predictions = []
        for model in self.models:
            pred = model(x)
            predictions.append(pred)
        
        # Stack predictions
        predictions = torch.stack(predictions, dim=0)
        
        # Calculate weights based on temperature ranges
        weights = torch.zeros_like(predictions)
        for i, (min_temp, max_temp) in enumerate(self.temperature_ranges):
            mask = (temperatures >= min_temp) & (temperatures <= max_temp)
            weights[i, mask] = 1.0
        
        # Normalize weights
        weights = weights / (weights.sum(dim=0, keepdim=True) + 1e-8)
        
        # Weighted average of predictions
        final_pred = (predictions * weights).sum(dim=0)
        return final_pred

def detect_outliers(X, y, contamination=0.1):
    """Detect outliers using Isolation Forest"""
    # Combine features and target for outlier detection
    data = np.column_stack([X, y])
    
    # Initialize and fit Isolation Forest
    iso_forest = IsolationForest(
        contamination=contamination,
        random_state=42,
        n_estimators=100,
        max_samples='auto'
    )
    
    # Fit and predict
    outliers = iso_forest.fit_predict(data)
    
    # Return mask for inliers (1 for inliers, -1 for outliers)
    return outliers == 1

def load_and_preprocess_data(metadata_path, features_path, test_size=0.2, random_state=42):
    print("Loading metadata...")
    metadata = pd.read_csv(metadata_path, sep='\t')
    
    print("Loading features...")
    features = pd.read_csv(features_path, sep='\t')
    
    # Merge metadata and features
    data = pd.merge(metadata, features, on='genome_id', how='inner')
    
    # Extract features and target
    X = data.drop(['genome_id', 'growth_rate', 'optimal_temperature', 'taxid', 'codon_table', 'kingdom'], axis=1)
    y = data['optimal_temperature']
    
    # Print original temperature statistics
    print("\nOriginal Temperature Statistics:")
    print(f"Min temperature: {y.min():.2f}")
    print(f"Max temperature: {y.max():.2f}")
    print(f"Mean temperature: {y.mean():.2f}")
    print(f"Std temperature: {y.std():.2f}")
    
    # Handle any NaN values in features
    X = X.fillna(X.mean())
    
    # Scale features using robust scaling
    feature_scaler = StandardScaler()
    X_scaled = feature_scaler.fit_transform(X)
    
    # Detect outliers separately for bacteria and archaea
    print("Detecting outliers...")
    bacteria_mask = data['kingdom'] == 'bac'
    archaea_mask = data['kingdom'] == 'arc'
    
    # Detect outliers for bacteria
    if bacteria_mask.any():
        bacteria_outliers = detect_outliers(
            X_scaled[bacteria_mask], 
            y.values[bacteria_mask],
            contamination=0.05  # Lower contamination for bacteria
        )
        bacteria_inliers = bacteria_outliers == 1
    else:
        bacteria_inliers = np.array([])
    
    # Detect outliers for archaea
    if archaea_mask.any():
        archaea_outliers = detect_outliers(
            X_scaled[archaea_mask], 
            y.values[archaea_mask],
            contamination=0.1  # Higher contamination for archaea due to smaller sample size
        )
        archaea_inliers = archaea_outliers == 1
    else:
        archaea_inliers = np.array([])
    
    # Combine inlier masks
    inlier_mask = np.zeros(len(X), dtype=bool)
    inlier_mask[bacteria_mask] = bacteria_inliers
    inlier_mask[archaea_mask] = archaea_inliers
    
    X_clean = X_scaled[inlier_mask]
    y_clean = y.values[inlier_mask]
    kingdoms_clean = data['kingdom'].values[inlier_mask]
    
    print(f"Removed {len(X) - len(X_clean)} outliers")
    print(f"Remaining samples: {len(X_clean)}")
    print(f"Bacteria samples: {np.sum(kingdoms_clean == 'bac')}")
    print(f"Archaea samples: {np.sum(kingdoms_clean == 'arc')}")
    
    # Verify no NaN values remain
    if np.isnan(X_clean).any():
        print("Warning: NaN values found in features after cleaning")
        X_clean = np.nan_to_num(X_clean, nan=0.0)
    
    if np.isnan(y_clean).any():
        print("Warning: NaN values found in targets after cleaning")
        y_clean = np.nan_to_num(y_clean, nan=np.nanmean(y_clean))
    
    # Create temperature bins for stratification
    temp_bins = pd.qcut(y_clean, 10, labels=False, duplicates='drop')
    
    # Split the data with stratification
    X_train, X_test, y_train, y_test, temp_bins_train, _ = train_test_split(
        X_clean, y_clean, temp_bins, test_size=test_size, random_state=random_state, stratify=temp_bins
    )
    
    # Normalize temperature values
    temp_mean = np.mean(y_train)
    temp_std = np.std(y_train)
    y_train = (y_train - temp_mean) / temp_std
    y_test = (y_test - temp_mean) / temp_std
    
    # Print normalized temperature statistics
    print("\nNormalized Temperature Statistics:")
    print(f"Min temperature: {y_train.min():.2f}")
    print(f"Max temperature: {y_train.max():.2f}")
    print(f"Mean temperature: {y_train.mean():.2f}")
    print(f"Std temperature: {y_train.std():.2f}")
    
    # Final verification
    print("Verifying data...")
    print(f"Training set shape: {X_train.shape}")
    print(f"Test set shape: {X_test.shape}")
    print(f"Feature range: [{X_train.min():.2f}, {X_train.max():.2f}]")
    print(f"Target range: [{y_train.min():.2f}, {y_train.max():.2f}]")
    
    return X_train, X_test, y_train, y_test, feature_scaler, temp_mean, temp_std

def create_data_loaders(X_train, X_test, y_train, y_test, batch_size=32):
    """Create data loaders with stratified sampling based on temperature ranges"""
    # Create temperature bins for stratification
    temp_bins = pd.qcut(y_train, 10, labels=False, duplicates='drop')
    
    # Convert to PyTorch tensors
    X_train_tensor = torch.FloatTensor(X_train)
    y_train_tensor = torch.FloatTensor(y_train).view(-1, 1)
    X_test_tensor = torch.FloatTensor(X_test)
    y_test_tensor = torch.FloatTensor(y_test).view(-1, 1)
    
    # Create datasets
    train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
    test_dataset = TensorDataset(X_test_tensor, y_test_tensor)
    
    # Create data loaders with stratified sampling
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=batch_size)
    
    return train_loader, test_loader

def calculate_r2(y_true, y_pred):
    """Calculate R² score"""
    ss_res = np.sum((y_true - y_pred) ** 2)
    ss_tot = np.sum((y_true - np.mean(y_true)) ** 2)
    r2 = 1 - (ss_res / ss_tot)
    return r2

def train_model(model, train_loader, test_loader, optimizer, criterion, scheduler, num_epochs, device, 
                l2_reg=0.0001, l1_reg=0.00001, patience=15, max_grad_norm=1.0):
    """Train model with improved stability"""
    train_losses = []
    test_losses = []
    train_r2_scores = []
    test_r2_scores = []
    best_test_loss = float('inf')
    best_test_r2 = float('-inf')
    patience_counter = 0
    best_model_path = 'models/best_temp_model.pth'
    
    # Initialize exponential moving averages for monitoring
    ema_train_loss = None
    ema_test_loss = None
    ema_train_r2 = None
    ema_test_r2 = None
    ema_alpha = 0.1  # Smoothing factor
    
    for epoch in range(num_epochs):
        model.train()
        train_loss = 0.0
        train_preds = []
        train_targets = []
        
        # Training phase
        for X_batch, y_batch in train_loader:
            X_batch, y_batch = X_batch.to(device), y_batch.to(device)
            
            optimizer.zero_grad()
            outputs = model(X_batch)
            
            # Check for NaN values
            if torch.isnan(outputs).any():
                print("Warning: NaN values detected in model outputs")
                continue
                
            loss = criterion(outputs, y_batch)
            
            # Check for NaN loss
            if torch.isnan(loss):
                print("Warning: NaN loss detected")
                continue
            
            # Add L1 and L2 regularization
            l1_loss = 0.0
            l2_loss = 0.0
            for param in model.parameters():
                l1_loss += torch.norm(param, 1)
                l2_loss += torch.norm(param, 2)
            loss += l1_reg * l1_loss + l2_reg * l2_loss
            
            loss.backward()
            
            # Check for NaN gradients
            has_nan_grad = False
            for param in model.parameters():
                if param.grad is not None and torch.isnan(param.grad).any():
                    has_nan_grad = True
                    break
            
            if has_nan_grad:
                print("Warning: NaN gradients detected")
                optimizer.zero_grad()
                continue
            
            # Gradient clipping
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_grad_norm)
            
            optimizer.step()
            train_loss += loss.item()
            
            # Store predictions and targets for R² calculation
            train_preds.extend(outputs.detach().cpu().numpy())
            train_targets.extend(y_batch.cpu().numpy())
        
        # Evaluation phase
        model.eval()
        test_loss = 0.0
        test_preds = []
        test_targets = []
        
        with torch.no_grad():
            for X_batch, y_batch in test_loader:
                X_batch, y_batch = X_batch.to(device), y_batch.to(device)
                outputs = model(X_batch)
                loss = criterion(outputs, y_batch)
                test_loss += loss.item()
                
                # Store predictions and targets for R² calculation
                test_preds.extend(outputs.cpu().numpy())
                test_targets.extend(y_batch.cpu().numpy())
        
        # Calculate average losses
        train_loss /= len(train_loader)
        test_loss /= len(test_loader)
        
        # Calculate R² scores
        train_r2 = calculate_r2(np.array(train_targets), np.array(train_preds))
        test_r2 = calculate_r2(np.array(test_targets), np.array(test_preds))
        
        # Update learning rate scheduler
        if isinstance(scheduler, optim.lr_scheduler.ReduceLROnPlateau):
            scheduler.step(test_loss)  # Use validation loss for ReduceLROnPlateau
        else:
            scheduler.step()
        
        # Update exponential moving averages
        if ema_train_loss is None:
            ema_train_loss = train_loss
            ema_test_loss = test_loss
            ema_train_r2 = train_r2
            ema_test_r2 = test_r2
        else:
            ema_train_loss = ema_alpha * train_loss + (1 - ema_alpha) * ema_train_loss
            ema_test_loss = ema_alpha * test_loss + (1 - ema_alpha) * ema_test_loss
            ema_train_r2 = ema_alpha * train_r2 + (1 - ema_alpha) * ema_train_r2
            ema_test_r2 = ema_alpha * test_r2 + (1 - ema_alpha) * ema_test_r2
        
        # Store metrics
        train_losses.append(ema_train_loss)
        test_losses.append(ema_test_loss)
        train_r2_scores.append(ema_train_r2)
        test_r2_scores.append(ema_test_r2)
        
        # Early stopping based on both loss and R²
        if test_r2 > best_test_r2:
            best_test_r2 = test_r2
            best_test_loss = test_loss
            patience_counter = 0
            # Save best model
            try:
                torch.save(model.state_dict(), best_model_path)
            except Exception as e:
                print(f"Warning: Failed to save model: {e}")
        else:
            patience_counter += 1
            if patience_counter >= patience:
                print(f"\nEarly stopping triggered after {epoch + 1} epochs")
                print(f"Best test R²: {best_test_r2:.4f}")
                # Load best model if it exists
                try:
                    if os.path.exists(best_model_path):
                        model.load_state_dict(torch.load(best_model_path))
                    else:
                        print("Warning: Best model file not found")
                except Exception as e:
                    print(f"Warning: Failed to load best model: {e}")
                break
        
        if (epoch + 1) % 10 == 0:
            print(f'Epoch [{epoch+1}/{num_epochs}], '
                  f'Train Loss: {ema_train_loss:.4f}, Test Loss: {ema_test_loss:.4f}, '
                  f'Train R²: {ema_train_r2:.4f}, Test R²: {ema_test_r2:.4f}')
    
    return train_losses, test_losses, train_r2_scores, test_r2_scores

def evaluate_model(model, X_test, y_test, device):
    model.eval()
    with torch.no_grad():
        X_test_tensor = torch.FloatTensor(X_test).to(device)
        outputs = model(X_test_tensor)
        predictions = outputs.cpu().numpy().flatten()
    
    mse = np.mean((predictions - y_test) ** 2)
    rmse = np.sqrt(mse)
    mae = np.mean(np.abs(predictions - y_test))
    r2 = 1 - np.sum((y_test - predictions) ** 2) / np.sum((y_test - np.mean(y_test)) ** 2)
    
    return {
        'MSE': mse,
        'RMSE': rmse,
        'MAE': mae,
        'R2': r2
    }

def plot_results(train_losses, test_losses, train_r2_scores, test_r2_scores, metrics, save_dir='plots'):
    # Create plots directory if it doesn't exist
    os.makedirs(save_dir, exist_ok=True)
    
    # Plot training and test losses
    plt.figure(figsize=(10, 6))
    plt.plot(train_losses, label='Training Loss')
    plt.plot(test_losses, label='Test Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('Training and Test Losses')
    plt.legend()
    plt.savefig(os.path.join(save_dir, 'temp_loss_plot.png'))
    plt.close()
    
    # Plot R² scores
    plt.figure(figsize=(10, 6))
    plt.plot(train_r2_scores, label='Training R²')
    plt.plot(test_r2_scores, label='Test R²')
    plt.xlabel('Epoch')
    plt.ylabel('R² Score')
    plt.title('Training and Test R² Scores')
    plt.legend()
    plt.savefig(os.path.join(save_dir, 'temp_r2_plot.png'))
    plt.close()
    
    # Plot metrics
    plt.figure(figsize=(10, 6))
    metrics_values = list(metrics.values())
    metrics_names = list(metrics.keys())
    plt.bar(metrics_names, metrics_values)
    plt.title('Temperature Model Evaluation Metrics')
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'temp_metrics_plot.png'))
    plt.close()

def train_ensemble(ensemble, train_loader, test_loader, optimizer, criterion, scheduler, 
                  num_epochs, device, l2_reg=0.0002, l1_reg=0.00002, patience=20, max_grad_norm=0.5):
    """Train ensemble model with improved stability"""
    train_losses = []
    test_losses = []
    train_r2_scores = []
    test_r2_scores = []
    best_test_loss = float('inf')
    best_test_r2 = float('-inf')
    patience_counter = 0
    best_model_path = 'models/best_temp_ensemble.pth'
    
    # Initialize exponential moving averages
    ema_train_loss = None
    ema_test_loss = None
    ema_train_r2 = None
    ema_test_r2 = None
    ema_alpha = 0.1
    
    for epoch in range(num_epochs):
        ensemble.train()
        train_loss = 0.0
        train_preds = []
        train_targets = []
        
        # Training phase
        for X_batch, y_batch in train_loader:
            X_batch, y_batch = X_batch.to(device), y_batch.to(device)
            
            optimizer.zero_grad()
            outputs = ensemble.forward(X_batch, y_batch)
            
            if torch.isnan(outputs).any():
                print("Warning: NaN values detected in model outputs")
                continue
            
            loss = criterion(outputs, y_batch)
            
            if torch.isnan(loss):
                print("Warning: NaN loss detected")
                continue
            
            # Add regularization
            l1_loss = 0.0
            l2_loss = 0.0
            for model in ensemble.models:
                for param in model.parameters():
                    l1_loss += torch.norm(param, 1)
                    l2_loss += torch.norm(param, 2)
            loss += l1_reg * l1_loss + l2_reg * l2_loss
            
            loss.backward()
            
            # Check for NaN gradients
            has_nan_grad = False
            for model in ensemble.models:
                for param in model.parameters():
                    if param.grad is not None and torch.isnan(param.grad).any():
                        has_nan_grad = True
                        break
            
            if has_nan_grad:
                print("Warning: NaN gradients detected")
                optimizer.zero_grad()
                continue
            
            # Gradient clipping
            for model in ensemble.models:
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_grad_norm)
            
            optimizer.step()
            train_loss += loss.item()
            
            train_preds.extend(outputs.detach().cpu().numpy())
            train_targets.extend(y_batch.cpu().numpy())
        
        scheduler.step()
        
        # Evaluation phase
        ensemble.eval()
        test_loss = 0.0
        test_preds = []
        test_targets = []
        
        with torch.no_grad():
            for X_batch, y_batch in test_loader:
                X_batch, y_batch = X_batch.to(device), y_batch.to(device)
                outputs = ensemble.forward(X_batch, y_batch)
                loss = criterion(outputs, y_batch)
                test_loss += loss.item()
                
                test_preds.extend(outputs.cpu().numpy())
                test_targets.extend(y_batch.cpu().numpy())
        
        # Calculate metrics
        train_loss /= len(train_loader)
        test_loss /= len(test_loader)
        
        train_r2 = calculate_r2(np.array(train_targets), np.array(train_preds))
        test_r2 = calculate_r2(np.array(test_targets), np.array(test_preds))
        
        # Update EMAs
        if ema_train_loss is None:
            ema_train_loss = train_loss
            ema_test_loss = test_loss
            ema_train_r2 = train_r2
            ema_test_r2 = test_r2
        else:
            ema_train_loss = ema_alpha * train_loss + (1 - ema_alpha) * ema_train_loss
            ema_test_loss = ema_alpha * test_loss + (1 - ema_alpha) * ema_test_loss
            ema_train_r2 = ema_alpha * train_r2 + (1 - ema_alpha) * ema_train_r2
            ema_test_r2 = ema_alpha * test_r2 + (1 - ema_alpha) * ema_test_r2
        
        # Store metrics
        train_losses.append(ema_train_loss)
        test_losses.append(ema_test_loss)
        train_r2_scores.append(ema_train_r2)
        test_r2_scores.append(ema_test_r2)
        
        # Early stopping
        if test_r2 > best_test_r2:
            best_test_r2 = test_r2
            best_test_loss = test_loss
            patience_counter = 0
            try:
                torch.save({
                    'ensemble_state_dict': [model.state_dict() for model in ensemble.models],
                    'temperature_ranges': ensemble.temperature_ranges
                }, best_model_path)
            except Exception as e:
                print(f"Warning: Failed to save model: {e}")
        else:
            patience_counter += 1
            if patience_counter >= patience:
                print(f"\nEarly stopping triggered after {epoch + 1} epochs")
                print(f"Best test R²: {best_test_r2:.4f}")
                try:
                    if os.path.exists(best_model_path):
                        checkpoint = torch.load(best_model_path)
                        for model, state_dict in zip(ensemble.models, checkpoint['ensemble_state_dict']):
                            model.load_state_dict(state_dict)
                    else:
                        print("Warning: Best model file not found")
                except Exception as e:
                    print(f"Warning: Failed to load best model: {e}")
                break
        
        if (epoch + 1) % 10 == 0:
            print(f'Epoch [{epoch+1}/{num_epochs}], '
                  f'Train Loss: {ema_train_loss:.4f}, Test Loss: {ema_test_loss:.4f}, '
                  f'Train R²: {ema_train_r2:.4f}, Test R²: {ema_test_r2:.4f}')
    
    return train_losses, test_losses, train_r2_scores, test_r2_scores

def create_temperature_ranges(temperatures):
    """Create temperature range labels with overlapping boundaries"""
    # Convert to numpy array if not already
    temperatures = np.array(temperatures)
    
    # Print temperature statistics
    print("\nTemperature Statistics:")
    print(f"Min temperature: {temperatures.min():.2f}")
    print(f"Max temperature: {temperatures.max():.2f}")
    print(f"Mean temperature: {temperatures.mean():.2f}")
    print(f"Std temperature: {temperatures.std():.2f}")
    
    # Create ranges based on temperature values
    ranges = np.zeros_like(temperatures, dtype=object)
    
    # Define temperature boundaries (using actual temperature values)
    psychrophile_mask = temperatures < 20
    mesophile_mask = (temperatures >= 20) & (temperatures < 45)
    thermophile_mask = (temperatures >= 45) & (temperatures < 70)
    hyperthermophile_mask = temperatures >= 70
    
    # Assign labels
    ranges[psychrophile_mask] = 'psychrophile'
    ranges[mesophile_mask] = 'mesophile'
    ranges[thermophile_mask] = 'thermophile'
    ranges[hyperthermophile_mask] = 'hyperthermophile'
    
    # Print range distribution
    unique_ranges, range_counts = np.unique(ranges, return_counts=True)
    print("\nTemperature Range Distribution:")
    for range_name, count in zip(unique_ranges, range_counts):
        print(f"{range_name}: {count} samples")
    
    return ranges

def engineer_features(X, y, metadata=None, feature_names=None, feature_stats=None, selected_features=None, feature_sets=None):
    """Engineer features for temperature prediction with improved interactions"""
    # Convert numpy array to DataFrame if needed
    if isinstance(X, np.ndarray):
        if feature_names is None:
            feature_names = [f'feature_{i}' for i in range(X.shape[1])]
        X_df = pd.DataFrame(X, columns=feature_names)
    else:
        X_df = X.copy()
    
    # Calculate or use provided feature statistics and feature sets
    if feature_stats is None:
        feature_stats = pd.DataFrame({
            'mean': X_df.mean(),
            'std': X_df.std(),
            'variance': X_df.var(),
            'skewness': X_df.skew(),
            'kurtosis': X_df.kurtosis()
        })
        
        # Calculate correlation with target
        if isinstance(y, np.ndarray):
            y_series = pd.Series(y)
        else:
            y_series = y
        
        correlations = pd.Series({
            col: X_df[col].corr(y_series) for col in X_df.columns
        })
        feature_stats['correlation'] = correlations
        
        # Select features for interactions based on multiple criteria
        # 1. High variance features
        high_var_features = feature_stats[feature_stats['variance'] > feature_stats['variance'].quantile(0.75)].index
        
        # 2. Features with significant correlation with target
        corr_features = feature_stats[abs(feature_stats['correlation']) > 0.1].index
        
        # 3. Features with significant skewness (non-normal distribution)
        skewed_features = feature_stats[abs(feature_stats['skewness']) > 1].index
        
        # Store feature sets
        feature_sets = {
            'high_var': high_var_features,
            'corr': corr_features,
            'skewed': skewed_features
        }
        
        # Combine selected features
        selected_features = list(set(high_var_features) | set(corr_features) | set(skewed_features))
        print(f"\nSelected {len(selected_features)} features for interactions")
        
        # Print top correlated features
        print("\nTop 10 features by absolute correlation with target:")
        top_corr = feature_stats.sort_values('correlation', key=abs, ascending=False).head(10)
        print(top_corr[['correlation', 'variance', 'skewness']])
    
    # Create interaction features
    interaction_features = {}
    interaction_count = 0
    max_interactions = 1000  # Limit total number of interactions
    
    # 1. Create multiplicative interactions between selected features
    for i, feat1 in enumerate(selected_features):
        for feat2 in selected_features[i+1:]:
            if interaction_count >= max_interactions:
                break
            # Only create interaction if at least one feature is correlated with target
            if feat1 in feature_sets['corr'] or feat2 in feature_sets['corr']:
                interaction_features[f'mul_{feat1}_{feat2}'] = X_df[feat1] * X_df[feat2]
                interaction_count += 1
    
    # 2. Create ratio interactions for features with non-zero values
    for feat1 in selected_features:
        for feat2 in selected_features:
            if feat1 != feat2 and interaction_count < max_interactions:
                # Only create ratio if at least one feature is correlated with target
                if (feat1 in feature_sets['corr'] or feat2 in feature_sets['corr']) and (X_df[feat2] != 0).all():
                    interaction_features[f'ratio_{feat1}_{feat2}'] = X_df[feat1] / X_df[feat2]
                    interaction_count += 1
    
    # 3. Create polynomial features for top correlated features
    top_corr_features = feature_stats.sort_values('correlation', key=abs, ascending=False).head(10).index
    for feat in top_corr_features:
        if interaction_count < max_interactions:
            interaction_features[f'{feat}_squared'] = X_df[feat] ** 2
            interaction_count += 1
    
    # Add all interaction features at once
    if interaction_features:
        interaction_df = pd.DataFrame(interaction_features)
        X_df = pd.concat([X_df, interaction_df], axis=1)
    
    # Add metadata features if available
    if metadata is not None:
        for col in metadata.columns:
            if col not in X_df.columns:
                X_df[col] = metadata[col]
    
    # Print feature engineering summary
    print(f"\nFeature Engineering Summary:")
    print(f"Original features: {X.shape[1]}")
    print(f"Added interaction features: {len(interaction_features)}")
    print(f"Total features after engineering: {X_df.shape[1]}")
    
    return X_df, feature_stats, selected_features, feature_sets

def train_classifier(X_train, y_train, X_test, y_test):
    """Train temperature range classifier with cross-validation"""
    # Get original temperature values from the data
    print("\nInput Temperature Statistics:")
    print(f"Min temperature: {y_train.min():.2f}")
    print(f"Max temperature: {y_train.max():.2f}")
    print(f"Mean temperature: {y_train.mean():.2f}")
    print(f"Std temperature: {y_train.std():.2f}")
    
    # Create temperature ranges
    y_train_ranges = create_temperature_ranges(y_train)
    y_test_ranges = create_temperature_ranges(y_test)
    
    # Print unique ranges before encoding
    print("\nUnique temperature ranges before encoding:")
    print(np.unique(y_train_ranges))
    
    # Encode temperature ranges to numerical values
    label_encoder = LabelEncoder()
    y_train_encoded = label_encoder.fit_transform(y_train_ranges)
    y_test_encoded = label_encoder.transform(y_test_ranges)
    
    # Print unique encoded values
    print("\nUnique encoded values:")
    print(np.unique(y_train_encoded))
    
    # Get number of classes
    n_classes = len(label_encoder.classes_)
    print(f"\nNumber of classes: {n_classes}")
    print("Class mapping:")
    for i, class_name in enumerate(label_encoder.classes_):
        print(f"{i}: {class_name}")
    
    # Ensure feature names are consistent
    feature_names = X_train.columns.tolist()
    
    # Convert DataFrames to numpy arrays to avoid feature name issues
    X_train_array = X_train.values
    X_test_array = X_test.values
    
    # Train XGBoost classifier with cross-validation
    classifier = xgb.XGBClassifier(
        n_estimators=200,
        max_depth=6,
        learning_rate=0.05,
        subsample=0.8,
        colsample_bytree=0.8,
        min_child_weight=3,
        gamma=0.1,
        random_state=42,
        objective='multi:softprob',  # Use softprob for multi-class
        num_class=n_classes,  # Specify number of classes
        base_score=0.5,  # Set base score to 0.5
        tree_method='hist'  # Use histogram-based algorithm for better performance
    )
    
    # Use stratified cross-validation
    from sklearn.model_selection import StratifiedKFold
    cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
    
    # Perform cross-validation
    cv_scores = cross_val_score(classifier, X_train_array, y_train_encoded, cv=cv)
    print(f"Cross-validation scores: {cv_scores}")
    print(f"Mean CV score: {cv_scores.mean():.4f} (+/- {cv_scores.std() * 2:.4f})")
    
    # Print class distribution
    unique_classes, class_counts = np.unique(y_train_encoded, return_counts=True)
    print("\nClass distribution in training data:")
    for cls, count in zip(unique_classes, class_counts):
        class_name = label_encoder.inverse_transform([cls])[0]
        print(f"{class_name}: {count} samples")
    
    # Train final classifier
    classifier.fit(X_train_array, y_train_encoded)
    
    # Evaluate classifier
    train_pred = classifier.predict(X_train_array)
    test_pred = classifier.predict(X_test_array)
    
    # Print unique predictions
    print("\nUnique predictions before inverse transform:")
    print(np.unique(train_pred))
    
    # Convert predictions back to original labels
    train_pred = label_encoder.inverse_transform(train_pred)
    test_pred = label_encoder.inverse_transform(test_pred)
    
    train_acc = accuracy_score(y_train_ranges, train_pred)
    test_acc = accuracy_score(y_test_ranges, test_pred)
    
    print("\nClassifier Performance:")
    print(f"Train Accuracy: {train_acc:.4f}")
    print(f"Test Accuracy: {test_acc:.4f}")
    print("\nClassification Report:")
    print(classification_report(y_test_ranges, test_pred))
    
    # Feature importance
    feature_importance = classifier.feature_importances_
    importance_df = pd.DataFrame({
        'feature': feature_names,
        'importance': feature_importance
    }).sort_values('importance', ascending=False)
    
    print("\nTop 10 Most Important Features:")
    print(importance_df.head(10))
    
    return classifier, label_encoder

def train_regressor(X_train, y_train, X_test, y_test, temp_range):
    """Train temperature regressor for specific range with cross-validation"""
    # Filter data for specific temperature range
    train_mask = create_temperature_ranges(y_train) == temp_range
    test_mask = create_temperature_ranges(y_test) == temp_range
    
    if not any(train_mask) or not any(test_mask):
        print(f"No data available for {temp_range}")
        return None
    
    X_train_range = X_train[train_mask]
    y_train_range = y_train[train_mask]
    X_test_range = X_test[test_mask]
    y_test_range = y_test[test_mask]
    
    print(f"\nTraining regressor for {temp_range}")
    print(f"Training samples: {len(X_train_range)}")
    print(f"Test samples: {len(X_test_range)}")
    
    # Train XGBoost regressor with cross-validation
    regressor = xgb.XGBRegressor(
        n_estimators=200,
        max_depth=6,
        learning_rate=0.05,
        subsample=0.8,
        colsample_bytree=0.8,
        min_child_weight=3,
        gamma=0.1,
        random_state=42
    )
    
    # Perform cross-validation
    cv_scores = cross_val_score(regressor, X_train_range, y_train_range, 
                              cv=5, scoring='r2')
    print(f"Cross-validation R² scores: {cv_scores}")
    print(f"Mean CV R² score: {cv_scores.mean():.4f} (+/- {cv_scores.std() * 2:.4f})")
    
    # Train final regressor
    regressor.fit(X_train_range, y_train_range)
    
    # Evaluate regressor
    train_pred = regressor.predict(X_train_range)
    test_pred = regressor.predict(X_test_range)
    
    train_r2 = r2_score(y_train_range, train_pred)
    test_r2 = r2_score(y_test_range, test_pred)
    train_rmse = np.sqrt(mean_squared_error(y_train_range, train_pred))
    test_rmse = np.sqrt(mean_squared_error(y_test_range, test_pred))
    
    print(f"\nRegressor Performance for {temp_range}:")
    print(f"Train R²: {train_r2:.4f}")
    print(f"Test R²: {test_r2:.4f}")
    print(f"Train RMSE: {train_rmse:.4f}")
    print(f"Test RMSE: {test_rmse:.4f}")
    
    # Feature importance
    feature_importance = regressor.feature_importances_
    feature_names = X_train_range.columns
    importance_df = pd.DataFrame({
        'feature': feature_names,
        'importance': feature_importance
    }).sort_values('importance', ascending=False)
    
    print("\nTop 10 Most Important Features:")
    print(importance_df.head(10))
    
    return regressor

def create_model_from_trial(trial: Trial, input_dim: int) -> TemperatureDNNModel:
    """Create a model with hyperparameters suggested by Optuna trial"""
    # Suggest number of layers and their dimensions
    n_layers = trial.suggest_int('n_layers', 3, 6)
    hidden_dims = []
    dropout_rates = []
    
    # First, suggest the first layer dimension and ensure it's divisible by 8
    first_dim = trial.suggest_int('layer_0_dim', 32, 512)
    first_dim = (first_dim // 8) * 8  # Make it divisible by 8
    hidden_dims.append(first_dim)
    
    # Suggest dropout rate for first layer
    dropout = trial.suggest_float('layer_0_dropout', 0.1, 0.5)
    dropout_rates.append(dropout)
    
    # Suggest remaining layers
    for i in range(1, n_layers):
        dim = trial.suggest_int(f'layer_{i}_dim', 32, 512)
        hidden_dims.append(dim)
        dropout = trial.suggest_float(f'layer_{i}_dropout', 0.1, 0.5)
        dropout_rates.append(dropout)
    
    # Suggest number of attention heads (2, 4, or 8)
    num_heads = trial.suggest_int('num_heads', 1, 3) * 2  # This will give us 2, 4, or 8
    
    # Create model
    model = TemperatureDNNModel(
        input_dim=input_dim,
        hidden_dims=hidden_dims,
        dropout_rates=dropout_rates,
        use_batch_norm=True,
        activation='relu',
        num_heads=num_heads
    )
    
    return model

def objective(trial: Trial, X_train, y_train, X_val, y_val, device):
    """Objective function for Optuna optimization"""
    # Create model
    input_dim = X_train.shape[1]
    model = create_model_from_trial(trial, input_dim).to(device)
    
    # Convert data to tensors, handling both DataFrame and numpy array inputs
    if isinstance(X_train, pd.DataFrame):
        X_train = X_train.values
    if isinstance(X_val, pd.DataFrame):
        X_val = X_val.values
    if isinstance(y_train, pd.Series):
        y_train = y_train.values
    if isinstance(y_val, pd.Series):
        y_val = y_val.values
    
    X_train_tensor = torch.FloatTensor(X_train).to(device)
    y_train_tensor = torch.FloatTensor(y_train).view(-1, 1).to(device)
    X_val_tensor = torch.FloatTensor(X_val).to(device)
    y_val_tensor = torch.FloatTensor(y_val).view(-1, 1).to(device)
    
    # Ensure batch size is at least 2
    batch_size = max(2, trial.suggest_int('batch_size', 16, 128))
    
    # Create data loaders with drop_last=True to avoid single-sample batches
    train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
    val_dataset = TensorDataset(X_val_tensor, y_val_tensor)
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, drop_last=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, drop_last=True)
    
    # Suggest optimizer hyperparameters
    lr = trial.suggest_float('learning_rate', 1e-5, 1e-2, log=True)
    weight_decay = trial.suggest_float('weight_decay', 1e-5, 1e-2, log=True)
    
    optimizer = optim.AdamW(model.parameters(), lr=lr, weight_decay=weight_decay)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, 
        mode='min',
        factor=trial.suggest_float('lr_factor', 0.1, 0.9),
        patience=trial.suggest_int('lr_patience', 3, 10)
    )
    
    criterion = nn.MSELoss()
    
    # Training parameters
    n_epochs = trial.suggest_int('n_epochs', 50, 200)
    l2_reg = trial.suggest_float('l2_reg', 1e-5, 1e-2, log=True)
    l1_reg = trial.suggest_float('l1_reg', 1e-6, 1e-3, log=True)
    max_grad_norm = trial.suggest_float('max_grad_norm', 0.1, 2.0)
    
    # Train model
    best_val_r2 = float('-inf')
    patience = trial.suggest_int('patience', 10, 30)
    patience_counter = 0
    
    for epoch in range(n_epochs):
        model.train()
        train_loss = 0.0
        
        for X_batch, y_batch in train_loader:
            optimizer.zero_grad()
            outputs = model(X_batch)
            loss = criterion(outputs, y_batch)
            
            # Add regularization
            l1_loss = sum(torch.norm(param, 1) for param in model.parameters())
            l2_loss = sum(torch.norm(param, 2) for param in model.parameters())
            loss += l1_reg * l1_loss + l2_reg * l2_loss
            
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_grad_norm)
            optimizer.step()
            train_loss += loss.item()
        
        # Validation
        model.eval()
        val_loss = 0.0
        val_preds = []
        val_targets = []
        
        with torch.no_grad():
            for X_batch, y_batch in val_loader:
                outputs = model(X_batch)
                loss = criterion(outputs, y_batch)
                val_loss += loss.item()
                val_preds.extend(outputs.cpu().numpy())
                val_targets.extend(y_batch.cpu().numpy())
        
        # Calculate validation R²
        val_r2 = calculate_r2(np.array(val_targets), np.array(val_preds))
        
        # Update learning rate
        scheduler.step(val_loss)
        
        # Early stopping
        if val_r2 > best_val_r2:
            best_val_r2 = val_r2
            patience_counter = 0
        else:
            patience_counter += 1
            if patience_counter >= patience:
                break
        
        # Report intermediate value
        trial.report(val_r2, epoch)
        
        # Handle pruning based on the intermediate value
        if trial.should_prune():
            raise optuna.TrialPruned()
    
    return best_val_r2

def main():
    # Set device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Load and preprocess data
    print("Loading and preprocessing data...")
    try:
        X_train, X_test, y_train, y_test, feature_scaler, temp_mean, temp_std = load_and_preprocess_data(
            'training_data/metadata.tsv',
            'training_data/combined_features.tsv'
        )
    except Exception as e:
        print(f"Error during data loading: {e}")
        raise
    
    # Convert data to numpy arrays if they are DataFrames
    if isinstance(X_train, pd.DataFrame):
        X_train = X_train.values
    if isinstance(X_test, pd.DataFrame):
        X_test = X_test.values
    if isinstance(y_train, pd.Series):
        y_train = y_train.values
    if isinstance(y_test, pd.Series):
        y_test = y_test.values
    
    # Print data shapes
    print("\nData shapes:")
    print(f"X_train: {X_train.shape}")
    print(f"X_test: {X_test.shape}")
    print(f"y_train: {y_train.shape}")
    print(f"y_test: {y_test.shape}")
    
    # Split training data into train and validation sets
    X_train_final, X_val, y_train_final, y_val = train_test_split(
        X_train, y_train, test_size=0.2, random_state=42
    )
    
    # Create study for hyperparameter optimization
    study = optuna.create_study(
        direction='maximize',
        pruner=optuna.pruners.MedianPruner(
            n_startup_trials=5,
            n_warmup_steps=10,
            interval_steps=1
        )
    )
    
    # Optimize hyperparameters
    print("\nStarting hyperparameter optimization...")
    study.optimize(
        lambda trial: objective(trial, X_train_final, y_train_final, X_val, y_val, device),
        n_trials=50,
        timeout=3600  # 1 hour timeout
    )
    
    # Print best trial results
    print("\nBest trial:")
    trial = study.best_trial
    print(f"  Value: {trial.value:.4f}")
    print("  Params: ")
    for key, value in trial.params.items():
        print(f"    {key}: {value}")
    
    # Train final model with best hyperparameters
    print("\nTraining final model with best hyperparameters...")
    best_model = create_model_from_trial(trial, X_train.shape[1]).to(device)
    
    # Convert data to tensors
    X_train_tensor = torch.FloatTensor(X_train).to(device)
    y_train_tensor = torch.FloatTensor(y_train).view(-1, 1).to(device)
    X_test_tensor = torch.FloatTensor(X_test).to(device)
    y_test_tensor = torch.FloatTensor(y_test).view(-1, 1).to(device)
    
    # Create data loaders
    train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
    test_dataset = TensorDataset(X_test_tensor, y_test_tensor)
    train_loader = DataLoader(train_dataset, batch_size=trial.params['batch_size'], shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=trial.params['batch_size'])
    
    # Initialize optimizer and scheduler
    optimizer = optim.AdamW(
        best_model.parameters(),
        lr=trial.params['learning_rate'],
        weight_decay=trial.params['weight_decay']
    )
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer,
        mode='min',
        factor=trial.params['lr_factor'],
        patience=trial.params['lr_patience']
    )
    
    # Train final model
    train_losses, test_losses, train_r2_scores, test_r2_scores = train_model(
        model=best_model,
        train_loader=train_loader,
        test_loader=test_loader,
        optimizer=optimizer,
        criterion=nn.MSELoss(),
        scheduler=scheduler,
        num_epochs=trial.params['n_epochs'],
        device=device,
        l2_reg=trial.params['l2_reg'],
        l1_reg=trial.params['l1_reg'],
        patience=trial.params['patience'],
        max_grad_norm=trial.params['max_grad_norm']
    )
    
    # Evaluate final model
    best_model.eval()
    with torch.no_grad():
        train_pred = best_model(X_train_tensor).cpu().numpy()
        test_pred = best_model(X_test_tensor).cpu().numpy()
    
    # Calculate metrics
    train_r2 = r2_score(y_train, train_pred)
    test_r2 = r2_score(y_test, test_pred)
    train_rmse = np.sqrt(mean_squared_error(y_train, train_pred))
    test_rmse = np.sqrt(mean_squared_error(y_test, test_pred))
    
    print(f"\nFinal Model Performance:")
    print(f"Train R²: {train_r2:.4f}")
    print(f"Test R²: {test_r2:.4f}")
    print(f"Train RMSE: {train_rmse:.4f}")
    print(f"Test RMSE: {test_rmse:.4f}")
    
    # Plot results
    metrics = {
        'Train R²': train_r2,
        'Test R²': test_r2,
        'Train RMSE': train_rmse,
        'Test RMSE': test_rmse
    }
    plot_results(train_losses, test_losses, train_r2_scores, test_r2_scores, metrics)
    
    # Save model and study
    print("\nSaving model and study...")
    try:
        os.makedirs('models', exist_ok=True)
        torch.save({
            'model_state_dict': best_model.state_dict(),
            'temp_mean': temp_mean,
            'temp_std': temp_std,
            'best_params': trial.params
        }, 'models/temperature_dnn_model_no_engineering.pth')
        
        # Save study
        joblib.dump(study, 'models/optuna_study_no_engineering.pkl')
    except Exception as e:
        print(f"Warning: Failed to save model or study: {e}")
    
    print("Training completed. Check the 'models' directory for the saved model and study.")

if __name__ == "__main__":
    main() 