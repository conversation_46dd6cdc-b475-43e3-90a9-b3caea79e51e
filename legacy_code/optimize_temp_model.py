#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Additional optimizations for the temperature model.
This script contains optimizations derived from the specialized models analysis.
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import numpy as np
import pandas as pd
import os
import logging
from typing import Dict, List, Tuple, Optional, Any

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EarlyStopping:
    """
    Early stopping implementation to prevent overfitting.
    """
    def __init__(self, patience=10, min_delta=0.0, restore_best_weights=True):
        self.patience = patience
        self.min_delta = min_delta
        self.restore_best_weights = restore_best_weights
        self.best_score = None
        self.counter = 0
        self.best_weights = None
    
    def __call__(self, val_loss, model):
        if self.best_score is None:
            self.best_score = val_loss
            if self.restore_best_weights:
                self.best_weights = model.state_dict().copy()
            return False
        
        if val_loss > self.best_score + self.min_delta:
            self.counter += 1
            if self.counter >= self.patience:
                return True
        else:
            self.best_score = val_loss
            if self.restore_best_weights:
                self.best_weights = model.state_dict().copy()
            self.counter = 0
        
        return False
    
    def restore_weights(self, model):
        if self.restore_best_weights and self.best_weights is not None:
            model.load_state_dict(self.best_weights)

def optimize_temperature_model_params():
    """
    Return optimized temperature model parameters based on analysis of successful models.
    
    Returns:
        Dict of optimized parameters
    """
    return {
        'hidden_dims': [512, 512, 384, 256, 128],
        'dropout_rates': [0.3, 0.4, 0.4, 0.5, 0.5],
        'lr': 0.001,
        'batch_size': 64,
        'epochs': 200,  # Increased from previous values
        'patience': 25,
        'use_batch_norm': True,
        'activation': 'relu',
        'use_residual': True,
        'l1_lambda': 1e-5,  # Add L1 regularization
        'weight_decay': 5e-6,  # AdamW weight decay
        'use_gradient_clipping': True,  # Use gradient clipping
        'max_grad_norm': 0.5,  # Maximum gradient norm
        'one_cycle_max_lr': 0.01,  # Maximum learning rate for one cycle
        'warmup_pct': 0.3,  # Percentage of training to use for warmup
    }

def add_l1_regularization(model, loss, l1_lambda):
    """
    Add L1 regularization to the loss.
    
    Args:
        model: PyTorch model
        loss: Current loss
        l1_lambda: L1 regularization coefficient
        
    Returns:
        Updated loss with L1 regularization
    """
    l1_reg = 0.0
    for param in model.parameters():
        l1_reg += torch.norm(param, 1)
    
    return loss + l1_lambda * l1_reg

def apply_gradient_clipping(model, max_grad_norm):
    """
    Apply gradient clipping to prevent exploding gradients.
    
    Args:
        model: PyTorch model
        max_grad_norm: Maximum gradient norm
    """
    torch.nn.utils.clip_grad_norm_(model.parameters(), max_grad_norm)

def setup_one_cycle_lr(optimizer, epochs, steps_per_epoch, max_lr, warmup_pct):
    """
    Set up one cycle learning rate scheduler.
    
    Args:
        optimizer: PyTorch optimizer
        epochs: Number of epochs
        steps_per_epoch: Steps per epoch
        max_lr: Maximum learning rate
        warmup_pct: Percentage of training to use for warmup
        
    Returns:
        One cycle learning rate scheduler
    """
    total_steps = epochs * steps_per_epoch
    return optim.lr_scheduler.OneCycleLR(
        optimizer,
        max_lr=max_lr,
        total_steps=total_steps,
        pct_start=warmup_pct,
        anneal_strategy='cos',
        div_factor=25.0,
        final_div_factor=10000.0
    )
