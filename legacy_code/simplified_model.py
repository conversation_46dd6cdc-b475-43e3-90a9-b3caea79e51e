#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Simplified Temperature Model for DeepMu.

This is a focused implementation that addresses the R² calculation discrepancy
and improves the temperature prediction performance.
"""

import os
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import TensorDataset, DataLoader
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, RobustScaler
import matplotlib.pyplot as plt
import logging
import joblib
import copy

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Set device
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

class TemperatureModel(nn.Module):
    """Improved temperature prediction model with residual connections."""
    
    def __init__(self, input_dim, hidden_dims=[512, 384, 256, 128, 64], dropout_rates=[0.2, 0.3, 0.3, 0.4, 0.5]):
        super().__init__()
        
        self.input_norm = nn.LayerNorm(input_dim)
        
        # Input layer
        layers = [
            nn.Linear(input_dim, hidden_dims[0]),
            nn.BatchNorm1d(hidden_dims[0]),
            nn.ReLU(),
            nn.Dropout(dropout_rates[0])
        ]
        
        # Hidden layers with residual connections
        for i in range(1, len(hidden_dims)):
            # Regular layer
            layers.extend([
                nn.Linear(hidden_dims[i-1], hidden_dims[i]),
                nn.BatchNorm1d(hidden_dims[i]),
                nn.ReLU(),
                nn.Dropout(dropout_rates[i])
            ])
            
        # Output layer
        layers.append(nn.Linear(hidden_dims[-1], 1))
        
        self.layers = nn.ModuleList(layers)
    
    def forward(self, x):
        # Apply input normalization
        x = self.input_norm(x)
        
        # Process through layers
        layer_idx = 0
        while layer_idx < len(self.layers) - 1:  # All except output layer
            if isinstance(self.layers[layer_idx], nn.Linear):
                # Apply linear layer
                lin_out = self.layers[layer_idx](x)
                # Apply batch norm, activation, and dropout
                x = self.layers[layer_idx+3](
                     self.layers[layer_idx+2](
                         self.layers[layer_idx+1](lin_out)
                     )
                )
                # Skip to next block
                layer_idx += 4
            else:
                # Skip any other layer
                layer_idx += 1
        
        # Output layer
        return self.layers[-1](x)

def load_data(feature_file, metadata_file, n_features=800):
    """Load data and select top n_features most relevant features."""
    
    features = pd.read_csv(feature_file, sep='\t', index_col='genome_id')
    metadata = pd.read_csv(metadata_file, sep='\t', index_col='genome_id')
    
    # Get common samples
    common_indices = features.index.intersection(metadata.index)
    features = features.loc[common_indices]
    metadata = metadata.loc[common_indices]
    
    # Handle missing values
    features = features.fillna(0)
    
    # Select top n_features with highest variance
    feature_variance = features.var().sort_values(ascending=False)
    selected_features = feature_variance.index[:n_features].tolist()
    
    return features[selected_features], metadata

def calculate_metrics(y_true, y_pred):
    """Calculate metrics with consistent R² implementation."""
    
    # Ensure numpy arrays
    y_true = np.array(y_true).flatten()
    y_pred = np.array(y_pred).flatten()
    
    # Calculate metrics using manual R² to ensure consistency
    y_mean = np.mean(y_true)
    ss_total = np.sum((y_true - y_mean) ** 2)
    ss_residual = np.sum((y_true - y_pred) ** 2)
    
    # Manual R² calculation
    r2_manual = 1 - (ss_residual / ss_total) if ss_total > 0 else 0.0
    
    # Also calculate sklearn's R² for comparison
    r2_sklearn = r2_score(y_true, y_pred)
    
    # Calculate additional metrics
    rmse = np.sqrt(mean_squared_error(y_true, y_pred))
    mae = mean_absolute_error(y_true, y_pred)
    correlation = np.corrcoef(y_true, y_pred)[0, 1]
    
    # Log detailed information
    logger.info(f"R² calculation details:")
    logger.info(f"  Mean of true values: {y_mean:.6f}")
    logger.info(f"  Sum squares total: {ss_total:.6f}")
    logger.info(f"  Sum squares residual: {ss_residual:.6f}")
    logger.info(f"  Manual R²: {r2_manual:.6f}")
    logger.info(f"  sklearn R²: {r2_sklearn:.6f}")
    logger.info(f"  RMSE: {rmse:.6f}")
    logger.info(f"  MAE: {mae:.6f}")
    logger.info(f"  Correlation: {correlation:.6f}")
    
    return {
        "r2": r2_manual,
        "r2_sklearn": r2_sklearn,
        "rmse": rmse,
        "mae": mae,
        "correlation": correlation
    }

def train_model(features, temperatures, n_features=800, output_dir="models/temp_model_fixed"):
    """Train a temperature prediction model with improved methodology."""
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Split data
    X_train, X_val, y_train, y_val = train_test_split(
        features, temperatures, test_size=0.2, random_state=42
    )
    
    # Scale features
    scaler = RobustScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_val_scaled = scaler.transform(X_val)
    
    # Create datasets and dataloaders
    train_dataset = TensorDataset(
        torch.FloatTensor(X_train_scaled),
        torch.FloatTensor(y_train.values.reshape(-1, 1))
    )
    
    val_dataset = TensorDataset(
        torch.FloatTensor(X_val_scaled),
        torch.FloatTensor(y_val.values.reshape(-1, 1))
    )
    
    train_loader = DataLoader(train_dataset, batch_size=64, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=128)
    
    # Initialize model
    model = TemperatureModel(input_dim=X_train.shape[1]).to(device)
    
    # Set up optimizer and scheduler
    optimizer = optim.AdamW(
        model.parameters(),
        lr=0.0005,
        weight_decay=1e-7,
        eps=1e-8
    )
    
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=0.5, patience=5, verbose=True
    )
    
    # Training loop
    best_val_r2 = float('-inf')
    best_model_state = None
    
    for epoch in range(200):
        # Training
        model.train()
        train_loss = 0.0
        
        for X_batch, y_batch in train_loader:
            X_batch = X_batch.to(device)
            y_batch = y_batch.to(device)
            
            optimizer.zero_grad()
            y_pred = model(X_batch)
            
            loss = F.mse_loss(y_pred, y_batch)
            loss.backward()
            
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            optimizer.step()
            train_loss += loss.item()
        
        train_loss /= len(train_loader)
        
        # Validation
        model.eval()
        val_loss = 0.0
        
        all_val_preds = []
        all_val_true = []
        
        with torch.no_grad():
            for X_batch, y_batch in val_loader:
                X_batch = X_batch.to(device)
                y_batch = y_batch.to(device)
                
                y_pred = model(X_batch)
                val_loss += F.mse_loss(y_pred, y_batch).item() * len(y_batch)
                
                all_val_preds.extend(y_pred.cpu().numpy().flatten())
                all_val_true.extend(y_batch.cpu().numpy().flatten())
        
        val_loss /= len(val_loader.dataset)
        
        # Update scheduler
        scheduler.step(val_loss)
        
        # Calculate metrics
        val_metrics = calculate_metrics(all_val_true, all_val_preds)
        
        # Log progress every 10 epochs
        if (epoch + 1) % 10 == 0:
            logger.info(f"Epoch {epoch+1}/200:")
            logger.info(f"  Train Loss: {train_loss:.6f}")
            logger.info(f"  Val Loss: {val_loss:.6f}")
            logger.info(f"  Val R² (manual): {val_metrics['r2']:.6f}")
            logger.info(f"  Val R² (sklearn): {val_metrics['r2_sklearn']:.6f}")
            logger.info(f"  Val RMSE: {val_metrics['rmse']:.6f}")
            logger.info(f"  Val MAE: {val_metrics['mae']:.6f}")
            
            # Log current learning rate
            current_lr = optimizer.param_groups[0]['lr']
            logger.info(f"  Learning rate: {current_lr:.8f}")
        
        # Save best model
        if val_metrics['r2'] > best_val_r2:
            best_val_r2 = val_metrics['r2']
            best_model_state = copy.deepcopy(model.state_dict())
            logger.info(f"New best model with R² = {best_val_r2:.6f}")
    
    # Load best model
    model.load_state_dict(best_model_state)
    
    # Final evaluation
    model.eval()
    with torch.no_grad():
        # Validate on full datasets
        X_train_tensor = torch.FloatTensor(X_train_scaled).to(device)
        X_val_tensor = torch.FloatTensor(X_val_scaled).to(device)
        
        train_preds = model(X_train_tensor).cpu().numpy().flatten()
        val_preds = model(X_val_tensor).cpu().numpy().flatten()
    
    # Calculate final metrics
    train_metrics = calculate_metrics(y_train, train_preds)
    val_metrics = calculate_metrics(y_val, val_preds)
    
    logger.info("Final model performance:")
    logger.info(f"  Train R² (manual): {train_metrics['r2']:.6f}")
    logger.info(f"  Val R² (manual): {val_metrics['r2']:.6f}")
    logger.info(f"  Train RMSE: {train_metrics['rmse']:.6f}")
    logger.info(f"  Val RMSE: {val_metrics['rmse']:.6f}")
    
    # Save model and artifacts
    torch.save(model.state_dict(), os.path.join(output_dir, "model.pt"))
    joblib.dump(scaler, os.path.join(output_dir, "scaler.pkl"))
    
    # Save model architecture info
    model_info = {
        "input_dim": X_train.shape[1],
        "hidden_dims": [512, 384, 256, 128, 64],
        "dropout_rates": [0.2, 0.3, 0.3, 0.4, 0.5]
    }
    joblib.dump(model_info, os.path.join(output_dir, "model_info.pkl"))
    
    return model, scaler, train_metrics, val_metrics

def main():
    """Run temperature model training with improved R² calculation."""
    # Load data
    logger.info("Loading data...")
    features, metadata = load_data(
        "training_data/combined_features.tsv",
        "training_data/metadata.tsv",
        n_features=800
    )
    
    # Train model
    logger.info(f"Training model with {features.shape[1]} features...")
    model, scaler, train_metrics, val_metrics = train_model(
        features,
        metadata['optimal_temperature'],
        n_features=features.shape[1],
        output_dir="models/temp_model_fixed"
    )
    
    logger.info("Training complete!")
    logger.info(f"Final model performance:")
    logger.info(f"  Training R²: {train_metrics['r2']:.6f}")
    logger.info(f"  Validation R²: {val_metrics['r2']:.6f}")
    logger.info(f"  Training RMSE: {train_metrics['rmse']:.6f}")
    logger.info(f"  Validation RMSE: {val_metrics['rmse']:.6f}")
    logger.info(f"Model and artifacts saved to models/temp_model_fixed")

if __name__ == "__main__":
    main() 