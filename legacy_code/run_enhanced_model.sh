#!/bin/bash

# Run the enhanced temperature model for DeepMu
# This script runs the improved temperature model with increased regularization
# and feature transformations for non-Gaussian features

echo "Running enhanced temperature model..."
mkdir -p models/enhanced_temp_model/plots

# Run the training script
python enhanced_temp_model.py

# Check if training was successful
if [ $? -eq 0 ]; then
    echo "Training completed successfully!"
    echo "Model saved to models/enhanced_temp_model/"
    
    # Display number of transformed features
    echo "Checking feature transformations..."
    if [ -f "models/enhanced_temp_model/feature_transformers.pkl" ]; then
        python -c "import joblib; transformers = joblib.load('models/enhanced_temp_model/feature_transformers.pkl'); print(f'Applied transformations to {len(transformers)} non-Gaussian features')"
    fi
    
    echo "Feature distribution visualizations saved to models/enhanced_temp_model/plots/"
    echo "Model evaluation plots are available in models/enhanced_temp_model/plots/"
    
    echo "Done!"
else
    echo "Training failed. See logs for details."
fi 