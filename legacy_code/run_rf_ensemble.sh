#!/bin/bash

# Run Random Forest Ensemble Temperature Model

# Set up environment
PYTHONPATH=$(pwd):$PYTHONPATH
export PYTHONPATH

# Create data and output directories
mkdir -p models/rf_ensemble_temp
mkdir -p models/rf_ensemble_temp/plots

# Log start time
echo "Starting random forest ensemble temperature model training at $(date)"

# Run the model
python rf_ensemble_temp_model.py \
    --features training_data/combined_features.tsv \
    --metadata training_data/metadata.tsv \
    --output_dir models/rf_ensemble_temp \
    --n_features 1000 \
    --n_estimators 300 \
    --max_depth 20 \
    --optimize_hyperparams true \
    --feature_selection true

# Log completion
echo "Training completed at $(date)"

# Generate evaluation report if available
if [ -f "models/rf_ensemble_temp/metrics.joblib" ]; then
    echo "Generating evaluation report..."
    python -c "
import joblib
import pandas as pd
import os

# Load metrics
metrics = joblib.load('models/rf_ensemble_temp/metrics.joblib')

# Create report
report = ['# Random Forest Ensemble Temperature Model - Evaluation Report\n']
report.append('## Overall Metrics\n')

for model_name, model_metrics in metrics.items():
    if model_name == 'ensemble':
        report.append(f'### Ensemble Model\n')
    else:
        report.append(f'### {model_name.upper()} Model\n')
    
    report.append(f'- Training R²: {model_metrics.get(\"train_r2\", 0):.6f}\n')
    report.append(f'- Validation R²: {model_metrics.get(\"val_r2\", 0):.6f}\n')
    report.append(f'- Training RMSE: {model_metrics.get(\"train_rmse\", 0):.4f}°C\n')
    report.append(f'- Validation RMSE: {model_metrics.get(\"val_rmse\", 0):.4f}°C\n')
    report.append('\n')

# Add feature importance
if os.path.exists('models/rf_ensemble_temp/feature_importance.csv'):
    importance = pd.read_csv('models/rf_ensemble_temp/feature_importance.csv')
    top_features = importance.head(20)
    
    report.append('## Top 20 Most Important Features\n')
    report.append('| Feature | Importance |\n')
    report.append('|---------|------------|\n')
    
    for _, row in top_features.iterrows():
        report.append(f'| {row[\"Feature\"]} | {row[\"Importance\"]:.6f} |\n')

# Add ensemble weights
if os.path.exists('models/rf_ensemble_temp/ensemble_weights.joblib'):
    weights = joblib.load('models/rf_ensemble_temp/ensemble_weights.joblib')
    
    report.append('\n## Ensemble Weights\n')
    report.append('| Model | Weight |\n')
    report.append('|-------|--------|\n')
    
    for model, weight in weights.items():
        report.append(f'| {model.upper()} | {weight:.4f} |\n')

# Write report
with open('models/rf_ensemble_temp/evaluation_report.md', 'w') as f:
    f.writelines(report)

print('Evaluation report generated at models/rf_ensemble_temp/evaluation_report.md')
"
fi

echo "Done!" 