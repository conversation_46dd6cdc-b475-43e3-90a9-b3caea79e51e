#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Ensemble model for combining multiple prediction models.
This module implements a weighted ensemble that combines predictions
from different model types (Random Forest, DNN) to improve performance.
"""

import os
import joblib
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from typing import List, Dict, Optional, Union, Tuple
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class EnsembleModel:
    """
    Ensemble model that combines predictions from multiple models with weighted averaging.
    
    Attributes:
        models: List of models to ensemble
        weights: List of weights for each model
        model_names: List of model names for reporting
    """
    
    def __init__(self, 
                 models: List, 
                 weights: Optional[List[float]] = None,
                 model_names: Optional[List[str]] = None):
        """
        Initialize ensemble model.
        
        Args:
            models: List of prediction models (must implement predict method)
            weights: Optional list of weights for each model (defaults to equal weighting)
            model_names: Optional list of model names for reporting
        """
        self.models = models
        
        # If weights not provided, use equal weighting
        if weights is None:
            self.weights = [1/len(models)] * len(models)
        else:
            # Normalize weights to sum to 1
            total = sum(weights)
            self.weights = [w/total for w in weights]
            
        # Default model names if not provided
        if model_names is None:
            self.model_names = [f"Model_{i}" for i in range(len(models))]
        else:
            self.model_names = model_names
            
        logger.info(f"Initialized ensemble with {len(models)} models and weights: {self.weights}")
    
    def predict(self, X: pd.DataFrame) -> np.ndarray:
        """
        Generate ensemble predictions by weighted averaging of individual model predictions.
        
        Args:
            X: Feature dataframe
            
        Returns:
            numpy array of predictions
        """
        logger.info("Generating ensemble predictions...")
        
        predictions = []
        
        # Get predictions from each model
        for i, model in enumerate(self.models):
            model_pred = model.predict(X)
            predictions.append(model_pred)
            logger.debug(f"{self.model_names[i]} predictions generated, shape: {model_pred.shape}")
        
        # Combine predictions with weighted average
        weighted_pred = np.zeros_like(predictions[0])
        for i, pred in enumerate(predictions):
            weighted_pred += self.weights[i] * pred
            
        return weighted_pred
    
    def evaluate(self, X: pd.DataFrame, y: Union[pd.Series, np.ndarray]) -> Dict[str, float]:
        """
        Evaluate ensemble model performance.
        
        Args:
            X: Feature dataframe
            y: Target values
            
        Returns:
            Dictionary of evaluation metrics
        """
        logger.info("Evaluating ensemble model...")
        
        # Generate predictions
        y_pred = self.predict(X)
        
        # Calculate metrics
        metrics = {
            'R2': r2_score(y, y_pred),
            'RMSE': np.sqrt(mean_squared_error(y, y_pred)),
            'MAE': mean_absolute_error(y, y_pred)
        }
        
        logger.info(f"Ensemble metrics - R²: {metrics['R2']:.4f}, RMSE: {metrics['RMSE']:.4f}, MAE: {metrics['MAE']:.4f}")
        
        return metrics
    
    def evaluate_with_components(self, X: pd.DataFrame, y: Union[pd.Series, np.ndarray]) -> Dict[str, Dict[str, float]]:
        """
        Evaluate ensemble model and individual component models.
        
        Args:
            X: Feature dataframe
            y: Target values
            
        Returns:
            Dictionary of evaluation metrics for ensemble and component models
        """
        logger.info("Evaluating ensemble and component models...")
        
        # Evaluate ensemble
        ensemble_metrics = self.evaluate(X, y)
        
        # Evaluate individual models
        component_metrics = {}
        for i, model in enumerate(self.models):
            name = self.model_names[i]
            pred = model.predict(X)
            
            metrics = {
                'R2': r2_score(y, pred),
                'RMSE': np.sqrt(mean_squared_error(y, pred)),
                'MAE': mean_absolute_error(y, pred)
            }
            
            component_metrics[name] = metrics
            logger.info(f"{name} metrics - R²: {metrics['R2']:.4f}, RMSE: {metrics['RMSE']:.4f}, MAE: {metrics['MAE']:.4f}")
        
        # Combine results
        all_metrics = {
            'Ensemble': ensemble_metrics,
            **component_metrics
        }
        
        return all_metrics
    
    def save(self, path: str) -> None:
        """
        Save ensemble model.
        
        Args:
            path: Path to save model
        """
        logger.info(f"Saving ensemble model to {path}")
        joblib.dump(self, path)
    
    @classmethod
    def load(cls, path: str) -> 'EnsembleModel':
        """
        Load ensemble model.
        
        Args:
            path: Path to load model from
            
        Returns:
            Loaded ensemble model instance
        """
        logger.info(f"Loading ensemble model from {path}")
        return joblib.load(path)
    
    def plot_comparison(self, X: pd.DataFrame, y: Union[pd.Series, np.ndarray], 
                       output_path: Optional[str] = None) -> None:
        """
        Plot comparison of ensemble and component model predictions.
        
        Args:
            X: Feature dataframe
            y: Target values
            output_path: Optional path to save plot
        """
        logger.info("Generating ensemble comparison plot...")
        
        # Get all metrics
        metrics = self.evaluate_with_components(X, y)
        
        # Extract R2 and RMSE values
        model_names = list(metrics.keys())
        r2_values = [metrics[model]['R2'] for model in model_names]
        rmse_values = [metrics[model]['RMSE'] for model in model_names]
        
        # Create plot
        plt.figure(figsize=(12, 6))
        
        # R2 plot
        plt.subplot(1, 2, 1)
        sns.barplot(x=model_names, y=r2_values)
        plt.title('R² Comparison')
        plt.ylim(0, 1)
        plt.xticks(rotation=45)
        
        # RMSE plot
        plt.subplot(1, 2, 2)
        sns.barplot(x=model_names, y=rmse_values)
        plt.title('RMSE Comparison')
        plt.xticks(rotation=45)
        
        plt.tight_layout()
        
        # Save or show plot
        if output_path:
            plt.savefig(output_path)
            logger.info(f"Comparison plot saved to {output_path}")
        else:
            plt.show()


class ModelWrapper:
    """
    Wrapper for sklearn models to ensure consistent interface.
    
    Attributes:
        model: The wrapped model
        name: Optional name for the model
    """
    
    def __init__(self, model, name: Optional[str] = None):
        """
        Initialize model wrapper.
        
        Args:
            model: Model to wrap (must have predict method)
            name: Optional name for the model
        """
        self.model = model
        self.name = name
        
    def predict(self, X: pd.DataFrame) -> np.ndarray:
        """
        Generate predictions.
        
        Args:
            X: Feature dataframe
            
        Returns:
            numpy array of predictions
        """
        return self.model.predict(X)


def create_ensemble_from_models(models_dict: Dict[str, Tuple[object, float]], 
                               output_path: Optional[str] = None) -> EnsembleModel:
    """
    Create ensemble from dictionary of models with weights.
    
    Args:
        models_dict: Dictionary mapping model names to (model, weight) tuples
        output_path: Optional path to save ensemble model
        
    Returns:
        Initialized ensemble model
    """
    models = []
    weights = []
    names = []
    
    for name, (model, weight) in models_dict.items():
        # Wrap model if needed
        if not hasattr(model, 'predict'):
            model = ModelWrapper(model)
            
        models.append(model)
        weights.append(weight)
        names.append(name)
    
    # Create ensemble
    ensemble = EnsembleModel(models, weights, names)
    
    # Save if path provided
    if output_path:
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        ensemble.save(output_path)
    
    return ensemble
