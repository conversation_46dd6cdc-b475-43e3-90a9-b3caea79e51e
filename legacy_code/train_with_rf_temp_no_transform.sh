#!/bin/bash

# Train the DeepMu hybrid model with Random Forest for temperature prediction
# without any transformation for temperature values

echo "=========================================================="
echo "   Training DeepMu hybrid model with RF for temperature   "
echo "   NO transformation for temperature values               "
echo "=========================================================="

# Set output directory
OUTPUT_DIR="models/deepmu_rf_temp_notransform_model"

# Run the model training script
python rf_temp_hybrid_model.py \
    --feature_file="./training_data/combined_features.tsv" \
    --metadata_file="./training_data/metadata.tsv" \
    --output_dir=$OUTPUT_DIR \
    --n_growth_features=250 \
    --n_temp_features=500 \
    --use_rf_temp_model \
    --rf_n_estimators=300 \
    --rf_max_depth=20 \
    --rf_min_samples_leaf=2 \
    --temp_transform=none \
    --growth_transform=log2 \
    --batch_size=128 \
    --epochs=300 \
    --patience=20

echo -e "\n========================================================"
echo "   Training completed successfully!   "
echo "========================================================"
echo -e "\nModel saved to: $OUTPUT_DIR"
echo -e "\nDone." 