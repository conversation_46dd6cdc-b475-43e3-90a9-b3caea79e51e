#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Optimize Tab Transformer model for growth rate and optimal temperature prediction.
"""

import os
import argparse
import logging
import numpy as np
import pandas as pd
import torch
from pytorch_tabular import TabularModel
from pytorch_tabular.models import TabTransformerConfig
from pytorch_tabular.config import (
    DataConfig,
    OptimizerConfig,
    TrainerConfig
)
from sklearn.model_selection import KFold
from sklearn.metrics import mean_squared_error, r2_score
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_data(feature_file, metadata_file):
    """Load and prepare feature and metadata files."""
    logger.info(f"Loading features from {feature_file}")
    features_df = pd.read_csv(feature_file, sep='\t', index_col='genome_id')
    
    # Check for NaN values in features
    nan_count = features_df.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in features. Filling with 0.")
        features_df = features_df.fillna(0)
    
    # Check for infinite values
    inf_count = np.isinf(features_df.values).sum()
    if inf_count > 0:
        logger.warning(f"Found {inf_count} infinite values in features. Replacing with max/min values.")
        features_df = features_df.replace([np.inf, -np.inf], np.nan)
        features_df = features_df.fillna(features_df.max())

    logger.info(f"Loading metadata from {metadata_file}")
    metadata_df = pd.read_csv(metadata_file, sep='\t', index_col='genome_id')
    
    # Check for NaN values in metadata
    nan_count = metadata_df.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in metadata. Filling with 0.")
        metadata_df = metadata_df.fillna(0)

    # Get common genomes
    common_genomes = list(set(features_df.index) & set(metadata_df.index))
    logger.info(f"Found {len(common_genomes)} common genomes")

    # Filter to common genomes
    features_df = features_df.loc[common_genomes]
    metadata_df = metadata_df.loc[common_genomes]
    
    # Merge categorical columns from metadata into features
    categorical_cols = ['kingdom', 'codon_table']
    features_df = features_df.join(metadata_df[categorical_cols])
    
    # Ensure continuous columns are float type
    continuous_cols = [col for col in features_df.columns if col not in categorical_cols + ['genome_id']]
    features_df[continuous_cols] = features_df[continuous_cols].astype(float)
    
    # Prepare target columns
    target_cols = ['growth_rate', 'optimal_temperature']
    
    return features_df, metadata_df[target_cols]

def get_model_config(features_df):
    """Return optimized configuration for Tab Transformer model."""
    data_config = DataConfig(
        target=['growth_rate', 'optimal_temperature'],
        continuous_cols=[col for col in features_df.columns if col not in ['kingdom', 'codon_table', 'genome_id']],
        categorical_cols=['kingdom', 'codon_table'],
        continuous_feature_transform="quantile_normal",
        normalize_continuous_features=True
    )
    
    # Calculate steps_per_epoch based on batch size and data size
    batch_size = 32
    steps_per_epoch = len(features_df) // batch_size
    
    optimizer_config = OptimizerConfig(
        optimizer="AdamW",
        optimizer_params={
            "weight_decay": 0.01,
            "betas": (0.9, 0.999)
        },
        lr_scheduler="OneCycleLR",
        lr_scheduler_params={
            "max_lr": 1e-3,
            "epochs": 100,
            "pct_start": 0.3,
            "div_factor": 10.0,
            "final_div_factor": 100.0,
            "steps_per_epoch": steps_per_epoch
        }
    )
    
    trainer_config = TrainerConfig(
        batch_size=batch_size,
        max_epochs=100,
        early_stopping="valid_loss",
        early_stopping_patience=10,
        checkpoints="valid_loss",
        accelerator="auto",
        devices=1,
        gradient_clip_val=1.0
    )
    
    model_config = TabTransformerConfig(
        task="regression",
        input_embed_dim=64,  # Increased from 32
        num_heads=8,
        num_attn_blocks=8,  # Increased from 6
        attn_dropout=0.2,  # Dropout for attention layers
        ff_dropout=0.2,  # Dropout for feed-forward layers
        embedding_dropout=0.2,  # Dropout for embeddings
        add_norm_dropout=0.2,  # Dropout for layer normalization
        learning_rate=1e-3,
        ff_hidden_multiplier=4,
        share_embedding=True
    )
    
    return data_config, optimizer_config, trainer_config, model_config

def train_with_cross_validation(data, n_splits=5):
    """Train model with k-fold cross validation."""
    kf = KFold(n_splits=n_splits, shuffle=True, random_state=42)
    fold_results = []
    models = []
    
    for fold, (train_idx, val_idx) in enumerate(kf.split(data)):
        logger.info(f"Training fold {fold + 1}/{n_splits}")
        
        # Split data
        train_data = data.iloc[train_idx]
        val_data = data.iloc[val_idx]
        
        # Get configurations
        data_config, optimizer_config, trainer_config, model_config = get_model_config(train_data)
        
        # Create and train model
        tabular_model = TabularModel(
            data_config=data_config,
            model_config=model_config,
            optimizer_config=optimizer_config,
            trainer_config=trainer_config
        )
        
        tabular_model.fit(train=train_data, validation=val_data)
        
        # Evaluate model
        result = tabular_model.evaluate(val_data)
        fold_results.append(result)
        models.append(tabular_model)
        
        logger.info(f"Fold {fold + 1} results: {result}")
    
    return models, fold_results

def ensemble_predict(models, data):
    """Make predictions using ensemble of models."""
    predictions = []
    for model in models:
        pred = model.predict(data)
        predictions.append(pred)
    
    # Average predictions
    ensemble_pred = np.mean(predictions, axis=0)
    return ensemble_pred

def plot_results(results, output_dir):
    """Plot model results."""
    # Extract metrics from the first result
    metrics = ['test_loss', 'test_mean_squared_error']
    folds = range(1, len(results) + 1)
    
    fig, axes = plt.subplots(1, 2, figsize=(15, 5))
    
    for i, metric in enumerate(metrics):
        values = [result[0][metric] for result in results]  # Access the first dictionary in each result
        sns.barplot(x=folds, y=values, ax=axes[i])
        axes[i].set_title(f'Cross-validation Results - {metric}')
        axes[i].set_xlabel('Fold')
        axes[i].set_ylabel(metric)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'cv_results.png'))
    plt.close()
    
    # Also plot individual target metrics
    target_metrics = ['test_loss_0', 'test_loss_1', 'test_mean_squared_error_0', 'test_mean_squared_error_1']
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    axes = axes.flatten()
    
    for i, metric in enumerate(target_metrics):
        values = [result[0][metric] for result in results]
        sns.barplot(x=folds, y=values, ax=axes[i])
        axes[i].set_title(f'Cross-validation Results - {metric}')
        axes[i].set_xlabel('Fold')
        axes[i].set_ylabel(metric)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'cv_results_targets.png'))
    plt.close()

def main():
    parser = argparse.ArgumentParser(description='Optimize Tab Transformer model')
    parser.add_argument('--feature_file', type=str, required=True,
                        help='Path to feature file')
    parser.add_argument('--metadata_file', type=str, required=True,
                        help='Path to metadata file')
    parser.add_argument('--output_dir', type=str, default='results',
                        help='Directory to save results')
    args = parser.parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Load data
    features_df, targets_df = load_data(args.feature_file, args.metadata_file)
    data = pd.concat([features_df, targets_df], axis=1)
    
    # Train with cross-validation
    models, results = train_with_cross_validation(data)
    
    # Plot results
    plot_results(results, args.output_dir)
    
    # Save results
    results_df = pd.DataFrame(results)
    results_df.to_csv(os.path.join(args.output_dir, 'cv_results.csv'))
    
    # Save models
    for i, model in enumerate(models):
        model_path = os.path.join(args.output_dir, f'model_fold_{i+1}')
        model.save_model(model_path)
    
    logger.info("Model optimization completed!")

if __name__ == '__main__':
    main() 