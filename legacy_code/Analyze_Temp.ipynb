{"cells": [{"cell_type": "code", "execution_count": 6, "id": "02a2f2c3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Raw temperature stats:\n", "  Min: 0.00\n", "  Max: 106.00\n", "  Mean: 34.15\n", "  Std: 11.40\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "# Load your metadata file (adjust the path as needed)\n", "metadata = pd.read_csv('./training_data/metadata.tsv', sep='\\t', index_col=0)\n", "\n", "# 1. Raw temperature statistics\n", "temps = metadata['optimal_temperature'].values\n", "print(\"Raw temperature stats:\")\n", "print(f\"  Min: {np.min(temps):.2f}\")\n", "print(f\"  Max: {np.max(temps):.2f}\")\n", "print(f\"  Mean: {np.mean(temps):.2f}\")\n", "print(f\"  Std: {np.std(temps):.2f}\")\n", "\n", "plt.hist(temps, bins=30, color='skyblue', edgecolor='black')\n", "plt.title('Raw Temperature Distribution')\n", "plt.xlabel('Temperature')\n", "plt.ylabel('Count')\n", "plt.show()\n", "\n", "# 2. Transformed temperature statistics (adjust transform as in your config)\n", "def temp_transform(y, transform_type='sqrt'):\n", "    if transform_type == 'none':\n", "        return y\n", "    elif transform_type == 'log':\n", "        return np.log(y+0.00001)\n", "    elif transform_type == 'log2':\n", "        return np.log2(y+0.00001)\n", "    elif transform_type == 'log10':\n", "        return np.log10(y+0.00001)\n", "    elif transform_type == 'sqrt':\n", "        return np.sqrt(y)\n", "    else:\n", "        raise ValueError(f\"Unknown transformation: {transform_type}\")\n"]}, {"cell_type": "code", "execution_count": 4, "id": "94ce5ab2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Transformed ('sqrt') temperature stats:\n", "  Min: 0.00\n", "  Max: 10.30\n", "  Mean: 5.77\n", "  Std: 0.91\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["trans_type = 'sqrt'  # Change if you use a different transform\n", "temps_trans = temp_transform(temps, trans_type)\n", "print(f\"\\nTransformed ('{trans_type}') temperature stats:\")\n", "print(f\"  Min: {np.min(temps_trans):.2f}\")\n", "print(f\"  Max: {np.max(temps_trans):.2f}\")\n", "print(f\"  Mean: {np.mean(temps_trans):.2f}\")\n", "print(f\"  Std: {np.std(temps_trans):.2f}\")\n", "\n", "plt.hist(temps_trans, bins=30, color='salmon', edgecolor='black')\n", "plt.title(f\"Transformed ('{trans_type}') Temperature Distribution\")\n", "plt.xlabel('Transformed Temperature')\n", "plt.ylabel('Count')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 7, "id": "2263925b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Transformed ('log') temperature stats:\n", "  Min: -11.51\n", "  Max: 4.66\n", "  Mean: 3.48\n", "  Std: 0.37\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["trans_type = 'log'  # Change if you use a different transform\n", "temps_trans = temp_transform(temps, trans_type)\n", "print(f\"\\nTransformed ('{trans_type}') temperature stats:\")\n", "print(f\"  Min: {np.min(temps_trans):.2f}\")\n", "print(f\"  Max: {np.max(temps_trans):.2f}\")\n", "print(f\"  Mean: {np.mean(temps_trans):.2f}\")\n", "print(f\"  Std: {np.std(temps_trans):.2f}\")\n", "\n", "plt.hist(temps_trans, bins=30, color='salmon', edgecolor='black')\n", "plt.title(f\"Transformed ('{trans_type}') Temperature Distribution\")\n", "plt.xlabel('Transformed Temperature')\n", "plt.ylabel('Count')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 8, "id": "bb9162ef", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== Temperature Distribution Statistics ===\n", "Number of samples: 6052\n", "Mean: 34.148\n", "Median: 33.500\n", "Std: 11.402\n", "Min: 0.000\n", "Max: 106.000\n", "Skewness: 2.135\n", "Kurtosis: 7.632\n", "\n", "NaN values: 0 (0.00%)\n", "Infinite values: 0 (0.00%)\n", "\n", "Outliers (IQR method): 485 (8.01%)\n", "\n", "=== Transformed Data Analysis ===\n", "\n", "Log-transformed statistics:\n", "Mean: 3.514\n", "Std: 0.305\n", "Skewness: -0.687\n", "\n", "Box-Cox transformed statistics (lambda = 0.316):\n", "Mean: 6.389\n", "Std: 0.943\n", "Skewness: 0.296\n", "\n", "Z-score normalized statistics:\n", "Mean: 0.000\n", "Std: 1.000\n", "Skewness: 2.135\n"]}], "source": ["!python analyze_temperature_distribution.py"]}, {"cell_type": "markdown", "id": "48fa5cf1", "metadata": {}, "source": ["### Transformed Data:\n", "- Log transform: Skewness becomes negative (-0.69), distribution is more symmetric.\n", "- Box-<PERSON> transform: Skewness is reduced (0.30), distribution is closer to normal.\n", "- Z-score normalization: Skewness remains high (2.14), as this only rescales."]}, {"cell_type": "markdown", "id": "feee2e81", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}