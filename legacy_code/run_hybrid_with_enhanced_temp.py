#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Run Hybrid Model with Enhanced Temperature DNN.
This script trains a hybrid model that combines the successful LightGBM+ensemble approach for growth rate prediction
with the optimized RegularizedDNN approach for temperature prediction.
"""

import os
import argparse
import logging
import numpy as np
import pandas as pd
import torch
from sklearn.model_selection import train_test_split
from typing import Dict, List, Tuple, Optional

from train_hybrid_with_enhanced_nn import load_data, prepare_data_with_selective_transform, train_model, evaluate_model

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """Main function."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Train and evaluate hybrid model with enhanced temperature DNN')
    parser.add_argument('--feature_file', type=str, required=True, help='Path to feature file (TSV)')
    parser.add_argument('--metadata_file', type=str, required=True, help='Path to metadata file (TSV)')
    parser.add_argument('--output_dir', type=str, required=True, help='Directory to save outputs')
    parser.add_argument('--n_features', type=int, default=150, help='Number of features to use')
    
    # Temperature DNN specific arguments - using optimal values from evaluate_enhanced_dnn_temperature.py
    parser.add_argument('--temp_nn_hidden_dims', type=str, default='512,512,384,256,128',
                       help='Comma-separated list of hidden layer dimensions for temperature neural network')
    parser.add_argument('--temp_nn_dropout_rates', type=str, default='0.3,0.4,0.4,0.5,0.5',
                       help='Comma-separated list of dropout rates for temperature neural network')
    parser.add_argument('--temp_nn_lr', type=float, default=0.001, 
                       help='Learning rate for temperature neural network')
    parser.add_argument('--temp_nn_batch_size', type=int, default=64,
                       help='Batch size for temperature neural network')
    parser.add_argument('--temp_nn_epochs', type=int, default=150,
                       help='Maximum number of epochs for temperature neural network')
    parser.add_argument('--temp_nn_patience', type=int, default=20,
                       help='Early stopping patience for temperature neural network')
    
    parser.add_argument('--seed', type=int, default=42, help='Random seed')
    
    args = parser.parse_args()
    
    # Set random seeds for reproducibility
    np.random.seed(args.seed)
    torch.manual_seed(args.seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(args.seed)
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Parse hidden dims and dropout rates
    hidden_dims = [int(x) for x in args.temp_nn_hidden_dims.split(',')]
    dropout_rates = [float(x) for x in args.temp_nn_dropout_rates.split(',')]
    
    logger.info(f"Using hidden dims: {hidden_dims}")
    logger.info(f"Using dropout rates: {dropout_rates}")
    
    # Load data
    features, metadata = load_data(args.feature_file, args.metadata_file)
    
    # Prepare data with selective transformation
    (X_train, y_growth_train, y_temp_train, 
     X_val, y_growth_val, y_temp_val, 
     X_test, y_growth_test, y_temp_test, 
     selected_features, transform_info) = prepare_data_with_selective_transform(
        features, metadata, args.n_features, args.output_dir
    )
    
    # Log dataset information
    logger.info(f"Training set: {X_train.shape[0]} samples, {X_train.shape[1]} features")
    logger.info(f"Validation set: {X_val.shape[0]} samples, {X_val.shape[1]} features")
    logger.info(f"Test set: {X_test.shape[0]} samples, {X_test.shape[1]} features")
    
    # Train model with optimized parameters for temperature prediction
    from hybrid_model_with_enhanced_nn import HybridModelWithEnhancedNN
    
    model = HybridModelWithEnhancedNN(
        # Growth rate ensemble parameters - keep as is
        growth_ensemble_weight_lr=0.01,
        growth_ensemble_weight_epochs=50,
        
        # Temperature neural network parameters - optimized
        temp_nn_hidden_dims=hidden_dims,
        temp_nn_dropout_rates=dropout_rates,
        temp_nn_lr=args.temp_nn_lr,
        temp_nn_batch_size=args.temp_nn_batch_size,
        temp_nn_epochs=args.temp_nn_epochs,
        temp_nn_patience=args.temp_nn_patience,
        
        # General parameters
        variance_percentile=25
    )
    
    # Fit model
    model.fit(
        X_train, 
        y_growth_train, 
        y_temp_train,
        X_val, 
        y_growth_val, 
        y_temp_val,
        args.output_dir
    )
    
    # Evaluate model
    metrics = evaluate_model(
        model, X_test, y_growth_test, y_temp_test,
        transform_info, args.output_dir
    )
    
    # Print final metrics
    logger.info("Hybrid model with optimized temperature DNN - training and evaluation completed")
    logger.info(f"Final metrics (scaled) - Overall R²: {metrics['scaled']['overall']['r2']:.4f}")
    logger.info(f"Final metrics (scaled) - Growth Rate R²: {metrics['scaled']['growth_rate']['r2']:.4f}")
    logger.info(f"Final metrics (scaled) - Temperature R²: {metrics['scaled']['temperature']['r2']:.4f}")
    
    logger.info(f"Final metrics (original) - Overall R²: {metrics['original']['overall']['r2']:.4f}")
    logger.info(f"Final metrics (original) - Growth Rate R²: {metrics['original']['growth_rate']['r2']:.4f}")
    logger.info(f"Final metrics (original) - Temperature R²: {metrics['original']['temperature']['r2']:.4f}")
    
    # Save command line arguments
    with open(os.path.join(args.output_dir, 'args.txt'), 'w') as f:
        for arg, value in vars(args).items():
            f.write(f"{arg}: {value}\n")
    
    logger.info(f"Model and results saved to {args.output_dir}")
    logger.info("Done!")

if __name__ == "__main__":
    main() 