#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Hybrid Model with Clean Component Separation (v6+).
This script implements a clean and coherent hybrid model that:
1. Uses dedicated growth rate and temperature prediction components
2. Properly integrates them without any dummy values or API workarounds
3. Uses task-specific feature selection and preprocessing
4. Provides clear evaluations and predictions
5. Incorporates optimizations from specialized models for improved performance
"""

import os
import argparse
import logging
import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import RobustScaler
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
import joblib
import torch
from typing import Dict, List, Tuple, Optional

# Import our dedicated models
from growth_model import GrowthRateModel
from temperature_model import TemperatureModel

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EarlyStopping:
    """
    Early stopping implementation to prevent overfitting.
    """
    def __init__(self, patience=10, min_delta=0.0, restore_best_weights=True):
        self.patience = patience
        self.min_delta = min_delta
        self.restore_best_weights = restore_best_weights
        self.best_score = None
        self.counter = 0
        self.best_weights = None
    
    def __call__(self, val_loss, model):
        if self.best_score is None:
            self.best_score = val_loss
            if self.restore_best_weights:
                self.best_weights = model.state_dict().copy()
            return False
        
        if val_loss > self.best_score + self.min_delta:
            self.counter += 1
            if self.counter >= self.patience:
                return True
        else:
            self.best_score = val_loss
            if self.restore_best_weights:
                self.best_weights = model.state_dict().copy()
            self.counter = 0
        
        return False
    
    def restore_weights(self, model):
        if self.restore_best_weights and self.best_weights is not None:
            model.load_state_dict(self.best_weights)

def get_optimized_temperature_model_params():
    """
    Return optimized temperature model parameters based on analysis of successful models.
    
    Returns:
        Dict of optimized parameters
    """
    return {
        'hidden_dims': [512, 512, 384, 256, 128],
        'dropout_rates': [0.3, 0.4, 0.4, 0.5, 0.5],
        'lr': 0.001,
        'batch_size': 64,
        'epochs': 200,  # Increased from previous values
        'patience': 25,
        'use_batch_norm': True,
        'activation': 'relu',
        'use_residual': True,
        'l1_lambda': 1e-5,  # Add L1 regularization
        'weight_decay': 5e-6,  # AdamW weight decay
        'use_gradient_clipping': True,  # Use gradient clipping
        'max_grad_norm': 0.5,  # Maximum gradient norm
        'one_cycle_max_lr': 0.01,  # Maximum learning rate for one cycle
        'warmup_pct': 0.3,  # Percentage of training to use for warmup
    }

def add_l1_regularization(model, loss, l1_lambda):
    """
    Add L1 regularization to the loss.
    
    Args:
        model: PyTorch model
        loss: Current loss
        l1_lambda: L1 regularization coefficient
        
    Returns:
        Updated loss with L1 regularization
    """
    l1_reg = 0.0
    for param in model.parameters():
        l1_reg += torch.norm(param, 1)
    
    return loss + l1_lambda * l1_reg

def apply_gradient_clipping(model, max_grad_norm):
    """
    Apply gradient clipping to prevent exploding gradients.
    
    Args:
        model: PyTorch model
        max_grad_norm: Maximum gradient norm
    """
    torch.nn.utils.clip_grad_norm_(model.parameters(), max_grad_norm)

def setup_one_cycle_lr(optimizer, epochs, steps_per_epoch, max_lr, warmup_pct):
    """
    Set up one cycle learning rate scheduler.
    
    Args:
        optimizer: PyTorch optimizer
        epochs: Number of epochs
        steps_per_epoch: Steps per epoch
        max_lr: Maximum learning rate
        warmup_pct: Percentage of training to use for warmup
        
    Returns:
        One cycle learning rate scheduler
    """
    total_steps = epochs * steps_per_epoch
    return optim.lr_scheduler.OneCycleLR(
        optimizer,
        max_lr=max_lr,
        total_steps=total_steps,
        pct_start=warmup_pct,
        anneal_strategy='cos',
        div_factor=25.0,
        final_div_factor=10000.0
    )

def load_data(feature_file: str, metadata_file: str) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    Load feature and metadata files.
    
    Args:
        feature_file: Path to feature file
        metadata_file: Path to metadata file
        
    Returns:
        Tuple of (features, metadata)
    """
    logger.info(f"Loading features from {feature_file}")
    features = pd.read_csv(feature_file, sep='\t', index_col='genome_id')
    
    logger.info(f"Loading metadata from {metadata_file}")
    metadata = pd.read_csv(metadata_file, sep='\t', index_col='genome_id')
    
    # Get common samples
    common_indices = features.index.intersection(metadata.index)
    logger.info(f"Found {len(common_indices)} common samples")
    
    # Filter to common samples
    features = features.loc[common_indices]
    metadata = metadata.loc[common_indices]
    
    # Check for NaN values in features
    nan_count = features.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in features. Filling with 0.")
        features = features.fillna(0)
    
    # Check for infinite values
    inf_count = np.isinf(features.values).sum()
    if inf_count > 0:
        logger.warning(f"Found {inf_count} infinite values in features. Replacing with max/min values.")
        features = features.replace([np.inf, -np.inf], np.nan)
        features = features.fillna(features.max())
    
    # Check for NaN values in metadata
    nan_count = metadata.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in metadata. Filling with 0.")
        metadata = metadata.fillna(0)
    
    return features, metadata

class CleanHybridModel:
    """
    Clean hybrid model with properly separated components.
    Growth rate and temperature models are integrated without dummy values or API hacks.
    """
    def __init__(self, 
                n_growth_features: int = 150,
                n_temp_features: int = 250,
                output_dir: Optional[str] = None):
        """
        Initialize the clean hybrid model.
        
        Args:
            n_growth_features: Number of features for growth rate model
            n_temp_features: Number of features for temperature model
            output_dir: Directory to save outputs
        """
        self.n_growth_features = n_growth_features
        self.n_temp_features = n_temp_features
        self.output_dir = output_dir
        
        # Create component models
        self.growth_model = GrowthRateModel(
            ensemble_weight_lr=0.01,
            ensemble_weight_epochs=75,  # Increased from 50 for better convergence
            variance_percentile=20,     # Decreased from 25 for more features
            output_dir=os.path.join(output_dir, 'growth_model') if output_dir else None
        )
        
        # Get temperature model optimized parameters
        temp_params = get_optimized_temperature_model_params()
        
        # Use improved temperature model configuration with optimized parameters
        self.temp_model = TemperatureModel(
            hidden_dims=temp_params['hidden_dims'],
            dropout_rates=temp_params['dropout_rates'],
            lr=temp_params['lr'],
            batch_size=temp_params['batch_size'],
            epochs=temp_params['epochs'],
            patience=temp_params['patience'],
            use_batch_norm=temp_params['use_batch_norm'],
            activation=temp_params['activation'],
            use_residual=temp_params['use_residual'],
            output_dir=os.path.join(output_dir, 'temp_model') if output_dir else None
        )
        
        # Initialize additional training parameters
        self.temp_model.additional_params = {
            'l1_lambda': temp_params['l1_lambda'],
            'weight_decay': temp_params['weight_decay'],
            'use_gradient_clipping': temp_params['use_gradient_clipping'],
            'max_grad_norm': temp_params['max_grad_norm'],
            'one_cycle_max_lr': temp_params['one_cycle_max_lr'],
            'warmup_pct': temp_params['warmup_pct']
        }
        
        # Transform info
        self.transform_info = None
        
        # Store feature lists for consistent predictions
        self.growth_features = None
        self.temp_features = None
    
    def _select_growth_features(self, X: pd.DataFrame) -> Tuple[pd.DataFrame, List[str]]:
        """
        Select features for growth rate prediction.
        Prioritizes codon usage, tRNA, and other growth-related features.
        
        Args:
            X: Feature DataFrame
            
        Returns:
            Tuple of (selected features DataFrame, list of feature names)
        """
        # If we already have selected features from training, use those
        if self.growth_features is not None:
            common_features = [col for col in self.growth_features if col in X.columns]
            logger.info(f"Using {len(common_features)} previously selected growth features")
            return X[common_features], common_features
        
        # Priority features for growth rate prediction (expanded list)
        growth_priority_features = [col for col in X.columns if any(x in col.lower() for x in 
                                   ['codon', 'tRNA', 'rRNA', 'growth', 'ribosome', 'translation',
                                    'protein', 'enzyme', 'metabolism', 'synthesis'])]
        
        # Add remaining features up to n_growth_features
        remaining_features = [col for col in X.columns if col not in growth_priority_features]
        selected_features = growth_priority_features + remaining_features
        selected_features = selected_features[:self.n_growth_features]
        
        logger.info(f"Selected {len(selected_features)} features for growth rate prediction")
        
        return X[selected_features], selected_features
    
    def _select_temp_features(self, X: pd.DataFrame) -> Tuple[pd.DataFrame, List[str]]:
        """
        Select features for temperature prediction.
        Prioritizes GC content, amino acid, and other temperature-related features.
        
        Args:
            X: Feature DataFrame
            
        Returns:
            Tuple of (selected features DataFrame, list of feature names)
        """
        # If we already have selected features from training, use those
        if self.temp_features is not None:
            common_features = [col for col in self.temp_features if col in X.columns]
            logger.info(f"Using {len(common_features)} previously selected temperature features")
            return X[common_features], common_features
        
        # Priority features for temperature prediction (expanded list)
        temp_priority_features = []
        
        # GC content related features
        temp_priority_features.extend([col for col in X.columns if 'GC' in col])
        
        # Amino acid and protein related features
        temp_priority_features.extend([col for col in X.columns if any(x in col.lower() for x in 
                                     ['amino', 'protein', 'keto', 'charged', 'polar', 'hydrophobic',
                                      'thermal', 'stability', 'fold', 'structure', 'membrane'])])
        
        # Add remaining features up to n_temp_features
        remaining_features = [col for col in X.columns if col not in temp_priority_features]
        selected_features = temp_priority_features + remaining_features
        selected_features = selected_features[:self.n_temp_features]
        
        logger.info(f"Selected {len(selected_features)} features for temperature prediction")
        
        return X[selected_features], selected_features
    
    def prepare_data(self, 
                    features: pd.DataFrame, 
                    metadata: pd.DataFrame) -> Dict:
        """
        Prepare data for both growth rate and temperature models.
        
        Args:
            features: Feature DataFrame
            metadata: Metadata DataFrame
            
        Returns:
            Dictionary containing all prepared data sets
        """
        logger.info("Preparing data for clean hybrid model...")
        
        # Extract targets
        y_growth_original = metadata['growth_rate']
        y_temp_original = metadata['optimal_temperature']
        
        # Apply square root transformation to growth rate
        logger.info("Applying square root transformation to growth rate only")
        y_growth = np.sqrt(y_growth_original)
        y_temp = y_temp_original  # Keep temperature in original scale
        
        # Create bins for stratification based on both targets (combined)
        growth_bins = pd.qcut(y_growth, 5, labels=False, duplicates='drop')
        temp_bins = pd.qcut(y_temp, 5, labels=False, duplicates='drop')
        combined_bins = growth_bins * 10 + temp_bins  # Combine bins for better stratification
        
        # Split data into train, validation, and test sets
        X_train_val, X_test, y_growth_train_val, y_growth_test, y_temp_train_val, y_temp_test, bins_train_val, _ = train_test_split(
            features, y_growth, y_temp, combined_bins, test_size=0.2, random_state=42, shuffle=True, stratify=combined_bins
        )
        
        X_train, X_val, y_growth_train, y_growth_val, y_temp_train, y_temp_val = train_test_split(
            X_train_val, y_growth_train_val, y_temp_train_val, test_size=0.25, random_state=42, 
            shuffle=True, stratify=bins_train_val
        )
        
        logger.info(f"Data split: Train={X_train.shape[0]} samples, Validation={X_val.shape[0]} samples, Test={X_test.shape[0]} samples")
        
        # Apply RobustScaler for both targets
        growth_scaler = RobustScaler()
        temp_scaler = RobustScaler()
        
        # Reshape for scaler
        y_growth_train_reshaped = y_growth_train.values.reshape(-1, 1)
        y_growth_val_reshaped = y_growth_val.values.reshape(-1, 1)
        y_growth_test_reshaped = y_growth_test.values.reshape(-1, 1)
        
        y_temp_train_reshaped = y_temp_train.values.reshape(-1, 1)
        y_temp_val_reshaped = y_temp_val.values.reshape(-1, 1)
        y_temp_test_reshaped = y_temp_test.values.reshape(-1, 1)
        
        # Fit scalers on training data only
        growth_scaler.fit(y_growth_train_reshaped)
        temp_scaler.fit(y_temp_train_reshaped)
        
        # Transform all sets
        y_growth_train_scaled = growth_scaler.transform(y_growth_train_reshaped).flatten()
        y_growth_val_scaled = growth_scaler.transform(y_growth_val_reshaped).flatten()
        y_growth_test_scaled = growth_scaler.transform(y_growth_test_reshaped).flatten()
        
        y_temp_train_scaled = temp_scaler.transform(y_temp_train_reshaped).flatten()
        y_temp_val_scaled = temp_scaler.transform(y_temp_val_reshaped).flatten()
        y_temp_test_scaled = temp_scaler.transform(y_temp_test_reshaped).flatten()
        
        # Convert back to pandas Series with original indices
        y_growth_train = pd.Series(y_growth_train_scaled, index=y_growth_train.index)
        y_growth_val = pd.Series(y_growth_val_scaled, index=y_growth_val.index)
        y_growth_test = pd.Series(y_growth_test_scaled, index=y_growth_test.index)
        
        y_temp_train = pd.Series(y_temp_train_scaled, index=y_temp_train.index)
        y_temp_val = pd.Series(y_temp_val_scaled, index=y_temp_val.index)
        y_temp_test = pd.Series(y_temp_test_scaled, index=y_temp_test.index)
        
        logger.info("Applied target scaling using RobustScaler for both targets")
        
        # Store transformation information
        self.transform_info = {
            'growth_scaler': growth_scaler,
            'temp_scaler': temp_scaler,
            'growth_sqrt_transform': True,
            'temp_sqrt_transform': False
        }
        
        # Save transformation info if output_dir is provided
        if self.output_dir:
            os.makedirs(self.output_dir, exist_ok=True)
            joblib.dump(self.transform_info, os.path.join(self.output_dir, 'transform_info.joblib'))
        
        # Select features for growth rate and temperature separately
        X_train_growth, growth_features = self._select_growth_features(X_train)
        X_val_growth = X_val[growth_features].copy()
        X_test_growth = X_test[growth_features].copy()
        
        X_train_temp, temp_features = self._select_temp_features(X_train)
        X_val_temp = X_val[temp_features].copy()
        X_test_temp = X_test[temp_features].copy()
        
        # Store the features for prediction consistency
        self.growth_features = growth_features
        self.temp_features = temp_features
        
        # Save selected features
        if self.output_dir:
            joblib.dump(growth_features, os.path.join(self.output_dir, 'growth_features.joblib'))
            joblib.dump(temp_features, os.path.join(self.output_dir, 'temp_features.joblib'))
        
        return {
            # Growth rate data
            'X_train_growth': X_train_growth, 
            'y_growth_train': y_growth_train,
            'X_val_growth': X_val_growth, 
            'y_growth_val': y_growth_val,
            'X_test_growth': X_test_growth, 
            'y_growth_test': y_growth_test,
            # Temperature data
            'X_train_temp': X_train_temp, 
            'y_temp_train': y_temp_train,
            'X_val_temp': X_val_temp, 
            'y_temp_val': y_temp_val,
            'X_test_temp': X_test_temp, 
            'y_temp_test': y_temp_test,
            # Original datasets
            'X_train': X_train, 
            'X_val': X_val, 
            'X_test': X_test,
            # Features
            'growth_features': growth_features, 
            'temp_features': temp_features
        }
    
    def fit(self, 
           features: pd.DataFrame, 
           metadata: pd.DataFrame) -> None:
        """
        Fit the hybrid model - train growth rate and temperature models separately.
        
        Args:
            features: Feature DataFrame
            metadata: Metadata DataFrame
        """
        # Prepare data
        data = self.prepare_data(features, metadata)
        
        # Train growth rate model
        logger.info("Training growth rate model...")
        self.growth_model.fit(
            data['X_train_growth'],
            data['y_growth_train'],
            data['X_val_growth'],
            data['y_growth_val']
        )
        
        # Train temperature model with additional optimizations from specialized models
        logger.info("Training temperature model with advanced optimizations...")
        
        # Get optimized parameters from the specialized model analysis
        optimized_params = get_optimized_temperature_model_params()
        
        # Update temperature model with optimized parameters
        self.temp_model.hidden_dims = optimized_params['hidden_dims']
        self.temp_model.dropout_rates = optimized_params['dropout_rates']
        self.temp_model.lr = optimized_params['lr']
        self.temp_model.batch_size = optimized_params['batch_size']
        self.temp_model.epochs = optimized_params['epochs']
        self.temp_model.patience = optimized_params['patience']
        self.temp_model.activation = optimized_params['activation']
        self.temp_model.use_residual = optimized_params['use_residual']
        
        # Set additional parameters for advanced training techniques
        self.temp_model.additional_params = {
            'l1_lambda': optimized_params['l1_lambda'],
            'weight_decay': optimized_params['weight_decay'],
            'use_gradient_clipping': optimized_params['use_gradient_clipping'],
            'max_grad_norm': optimized_params['max_grad_norm'],
            'one_cycle_max_lr': optimized_params['one_cycle_max_lr'],
            'warmup_pct': optimized_params['warmup_pct']
        }
        
        self.temp_model.fit(
            data['X_train_temp'],
            data['y_temp_train'],
            data['X_val_temp'],
            data['y_temp_val']
        )
        
        # Evaluate on test set
        growth_metrics = self.growth_model.evaluate(data['X_test_growth'], data['y_growth_test'])
        temp_metrics = self.temp_model.evaluate(data['X_test_temp'], data['y_temp_test'])
        
        # Calculate combined metrics
        overall_r2 = (growth_metrics['r2'] + temp_metrics['r2']) / 2
        overall_rmse = (growth_metrics['rmse'] + temp_metrics['rmse']) / 2
        overall_mae = (growth_metrics['mae'] + temp_metrics['mae']) / 2
        
        logger.info(f"Final Growth Rate Metrics - R²: {growth_metrics['r2']:.4f}, RMSE: {growth_metrics['rmse']:.4f}, MAE: {growth_metrics['mae']:.4f}")
        logger.info(f"Final Temperature Metrics - R²: {temp_metrics['r2']:.4f}, RMSE: {temp_metrics['rmse']:.4f}, MAE: {temp_metrics['mae']:.4f}")
        logger.info(f"Overall Metrics - R²: {overall_r2:.4f}, RMSE: {overall_rmse:.4f}, MAE: {overall_mae:.4f}")
        
        # Save metrics
        if self.output_dir:
            metrics = {
                'growth_rate': growth_metrics,
                'temperature': temp_metrics,
                'overall': {
                    'r2': overall_r2,
                    'rmse': overall_rmse,
                    'mae': overall_mae
                }
            }
            joblib.dump(metrics, os.path.join(self.output_dir, 'metrics.joblib'))
    
    def predict(self, X: pd.DataFrame) -> Dict[str, np.ndarray]:
        """
        Generate predictions for both growth rate and temperature.
        
        Args:
            X: Features
            
        Returns:
            Dictionary with growth rate and temperature predictions
        """
        # Ensure feature consistency - use the same features we trained with
        if self.growth_features is None or self.temp_features is None:
            raise ValueError("Model has not been trained. Please call fit() first.")
        
        # Select features for each model, with safety checks
        growth_common_features = [col for col in self.growth_features if col in X.columns]
        temp_common_features = [col for col in self.temp_features if col in X.columns]
        
        if len(growth_common_features) < len(self.growth_features):
            logger.warning(f"Missing {len(self.growth_features) - len(growth_common_features)} growth features")
        
        if len(temp_common_features) < len(self.temp_features):
            logger.warning(f"Missing {len(self.temp_features) - len(temp_common_features)} temperature features")
        
        X_growth = X[growth_common_features].copy()
        X_temp = X[temp_common_features].copy()
        
        # Make predictions
        growth_pred = self.growth_model.predict(X_growth)
        temp_pred = self.temp_model.predict(X_temp)
        
        # If transform_info is available, inverse transform predictions
        if self.transform_info:
            # Growth rate
            growth_pred_reshaped = growth_pred.reshape(-1, 1)
            growth_pred_original = self.transform_info['growth_scaler'].inverse_transform(growth_pred_reshaped).flatten()
            
            # If square root transform was applied, undo it
            if self.transform_info['growth_sqrt_transform']:
                growth_pred_original = growth_pred_original ** 2
                # Ensure non-negative values
                growth_pred_original = np.maximum(growth_pred_original, 0)
            
            # Temperature
            temp_pred_reshaped = temp_pred.reshape(-1, 1)
            temp_pred_original = self.transform_info['temp_scaler'].inverse_transform(temp_pred_reshaped).flatten()
            
            return {
                'growth_rate': growth_pred_original,
                'temperature': temp_pred_original,
                'growth_rate_scaled': growth_pred,
                'temperature_scaled': temp_pred
            }
        
        return {
            'growth_rate': growth_pred,
            'temperature': temp_pred
        }
    
    def evaluate(self, 
                X: pd.DataFrame, 
                y_growth: pd.Series, 
                y_temp: pd.Series,
                scaled: bool = True) -> Dict:
        """
        Evaluate model on data.
        
        Args:
            X: Features
            y_growth: Growth rate targets
            y_temp: Temperature targets
            scaled: Whether targets are already scaled
            
        Returns:
            Dictionary of evaluation metrics
        """
        # Get predictions
        predictions = self.predict(X)
        
        # Extract the appropriate predictions
        if scaled:
            growth_pred = predictions.get('growth_rate_scaled', predictions['growth_rate'])
            temp_pred = predictions.get('temperature_scaled', predictions['temperature'])
        else:
            growth_pred = predictions['growth_rate']
            temp_pred = predictions['temperature']
        
        # Calculate metrics for growth rate
        growth_r2 = r2_score(y_growth, growth_pred)
        growth_rmse = np.sqrt(mean_squared_error(y_growth, growth_pred))
        growth_mae = mean_absolute_error(y_growth, growth_pred)
        
        # Calculate metrics for temperature
        temp_r2 = r2_score(y_temp, temp_pred)
        temp_rmse = np.sqrt(mean_squared_error(y_temp, temp_pred))
        temp_mae = mean_absolute_error(y_temp, temp_pred)
        
        # Calculate overall metrics
        overall_r2 = (growth_r2 + temp_r2) / 2
        overall_rmse = (growth_rmse + temp_rmse) / 2
        overall_mae = (growth_mae + temp_mae) / 2
        
        # Log metrics
        logger.info(f"Evaluation metrics - Growth Rate: R²={growth_r2:.4f}, RMSE={growth_rmse:.4f}, MAE={growth_mae:.4f}")
        logger.info(f"Evaluation metrics - Temperature: R²={temp_r2:.4f}, RMSE={temp_rmse:.4f}, MAE={temp_mae:.4f}")
        logger.info(f"Evaluation metrics - Overall: R²={overall_r2:.4f}, RMSE={overall_rmse:.4f}, MAE={overall_mae:.4f}")
        
        return {
            'growth_rate': {'r2': growth_r2, 'rmse': growth_rmse, 'mae': growth_mae},
            'temperature': {'r2': temp_r2, 'rmse': temp_rmse, 'mae': temp_mae},
            'overall': {'r2': overall_r2, 'rmse': overall_rmse, 'mae': overall_mae}
        }
    
    def save(self, output_dir: str) -> None:
        """
        Save the hybrid model.
        
        Args:
            output_dir: Directory to save the model
        """
        os.makedirs(output_dir, exist_ok=True)
        
        # Save component models
        growth_dir = os.path.join(output_dir, 'growth_model')
        temp_dir = os.path.join(output_dir, 'temp_model')
        
        os.makedirs(growth_dir, exist_ok=True)
        os.makedirs(temp_dir, exist_ok=True)
        
        self.growth_model.save(growth_dir)
        self.temp_model.save(temp_dir)
        
        # Save transformation info
        if self.transform_info:
            joblib.dump(self.transform_info, os.path.join(output_dir, 'transform_info.joblib'))
        
        # Save feature lists
        if self.growth_features:
            joblib.dump(self.growth_features, os.path.join(output_dir, 'growth_features.joblib'))
        
        if self.temp_features:
            joblib.dump(self.temp_features, os.path.join(output_dir, 'temp_features.joblib'))
        
        logger.info(f"Hybrid model saved to {output_dir}")
    
    def load(self, model_dir: str) -> None:
        """
        Load the hybrid model.
        
        Args:
            model_dir: Directory containing the saved model
        """
        # Load component models
        growth_dir = os.path.join(model_dir, 'growth_model')
        temp_dir = os.path.join(model_dir, 'temp_model')
        
        self.growth_model.load(growth_dir)
        self.temp_model.load(temp_dir)
        
        # Load transformation info
        if os.path.exists(os.path.join(model_dir, 'transform_info.joblib')):
            self.transform_info = joblib.load(os.path.join(model_dir, 'transform_info.joblib'))
        
        # Load feature lists
        if os.path.exists(os.path.join(model_dir, 'growth_features.joblib')):
            self.growth_features = joblib.load(os.path.join(model_dir, 'growth_features.joblib'))
        
        if os.path.exists(os.path.join(model_dir, 'temp_features.joblib')):
            self.temp_features = joblib.load(os.path.join(model_dir, 'temp_features.joblib'))
        
        logger.info(f"Hybrid model loaded from {model_dir}")

def main():
    """Main function."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Train clean hybrid model with proper component separation')
    parser.add_argument('--feature_file', type=str, required=True, help='Path to feature file (TSV)')
    parser.add_argument('--metadata_file', type=str, required=True, help='Path to metadata file (TSV)')
    parser.add_argument('--output_dir', type=str, required=True, help='Directory to save outputs')
    parser.add_argument('--n_growth_features', type=int, default=150, help='Number of features for growth rate model')
    parser.add_argument('--n_temp_features', type=int, default=250, help='Number of features for temperature model')
    parser.add_argument('--seed', type=int, default=42, help='Random seed')
    
    args = parser.parse_args()
    
    # Set random seeds for reproducibility
    np.random.seed(args.seed)
    torch.manual_seed(args.seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(args.seed)
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Load data
    features, metadata = load_data(args.feature_file, args.metadata_file)
    
    # Initialize model
    model = CleanHybridModel(
        n_growth_features=args.n_growth_features,
        n_temp_features=args.n_temp_features,
        output_dir=args.output_dir
    )
    
    # Fit model
    model.fit(features, metadata)
    
    # Save command line arguments
    with open(os.path.join(args.output_dir, 'args.txt'), 'w') as f:
        for arg, value in vars(args).items():
            f.write(f"{arg}: {value}\n")
    
    logger.info(f"Model and results saved to {args.output_dir}")
    logger.info("Done!")

if __name__ == "__main__":
    main() 