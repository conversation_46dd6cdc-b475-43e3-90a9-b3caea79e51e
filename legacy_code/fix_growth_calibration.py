import pandas as pd
import numpy as np
import os
import joblib
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
import matplotlib.pyplot as plt

# Paths
pred_file = 'predictions_rl/predictions.tsv'
meta_file = 'training_data/metadata.tsv'
output_dir = 'predictions_rl/fixed_calibration'

# Create output directory
os.makedirs(output_dir, exist_ok=True)

print("Loading predictions and metadata...")
# Load predictions
pred_df = pd.read_csv(pred_file, sep='\t')
print(f"Loaded {len(pred_df)} predictions")

# Load actual metadata
meta_df = pd.read_csv(meta_file, sep='\t')
print(f"Loaded {len(meta_df)} metadata samples")

# Merge on genome_id
merged_df = pd.merge(pred_df, meta_df, on='genome_id')
print(f"Merged dataset has {len(merged_df)} samples")

# Basic metrics for understanding the data
print("\nData ranges:")
print(f"Predicted growth rate range: {merged_df['growth_rate_x'].min():.4f} to {merged_df['growth_rate_x'].max():.4f}")
print(f"Actual growth rate range: {merged_df['growth_rate_y'].min():.4f} to {merged_df['growth_rate_y'].max():.4f}")
print(f"Predicted temperature range: {merged_df['optimal_temperature_x'].min():.4f} to {merged_df['optimal_temperature_x'].max():.4f}")
print(f"Actual temperature range: {merged_df['optimal_temperature_y'].min():.4f} to {merged_df['optimal_temperature_y'].max():.4f}")

# Calculate Spearman correlation
growth_spearman = merged_df['growth_rate_x'].corr(merged_df['growth_rate_y'], method='spearman')
temp_spearman = merged_df['optimal_temperature_x'].corr(merged_df['optimal_temperature_y'], method='spearman')
print(f"\nSpearman correlation (growth): {growth_spearman:.4f}")
print(f"Spearman correlation (temp): {temp_spearman:.4f}")

# Since we have very high Spearman correlation for growth rate (0.98), 
# but poor R² in absolute values, we'll use quantile mapping for calibration

print("\nApplying quantile mapping calibration...")

# For growth rate
# Calculate percentile ranks
pred_ranks_growth = merged_df['growth_rate_x'].rank(pct=True)
actual_sorted_growth = np.sort(merged_df['growth_rate_y'].values)

# Map each predicted value to the corresponding percentile in the actual distribution
quantile_mapped_growth = np.zeros_like(pred_ranks_growth)
for i, rank in enumerate(pred_ranks_growth):
    idx = int(rank * (len(actual_sorted_growth) - 1))
    quantile_mapped_growth[i] = actual_sorted_growth[idx]

# For temperature - simple linear calibration works well
from sklearn.linear_model import LinearRegression
temp_model = LinearRegression()
temp_model.fit(
    merged_df['optimal_temperature_x'].values.reshape(-1, 1), 
    merged_df['optimal_temperature_y'].values
)
calibrated_temp = temp_model.predict(merged_df['optimal_temperature_x'].values.reshape(-1, 1))
print(f"Linear mapping for temperature: y = {temp_model.coef_[0]:.4f}x + {temp_model.intercept_:.4f}")

# Calculate metrics for the calibrated predictions
growth_r2 = r2_score(merged_df['growth_rate_y'], quantile_mapped_growth)
growth_rmse = np.sqrt(mean_squared_error(merged_df['growth_rate_y'], quantile_mapped_growth))
growth_mae = mean_absolute_error(merged_df['growth_rate_y'], quantile_mapped_growth)

temp_r2 = r2_score(merged_df['optimal_temperature_y'], calibrated_temp)
temp_rmse = np.sqrt(mean_squared_error(merged_df['optimal_temperature_y'], calibrated_temp))
temp_mae = mean_absolute_error(merged_df['optimal_temperature_y'], calibrated_temp)

print("\nCalibrated metrics:")
print(f"Growth R²: {growth_r2:.4f}, RMSE: {growth_rmse:.4f}, MAE: {growth_mae:.4f}")
print(f"Temperature R²: {temp_r2:.4f}, RMSE: {temp_rmse:.4f}, MAE: {temp_mae:.4f}")

# Visualizations 
plt.figure(figsize=(14, 12))

# Raw predictions
plt.subplot(2, 2, 1)
plt.scatter(merged_df['growth_rate_y'], merged_df['growth_rate_x'], alpha=0.5)
plt.xlabel('Actual Growth Rate')
plt.ylabel('Predicted Growth Rate (Raw)')
raw_growth_r2 = r2_score(merged_df['growth_rate_y'], merged_df['growth_rate_x'])
plt.title(f'Growth Rate - Raw (R²: {raw_growth_r2:.4f})')
plt.grid(alpha=0.3)

plt.subplot(2, 2, 2)
plt.scatter(merged_df['optimal_temperature_y'], merged_df['optimal_temperature_x'], alpha=0.5)
plt.xlabel('Actual Optimal Temperature')
plt.ylabel('Predicted Optimal Temperature (Raw)')
raw_temp_r2 = r2_score(merged_df['optimal_temperature_y'], merged_df['optimal_temperature_x'])
plt.title(f'Temperature - Raw (R²: {raw_temp_r2:.4f})')
plt.grid(alpha=0.3)

# Calibrated predictions
plt.subplot(2, 2, 3)
plt.scatter(merged_df['growth_rate_y'], quantile_mapped_growth, alpha=0.5)
plt.xlabel('Actual Growth Rate')
plt.ylabel('Calibrated Growth Rate (Quantile Mapping)')
plt.title(f'Growth Rate - Calibrated (R²: {growth_r2:.4f})')
plt.grid(alpha=0.3)

plt.subplot(2, 2, 4)
plt.scatter(merged_df['optimal_temperature_y'], calibrated_temp, alpha=0.5)
plt.xlabel('Actual Optimal Temperature')
plt.ylabel('Calibrated Optimal Temperature')
plt.title(f'Temperature - Calibrated (R²: {temp_r2:.4f})')
plt.grid(alpha=0.3)

plt.tight_layout()
plt.savefig(os.path.join(output_dir, 'calibration_results.png'), dpi=300)
plt.close()

# Create rank plots
plt.figure(figsize=(14, 6))

# Growth rate ranks
growth_actual_ranks = merged_df['growth_rate_y'].rank(pct=True)
growth_pred_ranks = merged_df['growth_rate_x'].rank(pct=True)
growth_rank_rmse = np.sqrt(mean_squared_error(growth_actual_ranks, growth_pred_ranks))
growth_rank_mae = mean_absolute_error(growth_actual_ranks, growth_pred_ranks)

plt.subplot(1, 2, 1)
plt.scatter(growth_actual_ranks, growth_pred_ranks, alpha=0.5)
plt.xlabel('Actual Growth Rate Percentile')
plt.ylabel('Predicted Growth Rate Percentile')
plt.title(f'Growth Rate Percentiles (MAE: {growth_rank_mae:.4f})')
plt.plot([0, 1], [0, 1], 'r--')  # Diagonal line
plt.grid(alpha=0.3)

# Temperature ranks
temp_actual_ranks = merged_df['optimal_temperature_y'].rank(pct=True)
temp_pred_ranks = merged_df['optimal_temperature_x'].rank(pct=True)
temp_rank_rmse = np.sqrt(mean_squared_error(temp_actual_ranks, temp_pred_ranks))
temp_rank_mae = mean_absolute_error(temp_actual_ranks, temp_pred_ranks)

plt.subplot(1, 2, 2)
plt.scatter(temp_actual_ranks, temp_pred_ranks, alpha=0.5)
plt.xlabel('Actual Temperature Percentile')
plt.ylabel('Predicted Temperature Percentile')
plt.title(f'Temperature Percentiles (MAE: {temp_rank_mae:.4f})')
plt.plot([0, 1], [0, 1], 'r--')  # Diagonal line
plt.grid(alpha=0.3)

plt.tight_layout()
plt.savefig(os.path.join(output_dir, 'rank_plots.png'), dpi=300)
plt.close()

# Log-space visualization for growth rate
plt.figure(figsize=(14, 6))

# Handle zeros and very small values for log transformation
offset = 1e-10

# Take log of actual values (for visualization)
log_actual = np.log10(merged_df['growth_rate_y'].values + offset)

# Take log of raw predicted values
raw_pred_for_log = np.abs(merged_df['growth_rate_x'].values) + offset
log_raw_pred = np.log10(raw_pred_for_log)
log_raw_pred[merged_df['growth_rate_x'].values < 0] *= -1  # Restore sign if negative

# Take log of calibrated values 
log_calibrated = np.log10(quantile_mapped_growth + offset)

# Plot log-space comparison
plt.subplot(1, 2, 1)
plt.scatter(log_actual, log_raw_pred, alpha=0.5)
plt.xlabel('Log10(Actual Growth Rate)')
plt.ylabel('Log10(Raw Predicted Growth Rate)')
plt.title(f'Log-Space Raw Comparison')
plt.grid(alpha=0.3)

plt.subplot(1, 2, 2)
plt.scatter(log_actual, log_calibrated, alpha=0.5)
plt.xlabel('Log10(Actual Growth Rate)')
plt.ylabel('Log10(Calibrated Growth Rate)')
plt.title(f'Log-Space Calibrated Comparison')
plt.grid(alpha=0.3)

plt.tight_layout()
plt.savefig(os.path.join(output_dir, 'log_space_plots.png'), dpi=300)
plt.close()

# Save the calibrated predictions
print("\nSaving calibrated predictions...")
calibrated_df = pred_df.copy()

# Apply calibration to all predictions
# For growth rate - use quantile mapping
pred_ranks_all = pred_df['growth_rate'].rank(pct=True)
calibrated_df['growth_rate'] = np.zeros_like(pred_ranks_all)
for i, rank in enumerate(pred_ranks_all):
    idx = int(rank * (len(actual_sorted_growth) - 1))
    calibrated_df['growth_rate'][i] = actual_sorted_growth[idx]

# For temperature - use linear model
calibrated_df['optimal_temperature'] = temp_model.predict(pred_df['optimal_temperature'].values.reshape(-1, 1))

# Save to file
calibrated_df.to_csv(os.path.join(output_dir, 'calibrated_predictions.tsv'), sep='\t', index=False)
print(f"Calibrated predictions saved to {os.path.join(output_dir, 'calibrated_predictions.tsv')}")

print("\nCalibration complete!") 