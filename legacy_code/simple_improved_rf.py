#!/usr/bin/env python
"""
Simplified improved Random Forest model for DeepMu that incorporates taxonomy information
and entropy-based breakpoint features.
"""

import os
import sys
import logging
import argparse
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.preprocessing import StandardScaler
from joblib import dump

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_genomic_features(feature_dir):
    """
    Load genomic features from feature files.

    Args:
        feature_dir (str): Directory containing feature files

    Returns:
        pd.DataFrame: DataFrame with genomic features
    """
    feature_dir = Path(feature_dir)
    # Try to find combined features TSV file first
    combined_file = feature_dir / "combined_features.tsv"

    if combined_file.exists():
        logger.info(f"Loading combined features from {combined_file}")
        return pd.read_csv(combined_file, sep='\t')

    # Try to find NPZ files
    feature_files = list(feature_dir.glob("*_combined_features.npz"))

    # If no combined NPZ files, try to find regular NPZ files
    if not feature_files:
        feature_files = list(feature_dir.glob("*_features.npz"))

    # If no NPZ files, try to find TSV files
    if not feature_files:
        feature_files = list(feature_dir.glob("*_features.tsv"))

    if not feature_files:
        logger.error(f"No feature files found in {feature_dir}")
        return None

    # Extract features
    genomic_data = []

    for file in feature_files:
        try:
            # Extract sample ID from filename
            if file.name.endswith('_combined_features.npz'):
                sample_id = file.name.split('_combined_features.npz')[0]
            elif file.name.endswith('_features.tsv'):
                sample_id = file.name.split('_features.tsv')[0]
            elif file.name.endswith('_features.npz'):
                sample_id = file.name.split('_features.npz')[0]

            # Load features
            if file.name.endswith('_combined_features.npz') or file.name.endswith('_features.npz'):
                features = np.load(file)
            elif file.name.endswith('_features.tsv'):
                features_df = pd.read_csv(file, sep='\t')
                features = {col: features_df[col].values[0] for col in features_df.columns}

            # Extract the genomic features
            feature_dict = {'genome_id': sample_id}

            # Add all features except metadata fields
            for key in features.keys():
                if key != 'genome_id' and not isinstance(features[key], (str, bytes)):
                    try:
                        value = float(features[key])
                        feature_dict[key] = value

                        # Log codon usage bias features for debugging
                        if any(pattern in key for pattern in ['CUB', 'CPB', 'CAI', 'ENC', 'HEG_', 'BG_', 'delta_', 'RSCU_', 'codon_bias_', 'GC1', 'GC2', 'GC3', 'Context_bias', 'AAAI', 'aa_bias_HEG_BP', 'MILC']):
                            logger.info(f"Including codon feature: {key} = {value}")
                    except (ValueError, TypeError):
                        logger.debug(f"Skipping non-numeric feature: {key}")

            genomic_data.append(feature_dict)

        except Exception as e:
            logger.warning(f"Error loading features from {file}: {e}")

    # Create DataFrame
    genomic_df = pd.DataFrame(genomic_data)

    return genomic_df

def create_taxonomy_features(metadata_file):
    """
    Create simple taxonomy features from metadata.

    Args:
        metadata_file (str): Path to metadata file

    Returns:
        pd.DataFrame: DataFrame with taxonomy features
    """
    # Load metadata
    metadata = pd.read_csv(metadata_file, sep='\t')

    # Create simple taxonomy features
    taxonomy_data = []

    for _, row in metadata.iterrows():
        sample_id = row['genome_id']
        taxid = int(row['taxid'])

        # Create simple features based on taxid
        features = {
            'genome_id': sample_id,
            'taxid_mod_10': taxid % 10,
            'taxid_mod_100': taxid % 100,
            'taxid_mod_1000': taxid % 1000,
            'taxid_div_10': taxid // 10,
            'taxid_div_100': taxid // 100,
            'taxid_div_1000': taxid // 1000
        }

        taxonomy_data.append(features)

    # Create DataFrame
    taxonomy_df = pd.DataFrame(taxonomy_data)

    return taxonomy_df

def create_breakpoint_features(feature_dir):
    """
    Create simple breakpoint features.

    Args:
        feature_dir (str): Directory containing feature files

    Returns:
        pd.DataFrame: DataFrame with breakpoint features
    """
    # Use the combined features file if it exists
    feature_dir = Path(feature_dir)
    combined_file = feature_dir / "combined_features.tsv"

    if combined_file.exists():
        logger.info(f"Loading breakpoint features from {combined_file}")
        df = pd.read_csv(combined_file, sep='\t')

        # Extract breakpoint-related features
        breakpoint_cols = ['genome_id'] + [col for col in df.columns if
                                         'skew' in col or
                                         '_change_points' in col or
                                         '_sign_changes' in col or
                                         '_mean' in col or
                                         '_std' in col or
                                         '_min' in col or
                                         '_max' in col or
                                         '_range' in col]

        # Create a new DataFrame with only breakpoint features
        breakpoint_df = df[breakpoint_cols].copy()

        # Add emphasized features
        for col in breakpoint_df.columns:
            if col != 'genome_id' and ('_change_points' in col or '_sign_changes' in col):
                breakpoint_df[f'important_{col}'] = breakpoint_df[col] * 2.0
                logger.info(f"Emphasizing important change point feature: important_{col}")

        return breakpoint_df

    # If no combined file, try individual files
    feature_files = list(feature_dir.glob("*_combined_features.npz"))
    if not feature_files:
        feature_files = list(feature_dir.glob("*_features.tsv"))

    if not feature_files:
        logger.error(f"No feature files found in {feature_dir}")
        # Return an empty DataFrame with genome_id column
        return pd.DataFrame({'genome_id': []})

    # Extract breakpoint features
    breakpoint_data = []

    for file in feature_files:
        try:
            # Extract sample ID from filename
            if file.name.endswith('_combined_features.npz'):
                sample_id = file.name.split('_combined_features.npz')[0]
            elif file.name.endswith('_features.tsv'):
                sample_id = file.name.split('_features.tsv')[0]

            # Load features
            if file.name.endswith('_combined_features.npz'):
                features = np.load(file)
            elif file.name.endswith('_features.tsv'):
                features_df = pd.read_csv(file, sep='\t')
                features = {col: features_df[col].values[0] for col in features_df.columns}

            # Create breakpoint features
            feature_dict = {'genome_id': sample_id}

            # Add all features that might be related to breakpoints
            for key in features.keys():
                if key != 'genome_id' and not isinstance(features[key], (str, bytes)):
                    try:
                        # Include all skew-related features
                        if ('skew' in key or '_change_points' in key or '_sign_changes' in key or
                            '_mean' in key or '_std' in key or '_min' in key or '_max' in key or '_range' in key):
                            value = float(features[key])
                            feature_dict[key] = value

                            # Log skew and change point features for debugging
                            logger.info(f"Including skew/change point feature: {key} = {value}")

                            # Emphasize important skew and change point features
                            if '_change_points' in key or '_sign_changes' in key:
                                # Add the same feature with a prefix to emphasize it
                                feature_dict[f'important_{key}'] = value * 2.0
                                logger.info(f"Emphasizing important change point feature: important_{key} = {value * 2.0}")

                            # Create additional derived features for skew metrics
                            if key.startswith('dinuc_skew_') and ('_mean' in key or '_std' in key):
                                base_key = key.split('_mean')[0].split('_std')[0]

                                # Create simple breakpoint features
                                if '_mean' in key:
                                    feature_dict[f'{base_key}_breakpoint_mean'] = value
                                    feature_dict[f'{base_key}_breakpoint_mean_squared'] = value ** 2
                                elif '_std' in key:
                                    feature_dict[f'{base_key}_breakpoint_std'] = value
                                    feature_dict[f'{base_key}_breakpoint_std_squared'] = value ** 2
                    except (ValueError, TypeError):
                        logger.debug(f"Skipping non-numeric feature: {key}")

            breakpoint_data.append(feature_dict)

        except Exception as e:
            logger.warning(f"Error creating breakpoint features for {file}: {e}")

    # Create DataFrame
    if breakpoint_data:
        breakpoint_df = pd.DataFrame(breakpoint_data)
    else:
        # Return an empty DataFrame with genome_id column
        breakpoint_df = pd.DataFrame({'genome_id': []})

    return breakpoint_df

def train_model(feature_dir, metadata_file, output_dir, target='growth_rate', metrics_dir=None):
    """
    Train a simplified improved Random Forest model.

    Args:
        feature_dir (str): Directory containing feature files
        metadata_file (str): Path to metadata file
        output_dir (str): Directory to save model and results
        target (str): Target variable to predict ('growth_rate' or 'temperature')
        metrics_dir (Path, optional): Directory to save metrics and plots

    Returns:
        dict: Dictionary with training results
    """
    logger.info(f"Training simplified improved Random Forest model for {target}")

    # Create output directory
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)

    # Create metrics directory
    if metrics_dir is None:
        metrics_dir = Path('metrics/simple_improved_rf')
    metrics_dir.mkdir(parents=True, exist_ok=True)

    # Load metadata
    metadata = pd.read_csv(metadata_file, sep='\t')
    logger.info(f"Loaded metadata with {len(metadata)} samples")

    # Load genomic features
    logger.info("Loading genomic features")
    genomic_features = load_genomic_features(feature_dir)

    if genomic_features is None:
        logger.error("Failed to load genomic features")
        return None

    # Create taxonomy features
    logger.info("Creating taxonomy features")
    taxonomy_features = create_taxonomy_features(metadata_file)

    # Create breakpoint features
    logger.info("Creating breakpoint features")
    breakpoint_features = create_breakpoint_features(feature_dir)

    if breakpoint_features is None:
        logger.error("Failed to create breakpoint features")
        return None

    # Merge all features
    logger.info("Merging all features")

    # Print sample counts for debugging
    logger.info(f"Metadata: {len(metadata)} samples")
    logger.info(f"Genomic features: {len(genomic_features)} samples")
    logger.info(f"Taxonomy features: {len(taxonomy_features)} samples")
    logger.info(f"Breakpoint features: {len(breakpoint_features)} samples")

    # Check for sample ID format consistency
    metadata_ids = set(metadata['genome_id'])
    genomic_ids = set(genomic_features['genome_id'])
    taxonomy_ids = set(taxonomy_features['genome_id'])
    breakpoint_ids = set(breakpoint_features['genome_id'])

    logger.info(f"Common IDs between metadata and genomic: {len(metadata_ids.intersection(genomic_ids))}")
    logger.info(f"Common IDs between metadata and taxonomy: {len(metadata_ids.intersection(taxonomy_ids))}")
    logger.info(f"Common IDs between metadata and breakpoint: {len(metadata_ids.intersection(breakpoint_ids))}")

    # Log feature counts to help diagnose missing codon features
    codon_features_count = sum(1 for col in genomic_features.columns if any(pattern in col for pattern in ['CUB', 'CPB', 'CAI', 'ENC', 'HEG_', 'BG_', 'delta_']))
    logger.info(f"Number of codon-related features: {codon_features_count}")

    # Print sample IDs for debugging
    logger.info(f"First 5 metadata IDs: {list(metadata_ids)[:5]}")
    logger.info(f"First 5 genomic IDs: {list(genomic_ids)[:5]}")

    # Merge genomic features with metadata
    merged_df = pd.merge(metadata, genomic_features, on='genome_id', how='inner')
    logger.info(f"After merging metadata and genomic: {len(merged_df)} samples")

    # Merge with taxonomy features
    merged_df = pd.merge(merged_df, taxonomy_features, on='genome_id', how='inner')
    logger.info(f"After merging taxonomy: {len(merged_df)} samples")

    # Merge with breakpoint features
    if 'genome_id' in breakpoint_features.columns and len(breakpoint_features) > 0:
        merged_df = pd.merge(merged_df, breakpoint_features, on='genome_id', how='left')
        logger.info(f"After merging breakpoint: {len(merged_df)} samples")
    else:
        logger.warning("Skipping breakpoint features as they don't have matching genome_ids")

    logger.info(f"Combined dataset has {len(merged_df)} samples and {len(merged_df.columns)} features")

    # Save combined features
    combined_file = output_dir / "combined_features.tsv"
    merged_df.to_csv(combined_file, sep='\t', index=False)
    logger.info(f"Saved combined features to {combined_file}")

    # Split into features and targets
    X = merged_df.drop(['genome_id', 'growth_rate', 'optimal_temperature', 'taxid', 'codon_table', 'kingdom'], axis=1)

    if target == 'growth_rate':
        y = merged_df['growth_rate']
    else:  # temperature
        y = merged_df['optimal_temperature']

    # Split into train and test sets
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

    # Standardize features
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)

    # Create DataFrame with scaled features for better interpretability
    X_train_scaled_df = pd.DataFrame(X_train_scaled, columns=X_train.columns)

    # Log statistics about standardized features
    logger.info("Feature standardization statistics:")
    codon_features = [col for col in X_train.columns if any(pattern in col for pattern in
                    ['CUB', 'CPB', 'CAI', 'ENC', 'HEG_', 'BG_', 'delta_', 'RSCU_',
                     'codon_bias_', 'AAAI', 'aa_bias_HEG_BP', 'MILC'])]

    for feature in codon_features[:10]:  # Log first 10 codon features
        before_mean = X_train[feature].mean()
        before_std = X_train[feature].std()
        after_mean = X_train_scaled_df[feature].mean()
        after_std = X_train_scaled_df[feature].std()
        logger.info(f"Feature {feature}: Before standardization - mean={before_mean:.4f}, std={before_std:.4f} | After - mean={after_mean:.4f}, std={after_std:.4f}")

    # Save scaler
    scaler_file = output_dir / f"{target}_scaler.joblib"
    dump(scaler, scaler_file)
    logger.info(f"Saved scaler to {scaler_file}")

    # Train Random Forest model
    logger.info(f"Training Random Forest model for {target}")
    model = RandomForestRegressor(
        n_estimators=200,
        max_depth=None,
        min_samples_split=2,
        min_samples_leaf=1,
        random_state=42,
        n_jobs=-1
    )

    model.fit(X_train_scaled, y_train)

    # Save model
    model_file = output_dir / f"{target}_model.joblib"
    dump(model, model_file)
    logger.info(f"Saved model to {model_file}")

    # Make predictions
    y_pred_train = model.predict(X_train_scaled)
    y_pred_test = model.predict(X_test_scaled)

    # Calculate metrics
    train_metrics = {
        'mse': mean_squared_error(y_train, y_pred_train),
        'rmse': np.sqrt(mean_squared_error(y_train, y_pred_train)),
        'mae': mean_absolute_error(y_train, y_pred_train),
        'r2': r2_score(y_train, y_pred_train)
    }

    test_metrics = {
        'mse': mean_squared_error(y_test, y_pred_test),
        'rmse': np.sqrt(mean_squared_error(y_test, y_pred_test)),
        'mae': mean_absolute_error(y_test, y_pred_test),
        'r2': r2_score(y_test, y_pred_test)
    }

    # Log metrics
    logger.info(f"{target} - Train: R²={train_metrics['r2']:.4f}, RMSE={train_metrics['rmse']:.4f}")
    logger.info(f"{target} - Test: R²={test_metrics['r2']:.4f}, RMSE={test_metrics['rmse']:.4f}")

    # Calculate feature importance
    importances = model.feature_importances_
    feature_names = X.columns

    # Create feature importance dataframe
    importance_df = pd.DataFrame({
        'feature': feature_names,
        'importance': importances
    }).sort_values('importance', ascending=False)

    # Save feature importance
    importance_file = metrics_dir / f"{target}_feature_importance.tsv"
    importance_df.to_csv(importance_file, sep='\t', index=False)
    logger.info(f"Saved feature importance to {importance_file}")

    # Analyze codon feature importance
    codon_features = [f for f in feature_names if any(pattern in f for pattern in
                    ['CUB', 'CPB', 'CAI', 'ENC', 'HEG_', 'BG_', 'delta_', 'RSCU_',
                     'codon_bias_', 'AAAI', 'aa_bias_HEG_BP', 'MILC'])]

    codon_importance_df = importance_df[importance_df['feature'].isin(codon_features)].sort_values('importance', ascending=False)

    # Save codon feature importance
    codon_importance_file = metrics_dir / f"{target}_codon_feature_importance.tsv"
    codon_importance_df.to_csv(codon_importance_file, sep='\t', index=False)
    logger.info(f"Saved codon feature importance to {codon_importance_file}")

    # Log top codon features
    logger.info(f"Top 10 codon features for {target}:")
    for i, (feature, importance) in enumerate(zip(codon_importance_df['feature'].head(10), codon_importance_df['importance'].head(10))):
        logger.info(f"{i+1}. {feature}: {importance:.6f}")

    # Plot feature importance
    plt.figure(figsize=(12, 8))
    sns.barplot(x='importance', y='feature', data=importance_df.head(20))
    plt.title(f"Top 20 Important Features for {target}")
    plt.tight_layout()
    plt.savefig(metrics_dir / f"{target}_feature_importance.png", dpi=300)
    plt.close()

    # Plot codon feature importance
    plt.figure(figsize=(12, 8))
    sns.barplot(x='importance', y='feature', data=codon_importance_df.head(20))
    plt.title(f"Top 20 Important Codon Features for {target}")
    plt.tight_layout()
    plt.savefig(metrics_dir / f"{target}_codon_feature_importance.png", dpi=300)
    plt.close()

    # Create scatter plot
    plt.figure(figsize=(10, 8))
    plt.scatter(y_test, y_pred_test, alpha=0.7)

    # Add identity line
    min_val = min(min(y_test), min(y_pred_test))
    max_val = max(max(y_test), max(y_pred_test))
    plt.plot([min_val, max_val], [min_val, max_val], 'r--')

    plt.title(f"Predicted vs. True {target}")
    plt.xlabel(f"True {target}")
    plt.ylabel(f"Predicted {target}")

    # Add metrics text
    plt.text(0.05, 0.95, f"R² = {test_metrics['r2']:.4f}", transform=plt.gca().transAxes)
    plt.text(0.05, 0.90, f"RMSE = {test_metrics['rmse']:.4f}", transform=plt.gca().transAxes)
    plt.text(0.05, 0.85, f"MAE = {test_metrics['mae']:.4f}", transform=plt.gca().transAxes)

    plt.tight_layout()
    plt.savefig(metrics_dir / f"{target}_scatter_plot.png", dpi=300)
    plt.close()

    # Save results
    results = {
        'train_metrics': train_metrics,
        'test_metrics': test_metrics,
        'feature_importance': importance_df.to_dict('records')
    }

    # Save results to file
    results_file = metrics_dir / f"{target}_results.json"
    pd.DataFrame({
        'metric': ['mse', 'rmse', 'mae', 'r2'],
        'train': [train_metrics['mse'], train_metrics['rmse'], train_metrics['mae'], train_metrics['r2']],
        'test': [test_metrics['mse'], test_metrics['rmse'], test_metrics['mae'], test_metrics['r2']]
    }).to_csv(metrics_dir / f"{target}_metrics.tsv", sep='\t', index=False)

    # Save predictions
    pd.DataFrame({
        'true': y_test,
        'pred': y_pred_test
    }).to_csv(metrics_dir / f"{target}_predictions.tsv", sep='\t', index=False)

    logger.info(f"Saved results to {metrics_dir}")

    return results

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Train simplified improved Random Forest model for DeepMu")
    parser.add_argument("--feature-dir", type=str, required=True, help="Directory containing feature files")
    parser.add_argument("--metadata", type=str, required=True, help="Path to metadata file")
    parser.add_argument("--output-dir", type=str, default="models/simple_improved_rf", help="Directory to save model and results")
    parser.add_argument("--metrics-dir", type=str, default="metrics/simple_improved_rf", help="Directory to save metrics and plots")
    parser.add_argument("--target", type=str, choices=['growth_rate', 'temperature', 'both'], default='both',
                        help="Target variable to predict")

    args = parser.parse_args()

    try:
        # Create output and metrics directories
        os.makedirs(args.output_dir, exist_ok=True)
        os.makedirs(args.metrics_dir, exist_ok=True)

        # Train models
        if args.target in ['growth_rate', 'both']:
            train_model(args.feature_dir, args.metadata, args.output_dir, 'growth_rate', Path(args.metrics_dir))

        if args.target in ['temperature', 'both']:
            train_model(args.feature_dir, args.metadata, args.output_dir, 'temperature', Path(args.metrics_dir))

        logger.info("Simplified improved Random Forest model training completed successfully")
        return 0

    except Exception as e:
        logger.error(f"Error training simplified improved Random Forest model: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
