#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Compare the performance of all models: original hybrid, improved hybrid, and enhanced hybrid.
"""

import os
import argparse
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns

def load_metrics(metrics_file):
    """Load metrics from a CSV file."""
    if os.path.exists(metrics_file):
        return pd.read_csv(metrics_file)
    return None

def main():
    parser = argparse.ArgumentParser(description="Compare all model performance")
    parser.add_argument("--original_metrics", default="models/hybrid/metrics.csv", help="Path to original model metrics")
    parser.add_argument("--improved_metrics", default="models/improved_hybrid/evaluation_metrics.csv", help="Path to improved model metrics")
    parser.add_argument("--enhanced_metrics", default="models/enhanced_hybrid/evaluation_metrics.csv", help="Path to enhanced model metrics")
    parser.add_argument("--enhanced_optimized_metrics", default="models/enhanced_hybrid_optimized/evaluation_metrics.csv", help="Path to enhanced optimized model metrics")
    parser.add_argument("--output_dir", default="models/comparison", help="Directory to save comparison")
    args = parser.parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Load metrics
    original_metrics = load_metrics(args.original_metrics)
    improved_metrics = load_metrics(args.improved_metrics)
    enhanced_metrics = load_metrics(args.enhanced_metrics)
    enhanced_optimized_metrics = load_metrics(args.enhanced_optimized_metrics)
    
    # Check if metrics files exist
    if original_metrics is None:
        print(f"Warning: Could not load original metrics from {args.original_metrics}")
        # Create dummy metrics
        original_metrics = pd.DataFrame({
            'Metric': ['Value'],
            'Growth Rate R2': [-0.1605],
            'Growth Rate RMSE': [4.1658],
            'Growth Rate MAE': [2.9091],
            'Temperature R2': [-0.0746],
            'Temperature RMSE': [12.2842],
            'Temperature MAE': [7.4826]
        })
    
    if improved_metrics is None:
        print(f"Warning: Could not load improved metrics from {args.improved_metrics}")
    
    if enhanced_metrics is None:
        print(f"Warning: Could not load enhanced metrics from {args.enhanced_metrics}")
    
    if enhanced_optimized_metrics is None:
        print(f"Warning: Could not load enhanced optimized metrics from {args.enhanced_optimized_metrics}")
        # If enhanced optimized metrics don't exist, use enhanced metrics
        if enhanced_metrics is not None:
            enhanced_optimized_metrics = enhanced_metrics.copy()
    
    # Create comparison DataFrame for R2
    r2_comparison = pd.DataFrame({
        'Model': ['Original Hybrid', 'Improved Hybrid', 'Enhanced Hybrid', 'Enhanced Optimized']
    })
    
    # Add growth rate R2
    r2_comparison['Growth Rate R2'] = [
        original_metrics['Growth Rate R2'].values[0] if original_metrics is not None else None,
        improved_metrics.loc[improved_metrics['Metric'] == 'R2', 'Growth Rate'].values[0] if improved_metrics is not None else None,
        enhanced_metrics.loc[enhanced_metrics['Metric'] == 'R2', 'Growth Rate'].values[0] if enhanced_metrics is not None else None,
        enhanced_optimized_metrics.loc[enhanced_optimized_metrics['Metric'] == 'R2', 'Growth Rate'].values[0] if enhanced_optimized_metrics is not None else None
    ]
    
    # Add temperature R2
    r2_comparison['Temperature R2'] = [
        original_metrics['Temperature R2'].values[0] if original_metrics is not None else None,
        improved_metrics.loc[improved_metrics['Metric'] == 'R2', 'Optimal Temperature'].values[0] if improved_metrics is not None else None,
        enhanced_metrics.loc[enhanced_metrics['Metric'] == 'R2', 'Optimal Temperature'].values[0] if enhanced_metrics is not None else None,
        enhanced_optimized_metrics.loc[enhanced_optimized_metrics['Metric'] == 'R2', 'Optimal Temperature'].values[0] if enhanced_optimized_metrics is not None else None
    ]
    
    # Create comparison DataFrame for RMSE
    rmse_comparison = pd.DataFrame({
        'Model': ['Original Hybrid', 'Improved Hybrid', 'Enhanced Hybrid', 'Enhanced Optimized']
    })
    
    # Add growth rate RMSE
    rmse_comparison['Growth Rate RMSE'] = [
        original_metrics['Growth Rate RMSE'].values[0] if original_metrics is not None else None,
        improved_metrics.loc[improved_metrics['Metric'] == 'RMSE', 'Growth Rate'].values[0] if improved_metrics is not None else None,
        enhanced_metrics.loc[enhanced_metrics['Metric'] == 'RMSE', 'Growth Rate'].values[0] if enhanced_metrics is not None else None,
        enhanced_optimized_metrics.loc[enhanced_optimized_metrics['Metric'] == 'RMSE', 'Growth Rate'].values[0] if enhanced_optimized_metrics is not None else None
    ]
    
    # Add temperature RMSE
    rmse_comparison['Temperature RMSE'] = [
        original_metrics['Temperature RMSE'].values[0] if original_metrics is not None else None,
        improved_metrics.loc[improved_metrics['Metric'] == 'RMSE', 'Optimal Temperature'].values[0] if improved_metrics is not None else None,
        enhanced_metrics.loc[enhanced_metrics['Metric'] == 'RMSE', 'Optimal Temperature'].values[0] if enhanced_metrics is not None else None,
        enhanced_optimized_metrics.loc[enhanced_optimized_metrics['Metric'] == 'RMSE', 'Optimal Temperature'].values[0] if enhanced_optimized_metrics is not None else None
    ]
    
    # Save comparison to CSV
    r2_comparison.to_csv(os.path.join(args.output_dir, 'r2_comparison.csv'), index=False)
    rmse_comparison.to_csv(os.path.join(args.output_dir, 'rmse_comparison.csv'), index=False)
    
    # Create bar plots for R2
    plt.figure(figsize=(12, 8))
    r2_melted = pd.melt(r2_comparison, id_vars=['Model'], var_name='Target', value_name='R2')
    sns.barplot(x='Model', y='R2', hue='Target', data=r2_melted)
    plt.title('R² Comparison Across Models')
    plt.ylabel('R² Score')
    plt.ylim(-0.2, 1.0)  # Set y-axis limits to include negative values
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig(os.path.join(args.output_dir, 'r2_comparison.png'))
    
    # Create bar plots for RMSE
    plt.figure(figsize=(12, 8))
    rmse_melted = pd.melt(rmse_comparison, id_vars=['Model'], var_name='Target', value_name='RMSE')
    sns.barplot(x='Model', y='RMSE', hue='Target', data=rmse_melted)
    plt.title('RMSE Comparison Across Models')
    plt.ylabel('RMSE')
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig(os.path.join(args.output_dir, 'rmse_comparison.png'))
    
    print("Comparison saved to:")
    print(f"  {os.path.join(args.output_dir, 'r2_comparison.csv')}")
    print(f"  {os.path.join(args.output_dir, 'rmse_comparison.csv')}")
    print(f"  {os.path.join(args.output_dir, 'r2_comparison.png')}")
    print(f"  {os.path.join(args.output_dir, 'rmse_comparison.png')}")

if __name__ == "__main__":
    main()
