#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Predict and Train with Enhanced DNN Temperature Model.
This script uses the enhanced DNN model for temperature prediction and includes training functionality.
"""

import os
import argparse
import logging
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import torch
import joblib
from typing import Dict, List, Tuple, Optional
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from sklearn.preprocessing import RobustScaler, StandardScaler
from scipy import stats

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import the RegularizedDNN model
from evaluate_enhanced_dnn_temperature import RegularizedDNN

# Import the hybrid model if available
try:
    from hybrid_model_with_enhanced_temp_dnn import HybridModelWithEnhancedTempDNN
    HYBRID_MODEL_AVAILABLE = True
except ImportError:
    logger.warning("Hybrid model not available. Training will use only the enhanced DNN model.")
    HYBRID_MODEL_AVAILABLE = False

def detect_outliers(values, zscore_threshold=7.0):
    """
    Detect outliers using z-score method.

    Args:
        values: Series of values
        zscore_threshold: Z-score threshold for outlier detection

    Returns:
        Boolean Series indicating outliers
    """
    from scipy import stats

    logger.info(f"Detecting outliers using z-score method (threshold: {zscore_threshold})...")

    # Drop NaN values
    valid_values = values.dropna()

    # Calculate z-scores
    z_scores = stats.zscore(valid_values)

    # Identify outliers based on z-score
    outliers_array = abs(z_scores) > zscore_threshold

    # Convert back to Series with original indices
    outliers = pd.Series(False, index=values.index)
    outliers[valid_values.index] = outliers_array

    # Log results
    logger.info(f"Identified {outliers.sum()} outliers ({outliers.sum()/len(values):.2%})")

    return outliers

def load_data(feature_file: str, metadata_file: str, filter_outliers: bool = False, percentile: float = 99.0,
              apply_sqrt: bool = False, use_zscore: bool = False, zscore_threshold: float = 7.0) -> tuple:
    """
    Load feature and metadata files.

    Args:
        feature_file: Path to feature file
        metadata_file: Path to metadata file
        filter_outliers: Whether to filter outliers in temperature
        percentile: Percentile threshold for filtering outliers
        apply_sqrt: Whether to apply square root transformation to temperature
        use_zscore: Whether to use z-score method for outlier detection
        zscore_threshold: Z-score threshold for outlier detection

    Returns:
        Tuple of (features, metadata)
    """
    logger.info(f"Loading features from {feature_file}")
    features = pd.read_csv(feature_file, sep='\t', index_col='genome_id')

    logger.info(f"Loading metadata from {metadata_file}")
    metadata = pd.read_csv(metadata_file, sep='\t', index_col='genome_id')

    # Get common samples
    common_indices = features.index.intersection(metadata.index)
    logger.info(f"Found {len(common_indices)} common samples")

    # Filter to common samples
    features = features.loc[common_indices]
    metadata = metadata.loc[common_indices]

    # Check for NaN values in features
    nan_count = features.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in features. Filling with 0.")
        features = features.fillna(0)

    # Check for infinite values
    inf_count = np.isinf(features.values).sum()
    if inf_count > 0:
        logger.warning(f"Found {inf_count} infinite values in features. Replacing with max/min values.")
        features = features.replace([np.inf, -np.inf], np.nan)
        features = features.fillna(features.max())

    # Check for NaN values in metadata
    nan_count = metadata.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in metadata. Filling with 0.")
        metadata = metadata.fillna(0)

    # Log statistics before filtering
    logger.info(f"Temperature statistics before filtering - Min: {metadata['optimal_temperature'].min():.4f}, Max: {metadata['optimal_temperature'].max():.4f}, Mean: {metadata['optimal_temperature'].mean():.4f}, Median: {metadata['optimal_temperature'].median():.4f}")

    # Filter outliers in temperature if requested
    if filter_outliers:
        if use_zscore:
            # Use z-score method
            logger.info(f"Using z-score method for outlier detection (threshold: {zscore_threshold})")
            temp_outliers = detect_outliers(metadata['optimal_temperature'], zscore_threshold)

            # Filter out outliers
            outlier_indices = metadata.index[temp_outliers]
            metadata = metadata[~temp_outliers]
            features = features.drop(outlier_indices, errors='ignore')
        else:
            # Use percentile method
            threshold = np.percentile(metadata['optimal_temperature'], percentile)
            logger.info(f"Using percentile method for outlier detection (threshold: {threshold:.4f}, percentile: {percentile})")

            # Count outliers
            outlier_count = (metadata['optimal_temperature'] > threshold).sum()
            logger.info(f"Found {outlier_count} outliers in temperature")

            # Filter out outliers
            outlier_indices = metadata[metadata['optimal_temperature'] > threshold].index
            metadata = metadata[metadata['optimal_temperature'] <= threshold]
            features = features.drop(outlier_indices, errors='ignore')

        # Log statistics after filtering
        logger.info(f"Temperature statistics after filtering - Min: {metadata['optimal_temperature'].min():.4f}, Max: {metadata['optimal_temperature'].max():.4f}, Mean: {metadata['optimal_temperature'].mean():.4f}, Median: {metadata['optimal_temperature'].median():.4f}")

    # Apply square root transformation to temperature if requested
    if apply_sqrt:
        logger.info("Applying square root transformation to temperature")

        # Store original temperature
        metadata['original_optimal_temperature'] = metadata['optimal_temperature']

        # Apply square root transformation
        metadata['optimal_temperature'] = np.sqrt(metadata['optimal_temperature'])

        # Log statistics after transformation
        logger.info(f"Temperature statistics after sqrt transformation - Min: {metadata['optimal_temperature'].min():.4f}, Max: {metadata['optimal_temperature'].max():.4f}, Mean: {metadata['optimal_temperature'].mean():.4f}, Median: {metadata['optimal_temperature'].median():.4f}")

    return features, metadata

def predict_temperature(features, metadata, model_dir):
    """
    Predict optimal temperature using the enhanced DNN model.

    Args:
        features: Feature DataFrame
        metadata: Metadata DataFrame
        model_dir: Directory containing the temperature model

    Returns:
        DataFrame with actual and predicted temperatures
    """
    logger.info(f"Predicting optimal temperature for {len(features)} samples")

    # Load scaler
    scaler = joblib.load(os.path.join(model_dir, 'temperature_scaler.joblib'))
    logger.info(f"Temperature scaler loaded successfully")

    # Load model
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model_state = torch.load(os.path.join(model_dir, 'temperature_model.pt'), map_location=device)

    # Extract input dimension from the first layer's weight shape
    input_dim = model_state['feature_extractor.0.weight'].shape[1]
    logger.info(f"Temperature model input dimension: {input_dim}")

    # Create model with the same architecture
    model = RegularizedDNN(
        input_dim=input_dim,
        hidden_dims=[512, 512, 384, 256, 128],
        dropout_rates=[0.3, 0.4, 0.4, 0.5, 0.5],
        use_batch_norm=True,
        activation='relu'
    )

    # Load weights
    model.load_state_dict(model_state)

    # Set model to evaluation mode
    model.eval()
    model.to(device)

    logger.info(f"Temperature model loaded successfully")

    # Get feature matrix - exclude metadata columns
    X = features.drop(['growth_rate', 'optimal_temperature', 'taxid', 'codon_table', 'kingdom'],
                     axis=1, errors='ignore')

    # Use the first input_dim features
    if X.shape[1] != input_dim:
        logger.warning(f"Feature mismatch: model expects {input_dim} features, but got {X.shape[1]}")
        X = X.iloc[:, :input_dim]
        logger.info(f"Using first {X.shape[1]} features for temperature prediction")

    # Scale features
    X_scaled = scaler.transform(X)

    # Convert to tensor
    X_tensor = torch.tensor(X_scaled, dtype=torch.float32).to(device)

    # Generate predictions
    with torch.no_grad():
        y_pred = model(X_tensor)
        y_pred = y_pred.cpu().numpy()

    # Create results DataFrame
    if 'original_optimal_temperature' in metadata.columns:
        # If square root transformation was applied
        # The model was trained on original temperature values, so we need to
        # square the actual values (which are sqrt-transformed) before comparing
        results = pd.DataFrame({
            'genome_id': features.index,
            'actual_temp': metadata['original_optimal_temperature'],
            'predicted_temp': y_pred  # Model already predicts in original scale
        })
    else:
        results = pd.DataFrame({
            'genome_id': features.index,
            'actual_temp': metadata['optimal_temperature'],
            'predicted_temp': y_pred
        })

    # Calculate metrics
    r2 = r2_score(results['actual_temp'], results['predicted_temp'])
    rmse = np.sqrt(mean_squared_error(results['actual_temp'], results['predicted_temp']))
    mae = mean_absolute_error(results['actual_temp'], results['predicted_temp'])

    logger.info(f"Temperature: R²={r2:.4f}, RMSE={rmse:.4f}, MAE={mae:.4f}")

    return results, {
        'R2': r2,
        'RMSE': rmse,
        'MAE': mae
    }

def prepare_data_with_selective_transform(
    features: pd.DataFrame,
    metadata: pd.DataFrame,
    n_features: int = 150,
    output_dir: Optional[str] = None,
    use_zscore: bool = False,
    zscore_threshold: float = 7.0
) -> Tuple[pd.DataFrame, pd.Series, pd.Series, pd.DataFrame, pd.Series, pd.Series, pd.DataFrame, pd.Series, pd.Series, List[str], Dict[str, any]]:
    """
    Prepare data for multi-task learning with selective transformation and proper train/validation/test split.
    Applies square root transformation only to growth rate, keeping temperature in original scale.
    Includes z-score-based outlier detection.

    Args:
        features: Feature DataFrame
        metadata: Metadata DataFrame
        n_features: Number of features to select
        output_dir: Directory to save outputs
        use_zscore: Whether to use z-score method for outlier detection
        zscore_threshold: Z-score threshold for outlier detection

    Returns:
        Tuple of (X_train, y_train_growth, y_train_temp, X_val, y_val_growth, y_val_temp,
                 X_test, y_test_growth, y_test_temp, selected_features, transform_info)
    """
    logger.info("Preparing data for multi-task learning with selective transformation...")

    # Extract targets
    y_growth_original = metadata['growth_rate']
    y_temp_original = metadata['optimal_temperature']

    # Apply square root transformation only to growth rate
    logger.info("Applying square root transformation to growth rate only")
    y_growth = np.sqrt(y_growth_original)
    y_temp = y_temp_original  # Keep temperature in original scale

    # Detect outliers using z-score method if requested
    if use_zscore:
        logger.info(f"Detecting outliers using z-score method (threshold: {zscore_threshold})")

        # Detect outliers in growth rate (using transformed values)
        growth_outliers = detect_outliers(y_growth, zscore_threshold)
        logger.info(f"Identified {growth_outliers.sum()} growth rate outliers ({growth_outliers.sum()/len(y_growth):.2%})")

        # Detect outliers in temperature
        temp_outliers = detect_outliers(y_temp, zscore_threshold)
        logger.info(f"Identified {temp_outliers.sum()} temperature outliers ({temp_outliers.sum()/len(y_temp):.2%})")

        # Combine outliers (either growth rate or temperature)
        combined_outliers = growth_outliers | temp_outliers
        logger.info(f"Identified {combined_outliers.sum()} combined outliers ({combined_outliers.sum()/len(y_growth):.2%})")

        # Filter out outliers
        non_outlier_indices = metadata.index[~combined_outliers]
        features = features.loc[non_outlier_indices]
        y_growth = y_growth.loc[non_outlier_indices]
        y_temp = y_temp.loc[non_outlier_indices]
        y_growth_original = y_growth_original.loc[non_outlier_indices]
        y_temp_original = y_temp_original.loc[non_outlier_indices]

    # Create bins for stratification based on transformed growth rate
    n_bins = 10
    y_binned = pd.qcut(y_growth, n_bins, labels=False, duplicates='drop')

    # Check if we have enough samples in each bin
    bin_counts = pd.Series(y_binned).value_counts()
    min_bin_count = bin_counts.min()

    if min_bin_count < 3:
        logger.warning(f"Some bins have very few samples (min: {min_bin_count}). Using fewer bins.")
        n_bins = max(2, min(5, min_bin_count))
        y_binned = pd.qcut(y_growth, n_bins, labels=False, duplicates='drop')

    # First split data into train+val and test sets with stratification
    X_train_val, X_test, y_growth_train_val, y_growth_test, y_temp_train_val, y_temp_test, y_binned_train_val, _ = train_test_split(
        features, y_growth, y_temp, y_binned, test_size=0.2, random_state=42, shuffle=True, stratify=y_binned
    )

    # Then split train+val into train and validation sets with stratification
    X_train, X_val, y_growth_train, y_growth_val, y_temp_train, y_temp_val = train_test_split(
        X_train_val, y_growth_train_val, y_temp_train_val, test_size=0.25, random_state=42,
        shuffle=True, stratify=y_binned_train_val
    )

    logger.info(f"Data split: Train={X_train.shape[0]} samples, Validation={X_val.shape[0]} samples, Test={X_test.shape[0]} samples")

    # Check distribution of target variables in each split
    logger.info(f"Growth Rate (sqrt) - Train: mean={y_growth_train.mean():.4f}, std={y_growth_train.std():.4f}")
    logger.info(f"Growth Rate (sqrt) - Validation: mean={y_growth_val.mean():.4f}, std={y_growth_val.std():.4f}")
    logger.info(f"Growth Rate (sqrt) - Test: mean={y_growth_test.mean():.4f}, std={y_growth_test.std():.4f}")

    logger.info(f"Temperature - Train: mean={y_temp_train.mean():.4f}, std={y_temp_train.std():.4f}")
    logger.info(f"Temperature - Validation: mean={y_temp_val.mean():.4f}, std={y_temp_val.std():.4f}")
    logger.info(f"Temperature - Test: mean={y_temp_test.mean():.4f}, std={y_temp_test.std():.4f}")

    # Apply target scaling using RobustScaler for both targets
    growth_scaler = RobustScaler()
    temp_scaler = RobustScaler()

    # Reshape for scaler
    y_growth_train_reshaped = y_growth_train.values.reshape(-1, 1)
    y_growth_val_reshaped = y_growth_val.values.reshape(-1, 1)
    y_growth_test_reshaped = y_growth_test.values.reshape(-1, 1)

    y_temp_train_reshaped = y_temp_train.values.reshape(-1, 1)
    y_temp_val_reshaped = y_temp_val.values.reshape(-1, 1)
    y_temp_test_reshaped = y_temp_test.values.reshape(-1, 1)

    # Fit scalers on training data only
    growth_scaler.fit(y_growth_train_reshaped)
    temp_scaler.fit(y_temp_train_reshaped)

    # Transform all sets
    y_growth_train_scaled = growth_scaler.transform(y_growth_train_reshaped).flatten()
    y_growth_val_scaled = growth_scaler.transform(y_growth_val_reshaped).flatten()
    y_growth_test_scaled = growth_scaler.transform(y_growth_test_reshaped).flatten()

    y_temp_train_scaled = temp_scaler.transform(y_temp_train_reshaped).flatten()
    y_temp_val_scaled = temp_scaler.transform(y_temp_val_reshaped).flatten()
    y_temp_test_scaled = temp_scaler.transform(y_temp_test_reshaped).flatten()

    # Convert back to pandas Series with original indices
    y_growth_train = pd.Series(y_growth_train_scaled, index=y_growth_train.index)
    y_growth_val = pd.Series(y_growth_val_scaled, index=y_growth_val.index)
    y_growth_test = pd.Series(y_growth_test_scaled, index=y_growth_test.index)

    y_temp_train = pd.Series(y_temp_train_scaled, index=y_temp_train.index)
    y_temp_val = pd.Series(y_temp_val_scaled, index=y_temp_val.index)
    y_temp_test = pd.Series(y_temp_test_scaled, index=y_temp_test.index)

    logger.info("Applied target scaling using RobustScaler for both targets")

    # For simplicity, we'll use all features for now
    # In a real implementation, you would want to use feature selection here
    selected_features = features.columns.tolist()[:n_features]

    # Filter to selected features
    X_train = X_train[selected_features]
    X_val = X_val[selected_features]
    X_test = X_test[selected_features]

    logger.info(f"Prepared data for multi-task learning with {len(selected_features)} features")

    # Store transformation information
    transform_info = {
        'growth_scaler': growth_scaler,
        'temp_scaler': temp_scaler,
        'growth_sqrt_transform': True,
        'temp_sqrt_transform': False
    }

    # Save transformation info if output_dir is provided
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
        joblib.dump(transform_info, os.path.join(output_dir, 'transform_info.joblib'))
        logger.info(f"Saved transformation info to {output_dir}")

    return (
        X_train, y_growth_train, y_temp_train,
        X_val, y_growth_val, y_temp_val,
        X_test, y_growth_test, y_temp_test,
        selected_features, transform_info
    )

def train_model(
    X_train: pd.DataFrame,
    y_growth_train: pd.Series,
    y_temp_train: pd.Series,
    X_val: pd.DataFrame,
    y_growth_val: pd.Series,
    y_temp_val: pd.Series,
    hidden_dims: List[int] = [512, 512, 384, 256, 128],
    dropout_rates: List[float] = [0.3, 0.4, 0.4, 0.5, 0.5],
    output_dir: Optional[str] = None
) -> object:
    """
    Train model with enhanced temperature DNN.

    Args:
        X_train: Training features
        y_growth_train: Training growth rate targets
        y_temp_train: Training temperature targets
        X_val: Validation features
        y_growth_val: Validation growth rate targets
        y_temp_val: Validation temperature targets
        hidden_dims: List of hidden layer dimensions for temperature DNN
        dropout_rates: List of dropout rates for temperature DNN
        output_dir: Directory to save outputs

    Returns:
        Trained model
    """
    if HYBRID_MODEL_AVAILABLE:
        logger.info("Training hybrid model with enhanced temperature DNN...")

        # Initialize model
        model = HybridModelWithEnhancedTempDNN(
            growth_ensemble_weight_lr=0.01,
            growth_ensemble_weight_epochs=50,
            temp_dnn_hidden_dims=hidden_dims,
            temp_dnn_dropout_rates=dropout_rates,
            temp_dnn_lr=0.001,
            temp_dnn_batch_size=64,
            temp_dnn_epochs=100,
            temp_dnn_patience=15,
            variance_percentile=25
        )

        # Train model
        model.fit(
            X_train,
            y_growth_train,
            y_temp_train,
            X_val,
            y_growth_val,
            y_temp_val,
            output_dir=output_dir
        )
    else:
        logger.info("Training enhanced DNN for temperature only...")

        # Initialize feature scaler
        feature_scaler = StandardScaler()
        X_train_scaled = feature_scaler.fit_transform(X_train)
        X_val_scaled = feature_scaler.transform(X_val)

        # Convert to DataFrame
        X_train_scaled_df = pd.DataFrame(
            X_train_scaled,
            columns=X_train.columns,
            index=X_train.index
        )

        X_val_scaled_df = pd.DataFrame(
            X_val_scaled,
            columns=X_val.columns,
            index=X_val.index
        )

        # Initialize model
        input_dim = X_train.shape[1]
        model = RegularizedDNN(
            input_dim=input_dim,
            hidden_dims=hidden_dims,
            dropout_rates=dropout_rates,
            use_batch_norm=True,
            activation='relu'
        )

        # Train model
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        model.to(device)

        # Convert data to tensors
        X_train_tensor = torch.tensor(X_train_scaled, dtype=torch.float32).to(device)
        y_temp_train_tensor = torch.tensor(y_temp_train.values, dtype=torch.float32).to(device)

        X_val_tensor = torch.tensor(X_val_scaled, dtype=torch.float32).to(device)
        y_temp_val_tensor = torch.tensor(y_temp_val.values, dtype=torch.float32).to(device)

        # Define loss function and optimizer
        criterion = torch.nn.MSELoss()
        optimizer = torch.optim.Adam(model.parameters(), lr=0.001)

        # Training loop
        batch_size = 64
        epochs = 100
        patience = 15
        best_val_loss = float('inf')
        best_model_state = None
        patience_counter = 0

        for epoch in range(epochs):
            # Training
            model.train()
            train_loss = 0.0

            # Batch training
            for i in range(0, len(X_train_tensor), batch_size):
                # Get batch
                X_batch = X_train_tensor[i:i+batch_size]
                y_batch = y_temp_train_tensor[i:i+batch_size]

                # Zero gradients
                optimizer.zero_grad()

                # Forward pass
                outputs = model(X_batch)

                # Calculate loss
                loss = criterion(outputs, y_batch)

                # Backward pass and optimization
                loss.backward()
                optimizer.step()

                train_loss += loss.item() * len(X_batch)

            train_loss /= len(X_train_tensor)

            # Validation
            model.eval()
            with torch.no_grad():
                val_outputs = model(X_val_tensor)
                val_loss = criterion(val_outputs, y_temp_val_tensor).item()

                # Calculate R²
                val_r2 = r2_score(y_temp_val_tensor.cpu().numpy(), val_outputs.cpu().numpy())

            # Print progress
            if (epoch + 1) % 10 == 0:
                logger.info(f"Epoch {epoch+1}/{epochs}: Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}, Val R²: {val_r2:.4f}")

            # Check for early stopping
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                best_model_state = model.state_dict().copy()
                patience_counter = 0
            else:
                patience_counter += 1

            if patience_counter >= patience:
                logger.info(f"Early stopping at epoch {epoch+1}")
                break

        # Load best model
        model.load_state_dict(best_model_state)

        # Save model if output_dir is provided
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)

            # Save model
            torch.save(best_model_state, os.path.join(output_dir, 'temperature_model.pt'))

            # Save scaler
            joblib.dump(feature_scaler, os.path.join(output_dir, 'temperature_scaler.joblib'))

            # Save transform info
            transform_info = {
                'temp_scaler': RobustScaler(),  # This is for target scaling
                'temp_sqrt_transform': False
            }
            joblib.dump(transform_info, os.path.join(output_dir, 'transform_info.joblib'))

            logger.info(f"Saved model to {output_dir}")

    return model

def main():
    parser = argparse.ArgumentParser(description="Predict and Train with Enhanced DNN Temperature Model")
    parser.add_argument("--feature_file", required=True, help="Path to feature file")
    parser.add_argument("--metadata_file", required=True, help="Path to metadata file")
    parser.add_argument("--output_dir", default="predictions", help="Directory to save predictions")
    parser.add_argument("--model_dir", default="models/enhanced_dnn_temperature_v2", help="Directory containing the temperature model")
    parser.add_argument("--filter_outliers", action="store_true", help="Filter outliers in temperature")
    parser.add_argument("--percentile", type=float, default=99.0, help="Percentile threshold for filtering outliers")
    parser.add_argument("--apply_sqrt", action="store_true", help="Apply square root transformation to temperature")
    parser.add_argument("--use_zscore", action="store_true", help="Use z-score method for outlier detection")
    parser.add_argument("--zscore_threshold", type=float, default=7.0, help="Z-score threshold for outlier detection")
    parser.add_argument("--train", action="store_true", help="Train the model instead of just predicting")
    parser.add_argument("--n_features", type=int, default=150, help="Number of features to select for training")
    parser.add_argument("--hidden_dims", type=str, default="512,512,384,256,128", help="Hidden layer dimensions (comma-separated)")
    parser.add_argument("--dropout_rates", type=str, default="0.3,0.4,0.4,0.5,0.5", help="Dropout rates (comma-separated)")
    args = parser.parse_args()

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    if args.train:
        # Load data
        features, metadata = load_data(
            args.feature_file,
            args.metadata_file,
            filter_outliers=False,  # We'll handle outliers in prepare_data_with_selective_transform
            apply_sqrt=False  # We'll handle transformations in prepare_data_with_selective_transform
        )

        # Parse hidden dimensions and dropout rates
        hidden_dims = [int(dim) for dim in args.hidden_dims.split(',')]
        dropout_rates = [float(rate) for rate in args.dropout_rates.split(',')]

        # Prepare data for training
        (
            X_train, y_growth_train, y_temp_train,
            X_val, y_growth_val, y_temp_val,
            X_test, y_growth_test, y_temp_test,
            selected_features, transform_info
        ) = prepare_data_with_selective_transform(
            features, metadata,
            n_features=args.n_features,
            output_dir=args.output_dir,
            use_zscore=args.use_zscore,
            zscore_threshold=args.zscore_threshold
        )

        # Train model
        model = train_model(
            X_train, y_growth_train, y_temp_train,
            X_val, y_growth_val, y_temp_val,
            hidden_dims=hidden_dims,
            dropout_rates=dropout_rates,
            output_dir=args.output_dir
        )

        # Evaluate model on test set
        if HYBRID_MODEL_AVAILABLE:
            # Evaluate hybrid model
            metrics = model.evaluate(X_test, y_growth_test, y_temp_test)

            # Print final metrics
            logger.info("Hybrid model with enhanced temperature DNN - training and evaluation completed")
            logger.info(f"Final metrics - Overall R²: {metrics['overall']['R2']:.4f}")
            logger.info(f"Final metrics - Growth Rate R²: {metrics['growth_rate']['R2']:.4f}")
            logger.info(f"Final metrics - Temperature R²: {metrics['temperature']['R2']:.4f}")
        else:
            # Evaluate temperature DNN only
            device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
            model.eval()

            # Scale features
            feature_scaler = joblib.load(os.path.join(args.output_dir, 'temperature_scaler.joblib'))
            X_test_scaled = feature_scaler.transform(X_test)

            # Convert to tensor
            X_test_tensor = torch.tensor(X_test_scaled, dtype=torch.float32).to(device)

            # Generate predictions
            with torch.no_grad():
                y_temp_pred = model(X_test_tensor)
                y_temp_pred = y_temp_pred.cpu().numpy()

            # Calculate metrics
            r2 = r2_score(y_temp_test, y_temp_pred)
            rmse = np.sqrt(mean_squared_error(y_temp_test, y_temp_pred))
            mae = mean_absolute_error(y_temp_test, y_temp_pred)

            logger.info(f"Temperature DNN - training and evaluation completed")
            logger.info(f"Final metrics - Temperature R²: {r2:.4f}, RMSE: {rmse:.4f}, MAE: {mae:.4f}")
    else:
        # Load data for prediction
        features, metadata = load_data(
            args.feature_file,
            args.metadata_file,
            filter_outliers=args.filter_outliers,
            percentile=args.percentile,
            apply_sqrt=args.apply_sqrt,
            use_zscore=args.use_zscore,
            zscore_threshold=args.zscore_threshold
        )

    # Generate predictions
    temp_results, temp_metrics = predict_temperature(features, metadata, args.model_dir)

    # Save predictions
    temp_results.to_csv(os.path.join(args.output_dir, 'temperature_predictions.tsv'), sep='\t', index=False)

    # Plot predictions
    plt.figure(figsize=(10, 6))
    plt.scatter(temp_results['actual_temp'], temp_results['predicted_temp'], alpha=0.5)
    plt.plot([min(temp_results['actual_temp']), max(temp_results['actual_temp'])],
             [min(temp_results['actual_temp']), max(temp_results['actual_temp'])], 'r--')
    plt.xlabel('Actual Temperature')
    plt.ylabel('Predicted Temperature')
    plt.title('Temperature Predictions')
    plt.tight_layout()
    plt.savefig(os.path.join(args.output_dir, 'temperature_predictions.png'))

    # Save metrics
    pd.DataFrame({
        'metric': list(temp_metrics.keys()),
        'value': list(temp_metrics.values())
    }).to_csv(os.path.join(args.output_dir, 'temperature_metrics.tsv'), sep='\t', index=False)

    logger.info(f"Predictions saved to {args.output_dir}")

if __name__ == "__main__":
    main()
