#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Ensemble Temperature Model.

This script combines Random Forest and DNN models for temperature prediction
to create an ensemble model with improved performance.
"""

import os
import argparse
import logging
import numpy as np
import pandas as pd
import torch
import joblib
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from typing import Dict, List, Tuple, Optional, Union, Any

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Set device
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

class EnsembleTemperatureModel:
    """Ensemble model combining Random Forest and DNN for temperature prediction."""

    def __init__(self, rf_model_path: str, dnn_model_path: str = None, rf_weight: float = 0.6, dnn_weight: float = 0.4):
        """
        Initialize the ensemble model.

        Args:
            rf_model_path: Path to the Random Forest model
            dnn_model_path: Path to the DNN model (optional)
            rf_weight: Weight for the Random Forest model (between 0 and 1)
            dnn_weight: Weight for the DNN model (between 0 and 1)
        """
        # Initialize model flags
        self.has_rf_model = False
        self.has_dnn_model = False

        # Set initial weights
        self.rf_weight = rf_weight
        self.dnn_weight = dnn_weight

        # Try to load Random Forest model
        try:
            logger.info(f"Loading Random Forest model from {rf_model_path}")
            self.rf_model = joblib.load(os.path.join(rf_model_path, "rf_temp_model.pkl"))
            self.rf_feature_selector = joblib.load(os.path.join(rf_model_path, "temp_feature_selector.pkl"))
            self.has_rf_model = True
            logger.info("Random Forest model loaded successfully")
        except (FileNotFoundError, OSError) as e:
            logger.warning(f"Failed to load Random Forest model: {e}")
            self.rf_model = None
            self.rf_feature_selector = None

        # Try to load DNN model if path is provided
        if dnn_model_path:
            try:
                logger.info(f"Loading DNN model from {dnn_model_path}")
                # Load model architecture and weights
                self.dnn_model = torch.load(os.path.join(dnn_model_path, "temp_model.pt"), map_location=device)
                self.dnn_feature_selector = joblib.load(os.path.join(dnn_model_path, "temp_feature_selector.pkl"))
                self.dnn_scaler = joblib.load(os.path.join(dnn_model_path, "temp_scaler.pkl"))
                self.has_dnn_model = True
                logger.info("DNN model loaded successfully")
            except (FileNotFoundError, OSError) as e:
                logger.warning(f"Failed to load DNN model: {e}")
                self.dnn_model = None
                self.dnn_feature_selector = None
                self.dnn_scaler = None
        else:
            logger.info("No DNN model path provided, using Random Forest model only")
            self.dnn_model = None
            self.dnn_feature_selector = None
            self.dnn_scaler = None

        # Adjust weights based on available models
        if self.has_rf_model and self.has_dnn_model:
            # Validate weights
            if not np.isclose(rf_weight + dnn_weight, 1.0):
                logger.warning(f"Weights must sum to 1.0: {rf_weight} + {dnn_weight} = {rf_weight + dnn_weight}")
                # Normalize weights
                total = rf_weight + dnn_weight
                self.rf_weight = rf_weight / total
                self.dnn_weight = dnn_weight / total
                logger.info(f"Normalized weights: RF={self.rf_weight:.2f}, DNN={self.dnn_weight:.2f}")
            logger.info(f"Ensemble model initialized with RF weight: {self.rf_weight}, DNN weight: {self.dnn_weight}")
        elif self.has_rf_model:
            logger.info("Using Random Forest model only (weight=1.0)")
            self.rf_weight = 1.0
            self.dnn_weight = 0.0
        elif self.has_dnn_model:
            logger.info("Using DNN model only (weight=1.0)")
            self.rf_weight = 0.0
            self.dnn_weight = 1.0
        else:
            raise ValueError("No models available. Please provide at least one valid model path.")

    def predict(self, X: np.ndarray) -> np.ndarray:
        """
        Make predictions using the ensemble model.

        Args:
            X: Input features

        Returns:
            Ensemble predictions
        """
        # Initialize predictions
        rf_preds = None
        dnn_preds = None

        # Get RF predictions if available
        if self.has_rf_model:
            # Apply feature selection for RF model
            X_rf = self.rf_feature_selector.transform(X)

            # Get RF predictions
            rf_preds = self.rf_model.predict(X_rf)
            logger.debug(f"RF predictions - min: {np.min(rf_preds):.4f}, max: {np.max(rf_preds):.4f}, mean: {np.mean(rf_preds):.4f}")

        # Get DNN predictions if available
        if self.has_dnn_model:
            # Apply feature selection and scaling for DNN model
            X_dnn = self.dnn_feature_selector.transform(X)
            X_dnn_scaled = self.dnn_scaler.transform(X_dnn)

            # Get DNN predictions
            self.dnn_model.eval()
            with torch.no_grad():
                X_dnn_tensor = torch.FloatTensor(X_dnn_scaled).to(device)
                dnn_preds = self.dnn_model(X_dnn_tensor).cpu().numpy().flatten()
            logger.debug(f"DNN predictions - min: {np.min(dnn_preds):.4f}, max: {np.max(dnn_preds):.4f}, mean: {np.mean(dnn_preds):.4f}")

        # Combine predictions using weights
        if self.has_rf_model and self.has_dnn_model:
            # Use ensemble
            ensemble_preds = self.rf_weight * rf_preds + self.dnn_weight * dnn_preds
            logger.debug(f"Ensemble predictions - min: {np.min(ensemble_preds):.4f}, max: {np.max(ensemble_preds):.4f}, mean: {np.mean(ensemble_preds):.4f}")
        elif self.has_rf_model:
            # Use RF only
            ensemble_preds = rf_preds
        elif self.has_dnn_model:
            # Use DNN only
            ensemble_preds = dnn_preds
        else:
            # No models available, return mean of target (fallback)
            logger.error("No models available for prediction. Returning zeros.")
            ensemble_preds = np.zeros(X.shape[0])

        return ensemble_preds

    def evaluate(self, X: np.ndarray, y: np.ndarray) -> Dict[str, float]:
        """
        Evaluate the ensemble model.

        Args:
            X: Input features
            y: True temperatures

        Returns:
            Dictionary of evaluation metrics
        """
        # Get predictions
        preds = self.predict(X)

        # Calculate metrics
        r2 = r2_score(y, preds)
        rmse = np.sqrt(mean_squared_error(y, preds))
        mae = mean_absolute_error(y, preds)

        # Calculate adjusted R²
        n = len(y)
        p = X.shape[1]  # Number of predictors
        adj_r2 = 1 - (1 - r2) * (n - 1) / (n - p - 1)

        # Calculate correlation
        corr = np.corrcoef(y, preds)[0, 1]

        # Calculate sum of squares
        ss_total = np.sum((y - np.mean(y)) ** 2)
        ss_residual = np.sum((y - preds) ** 2)

        # Log metrics
        model_type = "Ensemble" if self.has_rf_model and self.has_dnn_model else "Random Forest" if self.has_rf_model else "DNN"
        logger.info(f"{model_type} model metrics:")
        logger.info(f"  R²: {r2:.6f}, Adj R²: {adj_r2:.6f}")
        logger.info(f"  RMSE: {rmse:.6f}, MAE: {mae:.6f}")
        logger.info(f"  Correlation: {corr:.6f}")
        logger.info(f"  Sum squares total: {ss_total:.6f}, Sum squares residual: {ss_residual:.6f}")

        # Log prediction stats
        logger.info(f"Prediction stats:")
        logger.info(f"  True values: min={np.min(y):.4f}, max={np.max(y):.4f}, mean={np.mean(y):.4f}")
        logger.info(f"  Predicted values: min={np.min(preds):.4f}, max={np.max(preds):.4f}, mean={np.mean(preds):.4f}")

        # Evaluate individual models if ensemble is used
        if self.has_rf_model and self.has_dnn_model:
            # Get RF predictions
            X_rf = self.rf_feature_selector.transform(X)
            rf_preds = self.rf_model.predict(X_rf)
            rf_r2 = r2_score(y, rf_preds)
            rf_rmse = np.sqrt(mean_squared_error(y, rf_preds))

            # Get DNN predictions
            X_dnn = self.dnn_feature_selector.transform(X)
            X_dnn_scaled = self.dnn_scaler.transform(X_dnn)
            self.dnn_model.eval()
            with torch.no_grad():
                X_dnn_tensor = torch.FloatTensor(X_dnn_scaled).to(device)
                dnn_preds = self.dnn_model(X_dnn_tensor).cpu().numpy().flatten()
            dnn_r2 = r2_score(y, dnn_preds)
            dnn_rmse = np.sqrt(mean_squared_error(y, dnn_preds))

            # Log individual model metrics
            logger.info(f"Individual model metrics:")
            logger.info(f"  Random Forest - R²: {rf_r2:.6f}, RMSE: {rf_rmse:.6f}")
            logger.info(f"  DNN - R²: {dnn_r2:.6f}, RMSE: {dnn_rmse:.6f}")
            logger.info(f"  Ensemble - R²: {r2:.6f}, RMSE: {rmse:.6f}")

        # Return metrics
        return {
            'r2': r2,
            'adj_r2': adj_r2,
            'rmse': rmse,
            'mae': mae,
            'correlation': corr,
            'ss_total': ss_total,
            'ss_residual': ss_residual
        }

    def save(self, output_dir: str):
        """
        Save the ensemble model.

        Args:
            output_dir: Directory to save the model
        """
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)

        # Save model parameters
        params = {
            'rf_weight': self.rf_weight,
            'dnn_weight': self.dnn_weight,
            'has_rf_model': self.has_rf_model,
            'has_dnn_model': self.has_dnn_model
        }

        joblib.dump(params, os.path.join(output_dir, "ensemble_params.pkl"))

        # Save RF model if available
        if self.has_rf_model:
            joblib.dump(self.rf_model, os.path.join(output_dir, "rf_model.pkl"))
            joblib.dump(self.rf_feature_selector, os.path.join(output_dir, "rf_feature_selector.pkl"))

        # Save DNN model if available
        if self.has_dnn_model:
            torch.save(self.dnn_model, os.path.join(output_dir, "dnn_model.pt"))
            joblib.dump(self.dnn_feature_selector, os.path.join(output_dir, "dnn_feature_selector.pkl"))
            joblib.dump(self.dnn_scaler, os.path.join(output_dir, "dnn_scaler.pkl"))

        # Log model type
        model_type = "Ensemble" if self.has_rf_model and self.has_dnn_model else "Random Forest" if self.has_rf_model else "DNN"
        logger.info(f"{model_type} model saved to {output_dir}")

def load_data(feature_file: str, metadata_file: str) -> Dict[str, Any]:
    """
    Load data for model evaluation.

    Args:
        feature_file: Path to feature file
        metadata_file: Path to metadata file

    Returns:
        Dictionary containing data splits
    """
    # Load features
    logger.info(f"Loading features from {feature_file}")
    features = pd.read_csv(feature_file, sep='\t', index_col='genome_id')

    # Load metadata
    logger.info(f"Loading metadata from {metadata_file}")
    metadata = pd.read_csv(metadata_file, sep='\t', index_col='genome_id')

    # Get common samples
    common_indices = features.index.intersection(metadata.index)
    logger.info(f"Found {len(common_indices)} common samples")

    # Filter to common samples
    features = features.loc[common_indices]
    metadata = metadata.loc[common_indices]

    # Fill missing values
    features = features.fillna(0)

    # Extract temperature
    temperature = metadata['optimal_temperature']

    # Split data
    X_train, X_test, y_train, y_test = train_test_split(
        features.values, temperature.values,
        test_size=0.1,
        random_state=42
    )

    # Further split training data into train and validation
    X_train, X_val, y_train, y_val = train_test_split(
        X_train, y_train,
        test_size=0.22,
        random_state=42
    )

    logger.info(f"Data split - Train: {X_train.shape[0]}, Val: {X_val.shape[0]}, Test: {X_test.shape[0]}")

    return {
        'X_train': X_train,
        'X_val': X_val,
        'X_test': X_test,
        'y_train': y_train,
        'y_val': y_val,
        'y_test': y_test,
        'features': features,
        'temperature': temperature
    }

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Ensemble Temperature Model')
    parser.add_argument('--rf_model_dir', type=str, required=True, help='Directory containing the Random Forest model')
    parser.add_argument('--dnn_model_dir', type=str, help='Directory containing the DNN model (optional)')
    parser.add_argument('--output_dir', type=str, required=True, help='Directory to save the ensemble model')
    parser.add_argument('--rf_weight', type=float, default=0.6, help='Weight for the Random Forest model')
    parser.add_argument('--dnn_weight', type=float, default=0.4, help='Weight for the DNN model')
    parser.add_argument('--feature_file', type=str, required=True, help='Path to feature file')
    parser.add_argument('--metadata_file', type=str, required=True, help='Path to metadata file')
    parser.add_argument('--verbose', action='store_true', help='Enable verbose output')

    args = parser.parse_args()

    # Set logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.INFO)
    else:
        logging.getLogger().setLevel(logging.WARNING)

    # Load data
    data = load_data(args.feature_file, args.metadata_file)

    # Initialize ensemble model
    ensemble_model = EnsembleTemperatureModel(
        rf_model_path=args.rf_model_dir,
        dnn_model_path=args.dnn_model_dir,
        rf_weight=args.rf_weight,
        dnn_weight=args.dnn_weight
    )

    # Evaluate on validation set
    logger.info("Evaluating model on validation set")
    val_metrics = ensemble_model.evaluate(data['X_val'], data['y_val'])

    # Evaluate on test set
    logger.info("Evaluating model on test set")
    test_metrics = ensemble_model.evaluate(data['X_test'], data['y_test'])

    # Save metrics
    metrics = {
        'validation_r2': val_metrics['r2'],
        'validation_rmse': val_metrics['rmse'],
        'validation_mae': val_metrics['mae'],
        'test_r2': test_metrics['r2'],
        'test_rmse': test_metrics['rmse'],
        'test_mae': test_metrics['mae']
    }

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Save metrics
    pd.DataFrame([metrics]).to_csv(os.path.join(args.output_dir, "model_metrics.csv"), index=False)

    # Save model
    ensemble_model.save(args.output_dir)

    # Determine model type
    model_type = "Ensemble" if ensemble_model.has_rf_model and ensemble_model.has_dnn_model else "Random Forest" if ensemble_model.has_rf_model else "DNN"

    # Print final results
    print("\n============================================================")
    print(f"{model_type} Temperature Model Results:")
    print(f"  Validation - R²: {val_metrics['r2']:.4f}, RMSE: {val_metrics['rmse']:.4f}, MAE: {val_metrics['mae']:.4f}")
    print(f"  Test - R²: {test_metrics['r2']:.4f}, RMSE: {test_metrics['rmse']:.4f}, MAE: {test_metrics['mae']:.4f}")
    print("============================================================")

if __name__ == "__main__":
    main()
