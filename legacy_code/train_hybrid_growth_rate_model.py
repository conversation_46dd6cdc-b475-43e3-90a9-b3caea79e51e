#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Train Hybrid Growth Rate Model.
This script trains a hybrid model for growth rate prediction that combines
ensemble methods with a neural network component.
"""

import os
import argparse
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from sklearn.preprocessing import RobustScaler
import matplotlib.pyplot as plt

from hybrid_growth_rate_model import HybridGrowthRateModel
from enhanced_feature_selection import select_features_ensemble

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_data(feature_file: str, metadata_file: str) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    Load feature and metadata files.

    Args:
        feature_file: Path to feature file
        metadata_file: Path to metadata file

    Returns:
        Tuple of (features, metadata)
    """
    logger.info(f"Loading features from {feature_file}")
    features = pd.read_csv(feature_file, sep='\t', index_col='genome_id')

    logger.info(f"Loading metadata from {metadata_file}")
    metadata = pd.read_csv(metadata_file, sep='\t', index_col='genome_id')

    # Get common samples
    common_indices = features.index.intersection(metadata.index)
    logger.info(f"Found {len(common_indices)} common samples")

    # Filter to common samples
    features = features.loc[common_indices]
    metadata = metadata.loc[common_indices]

    # Check for NaN values in features
    nan_count = features.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in features. Filling with 0.")
        features = features.fillna(0)

    # Check for infinite values
    inf_count = np.isinf(features.values).sum()
    if inf_count > 0:
        logger.warning(f"Found {inf_count} infinite values in features. Replacing with max/min values.")
        features = features.replace([np.inf, -np.inf], np.nan)
        features = features.fillna(features.max())

    # Check for NaN values in metadata
    nan_count = metadata.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in metadata. Filling with 0.")
        metadata = metadata.fillna(0)

    return features, metadata

def prepare_data(features: pd.DataFrame, metadata: pd.DataFrame,
                n_features: int = 150, output_dir: Optional[str] = None) -> Tuple[pd.DataFrame, pd.Series, pd.DataFrame, pd.Series, pd.DataFrame, pd.Series, List[str], Optional[object]]:
    """
    Prepare data for growth rate prediction with improved train/validation/test split and target scaling.

    Args:
        features: Feature DataFrame
        metadata: Metadata DataFrame
        n_features: Number of features to select
        output_dir: Directory to save outputs

    Returns:
        Tuple of (X_train, y_train, X_val, y_val, X_test, y_test, selected_features, target_scaler)
    """
    logger.info("Preparing data for growth rate prediction...")

    # Extract target
    y = metadata['growth_rate']

    # Create bins for stratification to ensure similar distributions
    # This helps with ensuring similar distributions of the target variable
    n_bins = 10
    y_binned = pd.qcut(y, n_bins, labels=False, duplicates='drop')

    # Check if we have enough samples in each bin
    bin_counts = pd.Series(y_binned).value_counts()
    min_bin_count = bin_counts.min()

    if min_bin_count < 3:
        logger.warning(f"Some bins have very few samples (min: {min_bin_count}). Using fewer bins.")
        n_bins = max(2, min(5, min_bin_count))
        y_binned = pd.qcut(y, n_bins, labels=False, duplicates='drop')

    # First split data into train+val and test sets with stratification
    X_train_val, X_test, y_train_val, y_test, y_binned_train_val, _ = train_test_split(
        features, y, y_binned, test_size=0.2, random_state=42, shuffle=True, stratify=y_binned
    )

    # Then split train+val into train and validation sets with stratification
    X_train, X_val, y_train, y_val = train_test_split(
        X_train_val, y_train_val, test_size=0.25, random_state=42, shuffle=True, stratify=y_binned_train_val
    )

    logger.info(f"Data split: Train={X_train.shape[0]} samples, Validation={X_val.shape[0]} samples, Test={X_test.shape[0]} samples")

    # Check distribution of target variable in each split
    train_mean, train_std = y_train.mean(), y_train.std()
    val_mean, val_std = y_val.mean(), y_val.std()
    test_mean, test_std = y_test.mean(), y_test.std()

    logger.info(f"Target distribution - Train: mean={train_mean:.4f}, std={train_std:.4f}")
    logger.info(f"Target distribution - Validation: mean={val_mean:.4f}, std={val_std:.4f}")
    logger.info(f"Target distribution - Test: mean={test_mean:.4f}, std={test_std:.4f}")

    # Check for significant differences in distributions
    if abs(train_mean - test_mean) > 0.5 * train_std or abs(val_mean - test_mean) > 0.5 * val_std:
        logger.warning("Significant difference in target distributions between splits!")
        logger.warning("This may lead to poor generalization to the test set.")

    # Apply target scaling using RobustScaler (important improvement from hybrid model)
    target_scaler = RobustScaler()

    # Reshape for scaler
    y_train_reshaped = y_train.values.reshape(-1, 1)
    y_val_reshaped = y_val.values.reshape(-1, 1)
    y_test_reshaped = y_test.values.reshape(-1, 1)

    # Fit scaler on training data only
    target_scaler.fit(y_train_reshaped)

    # Transform all sets
    y_train_scaled = target_scaler.transform(y_train_reshaped).flatten()
    y_val_scaled = target_scaler.transform(y_val_reshaped).flatten()
    y_test_scaled = target_scaler.transform(y_test_reshaped).flatten()

    # Convert back to pandas Series with original indices
    y_train = pd.Series(y_train_scaled, index=y_train.index)
    y_val = pd.Series(y_val_scaled, index=y_val.index)
    y_test = pd.Series(y_test_scaled, index=y_test.index)

    logger.info("Applied target scaling using RobustScaler")
    logger.info(f"Scaled target distribution - Train: mean={y_train.mean():.4f}, std={y_train.std():.4f}")
    logger.info(f"Scaled target distribution - Validation: mean={y_val.mean():.4f}, std={y_val.std():.4f}")
    logger.info(f"Scaled target distribution - Test: mean={y_test.mean():.4f}, std={y_test.std():.4f}")

    # Select features using ensemble of methods
    if output_dir:
        feature_output_dir = os.path.join(output_dir, 'features')
        os.makedirs(feature_output_dir, exist_ok=True)
    else:
        feature_output_dir = None

    # Use training data for feature selection with validation data for evaluation
    selected_features = select_features_ensemble(
        X_train, y_train, n_features, feature_output_dir,
        X_val=X_val, y_val=y_val
    )

    # Filter to selected features
    X_train = X_train[selected_features]
    X_val = X_val[selected_features]
    X_test = X_test[selected_features]

    logger.info(f"Prepared data for growth rate prediction with {len(selected_features)} features")

    return X_train, y_train, X_val, y_val, X_test, y_test, selected_features, target_scaler

def train_model(
    X_train: pd.DataFrame,
    y_train: pd.Series,
    X_val: pd.DataFrame,
    y_val: pd.Series,
    n_features: int = 150,
    use_interactions: bool = True,
    output_dir: Optional[str] = None
) -> HybridGrowthRateModel:
    """
    Train hybrid growth rate model.

    Args:
        X_train: Training features
        y_train: Training targets
        X_val: Validation features
        y_val: Validation targets
        n_features: Number of features to select
        use_interactions: Whether to use interaction features
        output_dir: Directory to save outputs

    Returns:
        Trained model
    """
    logger.info("Training hybrid growth rate model...")

    # Initialize model
    model = HybridGrowthRateModel(
        nn_hidden_dim=128,
        nn_dropout=0.3,
        nn_lr=0.001,
        nn_batch_size=64,
        nn_epochs=100,
        nn_patience=10,
        ensemble_weight_lr=0.01,
        ensemble_weight_epochs=50
    )

    # Train model
    model.fit(
        X_train,
        y_train,
        X_val,
        y_val,
        n_features=n_features,
        use_interactions=use_interactions,
        output_dir=output_dir
    )

    return model

def evaluate_model(
    model: HybridGrowthRateModel,
    X_test: pd.DataFrame,
    y_test: pd.Series,
    output_dir: Optional[str] = None
) -> Dict[str, float]:
    """
    Evaluate model on test data.

    Args:
        model: Trained model
        X_test: Test features
        y_test: Test targets
        output_dir: Directory to save outputs

    Returns:
        Dictionary of evaluation metrics
    """
    logger.info("Evaluating model on test data...")

    # Evaluate model
    metrics = model.evaluate(X_test, y_test)

    # Plot predictions vs actual values
    if output_dir:
        # Generate predictions
        y_pred = model.predict(X_test)

        # Create scatter plot
        plt.figure(figsize=(10, 6))
        plt.scatter(y_test, y_pred, alpha=0.5)
        plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--')
        plt.xlabel('Actual Growth Rate (Scaled)')
        plt.ylabel('Predicted Growth Rate (Scaled)')
        plt.title(f'Growth Rate Predictions (R² = {metrics["R2"]:.4f}, RMSE = {metrics["RMSE"]:.4f})')
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'test_predictions.png'))

        # Create residual plot
        plt.figure(figsize=(10, 6))
        residuals = y_test - y_pred
        plt.scatter(y_pred, residuals, alpha=0.5)
        plt.axhline(y=0, color='r', linestyle='--')
        plt.xlabel('Predicted Growth Rate (Scaled)')
        plt.ylabel('Residuals')
        plt.title('Residual Plot for Growth Rate Predictions')
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'test_residuals.png'))

    return metrics

def main():
    parser = argparse.ArgumentParser(description="Train and evaluate hybrid growth rate model")
    parser.add_argument("--feature_file", required=True, help="Path to feature file")
    parser.add_argument("--metadata_file", required=True, help="Path to metadata file")
    parser.add_argument("--output_dir", default="models/hybrid_growth_rate", help="Directory to save outputs")
    parser.add_argument("--n_features", type=int, default=150, help="Number of features to select")
    parser.add_argument("--use_interactions", action="store_true", help="Use interaction features")
    args = parser.parse_args()

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Load data
    features, metadata = load_data(args.feature_file, args.metadata_file)

    # Prepare data with proper train/validation/test split and target scaling
    X_train, y_train, X_val, y_val, X_test, y_test, selected_features, target_scaler = prepare_data(
        features, metadata, args.n_features, args.output_dir
    )

    # Train model
    model = train_model(
        X_train,
        y_train,
        X_val,
        y_val,
        n_features=args.n_features,
        use_interactions=args.use_interactions,
        output_dir=args.output_dir
    )

    # Evaluate model on the test set
    metrics = evaluate_model(model, X_test, y_test, args.output_dir)

    # Save the target scaler for later use
    if args.output_dir:
        import joblib
        joblib.dump(target_scaler, os.path.join(args.output_dir, 'target_scaler.joblib'))
        logger.info(f"Saved target scaler to {os.path.join(args.output_dir, 'target_scaler.joblib')}")

    # Convert metrics back to original scale for better interpretability
    if target_scaler is not None:
        # For test metrics
        test_rmse_orig = metrics['RMSE'] * target_scaler.scale_[0]
        test_mae_orig = metrics['MAE'] * target_scaler.scale_[0]

        logger.info("Hybrid growth rate model training and evaluation completed")
        logger.info(f"Test metrics - R²: {metrics['R2']:.4f}, RMSE (scaled): {metrics['RMSE']:.4f}, MAE (scaled): {metrics['MAE']:.4f}")
        logger.info(f"Test metrics (original scale) - RMSE: {test_rmse_orig:.4f}, MAE: {test_mae_orig:.4f}")
    else:
        logger.info("Hybrid growth rate model training and evaluation completed")
        logger.info(f"Test metrics - R²: {metrics['R2']:.4f}, RMSE: {metrics['RMSE']:.4f}, MAE: {metrics['MAE']:.4f}")

if __name__ == "__main__":
    main()
