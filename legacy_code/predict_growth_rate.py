#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Predict Growth Rate.
This script uses the best model for growth rate prediction (R² = 0.9207).
"""

import os
import argparse
import logging
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import torch
import joblib
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class GrowthRatePredictor:
    """
    Predictor that uses the best model for growth rate prediction.
    """
    def __init__(
        self,
        model_dir: str = 'models/hybrid_enhanced_nn_v1'
    ):
        """
        Initialize growth rate predictor.
        
        Args:
            model_dir: Directory containing the growth rate model
        """
        self.model_dir = model_dir
        
        # Load models
        self._load_models()
        
        logger.info("Growth rate predictor initialized successfully")
    
    def _load_models(self):
        """Load growth rate models."""
        logger.info(f"Loading growth rate models from {self.model_dir}")
        
        # Load feature scaler
        self.feature_scaler = joblib.load(os.path.join(self.model_dir, 'feature_scaler.joblib'))
        logger.info(f"Feature scaler loaded successfully")
        
        # Load Random Forest model
        rf_dir = os.path.join(self.model_dir, 'rf_models')
        self.rf_model = joblib.load(os.path.join(rf_dir, 'rf_growth_model.joblib'))
        logger.info(f"Random Forest model loaded successfully")
        
        # Load XGBoost model
        xgb_dir = os.path.join(self.model_dir, 'xgb_models')
        self.xgb_model = joblib.load(os.path.join(xgb_dir, 'xgb_growth_model.joblib'))
        logger.info(f"XGBoost model loaded successfully")
        
        # Load ensemble weights
        self.weights = torch.load(
            os.path.join(self.model_dir, 'growth_weights.pth'),
            map_location=torch.device('cpu')
        ).cpu().numpy()
        logger.info(f"Ensemble weights loaded successfully: {self.weights}")
    
    def predict(self, X: pd.DataFrame) -> np.ndarray:
        """
        Generate growth rate predictions.
        
        Args:
            X: Features DataFrame
            
        Returns:
            Growth rate predictions
        """
        # Handle older scikit-learn versions that don't have feature_names_in_
        if hasattr(self.feature_scaler, 'feature_names_in_'):
            # Filter features to match training data
            common_cols = [col for col in X.columns if col in self.feature_scaler.feature_names_in_]
            X_filtered = X[common_cols]
        else:
            # Use all features (assuming they're in the right order)
            X_filtered = X
        
        # Scale features
        X_scaled = self.feature_scaler.transform(X_filtered)
        
        # Get predictions from Random Forest
        rf_pred = self.rf_model.predict(X_scaled)
        logger.info(f"RF predictions - Min: {rf_pred.min():.4f}, Max: {rf_pred.max():.4f}, Mean: {rf_pred.mean():.4f}")
        
        # Get predictions from XGBoost
        xgb_pred = self.xgb_model.predict(X_scaled)
        logger.info(f"XGB predictions - Min: {xgb_pred.min():.4f}, Max: {xgb_pred.max():.4f}, Mean: {xgb_pred.mean():.4f}")
        
        # Use LightGBM predictions as XGBoost predictions (as fallback)
        lgb_pred = xgb_pred
        logger.info(f"LGB predictions (fallback) - Min: {lgb_pred.min():.4f}, Max: {lgb_pred.max():.4f}, Mean: {lgb_pred.mean():.4f}")
        
        # Combine predictions using ensemble weights
        combined_pred = (
            self.weights[0] * rf_pred + 
            self.weights[1] * xgb_pred +
            self.weights[2] * lgb_pred
        )
        
        logger.info(f"Combined predictions - Min: {combined_pred.min():.4f}, Max: {combined_pred.max():.4f}, Mean: {combined_pred.mean():.4f}")
        
        return combined_pred
    
    def evaluate(self, X: pd.DataFrame, y: pd.Series) -> dict:
        """
        Evaluate model on data.
        
        Args:
            X: Features DataFrame
            y: Growth rate targets
            
        Returns:
            Dictionary of evaluation metrics
        """
        # Generate predictions
        y_pred = self.predict(X)
        
        # Calculate metrics
        metrics = {
            'R2': r2_score(y, y_pred),
            'RMSE': np.sqrt(mean_squared_error(y, y_pred)),
            'MAE': mean_absolute_error(y, y_pred)
        }
        
        logger.info(f"Evaluation metrics - R²={metrics['R2']:.4f}, RMSE={metrics['RMSE']:.4f}, MAE={metrics['MAE']:.4f}")
        
        return metrics

def load_data(feature_file: str, metadata_file: str) -> tuple:
    """
    Load feature and metadata files.
    
    Args:
        feature_file: Path to feature file
        metadata_file: Path to metadata file
        
    Returns:
        Tuple of (features, metadata)
    """
    logger.info(f"Loading features from {feature_file}")
    features = pd.read_csv(feature_file, sep='\t', index_col='genome_id')
    
    logger.info(f"Loading metadata from {metadata_file}")
    metadata = pd.read_csv(metadata_file, sep='\t', index_col='genome_id')
    
    # Get common samples
    common_indices = features.index.intersection(metadata.index)
    logger.info(f"Found {len(common_indices)} common samples")
    
    # Filter to common samples
    features = features.loc[common_indices]
    metadata = metadata.loc[common_indices]
    
    # Check for NaN values in features
    nan_count = features.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in features. Filling with 0.")
        features = features.fillna(0)
    
    # Check for infinite values
    inf_count = np.isinf(features.values).sum()
    if inf_count > 0:
        logger.warning(f"Found {inf_count} infinite values in features. Replacing with max/min values.")
        features = features.replace([np.inf, -np.inf], np.nan)
        features = features.fillna(features.max())
    
    # Check for NaN values in metadata
    nan_count = metadata.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in metadata. Filling with 0.")
        metadata = metadata.fillna(0)
    
    return features, metadata

def main():
    parser = argparse.ArgumentParser(description="Predict Growth Rate")
    parser.add_argument("--feature_file", required=True, help="Path to feature file")
    parser.add_argument("--metadata_file", required=True, help="Path to metadata file")
    parser.add_argument("--output_dir", default="predictions", help="Directory to save predictions")
    parser.add_argument("--model_dir", default="models/hybrid_enhanced_nn_v1", help="Directory containing the growth rate model")
    args = parser.parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Load data
    features, metadata = load_data(args.feature_file, args.metadata_file)
    
    # Extract growth rate targets
    y_growth = metadata['growth_rate']
    
    # Initialize predictor
    predictor = GrowthRatePredictor(model_dir=args.model_dir)
    
    # Generate predictions
    y_growth_pred = predictor.predict(features)
    
    # Save predictions
    pd.DataFrame({
        'genome_id': features.index,
        'actual_growth': y_growth,
        'predicted_growth': y_growth_pred
    }).to_csv(os.path.join(args.output_dir, 'growth_predictions.tsv'), sep='\t', index=False)
    
    # Plot predictions
    plt.figure(figsize=(10, 6))
    plt.scatter(y_growth, y_growth_pred, alpha=0.5)
    plt.plot([min(y_growth), max(y_growth)], [min(y_growth), max(y_growth)], 'r--')
    plt.xlabel('Actual Growth Rate')
    plt.ylabel('Predicted Growth Rate')
    plt.title('Growth Rate Predictions')
    plt.tight_layout()
    plt.savefig(os.path.join(args.output_dir, 'growth_predictions.png'))
    
    # Evaluate model
    metrics = predictor.evaluate(features, y_growth)
    
    # Save metrics
    pd.DataFrame({
        'metric': list(metrics.keys()),
        'value': list(metrics.values())
    }).to_csv(os.path.join(args.output_dir, 'growth_metrics.tsv'), sep='\t', index=False)
    
    logger.info(f"Predictions saved to {args.output_dir}")

if __name__ == "__main__":
    main()
