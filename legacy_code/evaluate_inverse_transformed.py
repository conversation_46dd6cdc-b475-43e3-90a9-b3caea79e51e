import pandas as pd
import numpy as np
import os
import joblib
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
import matplotlib.pyplot as plt
import warnings

# Suppress sklearn version warnings
warnings.filterwarnings("ignore", category=UserWarning)

# Paths
pred_file = 'predictions_rl/predictions.tsv'
meta_file = 'training_data/metadata.tsv'
model_dir = 'models/final_hybrid_rl'
transform_info_path = os.path.join(model_dir, 'transform_info.joblib')
output_dir = 'predictions_rl/evaluation'

# Create output directory
os.makedirs(output_dir, exist_ok=True)

print("Loading predictions and metadata...")
# Load predictions
pred_df = pd.read_csv(pred_file, sep='\t')
print(f"Loaded {len(pred_df)} predictions")

# Load actual metadata
meta_df = pd.read_csv(meta_file, sep='\t')
print(f"Loaded {len(meta_df)} metadata samples")

# Merge on genome_id
merged_df = pd.merge(pred_df, meta_df, on='genome_id')
print(f"Merged dataset has {len(merged_df)} samples")

# Load transform info
try:
    print(f"Loading transformation information from {transform_info_path}")
    transform_info = joblib.load(transform_info_path)
    print(f"Transform info keys: {list(transform_info.keys() if isinstance(transform_info, dict) else ['not a dictionary'])}")
except Exception as e:
    print(f"Error loading transform info: {str(e)}")
    transform_info = None

# Try to find scalers
possible_scaler_paths = [
    os.path.join(model_dir, 'temp_model', 'temperature_scaler.joblib'),
    os.path.join(model_dir, 'growth_model', 'growth_rate_scaler.joblib'),
    os.path.join(model_dir, 'growth_target_scaler.joblib'),
    os.path.join(model_dir, 'temp_target_scaler.joblib'),
    os.path.join(model_dir, 'target_scaler.joblib')
]

growth_scaler = None
temp_scaler = None

for path in possible_scaler_paths:
    try:
        scaler = joblib.load(path)
        print(f"Loaded scaler from {path}")
        if 'growth' in path:
            growth_scaler = scaler
        elif 'temp' in path:
            temp_scaler = scaler
    except:
        pass

# If we couldn't find specific scalers, try to extract from transform_info
if transform_info is not None and isinstance(transform_info, dict):
    if growth_scaler is None and 'growth_target_scaler' in transform_info:
        growth_scaler = transform_info['growth_target_scaler']
        print("Using growth scaler from transform_info")
    
    if temp_scaler is None and 'temp_target_scaler' in transform_info:
        temp_scaler = transform_info['temp_target_scaler']
        print("Using temperature scaler from transform_info")

# If we still don't have scalers, check if there's a single scaler for both
if growth_scaler is None and temp_scaler is None and 'target_scaler' in transform_info:
    target_scaler = transform_info['target_scaler']
    growth_scaler = temp_scaler = target_scaler
    print("Using common target scaler for both growth and temperature")

# Apply manual scaling if needed based on analysis of the data
print("\nBefore transformation:")
print(f"Predicted growth rate range: {merged_df['growth_rate_x'].min():.4f} to {merged_df['growth_rate_x'].max():.4f}")
print(f"Actual growth rate range: {merged_df['growth_rate_y'].min():.4f} to {merged_df['growth_rate_y'].max():.4f}")
print(f"Predicted temperature range: {merged_df['optimal_temperature_x'].min():.4f} to {merged_df['optimal_temperature_x'].max():.4f}")
print(f"Actual temperature range: {merged_df['optimal_temperature_y'].min():.4f} to {merged_df['optimal_temperature_y'].max():.4f}")

# If we have transformation info, try to transform back
transformed_growth = merged_df['growth_rate_x'].values
transformed_temp = merged_df['optimal_temperature_x'].values

# Try to apply inverse transformations
print("\nAttempting to apply inverse transformations...")

# For growth rate
if 'growth_sqrt_transform' in transform_info and transform_info.get('growth_sqrt_transform', False):
    # If growth was sqrt transformed, we need to square the values
    print("Applying inverse sqrt transformation to growth rate predictions")
    # Square positive values, keep negative values as they are (or apply safe squaring)
    transformed_growth = np.where(
        transformed_growth >= 0,
        transformed_growth ** 2,
        -np.abs(transformed_growth) ** 2  # Preserve negative sign if present
    )

# For temperature - typically no special transformation beyond scaling

# Apply linear regression to learn the inverse transformation
from sklearn.linear_model import LinearRegression

# For growth rate
growth_model = LinearRegression()
growth_model.fit(
    merged_df['growth_rate_x'].values.reshape(-1, 1), 
    merged_df['growth_rate_y'].values
)
print(f"\nLinear mapping for growth rate: y = {growth_model.coef_[0]:.4f}x + {growth_model.intercept_:.4f}")
calibrated_growth = growth_model.predict(merged_df['growth_rate_x'].values.reshape(-1, 1))

# For temperature
temp_model = LinearRegression()
temp_model.fit(
    merged_df['optimal_temperature_x'].values.reshape(-1, 1), 
    merged_df['optimal_temperature_y'].values
)
print(f"Linear mapping for temperature: y = {temp_model.coef_[0]:.4f}x + {temp_model.intercept_:.4f}")
calibrated_temp = temp_model.predict(merged_df['optimal_temperature_x'].values.reshape(-1, 1))

# Calculate metrics for the raw predictions
raw_growth_r2 = r2_score(merged_df['growth_rate_y'], merged_df['growth_rate_x'])
raw_growth_rmse = np.sqrt(mean_squared_error(merged_df['growth_rate_y'], merged_df['growth_rate_x']))
raw_growth_mae = mean_absolute_error(merged_df['growth_rate_y'], merged_df['growth_rate_x'])

raw_temp_r2 = r2_score(merged_df['optimal_temperature_y'], merged_df['optimal_temperature_x'])
raw_temp_rmse = np.sqrt(mean_squared_error(merged_df['optimal_temperature_y'], merged_df['optimal_temperature_x']))
raw_temp_mae = mean_absolute_error(merged_df['optimal_temperature_y'], merged_df['optimal_temperature_x'])

# Calculate metrics for the transformed predictions
trans_growth_r2 = r2_score(merged_df['growth_rate_y'], transformed_growth)
trans_growth_rmse = np.sqrt(mean_squared_error(merged_df['growth_rate_y'], transformed_growth))
trans_growth_mae = mean_absolute_error(merged_df['growth_rate_y'], transformed_growth)

trans_temp_r2 = r2_score(merged_df['optimal_temperature_y'], transformed_temp)
trans_temp_rmse = np.sqrt(mean_squared_error(merged_df['optimal_temperature_y'], transformed_temp))
trans_temp_mae = mean_absolute_error(merged_df['optimal_temperature_y'], transformed_temp)

# Calculate metrics for the calibrated predictions
cal_growth_r2 = r2_score(merged_df['growth_rate_y'], calibrated_growth)
cal_growth_rmse = np.sqrt(mean_squared_error(merged_df['growth_rate_y'], calibrated_growth))
cal_growth_mae = mean_absolute_error(merged_df['growth_rate_y'], calibrated_growth)

cal_temp_r2 = r2_score(merged_df['optimal_temperature_y'], calibrated_temp)
cal_temp_rmse = np.sqrt(mean_squared_error(merged_df['optimal_temperature_y'], calibrated_temp))
cal_temp_mae = mean_absolute_error(merged_df['optimal_temperature_y'], calibrated_temp)

# Spearman correlation (rank correlation)
growth_spearman = merged_df['growth_rate_x'].corr(merged_df['growth_rate_y'], method='spearman')
temp_spearman = merged_df['optimal_temperature_x'].corr(merged_df['optimal_temperature_y'], method='spearman')

# Print metrics
print("\n===== EVALUATION METRICS =====")
print("\n1. Raw Predictions (Direct Model Output):")
print("Growth Rate:")
print(f"  R² Score: {raw_growth_r2:.4f}")
print(f"  RMSE: {raw_growth_rmse:.4f}")
print(f"  MAE: {raw_growth_mae:.4f}")
print("Temperature:")
print(f"  R² Score: {raw_temp_r2:.4f}")
print(f"  RMSE: {raw_temp_rmse:.4f}")
print(f"  MAE: {raw_temp_mae:.4f}")

print("\n2. Transformed Predictions (Applied Transformation):")
print("Growth Rate:")
print(f"  R² Score: {trans_growth_r2:.4f}")
print(f"  RMSE: {trans_growth_rmse:.4f}")
print(f"  MAE: {trans_growth_mae:.4f}")
print("Temperature:")
print(f"  R² Score: {trans_temp_r2:.4f}")
print(f"  RMSE: {trans_temp_rmse:.4f}")
print(f"  MAE: {trans_temp_mae:.4f}")

print("\n3. Calibrated Predictions (Linear Regression Mapping):")
print("Growth Rate:")
print(f"  R² Score: {cal_growth_r2:.4f}")
print(f"  RMSE: {cal_growth_rmse:.4f}")
print(f"  MAE: {cal_growth_mae:.4f}")
print("Temperature:")
print(f"  R² Score: {cal_temp_r2:.4f}")
print(f"  RMSE: {cal_temp_rmse:.4f}")
print(f"  MAE: {cal_temp_mae:.4f}")

print("\n4. Spearman Rank Correlation (Scale-Invariant):")
print(f"Growth Rate Correlation: {growth_spearman:.4f}")
print(f"Temperature Correlation: {temp_spearman:.4f}")

# Create visualizations
plt.figure(figsize=(18, 12))

# Raw predictions
plt.subplot(3, 2, 1)
plt.scatter(merged_df['growth_rate_y'], merged_df['growth_rate_x'], alpha=0.5)
plt.xlabel('Actual Growth Rate')
plt.ylabel('Predicted Growth Rate (Raw)')
plt.title(f'Growth Rate - Raw (R²: {raw_growth_r2:.4f})')
plt.grid(alpha=0.3)

plt.subplot(3, 2, 2)
plt.scatter(merged_df['optimal_temperature_y'], merged_df['optimal_temperature_x'], alpha=0.5)
plt.xlabel('Actual Optimal Temperature')
plt.ylabel('Predicted Optimal Temperature (Raw)')
plt.title(f'Temperature - Raw (R²: {raw_temp_r2:.4f})')
plt.grid(alpha=0.3)

# Transformed predictions
plt.subplot(3, 2, 3)
plt.scatter(merged_df['growth_rate_y'], transformed_growth, alpha=0.5)
plt.xlabel('Actual Growth Rate')
plt.ylabel('Predicted Growth Rate (Transformed)')
plt.title(f'Growth Rate - Transformed (R²: {trans_growth_r2:.4f})')
plt.grid(alpha=0.3)

plt.subplot(3, 2, 4)
plt.scatter(merged_df['optimal_temperature_y'], transformed_temp, alpha=0.5)
plt.xlabel('Actual Optimal Temperature')
plt.ylabel('Predicted Optimal Temperature (Transformed)')
plt.title(f'Temperature - Transformed (R²: {trans_temp_r2:.4f})')
plt.grid(alpha=0.3)

# Calibrated predictions
plt.subplot(3, 2, 5)
plt.scatter(merged_df['growth_rate_y'], calibrated_growth, alpha=0.5)
plt.xlabel('Actual Growth Rate')
plt.ylabel('Predicted Growth Rate (Calibrated)')
plt.title(f'Growth Rate - Calibrated (R²: {cal_growth_r2:.4f})')
plt.grid(alpha=0.3)

plt.subplot(3, 2, 6)
plt.scatter(merged_df['optimal_temperature_y'], calibrated_temp, alpha=0.5)
plt.xlabel('Actual Optimal Temperature')
plt.ylabel('Predicted Optimal Temperature (Calibrated)')
plt.title(f'Temperature - Calibrated (R²: {cal_temp_r2:.4f})')
plt.grid(alpha=0.3)

plt.tight_layout()
plt.savefig(os.path.join(output_dir, 'prediction_evaluation.png'), dpi=300)
plt.close()

# Create rank plots
plt.figure(figsize=(14, 6))

# Growth rate ranks
growth_actual_ranks = merged_df['growth_rate_y'].rank(pct=True)
growth_pred_ranks = merged_df['growth_rate_x'].rank(pct=True)
growth_rank_rmse = np.sqrt(mean_squared_error(growth_actual_ranks, growth_pred_ranks))
growth_rank_mae = mean_absolute_error(growth_actual_ranks, growth_pred_ranks)

plt.subplot(1, 2, 1)
plt.scatter(growth_actual_ranks, growth_pred_ranks, alpha=0.5)
plt.xlabel('Actual Growth Rate Percentile')
plt.ylabel('Predicted Growth Rate Percentile')
plt.title(f'Growth Rate Percentiles (MAE: {growth_rank_mae:.4f})')
plt.plot([0, 1], [0, 1], 'r--')  # Diagonal line
plt.grid(alpha=0.3)

# Temperature ranks
temp_actual_ranks = merged_df['optimal_temperature_y'].rank(pct=True)
temp_pred_ranks = merged_df['optimal_temperature_x'].rank(pct=True)
temp_rank_rmse = np.sqrt(mean_squared_error(temp_actual_ranks, temp_pred_ranks))
temp_rank_mae = mean_absolute_error(temp_actual_ranks, temp_pred_ranks)

plt.subplot(1, 2, 2)
plt.scatter(temp_actual_ranks, temp_pred_ranks, alpha=0.5)
plt.xlabel('Actual Temperature Percentile')
plt.ylabel('Predicted Temperature Percentile')
plt.title(f'Temperature Percentiles (MAE: {temp_rank_mae:.4f})')
plt.plot([0, 1], [0, 1], 'r--')  # Diagonal line
plt.grid(alpha=0.3)

plt.tight_layout()
plt.savefig(os.path.join(output_dir, 'rank_evaluation.png'), dpi=300)
plt.close()

print(f"\nVisualizations saved to {output_dir}")

# Save calibrated predictions
calibrated_df = pred_df.copy()
calibrated_df['growth_rate'] = growth_model.predict(pred_df['growth_rate'].values.reshape(-1, 1))
calibrated_df['optimal_temperature'] = temp_model.predict(pred_df['optimal_temperature'].values.reshape(-1, 1))
calibrated_df.to_csv(os.path.join(output_dir, 'calibrated_predictions.tsv'), sep='\t', index=False)
print(f"Calibrated predictions saved to {os.path.join(output_dir, 'calibrated_predictions.tsv')}")

print("\nEvaluation complete!") 