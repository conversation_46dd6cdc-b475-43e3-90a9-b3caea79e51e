#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Enhanced hybrid model for microbial growth rate and optimal temperature prediction.
This script combines TabNet for growth rate prediction and DNN with One-Cycle Learning for temperature prediction.
"""

import os
import argparse
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Union, Optional
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
import matplotlib.pyplot as plt
import seaborn as sns

# Import custom modules
from improved_feature_selection import select_optimal_features, get_feature_importance
from enhanced_growth_model import EnhancedGrowthModel
from enhanced_temperature_model import EnhancedTemperatureModel

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_data(feature_file: str, metadata_file: str) -> Tuple[pd.<PERSON><PERSON>rame, pd.DataFrame]:
    """
    Load feature and metadata files.
    
    Args:
        feature_file: Path to feature file
        metadata_file: Path to metadata file
        
    Returns:
        Tuple of (features, metadata)
    """
    logger.info(f"Loading features from {feature_file}")
    features = pd.read_csv(feature_file, sep='\t', index_col=0)
    
    logger.info(f"Loading metadata from {metadata_file}")
    metadata = pd.read_csv(metadata_file, sep='\t', index_col=0)
    
    # Fill NaN values
    features = features.fillna(0)
    metadata = metadata.fillna(0)
    
    # Get common indices
    common_indices = features.index.intersection(metadata.index)
    logger.info(f"Found {len(common_indices)} common samples")
    
    # Filter to common indices
    features = features.loc[common_indices]
    metadata = metadata.loc[common_indices]
    
    return features, metadata

def prepare_data_for_growth_rate(features: pd.DataFrame, metadata: pd.DataFrame, 
                                n_features: int = 100, output_dir: Optional[str] = None) -> Tuple[pd.DataFrame, pd.Series, pd.DataFrame, pd.Series, List[str]]:
    """
    Prepare data for growth rate prediction.
    
    Args:
        features: Feature DataFrame
        metadata: Metadata DataFrame
        n_features: Number of features to select
        output_dir: Directory to save outputs
        
    Returns:
        Tuple of (X_train, y_train, X_test, y_test, selected_features)
    """
    logger.info("Preparing data for growth rate prediction...")
    
    # Extract target
    y = metadata['growth_rate']
    
    # Split data
    X_train, X_test, y_train, y_test = train_test_split(
        features, y, test_size=0.2, random_state=42
    )
    
    # Select optimal features
    if output_dir:
        feature_output_dir = os.path.join(output_dir, 'growth_rate_features')
        os.makedirs(feature_output_dir, exist_ok=True)
    else:
        feature_output_dir = None
    
    selected_features = select_optimal_features(
        X_train, y_train, 'growth_rate', n_features, feature_output_dir
    )
    
    # Filter to selected features
    X_train = X_train[selected_features]
    X_test = X_test[selected_features]
    
    logger.info(f"Prepared data for growth rate prediction with {len(selected_features)} features")
    
    return X_train, y_train, X_test, y_test, selected_features

def prepare_data_for_temperature(features: pd.DataFrame, metadata: pd.DataFrame, 
                               n_features: int = 100, output_dir: Optional[str] = None) -> Tuple[pd.DataFrame, pd.Series, pd.DataFrame, pd.Series, List[str]]:
    """
    Prepare data for temperature prediction.
    
    Args:
        features: Feature DataFrame
        metadata: Metadata DataFrame
        n_features: Number of features to select
        output_dir: Directory to save outputs
        
    Returns:
        Tuple of (X_train, y_train, X_test, y_test, selected_features)
    """
    logger.info("Preparing data for temperature prediction...")
    
    # Extract target
    y = metadata['optimal_temperature']
    
    # Split data
    X_train, X_test, y_train, y_test = train_test_split(
        features, y, test_size=0.2, random_state=42
    )
    
    # Select optimal features
    if output_dir:
        feature_output_dir = os.path.join(output_dir, 'temperature_features')
        os.makedirs(feature_output_dir, exist_ok=True)
    else:
        feature_output_dir = None
    
    selected_features = select_optimal_features(
        X_train, y_train, 'temperature', n_features, feature_output_dir
    )
    
    # Filter to selected features
    X_train = X_train[selected_features]
    X_test = X_test[selected_features]
    
    logger.info(f"Prepared data for temperature prediction with {len(selected_features)} features")
    
    return X_train, y_train, X_test, y_test, selected_features

def train_growth_model(X_train: pd.DataFrame, y_train: pd.Series, 
                      X_val: pd.DataFrame, y_val: pd.Series,
                      optimize_hyperparams: bool = False,
                      output_dir: Optional[str] = None) -> EnhancedGrowthModel:
    """
    Train growth rate prediction model.
    
    Args:
        X_train: Training features
        y_train: Training target
        X_val: Validation features
        y_val: Validation target
        optimize_hyperparams: Whether to optimize hyperparameters
        output_dir: Directory to save outputs
        
    Returns:
        Trained growth rate model
    """
    logger.info("Training growth rate model using TabNet...")
    
    # Initialize model
    model = EnhancedGrowthModel()
    
    # Optimize hyperparameters if requested
    if optimize_hyperparams:
        logger.info("Optimizing hyperparameters for growth rate model...")
        model.optimize_hyperparameters(X_train, y_train, cv=5)
    
    # Train model
    model.fit(X_train, y_train, X_val, y_val, max_epochs=200, patience=15)
    
    # Evaluate model
    metrics = model.evaluate(X_val, y_val)
    
    # Save model if output_dir is provided
    if output_dir:
        model_output_dir = os.path.join(output_dir, 'growth_rate_model')
        os.makedirs(model_output_dir, exist_ok=True)
        
        # Save model
        model.save(model_output_dir)
        
        # Plot feature importance
        model.plot_feature_importance(output_dir=model_output_dir)
        
        # Plot predictions
        model.plot_predictions(X_val, y_val, output_dir=model_output_dir)
    
    return model

def train_temperature_model(X_train: pd.DataFrame, y_train: pd.Series, 
                           X_val: pd.DataFrame, y_val: pd.Series,
                           hidden_dims: List[int] = None,
                           dropout_rates: List[float] = None,
                           output_dir: Optional[str] = None) -> EnhancedTemperatureModel:
    """
    Train temperature prediction model.
    
    Args:
        X_train: Training features
        y_train: Training target
        X_val: Validation features
        y_val: Validation target
        hidden_dims: List of hidden dimensions
        dropout_rates: List of dropout rates
        output_dir: Directory to save outputs
        
    Returns:
        Trained temperature model
    """
    logger.info("Training temperature model using DNN with One-Cycle Learning...")
    
    # Set default hidden dimensions and dropout rates if not provided
    hidden_dims = hidden_dims or [512, 256, 128, 64]
    dropout_rates = dropout_rates or [0.3, 0.4, 0.4, 0.5]
    
    # Initialize model
    model = EnhancedTemperatureModel(hidden_dims=hidden_dims, dropout_rates=dropout_rates)
    
    # Train model
    model.fit(
        X_train, y_train, X_val, y_val,
        batch_size=64, epochs=150,
        learning_rate=0.001, weight_decay=1e-5
    )
    
    # Evaluate model
    metrics = model.evaluate(X_val, y_val)
    
    # Save model if output_dir is provided
    if output_dir:
        model_output_dir = os.path.join(output_dir, 'temperature_model')
        os.makedirs(model_output_dir, exist_ok=True)
        
        # Save model
        model.save(model_output_dir)
        
        # Plot training history
        model.plot_training_history(output_dir=model_output_dir)
        
        # Plot predictions
        model.plot_predictions(X_val, y_val, output_dir=model_output_dir)
    
    return model

def evaluate_models(growth_model: EnhancedGrowthModel, temperature_model: EnhancedTemperatureModel,
                   features: pd.DataFrame, metadata: pd.DataFrame,
                   growth_features: List[str], temperature_features: List[str],
                   output_dir: Optional[str] = None) -> Dict[str, Dict[str, float]]:
    """
    Evaluate models on test data.
    
    Args:
        growth_model: Trained growth rate model
        temperature_model: Trained temperature model
        features: Feature DataFrame
        metadata: Metadata DataFrame
        growth_features: List of features for growth rate prediction
        temperature_features: List of features for temperature prediction
        output_dir: Directory to save outputs
        
    Returns:
        Dictionary of evaluation metrics
    """
    logger.info("Evaluating models...")
    
    # Split data
    _, X_test, _, y_test = train_test_split(
        features, metadata, test_size=0.2, random_state=42
    )
    
    # Prepare test data for growth rate prediction
    X_test_growth = X_test[growth_features]
    y_test_growth = y_test['growth_rate']
    
    # Prepare test data for temperature prediction
    X_test_temp = X_test[temperature_features]
    y_test_temp = y_test['optimal_temperature']
    
    # Evaluate growth rate model
    growth_metrics = growth_model.evaluate(X_test_growth, y_test_growth)
    
    # Evaluate temperature model
    temp_metrics = temperature_model.evaluate(X_test_temp, y_test_temp)
    
    # Combine metrics
    metrics = {
        'growth_rate': growth_metrics,
        'optimal_temperature': temp_metrics
    }
    
    # Log metrics
    logger.info(f"Growth Rate - Test R²: {growth_metrics['R2']:.4f}, RMSE: {growth_metrics['RMSE']:.4f}, MAE: {growth_metrics['MAE']:.4f}")
    logger.info(f"Optimal Temperature - Test R²: {temp_metrics['R2']:.4f}, RMSE: {temp_metrics['RMSE']:.4f}, MAE: {temp_metrics['MAE']:.4f}")
    
    # Save metrics if output_dir is provided
    if output_dir:
        metrics_df = pd.DataFrame({
            'Metric': ['R2', 'RMSE', 'MAE'],
            'Growth Rate': [growth_metrics['R2'], growth_metrics['RMSE'], growth_metrics['MAE']],
            'Optimal Temperature': [temp_metrics['R2'], temp_metrics['RMSE'], temp_metrics['MAE']]
        })
        
        metrics_df.to_csv(os.path.join(output_dir, 'evaluation_metrics.csv'), index=False)
        
        # Create bar plot for R2
        plt.figure(figsize=(10, 6))
        sns.barplot(x=['Growth Rate', 'Optimal Temperature'], y=[growth_metrics['R2'], temp_metrics['R2']])
        plt.title('R² Score by Target')
        plt.ylabel('R² Score')
        plt.ylim(0, 1)
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'r2_comparison.png'))
        
        # Create bar plot for RMSE
        plt.figure(figsize=(10, 6))
        sns.barplot(x=['Growth Rate', 'Optimal Temperature'], y=[growth_metrics['RMSE'], temp_metrics['RMSE']])
        plt.title('RMSE by Target')
        plt.ylabel('RMSE')
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'rmse_comparison.png'))
    
    return metrics

def main():
    parser = argparse.ArgumentParser(description="Train enhanced hybrid model for microbial growth rate and optimal temperature prediction")
    parser.add_argument("--feature_file", required=True, help="Path to feature file")
    parser.add_argument("--metadata_file", required=True, help="Path to metadata file")
    parser.add_argument("--output_dir", default="models/enhanced_hybrid", help="Directory to save outputs")
    parser.add_argument("--n_features_growth", type=int, default=100, help="Number of features for growth rate prediction")
    parser.add_argument("--n_features_temp", type=int, default=100, help="Number of features for temperature prediction")
    parser.add_argument("--optimize_hyperparams", action="store_true", help="Optimize hyperparameters for growth rate model")
    parser.add_argument("--hidden_dims", type=int, nargs="+", default=[512, 256, 128, 64], help="Hidden dimensions for temperature model")
    parser.add_argument("--dropout_rates", type=float, nargs="+", default=[0.3, 0.4, 0.4, 0.5], help="Dropout rates for temperature model")
    args = parser.parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Load data
    features, metadata = load_data(args.feature_file, args.metadata_file)
    
    # Prepare data for growth rate prediction
    X_train_growth, y_train_growth, X_test_growth, y_test_growth, growth_features = prepare_data_for_growth_rate(
        features, metadata, args.n_features_growth, args.output_dir
    )
    
    # Prepare data for temperature prediction
    X_train_temp, y_train_temp, X_test_temp, y_test_temp, temp_features = prepare_data_for_temperature(
        features, metadata, args.n_features_temp, args.output_dir
    )
    
    # Train growth rate model
    growth_model = train_growth_model(
        X_train_growth, y_train_growth, X_test_growth, y_test_growth,
        optimize_hyperparams=args.optimize_hyperparams,
        output_dir=args.output_dir
    )
    
    # Train temperature model
    temp_model = train_temperature_model(
        X_train_temp, y_train_temp, X_test_temp, y_test_temp,
        hidden_dims=args.hidden_dims,
        dropout_rates=args.dropout_rates,
        output_dir=args.output_dir
    )
    
    # Evaluate models
    metrics = evaluate_models(
        growth_model, temp_model, features, metadata, growth_features, temp_features, args.output_dir
    )
    
    logger.info("Enhanced hybrid model training completed")

if __name__ == "__main__":
    main()
