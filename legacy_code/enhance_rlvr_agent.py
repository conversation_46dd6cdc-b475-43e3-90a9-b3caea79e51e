#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to enhance the RLVR agent architecture.

This script creates an improved version of the RLVR agent with:
1. Residual connections
2. Feature interaction layers
3. Improved initialization
4. Normalization techniques
"""

import os
import shutil
from pathlib import Path

# Define the enhanced agent code
enhanced_agent_code = """
class EnhancedRLVRAgent(nn.Module):
    """
    Enhanced Reinforcement Learning with Verifiable Rewards (RLVR) agent for growth rate prediction.
    
    This agent uses an advanced multi-branch architecture with improved attention mechanisms,
    residual connections, and feature interaction layers to process different feature types
    and make predictions. It is trained using reinforcement learning with a verifiable reward function.
    
    The architecture consists of:
    1. Feature-specific encoders with residual connections
    2. Multi-head attention with improved positional encoding
    3. Feature interaction layers for cross-feature learning
    4. Dual-head policy network for prediction
    5. Calibrated value network for uncertainty estimation
    """
    
    def __init__(
        self,
        feature_dims: Dict[str, int],
        hidden_dim: int = 256,
        num_heads: int = 4,
        num_layers: int = 2,
        dropout: float = 0.2,
        use_layer_norm: bool = True,
        use_residual: bool = True,
        use_value_head: bool = True,
        activation: str = 'relu',
        use_feature_interaction: bool = True,
        interaction_layers: int = 2,
        use_batch_norm: bool = False
    ):
        """
        Initialize the enhanced RLVR agent.
        
        Args:
            feature_dims: Dictionary mapping feature types to dimensions
            hidden_dim: Dimension of hidden layers (default: 256)
            num_heads: Number of attention heads (default: 4)
            num_layers: Number of transformer layers (default: 2)
            dropout: Dropout rate (default: 0.2)
            use_layer_norm: Whether to use layer normalization (default: True)
            use_residual: Whether to use residual connections (default: True)
            use_value_head: Whether to include a value head for uncertainty (default: True)
            activation: Activation function to use ('relu', 'gelu', or 'silu') (default: 'relu')
            use_feature_interaction: Whether to use feature interaction layers (default: True)
            interaction_layers: Number of feature interaction layers (default: 2)
            use_batch_norm: Whether to use batch normalization (default: False)
        """
        super().__init__()
        
        self.feature_dims = feature_dims
        self.hidden_dim = hidden_dim
        self.use_value_head = use_value_head
        self.use_residual = use_residual
        self.use_feature_interaction = use_feature_interaction
        
        # Select activation function
        if activation == 'gelu':
            self.activation = nn.GELU()
        elif activation == 'silu':
            self.activation = nn.SiLU()
        else:
            self.activation = nn.ReLU()
        
        # Feature encoders for different feature types
        self.feature_encoders = nn.ModuleDict({
            ft: self._create_feature_encoder(dim, hidden_dim, dropout, use_layer_norm, use_batch_norm)
            for ft, dim in feature_dims.items()
        })
        
        # Positional encoding for transformer
        self.pos_encoder = PositionalEncoding(hidden_dim, dropout)
        
        # Transformer encoder for feature fusion
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=hidden_dim,
            nhead=num_heads,
            dim_feedforward=hidden_dim * 4,
            dropout=dropout,
            batch_first=True
        )
        self.transformer_encoder = nn.TransformerEncoder(
            encoder_layer=encoder_layer,
            num_layers=num_layers
        )
        
        # Feature interaction layers
        if use_feature_interaction:
            self.interaction_layers = nn.ModuleList([
                FeatureInteractionLayer(hidden_dim, dropout, use_layer_norm)
                for _ in range(interaction_layers)
            ])
        
        # Policy network (for growth rate prediction)
        self.policy_net = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            self.activation,
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, hidden_dim // 2),
            self.activation,
            nn.Linear(hidden_dim // 2, 1)
        )
        
        # Secondary policy head for ensemble prediction
        self.policy_net2 = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            self.activation,
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, 1)
        )
        
        # Value network (for uncertainty estimation)
        if use_value_head:
            self.value_net = nn.Sequential(
                nn.Linear(hidden_dim, hidden_dim),
                self.activation,
                nn.Dropout(dropout),
                nn.Linear(hidden_dim, hidden_dim // 2),
                self.activation,
                nn.Linear(hidden_dim // 2, 1)
            )
        
        # Feature importance tracking
        self.feature_importance = {ft: 0.0 for ft in feature_dims.keys()}
        
        # Initialize weights
        self._init_weights()
        
        logger.info(f"Initialized EnhancedRLVRAgent with {len(feature_dims)} feature types, "
                   f"{hidden_dim} hidden dimensions, and {num_heads} attention heads")
    
    def _create_feature_encoder(self, input_dim, hidden_dim, dropout, use_layer_norm, use_batch_norm):
        """Create a feature encoder network with optional residual connections."""
        layers = []
        
        # First layer
        layers.append(nn.Linear(input_dim, hidden_dim))
        layers.append(self.activation)
        layers.append(nn.Dropout(dropout))
        
        if use_batch_norm:
            layers.append(nn.BatchNorm1d(hidden_dim))
        elif use_layer_norm:
            layers.append(nn.LayerNorm(hidden_dim))
        
        # Second layer with residual connection
        if self.use_residual and input_dim == hidden_dim:
            # If input and output dimensions match, use identity residual
            layers.append(ResidualBlock(
                nn.Sequential(
                    nn.Linear(hidden_dim, hidden_dim),
                    self.activation,
                    nn.Dropout(dropout),
                    nn.Linear(hidden_dim, hidden_dim)
                ),
                use_layer_norm=use_layer_norm,
                use_batch_norm=use_batch_norm
            ))
        else:
            # Otherwise, use a regular layer
            layers.append(nn.Linear(hidden_dim, hidden_dim))
            layers.append(self.activation)
            layers.append(nn.Dropout(dropout))
            
            if use_batch_norm:
                layers.append(nn.BatchNorm1d(hidden_dim))
            elif use_layer_norm:
                layers.append(nn.LayerNorm(hidden_dim))
        
        return nn.Sequential(*layers)
    
    def _init_weights(self):
        """Initialize weights for better training using Kaiming initialization."""
        for name, param in self.named_parameters():
            if 'weight' in name:
                if 'norm' not in name and len(param.shape) >= 2:  # Skip LayerNorm weights and 1D tensors
                    if 'Linear' in name:
                        # Use Kaiming initialization for linear layers
                        nn.init.kaiming_normal_(param, nonlinearity='relu')
                    else:
                        # Use Xavier for other layers
                        nn.init.xavier_uniform_(param)
            elif 'bias' in name:
                nn.init.zeros_(param)
    
    def forward(self, features: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        Forward pass through the enhanced RLVR agent.
        
        Args:
            features: Dictionary mapping feature types to feature tensors
            
        Returns:
            Dictionary with 'growth_rate' prediction and optional 'uncertainty'
        """
        # Process each feature type
        encoded_features = []
        for ft, encoder in self.feature_encoders.items():
            if ft in features:
                # Apply feature encoder
                encoded = encoder(features[ft])
                encoded_features.append(encoded)
            else:
                logger.warning(f"Feature type '{ft}' not found in input features")
        
        if not encoded_features:
            raise ValueError("No valid features found in input")
        
        # Stack encoded features
        combined = torch.stack(encoded_features, dim=1)
        
        # Apply positional encoding
        combined = self.pos_encoder(combined)
        
        # Apply transformer encoder
        transformer_out = self.transformer_encoder(combined)
        
        # Apply feature interaction layers if enabled
        if self.use_feature_interaction and hasattr(self, 'interaction_layers'):
            for layer in self.interaction_layers:
                transformer_out = layer(transformer_out)
        
        # Global feature representation (mean across feature types)
        global_features = transformer_out.mean(dim=1)
        
        # Apply policy networks for growth rate prediction
        growth_rate1 = self.policy_net(global_features)
        growth_rate2 = self.policy_net2(global_features)
        
        # Ensemble the predictions (weighted average)
        growth_rate = 0.7 * growth_rate1 + 0.3 * growth_rate2
        
        # Prepare output
        output = {'growth_rate': growth_rate}
        
        # Add uncertainty estimation if value head is enabled
        if self.use_value_head:
            uncertainty = torch.abs(self.value_net(global_features))
            output['uncertainty'] = uncertainty
        
        return output
    
    def predict(self, features: Dict[str, torch.Tensor]) -> Dict[str, np.ndarray]:
        """
        Make predictions with the enhanced RLVR agent.
        
        Args:
            features: Dictionary mapping feature types to feature tensors
            
        Returns:
            Dictionary with 'growth_rate' prediction and optional 'uncertainty'
        """
        self.eval()
        with torch.no_grad():
            output = self.forward(features)
            
            # Convert to numpy
            result = {
                'growth_rate': output['growth_rate'].cpu().numpy().flatten()
            }
            
            if 'uncertainty' in output:
                result['uncertainty'] = output['uncertainty'].cpu().numpy().flatten()
            
            return result
    
    def get_feature_importance(self) -> Dict[str, float]:
        """
        Get feature importance scores.
        
        Returns:
            Dictionary mapping feature types to importance scores
        """
        return self.feature_importance
    
    def update_feature_importance(self, gradients: Dict[str, torch.Tensor]):
        """
        Update feature importance based on gradients.
        
        Args:
            gradients: Dictionary mapping feature types to gradient tensors
        """
        for ft, grad in gradients.items():
            if ft in self.feature_importance:
                # Update importance based on gradient magnitude
                importance = torch.abs(grad).mean().item()
                # Exponential moving average
                self.feature_importance[ft] = 0.9 * self.feature_importance[ft] + 0.1 * importance


class PositionalEncoding(nn.Module):
    """
    Positional encoding for transformer models.
    
    This adds positional information to the input features to help the
    transformer model understand the order of features.
    """
    
    def __init__(self, d_model: int, dropout: float = 0.1, max_len: int = 100):
        """
        Initialize the positional encoding.
        
        Args:
            d_model: Dimension of the model
            dropout: Dropout rate (default: 0.1)
            max_len: Maximum length of the input sequence (default: 100)
        """
        super().__init__()
        self.dropout = nn.Dropout(p=dropout)
        
        # Create positional encoding
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-math.log(10000.0) / d_model))
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0)
        
        # Register buffer (not a parameter, but part of the module)
        self.register_buffer('pe', pe)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Add positional encoding to the input.
        
        Args:
            x: Input tensor of shape [batch_size, seq_len, d_model]
            
        Returns:
            Tensor with positional encoding added
        """
        x = x + self.pe[:, :x.size(1), :]
        return self.dropout(x)


class ResidualBlock(nn.Module):
    """
    Residual block with optional normalization.
    
    This implements a residual connection around a sequence of layers,
    with optional layer or batch normalization.
    """
    
    def __init__(self, layers: nn.Module, use_layer_norm: bool = True, use_batch_norm: bool = False):
        """
        Initialize the residual block.
        
        Args:
            layers: The layers to wrap with a residual connection
            use_layer_norm: Whether to use layer normalization (default: True)
            use_batch_norm: Whether to use batch normalization (default: False)
        """
        super().__init__()
        self.layers = layers
        self.use_layer_norm = use_layer_norm
        self.use_batch_norm = use_batch_norm
        
        if use_layer_norm:
            self.norm = nn.LayerNorm(layers[-1].out_features)
        elif use_batch_norm:
            self.norm = nn.BatchNorm1d(layers[-1].out_features)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Forward pass through the residual block.
        
        Args:
            x: Input tensor
            
        Returns:
            Output tensor with residual connection
        """
        # Apply layers
        out = self.layers(x)
        
        # Add residual connection
        out = out + x
        
        # Apply normalization if enabled
        if self.use_layer_norm or self.use_batch_norm:
            out = self.norm(out)
        
        return out


class FeatureInteractionLayer(nn.Module):
    """
    Feature interaction layer for cross-feature learning.
    
    This layer implements a mechanism for features to interact with each other,
    allowing the model to learn complex relationships between different feature types.
    """
    
    def __init__(self, hidden_dim: int, dropout: float = 0.1, use_layer_norm: bool = True):
        """
        Initialize the feature interaction layer.
        
        Args:
            hidden_dim: Dimension of hidden layers
            dropout: Dropout rate (default: 0.1)
            use_layer_norm: Whether to use layer normalization (default: True)
        """
        super().__init__()
        
        # Self-attention for feature interaction
        self.self_attn = nn.MultiheadAttention(hidden_dim, num_heads=4, dropout=dropout, batch_first=True)
        
        # Feed-forward network
        self.feed_forward = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim * 2),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 2, hidden_dim)
        )
        
        # Normalization layers
        if use_layer_norm:
            self.norm1 = nn.LayerNorm(hidden_dim)
            self.norm2 = nn.LayerNorm(hidden_dim)
        else:
            self.norm1 = nn.Identity()
            self.norm2 = nn.Identity()
        
        # Dropout
        self.dropout = nn.Dropout(dropout)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Forward pass through the feature interaction layer.
        
        Args:
            x: Input tensor of shape [batch_size, seq_len, hidden_dim]
            
        Returns:
            Output tensor with feature interactions
        """
        # Self-attention with residual connection and normalization
        attn_output, _ = self.self_attn(x, x, x)
        x = x + self.dropout(attn_output)
        x = self.norm1(x)
        
        # Feed-forward with residual connection and normalization
        ff_output = self.feed_forward(x)
        x = x + self.dropout(ff_output)
        x = self.norm2(x)
        
        return x
"""

# Check if the required modules are imported
required_imports = """
import math
"""

# Path to the RLVR agent file
rlvr_agent_path = Path("deepmu/models/rlvr_agent.py")

# Backup the original file
backup_path = rlvr_agent_path.with_suffix(".py.bak")
shutil.copy2(rlvr_agent_path, backup_path)
print(f"Backed up original file to {backup_path}")

# Read the original file
with open(rlvr_agent_path, "r") as f:
    original_code = f.read()

# Check if the required imports are already present
if "import math" not in original_code:
    # Add the required imports after the existing imports
    import_end = original_code.find("# Setup logging")
    if import_end == -1:
        import_end = original_code.find("logger = logging.getLogger")
    
    enhanced_code = original_code[:import_end] + required_imports + original_code[import_end:]
else:
    enhanced_code = original_code

# Add the enhanced agent code before the RLVRTrainer class
trainer_start = enhanced_code.find("class RLVRTrainer:")
if trainer_start != -1:
    enhanced_code = enhanced_code[:trainer_start] + enhanced_agent_code + enhanced_code[trainer_start:]
else:
    # If RLVRTrainer is not found, add at the end
    enhanced_code += enhanced_agent_code

# Write the enhanced code back to the file
with open(rlvr_agent_path, "w") as f:
    f.write(enhanced_code)

print(f"Enhanced RLVR agent code written to {rlvr_agent_path}")
print("You can now use the EnhancedRLVRAgent class in your training scripts.")
