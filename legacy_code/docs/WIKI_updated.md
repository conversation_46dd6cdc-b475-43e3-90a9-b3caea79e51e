# DeepMu Technical Documentation

## Table of Contents
1. [Introduction](#introduction)
2. [Installation](#installation)
3. [Usage](#usage)
4. [Core Components](#core-components)
5. [Algorithm Details](#algorithm-details)
6. [Data Processing Workflow](#data-processing-workflow)
7. [Model Architecture](#model-architecture)
8. [Training Process](#training-process)
9. [Prediction Pipeline](#prediction-pipeline)
10. [Package Structure](#package-structure)
11. [Error Handling Architecture](#error-handling-architecture)
12. [API Reference](#api-reference)
13. [Command-Line Interface](#command-line-interface)
14. [Examples](#examples)
15. [Troubleshooting](#troubleshooting)
16. [Advanced Codon Features](#advanced-codon-features)
17. [Pathway-Aware Features](#pathway-aware-features)
18. [Genome Preprocessing](#genome-preprocessing)
19. [Hierarchical Taxonomy](#hierarchical-taxonomy)
20. [KO Similarity Analysis](#ko-similarity-analysis)
21. [Performance Optimization](#performance-optimization)
22. [Extending DeepMu](#extending-deepmu)
23. [Hybrid RF-Transformer Architecture](#hybrid-rf-transformer-architecture)
24. [Advanced Regularization Techniques](#advanced-regularization-techniques)
25. [Multi-Branch Neural Networks](#multi-branch-neural-networks)
26. [Random Forest Models](#random-forest-models)

## Introduction

DeepMu is an advanced tool for predicting microbial growth rates and optimal growth temperatures using a multi-branch neural network approach. Version 1.1.1 introduces significant enhancements, including:

1. **Hybrid RF-Transformer Architecture** - Combining Random Forest for growth rate prediction with Transformer models for temperature prediction
2. **Multi-Branch Neural Networks** - Specialized feature processing pathways for different feature types
3. **Advanced Regularization Techniques** - Implementation of L1/L2 regularization, dropout strategies, and early stopping
4. **Improved Feature Extraction** - Enhanced modular feature extraction with better code organization
5. **Expanded Protein Isoelectric Point Analysis** - More granular pI distribution features
6. **Dual-Target Model Training** - Enhanced training scripts for simultaneous prediction of growth rate and optimal temperature
7. **Documentation Reorganization** - Comprehensive documentation structure with detailed guides for each component

These enhancements build on the existing foundations of hierarchical taxonomy support, KO profile similarity analysis, and integrated preprocessing capabilities from previous versions.

## Installation

### Prerequisites
- Python 3.8 or higher
- Prodigal (for gene prediction)
- KofamScan (for KO annotation)
- CUDA-compatible GPU (recommended for training)
- At least 16GB RAM (32GB recommended for large datasets)
- 100GB+ disk space for KofamScan database

### Setup
1. Clone the repository:
```bash
git clone https://github.com/username/DeepMu.git
cd DeepMu
```

2. Create and activate a virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

4. Install external tools:
- Prodigal: https://github.com/hyattpd/Prodigal
- KofamScan: https://github.com/takaram/kofam_scan

5. Download and setup KofamScan database:
```bash
# Download KofamScan database
wget https://www.genome.jp/ftp/db/kofam/profiles.tar.gz
wget https://www.genome.jp/ftp/db/kofam/ko_list.gz

# Extract and setup
tar xzf profiles.tar.gz
gunzip ko_list.gz
```

## Core Components

### 1. GenomePreprocessor
Handles gene prediction and KO annotation:
- Prodigal integration for gene prediction
- KofamScan integration for KO annotation
- Support for different genetic codes
- Batch processing capabilities
- Error handling and recovery strategies

The `GenomePreprocessor` class automates the workflow from raw genome sequences to annotated protein sequences with KO assignments, enabling direct use of genomic data with DeepMu.

### 2. TaxonomicEmbedder
Manages hierarchical taxonomy:
- Multi-level taxonomy embedding (phylum to genus)
- Adaptive embedding dimensions
- Attention-based level integration
- Taxonomy-aware similarity calculations

The `TaxonomicEmbedder` creates learned embeddings for each taxonomy level and combines them into a unified representation, enabling the model to leverage hierarchical taxonomic relationships.

### 3. KOSimilarityCalculator
Calculates functional similarity:
- KO profile comparison using weighted Jaccard similarity
- Position-aware similarity metrics
- Integration with taxonomic information
- Adaptive weighting mechanism

The `KOSimilarityCalculator` enables the model to incorporate functional similarity between organisms based on their KO profiles, enriching the phylogenetic information with functional context.

### 4. HierarchicalGNN
Graph-based phylogenetic modeling:
- Multi-level graph construction
- Adaptive gating mechanism
- Message passing across taxonomy levels
- Feature fusion across levels

The `HierarchicalGNN` implements a Graph Neural Network that processes both taxonomic relationships and functional similarities, creating rich embeddings for improved predictions.

### 5. EnhancedPhyloBranch
Specialized component for advanced phylogenetic modeling:
- Replaces the original embedding-based phylogeny approach
- Integrates taxonomy and KO information
- Implements a residual architecture for improved gradient flow
- Provides integrated feature extraction

The `EnhancedPhyloBranch` serves as the central component for hierarchical taxonomy support, orchestrating the TaxonomicEmbedder, KOSimilarityCalculator, and HierarchicalGNN.

### 6. CodonFeatureCalculator
Calculates codon usage features:
- Codon Usage Bias (CUB)
- Codon Pair Bias (CPB)
- Consistency metrics
- Remote interaction scores
- HEG/Background differentiation
- Advanced codon features (CAI, ENC, etc.)

The `CodonFeatureCalculator` provides comprehensive analysis of codon usage patterns, which are critical for accurate growth rate prediction.

### 7. HybridRFTransformer
Combines Random Forest and Transformer models:
- Random Forest for growth rate prediction
- Transformer for temperature prediction
- Feature sharing between models
- Ensemble prediction capabilities
- Confidence estimation

The `HybridRFTransformer` leverages the strengths of both Random Forest and Transformer architectures to optimize predictions for each target variable.

## Model Architecture

### Multi-Branch Neural Network

The model architecture consists of four main branches:

1. **Codon Branch**: Processes codon usage patterns
   - Input: Codon frequency vectors (64-dimensional)
   - Architecture: 3 fully connected layers with ReLU activation
   - Layer dimensions: 64 → 128 → 128 → 128
   - Output: 128-dimensional feature vector

2. **Phylogenetic Branch**: Processes taxonomic information
   - Input: Taxonomy strings (hierarchical levels from phylum to genus)
   - Components:
     - TaxonomicEmbedder: Creates embeddings for each taxonomy level
     - HierarchicalGNN: Processes taxonomic relationships as a graph
     - KO Similarity integration: Enriches the graph with functional similarity
   - Layer dimensions: Taxonomy embeddings → GNN → 128 → 64
   - Output: 64-dimensional feature vector

3. **Sequence Branch**: Processes DNA sequence patterns
   - Input: k-mer frequencies from genome sequences
   - Architecture: Parallel 1D CNN with multiple kernel sizes (3, 5, 7)
   - Global max pooling for position invariance
   - Output: 96-dimensional feature vector

4. **Pathway Branch (Optional)**: Processes metabolic pathway information
   - Input: KEGG pathway completeness scores
   - Architecture: 2 fully connected layers with ReLU activation
   - Layer dimensions: Input → 64 → 32
   - Output: 32-dimensional feature vector

### Feature Fusion

The outputs from all branches are combined through:

1. **Cross-Attention Mechanism**:
   - Computes attention weights between different feature types
   - Allows the model to focus on the most informative features
   - Enables adaptive weighting based on organism characteristics

2. **Feature Concatenation**:
   - Combines attended features from all branches
   - Preserves branch-specific information

3. **Fusion Network**:
   - Fully connected layers: 320 → 256 → 128 → 64
   - Dropout (p=0.3) for regularization
   - Separate prediction heads for growth rate and temperature

### Hybrid RF-Transformer Architecture

The hybrid architecture combines:

1. **Random Forest for Growth Rate**:
   - Optimized for regression tasks
   - Handles non-linear relationships effectively
   - Feature importance analysis
   - Robust to outliers and missing data

2. **Transformer for Temperature**:
   - Captures long-range dependencies in sequence data
   - Self-attention mechanism for feature interaction
   - Position-aware encoding
   - Effective for temperature prediction

This hybrid approach leverages the strengths of each architecture for its specific prediction task, resulting in improved overall performance.

## Advanced Codon Features

DeepMu v1.1.1 includes advanced codon feature analysis:

1. **Codon Adaptation Index (CAI)**:
   - Measures how well adapted a gene's codon usage is to the most expressed genes
   - Calculated separately for HEG and background genes
   - Normalized to account for amino acid composition bias

2. **Effective Number of Codons (ENC)**:
   - Measures the evenness of codon usage (range 20-61)
   - Lower values indicate stronger codon bias
   - Calculated with Wright's formula

3. **GC Content at Different Positions**:
   - Measures GC content at 1st, 2nd, and 3rd codon positions
   - Provides insights into selection pressure on different codon positions
   - Calculated for both HEG and background genes

4. **Codon Context Bias**:
   - Measures non-random associations between adjacent codons
   - Captures context-dependent codon preferences
   - Calculated using conditional probability matrices

5. **HEG-specific Metrics**:
   - Calculates all the above metrics specifically for highly expressed genes
   - Provides delta values (difference between HEG and background)
   - Enables more accurate growth rate prediction

These advanced codon features provide a more comprehensive analysis of codon usage patterns, which are critical for accurate growth rate prediction.

## Random Forest Models

DeepMu v1.1.1 includes several Random Forest model implementations:

1. **Basic Random Forest**:
   - Standard implementation with default hyperparameters
   - Used as a baseline for comparison

2. **Improved Random Forest**:
   - Optimized hyperparameters through grid search
   - Feature selection through importance ranking
   - Cross-validation for robust performance estimation

3. **RF with Feature Engineering**:
   - Advanced feature engineering techniques
   - Interaction features for capturing non-linear relationships
   - Polynomial features for key metrics
   - Normalization and transformation techniques

4. **Hybrid RF-Transformer**:
   - RF component for growth rate prediction
   - Transformer component for temperature prediction
   - Feature sharing between components
   - Ensemble prediction capabilities

5. **RF with Recursive Feature Elimination**:
   - Systematic feature selection through RFE
   - Cross-validated feature selection (RFECV)
   - Optimal feature subset identification
   - Improved model interpretability

These Random Forest implementations provide a range of options for different use cases, from simple baseline models to sophisticated hybrid architectures.

## API Reference

### MicrobialGrowthPredictor

Main interface for predicting microbial growth rates and optimal temperatures.

This class provides a unified interface for making predictions using the enhanced DeepMu models. It supports single organism prediction, community prediction, and batch prediction modes.

#### Constructor

```python
MicrobialGrowthPredictor(
    model_path=None,
    genetic_code=11,
    multi_task=True,
    use_pathways=False,
    use_heg_features=False,
    kegg_path=None,
    heg_ko_list=None,
    kofamscan_db_path=None,
    use_hierarchical_phylo=False,
    taxonomy_vocab_sizes=None
)
```

**Parameters**:
- `model_path` (str, optional): Path to pretrained model
- `genetic_code` (int): NCBI genetic code ID (default: 11 for bacterial)
- `multi_task` (bool): Whether to predict both growth rate and temperature
- `use_pathways` (bool): Whether to use pathway features
- `use_heg_features` (bool): Whether to use HEG/genomic background differentiation
- `kegg_path` (str): Path to KEGG mapping file
- `heg_ko_list` (str or set): List of KO IDs considered highly expressed genes
- `kofamscan_db_path` (str): Path to KofamScan database
- `use_hierarchical_phylo` (bool): Whether to use hierarchical taxonomy and KO profile
- `taxonomy_vocab_sizes` (dict): Dict of vocabulary sizes for taxonomy levels

#### Methods

##### predict_single

```python
predict_single(
    cds_fasta_path=None,
    ko_file=None,
    temperature=None,
    predict_temp=True,
    analyze_features=False,
    heg_ko_list=None,
    sequences=None,
    taxonomy_string=None,
    taxid=None
)
```

Predict growth rate and optimal temperature for a single organism.

**Parameters**:
- `cds_fasta_path` (str): Path to FASTA file containing predicted CDS sequences
- `ko_file` (str): Path to file containing KO annotations
- `temperature` (float): Growth temperature in Celsius
- `predict_temp` (bool): Whether to predict optimal temperature
- `analyze_features` (bool): Whether to include feature importance analysis
- `heg_ko_list` (set or str): Set of KO IDs for HEGs or path to list file
- `sequences` (list): List of SeqRecord objects (optional, instead of cds_fasta_path)
- `taxonomy_string` (str): Hierarchical taxonomy string (e.g., "2|1224|1236|91347|543|570")
- `taxid` (str): NCBI taxonomy ID (e.g., "511145") - alternative to taxonomy_string

**Returns**:
- `dict`: Prediction results including growth rate, temperature, and features

##### predict_community

```python
predict_community(
    fasta_path,
    coverage_path=None,
    temperature=None,
    predict_temp=True,
    ko_file=None,
    ko_map=None,
    org_to_seq_map=None,
    heg_ko_list=None,
    abundances=None,
    taxonomy_file=None,
    taxid_map=None,
    contigs_file=None,
    output_dir=None,
    analyze_features=False
)
```

Predict community growth rate.

**Parameters**:
- `fasta_path` (str): Path to FASTA file with CDS sequences
- `coverage_path` (str): Path to coverage/abundance file
- `temperature` (float): Growth temperature in Celsius
- `predict_temp` (bool): Whether to predict optimal temperature
- `ko_file` (str): Path to file containing KO terms
- `ko_map` (dict): Mapping of sequence IDs to KO terms
- `org_to_seq_map` (dict): Mapping of organism IDs to sequence IDs
- `heg_ko_list` (set): Set of KO IDs for HEGs
- `abundances` (dict): Dictionary of organism IDs to abundances
- `taxonomy_file` (str): Path to file with hierarchical taxonomy IDs
- `taxid_map` (dict or str): Mapping of contig IDs to NCBI taxids or path to file
- `contigs_file` (str): Path to assembled contigs file
- `output_dir` (str): Directory for output files
- `analyze_features` (bool): Whether to include advanced feature analysis

**Returns**:
- `dict`: Community prediction results

### CodonFeatureCalculator

Calculates codon usage features for microbial growth rate prediction.

This class calculates various codon usage metrics including:
- Codon Usage Bias (CUB): Measures bias in synonymous codon choice
- Codon Pair Bias (CPB): Analyzes preferences for adjacent codon pairs
- Consistency: Measures consistency of codon usage across genes
- Remote Interaction: Analyzes interactions between non-adjacent codons
- Advanced metrics: CAI, ENC, GC content, codon context bias

#### Constructor

```python
CodonFeatureCalculator(
    genetic_code=11,
    heg_ko_ids=None
)
```

**Parameters**:
- `genetic_code` (int): NCBI genetic code ID (default: 11 for bacterial)
- `heg_ko_ids` (set): Set of KO IDs considered highly expressed genes

#### Methods

##### calculate_features

```python
calculate_features(
    sequence
)
```

Calculate codon usage features from a DNA sequence string.

**Parameters**:
- `sequence` (str): DNA sequence string

**Returns**:
- `dict`: Dictionary of codon usage metrics

##### calculate_features_with_heg

```python
calculate_features_with_heg(
    sequences,
    ko_map
)
```

Calculate codon usage features with HEG differentiation.

**Parameters**:
- `sequences` (list): List of SeqRecord objects
- `ko_map` (dict): Mapping of sequence IDs to KO terms

**Returns**:
- `dict`: Dictionary of codon usage metrics for HEG and background genes

##### calculate_advanced_features

```python
calculate_advanced_features(
    sequence
)
```

Calculate advanced codon usage features from a DNA sequence string.

**Parameters**:
- `sequence` (str): DNA sequence string

**Returns**:
- `dict`: Dictionary of advanced codon usage metrics

## Command-Line Interface

DeepMu provides a comprehensive command-line interface with several subcommands for different tasks:

### Global Options

These options apply to most subcommands:

- `-v`, `--verbose`: Enable verbose logging for detailed output
- `--output`: Path to save the output results in JSON format
- `--output-dir`: Directory to store output files (default: ./results)

### `preprocess` Subcommand

Preprocess genome sequences with gene prediction and KO annotation.

```bash
python deepmu_cli.py preprocess INPUT [options]
```

**Arguments:**
- `INPUT`: Path to input genome FASTA file or directory containing multiple genomes

**Options:**
- `-o`, `--output-dir`: Directory to store output files
- `-k`, `--kofamscan-db`: Path to KofamScan database
- `-g`, `--genetic-code`: NCBI genetic code ID (default: 11 for bacterial)
- `-m`, `--meta-mode`: Use metagenome mode for gene prediction
- `-b`, `--batch`: Process multiple genomes in batch mode
- `-c`, `--cpu`: Number of CPU cores to use for KofamScan
- `--log-level`: Set the logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)

### `single` Subcommand

Predict growth rate and optimal temperature for a single organism.

```bash
python deepmu_cli.py single CDS_FILE KO_FILE [options]
```

**Arguments:**
- `CDS_FILE`: Path to CDS sequences in FASTA format
- `KO_FILE`: Path to KO annotations file

**Options:**
- `-t`, `--temp`: Growth temperature in Celsius (default: 37.0)
- `--no-temp-predict`: Disable optimal temperature prediction
- `-o`, `--output-dir`: Directory to store output files
- `-k`, `--kofamscan-db`: Path to KofamScan database for hierarchical taxonomy features
- `--taxid`: NCBI taxonomy ID or taxonomy string in pipe-separated format
- `--output`: Path to output JSON file
- `--heg-ko-list`: Path to file containing list of KO IDs for highly expressed genes
- `--model-path`: Path to pretrained model file
- `--advanced-features`: Use advanced codon features for analysis

### `batch` Subcommand

Process multiple genomes in batch mode.

```bash
python deepmu_cli.py batch INPUT_DIR [options]
```

**Arguments:**
- `INPUT_DIR`: Directory containing input genome files

**Options:**
- `-o`, `--output-dir`: Directory to store output files
- `-k`, `--kofamscan-db`: Path to KofamScan database
- `-m`, `--meta-mode`: Use metagenome mode for gene prediction
- `-t`, `--temp`: Growth temperature in Celsius (default: 37.0)
- `--no-temp-predict`: Disable optimal temperature prediction
- `--taxid`: NCBI taxonomy ID or taxonomy string in pipe-separated format
- `--heg-ko-list`: Path to file containing list of KO IDs for highly expressed genes
- `--model-path`: Path to pretrained model file
- `--advanced-features`: Use advanced codon features for analysis

### `community` Subcommand

Predict growth rates for a microbial community.

```bash
python deepmu_cli.py community INPUT [options]
```

**Arguments:**
- `INPUT`: Path to input metagenome FASTA file

**Options:**
- `--coverage`: Path to coverage file for abundance information
- `--contigs`: Path to contigs file
- `--taxonomy-file`: Path to taxonomy mapping file
- `-o`, `--output-dir`: Directory to store output files
- `-k`, `--kofamscan-db`: Path to KofamScan database
- `--ko-file`: Path to pre-computed KO annotations
- `--ko-lookup`: Path to KO lookup file
- `--org-to-seq`: Path to organism to sequence mapping file
- `--heg-ko-list`: Path to HEG KO list file
- `--temp`: Temperature for growth rate prediction
- `--no-temp-predict`: Disable temperature prediction
- `--model-path`: Path to pretrained model file
- `--output`: Path to output JSON file
- `--advanced-features`: Use advanced codon features for analysis

### `feature` Subcommand

Extract and analyze features from genome sequences.

```bash
python deepmu_cli.py feature [options]
```

**Options:**
Refer to the feature extraction documentation for detailed options.

## Package Structure

DeepMu is organized into several modules:

```
deepmu/
├── __init__.py
├── cli/                  # Command-line interface
│   ├── __init__.py
│   └── feature.py        # Feature extraction CLI
├── data/                 # Data handling
│   ├── __init__.py
│   ├── dataset.py        # Dataset classes
│   └── tsv_dataset.py    # TSV dataset handling
├── features/             # Feature extraction
│   ├── __init__.py
│   ├── amino_acid_features.py
│   ├── codon_features.py
│   ├── feature_extraction.py
│   ├── genomic_features.py
│   ├── heg_analysis.py
│   ├── integrated_features.py
│   ├── ko_cooccurrence.py
│   ├── ko_similarity.py
│   ├── pathway_clustering.py
│   ├── pathway_features.py
│   ├── protein_pi_features.py
│   ├── rna_features.py
│   ├── sequence_features.py
│   └── taxonomy_features.py
├── models/               # Model architectures
│   ├── __init__.py
│   ├── advanced_regularization.py
│   ├── attention.py
│   ├── combined_dnn_transformer.py
│   ├── config.py
│   ├── dual_target_model.py
│   ├── enhanced_networks.py
│   ├── ensemble_model.py
│   ├── feature_selected_network.py
│   ├── hybrid_model.py
│   ├── hybrid_rf_transformer.py
│   ├── improved_hybrid_model.py
│   ├── improved_rf.py
│   ├── multi_branch_network.py
│   ├── networks.py
│   ├── phylo.py
│   ├── simple_networks.py
│   ├── stacking_rf_transformer.py
│   └── transformer_network.py
├── predictors/           # Prediction interfaces
│   ├── __init__.py
│   └── predictor.py
├── taxonomy/             # Taxonomy handling
│   ├── __init__.py
│   └── taxonomy_utils.py
└── utils/                # Utility functions
    ├── __init__.py
    ├── correlation_feature_selection.py
    ├── custom_loss.py
    ├── exceptions.py
    ├── feature_selection.py
    ├── gene_annotation.py
    ├── logging.py
    ├── metrics.py
    ├── preprocess.py
    ├── rrna_annotation.py
    ├── sequence.py
    ├── transforms.py
    ├── trna_annotation.py
    └── visualization.py
```

## Extending DeepMu

DeepMu is designed to be extensible. Here are some common extension points:

### Adding New Features

To add a new feature type:

1. Create a new feature calculator in `deepmu/features/`
2. Implement the feature calculation logic
3. Update the feature extraction pipeline in `deepmu/features/feature_extraction.py`
4. Add CLI support in `deepmu/cli/feature.py`

### Adding New Models

To add a new model architecture:

1. Create a new model class in `deepmu/models/`
2. Implement the forward pass and any specialized layers
3. Update the model factory in `deepmu/models/__init__.py`
4. Add training script support

### Adding New Prediction Methods

To add a new prediction method:

1. Extend the `MicrobialGrowthPredictor` class in `deepmu/predictors/predictor.py`
2. Implement the new prediction logic
3. Update the CLI in `deepmu_cli.py`

## Troubleshooting

### Common Issues

1. **Missing KofamScan Database**:
   - Error: "KofamScan database not found"
   - Solution: Download and set up the KofamScan database as described in the installation section

2. **Memory Errors During Training**:
   - Error: "CUDA out of memory" or "MemoryError"
   - Solution: Reduce batch size, use gradient accumulation, or switch to CPU training

3. **Feature Extraction Errors**:
   - Error: "Failed to calculate features"
   - Solution: Check input sequences for validity, ensure proper genetic code is specified

4. **Taxonomy Lookup Failures**:
   - Error: "Taxonomy ID not found"
   - Solution: Ensure NCBI taxonomy database is properly set up, check taxid validity

5. **Model Loading Errors**:
   - Error: "Failed to load model"
   - Solution: Ensure model file exists and is compatible with the current version

### Getting Help

If you encounter issues not covered in this documentation, please:

1. Check the GitHub issues for similar problems
2. Create a new issue with detailed information about the problem
3. Include error messages, system information, and steps to reproduce

## Performance Optimization

DeepMu includes several performance optimizations:

1. **Mixed Precision Training**:
   - Enables faster training with reduced memory usage
   - Activated with the `--amp` flag in training scripts

2. **Batch Processing**:
   - Processes multiple genomes in parallel
   - Reduces overhead for large datasets

3. **Multi-threading**:
   - Utilizes multiple CPU cores for feature extraction
   - Controlled with the `--num-processes` parameter

4. **GPU Acceleration**:
   - Automatically uses GPU when available
   - Falls back to CPU when GPU is not available

5. **Memory Optimization**:
   - Gradient accumulation for large models
   - Efficient feature caching
   - Lazy loading of large datasets

These optimizations enable DeepMu to handle large datasets efficiently, even on systems with limited resources.
