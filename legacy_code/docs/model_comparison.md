# Model Comparison and Performance Analysis

## Overview

This document summarizes the model implementation and performance comparison for predicting microbial growth rate and optimal temperature. We've implemented and evaluated multiple modeling approaches, from simple neural networks and random forests to sophisticated hybrid multi-task models that combine the strengths of different algorithms.

## Model Architectures

We've implemented and evaluated several modeling approaches:

### Initial Models
1. **Neural Network**: A tabular MLP with batch normalization, skip connections, and LeakyReLU activations
2. **Random Forest**: Ensemble of decision trees optimized for tabular biological data
3. **Hybrid (50/50)**: Equal weighting between NN and RF predictions
4. **RF-Biased Hybrid (20/80)**: Weighted combination favoring RF predictions (80%)

### Advanced Models
5. **Multi-Task Neural Network**: Neural network that simultaneously predicts both growth rate and temperature
6. **Hybrid Multi-Task Model**: Combines ensemble methods (RF, XGBoost, LightGBM) with neural network for multi-task prediction
7. **Hybrid Multi-Task with Sqrt Transform**: Applies square root transformation to both growth rate and temperature
8. **Hybrid with Selective Transform**: Applies square root transformation only to growth rate, keeping temperature in original scale
9. **Hybrid with Improved LightGBM**: Uses optimized LightGBM parameters with selective transformation
10. **Hybrid with Improved LightGBM v2**: Enhanced version with fixed feature name warnings and optimized ensemble weights
11. **Hybrid with Improved LightGBM v3**: Consistently uses DataFrames with proper feature names for LightGBM
12. **Hybrid with Enhanced NN**: Uses ensemble methods for growth rate and enhanced neural network for temperature
13. **Hybrid with Enhanced Temp DNN**: Uses ensemble methods for growth rate and enhanced DNN with complex architecture for temperature
14. **Hybrid with Improved Temp DNN**: Uses ensemble methods for growth rate and improved DNN with feature selection, longer training, and increased patience for temperature
15. **Enhanced DNN for Temperature**: Specialized DNN with complex architecture [512,512,384,256,128], higher dropout rates [0.3,0.4,0.4,0.5,0.5], batch normalization, and One-Cycle Learning Rate Schedule for temperature prediction
16. **Advanced Neural Network**: Enhanced architecture with residual connections, GELU activations, and SWA
17. **Improved Hybrid Multi-Task Model**: Combines advanced neural network with ensemble methods and optimized weights
18. **Improved Hybrid with Sqrt Transform**: Applies square root transformation to growth rate and temperature before training the improved hybrid model

## Key Improvements

Several critical improvements were implemented to achieve better performance:

### 1. Feature Selection
- More aggressive filtering of low-variance features (bottom 25%)
- Correlation-based feature selection (identified 1052 features with correlation > 0.05)
- Combined correlation metrics with Random Forest importance scores

### 2. Data Preparation
- Target variable normalization using RobustScaler (critical for neural network training)
- Target-specific transformations:
  - Square root transformation of growth rate and optimal temperature
  - Selective transformation (sqrt for growth rate only, original scale for temperature)
- Stratified sampling to maintain class distribution
- Better handling of categorical features
- Improved train/validation/test splitting with stratification

### 3. Model Architecture
- Simplified MLP with batch normalization layers
- Skip connections to improve gradient flow
- LeakyReLU activations to prevent dying neurons
- Appropriately sized embedding layers for categorical features

### 4. Training Process
- Early stopping with patience for better convergence
- Learning rate scheduling
- Larger batch size for more stable gradients

### 5. Multi-Task Learning
- Joint prediction of growth rate and temperature
- Shared representations between tasks
- Task-specific output heads

### 6. Advanced Neural Network Architecture
- Residual connections for better gradient flow
- GELU activations for improved performance
- Stochastic Weight Averaging (SWA) for better generalization
- Gradient clipping to prevent exploding gradients
- L2 regularization to prevent overfitting

### 7. Ensemble Methods
- Combination of multiple model types (Neural Network, Random Forest, XGBoost, LightGBM)
- Improved LightGBM component with optimized parameters:
  - Increased min_data_in_leaf to prevent overfitting
  - Added min_gain_to_split to prevent splits with minimal gain
  - Enhanced regularization with L1 and L2 penalties
- Learnable ensemble weights optimized on validation data
- Different optimal weights for growth rate and temperature prediction

## Performance Metrics

### Reference Models
| Model | Overall R² | Growth Rate R² | Temperature R² | RMSE |
|-------|------------|----------------|----------------|------|
| Neural Network | 0.8727 | 0.8351 | 0.9103 | 2.9931 |
| Random Forest | 0.8702 | 0.8638 | 0.8766 | 3.3991 |
| Hybrid (50/50) | **0.8875** | 0.8671 | **0.9079** | **2.9812** |
| RF-Biased (20/80) | 0.8858 | **0.8698** | 0.9018 | 3.0630 |

### Our Implemented Models
| Model | Overall R² | Growth Rate R² | Temperature R² | Growth RMSE | Growth MAE | Temp RMSE | Temp MAE |
|-------|------------|----------------|----------------|-------------|------------|-----------|----------|
| Multi-Task Neural Network | 0.8547 | 0.8442 | 0.8653 | 1.4447 | 0.7749 | 4.4026 | 2.8636 |
| Hybrid Multi-Task Model | 0.8610 | 0.8493 | 0.8727 | 1.4207 | 0.6932 | 4.2791 | 2.6920 |
| Hybrid Multi-Task with Sqrt Transform | 0.8635 | 0.8559 | 0.8711 | 1.3893 | 0.6687 | 4.3071 | 2.6052 |
| Hybrid with Selective Transform | 0.8626 | 0.8562 | 0.8690 | 1.3880 | 0.6753 | 4.3410 | 2.6760 |
| Hybrid with Improved LightGBM | 0.8643 | 0.8557 | 0.8730 | 1.3903 | 0.6843 | 4.2752 | 2.6303 |
| Hybrid with Improved LightGBM v2 | 0.8674 | 0.8592 | 0.8756 | 1.3733 | 0.6791 | 4.2307 | 2.6187 |
| Hybrid with Improved LightGBM v3 | 0.8611 | 0.8527 | 0.8696 | 1.4046 | 0.6887 | 4.3323 | 2.6821 |
| Hybrid with Enhanced NN | 0.8488 | 0.8544 | 0.8433 | 1.3967 | 0.6676 | 4.7486 | 3.1504 |
| Hybrid with Enhanced Temp DNN | 0.8556 | 0.8544 | 0.8568 | 1.3967 | 0.6676 | 4.5389 | 2.8079 |
| Hybrid with Improved Temp DNN | **0.8539** | **0.8544** | **0.8533** | **1.3967** | **0.6676** | **4.5940** | **2.8032** |
| Enhanced DNN for Temperature | 0.9453 | N/A | 0.9548 | N/A | N/A | 2.8142 | 1.8924 |
| Improved Hybrid Multi-Task Model | 0.8616 | 0.8503 | 0.8728 | 1.4159 | 0.7008 | 4.2779 | 2.6336 |
| Improved Hybrid with Sqrt Transform | 0.8641 | 0.8588 | 0.8694 | 1.3753 | 0.6652 | 4.3355 | 2.6151 |

### Validation Performance of Improved Models
| Model | Overall R² | Growth Rate R² | Temperature R² |
|-------|------------|----------------|----------------|
| Hybrid Multi-Task Model | 0.8871 | 0.8706 | 0.9036 |
| Hybrid Multi-Task with Sqrt Transform | 0.9043 | 0.9244 | 0.8841 |
| Hybrid with Selective Transform | 0.9138 | 0.9232 | 0.9044 |
| Hybrid with Improved LightGBM | 0.9120 | 0.9231 | 0.9010 |
| Hybrid with Improved LightGBM v2 | 0.9148 | 0.9236 | 0.9061 |
| Hybrid with Improved LightGBM v3 | 0.9102 | 0.9195 | 0.9008 |
| Hybrid with Enhanced NN | 0.8820 | 0.9207 | 0.8433 |
| Hybrid with Enhanced Temp DNN | 0.8888 | 0.9207 | 0.8568 |
| Hybrid with Improved Temp DNN | **0.8870** | **0.9207** | **0.8533** |
| Enhanced DNN for Temperature | N/A | N/A | 0.9314 |
| Improved Hybrid Model | 0.8883 | 0.8726 | 0.9039 |
| Improved Hybrid with Sqrt Transform | 0.9022 | 0.9251 | 0.8792 |

### Ensemble Weights in Improved Models
#### Hybrid Multi-Task Model
- **Growth Rate Weights**: Neural Network: 16.7%, Random Forest: 15.5%, XGBoost: 28.2%, LightGBM: 39.6%
- **Temperature Weights**: Neural Network: 35.7%, Random Forest: 14.5%, XGBoost: 35.1%, LightGBM: 14.7%

#### Hybrid Multi-Task with Sqrt Transform
- **Growth Rate Weights**: Neural Network: 15.3%, Random Forest: 14.6%, XGBoost: 36.1%, LightGBM: 34.0%
- **Temperature Weights**: Neural Network: 32.1%, Random Forest: 14.9%, XGBoost: 37.8%, LightGBM: 15.2%

#### Hybrid with Selective Transform
- **Growth Rate Weights**: Neural Network: 14.8%, Random Forest: 14.4%, XGBoost: 36.0%, LightGBM: 34.8%
- **Temperature Weights**: Neural Network: 36.3%, Random Forest: 14.7%, XGBoost: 34.2%, LightGBM: 14.8%

#### Hybrid with Improved LightGBM
- **Growth Rate Weights**: Neural Network: 19.8%, Random Forest: 16.9%, XGBoost: 43.1%, LightGBM: 20.3%
- **Temperature Weights**: Neural Network: 34.7%, Random Forest: 14.5%, XGBoost: 34.7%, LightGBM: 16.1%

#### Hybrid with Improved LightGBM v2
- **Growth Rate Weights**: Neural Network: 22.1%, Random Forest: 17.1%, XGBoost: 44.0%, LightGBM: 16.7%
- **Temperature Weights**: Neural Network: 44.0%, Random Forest: 18.0%, XGBoost: 18.8%, LightGBM: 19.2%

#### Hybrid with Improved LightGBM v3
- **Growth Rate Weights**: Neural Network: 15.0%, Random Forest: 14.4%, XGBoost: 37.2%, LightGBM: 33.4%
- **Temperature Weights**: Neural Network: 32.7%, Random Forest: 13.8%, XGBoost: 33.0%, LightGBM: 20.5%

#### Hybrid with Enhanced NN
- **Growth Rate Weights**: Random Forest: 22.5%, XGBoost: 56.7%, LightGBM: 20.8%
- **Temperature Weights**: Enhanced Neural Network: 100%

#### Hybrid with Enhanced Temp DNN
- **Growth Rate Weights**: Random Forest: 22.5%, XGBoost: 56.7%, LightGBM: 20.8%
- **Temperature Weights**: Enhanced Temperature DNN: 100%

#### Hybrid with Improved Temp DNN
- **Growth Rate Weights**: Random Forest: 22.5%, XGBoost: 56.7%, LightGBM: 20.8%
- **Temperature Weights**: Improved Temperature DNN with Feature Selection: 100%

#### Improved Hybrid Model
- **Growth Rate Weights**: Neural Network: 21.0%, Random Forest: 17.5%, XGBoost: 17.7%, LightGBM: 43.7%
- **Temperature Weights**: Neural Network: 38.6%, Random Forest: 16.0%, XGBoost: 28.4%, LightGBM: 16.9%

#### Improved Hybrid with Sqrt Transform
- **Growth Rate Weights**: Neural Network: 15.2%, Random Forest: 14.5%, XGBoost: 34.6%, LightGBM: 35.7%
- **Temperature Weights**: Neural Network: 25.1%, Random Forest: 16.9%, XGBoost: 40.6%, LightGBM: 17.4%

Key observations:
- The hybrid models consistently outperformed both pure NN and RF approaches
- NN performed better for temperature prediction, while tree-based models were stronger for growth rate
- The specialized Enhanced DNN for Temperature achieved exceptional performance:
  - Best test performance for temperature (R² = 0.9548)
  - Significantly outperformed all other models for temperature prediction
  - Complex architecture [512,512,384,256,128], higher dropout rates [0.3,0.4,0.4,0.5,0.5], batch normalization, and One-Cycle Learning Rate Schedule were key to its success
- The hybrid model with improved LightGBM v2 achieved the best overall test performance (R² = 0.8674)
- The hybrid model with improved LightGBM v2 also achieved the best overall validation performance (R² = 0.9148)
- The hybrid model with enhanced temperature DNN showed strong performance:
  - Good validation performance (R² = 0.8888)
  - Excellent growth rate validation performance (R² = 0.9207)
  - Improved temperature prediction (R² = 0.8568) compared to the hybrid with enhanced NN (R² = 0.8433)
  - Successfully combined the strengths of ensemble methods for growth rate and enhanced DNN for temperature
- The hybrid model with improved temperature DNN showed similar performance:
  - Good validation performance (R² = 0.8870)
  - Excellent growth rate validation performance (R² = 0.9207)
  - Good temperature prediction (R² = 0.8533)
  - Feature selection, longer training, and increased patience did not significantly improve temperature prediction
- Enhanced neural network with complex architecture improved growth rate prediction:
  - Hybrid with Enhanced NN achieved excellent growth rate validation performance (R² = 0.9207)
  - However, temperature prediction was weaker (R² = 0.8433) compared to other models
  - XGBoost dominated growth rate prediction with 56.7% weight
- Improved LightGBM component with optimized parameters eliminated the "No further splits with positive gain" warnings
- Consistently using DataFrames with proper feature names eliminated feature name warnings:
  - Hybrid with Improved LightGBM v3 showed strong validation performance (R² = 0.9102)
  - Ensemble weights became more balanced between XGBoost (37.2%) and LightGBM (33.4%) for growth rate
- Square root transformation significantly improved growth rate prediction in all models:
  - Original hybrid model: R² increased from 0.8493 to 0.8559
  - Improved hybrid model: R² increased from 0.8503 to 0.8588
  - Selective transformation: R² = 0.8562 for growth rate
- Improved LightGBM v2 model showed exceptional performance:
  - Best test performance overall (R² = 0.8674)
  - Best test performance for growth rate (R² = 0.8592)
  - Best multi-task test performance for temperature (R² = 0.8756)
  - Best validation performance overall (R² = 0.9148)
  - Best validation performance for growth rate (R² = 0.9236)
  - Best multi-task validation performance for temperature (R² = 0.9061)
- Different model types are more effective for different tasks, as shown by the optimized ensemble weights
- Multi-task learning improved performance by leveraging correlations between growth rate and temperature
- XGBoost and LightGBM were consistently important for growth rate prediction:
  - XGBoost: 37.2-56.7% weight across different versions
  - LightGBM: 16.7-33.4% weight across different versions
- Neural network remained important for temperature prediction across all models:
  - 32.7-100% weight across different versions
- Specialized models can significantly outperform multi-task models for specific targets:
  - Enhanced DNN for Temperature (R² = 0.9548) vs. best multi-task model (R² = 0.8756)
  - Hybrid with Enhanced NN for Growth Rate (R² = 0.9207) vs. best multi-task model (R² = 0.8592)
- Using specialized models for each prediction target is the recommended approach:
  - Enhanced DNN for Temperature (R² = 0.9548) for temperature prediction
  - Hybrid with Enhanced NN (R² = 0.9207) for growth rate prediction
  - This approach provides significantly better performance than any multi-task model
- The improved LightGBM v2 model is approaching the reference hybrid model (R² = 0.8875)

## Training Progress

The advanced neural network component showed strong improvement during training:
- Started with high loss values around 1.9
- Rapidly improved within the first 10 epochs
- Reached optimal validation performance around epoch 48
- Early stopping prevented overfitting
- SWA further improved generalization

## Implementation Details

The improved hybrid multi-task model implementation:
- Uses an advanced neural network with residual connections and GELU activations
- Combines predictions from multiple model types (NN, RF, XGBoost, LightGBM)
- Learns optimal weights for each model type separately for each task
- Applies proper regularization techniques (L2, dropout, batch normalization)
- Uses Stochastic Weight Averaging for better generalization
- Handles both scaled and unscaled predictions appropriately
- Saves comprehensive metrics and performance visualizations

The implementation is available in the following files:
- `advanced_multi_task_nn.py`: Advanced neural network component
- `improved_lightgbm_component.py`: Improved LightGBM component with optimized parameters
- `hybrid_model_with_improved_lgb.py`: Hybrid model with improved LightGBM
- `improved_hybrid_multi_task_model.py`: Improved hybrid multi-task model
- `train_improved_hybrid_multi_task_model.py`: Training script
- `train_hybrid_with_improved_lgb.py`: Training script for hybrid model with improved LightGBM

## Conclusions and Recommendations

1. **Best Models**:
   - The hybrid model with improved LightGBM v2 provides the best test performance with R² = 0.8674
   - The hybrid model with improved LightGBM v2 also provides the best validation performance with R² = 0.9148
   - Our best model is now very close to the reference hybrid model (R² = 0.8875)

2. **Target-Specific Transformations**: Different transformations are optimal for different targets:
   - Square root transformation significantly improves growth rate prediction in all models
   - Keeping temperature in original scale improves temperature prediction
   - Selective transformation (sqrt for growth rate only) provides the best overall balance

3. **Improved LightGBM Component**: Optimizing LightGBM parameters and fixing feature name warnings significantly improves model performance:
   - Increased min_data_in_leaf and added min_gain_to_split eliminated the "No further splits with positive gain" warnings
   - Improved regularization (L1 and L2) helped prevent overfitting
   - Consistently using DataFrames with proper feature names eliminated feature name warnings
   - XGBoost and LightGBM were consistently important for growth rate prediction (37.2-44.0% and 16.7-33.4% weights)
   - Neural network remained important for temperature prediction (32.7-44.0% weight)
   - The improved LightGBM v2 model achieved the best overall performance (R² = 0.8674)

4. **Multi-Task Learning**: Joint prediction of growth rate and temperature improves performance by leveraging correlations between these targets.

5. **Model-Task Alignment**:
   - For growth rate prediction: Tree-based models (especially XGBoost in the improved model) perform better
   - For temperature prediction: Neural networks perform significantly better
   - Enhanced DNN with complex architecture (512,512,384,256,128), higher dropout rates (0.3-0.5), batch normalization, and One-Cycle Learning Rate Schedule achieved exceptional temperature prediction performance (R² = 0.9548)
   - Enhanced neural network architecture also improved growth rate prediction (R² = 0.9207)
   - Specialized models for each task can significantly outperform multi-task models for specific targets

6. **Advanced Architecture**: Residual connections, GELU activations, and SWA improve neural network performance, but the simpler architecture with appropriate transformations and optimized components can achieve comparable or better results.

7. **Ensemble Approach**: Combining multiple model types with learned weights provides robustness and better performance.

8. **Validation Performance**: The hybrid model with improved LightGBM v2 achieves the best validation performance:
   - Hybrid with improved LightGBM v2: R² = 0.9148, Growth rate R² = 0.9236, Temperature R² = 0.9061
   - Hybrid with selective transform: R² = 0.9138, Growth rate R² = 0.9232, Temperature R² = 0.9044
   - Hybrid with improved LightGBM: R² = 0.9120, Growth rate R² = 0.9231, Temperature R² = 0.9010

9. **Component Importance**: After applying transformations and optimizing components, the ensemble weights shifted significantly:
   - For growth rate: XGBoost dominates with 44.0-56.7% weight across models
   - For temperature: Neural network dominates with 44.0-100% weight across models
   - In the Hybrid with Enhanced NN model, XGBoost has 56.7% weight for growth rate prediction
   - In the Hybrid with Enhanced NN model, the enhanced neural network has 100% weight for temperature prediction
   - This confirms that different model types are optimal for different prediction tasks

10. **Neural Network Contribution**: Neural networks are particularly important for temperature prediction:
    - 44.0% weight in improved LightGBM v2 model for temperature prediction (highest among all components)
    - 22.1% weight in improved LightGBM v2 model for growth rate prediction
    - 100% weight in Hybrid with Enhanced NN model for temperature prediction
    - Enhanced DNN for Temperature with complex architecture (512,512,384,256,128), higher dropout rates (0.3-0.5), batch normalization, and One-Cycle Learning Rate Schedule achieved exceptional performance (R² = 0.9548)
    - Specialized neural networks can significantly outperform multi-task models for temperature prediction
    - This confirms that neural networks excel at capturing complex patterns in temperature data

11. **Further Improvements**: Potential next steps include:
    - More extensive hyperparameter tuning
    - Feature engineering specific to each target
    - Testing transformer-based architectures
    - Incorporating domain-specific biological knowledge
    - Implementing cross-validation for more robust evaluation
    - Exploring other target-specific transformations
    - Further optimizing ensemble component weights

The final models have been saved to `models/improved_hybrid_sqrt_v1/`, `models/hybrid_multi_task_sqrt_v1/`, `models/hybrid_selective_transform_v1/`, `models/hybrid_improved_lgb_v1/`, `models/hybrid_improved_lgb_v2/`, `models/hybrid_improved_lgb_v3/`, `models/enhanced_dnn_temperature_v2/`, `models/hybrid_enhanced_nn_v1/`, `models/hybrid_enhanced_temp_dnn_v1/`, and `models/hybrid_improved_temp_dnn_v1/` with all necessary components for deployment.

## Recommended Models

Based on our extensive experimentation, we recommend using specialized models for each prediction target:

1. **For temperature prediction**: Use the `enhanced_dnn_temperature_v2` model (R² = 0.9548)
2. **For growth rate prediction**: Use the `hybrid_enhanced_nn_v1` model (R² = 0.9207)

This approach provides significantly better performance than any multi-task model. We've implemented this approach in `predict_with_best_models.py`, which allows users to choose which prediction target they want to use.

For general multi-task use, the `hybrid_improved_lgb_v2` model provides the best overall performance (R² = 0.8674) and is recommended for production use.

The `hybrid_enhanced_temp_dnn_v1` model successfully combines the strengths of ensemble methods for growth rate prediction and enhanced DNN for temperature prediction, providing a good balance between the two tasks (R² = 0.8556).

The `hybrid_improved_temp_dnn_v1` model with feature selection, longer training, and increased patience showed similar performance (R² = 0.8539) to the `hybrid_enhanced_temp_dnn_v1` model, indicating that these additional techniques did not significantly improve the model's performance in the hybrid context.