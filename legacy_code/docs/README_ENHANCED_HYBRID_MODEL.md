# Enhanced Hybrid Model for Microbial Growth Rate and Optimal Temperature Prediction

This repository contains an enhanced hybrid model for predicting microbial growth rate and optimal temperature from genomic features.

## Overview

The enhanced hybrid model builds upon the improved hybrid model and addresses its limitations by using more advanced model architectures and techniques. The new model uses TabNet for growth rate prediction and a Deep Neural Network with One-Cycle Learning for temperature prediction.

## Components

The enhanced hybrid model consists of the following components:

1. **Enhanced Growth Rate Model (`enhanced_growth_model.py`)**
   - Uses TabNet, a state-of-the-art neural network architecture for tabular data
   - Implements Bayesian hyperparameter optimization for finding optimal model parameters
   - Provides comprehensive evaluation metrics and visualizations
   - Expected to achieve R² values around 0.83-0.87 based on TabNet's performance in previous experiments

2. **Enhanced Temperature Model (`enhanced_temperature_model.py`)**
   - Uses a Deep Neural Network with One-Cycle Learning
   - Implements advanced regularization techniques (batch normalization, dropout, gradient clipping)
   - Provides comprehensive evaluation metrics and visualizations
   - Expected to achieve R² values around 0.90 based on DNN's performance in previous experiments

3. **Enhanced Hybrid Model (`enhanced_hybrid_model.py`)**
   - Combines TabNet for growth rate prediction and DNN for temperature prediction
   - Uses the same feature selection approach as the improved hybrid model
   - Provides options for hyperparameter optimization
   - Provides comprehensive evaluation and visualization

## Usage

To run the enhanced hybrid model, use the following command:

```bash
./run_enhanced_hybrid_model.sh
```

This will:
1. Load the feature and metadata files
2. Perform feature selection for each target variable
3. Train the growth rate model using TabNet
4. Train the temperature model using DNN with One-Cycle Learning
5. Evaluate the models on test data
6. Save the models, metrics, and visualizations to the output directory

To run the enhanced hybrid model with hyperparameter optimization, use:

```bash
./run_enhanced_hybrid_model_optimized.sh
```

## Key Features

1. **Advanced Model Architectures**
   - TabNet for growth rate prediction
   - DNN with One-Cycle Learning for temperature prediction

2. **Hyperparameter Optimization**
   - Bayesian optimization for finding optimal model parameters

3. **Advanced Regularization**
   - Batch normalization
   - Dropout
   - Gradient clipping
   - Weight decay

4. **Comprehensive Evaluation**
   - R² score
   - RMSE
   - MAE
   - Visualizations of predictions and residuals

## Requirements

- Python 3.8+
- PyTorch
- pytorch-tabnet
- scikit-learn
- scikit-optimize
- pandas
- numpy
- matplotlib
- seaborn

## License

This project is licensed under the MIT License - see the LICENSE file for details.
