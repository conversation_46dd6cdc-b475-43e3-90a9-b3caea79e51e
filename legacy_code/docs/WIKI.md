# DeepMu Technical Documentation

## Table of Contents
1. [Introduction](#introduction)
2. [Installation](#installation)
3. [Usage](#usage)
4. [Core Components](#core-components)
5. [Algorithm Details](#algorithm-details)
6. [Data Processing Workflow](#data-processing-workflow)
7. [Model Architecture](#model-architecture)
8. [Training Process](#training-process)
9. [Prediction Pipeline](#prediction-pipeline)
10. [Package Structure](#package-structure)
11. [Error Handling Architecture](#error-handling-architecture)
12. [API Reference](#api-reference)
13. [Command-Line Interface](#command-line-interface)
14. [Examples](#examples)
15. [Troubleshooting](#troubleshooting)
16. [Advanced Codon Features](#advanced-codon-features)
17. [Pathway-Aware Features](#pathway-aware-features)
18. [Genome Preprocessing](#genome-preprocessing)
19. [Hierarchical Taxonomy](#hierarchical-taxonomy)
20. [KO Similarity Analysis](#ko-similarity-analysis)
21. [Performance Optimization](#performance-optimization)
22. [Extending DeepMu](#extending-deepmu)
23. [Hybrid RF-Transformer Architecture](#hybrid-rf-transformer-architecture)
24. [Advanced Regularization Techniques](#advanced-regularization-techniques)
25. [Multi-Branch Neural Networks](#multi-branch-neural-networks)
26. [Documentation Structure](#documentation-structure)

## Introduction

DeepMu is an advanced tool for predicting microbial growth rates and optimal growth temperatures using a multi-branch neural network approach. Version 1.1.1 introduces significant enhancements, including:

1. **Hybrid RF-Transformer Architecture** - Combining Random Forest for growth rate prediction with Transformer models for temperature prediction
2. **Multi-Branch Neural Networks** - Specialized feature processing pathways for different feature types
3. **Advanced Regularization Techniques** - Implementation of L1/L2 regularization, dropout strategies, and early stopping
4. **Improved Feature Extraction** - Enhanced modular feature extraction with better code organization
5. **Expanded Protein Isoelectric Point Analysis** - More granular pI distribution features
6. **Dual-Target Model Training** - Enhanced training scripts for simultaneous prediction of growth rate and optimal temperature
7. **Documentation Reorganization** - Comprehensive documentation structure with detailed guides for each component

These enhancements build on the existing foundations of hierarchical taxonomy support, KO profile similarity analysis, and integrated preprocessing capabilities from previous versions.

## Installation

### Prerequisites
- Python 3.8 or higher
- Prodigal (for gene prediction)
- KofamScan (for KO annotation)
- CUDA-compatible GPU (recommended for training)
- At least 16GB RAM (32GB recommended for large datasets)
- 100GB+ disk space for KofamScan database

### Setup
1. Clone the repository:
```bash
git clone https://github.com/username/DeepMu.git
cd DeepMu
```

2. Create and activate a virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

4. Install external tools:
- Prodigal: https://github.com/hyattpd/Prodigal
- KofamScan: https://github.com/takaram/kofam_scan

5. Download and setup KofamScan database:
```bash
# Download KofamScan database
wget https://www.genome.jp/ftp/db/kofam/profiles.tar.gz
wget https://www.genome.jp/ftp/db/kofam/ko_list.gz

# Extract and setup
tar xzf profiles.tar.gz
gunzip ko_list.gz
```

## Usage

DeepMu provides both a Python API and a command-line interface for making predictions. Here are examples for common use cases:

### Single Organism Prediction

#### Using the Python API

```python
from deepmu.predictors.predictor import MicrobialGrowthPredictor

# Initialize the predictor
predictor = MicrobialGrowthPredictor(
    model_path="models/trained_model.pt",  # optional, uses default if not provided
    use_hierarchical_phylo=True,  # enable hierarchical taxonomy features
    use_heg_features=True  # enable HEG/BP differentiation
)

# Make a prediction for a single organism
result = predictor.predict_single(
    cds_fasta_path="path/to/genome_cds.faa",  # protein-coding sequences
    ko_file="path/to/genome_ko.tsv",  # KO annotations
    temperature=37.0,  # optional growth temperature
    predict_temp=True,  # predict optimal temperature
    taxid="511145",  # NCBI taxonomy ID for E. coli
    analyze_features=True  # include feature analysis in results
)

# Access prediction results
growth_rate = result['growth_rate']
optimal_temperature = result['optimal_temperature']
print(f"Predicted growth rate: {growth_rate:.2f} h⁻¹")
print(f"Optimal temperature: {optimal_temperature:.1f}°C")
```

#### Using the Command-Line Interface

```bash
# Predict using preprocessed files
python deepmu_cli.py single genome_cds.faa genome_ko.tsv \
    --taxid 511145 \
    --temp 37.0 \
    --output results.json

# Process and predict directly from a genome file
python deepmu_cli.py preprocess genome.fna \
    --output-dir processed/ \
    --kofamscan-db /path/to/kofamscan_db

python deepmu_cli.py single processed/genome_cds.faa \
    processed/genome_ko.tsv \
    --taxid 511145 \
    --output results.json
```

### Community Prediction

#### Using the Python API

```python
from deepmu.predictors.predictor import MicrobialGrowthPredictor

# Initialize the predictor
predictor = MicrobialGrowthPredictor()

# Make a prediction for a microbial community
result = predictor.predict_community(
    fasta_path="path/to/metagenome.fna",
    coverage_path="path/to/coverage.tsv",  # abundance information
    temperature=37.0,
    taxonomy_file="path/to/taxonomy.tsv"  # taxid mapping
)

# Access community prediction results
community_growth_rate = result['community_growth_rate']
print(f"Community growth rate: {community_growth_rate:.2f} h⁻¹")

# Access organism-specific growth rates
if 'organism_growth_rates' in result:
    for org_id, growth_rate in result['organism_growth_rates'].items():
        abundance = result['community_composition'].get(org_id, 0.0)
        print(f"{org_id}: {growth_rate:.2f} h⁻¹ (abundance: {abundance:.2f})")
```

#### Using the Command-Line Interface

```bash
# Predict growth rates for a microbial community
python deepmu_cli.py community metagenome.fna \
    --coverage coverage.tsv \
    --taxonomy-file taxonomy.tsv \
    --temp 37.0 \
    --output community_results.json
```

### Batch Processing

```bash
# Process multiple genomes in batch mode
python deepmu_cli.py batch genome_dir/ \
    --output-dir results/ \
    --kofamscan-db /path/to/kofamscan_db \
    --heg-ko-list heg_ko_list.txt
```

## Core Components

### 1. GenomePreprocessor
Handles gene prediction and KO annotation:
- Prodigal integration for gene prediction
- KofamScan integration for KO annotation
- Support for different genetic codes
- Batch processing capabilities
- Error handling and recovery strategies

The `GenomePreprocessor` class automates the workflow from raw genome sequences to annotated protein sequences with KO assignments, enabling direct use of genomic data with DeepMu.

### 2. TaxonomicEmbedder
Manages hierarchical taxonomy:
- Multi-level taxonomy embedding (phylum to genus)
- Adaptive embedding dimensions
- Attention-based level integration
- Taxonomy-aware similarity calculations

The `TaxonomicEmbedder` creates learned embeddings for each taxonomy level and combines them into a unified representation, enabling the model to leverage hierarchical taxonomic relationships.

### 3. KOSimilarityCalculator
Calculates functional similarity:
- KO profile comparison using weighted Jaccard similarity
- Position-aware similarity metrics
- Integration with taxonomic information
- Adaptive weighting mechanism

The `KOSimilarityCalculator` enables the model to incorporate functional similarity between organisms based on their KO profiles, enriching the phylogenetic information with functional context.

### 4. HierarchicalGNN
Graph-based phylogenetic modeling:
- Multi-level graph construction
- Adaptive gating mechanism
- Message passing across taxonomy levels
- Feature fusion across levels

The `HierarchicalGNN` implements a Graph Neural Network that processes both taxonomic relationships and functional similarities, creating rich embeddings for improved predictions.

### 5. EnhancedPhyloBranch
Specialized component for advanced phylogenetic modeling:
- Replaces the original embedding-based phylogeny approach
- Integrates taxonomy and KO information
- Implements a residual architecture for improved gradient flow
- Provides integrated feature extraction

The `EnhancedPhyloBranch` serves as the central component for hierarchical taxonomy support, orchestrating the TaxonomicEmbedder, KOSimilarityCalculator, and HierarchicalGNN.

### 6. TaxonomyUtils
Utility class for taxonomy handling:
- Parsing taxonomy strings
- Formatting taxonomy dictionaries
- Validation and normalization
- Integration with NCBI taxonomy database

The `TaxonomyUtils` provides a consistent interface for taxonomy data handling, ensuring standardized formats and operations.

### 7. EnhancedPhyloGrowthModel
Core prediction model:
- Multi-branch architecture
- Position-aware attention
- Multi-task learning
- Integration of hierarchical taxonomy features

## Dual-Target Training Architecture

DeepMu v1.1.1 introduces a sophisticated dual-output architecture with:
- Separate input pathways for growth rate and optimal temperature features
- Shared hidden layers for common pattern recognition
- Task-specific output heads with customized loss weighting
- Correlation-based feature selection using Pearson/Spearman metrics

### Training Pipeline:
```bash
./run_dual_target_training.sh \
  --growth-rate-features 30 \
  --optimal-temperature-features 30 \
  --hidden-dim 128 \
  --num-layers 2 \
  --mixed-precision
```

## Enhanced Codon Adaptation

Our improved CAI calculation now includes:
1. HEG/BP prioritized codon tables
2. Position-aware codon weighting
3. Taxonomic group-specific optimization
4. Enhanced statistical reporting

Example enhanced output:
```
CAI Score: 0.87 (±0.03)
HEG Coverage: 92%
BP Enrichment: 1.8x
```

## Model Architecture

DeepMu employs a hierarchical neural network architecture with

### Multi-Branch Neural Network

The model architecture consists of four main branches:

1. **Codon Branch**: Processes codon usage patterns
   - Input: Codon frequency vectors (64-dimensional)
   - Architecture: 3 fully connected layers with ReLU activation
   - Layer dimensions: 64 → 128 → 128 → 128
   - Output: 128-dimensional feature vector

2. **Phylogenetic Branch**: Processes taxonomic information
   - Input: Taxonomy strings (hierarchical levels from phylum to genus)
   - Components:
     - TaxonomicEmbedder: Creates embeddings for each taxonomy level
     - HierarchicalGNN: Processes taxonomic relationships as a graph
     - KO Similarity integration: Enriches the graph with functional similarity
   - Layer dimensions: Taxonomy embeddings → GNN → 128 → 64
   - Output: 64-dimensional feature vector

3. **Sequence Branch**: Processes DNA sequence patterns
   - Input: k-mer frequencies from genome sequences
   - Architecture: Parallel 1D CNN with multiple kernel sizes (3, 5, 7)
   - Global max pooling for position invariance
   - Output: 96-dimensional feature vector

4. **Pathway Branch (Optional)**: Processes metabolic pathway information
   - Input: KEGG pathway completeness scores
   - Architecture: 2 fully connected layers with ReLU activation
   - Layer dimensions: Input → 64 → 32
   - Output: 32-dimensional feature vector

### Feature Fusion

The outputs from all branches are combined through:

1. **Cross-Attention Mechanism**:
   - Computes attention weights between different feature types
   - Allows the model to focus on the most informative features
   - Enables adaptive weighting based on organism characteristics

2. **Feature Concatenation**:
   - Combines attended features from all branches
   - Preserves branch-specific information

3. **Fusion Network**:
   - Fully connected layers: 320 → 256 → 128 → 64
   - Dropout (p=0.3) for regularization
   - Separate prediction heads for growth rate and temperature

### Multi-Task Learning

The model simultaneously predicts:
- Microbial growth rate (doubling time in hours)
- Optimal growth temperature (in degrees Celsius)

This multi-task approach enables the model to leverage shared features while specialized prediction heads capture task-specific patterns.

## Prediction Pipeline

The prediction pipeline in DeepMu consists of several sequential stages, orchestrated by the `MicrobialGrowthPredictor` class:

### Prediction Workflow

1. **Input Processing**:
   - Genome sequences are processed to extract protein-coding genes using Prodigal
   - Proteins are annotated with KO terms using KofamScan
   - Taxonomy information is parsed and converted to embeddings
   - KO profiles are analyzed for functional similarity

2. **Feature Extraction**:
   - **Codon Features**: Calculate codon usage bias, codon pair bias, and other metrics
   - **Phylogenetic Features**: Create embeddings from taxonomy and KO similarity
   - **Sequence Features**: Extract k-mer patterns and position-aware metrics
   - **Pathway Features**: Calculate KEGG pathway completeness (optional)

3. **Model Forward Pass**:
   - Process features through each specialized branch
   - Apply cross-attention to focus on informative features
   - Combine branch outputs and predict growth rate and temperature

4. **Post-processing**:
   - Apply correction factors based on model confidence
   - Calculate confidence intervals for predictions
   - Format and return results with optional feature analysis

The key class implementing this pipeline is `MicrobialGrowthPredictor` in `deepmu/predictors/predictor.py`.

## API Reference

### MicrobialGrowthPredictor

Main interface for predicting microbial growth rates and optimal temperatures.

This class provides a unified interface for making predictions using the enhanced DeepMu models. It supports single organism prediction, community prediction, and batch prediction modes.

#### Constructor

```python
MicrobialGrowthPredictor(
    model_path=None,
    genetic_code=11,
    multi_task=True,
    use_pathways=False,
    use_heg_features=False,
    kegg_path=None,
    heg_ko_list=None,
    kofamscan_db_path=None,
    use_hierarchical_phylo=False,
    taxonomy_vocab_sizes=None
)
```

**Parameters**:
- `model_path` (str, optional): Path to pretrained model
- `genetic_code` (int): NCBI genetic code ID (default: 11 for bacterial)
- `multi_task` (bool): Whether to predict both growth rate and temperature
- `use_pathways` (bool): Whether to use pathway features
- `use_heg_features` (bool): Whether to use HEG/genomic background differentiation
- `kegg_path` (str): Path to KEGG mapping file
- `heg_ko_list` (str or set): List of KO IDs considered highly expressed genes
- `kofamscan_db_path` (str): Path to KofamScan database
- `use_hierarchical_phylo` (bool): Whether to use hierarchical taxonomy and KO profile
- `taxonomy_vocab_sizes` (dict): Dict of vocabulary sizes for taxonomy levels

#### Methods

##### predict_single

```python
predict_single(
    cds_fasta_path=None,
    ko_file=None,
    temperature=None,
    predict_temp=True,
    analyze_features=False,
    heg_ko_list=None,
    sequences=None,
    taxonomy_string=None,
    taxid=None
)
```

Predict growth rate and optimal temperature for a single organism.

**Parameters**:
- `cds_fasta_path` (str): Path to FASTA file containing predicted CDS sequences
- `ko_file` (str): Path to file containing KO annotations
- `temperature` (float): Growth temperature in Celsius
- `predict_temp` (bool): Whether to predict optimal temperature
- `analyze_features` (bool): Whether to include feature importance analysis
- `heg_ko_list` (set or str): Set of KO IDs for HEGs or path to list file
- `sequences` (list): List of SeqRecord objects (optional, instead of cds_fasta_path)
- `taxonomy_string` (str): Hierarchical taxonomy string (e.g., "2|1224|1236|91347|543|570")
- `taxid` (str): NCBI taxonomy ID (e.g., "511145") - alternative to taxonomy_string

**Returns**:
- `dict`: Prediction results including growth rate, temperature, and features

##### predict_community

```python
predict_community(
    fasta_path,
    coverage_path=None,
    temperature=None,
    predict_temp=True,
    ko_file=None,
    ko_map=None,
    org_to_seq_map=None,
    heg_ko_list=None,
    abundances=None,
    taxonomy_file=None,
    taxid_map=None,
    contigs_file=None,
    output_dir=None,
    analyze_features=False
)
```

Predict community growth rate.

**Parameters**:
- `fasta_path` (str): Path to FASTA file with CDS sequences
- `coverage_path` (str): Path to coverage/abundance file
- `temperature` (float): Growth temperature in Celsius
- `predict_temp` (bool): Whether to predict optimal temperature
- `ko_file` (str): Path to file containing KO terms
- `ko_map` (dict): Mapping of sequence IDs to KO terms
- `org_to_seq_map` (dict): Mapping of organism IDs to sequence IDs
- `heg_ko_list` (set): Set of KO IDs for HEGs
- `abundances` (dict): Dictionary of organism IDs to abundances
- `taxonomy_file` (str): Path to file with hierarchical taxonomy IDs
- `taxid_map` (dict or str): Mapping of contig IDs to NCBI taxids or path to file
- `contigs_file` (str): Path to assembled contigs file
- `output_dir` (str): Directory for output files
- `analyze_features` (bool): Whether to include advanced feature analysis

**Returns**:
- `dict`: Community prediction results

### CodonFeatureCalculator

Calculates codon usage features for microbial growth rate prediction.

This class calculates various codon usage metrics including:
- Codon Usage Bias (CUB): Measures bias in synonymous codon choice
- Codon Pair Bias (CPB): Analyzes preferences for adjacent codon pairs
- Consistency: Measures how consistently preferred codons are used
- Remote Interaction: Novel metric for position-aware codon relationship analysis

#### Constructor

```python
CodonFeatureCalculator(
    genetic_code=11,
    context_window=10,
    heg_ko_ids=None,
    use_heg_features=False
)
```

**Parameters**:
- `genetic_code` (int): Genetic code to use (default: 11 for bacterial)
- `context_window` (int): Window size for remote codon interactions
- `heg_ko_ids` (set): Set of KO IDs that are considered highly expressed genes (HEGs)
- `use_heg_features` (bool): Whether to calculate separate metrics for HEGs and genomic background

#### Key Methods

##### calculate_features

```python
calculate_features(sequence, calculate_advanced_features=False)
```

Calculate codon usage features including remote interactions.

**Parameters**:
- `sequence` (str): DNA sequence to analyze
- `calculate_advanced_features` (bool): Whether to calculate advanced codon features

**Returns**:
- `dict`: Dictionary with CUB, CPB, Consistency, RemoteInteraction scores

##### calculate_features_with_heg

```python
calculate_features_with_heg(sequences, ko_map, calculate_advanced_features=False)
```

Calculate codon usage features with distinction between HEGs and background.

**Parameters**:
- `sequences` (list): List of sequence records or DNA strings
- `ko_map` (dict): Dictionary mapping sequence IDs to KO IDs
- `calculate_advanced_features` (bool): Whether to calculate advanced codon features

**Returns**:
- `dict`: Dictionary with standard, HEG-specific, background, and delta metrics

## Performance Optimization

Several strategies can be employed to optimize DeepMu's performance:

### Hardware Optimization

1. **GPU Acceleration**:
   - Enable GPU support for significant speedups during model inference:
   ```python
   predictor = MicrobialGrowthPredictor(device="cuda")
   ```
   - For multi-GPU systems, specify which GPU to use:
   ```python
   import os
   os.environ["CUDA_VISIBLE_DEVICES"] = "0"  # Use first GPU
   ```

2. **CPU Parallelism**:
   - Increase the number of CPU cores for preprocessing:
   ```bash
   python deepmu_cli.py preprocess genome.fna --cpu 8
   ```

### Data Processing Optimization

1. **Batch Processing**:
   - Process multiple genomes at once:
   ```bash
   python deepmu_cli.py batch genome_dir/ --output-dir results.json
   ```

2. **Preprocessing Caching**:
   - Cache preprocessed results to avoid redundant computations:
   ```python
   # Process once
   cds_file, ko_file = preprocess_genome(genome_file)

   # Reuse for multiple analyses
   predictor.predict_single(cds_file, ko_file)
   ```

### Memory Optimization

1. **Low-Memory Mode**:
   - Enable batch processing of sequences for large genomes:
   ```python
   predictor.predict_single(cds_file, ko_file, low_memory=True)
   ```

2. **Feature Selection**:
   - Use only essential features for prediction:
   ```python
   predictor = MicrobialGrowthPredictor(
       use_pathways=False,  # Disable pathway features
       use_hierarchical_phylo=False  # Use simpler phylogeny model
   )
   ```

### Inference Optimization

1. **Half-Precision Inference**:
   - Enable FP16 computation for faster inference:
   ```python
   predictor = MicrobialGrowthPredictor(half_precision=True)
   ```

2. **Model Quantization**:
   - Use quantized models for faster inference with slight accuracy loss:
   ```python
   predictor = MicrobialGrowthPredictor(model_path="models/quantized_model.pt")
   ```

## Troubleshooting

Common issues and their solutions:

### Installation Issues

1. **KofamScan installation fails**
   - **Symptom**: Error installing KofamScan
   - **Solution**: Ensure Ruby is installed (`ruby -v`), then install KofamScan with:
     ```bash
     gem install parallel
     git clone https://github.com/takaram/kofam_scan.git
     ```

2. **Prodigal not found**
   - **Symptom**: `GenePredictionError: Prodigal failed with error: Command not found`
   - **Solution**: Install Prodigal and ensure it's in your PATH:
     ```bash
     which prodigal  # Should return a path
     ```

### Runtime Errors

1. **Input validation errors**
   - **Symptom**: `InputValidationError: FASTA file not found`
   - **Solution**: Verify file paths and ensure files exist

2. **Feature calculation errors**
   - **Symptom**: `FeatureCalculationError: Failed to calculate features`
   - **Solution**: Check sequence quality and format, ensure sequences are valid coding sequences

3. **Memory errors**
   - **Symptom**: `MemoryError` or system freezes
   - **Solution**:
     - Enable low memory mode: `--low-memory` option
     - Process smaller batches of sequences
     - Use a machine with more RAM

### Model Prediction Issues

1. **Unexpected growth rate predictions**
   - **Symptom**: Growth rates seem unrealistic
   - **Solution**:
     - Ensure taxonomy information is correct
     - Check that the input sequences are properly annotated
     - Verify temperature parameter if used

2. **NaN or infinite values in results**
   - **Symptom**: `nan` or `inf` in prediction results
   - **Solution**:
     - Check for invalid input sequences
     - Ensure KO annotations are properly formatted
     - Update to the latest version of DeepMu

3. **CUDA out of memory**
   - **Symptom**: `RuntimeError: CUDA out of memory`
   - **Solution**:
     - Reduce batch size
     - Use CPU mode: `predictor = MicrobialGrowthPredictor(device="cpu")`
     - Free GPU memory between predictions

For persistent issues, check the error logs in the output directory or enable verbose mode with `--verbose` flag.

## Feature Engineering

### Pathway-Clustered Features
- KEGG pathway-based genomic feature grouping
- Automatic pathway enrichment analysis
- Cluster-specific normalization

```python
from deepmu.features import PathwayClusterFeatures

cluster_features = PathwayClusterFeatures(kegg_path='pathways/')
cluster_features.save_cluster_report('reports/')
```

We calculate genomic features including

### Feature Categories

The system calculates four main metrics for HEGs, background genes, and their deltas:
1. **Codon Usage Bias (CUB)**: Measures the non-uniformity of synonymous codon usage
2. **Codon Pair Bias (CPB)**: Quantifies preferences for specific adjacent codon pairs
3. **Codon Consistency**: Measures consistent usage of preferred codons
4. **Remote Codon Interaction**: Quantifies non-random patterns in codon arrangement

### Implementation Details

```python
def calculate_features_with_heg(self, sequences, ko_map=None):
    """Calculate codon usage features with distinction between HEGs and background.

    Args:
        sequences (list): List of sequence records or DNA strings
        ko_map (dict): Dictionary mapping sequence IDs to KO IDs

    Returns:
        dict: Dictionary with standard, HEG-specific, background, and delta metrics
    """
    # If no KO map is provided, treat all sequences as background
    if ko_map is None:
        heg_sequences = []
        bg_sequences = sequences
    else:
        heg_sequences, bg_sequences = self.split_heg_bg_sequences(sequences, ko_map)

    # Calculate metrics for the entire genomic set
    genomic_seq = ''.join(str(seq.seq) if hasattr(seq, 'seq') else seq for seq in sequences)
    genomic_metrics = self.calculate_features(genomic_seq)

    # Calculate metrics for HEGs and background
    heg_seq = ''.join(str(seq.seq) if hasattr(seq, 'seq') else seq for seq in heg_sequences)
    heg_metrics = self.calculate_features(heg_seq)

    bg_seq = ''.join(str(seq.seq) if hasattr(seq, 'seq') else seq for seq in bg_sequences)
    bg_metrics = self.calculate_features(bg_seq)

    # Calculate delta metrics (HEG - BG)
    delta_metrics = {
        'delta_CUB': heg_metrics['CUB'] - bg_metrics['CUB'],
        'delta_CPB': heg_metrics['CPB'] - bg_metrics['CPB'],
        'delta_Consistency': heg_metrics['Consistency'] - bg_metrics['Consistency'],
        'delta_RemoteInteraction': heg_metrics['RemoteInteraction'] - bg_metrics['RemoteInteraction']
    }

    # Combine all metrics into unified feature dictionary
    return {
        # Standard metrics for whole genome
        'CUB': genomic_metrics['CUB'],
        'CPB': genomic_metrics['CPB'],
        'Consistency': genomic_metrics['Consistency'],
        'RemoteInteraction': genomic_metrics['RemoteInteraction'],
        # HEG-specific metrics
        'HEG_CUB': heg_metrics['CUB'],
        'HEG_CPB': heg_metrics['CPB'],
        'HEG_Consistency': heg_metrics['Consistency'],
        'HEG_RemoteInteraction': heg_metrics['RemoteInteraction'],
        # Background metrics
        'BG_CUB': bg_metrics['CUB'],
        'BG_CPB': bg_metrics['CPB'],
        'BG_Consistency': bg_metrics['Consistency'],
        'BG_RemoteInteraction': bg_metrics['RemoteInteraction'],
        # Delta metrics
        'delta_CUB': delta_metrics['delta_CUB'],
        'delta_CPB': delta_metrics['delta_CPB'],
        'delta_Consistency': delta_metrics['delta_Consistency'],
        'delta_RemoteInteraction': delta_metrics['delta_RemoteInteraction']
    }
```

## Pathway-Aware Features

The pathway-aware feature system integrates metabolic pathway information into microbial growth rate prediction:

### Feature Categories

The system calculates two main metrics for pathway-aware predictions:
1. **Pathway Completeness**: Measures the completeness of the metabolic pathway
2. **Pathway Similarity**: Measures the similarity between pathways of different organisms

### Implementation Details

```python
def calculate_pathway_features(self, ko_file):
    """Calculate pathway features from a KO file."""
    pathway_features = {}
    with open(ko_file, 'r') as f:
        for line in f:
            if line.startswith('#'):
                continue
            parts = line.strip().split()
            if len(parts) >= 2:
                seq_id = parts[0]
                ko_id = parts[1]
                pathway_features[seq_id] = ko_id
    return pathway_features
```

## Genome Preprocessing

The genome preprocessing pipeline streamlines the transition from raw genomic sequences to annotated data ready for growth rate prediction:

### Gene Prediction with Prodigal

Prodigal is integrated as a state-of-the-art gene prediction tool, with DeepMu handling:
- Automatic execution and parameter management
- Support for different genetic codes
- Metagenome-aware prediction
- Output parsing and integration

Implementation details:
```python
def predict_genes(self, genome_file: str, output_prefix: str, is_meta: bool = False) -> Tuple[str, str]:
    """Predict genes using Prodigal."""
    protein_file = f"{output_prefix}_proteins.faa"
    cds_file = f"{output_prefix}_cds.ffn"

    # Build Prodigal command
    cmd = [
        "prodigal",
        "-i", genome_file,
        "-a", protein_file,
        "-d", cds_file,
        "-g", str(self.genetic_code),
        "-p", "meta" if is_meta else "single",
        "-q"  # Quiet mode
    ]

    # Run Prodigal
    process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    _, stderr = process.communicate()

    # Check for errors
    if process.returncode != 0:
        raise GenePredictionError(f"Prodigal failed with error: {stderr.decode()}")

    return protein_file, cds_file
```

### KO Annotation with KofamScan

KofamScan is used for functional annotation with KEGG Orthology terms:
- Automatic execution with appropriate parameters
- Handling of large datasets through batching
- Custom parsing and output formatting
- Adaptive threshold management

Implementation details:
```python
def annotate_proteins(self, protein_file: str, output_prefix: str) -> Tuple[str, Dict[str, str]]:
    """Annotate proteins with KO terms using KofamScan."""
    ko_file = f"{output_prefix}_ko.tsv"

    # Build KofamScan command
    cmd = [
        "exec_annotation",
        "-p", self.kofamscan_profile_dir,
        "-k", self.kofamscan_ko_list,
        "-o", ko_file,
        "--cpu", str(self.cpu),
        "--tmp-dir", self.tmp_dir,
        protein_file
    ]

    # Run KofamScan
    process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    _, stderr = process.communicate()

    # Check for errors
    if process.returncode != 0:
        raise KOAnnotationError(f"KofamScan failed with error: {stderr.decode()}")

    # Parse KO file to build sequence-to-KO mapping
    ko_dict = {}
    with open(ko_file, 'r') as f:
        for line in f:
            if line.startswith('#'):
                continue
            parts = line.strip().split()
            if len(parts) >= 2:
                seq_id = parts[0]
                ko_id = parts[1]
                ko_dict[seq_id] = ko_id

    return ko_file, ko_dict
```

### Integrated Workflow

The complete preprocessing workflow combines gene prediction and KO annotation:
```python
def preprocess_genome(self, genome_file: str, output_prefix: str, is_meta: bool = False) -> Tuple[str, str, Dict[str, str]]:
    """Preprocess a genome by predicting genes and annotating with KO terms."""
    try:
        # Predict genes using Prodigal
        protein_file, cds_file = self.predict_genes(genome_file, output_prefix, is_meta)

        # Annotate proteins with KO terms
        ko_file, ko_dict = self.annotate_proteins(protein_file, output_prefix)

        return protein_file, cds_file, ko_dict

    except (GenePredictionError, KOAnnotationError) as e:
        raise PreprocessingError(f"Preprocessing failed: {str(e)}")
```

## Hierarchical Taxonomy

The hierarchical taxonomy system enhances phylogenetic modeling by incorporating multi-level taxonomic relationships:

### Taxonomy String Format

Taxonomic information is represented as pipe-separated strings of taxonomic IDs:
```
2|1224|1236|91347|543|570
```
Where the levels represent (in order):
- Domain/Kingdom
- Phylum
- Class
- Order
- Family
- Genus

### NCBI Taxonomy Integration

DeepMu v1.1.1 now supports automatic retrieval of taxonomy lineages from NCBI taxonomy IDs using the ETE3 toolkit:

```python
def get_taxonomy_string_from_taxid(taxid):
    """
    Get a taxonomy string from a single NCBI taxonomy ID using ETE3.

    Args:
        taxid (str): NCBI taxonomy ID

    Returns:
        str: Pipe-separated taxonomy string (e.g., "2|1224|1236|91347|543|570")
    """
    if not taxid:
        return None

    try:
        from ete3 import NCBITaxa
        ncbi = NCBITaxa()

        # Convert taxid to int
        taxid = int(taxid)

        # Get the lineage
        lineage = ncbi.get_lineage(taxid)

        # Get the names of the taxa at specific ranks
        ranks = ["superkingdom", "phylum", "class", "order", "family", "genus", "species"]
        ranks2levels = ncbi.get_rank(lineage)
        levels2ranks = dict((ranks2levels[taxon], taxon) for taxon in lineage)

        # Extract the taxids at each rank
        taxonomy = []
        for rank in ranks:
            if rank in levels2ranks:
                taxonomy.append(str(levels2ranks[rank]))
            else:
                taxonomy.append("0")  # Use 0 for missing ranks

        # Return pipe-separated string, excluding superkingdom
        return "|".join(taxonomy[1:6])  # phylum to genus

    except ImportError:
        logging.warning("ETE3 not installed. Cannot retrieve taxonomy lineage.")
        return None
    except Exception as e:
        logging.warning(f"Error retrieving taxonomy lineage for taxid {taxid}: {e}")
        return None
```

This functionality enables users to provide just a single NCBI taxonomy ID (e.g., 511145 for E. coli K-12 MG1655) and have the system automatically retrieve the full lineage.

### TaxonomicEmbedder Implementation

The `TaxonomicEmbedder` class creates embeddings for each taxonomy level:

```python
class TaxonomicEmbedder(nn.Module):
    """
    Hierarchical taxonomic embedder.

    This class embeds taxonomic IDs at multiple levels of the taxonomy
    (phylum, class, order, family, genus) and combines them.
    """

    def __init__(self,
                level_vocab_sizes: Dict[str, int],
                embed_dim: int = 32,
                use_attention: bool = True):
        """
        Initialize the taxonomic embedder.

        Args:
            level_vocab_sizes: Dictionary mapping taxonomy levels to vocabulary sizes.
                            Example: {"phylum": 100, "class": 500, ...}
            embed_dim: Dimension of embeddings for each level.
            use_attention: Whether to use attention for combining level embeddings.
        """
        super().__init__()
        self.embed_dim = embed_dim
        self.use_attention = use_attention
        self.taxonomy_levels = ["phylum", "class", "order", "family", "genus"]

        # Create embedding layers for each taxonomy level
        self.embeddings = nn.ModuleDict({
            level: nn.Embedding(vocab_size, embed_dim)
            for level, vocab_size in level_vocab_sizes.items()
            if level in self.taxonomy_levels
        })

        # Attention mechanism for combining embeddings
        if use_attention:
            self.attention = nn.Linear(embed_dim, 1, bias=False)
```

### Enhanced Graph Neural Network

The hierarchical GNN architecture creates a graph representation of organisms based on taxonomy and KO similarity:

```python
class HierarchicalGNN(nn.Module):
    """
    Hierarchical Graph Neural Network for phylogenetic modeling.

    This model uses both taxonomic information and KO profile similarity
    to create a graph representation of organisms and process it using
    Graph Neural Networks.
    """

    def __init__(self,
                taxonomy_vocab_sizes: Dict[str, int],
                tax_embed_dim: int = 32,
                hidden_dim: int = 128,
                output_dim: int = 128,
                ko_weight: float = 0.6,
                use_attention: bool = True,
                dropout: float = 0.3):
        """
        Initialize the hierarchical GNN.

        Args:
            taxonomy_vocab_sizes: Dictionary mapping taxonomy levels to vocabulary sizes
            tax_embed_dim: Dimension of taxonomy embeddings
            hidden_dim: Dimension of hidden GNN layers
            output_dim: Output dimension
            ko_weight: Weight of KO similarity vs. taxonomy similarity (0-1)
            use_attention: Whether to use attention for taxonomy embeddings
            dropout: Dropout rate
        """
        super().__init__()
        self.ko_weight = ko_weight

        # Taxonomy embedder
        self.tax_embedder = TaxonomicEmbedder(
            level_vocab_sizes=taxonomy_vocab_sizes,
            embed_dim=tax_embed_dim,
            use_attention=use_attention
        )
```

## KO Similarity Analysis

The KO similarity analysis enhances functional context through KEGG Orthology profiles:

### KOSimilarityCalculator Implementation

The `KOSimilarityCalculator` class computes similarity between organisms based on their KO profiles:

```python
class KOSimilarityCalculator:
    """
    Calculator for KO profile similarity between genomes.

    This class calculates the similarity between KO profiles of genomes,
    which can be used to build a graph for the GNN-based phylogenetic model.
    """

    def __init__(self, use_weighted_similarity: bool = True):
        """
        Initialize the KO similarity calculator.

        Args:
            use_weighted_similarity: Whether to use weighted Jaccard similarity.
        """
        self.use_weighted_similarity = use_weighted_similarity

        # KO index mapping
        self.ko_index = {}  # Maps KO IDs to indices
        self.current_idx = 0
```

### Feature Calculation Pipeline

The KO similarity feature calculation follows this workflow:
1. Parse KO annotations from file
2. Convert to frequency-based profile
3. Calculate pairwise Jaccard similarities
4. Build similarity graph
5. Integrate with taxonomic information

### Integration with Hierarchical GNN

The KO similarity features are integrated with taxonomic information in the GNN:

```python
def forward(self, taxonomy_dict: Dict[str, torch.Tensor], ko_dict: Dict[str, Set[str]]) -> torch.Tensor:
    """
    Forward pass through the GNN.

    Args:
        taxonomy_dict: Dictionary of taxonomy tensors by level
        ko_dict: Dictionary of KO sets by batch ID

    Returns:
        Tensor of embeddings [batch_size, output_dim]
    """
    # Get batch size
    batch_size = next(iter(taxonomy_dict.values())).size(0)

    # Get taxonomy embeddings
    tax_embeds = self.tax_embedder(taxonomy_dict)

    # Create graph adjacency matrix
    adj_matrix = torch.eye(batch_size, device=tax_embeds.device)

    # Add edges based on taxonomic similarity
    for i in range(batch_size):
        for j in range(i+1, batch_size):
            # Calculate taxonomic similarity
            tax_sim = self.calculate_taxonomic_similarity(
                taxonomy_dict, i, j, self.taxonomy_levels
            )

            # Calculate KO similarity
            ko_sim = 0.0
            if i in ko_dict and j in ko_dict:
                ko_sim = self.ko_calculator.jaccard_similarity(
                    ko_dict[i], ko_dict[j]
                )

            # Combine similarities with weighting
            combined_sim = (1 - self.ko_weight) * tax_sim + self.ko_weight * ko_sim

            # Add to adjacency matrix
            adj_matrix[i, j] = combined_sim
            adj_matrix[j, i] = combined_sim
```

## Performance Optimization

Several strategies can be employed to optimize DeepMu's performance:

### Hardware Optimization

1. **GPU Acceleration**:
   - Enable GPU support for significant speedups during model inference:
   ```python
   predictor = MicrobialGrowthPredictor(device="cuda")
   ```
   - For multi-GPU systems, specify which GPU to use:
   ```python
   import os
   os.environ["CUDA_VISIBLE_DEVICES"] = "0"  # Use first GPU
   ```

2. **CPU Parallelism**:
   - Increase the number of CPU cores for preprocessing:
   ```bash
   python deepmu_cli.py preprocess genome.fna --cpu 8
   ```

### Data Processing Optimization

1. **Batch Processing**:
   - Process multiple genomes at once:
   ```bash
   python deepmu_cli.py batch genome_dir/ --output-dir results.json
   ```

2. **Preprocessing Caching**:
   - Cache preprocessed results to avoid redundant computations:
   ```python
   # Process once
   cds_file, ko_file = preprocess_genome(genome_file)

   # Reuse for multiple analyses
   predictor.predict_single(cds_file, ko_file)
   ```

### Memory Optimization

1. **Low-Memory Mode**:
   - Enable batch processing of sequences for large genomes:
   ```python
   predictor.predict_single(cds_file, ko_file, low_memory=True)
   ```

2. **Feature Selection**:
   - Use only essential features for prediction:
   ```python
   predictor = MicrobialGrowthPredictor(
       use_pathways=False,  # Disable pathway features
       use_hierarchical_phylo=False  # Use simpler phylogeny model
   )
   ```

### Inference Optimization

1. **Half-Precision Inference**:
   - Enable FP16 computation for faster inference:
   ```python
   predictor = MicrobialGrowthPredictor(half_precision=True)
   ```

2. **Model Quantization**:
   - Use quantized models for faster inference with slight accuracy loss:
   ```python
   predictor = MicrobialGrowthPredictor(model_path="models/quantized_model.pt")
   ```

## Extending DeepMu

DeepMu is designed to be easily extensible. Here are some ways to extend the functionality:

### Adding New Features

To add new features to the model, you can:
1. Implement a new feature calculator class
2. Integrate the new feature into the model architecture
3. Update the feature extraction pipeline

### Implementing New Predictors

To implement a new predictor, you can:
1. Create a new predictor class
2. Implement the prediction logic
3. Update the command-line interface

### Customizing Model Architecture

To customize the model architecture, you can:
1. Modify the network architecture
2. Update the feature extraction pipeline
3. Implement new feature fusion mechanisms

### Integrating External Data

To integrate external data, you can:
1. Implement a new feature calculator
2. Integrate the new feature into the model architecture
3. Update the feature extraction pipeline

### Customizing Error Handling

To customize error handling, you can:
1. Implement a new exception class
2. Update the error handling logic
3. Implement new error handling strategies

### Customizing Command-Line Interface

To customize the command-line interface, you can:
1. Implement new command-line options
2. Update the command-line logic
3. Implement new command-line commands

## Command-Line Interface

The DeepMu CLI provides a comprehensive interface for all functionality:

### Preprocessing Command

```bash
python deepmu_cli.py preprocess <genome_file> [options]
```

Options:
- `--output-dir`: Directory for output files
- `--kofamscan-db`: Path to KofamScan database
- `--genetic-code`: Genetic code for Prodigal (default: 11)
- `--batch`: Process multiple genomes in batch mode
- `--meta-mode`: Use metagenome mode for Prodigal
- `--cpu`: Number of CPUs to use (default: all available)

### Single Organism Prediction

```bash
python deepmu_cli.py single <sequence_file> [options]
```

Options:
- `--is-genome`: Treat input as genome file requiring preprocessing
- `--ko-file`: File with KO annotations
- `--taxid`: NCBI taxonomy ID or taxonomy string in format "phylum|class|order|family|genus"
- `--output-dir`: Directory for output files
- `--temp`: Temperature value
- `--analyze`: Enable detailed feature analysis
- `--output`: Output JSON file

### Community Prediction

```bash
python deepmu_cli.py community <sequence_file> [options]
```

Options:
- `--coverage`: Coverage file (abundance)
- `--contigs`: Contig file
- `--taxonomy-file`: Taxonomy mapping file
- `--org-to-seq`: Organism to sequence mapping
- `--kofamscan-db`: Path to KofamScan database
- `--output-dir`: Directory for output files
- `--output`: Output JSON file

### Batch Processing

```bash
python deepmu_cli.py batch <directory> [options]
```

Options:
- `--is-genome`: Treat inputs as genome files requiring preprocessing
- `--ko-dir`: Directory with KO files
- `--taxonomy-dir`: Directory with taxonomy files
- `--kofamscan-db`: Path to KofamScan database
- `--output-dir`: Directory for output files
- `--heg-ko-list`: List of HEG KO IDs
- `--output`: Output JSON file

## Error Handling Architecture

DeepMu features a comprehensive error handling architecture to ensure reliability and provide useful diagnostic information:

### Exception Hierarchy

```
DeepMuError (base exception)
├── InputValidationError
├── ModelLoadError
├── FeatureCalculationError
├── SequenceProcessingError
├── PreprocessingError
│   ├── GenePredictionError
│   └── KOAnnotationError
├── PathwayProcessingError
└── PredictionError
```

### Error Categories

1. **InputValidationError**: Issues with input data format, missing files, etc.
2. **ModelLoadError**: Problems loading the pre-trained model or its weights
3. **FeatureCalculationError**: Failures during the computation of codon usage features
4. **SequenceProcessingError**: Problems with sequence reading or validation
5. **PreprocessingError**: Generic preprocessing failures
   - **GenePredictionError**: Failures in Prodigal gene prediction
   - **KOAnnotationError**: Failures in KofamScan annotation
6. **PathwayProcessingError**: Issues with KEGG pathway processing
7. **PredictionError**: Runtime errors during model prediction

### Implementation Details

```python
class DeepMuError(Exception):
    """Base exception for all DeepMu errors."""
    pass

class InputValidationError(DeepMuError):
    """Raised when input validation fails."""
    pass

class ModelLoadError(DeepMuError):
    """Raised when the model cannot be loaded."""
    pass

class FeatureCalculationError(DeepMuError):
    """Raised when there is an error calculating features."""
    pass

class SequenceProcessingError(DeepMuError):
    """Raised when there is an error processing sequences."""
    pass

class PreprocessingError(DeepMuError):
    """Raised when there is an error in the preprocessing pipeline."""
    pass

class GenePredictionError(PreprocessingError):
    """Raised when gene prediction fails."""
    pass

class KOAnnotationError(PreprocessingError):
    """Raised when KO annotation fails."""
    pass

class PathwayProcessingError(DeepMuError):
    """Raised when there is an error processing KEGG pathway data."""
    pass

class PredictionError(DeepMuError):
    """Raised when the prediction fails."""
    pass
```

### Error Handling in Action

```python
def predict_single(self, fasta_path, temperature=None, predict_temp=True,
                 ko_file=None, analyze_features=False, ko_lookup=None,
                 heg_ko_list=None, taxonomy_string=None, output_dir=None):
    """
    Predict growth rate and optimal temperature for a single organism.

    Args:
        fasta_path (str): Path to FASTA file with CDS sequences
        temperature (float, optional): Growth temperature in Celsius
        predict_temp (bool): Whether to predict optimal temperature
        ko_file (str, optional): Path to KO annotation file
        analyze_features (bool): Whether to include feature analysis in results
        ko_lookup (str, optional): Path to KO lookup file
        heg_ko_list (str, optional): Path to list of HEG KO IDs
        taxonomy_string (str, optional): Taxonomy string
        output_dir (str, optional): Directory for output files

    Returns:
        dict: Prediction results

    Raises:
        InputValidationError: If inputs are invalid
        SequenceProcessingError: If sequences can't be processed
        FeatureCalculationError: If features can't be calculated
        PredictionError: If prediction fails
    """
    try:
        # Validate inputs
        if not os.path.exists(fasta_path):
            raise InputValidationError(f"FASTA file not found: {fasta_path}")

        if ko_file and not os.path.exists(ko_file):
            raise InputValidationError(f"KO file not found: {ko_file}")

        # Load sequences
        try:
            sequences = self._load_sequences(fasta_path)
        except Exception as e:
            raise SequenceProcessingError(f"Failed to load sequences: {e}")

        # Calculate features
        try:
            features = self.calculate_features(sequences, ko_file, ko_lookup,
                                             heg_ko_list, taxonomy_string)
        except Exception as e:
            raise FeatureCalculationError(f"Failed to calculate features: {e}")

        # Make predictions
        try:
            results = self._predict(features, temperature, predict_temp)

            # Add feature analysis if requested
            if analyze_features:
                results['features'] = features

            return results
        except Exception as e:
            raise PredictionError(f"Prediction failed: {e}")

    except DeepMuError:
        # Re-raise DeepMu-specific errors
        raise
    except Exception as e:
        # Catch-all for unexpected errors
        raise DeepMuError(f"Unexpected error: {e}")
```

### Debugging Features

To facilitate troubleshooting, several debugging features are available:

1. **Verbose Logging**: Detailed logging of all operations
2. **Feature Introspection**: Analyze and visualize calculated features
3. **Step-by-Step Execution**: Trace execution path with detailed logs
4. **Contextual Error Messages**: Errors include context to help pinpoint issues

### Common Error Resolution Strategies

1. **Input Validation Errors**:
   - Check file paths and formats
   - Validate taxonomy strings
   - Ensure all required files are present

2. **Feature Calculation Errors**:
   - Check sequence quality and format
   - Verify KO annotations
   - Ensure sufficient sequence length

3. **Prediction Errors**:
   - Check model compatibility
   - Verify feature normalization
   - Ensure hardware compatibility

### Error Capture in CLI

The CLI captures and reports errors in a user-friendly format:

```python
def main():
    try:
        # Setup and parse arguments
        args = parse_arguments()

        # Execute command
        if args.command == 'single':
            single_organism_command(args)
        elif args.command == 'community':
            community_command(args)
        elif args.command == 'batch':
            batch_command(args)
        elif args.command == 'preprocess':
            preprocess_command(args)
        else:
            print("Unknown command. Use --help for usage information.")
            sys.exit(1)

    except InputValidationError as e:
        print(f"Input validation error: {e}", file=sys.stderr)
        sys.exit(1)
    except ModelLoadError as e:
        print(f"Model loading error: {e}", file=sys.stderr)
        sys.exit(1)
    except FeatureCalculationError as e:
        print(f"Feature calculation error: {e}", file=sys.stderr)
        sys.exit(1)
    except SequenceProcessingError as e:
        print(f"Sequence processing error: {e}", file=sys.stderr)
        sys.exit(1)
    except PreprocessingError as e:
        print(f"Preprocessing error: {e}", file=sys.stderr)
        sys.exit(1)
    except PredictionError as e:
        print(f"Prediction error: {e}", file=sys.stderr)
        sys.exit(1)
    except DeepMuError as e:
        print(f"DeepMu error: {e}", file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        print(f"Unexpected error: {e}", file=sys.stderr)
        if args.verbose:
            traceback.print_exc()
        sys.exit(1)
```

## Hybrid RF-Transformer Architecture

DeepMu v1.1.1 introduces a hybrid Random Forest-Transformer architecture for growth rate prediction with temperature prediction.

## Advanced Regularization Techniques

DeepMu implements various regularization techniques to prevent overfitting and improve generalization:

1. **L1 Regularization**: Penalizes large weights
2. **L2 Regularization**: Penalizes large weight magnitudes
3. **Dropout**: Randomly drops out neurons during training
4. **Early Stopping**: Stops training when validation loss stops improving

## Multi-Branch Neural Networks

DeepMu employs multiple specialized branches for different feature types:

1. **Codon Branch**: Processes codon usage patterns
2. **Phylogenetic Branch**: Processes taxonomic information
3. **Sequence Branch**: Processes DNA sequence patterns
4. **Pathway Branch**: Processes metabolic pathway information

These branches enable the model to focus on the most relevant features for each task.

## Documentation Structure

DeepMu v1.1.1 features a reorganized documentation structure for better accessibility and comprehension:

1. **Core Documentation**
   - User Guide: End-to-end guide for using DeepMu
   - Installation Guide: Detailed installation instructions
   - API Reference: Comprehensive API documentation

2. **Feature Extraction Documentation**
   - Feature Extraction Overview: General guide to feature extraction
   - Modular Feature Extraction: Details on the modular approach
   - Genomic Features: Guide to genomic feature extraction

3. **Model Documentation**
   - Hybrid RF-Transformer Model: Details on the hybrid architecture
   - Random Forest Model: Information about the RF component
   - Transformer Model: Information about the Transformer component
   - Advanced Regularization: Guide to regularization techniques

4. **Architecture Documentation**
   - Multi-Branch Architecture: Details on the neural network design
   - Ensemble Approach: Information about model ensembling

All documentation is available in the `docs/` directory, with a comprehensive README.md file providing navigation assistance.
