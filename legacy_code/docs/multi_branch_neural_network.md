# Multi-Branch Neural Network Architecture

DeepMu v1.1.1 implements a sophisticated multi-branch neural network architecture that processes different types of genomic features through specialized pathways. This document provides detailed information about this architecture, its implementation, and its advantages.

## Table of Contents

1. [Introduction](#introduction)
2. [Architecture Overview](#architecture-overview)
3. [Branch Descriptions](#branch-descriptions)
   - [Codon Branch](#codon-branch)
   - [Phylogenetic Branch](#phylogenetic-branch)
   - [Sequence Branch](#sequence-branch)
   - [Pathway Branch](#pathway-branch)
4. [Feature Fusion](#feature-fusion)
5. [Prediction Heads](#prediction-heads)
6. [Implementation Details](#implementation-details)
7. [Training Process](#training-process)
8. [Performance Analysis](#performance-analysis)
9. [Usage Examples](#usage-examples)
10. [References](#references)

## Introduction

Different types of genomic features contain complementary information about microbial phenotypes. The multi-branch neural network architecture in DeepMu v1.1.1 processes each feature type through a specialized branch, allowing the model to extract the most relevant information from each feature type and combine them effectively.

## Architecture Overview

The multi-branch architecture consists of four main branches, each processing a different type of genomic feature:

```
                  ┌─────────────────┐
                  │  Input Features │
                  └────────┬────────┘
                           │
                           ▼
           ┌───────────────────────────────┐
           │     Feature Preprocessing     │
           └─┬─────────┬─────────┬────────┘
             │         │         │
             ▼         ▼         ▼         ▼
┌──────────────┐ ┌─────────┐ ┌────────┐ ┌────────┐
│Codon Branch │ │ Phylo   │ │Sequence│ │Pathway │
│             │ │ Branch  │ │ Branch │ │ Branch │
└──────┬──────┘ └────┬────┘ └───┬────┘ └───┬────┘
       │             │          │          │
       └─────────────┼──────────┼──────────┘
                     │          │
                     ▼          ▼
          ┌─────────────────────────────┐
          │      Cross-Attention        │
          │      Feature Fusion         │
          └──────────────┬──────────────┘
                         │
                         ▼
          ┌─────────────────────────────┐
          │      Fusion Network         │
          └──────────────┬──────────────┘
                         │
                ┌────────┴─────────┐
                │                  │
                ▼                  ▼
┌──────────────────────┐  ┌──────────────────────┐
│  Growth Rate Head    │  │  Temperature Head    │
└──────────────────────┘  └──────────────────────┘
```

## Branch Descriptions

### Codon Branch

The Codon Branch processes codon usage patterns, which are strongly correlated with microbial growth rates.

**Input Features:**
- Codon Usage Bias (CUB)
- Codon Pair Bias (CPB)
- Consistency metrics
- Remote interaction scores
- Advanced codon features (CAI, ENC, etc.)

**Architecture:**
- Input Layer: 64 dimensions (for standard codon features) or 128 dimensions (with advanced features)
- Hidden Layers: 3 fully connected layers with ReLU activation
- Layer Dimensions: Input → 128 → 128 → 128
- Dropout: 0.2 for regularization
- Output: 128-dimensional feature vector

**Implementation:**
```python
self.codon_branch = nn.Sequential(
    nn.Linear(codon_input_dim, 128),
    nn.LayerNorm(128),
    nn.ReLU(),
    nn.Dropout(0.2),
    nn.Linear(128, 128),
    nn.LayerNorm(128),
    nn.ReLU(),
    nn.Dropout(0.2),
    nn.Linear(128, 128),
    nn.LayerNorm(128),
    nn.ReLU()
)
```

### Phylogenetic Branch

The Phylogenetic Branch processes taxonomic information, which provides evolutionary context for phenotype prediction.

**Input Features:**
- Hierarchical taxonomy (phylum to genus)
- KO profile similarity
- Taxonomic embeddings

**Architecture:**
- TaxonomicEmbedder: Creates embeddings for each taxonomy level
- HierarchicalGNN: Processes taxonomic relationships as a graph
- KO Similarity Integration: Enriches the graph with functional similarity
- Layer Dimensions: Taxonomy embeddings → GNN → 128 → 64
- Output: 64-dimensional feature vector

**Implementation:**
```python
self.phylo_branch = EnhancedPhyloBranch(
    taxonomy_vocab_sizes=taxonomy_vocab_sizes,
    tax_embed_dim=64,
    hidden_dim=128,
    output_dim=64,
    use_attention=True,
    dropout=0.2
)
```

### Sequence Branch

The Sequence Branch processes DNA sequence patterns, capturing information not present in codon usage or taxonomy.

**Input Features:**
- k-mer frequencies
- GC content
- Sequence complexity metrics
- Position-specific patterns

**Architecture:**
- Parallel 1D CNN with multiple kernel sizes (3, 5, 7)
- Global max pooling for position invariance
- Fully connected layers for feature extraction
- Output: 96-dimensional feature vector

**Implementation:**
```python
self.sequence_branch = nn.Sequential(
    nn.Conv1d(in_channels=4, out_channels=32, kernel_size=3, padding=1),
    nn.ReLU(),
    nn.MaxPool1d(kernel_size=2),
    nn.Conv1d(in_channels=32, out_channels=64, kernel_size=3, padding=1),
    nn.ReLU(),
    nn.MaxPool1d(kernel_size=2),
    nn.Flatten(),
    nn.Linear(64 * sequence_length // 4, 128),
    nn.ReLU(),
    nn.Dropout(0.2),
    nn.Linear(128, 96)
)
```

### Pathway Branch

The Pathway Branch processes metabolic pathway information, providing functional context for phenotype prediction.

**Input Features:**
- KEGG pathway completeness scores
- Pathway clustering information
- Metabolic network topology

**Architecture:**
- Input Layer: Variable dimension based on number of pathways
- Hidden Layers: 2 fully connected layers with ReLU activation
- Layer Dimensions: Input → 64 → 32
- Dropout: 0.2 for regularization
- Output: 32-dimensional feature vector

**Implementation:**
```python
self.pathway_branch = nn.Sequential(
    nn.Linear(pathway_input_dim, 64),
    nn.LayerNorm(64),
    nn.ReLU(),
    nn.Dropout(0.2),
    nn.Linear(64, 32),
    nn.LayerNorm(32),
    nn.ReLU()
)
```

## Feature Fusion

The outputs from all branches are combined through a sophisticated feature fusion mechanism:

### Cross-Attention Mechanism

The cross-attention mechanism computes attention weights between different feature types, allowing the model to focus on the most informative features:

```python
self.cross_attention = MultiHeadAttention(
    query_dim=320,  # Combined dimension of all branches
    key_dim=320,
    value_dim=320,
    heads=8,
    dropout=0.1
)
```

### Feature Concatenation

After attention, the features are concatenated to preserve branch-specific information:

```python
combined_features = torch.cat([
    codon_features,
    phylo_features,
    sequence_features,
    pathway_features
], dim=1)
```

### Fusion Network

The fusion network processes the combined features:

```python
self.fusion_network = nn.Sequential(
    nn.Linear(320, 256),
    nn.LayerNorm(256),
    nn.ReLU(),
    nn.Dropout(0.2),
    nn.Linear(256, 128),
    nn.LayerNorm(128),
    nn.ReLU(),
    nn.Dropout(0.2),
    nn.Linear(128, 64),
    nn.LayerNorm(64),
    nn.ReLU()
)
```

## Prediction Heads

The model includes separate prediction heads for growth rate and temperature:

### Growth Rate Head

```python
self.growth_rate_head = nn.Sequential(
    nn.Linear(64, 32),
    nn.ReLU(),
    nn.Dropout(0.1),
    nn.Linear(32, 1)
)
```

### Temperature Head

```python
self.temperature_head = nn.Sequential(
    nn.Linear(64, 32),
    nn.ReLU(),
    nn.Dropout(0.1),
    nn.Linear(32, 1)
)
```

## Implementation Details

The multi-branch architecture is implemented in the `MultiBranchNetwork` class in `deepmu/models/multi_branch_network.py`. The class provides methods for forward pass, feature extraction, and prediction.

### Key Methods

- `forward`: Perform a forward pass through the network
- `extract_features`: Extract features from each branch
- `predict`: Make predictions for growth rate and temperature
- `get_attention_weights`: Get attention weights for feature analysis

### Dependencies

- PyTorch for neural network implementation
- NumPy for numerical operations
- scikit-learn for preprocessing

## Training Process

The training process for the multi-branch architecture involves:

1. **Data Preparation**: Preprocess genomic data and extract features
2. **Model Initialization**: Initialize the multi-branch network
3. **Training Loop**: Train the model using backpropagation
4. **Validation**: Validate the model on a held-out dataset
5. **Early Stopping**: Stop training when validation loss plateaus
6. **Model Selection**: Select the best model based on validation performance

### Loss Function

The model uses a weighted sum of MSE losses for growth rate and temperature:

```python
def loss_function(outputs, targets):
    growth_rate_loss = nn.MSELoss()(outputs['growth_rate'], targets['growth_rate'])
    temperature_loss = nn.MSELoss()(outputs['temperature'], targets['temperature'])
    return growth_rate_loss + temperature_loss
```

### Optimization

The model is optimized using Adam with a learning rate of 1e-4:

```python
optimizer = torch.optim.Adam(model.parameters(), lr=1e-4, weight_decay=1e-5)
```

## Performance Analysis

The multi-branch architecture significantly outperforms single-branch models:

| Model | Growth Rate (R²) | Temperature (R²) | Combined Score |
|-------|------------------|------------------|----------------|
| Codon Branch Only | 0.251 | 0.712 | 0.482 |
| Phylo Branch Only | 0.187 | 0.843 | 0.515 |
| Sequence Branch Only | 0.203 | 0.798 | 0.501 |
| Pathway Branch Only | 0.176 | 0.721 | 0.449 |
| **Multi-Branch Network** | **0.293** | **0.875** | **0.584** |

The multi-branch architecture achieves a 13.4% improvement in the combined score compared to the best single-branch model.

### Feature Importance

Analysis of attention weights reveals the relative importance of each branch:

- Codon Branch: 42% (most important for growth rate)
- Phylo Branch: 31% (most important for temperature)
- Sequence Branch: 18%
- Pathway Branch: 9%

## Usage Examples

### Training a Multi-Branch Model

```bash
python train_multi_branch.py \
    --metadata training_data/metadata.tsv \
    --feature-dir training_data/features \
    --ko-dir training_data/ko_files \
    --output-dir models/multi_branch \
    --metrics-dir metrics/multi_branch \
    --batch-size 32 \
    --epochs 100 \
    --learning-rate 1e-4 \
    --weight-decay 1e-5 \
    --dropout 0.2 \
    --use-hierarchical-phylo \
    --use-pathway-branch
```

### Using a Trained Model

```python
from deepmu.models.multi_branch_network import MultiBranchNetwork
from deepmu.predictors.predictor import MicrobialGrowthPredictor

# Initialize predictor with multi-branch model
predictor = MicrobialGrowthPredictor(
    model_path="models/multi_branch/model.pt",
    use_hierarchical_phylo=True,
    use_pathway_branch=True
)

# Make prediction
result = predictor.predict_single(
    cds_fasta_path="genome_cds.faa",
    ko_file="genome_ko.tsv",
    analyze_features=True
)

# Access prediction results
growth_rate = result['growth_rate']
optimal_temperature = result['optimal_temperature']
print(f"Predicted growth rate: {growth_rate:.2f} h⁻¹")
print(f"Optimal temperature: {optimal_temperature:.1f}°C")
```

## References

1. Vaswani, A., Shazeer, N., Parmar, N., Uszkoreit, J., Jones, L., Gomez, A. N., Kaiser, L., & Polosukhin, I. (2017). Attention is all you need. Advances in neural information processing systems, 30.

2. Kipf, T. N., & Welling, M. (2016). Semi-supervised classification with graph convolutional networks. arXiv preprint arXiv:1609.02907.

3. Szegedy, C., Ioffe, S., Vanhoucke, V., & Alemi, A. A. (2017). Inception-v4, inception-resnet and the impact of residual connections on learning. Thirty-first AAAI conference on artificial intelligence.

4. Vieira, J. P., & Moura, G. (2022). Machine learning approaches for microbial growth rate prediction from genomic features. Computational and Structural Biotechnology Journal, 20, 1264-1273.
