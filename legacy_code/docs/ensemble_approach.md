# Ensemble Approach for DeepMu

This document describes the ensemble approach implemented in DeepMu for microbial growth rate and optimal temperature prediction.

## Overview

The ensemble approach combines two specialized models:

1. **Improved Random Forest**: Used for growth rate prediction (R² = 0.773), as it excels at tabular data and regression tasks
2. **Transformer-based Neural Network**: Used for temperature prediction (R² = 0.875), as it has shown superior performance for this task

This specialized approach allows each prediction task to benefit from the architecture that works best for it.

![Ensemble Approach](../assets/improved_ensemble_model.txt)

## Key Components

### 1. Improved Random Forest for Growth Rate Prediction

The Improved Random Forest component is a powerful ensemble learning method that:

- Incorporates taxonomy information to capture phylogenetic signals
- Uses entropy-based breakpoint features to detect genomic patterns
- Emphasizes important codon features to improve prediction accuracy
- Provides detailed feature importance analysis for interpretability
- Is robust to overfitting and outliers

For more details, see [Improved Random Forest Model](improved_rf_model.md).

### 2. Transformer for Temperature Prediction

The temperature prediction component uses a transformer-based architecture:

- **Feature Branches**: Process different feature types through dedicated branches
- **Transformer Fusion**: Models complex interactions between features using self-attention
- **Fusion Network**: Processes fused features through multiple layers
- **Output Layer**: Single neuron for temperature prediction

For more details, see [Transformer Model](transformer_model.md).

## Advantages of the Ensemble Approach

The ensemble approach offers several advantages:

1. **Specialized Models**: Each prediction task uses the model that works best for it
2. **Complementary Strengths**:
   - Improved Random Forest: Incorporates taxonomy and breakpoint features, emphasizes important codon features
   - Transformer: Captures complex patterns and interactions, excels at sequence-like data
3. **Improved Performance**: Better performance on both growth rate and temperature prediction tasks
4. **Interpretability**: Improved Random Forest provides detailed feature importance analysis
5. **Efficiency**: Random Forest is faster to train than deep learning models

## Performance Comparison

| Metric | Multi-Branch Advanced | Transformer | Combined DNN-Transformer | Hybrid RF-Transformer | Random Forest | Improved RF | Ensemble Approach |
|--------|----------------------|-------------|--------------------------|------------------------|----------------|-------------|-------------------|
| Growth Rate R² | 0.182 | 0.259 | 0.714 | 0.773 | 0.293 | 0.773 | 0.773 |
| Growth Rate RMSE | 3.78 | 3.82 | 2.06 | 2.12 | 3.39 | 2.12 | 2.12 |
| Temperature R² | 0.772 | 0.875 | 0.744 | -0.021 | - | - | 0.875 |
| Temperature RMSE | 6.80 | 5.97 | 7.80 | 19.07 | - | - | 5.97 |

The ensemble approach combines:
- The excellent growth rate prediction of the Improved Random Forest (R² = 0.773)
- The superior temperature prediction of the Transformer (R² = 0.875)

The Improved Random Forest model achieves the same growth rate prediction performance as the hybrid RF-Transformer model (R² = 0.773), but with a simpler architecture that is easier to implement and maintain.

## Feature Types

The models support the following feature types:

1. **Codon Features**: Codon usage bias, codon pair bias, CAI, etc.
2. **Amino Acid Features**: Amino acid composition, physicochemical properties, etc.
3. **Genomic Features**: Genome size, GC content, dinucleotide frequencies, etc.
4. **RNA Features**: tRNA and rRNA counts, tAI, etc.
5. **Protein pI Features**: Isoelectric point features
6. **Taxonomy Features**: Taxonomic classification features

## Usage

### Training

To train the Improved Random Forest model for growth rate prediction:

```bash
python train_improved_rf.py \
    --feature-file features_all/combined_features.tsv \
    --metadata training_data/metadata.tsv \
    --output-dir models/improved_rf \
    --metrics-dir metrics/improved_rf
```

To train the Transformer model for temperature prediction:

```bash
python train_transformer.py \
    --feature-file features_all/combined_features.tsv \
    --metadata training_data/metadata.tsv \
    --output-dir models/transformer \
    --metrics-dir metrics/transformer
```

### Evaluation

To evaluate the Improved Random Forest model for growth rate prediction:

```bash
python train_improved_rf.py \
    --feature-file features_all/combined_features.tsv \
    --metadata training_data/metadata.tsv \
    --output-dir models/improved_rf_evaluation \
    --metrics-dir metrics/improved_rf_evaluation
```

To evaluate the Transformer model for temperature prediction, you would use a similar script.

### Prediction

To make predictions using the Improved Random Forest model for growth rate prediction:

```python
from deepmu.models.improved_rf import ImprovedRandomForest

# Load model
model = ImprovedRandomForest()
model.load('models/improved_rf')

# Make predictions
predictions = model.predict(features, taxonomy_features, breakpoint_features)
```

To make predictions using the Transformer model for temperature prediction, you would use a similar approach.

## Implementation Details

The implementation is available in the following files:

- `deepmu/models/improved_rf.py`: Implementation of the Improved Random Forest model
- `deepmu/models/transformer_network.py`: Implementation of the Transformer model
- `train_improved_rf.py`: Training script for the Improved Random Forest model
- `train_transformer.py`: Training script for the Transformer model

## References

1. Breiman, L. (2001). Random forests. *Machine learning*, 45(1), 5-32.
2. Vaswani, A., et al. (2017). Attention is all you need. *Advances in neural information processing systems*.
