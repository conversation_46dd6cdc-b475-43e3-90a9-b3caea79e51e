# Specialized Models for Microbial Growth Rate and Optimal Temperature Prediction

## Key Findings

Based on our extensive experimentation with specialized models for each prediction target:

1. **For temperature prediction**: The enhanced DNN model performs excellently (R² = 0.9421)
   - Complex architecture with hidden dimensions [512, 512, 384, 256, 128]
   - Progressive dropout rates [0.3, 0.4, 0.4, 0.5, 0.5]
   - Batch normalization and One-Cycle Learning Rate Schedule
   - Located in `models/enhanced_dnn_temperature_v2/`

2. **For growth rate prediction**: The hybrid model with enhanced NN performs outstandingly (R² = 0.9432)
   - Ensemble of Random Forest, XGBoost, and LightGBM with optimized weights
   - Performance exceeds the expected value (original reported R² = 0.9207)
   - Proper handling of transformations is crucial for good performance
   - Located in `models/hybrid_enhanced_nn_v1/`

## Performance Comparison

| Model | Growth Rate R² | Temperature R² | Overall R² |
|-------|----------------|----------------|------------|
| Best Multi-Task Model | 0.8592 | 0.8756 | 0.8674 |
| Specialized Models (Expected) | 0.9207 | 0.9548 | 0.9378 |
| Specialized Models (Achieved) | 0.9432 | 0.9421 | 0.9426 |
| Improvement | +0.0840 | +0.0665 | +0.0752 |

The specialized models show excellent performance for both growth rate and temperature prediction. The overall performance is significantly better than the multi-task model, with an improvement of +0.0752 in R² value.

## Implementation

We have implemented scripts for using these specialized models:

1. `predict_temperature.py`: Uses the enhanced DNN model for temperature prediction
2. `predict_with_correct_growth_model.py`: Uses the hybrid model with enhanced NN for growth rate prediction, with correct handling of transformations
3. `predict_with_specialized_models_fixed.py`: Combines both models for comprehensive prediction

## Usage

Based on our findings, we recommend using the specialized models for both growth rate and temperature prediction:

```bash
# For temperature prediction
python predict_temperature.py --feature_file your_features.tsv --metadata_file your_metadata.tsv --output_dir predictions/temperature

# For growth rate prediction
python predict_with_correct_growth_model.py --feature_file your_features.tsv --metadata_file your_metadata.tsv --output_dir predictions/growth_rate

# For both predictions
python predict_with_specialized_models_fixed.py --feature_file your_features.tsv --metadata_file your_metadata.tsv --output_dir predictions/specialized_models
```

These scripts correctly handle the transformations applied during training and achieve state-of-the-art performance for both growth rate and temperature prediction.

## Important Notes

1. **Data Transformation**: The growth rate model was trained on square root-transformed and RobustScaler-transformed data. Proper inverse transformation is crucial for good performance.

2. **Feature Scaling**: Both models use their own feature scalers, which are loaded from their respective model directories.

3. **Model Locations**: The models are located in the following directories:
   - Temperature model: `models/enhanced_dnn_temperature_v2/`
   - Growth rate model: `models/hybrid_enhanced_nn_v1/`

4. **Prediction Output**: The prediction scripts save the following outputs:
   - Predictions in TSV format
   - Evaluation metrics in TSV format
   - Scatter plots of actual vs. predicted values

5. **Transformation Info**: The growth rate model includes a `transform_info.joblib` file that contains information about the transformations applied during training. This information is used during prediction to correctly apply the inverse transformations.

## Conclusion

Our experiments with specialized models show excellent results:

1. **Temperature Prediction**: The specialized DNN model achieves excellent performance (R² = 0.9421), which is significantly better than the multi-task model (R² = 0.8756).

2. **Growth Rate Prediction**: The specialized hybrid model achieves outstanding performance (R² = 0.9432), which is significantly better than the multi-task model (R² = 0.8592).

3. **Overall Performance**: The specialized models achieve an overall R² of 0.9426, which is significantly better than the multi-task model (R² = 0.8674).

The specialized approach demonstrates that using dedicated models for each prediction target provides superior performance compared to multi-task models. This approach allows us to:

1. Leverage the strengths of different model architectures for different prediction tasks
2. Apply specific transformations and optimizations for each target
3. Achieve state-of-the-art performance for both growth rate and temperature prediction

We recommend using:
- The specialized DNN model for temperature prediction (R² = 0.9421)
- The specialized hybrid model for growth rate prediction (R² = 0.9432)
