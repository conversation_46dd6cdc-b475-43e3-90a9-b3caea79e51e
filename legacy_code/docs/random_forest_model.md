# Random Forest Model for Growth Rate Prediction

This document describes the Random Forest model implemented in DeepMu for microbial growth rate prediction.

## Overview

The Random Forest model is a powerful ensemble learning method that:

1. **Builds multiple decision trees** during training
2. **Outputs the mean prediction** of individual trees
3. **Handles tabular data effectively** without extensive preprocessing
4. **Provides feature importance scores** for interpretability
5. **Is robust to overfitting and outliers**

This approach leverages the strengths of Random Forest for tabular data and regression tasks like growth rate prediction.

![Random Forest Architecture](../assets/random_forest_model.txt)

## Key Components

### Random Forest Architecture

The Random Forest model consists of the following components:

- **Decision Trees**: Multiple decision trees trained on bootstrap samples of the data
- **Feature Sampling**: Random subset of features considered at each split
- **Ensemble Prediction**: Average of predictions from all trees
- **Feature Importance**: Measure of feature contribution to prediction accuracy

### Hyperparameters

The Random Forest model has several key hyperparameters:

- **n_estimators**: Number of trees in the forest (default: 200)
- **max_depth**: Maximum depth of trees (default: 20)
- **min_samples_split**: Minimum samples required to split a node (default: 2)
- **min_samples_leaf**: Minimum samples required at a leaf node (default: 1)

## Advantages of Random Forest

The Random Forest model offers several advantages for growth rate prediction:

1. **Robustness**: Resistant to overfitting and handles noisy data well
2. **Feature Importance**: Provides insights into which features are most predictive
3. **No Scaling Required**: Works well with features on different scales without normalization
4. **Handles Non-linearity**: Captures complex non-linear relationships between features
5. **Efficiency**: Faster to train than deep learning models and can be parallelized
6. **Few Hyperparameters**: Relatively easy to tune compared to deep learning models

## Performance Comparison

| Metric | Multi-Branch Advanced | Transformer | Combined DNN-Transformer | Hybrid RF-Transformer | Random Forest |
|--------|----------------------|-------------|--------------------------|------------------------|----------------|
| Growth Rate R² | 0.182 | 0.259 | 0.714 | 0.773 | 0.293 |
| Growth Rate RMSE | 3.78 | 3.82 | 2.06 | 2.12 | 3.39 |
| Temperature R² | 0.772 | 0.875 | 0.744 | -0.021 | - |
| Temperature RMSE | 6.80 | 5.97 | 7.80 | 19.07 | - |

The Random Forest model provides a good baseline for growth rate prediction, but the hybrid RF-Transformer model achieves better performance. For temperature prediction, the standalone Transformer model remains the best option.

## Feature Types

The model supports the following feature types:

1. **Codon Features**: Codon usage bias, codon pair bias, CAI, etc.
2. **Amino Acid Features**: Amino acid composition, physicochemical properties, etc.
3. **Genomic Features**: Genome size, GC content, dinucleotide frequencies, etc.
4. **RNA Features**: tRNA and rRNA counts, tAI, etc.
5. **Protein pI Features**: Isoelectric point features
6. **Taxonomy Features**: Taxonomic classification features

## Training Process

The Random Forest model is trained using scikit-learn's RandomForestRegressor:

1. **Data Preparation**:
   - Features are extracted from genomic data
   - Features are categorized by type (codon, amino acid, genomic, etc.)
   - Data is split into training and testing sets

2. **Model Training**:
   - Random Forest is trained on the concatenated features
   - Bootstrap samples are created for each tree
   - Random feature subsets are considered at each split

3. **Evaluation**:
   - Model is evaluated on the test set
   - Performance is measured using R² and RMSE metrics
   - Feature importance is calculated

## Uncertainty Estimation

The Random Forest model provides built-in uncertainty estimation:

1. **Variance Across Trees**: The variance of predictions across all trees in the ensemble
2. **Prediction Intervals**: Can be calculated based on the distribution of predictions
3. **Out-of-Bag Error**: Estimated using samples not included in bootstrap training sets

## Usage

### Evaluation

To evaluate the Random Forest model for growth rate prediction, use the `evaluate_rf_model.py` script:

```bash
python evaluate_rf_model.py \
    --feature-file features_all/combined_features.tsv \
    --metadata training_data/metadata.tsv \
    --rf-model models/hybrid_rf_transformer/rf_model.joblib \
    --output-dir models/rf_evaluation
```

### Prediction

To make predictions using the Random Forest model for growth rate prediction, use the `predict_ensemble_simple.py` script:

```bash
python predict_ensemble_simple.py \
    --feature-file features_all/combined_features.tsv \
    --rf-model models/hybrid_rf_transformer/rf_model.joblib \
    --output-file predictions/rf_predictions.tsv
```

## Implementation Details

The implementation is available in the following files:

- `deepmu/models/hybrid_rf_transformer.py`: Implementation of the hybrid RF-Transformer model
- `train_hybrid_rf_transformer.py`: Training script for the hybrid model
- `evaluate_rf_model.py`: Evaluation script for the Random Forest model
- `predict_ensemble_simple.py`: Prediction script using the Random Forest model

## References

1. Breiman, L. (2001). Random forests. *Machine learning*, 45(1), 5-32.
2. Vaswani, A., et al. (2017). Attention is all you need. *Advances in neural information processing systems*.
