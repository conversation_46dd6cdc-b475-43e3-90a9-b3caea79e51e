# DeepMu Developer Guide

This guide provides detailed information for developers who want to understand, modify, or extend the DeepMu codebase.

## Table of Contents

- [Architecture Overview](#architecture-overview)
- [Core Components](#core-components)
- [Model Architecture](#model-architecture)
- [Feature Calculation](#feature-calculation)
- [Feature Engineering](#feature-engineering)
- [Preprocessing Pipeline](#preprocessing-pipeline)
- [<PERSON>rro<PERSON>](#error-handling)
- [Known Issues and Fixes](#known-issues-and-fixes)
- [Adding New Features](#adding-new-features)
- [Testing](#testing)
- [Performance Optimization](#performance-optimization)

## Architecture Overview

DeepMu follows a modular architecture with clear separation of concerns:

```
deepmu/
├── models/           # Neural network models
├── features/         # Feature calculation
├── predictors/       # Prediction interfaces
├── preprocessing/    # Data preprocessing
├── utils/            # Utility functions
└── cli/              # Command-line interface
```

The system follows a pipeline architecture:

1. **Input Processing**: Parse and validate input files (FASTA, KO annotations, etc.)
2. **Preprocessing**: Gene prediction and KO annotation (if needed)
3. **Feature Calculation**: Extract codon usage, sequence, and phylogenetic features
4. **Model Prediction**: Feed features to the neural network model
5. **Result Processing**: Format and return prediction results

## Core Components

### MicrobialGrowthPredictor

The `MicrobialGrowthPredictor` class serves as the main interface for the system. It orchestrates the entire prediction pipeline, from input validation to result formatting.

Key responsibilities:
- Loading and validating input data
- Coordinating preprocessing (if needed)
- Managing feature calculation
- Running the neural network model
- Formatting and returning results

Implementation details:
- Located in `deepmu/predictors/predictor.py`
- Provides methods for single organism, community, and batch predictions
- Handles error conditions and provides detailed error messages

### EnhancedPhyloGrowthModel

The main neural network model implementing a multi-branch architecture:

```python
class EnhancedPhyloGrowthModel(nn.Module):
    def __init__(self,
                 phylo_vocab_size=10000,
                 phylo_embed_dim=128,
                 # ... other parameters ...
                ):
        super().__init__()
        # Initialize branches
        self.codon_branch = self._build_codon_branch()
        self.phylo_branch = self._build_phylo_branch()
        self.seq_branch = self._build_seq_branch()
        if use_pathway_branch:
            self.pathway_branch = self._build_pathway_branch()

        # Feature fusion
        self.fusion = self._build_fusion_layers()
```

Key Components:
- Codon metrics processing
- Phylogenetic feature extraction (standard or hierarchical)
- Sequence processing with attention
- Optional pathway integration
- Feature fusion with interaction layer
- Multi-task prediction heads

Implementation details:
- Located in `deepmu/models/networks.py`
- Uses PyTorch for neural network implementation
- Implements a forward pass that handles multiple input types

### CodonFeatureCalculator

The `CodonFeatureCalculator` class extracts codon usage features from DNA sequences.

Key features:
- Calculation of codon usage bias (CUB)
- Codon pair bias (CPB) analysis
- Codon consistency metrics
- Remote codon interaction analysis
- HEG/background differentiation

Implementation details:
- Located in `deepmu/features/codon_features.py`
- Uses efficient algorithms for feature calculation
- Supports both single sequences and sequence collections

## Model Architecture

DeepMu uses a dual-target neural network architecture with separate feature processing streams:

```python
class ImprovedPhyloGrowthModel(EnhancedPhyloGrowthModel):
    def __init__(self, 
                 # Existing parameters
                 enable_dual_target=True,
                 growth_rate_features=30,
                 temperature_features=30,
                 correlation_method='pearson',
                 **kwargs):
        super().__init__(**kwargs)
        
        # Dual target specific components
        if enable_dual_target:
            self.growth_rate_head = nn.Linear(self.hidden_dim, 1)
            self.temperature_head = nn.Linear(self.hidden_dim, 1)
            
            # Feature selectors
            self.growth_feature_selector = CorrelationFeatureSelector(
                num_features=growth_rate_features,
                correlation_method=correlation_method
            )
            self.temp_feature_selector = CorrelationFeatureSelector(
                num_features=temperature_features,
                correlation_method=correlation_method
            )
```

Key Dual-Target Components:
1. **Separate Prediction Heads**: Specialized output layers for growth rate and temperature
2. **Feature Selectors**: Independent feature selection for each target
3. **Modified Loss Function**: Combined loss with adjustable weights
4. **Pathway Cluster Integration**: Uses clustered KO features from pathway analysis

### Training Parameters

The dual-target training process uses several key parameters from the training script:

```bash
# From run_dual_target_training.sh
BATCH_SIZE=16
EPOCHS=50
LEARNING_RATE=0.0005
GROWTH_RATE_FEATURES=30
OPTIMAL_TEMPERATURE_FEATURES=30
CORRELATION_METHOD="pearson"
HIDDEN_DIM=128
DROPOUT=0.3
```

Implementation details:
- Located in `train_dual_target_model.py`
- Uses correlation-based feature selection (`pearson`/`spearman`)
- Implements sample limiting for feature selection stability
- Generates feature overlap visualizations using matplotlib-venn

```
                                  ┌─────────────┐
                                  │   Output    │
                                  │  (Growth,   │
                                  │Temperature) │
                                  └──────┬──────┘
                                         │
                                  ┌──────┴──────┐
                                  │   Fusion    │
                                  │    Layer    │
                                  └─┬────┬────┬─┘
                                    │    │    │
             ┌────────────────────┐ │    │    │ ┌────────────────────┐
             │  Codon Branch      │◄┘    │    └►│  Sequence Branch    │
             └────────────────────┘      │      └────────────────────┘
                                         │
                                  ┌──────┴──────┐
                                  │ Phylogenetic │
                                  │    Branch    │
                                  └──────┬──────┘
                                         │
                          ┌──────────────┴──────────────┐
                          │                             │
                   ┌──────┴──────┐               ┌──────┴──────┐
                   │  Taxonomy   │               │     KO      │
                   │  Embedder   │               │ Similarity  │
                   └──────┬──────┘               └──────┬──────┘
                          │                             │
                   ┌──────┴──────┐               ┌──────┴──────┐
                   │ Hierarchical│               │  Functional │
                   │  Taxonomy   │               │  Similarity │
                   └─────────────┘               └─────────────┘
```

### ImprovedPhyloGrowthModel: Dual-Target Architecture

The `ImprovedPhyloGrowthModel` extends the base architecture with several enhancements:

- **Hierarchical Feature Fusion**: Combines features at multiple levels of abstraction
- **Enhanced Pathway Grouping**: Groups KO terms by pathway for better functional representation
- **Uncertainty Estimation**: Optional uncertainty quantification for predictions

### FeatureSelectedGrowthModel: Simplified Architecture

The `FeatureSelectedGrowthModel` takes a different approach by focusing on the most important features:

- **Feature Selection**: Uses only the most predictive features identified through feature importance analysis
- **Simplified Architecture**: Direct processing of important features without complex fusion mechanisms
- **Reduced Complexity**: Fewer parameters and simpler training process

```
                      ┌─────────────┐
                      │   Output    │
                      │  (Growth,   │
                      │Temperature) │
                      └──────┬──────┘
                             │
                      ┌──────┴──────┐
                      │    MLP      │
                      │   Layers    │
                      └──────┬──────┘
                             │
                      ┌──────┴──────┐
                      │  Important  │
                      │   Features  │
                      └─────────────┘
```

This model is particularly useful when:
- The dataset is small
- Feature importance analysis reveals a few highly predictive features
- Model performance plateaus with more complex architectures

The latest DeepMu version introduces the `ImprovedPhyloGrowthModel` class, which extends the base architecture with several advanced features:

1. **Hierarchical Feature Fusion**: Multi-stage feature integration with cross-attention
2. **Enhanced Pathway Processing**: Pathway-specific processing with biological grouping
3. **Uncertainty Estimation**: Calibrated confidence intervals for predictions
4. **Improved Interpretability**: Dynamic feature importance tracking

#### Hierarchical Feature Fusion

The hierarchical fusion approach replaces the simple concatenation-based fusion with a sophisticated multi-stage process:

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│  Input Features │     │  Pairwise       │     │  Final          │
│  - Codon        │────►│  Cross-Attention│────►│  Integration    │
│  - Pathway      │     │  Between        │     │  with Feature   │
│  - Phylogenetic │     │  Feature Types  │     │  Importance     │
└─────────────────┘     └─────────────────┘     └─────────────────┘
```

**Stage 1**: Pairwise Cross-Attention
- Each feature type attends to every other feature type
- Captures specialized interactions between feature pairs
- Enriches features with cross-modal information

**Stage 2**: Mid-level Feature Importance Attention
- Features are weighted according to their importance
- Dynamic importance tracking for interpretability
- Adaptive feature integration

**Stage 3**: Final Integration
- Combines enriched features through residual blocks
- Maintains gradient flow for stable training
- Produces a unified feature representation

#### Enhanced Pathway Module

The `EnhancedPathwayModule` treats pathway groups as independent features for improved biological interpretability:

- Groups pathways by biological function (central metabolism, energy, etc.)
- Each pathway group is processed separately
- Cross-attention between pathway groups
- Importance tracking for each pathway group

#### Implementation

```python
class ImprovedPhyloGrowthModel(EnhancedPhyloGrowthModel):
    def __init__(self,
                 codon_input_dim,
                 pathway_input_dim=0,
                 heg_input_dim=0,
                 seq_input_dim=0,
                 taxonomy_vocab_sizes=None,
                 hidden_dim=256,
                 output_dim=2,
                 dropout=0.2,
                 use_pathway=False,
                 use_heg=False,
                 use_hierarchical_phylo=True,
                 use_seq=False,
                 enable_hierarchical_fusion=True,
                 enable_enhanced_pathways=True,
                 uncertainty_calibration=True):
        # Initialize base model
        super().__init__(...)

        # Replace fusion with hierarchical fusion
        if enable_hierarchical_fusion:
            self.feature_fusion = HierarchicalFeatureFusion(...)

        # Replace pathway branch with enhanced pathway module
        if use_pathway and enable_enhanced_pathways:
            self.pathway_branch = EnhancedPathwayModule(...)
```

### Codon Branch

The codon branch processes codon usage features:

```python
def _build_codon_branch(self):
    return nn.Sequential(
        nn.Linear(self.codon_input_dim, self.hidden_dim),
        nn.ReLU(),
        nn.LayerNorm(self.hidden_dim),
        nn.Dropout(self.dropout),
        nn.Linear(self.hidden_dim, self.hidden_dim),
        nn.ReLU(),
        nn.LayerNorm(self.hidden_dim)
    )
```

The codon branch processes metrics including:
- Codon Usage Bias (CUB)
- Codon Pair Bias (CPB)
- Codon Consistency
- Remote Interaction
- Advanced features like CAI, ENC, and GC content (when enabled)
- HEG/background metrics (when HEG features are enabled)

### Hierarchical Phylogenetic Branch

The enhanced phylogenetic branch integrates taxonomic and functional information:

```python
class HierarchicalPhyloBranch(nn.Module):
    def __init__(self, taxonomy_vocab_sizes, ko_dim, output_dim, hidden_dim):
        # Taxonomy GNN with levels (phylum, class, order, family, genus)
        self.tax_embedder = TaxonomicEmbedder(taxonomy_vocab_sizes, hidden_dim)

        # KO similarity processing
        self.ko_projector = nn.Linear(ko_dim, hidden_dim)

        # Cross-attention between taxonomy and KO features
        self.cross_attention = CrossModalAttention(hidden_dim, hidden_dim)

        # Final integration
        self.integration = nn.Sequential(...)
```

The branch consists of:
1. **Taxonomic Embedder**: Processes hierarchical taxonomy (phylum to genus)
2. **KO Similarity Calculator**: Processes KO profile similarity
3. **Cross-Modal Attention**: Captures interactions between taxonomy and KO profiles
4. **Feature Integration**: Combines taxonomy and KO information

### Model Usage

To use the improved model architecture:

```python
from deepmu.models import ImprovedPhyloGrowthModel

model = ImprovedPhyloGrowthModel(
    codon_input_dim=16,
    pathway_input_dim=300,
    taxonomy_vocab_sizes=[100, 200, 300, 400, 500],  # [phylum, class, order, family, genus]
    hidden_dim=256,
    enable_hierarchical_fusion=True,
    enable_enhanced_pathways=True
)
```

Key parameters:
- `enable_hierarchical_fusion`: Enables the hierarchical feature fusion
- `enable_enhanced_pathways`: Activates the enhanced pathway module
- `uncertainty_calibration`: Enables uncertainty estimation

## Feature Calculation

### Codon Usage Features

The system calculates several codon usage metrics:

1. **Codon Usage Bias (CUB)**:
   - Measures the non-uniformity of synonymous codon usage
   - Calculated using relative synonymous codon usage (RSCU)
   - Implementation in `_calculate_cub` method

2. **Codon Pair Bias (CPB)**:
   - Quantifies preferences for specific adjacent codon pairs
   - Calculated as the log ratio of observed to expected frequencies
   - Implementation in `_calculate_cpb` method

3. **Codon Consistency**:
   - Measures consistent usage of preferred codons
   - Calculated using Jensen-Shannon divergence
   - Implementation in `_calculate_consistency` method

4. **Remote Codon Interaction**:
   - Quantifies non-random patterns in codon arrangement
   - Uses a sliding window approach
   - Implementation in `_calculate_remote_interaction` method

### Feature Calculation Methods

The system provides multiple ways to calculate features:

1. **From Sequence String**:
   - Directly calculates features from a DNA sequence string
   - Implementation in `calculate_features` method
   - Validates sequence integrity before calculation

2. **From FASTA File**:
   - Reads sequences from a FASTA file and calculates features
   - Implementation in `calculate_features_from_file` method
   - Handles errors and returns zero features if the file can't be read

3. **With HEG Differentiation**:
   - Calculates separate metrics for HEGs and background genes
   - Implementation in `calculate_features_with_heg` method
   - Requires KO annotations to identify HEGs

### HEG/Background Differentiation

The system differentiates between highly expressed genes (HEGs) and background genes:

1. **HEG Identification**:
   - Uses a predefined list of KO IDs associated with HEGs
   - Typically includes ribosomal proteins, RNA polymerase, etc.
   - Implementation in `split_heg_bg_sequences` method

2. **Delta Metrics**:
   - Calculates the difference between HEG and background metrics
   - Provides insight into translational optimization
   - Implementation in `calculate_features_with_heg` method

### KO Similarity Features

The system calculates functional similarity based on KO profiles:

1. **KO Profile Creation**:
   - Parses KO annotations from files
   - Creates a set of KO IDs for each organism
   - Implementation in `parse_ko_file` method

2. **Similarity Calculation**:
   - Uses weighted Jaccard similarity
   - Accounts for KO importance and frequency
   - Implementation in `jaccard_similarity` method

3. **Graph Construction**:
   - Builds a similarity graph based on KO profiles
   - Integrates with taxonomic information
   - Implementation in `build_similarity_graph` method

## Feature Engineering

DeepMu includes enhanced feature engineering capabilities with dual-target optimization:

### Correlation-Based Feature Selection

The dual-target training implements correlation-based feature selection using parameters from `run_dual_target_training.sh`:

```python
class CorrelationFeatureSelector:
    def __init__(self, num_features=30, correlation_method='pearson'):
        self.selector = SelectKBest(
            f_regression if correlation_method == 'f' else lambda X,y: np.abs([pearsonr(X[:,i],y)[0] for i in range(X.shape[1])]),
            k=num_features
        )

    def transform(self, features, target):
        return self.selector.fit_transform(features, target)
```

Key aspects from training script:
- Separate feature selection for growth rate (--growth-rate-features) and temperature (--optimal-temperature-features)
- Correlation methods (pearson/spearman/f_regression)
- Feature overlap visualization using matplotlib-venn

### Dual-Target Parameters

Implementation details from `train_dual_target_model.py`:
1. **Sample Limiting**: Reduces feature selection samples to 50 for stability
2. **Weighted Loss**: Combines MSE losses with adjustable weights
3. **Checkpointing**: Saves best model based on combined validation loss

```bash
# From run_dual_target_training.sh
BATCH_SIZE=16
EPOCHS=50
LEARNING_RATE=0.0005
HIDDEN_DIM=128
DROPOUT=0.3
```

DeepMu includes enhanced feature engineering capabilities focusing on KO similarity calculation and feature selection.

### Weighted Jaccard Similarity

The standard Jaccard similarity index treats all KO terms equally, which may not be optimal since some KO terms are more important for growth rate prediction than others. We've implemented a weighted Jaccard similarity that gives higher importance to essential and functionally significant KO terms.

#### Implementation

The weighted Jaccard similarity is calculated as:

```
weighted_jaccard(A, B) = sum(weight(x) for x in A ∩ B) / sum(weight(x) for x in A ∪ B)
```

Where:
- A and B are sets of KO terms
- weight(x) is the importance weight of KO term x

#### KO Weights

KO weights are assigned based on:

1. **Feature importance scores**: Calculated using statistical correlation with growth rate
2. **Functional categories**: Different categories of KOs have different default weights:
   - Essential genes: 2.0
   - Metabolism-related: 1.5
   - Information processing: 1.3
   - Others: 1.0

### Feature Selection

DeepMu implements feature selection to identify the most important KO terms for growth rate prediction. This helps to:

1. Reduce dimensionality and prevent overfitting
2. Improve model interpretability
3. Focus on the most relevant biological functions

#### Implementation

Feature selection is performed using the SelectKBest algorithm with f_regression as the scoring function. This selects the KO terms that have the highest correlation with growth rate.

```python
from sklearn.feature_selection import SelectKBest, f_regression

# Convert KO sets to sparse matrix
X = sparse_matrix.toarray()

# Apply feature selection
selector = SelectKBest(f_regression, k=100)
selector.fit(X, targets)

# Get selected feature indices
selected_indices = selector.get_support(indices=True)
```

#### Additional Features

Based on feature selection, we calculate additional features:

1. **KO importance score**: Average importance of KO terms in a genome
2. **Important KO coverage**: Proportion of important KO terms present in a genome

### Advanced Feature Extraction

The feature extraction functionality is integrated directly into the DeepMu codebase as a module and can be run with the following command:

```bash
python -m deepmu.features \
  --cds-dir data/cds \
  --ko-dir data/ko \
  --metadata-file data/metadata.tsv \
  --taxonomy-dir data/taxonomy \
  --heg-ko-list data/heg_ko_list.txt \
  --output-dir features/enhanced \
  --use-weighted-similarity \
  --select-important-features \
  --advanced-codon-features \
  --scan-directory \
  --num-processes 4 \
  --output-format tsv
```

Key options:
- `--scan-directory`: Automatically scan directory for CDS files
- `--genome-list`: Alternatively, provide a file with genome IDs to process
- `--advanced-codon-features`: Calculate additional codon features (CAI, ENC, GC content)
- `--output-format`: Choose between individual files (npz) or combined output (tsv)

### Benefits of Enhanced Feature Engineering

The enhanced feature engineering provides several benefits:

1. **Improved accuracy**: By focusing on the most relevant KO terms
2. **Better generalization**: By reducing noise from less important features
3. **Biological interpretability**: By identifying the most important functional categories
4. **Computational efficiency**: By reducing the dimensionality of the feature space

## Co-occurrence-based KO Grouping

The co-occurrence-based KO grouping approach is a powerful feature engineering technique that leverages the co-occurrence patterns of KO terms across genomes to create more robust functional features.

### Implementation Overview

The implementation of co-occurrence-based KO grouping is primarily in the `deepmu/features/ko_cooccurrence.py` module, which defines the `KOCooccurrenceCalculator` class and related functions.

#### Key Components:

1. **KOCooccurrenceCalculator**: The main class for calculating co-occurrence patterns and clustering KO terms
   ```python
   class KOCooccurrenceCalculator:
       def __init__(self, ko_dir, n_clusters=50, min_occurrence=5, distance_metric='jaccard'):
           # Initialize data structures
           self.ko_presence = defaultdict(set)  # KO -> set of genomes
           self.genome_kos = defaultdict(set)   # Genome -> set of KOs
           self.ko_clusters = {}                # KO -> cluster ID
           self.cluster_kos = defaultdict(set)  # Cluster ID -> set of KOs
           
           # Load KO data
           self._load_ko_data()
           
           # Filter KOs by occurrence
           self._filter_kos()
           
           # Create KO co-occurrence matrix
           self.ko_matrix, self.ko_list = self._create_ko_matrix()
           
           # Cluster KOs
           self._cluster_kos()
   ```

2. **Feature Calculation Functions**: Methods to calculate KO group features for genomes
   ```python
   def calculate_ko_group_features(self, ko_terms):
       """Calculate KO group features for a set of KO terms."""
       # Convert KO terms to a set for faster lookups
       ko_set = set(ko_terms)
       
       # Calculate completeness for each cluster
       cluster_features = {}
       for cluster_id, cluster_kos in self.cluster_kos.items():
           if cluster_kos:
               # Calculate completeness as proportion of KO terms present
               completeness = len(ko_set.intersection(cluster_kos)) / len(cluster_kos)
               cluster_features[f"ko_cluster_{cluster_id}"] = completeness
               
       return cluster_features
   ```

3. **Utility Functions**: Helper functions for dataset processing
   ```python
   def generate_ko_cooccurrence_features(ko_dir, output_dir, n_clusters=50, min_occurrence=5, distance_metric='jaccard'):
       """Generate co-occurrence-based KO group features for all genomes in a dataset."""
       # Initialize KO co-occurrence calculator
       calculator = KOCooccurrenceCalculator(
           ko_dir=ko_dir,
           n_clusters=n_clusters,
           min_occurrence=min_occurrence,
           distance_metric=distance_metric
       )
       
       # Process all KO files
       ko_files = list(Path(ko_dir).glob("*.tsv"))
       for ko_file in ko_files:
           # Calculate features for this genome
           features = calculator.calculate_ko_group_features_from_file(ko_file)
           
           # Integrate with existing features
           genome_id = ko_file.stem.split('_ko')[0]
           feature_file = Path(output_dir) / f"{genome_id}_cds_features.npz"
           
           # Update feature file
           if feature_file.exists():
               existing_features = dict(np.load(feature_file))
               existing_features.update(features)
               np.savez(feature_file, **existing_features)
           else:
               np.savez(feature_file, **features)
   ```

### Integration with DeepMu

To integrate co-occurrence-based KO grouping into the DeepMu workflow:

1. **Preprocess KO Data**: Generate KO annotation files for all genomes in the dataset
   ```bash
   ./run_preprocessing.sh
   ```

2. **Generate KO Co-occurrence Features**: Run the co-occurrence feature generation script
   ```bash
   ./run_ko_cooccurrence_features.sh
   ```

3. **Train Model with Co-occurrence Features**: Use the generated features for model training
   ```bash
   ./run_dual_target_training.sh --cooccurrence-features
   ```

### Customization

The co-occurrence-based KO grouping approach can be customized by modifying the following parameters:

- **n_clusters**: Number of KO clusters to create (default: 50)
- **min_occurrence**: Minimum number of genomes a KO must appear in to be included (default: 5)
- **distance_metric**: Distance metric for clustering ('jaccard', 'cosine', etc.) (default: 'jaccard')

These parameters can be modified in the `run_ko_cooccurrence_features.sh` script:

```bash
# Set parameters
N_CLUSTERS=50
MIN_OCCURRENCE=5
DISTANCE_METRIC="jaccard"
```

## Enhanced Codon Adaptation Features

Enhanced codon adaptation features build upon standard codon usage metrics, providing more sophisticated measures of codon optimization that better correlate with growth rate.

### Implementation Overview

The implementation of enhanced codon adaptation features is primarily in the `deepmu/features/ko_cooccurrence.py` module (in the `enhance_codon_adaptation_features` function):

```python
def enhance_codon_adaptation_features(feature_dir, output_dir=None):
    """
    Enhance codon adaptation features to better capture growth rate signal.
    
    Args:
        feature_dir: Directory containing feature files
        output_dir: Output directory for enhanced features (default: feature_dir)
    """
    # Default output directory to feature directory if not specified
    if output_dir is None:
        output_dir = feature_dir
        
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Get all feature files
    feature_files = list(Path(feature_dir).glob("*_cds_features.npz"))
    logger.info(f"Found {len(feature_files)} feature files")
    
    # Process each feature file
    for feature_file in feature_files:
        # Load features
        features = dict(np.load(feature_file))
        
        # Extract standard codon features
        codon_features = {k: v for k, v in features.items() if k.startswith('codon_') or k == 'gc_content'}
        
        # Create enhanced features
        enhanced_features = {}
        
        # Enhanced metrics
        if 'codon_cai' in codon_features and 'codon_cai_heg' in codon_features:
            # Enhanced CAI (eCAI)
            enhanced_features['enhanced_cai'] = (
                codon_features['codon_cai_heg'] * 2 - codon_features['codon_cai']
            )
            
            # Codon Adaptation Contrast (CAC)
            enhanced_features['enhanced_cac'] = (
                (codon_features['codon_cai_heg'] - codon_features['codon_cai']) /
                (codon_features['codon_cai_heg'] + codon_features['codon_cai'] + 1e-10)
            )
        
        # NOTE: RSCU_HEG and RSCU_BP have been removed and replaced with ENC_HEG and ENC_BP
        # which are more biologically meaningful metrics of codon usage bias
        if 'codon_rscu_heg' in codon_features and 'codon_rscu_bg' in codon_features:
            codon_features['combined_rscu'] = \
            codon_features['ENC_HEG'] * 0.7 + codon_features['ENC_BP'] * 0.3
        
        # ... other enhanced metrics ...
        
        # Update feature file with enhanced features
        features.update(enhanced_features)
        np.savez(feature_file, **features)
        
        logger.info(f"Enhanced features for {feature_file.stem}")
    
    logger.info("Codon adaptation feature enhancement complete!")
```

### Key Enhanced Metrics

The function creates several enhanced codon adaptation metrics:

1. **Enhanced CAI (eCAI)**: An improved version of the Codon Adaptation Index that emphasizes HEG bias
   ```python
   enhanced_features['enhanced_cai'] = (
       codon_features['codon_cai_heg'] * 2 - codon_features['codon_cai']
   )
   ```

2. **Codon Adaptation Contrast (CAC)**: The normalized difference between HEG and background CAI
   ```python
   enhanced_features['enhanced_cac'] = (
       (codon_features['codon_cai_heg'] - codon_features['codon_cai']) /
       (codon_features['codon_cai_heg'] + codon_features['codon_cai'] + 1e-10)
   )
   ```

3. **Context-Weighted CAI (cwCAI)**: CAI weighted by codon context sensitivity
   ```python
   if 'codon_cai' in codon_features and 'codon_cpb' in codon_features:
       enhanced_features['enhanced_cwcai'] = (
           codon_features['codon_cai'] * (1 + codon_features['codon_cpb'])
       )
   ```

4. **Adaptive Weighted RSCU (awRSCU)**: A weighted RSCU metric that emphasizes growth-rate-relevant codons
   ```python
   if 'codon_rscu_heg' in codon_features and 'codon_rscu_bg' in codon_features:
       enhanced_features['enhanced_awrscu'] = (
           codon_features['codon_rscu_heg'] * 0.7 + codon_features['codon_rscu_bg'] * 0.3
       )
   ```

### Integration with DeepMu

To integrate enhanced codon adaptation features into the DeepMu workflow:

1. **Preprocess Genome Data**: Generate codon usage metrics for all genomes
   ```bash
   ./run_preprocessing.sh
   ```

2. **Generate Enhanced Codon Adaptation Features**: Run the feature enhancement script
   ```bash
   ./run_ko_cooccurrence_features.sh  # Also enhances codon features
   ```

3. **Train Model with Enhanced Features**: Use the enhanced features for model training
   ```bash
   ./run_dual_target_training.sh --cooccurrence-features
   ```

### Customization

The enhanced codon adaptation features can be customized by modifying the enhancement algorithm in the `enhance_codon_adaptation_features` function. For example:

- **Adjust the weighting**: Change the weighting factors for feature combinations
- **Add new metrics**: Implement additional metrics based on codon usage patterns
- **Customize normalization**: Modify how features are normalized or combined

## Error Handling

The system uses a comprehensive error handling architecture:

### Exception Hierarchy

```
DeepMuError (base exception)
├── InputValidationError
├── ModelLoadError
├── FeatureCalculationError
├── SequenceProcessingError
├── PreprocessingError
│   ├── GenePredictionError
│   └── KOAnnotationError
├── PathwayProcessingError
└── PredictionError
```

### Error Categories

1. **InputValidationError**: Issues with input data format, missing files, etc.
2. **ModelLoadError**: Problems loading the pre-trained model or its weights
3. **FeatureCalculationError**: Failures during the computation of codon usage features
4. **SequenceProcessingError**: Problems with sequence reading or validation
5. **PreprocessingError**: Generic preprocessing failures
   - **GenePredictionError**: Failures in Prodigal gene prediction
   - **KOAnnotationError**: Failures in KofamScan annotation
6. **PathwayProcessingError**: Issues with KEGG pathway processing
7. **PredictionError**: Runtime errors during model prediction

### Implementation

```python
class DeepMuError(Exception):
    """Base exception for all DeepMu errors."""
    pass

class InputValidationError(DeepMuError):
    """Raised when input validation fails."""
    pass

class ModelLoadError(DeepMuError):
    """Raised when the model cannot be loaded."""
    pass

class FeatureCalculationError(DeepMuError):
    """Raised when there is an error calculating features."""
    pass

class SequenceProcessingError(DeepMuError):
    """Raised when there is an error processing sequences."""
    pass

class PreprocessingError(DeepMuError):
    """Raised when there is an error in the preprocessing pipeline."""
    pass

class GenePredictionError(PreprocessingError):
    """Raised when gene prediction fails."""
    pass

class KOAnnotationError(PreprocessingError):
    """Raised when KO annotation fails."""
    pass

class PathwayProcessingError(DeepMuError):
    """Raised when there is an error processing KEGG pathway data."""
    pass

class PredictionError(DeepMuError):
    """Raised when the prediction fails."""
    pass
```

### Error Handling in Action

```python
try:
    # Validate inputs
    if not os.path.exists(fasta_path):
        raise InputValidationError(f"FASTA file not found: {fasta_path}")

    # Load sequences
    try:
        sequences = self._load_sequences(fasta_path)
    except Exception as e:
        raise SequenceProcessingError(f"Failed to load sequences: {e}")

    # Calculate features
    try:
        features = self.calculate_features(sequences, ko_file, ko_lookup,
                                         heg_ko_list, taxonomy_string)
    except Exception as e:
        raise FeatureCalculationError(f"Failed to calculate features: {e}")

    # Make predictions
    try:
        results = self._predict(features, temperature, predict_temp)
        return results
    except Exception as e:
        raise PredictionError(f"Prediction failed: {e}")

except DeepMuError:
    # Re-raise DeepMu-specific errors
    raise
except Exception as e:
    # Catch-all for unexpected errors
    raise DeepMuError(f"Unexpected error: {e}")
```

## Known Issues and Fixes

This section documents known issues and fixes implemented in DeepMu, particularly in handling taxonomy features and other technical challenges.

### Taxonomy Handling Fixes

#### Summary of Issues

1. The training loop was encountering a `RuntimeError` with a string key in tensor when trying to process taxonomy data
2. The hierarchical GNN implementation had issues handling very small batch sizes
3. There were dimension mismatches when processing tensors without checking shapes
4. The KO data format processing needed to be standardized for proper input to the model

#### Fixes Implemented

##### 1. Taxonomy Feature Processing

- Modified the `adapt_taxonomy_features` function to properly convert taxonomy tensors into a dictionary format
- Added error handling for empty taxonomy tensors
- Updated how taxonomy features are passed to the model to avoid the "string key in tensor" error

##### 2. KO Feature Processing

- Improved the `adapt_ko_features` function to convert tensor-based KO features to a list of sets format
- Added handling for both KO sets and similarity matrices
- Fixed the dictionary structure to match what the model expects

##### 3. Hierarchical GNN Improvements

- Added robust error handling for the `_build_similarity_graph` method to handle small batch sizes
- Fixed the logic for creating graph edges and edge attributes
- Added fallback mechanisms for when not enough nodes are available for a proper k-NN graph

##### 4. Training Loop Robustness

- Added checks for missing `phylo_id` input and automatic mock creation
- Improved error handling in the forward pass to catch and report issues
- Added safety checks for division by zero in validation loss calculation

##### 5. Model Creation

- Added dimension safety checks with the `get_tensor_dim` helper function
- Made taxonomy vocabulary size handling more robust
- Added graceful handling of tensor shapes to avoid index errors

#### Testing the Fixes

These fixes were validated with:

1. A dedicated test script (`test_hierarchical.py`) that tests the core functionality
2. An end-to-end test of the training loop (`test_training_script.py`)
3. Manual review and validation of the tensor shapes and processing steps

### Additional Improvements

- Added informative logging for debugging tensor shapes and model inputs
- Improved error messages for better debugging
- Standardized the handling of dictionary-based inputs across the codebase

## Adding New Features

### Adding a New Feature Calculator

To add a new feature calculator:

1. Create a new module in the `features` package
2. Implement a calculator class with a `calculate_features` method
3. Update the `MicrobialGrowthPredictor` class to use the new calculator
4. Update the model architecture to incorporate the new features

Example:
```python
# In deepmu/features/new_features.py
class NewFeatureCalculator:
    def __init__(self, param1=default1, param2=default2):
        self.param1 = param1
        self.param2 = param2

    def calculate_features(self, input_data):
        # Calculate features
        features = {}
        # ... feature calculation logic ...
        return features

# In deepmu/predictors/predictor.py
from ..features.new_features import NewFeatureCalculator

class MicrobialGrowthPredictor:
    def __init__(self, ...):
        # ...
        self.new_feature_calculator = NewFeatureCalculator()

    def calculate_features(self, ...):
        # ...
        new_features = self.new_feature_calculator.calculate_features(input_data)
        features.update(new_features)
        # ...
```

### Adding a New Model Branch

To add a new model branch:

1. Create a new module in the `models` package
2. Implement a branch class with a `forward` method
3. Update the `EnhancedPhyloGrowthModel` class to use the new branch
4. Update the fusion layer to incorporate the new branch

Example:
```python
# In deepmu/models/new_branch.py
class NewBranch(nn.Module):
    def __init__(self, input_dim, hidden_dim, output_dim):
        super().__init__()
        self.layers = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, output_dim)
        )

    def forward(self, x):
        return self.layers(x)

# In deepmu/models/networks.py
from .new_branch import NewBranch

class EnhancedPhyloGrowthModel(nn.Module):
    def __init__(self, ...):
        # ...
        self.new_branch = NewBranch(new_dim, hidden_dim, hidden_dim // 2)

    def forward(self, ...):
        # ...
        new_embed = self.new_branch(new_features)
        # ...
        fused = self._fuse_features(codon_embed, seq_embed, phylo_embed, new_embed)
        # ...
```

## Testing

### Unit Testing

The system uses pytest for unit testing:

1. **Feature Calculators**:
   - Test with known input sequences
   - Verify feature values against expected results
   - Test edge cases (empty sequences, invalid codons, etc.)

2. **Model Components**:
   - Test forward pass with dummy inputs
   - Verify output shapes and types
   - Test with edge cases (empty inputs, all zeros, etc.)

3. **Preprocessing Pipeline**:
   - Test with mock external tools
   - Verify output file formats
   - Test error handling

### Integration Testing

The system uses integration tests to verify end-to-end functionality:

1. **Prediction Pipeline**:
   - Test with real genome sequences
   - Verify prediction results
   - Test with different input formats

2. **CLI Interface**:
   - Test command-line arguments
   - Verify output files
   - Test error handling

### Performance Testing

The system uses performance tests to ensure efficiency:

1. **Memory Usage**:
   - Test with large genomes
   - Monitor memory consumption
   - Verify memory cleanup

2. **Processing Time**:
   - Test with multiple genomes
   - Measure processing time
   - Verify parallel processing efficiency

## Performance Optimization

### Memory Optimization

The system uses several techniques to optimize memory usage:

1. **Streaming Processing**:
   - Process sequences in chunks
   - Avoid loading entire genomes into memory
   - Implementation in `_process_sequence_chunks` method

2. **Memory Cleanup**:
   - Explicitly delete large objects after use
   - Use context managers for file handling
   - Implementation throughout the codebase

### Computational Optimization

The system uses several techniques to optimize computation:

1. **Vectorized Operations**:
   - Use NumPy and PyTorch for vectorized calculations
   - Avoid explicit loops where possible
   - Implementation in feature calculators

2. **Parallel Processing**:
   - Use multiprocessing for batch operations
   - Optimize CPU allocation
   - Implementation in `preprocess_batch` method

3. **GPU Acceleration**:
   - Use GPU for model inference
   - Optimize tensor operations
   - Implementation in model components
