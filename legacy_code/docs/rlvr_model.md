# Reinforcement Learning with Verifiable Rewards (RLVR) for Growth Rate Prediction

This document describes the implementation of Reinforcement Learning with Verifiable Rewards (RLVR) for microbial growth rate prediction in the DeepMu project.

## Overview

RLVR is a novel approach that combines reinforcement learning techniques with carefully designed reward functions based on prediction accuracy. The method enhances existing models by using a multi-branch architecture with attention mechanisms and a verifiable reward function that guides the learning process.

## Architecture

The RLVR implementation consists of three main components:

1. **VerifiableRewardFunction**: A reward function that verifies prediction accuracy by combining multiple components:
   - Accuracy: How close the prediction is to the true value
   - Consistency: How consistent the prediction is with a baseline model
   - Improvement: How much the prediction improves over the baseline
   - Calibration: How well-calibrated the prediction uncertainty is

2. **RLVRAgent**: A neural network model that uses a multi-branch architecture with attention mechanisms:
   - Feature-specific encoders for different feature types
   - Multi-head attention for feature fusion
   - Policy network for prediction
   - Value network for uncertainty estimation

3. **RLVRTrainer**: A trainer that implements the training process:
   - Policy gradient training with verifiable rewards
   - Experience replay for more stable learning
   - Adaptive learning rate scheduling
   - Comprehensive metrics tracking
   - Early stopping based on validation performance

## Reward Function

The verifiable reward function is a key component of the RLVR approach. It combines multiple components to guide the learning process:

```
reward = α * accuracy_component + β * consistency_component + γ * improvement_component
```

Where:
- `accuracy_component`: Measures how close the prediction is to the true value
- `consistency_component`: Measures how consistent the prediction is with a baseline model
- `improvement_component`: Measures how much the prediction improves over the baseline
- `α`, `β`, `γ`: Weighting factors for each component

## Training Process

The RLVR model is trained using a policy gradient approach with the following steps:

1. Initialize the agent and reward function
2. For each epoch:
   - Sample a batch of data
   - Make predictions using the agent
   - Calculate rewards using the verifiable reward function
   - Update the agent's parameters using policy gradient
   - Optionally sample from the experience replay buffer
   - Validate on a separate validation set
   - Update learning rate based on validation performance
   - Save the best model based on validation reward

## Usage

### Training a Model

To train an RLVR model, use the `train_rlvr_model.py` script:

```bash
./run_rlvr_model.sh
```

Or with custom parameters:

```bash
python train_rlvr_model.py \
    --feature-file features_all/combined_features.tsv \
    --metadata training_data/metadata.tsv \
    --output-dir models/rlvr_model \
    --metrics-dir metrics/rlvr_model \
    --hidden-dim 256 \
    --num-heads 4 \
    --num-layers 2 \
    --dropout 0.2 \
    --use-layer-norm \
    --use-residual \
    --use-value-head \
    --activation gelu \
    --alpha 0.6 \
    --beta 0.3 \
    --gamma 0.1 \
    --accuracy-scale 1.0 \
    --lr 1e-4 \
    --weight-decay 1e-5 \
    --batch-size 32 \
    --epochs 100 \
    --early-stopping-patience 10 \
    --entropy-coef 0.01 \
    --value-loss-coef 0.5 \
    --max-grad-norm 1.0 \
    --seed 42 \
    --feature-selection \
    --n-features 50
```

### Using a Trained Model

To use a trained RLVR model for prediction:

```python
import torch
from deepmu.models.rlvr_agent import RLVRAgent, RLVRTrainer

# Load model
agent = RLVRAgent(feature_dims={'all_features': 50}, hidden_dim=256)
trainer = RLVRTrainer(agent=agent, reward_fn=None)
trainer.load_model('models/rlvr_model/rlvr_model.pt')

# Prepare features
features = {'all_features': torch.tensor(feature_array, dtype=torch.float32)}

# Make prediction
predictions = agent.predict(features)
growth_rate = predictions['growth_rate']
uncertainty = predictions.get('uncertainty', None)
```

## Parameters

### Agent Parameters

- `feature_dims`: Dictionary mapping feature types to dimensions
- `hidden_dim`: Dimension of hidden layers (default: 256)
- `num_heads`: Number of attention heads (default: 4)
- `num_layers`: Number of transformer layers (default: 2)
- `dropout`: Dropout rate (default: 0.2)
- `use_layer_norm`: Whether to use layer normalization (default: True)
- `use_residual`: Whether to use residual connections (default: True)
- `use_value_head`: Whether to include a value head for uncertainty (default: True)
- `activation`: Activation function to use ('relu', 'gelu', or 'silu') (default: 'relu')

### Reward Function Parameters

- `baseline_model`: A pre-trained model to compare predictions against
- `alpha`: Weight for accuracy component (default: 0.6)
- `beta`: Weight for consistency component (default: 0.3)
- `gamma`: Weight for improvement component (default: 0.1)
- `accuracy_scale`: Scaling factor for accuracy component (default: 1.0)
- `use_r2_component`: Whether to include R² in accuracy component (default: True)
- `use_improvement_component`: Whether to include improvement over baseline (default: True)

### Trainer Parameters

- `lr`: Learning rate (default: 1e-4)
- `weight_decay`: Weight decay for regularization (default: 1e-5)
- `gamma`: Discount factor for future rewards (default: 0.99)
- `entropy_coef`: Coefficient for entropy regularization (default: 0.01)
- `value_loss_coef`: Coefficient for value loss (default: 0.5)
- `max_grad_norm`: Maximum gradient norm for clipping (default: 1.0)
- `use_lr_scheduler`: Whether to use learning rate scheduling (default: True)
- `use_experience_replay`: Whether to use experience replay (default: True)
- `replay_buffer_size`: Size of the experience replay buffer (default: 10000)
- `batch_size`: Batch size for experience replay (default: 32)

## Advantages of RLVR

The RLVR approach offers several advantages over traditional supervised learning methods:

1. **Verifiable Rewards**: The reward function provides a clear signal for what constitutes good predictions, making the learning process more interpretable.

2. **Baseline Integration**: The method can leverage existing models as baselines, building on their strengths while addressing their weaknesses.

3. **Uncertainty Estimation**: The value head provides uncertainty estimates for predictions, which is crucial for decision-making in biological applications.

4. **Feature Importance**: The model tracks feature importance during training, providing insights into which features are most relevant for prediction.

5. **Adaptive Learning**: The experience replay and adaptive learning rate scheduling make the training process more stable and efficient.

## References

1. Schulman, J., Wolski, F., Dhariwal, P., Radford, A., & Klimov, O. (2017). Proximal policy optimization algorithms. arXiv preprint arXiv:1707.06347.

2. Mnih, V., Kavukcuoglu, K., Silver, D., Rusu, A. A., Veness, J., Bellemare, M. G., ... & Hassabis, D. (2015). Human-level control through deep reinforcement learning. Nature, 518(7540), 529-533.

3. Vaswani, A., Shazeer, N., Parmar, N., Uszkoreit, J., Jones, L., Gomez, A. N., ... & Polosukhin, I. (2017). Attention is all you need. Advances in neural information processing systems, 30.
