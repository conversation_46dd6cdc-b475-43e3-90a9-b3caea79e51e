# Random Forest Ensemble Temperature Model

## Overview

This implementation provides a new approach for optimal temperature prediction in the DeepMu hybrid model. Instead of using neural networks, which have shown mixed results for temperature prediction, this implementation utilizes an ensemble of tree-based models to achieve better performance.

## Key Features

1. **Ensemble of Tree-Based Models**:
   - Random Forest
   - Extra Trees
   - Gradient Boosting
   - XGBoost
   - LightGBM

2. **Advanced Features**:
   - Automatic feature selection using preliminary importance analysis
   - Hyperparameter optimization via RandomizedSearchCV
   - Weighted ensemble based on validation performance
   - Comprehensive feature importance analysis with SHAP values

3. **Integration with Existing Growth Model**:
   - Compatible with the existing DeepMu growth rate predictor
   - Complete hybrid model combining both components

## Usage

### Training the RF Ensemble Temperature Model

```bash
./run_rf_ensemble.sh
```

This script:
- Trains the ensemble of tree-based models for temperature prediction
- Performs hyperparameter optimization
- Conducts feature selection
- Generates detailed evaluation reports and visualizations

### Training the Hybrid Model with RF Ensemble

```bash
./run_hybrid_rf_ensemble.sh
```

This script:
- Integrates the RF ensemble temperature model with an existing growth rate model
- Creates a complete hybrid model
- Evaluates performance on test data
- Generates comparison with previous approaches

## Expected Performance

The Random Forest ensemble approach is expected to provide several advantages over the neural network-based approaches:

1. **Improved R² for Temperature Prediction**: Tree-based models often perform better on tabular data with complex feature interactions.

2. **Better Generalization**: Random forests are less prone to overfitting compared to deep neural networks, especially with limited training data.

3. **Feature Importance Analysis**: More interpretable insights into which genomic features drive optimal temperature prediction.

4. **Reduced Training/Validation Gap**: Tree-based models typically show smaller gaps between training and validation performance.

## Model Architecture

The RF ensemble temperature model combines five different tree-based models:

1. **Random Forest**: Robust base model with good generalization capabilities
2. **Extra Trees**: Introduces more randomness for better ensemble diversity
3. **Gradient Boosting**: Sequential fitting to correct previous model errors
4. **XGBoost**: Implementation with advanced regularization and tree pruning
5. **LightGBM**: Gradient boosting with histogram-based learning

Each model is weighted according to its validation set performance to create the final ensemble prediction.

## Implementation Details

- `rf_ensemble_temp_model.py`: Core implementation of the RF ensemble temperature model
- `hybrid_rf_ensemble_model.py`: Integration with the growth rate model
- `run_rf_ensemble.sh`: Script to train and evaluate the temperature model
- `run_hybrid_rf_ensemble.sh`: Script to train and evaluate the hybrid model

## Evaluation

The model evaluation includes:

- R² score (coefficient of determination)
- RMSE (Root Mean Square Error)
- MAE (Mean Absolute Error)
- Feature importance analysis
- Model weight analysis
- Comparison with previous approaches

Results are saved in the model's output directory with comprehensive visualizations. 