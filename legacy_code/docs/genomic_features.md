# Genomic Features

This document explains how to handle genomic features in the DeepMu feature extraction pipeline, including common issues and their solutions.

## Overview

Genomic features such as genome size, GC content, nucleotide frequencies, dinucleotide frequencies, and skewness metrics are essential for accurate prediction of microbial growth rates and optimal temperatures.

## Common Issues

### Missing Genomic Features

Some genomes may have missing genomic features due to several reasons:

1. **GCF/GCA Prefix Mismatches**: Genome IDs in the metadata file might have "GCF_" prefixes, while the actual genome files have "GCA_" prefixes, or vice versa.
2. **Missing Genome Files**: The genome files might not be available in the specified directory.
3. **Corrupted Files**: The genome files might be corrupted or have incorrect formats.
4. **Taxonomy Database Issues**: The NCBI taxonomy database might not be properly prepared.

### Solutions

The DeepMu v1.1.1 codebase includes several fixes for these issues:

1. **Enhanced Genome ID Handling**: The feature extraction code now tries multiple naming conventions when looking for genome files:
   - `{genome_id}.fna`
   - `{genome_id.replace('GCF_', 'GCA_')}.fna`
   - `{genome_id.replace('GCA_', 'GCF_')}.fna`

2. **Improved Error Handling**: More detailed logging and error messages help identify and diagnose issues.

3. **Better Taxonomy Handling**: Updated taxonomy features code to handle the case when the NCBI taxonomy database is not yet downloaded.

## Preparing the NCBI Taxonomy Database

Before running feature extraction with taxonomy features, you need to prepare the NCBI taxonomy database:

```bash
python prepare_ncbi_taxonomy.py
```

This script:
1. Downloads and prepares the NCBI taxonomy database using the ETE3 toolkit
2. Stores the database in the default location (`~/.etetoolkit/taxa.sqlite`)

You can also specify a custom location for the database:

```bash
python prepare_ncbi_taxonomy.py --output-dir /path/to/custom/location
```

This is a one-time process that needs to be done before running feature extraction. The database is quite large (several hundred MB), so the download and preparation can take some time.

## Regenerating Missing Features

To regenerate features for genomes with missing genomic features, use the `regenerate_missing_features.py` script:

```bash
python regenerate_missing_features.py \
  --features-tsv features/combined_features.tsv \
  --metadata metadata.tsv \
  --genome-dir genomes/ \
  --cds-dir cds_ffn/ \
  --faa-dir cds_faa/ \
  --ko-dir ko_files/ \
  --output-dir features_fixed \
  --heg-ko-list heg_ko_list.txt \
  --combine \
  --output-tsv features_fixed/combined_features.tsv
```

This script will:
1. Identify genomes with missing genomic features
2. Regenerate features for those genomes
3. Combine all feature files into a single TSV file

## Checking for GCF/GCA Prefix Mismatches

To check for GCF/GCA prefix mismatches in your genome files, use the `check_genome_prefixes.py` script:

```bash
python check_genome_prefixes.py \
  --metadata metadata.tsv \
  --genome-dir genomes/
```

This script will:
1. Check if there are mismatches between the genome IDs in the metadata file and the actual genome files
2. Create a mapping file (`genome_id_mapping.tsv`) for genomes with GCF/GCA prefix mismatches

## Best Practices

To prevent genomic feature issues in the future, consider these best practices:

1. **Standardize Genome IDs**: Use either GCF or GCA prefixes consistently in both metadata and file names.

2. **Validate Inputs Before Processing**: Ensure all required files are available and have the correct format before running feature extraction.

3. **Prepare Taxonomy Database First**: Always run `prepare_ncbi_taxonomy.py` before extracting features that require taxonomic information.

4. **Use Consistent Directory Structure**: Maintain a consistent directory structure for genome files, CDS files, and other inputs.

5. **Check for Missing Features**: After feature extraction, verify that all expected features are present in the output files.

6. **Keep Detailed Logs**: Enable verbose logging to help troubleshoot any issues that might arise.

## Implementation Details

The genomic feature extraction is primarily implemented in `genomic_features.py`, which has been updated to handle the issues described above.

Key functions include:

- `process_genomes`: Processes genomic sequences to extract features
- `calculate_genomic_features`: Calculates various genomic features for a single genome
- `calculate_skew_features`: Calculates skew-related features
- `calculate_di_nucleotide_frequencies`: Calculates dinucleotide frequencies

These functions have been enhanced to handle different naming conventions and provide more informative error messages. 