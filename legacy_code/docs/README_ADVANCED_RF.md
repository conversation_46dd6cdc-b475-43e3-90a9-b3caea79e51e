# Advanced Random Forest with Feature Engineering

This module provides advanced feature engineering techniques to enhance Random Forest models for microbial growth rate prediction. The implementation builds on our base RF model by adding sophisticated feature processing steps.

## Features

- **Codon Feature Refinement**: Creates derived features from codon usage data including:
  - Normalized feature ratios
  - Log transformations
  - Pairwise differences 
  - Polynomial features for key delta features

- **Interaction Features**: Automatically discovers and creates interaction terms between important features to capture non-linear relationships.

- **Feature Selection**: Implements Recursive Feature Elimination (RFE) and cross-validated RFECV to identify optimal feature subsets.

- **Taxonomy Features**: Generates additional features from taxonomy information when metadata is provided.

## Scripts

- `advanced_rf_features.py`: Core implementation of the advanced feature engineering techniques.
- `run_advanced_rf.py`: Helper script to execute the feature engineering pipeline with different configurations.

## Usage

### Basic Example

```bash
python run_advanced_rf.py --feature-file data/codon_features.tsv
```

### With Metadata for Taxonomy Features

```bash
python run_advanced_rf.py \
  --feature-file data/codon_features.tsv \
  --metadata data/metadata.tsv \
  --output-prefix my_model \
  --target growth_rate
```

### Custom Configuration

```bash
python run_advanced_rf.py \
  --feature-file data/codon_features.tsv \
  --metadata data/metadata.tsv \
  --output-prefix my_model \
  --target growth_rate \
  --feature-engineering all
```

### Direct Usage of Feature Engineering Module

```bash
python advanced_rf_features.py \
  --feature-file data/codon_features.tsv \
  --metadata data/metadata.tsv \
  --output-dir rf_models \
  --metrics-dir rf_metrics \
  --target growth_rate \
  --refine-codon \
  --create-interactions \
  --top-n-interactions 20 \
  --feature-selection rfecv
```

## Feature Engineering Options

You can select which feature engineering techniques to apply:

- `--feature-engineering all`: Apply all techniques (default)
- `--feature-engineering codon`: Only apply codon feature refinement
- `--feature-engineering interaction`: Only create interaction features
- `--feature-engineering selection`: Only perform feature selection
- `--feature-engineering none`: Use original features without engineering

## Output

The pipeline generates multiple outputs:

1. **Enhanced Feature Set**: The dataset with all engineered features
2. **Selected Feature Set**: The optimal subset of features after selection
3. **Model Metrics**: Performance evaluation metrics (MSE, RMSE, MAE, R²)
4. **Feature Importance**: Visualizations and rankings of feature importance
5. **Predictions**: Model predictions for the test set

## Requirements

- Python 3.8+
- pandas
- numpy
- scikit-learn
- matplotlib
- seaborn

## Advanced Configuration

For more fine-grained control, you can use the individual parameters in the `advanced_rf_features.py` script:

```bash
python advanced_rf_features.py --help
```

## Performance Notes

The advanced feature engineering typically improves model performance by capturing complex relationships in the data. However, it also increases computational requirements and can potentially lead to overfitting if not properly tuned. The implementation includes safeguards against overfitting through cross-validation. 