# DeepMu User Guide

This guide provides detailed instructions for using DeepMu to predict microbial growth rates and optimal growth temperatures.

## Table of Contents

- [Introduction](#introduction)
- [Installation](#installation)
- [Basic Usage](#basic-usage)
- [Advanced Usage](#advanced-usage)
- [Input Formats](#input-formats)
- [Output Formats](#output-formats)
- [Tutorials](#tutorials)
- [Feature Extraction](#feature-extraction)
- [Troubleshooting](#troubleshooting)
- [FAQ](#faq)
- [Prediction Using Taxonomy IDs](#prediction-using-taxonomy-ids)

## Introduction

DeepMu is a powerful tool for predicting microbial growth rates and optimal growth temperatures using a multi-branch neural network approach. It incorporates multiple types of biological information:

1. **Codon usage patterns** - Analyzing how microbes use different codons to encode proteins
2. **HEG/BP codon features** - Differentiating codon usage between highly expressed genes and background genes
3. **Phylogenetic information** - Using evolutionary relationships between microbes
4. **Sequence features** - Extracting patterns from DNA sequences
5. **Hierarchical taxonomy** - Leveraging multi-level taxonomic classification
6. **KO (KEGG Orthology) profiles** - Analyzing functional gene annotations
7. **Pathway completeness** - Calculating the completeness of metabolic pathways

DeepMu can make predictions for:
- Individual organisms (single genomes)
- Microbial communities (metagenomes)
- Batches of multiple genomes

## Installation

### Prerequisites

Before installing DeepMu, ensure you have the following:

- Python 3.8 or higher
- Prodigal (for gene prediction)
- KofamScan (for KO annotation)
- CUDA-compatible GPU (recommended for training)
- At least 16GB RAM (32GB recommended for large datasets)
- 100GB+ disk space for KofamScan database

### Step-by-Step Installation

1. **Clone the repository**:
   ```bash
   git clone https://github.com/username/DeepMu.git
   cd DeepMu
   ```

2. **Create and activate a virtual environment**:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install Python dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

4. **Install Prodigal**:

   On Linux/macOS:
   ```bash
   # Using conda
   conda install -c bioconda prodigal

   # Or from source
   git clone https://github.com/hyattpd/Prodigal.git
   cd Prodigal
   make
   sudo make install
   ```

   On Windows:
   ```bash
   # Using conda
   conda install -c bioconda prodigal

   # Or download the binary from https://github.com/hyattpd/Prodigal/releases
   ```

5. **Install KofamScan**:

   On Linux/macOS:
   ```bash
   # Clone the repository
   git clone https://github.com/takaram/kofam_scan.git
   cd kofam_scan

   # Install Ruby if not already installed
   # On Ubuntu/Debian
   sudo apt-get install ruby

   # On macOS
   brew install ruby

   # Install required gems
   gem install parallel
   ```

   On Windows:
   ```bash
   # Install Ruby from https://rubyinstaller.org/
   # Install required gems
   gem install parallel

   # Clone the repository
   git clone https://github.com/takaram/kofam_scan.git
   ```

6. **Download and setup KofamScan database**:
   ```bash
   # Create a directory for the database
   mkdir -p kofam_db
   cd kofam_db

   # Download KofamScan database
   wget https://www.genome.jp/ftp/db/kofam/profiles.tar.gz
   wget https://www.genome.jp/ftp/db/kofam/ko_list.gz

   # Extract and setup
   tar xzf profiles.tar.gz
   gunzip ko_list.gz

   # Note the full path to the database
   KOFAMSCAN_DB=$(pwd)
   cd ..
   ```

7. **Install ETE3 for taxonomy lookup (optional but recommended)**:
   ```bash
   pip install ete3
   ```

8. **Verify installation**:
   ```bash
   # Test Prodigal
   prodigal -h

   # Test KofamScan
   cd kofam_scan
   ./exec_annotation -h
   cd ..

   # Test DeepMu
   python deepmu_cli.py -h
   ```

## Basic Usage

DeepMu provides a command-line interface (CLI) for easy use. Here are the basic commands for common tasks:

### Preprocessing a Genome

Before making predictions, you can preprocess your genome to extract genes and annotate them with KO terms:

```bash
python deepmu_cli.py preprocess genome.fna \
    --output-dir processed/ \
    --kofamscan-db /path/to/kofamscan_db \
    --genetic-code 11  # Default for bacteria
```

This will:
1. Predict genes using Prodigal
2. Annotate the predicted genes with KO terms using KofamScan
3. Save the results in the specified output directory

### Single Organism Prediction

To predict the growth rate and optimal temperature for a single organism:

```bash
# Using preprocessed files
python deepmu_cli.py single processed/genome_cds.faa \
    --ko-file processed/genome_ko.tsv \
    --taxid 511145 \  # NCBI taxonomy ID for E. coli K-12 MG1655
    --output result.json
```

Or directly from a genome file:

```bash
# Direct genome analysis
python deepmu_cli.py single genome.fna \
    --is-genome \
    --kofamscan-db /path/to/kofamscan_db \
    --output-dir processed/ \
    --output result.json
```

### Community Prediction

To predict growth rates and optimal temperatures for a microbial community:

```bash
# Basic community analysis
python deepmu_cli.py community metagenome.fna \
    --coverage coverage.tsv \
    --temp 37 \
    --output community_result.json
```

### Batch Processing

To process multiple genomes at once:

```bash
# Process multiple genomes
python deepmu_cli.py batch genomes_dir/ \
    --temp 37 \
    --output batch_results.json
```

## Advanced Usage

### Multi-Branch Neural Network Architecture

DeepMu v1.1.1 implements a sophisticated multi-branch neural network architecture that processes different types of genomic features through specialized pathways:

1. **Codon Branch**: Processes codon usage patterns
   - Input: Codon frequency vectors (64-dimensional)
   - Architecture: 3 fully connected layers with ReLU activation
   - Layer dimensions: 64 → 128 → 128 → 128
   - Output: 128-dimensional feature vector
   - Features:
     - Codon Usage Bias (CUB)
     - Codon Pair Bias (CPB)
     - Codon Consistency
     - Remote Interaction Analysis
     - Advanced codon features (CAI, ENC, etc.)
     - HEG/BP differentiation

2. **Phylogenetic Branch**: Processes taxonomic information
   - Input: Taxonomy strings (hierarchical levels from phylum to genus)
   - Components:
     - TaxonomicEmbedder: Creates embeddings for each taxonomy level
     - HierarchicalGNN: Processes taxonomic relationships as a graph
     - KO Similarity integration: Enriches the graph with functional similarity
   - Layer dimensions: Taxonomy embeddings → GNN → 128 → 64
   - Output: 64-dimensional feature vector
   - Features:
     - Standard embedding-based approach
     - Optional hierarchical taxonomy support
     - KO profile similarity integration
     - Position-aware attention mechanisms

3. **Sequence Branch**: Processes DNA sequence patterns
   - Input: k-mer frequencies from genome sequences
   - Architecture: Parallel 1D CNN with multiple kernel sizes (3, 5, 7)
   - Global max pooling for position invariance
   - Output: 96-dimensional feature vector
   - Features:
     - CNN-based processing
     - Position-aware attention
     - Variable length support

4. **Pathway Branch** (Optional): Processes metabolic pathway information
   - Input: KEGG pathway completeness scores
   - Architecture: 2 fully connected layers with ReLU activation
   - Layer dimensions: Input → 64 → 32
   - Output: 32-dimensional feature vector
   - Features:
     - KEGG pathway integration
     - Pathway completeness analysis
     - Metabolic function analysis
     - Pathway-level attention

### Feature Fusion

The outputs from all branches are combined through:

1. **Cross-Attention Mechanism**:
   - Computes attention weights between different feature types
   - Allows the model to focus on the most informative features
   - Enables adaptive weighting based on organism characteristics

2. **Feature Concatenation**:
   - Combines attended features from all branches
   - Preserves branch-specific information

3. **Fusion Network**:
   - Fully connected layers: 320 → 256 → 128 → 64
   - Dropout (p=0.3) for regularization
   - Separate prediction heads for growth rate and temperature

### Feature Selection (when enabled)
   - Automatic identification of most important features
   - Simplified model architecture
   - Reduced computational requirements
   - Support for multiple selection methods (mutual information, random forest)

### Advanced Regularization Techniques

DeepMu v1.1.1 implements sophisticated regularization strategies to prevent overfitting and improve model generalization:

1. **L1/L2 Regularization**:
   - Adds penalty terms to the loss function based on the magnitude of model weights
   - L1 regularization promotes sparsity in the model weights
   - L2 regularization prevents weights from becoming too large
   - Branch-specific regularization applies different strengths to different feature types

2. **Dropout Strategies**:
   - Standard Dropout: Randomly deactivates neurons during training
   - Adaptive Dropout: Varies dropout rates based on layer depth
   - Feature-Type Dropout: Applies different dropout rates to different feature types
   - Spatial Dropout: Deactivates entire feature maps in convolutional layers

3. **Early Stopping**:
   - Monitors validation performance during training
   - Stops training when performance plateaus or degrades
   - Prevents overfitting by avoiding unnecessary training iterations
   - Multi-metric early stopping considers both growth rate and temperature prediction

4. **Gradient Clipping**:
   - Prevents exploding gradients by limiting the gradient norm
   - Improves training stability, especially for deep networks
   - Adaptive gradient clipping adjusts the clipping threshold based on gradient statistics

5. **Batch/Layer Normalization**:
   - Batch normalization normalizes activations within each mini-batch
   - Layer normalization normalizes activations across features for each sample
   - Improves training stability and allows higher learning rates
   - Reduces internal covariate shift during training

### Command Line Usage

```bash
# Basic usage with hierarchical taxonomy
python deepmu_cli.py single genome.fna \
    --use-hierarchical-phylo \
    --taxid 511145 \
    --output result.json

# Using pathway features
python deepmu_cli.py single genome.fna \
    --use-pathway-branch \
    --ko-dir ko_files/ \
    --output result.json

# Community analysis with HEG features
python deepmu_cli.py community metagenome.fna \
    --use-heg-features \
    --coverage coverage.tsv \
    --output community_result.json
```

### Using Hierarchical Taxonomy

DeepMu supports hierarchical taxonomy integration through the `--taxid` parameter:

```bash
# Using NCBI taxonomy ID
python deepmu_cli.py single genome.fna \
    --is-genome \
    --taxid 511145 \  # E. coli K-12 MG1655 taxonomy ID
    --output result.json
```

Alternatively, you can provide a pipe-separated string of taxonomy IDs directly:

```bash
# Using pipe-separated taxonomy string
python deepmu_cli.py single genome.fna \
    --is-genome \
    --taxid "1224|1236|91347|543|570" \  # phylum|class|order|family|genus
    --output result.json
```

### Advanced Codon Feature Analysis

DeepMu v1.1.1 includes advanced codon feature analysis that significantly improves prediction accuracy. To enable these features:

```bash
python deepmu_cli.py single genome.fna \
    --is-genome \
    --heg-ko-list heg_ko_list.txt \
    --advanced-features \
    --output result_advanced.json
```

This will include the following advanced codon features:

- **Codon Adaptation Index (CAI)**: Measures how well adapted a gene's codon usage is to the most expressed genes
- **Effective Number of Codons (ENC)**: Measures the evenness of codon usage (range 20-61)
- **GC Content at Different Positions**: Measures GC content at 1st, 2nd, and 3rd codon positions
- **Codon Context Bias**: Measures non-random associations between adjacent codons
- **HEG-specific Metrics**: Calculates all the above metrics specifically for highly expressed genes

### HEG/BP Codon Feature Analysis

To enable Highly Expressed Gene (HEG) and Background (BP) codon feature analysis:

```bash
python deepmu_cli.py single genome.fna \
    --is-genome \
    --heg-ko-list heg_ko_list.txt \
    --analyze \
    --output result_heg.json
```

The HEG KO list file should contain one KO ID per line, e.g.:
```
K01977
K01985
K02863
K02864
K02867
K02879
K02881
K02884
K02886
K02887
K02888
K02889
K02890
K02892
K02895
K02897
K02898
```

When using a pre-trained model with HEG/BP codon features:

```bash
python deepmu_cli.py single genome.fna \
    --is-genome \
    --heg-ko-list heg_ko_list.txt \
    --model-path models/trained_model.pt \
    --output result_heg.json
```

The model will automatically detect whether it was trained with HEG/BP codon features and use the appropriate parameters.

### Pathway Completeness Analysis

To incorporate KEGG pathway completeness analysis:

```bash
python deepmu_cli.py single genome.fna \
    --is-genome \
    --kegg-path /path/to/kegg.txt \
    --output result_pathway.json
```

The KEGG pathway file should contain pathway information in the format:
```
ko00010  Glycolysis / Gluconeogenesis
ko00020  Citrate cycle (TCA cycle)
ko00030  Pentose phosphate pathway
...
```

This will calculate the completeness of each pathway based on the presence of required KO terms, providing valuable functional context for growth rate prediction. The completeness is calculated as the proportion of KO terms present in each pathway.

### Pathway-Based Clustering

To use pathway-based clustering for dimensionality reduction and improved biological interpretability:

```bash
# Generate pathway cluster features and train a model
./run_training_with_pathway_clusters.sh
```

This script will:
1. Generate pathway cluster features by grouping KO terms based on their participation in the same KEGG pathways
2. Create both pathway-level clusters (individual pathways) and category-level clusters (e.g., carbohydrate metabolism, energy metabolism)
3. Train a model using these cluster features
4. Generate reports and visualizations to analyze the importance of different pathway clusters

Pathway-based clustering offers several advantages:
- Reduces dimensionality from thousands of KO terms to hundreds of pathway clusters
- Improves biological interpretability by focusing on functional modules
- Enhances model performance by reducing noise and focusing on functional patterns

### Metagenome Analysis

For metagenomic analysis with organism mapping:

```bash
python deepmu_cli.py community metagenome.fna \
    --coverage coverage.tsv \
    --org-to-seq org_to_seq.tsv \
    --taxonomy-file taxonomy.tsv \
    --output mapped_community_result.json
```

The organism-to-sequence mapping file should have the format:
```
organism_id  sequence_id
org1         seq1
org1         seq2
org2         seq3
...
```

The taxonomy file should have the format:
```
organism_id  taxonomy_string
org1         1224|1236|91347|543|570
org2         1224|1236|91347|543|571
...
```

### Detailed Feature Analysis

To include detailed feature analysis in the results:

```bash
python deepmu_cli.py single genome.fna \
    --is-genome \
    --analyze \
    --output result_detailed.json
```

This will include all calculated features in the output JSON file, which can be useful for understanding the prediction.

## Hybrid RF-Transformer Architecture

DeepMu v1.1.1 introduces a hybrid architecture that combines Random Forest for growth rate prediction with Transformer models for temperature prediction. This specialized approach allows each prediction task to benefit from the architecture that works best for it.

### Using the Hybrid Architecture

To use the hybrid RF-Transformer architecture:

```bash
python deepmu_cli.py single genome.fna \
    --is-genome \
    --kofamscan-db /path/to/kofamscan_db \
    --model-path models/hybrid_rf_transformer/model.pt \
    --output result.json
```

### Benefits of the Hybrid Architecture

- **Improved Growth Rate Prediction**: Random Forest excels at capturing the non-linear relationships in codon usage patterns that affect growth rate
- **Better Temperature Prediction**: Transformer models are more effective at capturing the sequence patterns that determine optimal temperature
- **Feature Importance Analysis**: Random Forest provides detailed feature importance analysis
- **Confidence Estimation**: The hybrid architecture provides confidence estimates for predictions

## Training Custom Models

DeepMu allows you to train custom models on your own data. This is useful if you have specific requirements or want to incorporate additional data sources.

### Training a Hybrid RF-Transformer Model

```bash
python train_hybrid_rf_transformer.py \
    --feature-file features_all/combined_features.tsv \
    --metadata training_data/metadata.tsv \
    --output-dir models/hybrid_rf_transformer \
    --metrics-dir metrics/hybrid_rf_transformer \
    --rf-estimators 500 \
    --rf-max-depth 20 \
    --transformer-layers 4 \
    --transformer-heads 8 \
    --batch-size 32 \
    --epochs 100 \
    --learning-rate 1e-4
```

### Using the Enhanced Training Pipeline

The enhanced training pipeline provides numerous improvements over the standard training process, including better feature engineering, advanced optimization techniques, and uncertainty estimation.

#### Basic Usage

The easiest way to train a custom model is to use the provided script:

```bash
./run_training_enhanced.sh
```

This script sets up all the necessary parameters and directories for training.

#### Custom Training

For more control over the training process, you can run the training script directly with custom parameters:

```bash
python train_models_enhanced.py \
    --metadata data/metadata.tsv \
    --feature-dir data/features \
    --ko-dir data/ko \
    --kegg-path data/kegg/pathway_mapping.txt \
    --use-hierarchical-phylo \
    --use-heg-features \
    --use-pathway \
    --use-weighted-similarity \
    --use-feature-selection \
    --batch-size 32 \
    --num-epochs 100 \
    --learning-rate 0.001 \
    --output-dir models/custom
```

#### Key Training Options

- **Data Options**:
  - `--metadata`: Path to metadata file with growth rates and temperatures
  - `--feature-dir`: Directory containing extracted features
  - `--ko-dir`: Directory containing KO annotations
  - `--kegg-path`: Path to KEGG pathway mapping file

- **Model Architecture**:
  - `--use-hierarchical-phylo`: Use hierarchical phylogenetic features
  - `--use-heg-features`: Use highly expressed gene features
  - `--use-pathway`: Use pathway features
  - `--hidden-dim`: Size of hidden dimensions (default: 256)

- **Feature Engineering**:
  - `--use-weighted-similarity`: Use weighted similarity for KO profiles
  - `--use-feature-selection`: Select important features based on correlation
  - `--important-ko-features`: Number of important KO features to select

- **Training Parameters**:
  - `--batch-size`: Batch size for training (default: 32)
  - `--learning-rate`: Learning rate (default: 0.001)
  - `--weight-decay`: Weight decay for regularization (default: 1e-5)
  - `--num-epochs`: Maximum number of training epochs (default: 100)
  - `--patience`: Patience for early stopping (default: 10)

- **Advanced Options**:
  - `--mixed-precision`: Use mixed precision training for faster training
  - `--model-parallel`: Use model parallelism for large models
  - `--gradient-accumulation-steps`: Accumulate gradients over multiple batches
  - `--use-uncertainty`: Enable uncertainty estimation

### Monitoring Training Progress

During training, the script will output metrics for each epoch, including loss, MAE, and R² values for both training and validation sets. These metrics are also saved to the specified output directory for later analysis.

```
Epoch 10/100: Train Loss: 0.0123, Val Loss: 0.0145, Train MAE: 0.0876, Val MAE: 0.0923, Train R²: 0.8765, Val R²: 0.8543
```

### Visualizing Training Results

After training, you can visualize the results using the provided plotting scripts:

```bash
python -m deepmu.visualization.plot_training_metrics \
    --metrics-dir models/custom/metrics \
    --output-dir models/custom/plots
```

This will generate plots of loss, MAE, and R² values over time, as well as scatter plots comparing predicted vs. actual values.

## Feature Extraction

DeepMu provides functionality to extract features from genomic data for custom analysis or debugging. The feature extraction module can be used independently of the prediction pipeline.

### Basic Feature Extraction

To extract basic features from a set of genomes:

```bash
python -m deepmu.features \
    --cds-dir data/cds \
    --output-dir data/features \
    --scan-directory
```

This will extract basic codon usage features and save them as NPZ files in the output directory.

### Enhanced Feature Extraction

For more comprehensive feature extraction, including KO similarity, taxonomy, and HEG features:

```bash
python -m deepmu.features \
    --cds-dir data/cds \
    --ko-dir data/ko \
    --taxonomy-dir data/taxonomy \
    --heg-ko-list data/heg_ko_list.txt \
    --output-dir data/features \
    --genome-list data/genome_list.txt \
    --use-weighted-similarity
```

### Advanced Codon Features

DeepMu v1.1.1 includes support for advanced codon usage features, which can be enabled with the `--advanced-codon-features` flag:

```bash
python -m deepmu.features \
    --cds-dir data/cds \
    --output-dir data/features \
    --genome-list data/genome_list.txt \
    --advanced-codon-features
```

The advanced codon features include:

- **Codon Adaptation Index (CAI)**: Measures how well adapted a sequence is to the codon usage of highly expressed genes
- **CAI_HEG and CAI_BP**: CAI calculated separately for highly expressed genes and background genes
- **ENC_HEG and ENC_BP**: Effective Number of Codons for Highly Expressed Genes and Background
- **RSCU_diff**: Difference between HEG and BP RSCU values
- **codon_bias_HEG_BP**: Codon bias between HEG and BP genes
- **Effective Number of Codons (ENC)**: Measures the deviation from equal usage of synonymous codons (ranges from 20-61)
- **GC Content Analysis**: Total GC content and position-specific GC content (GC1, GC2, GC3)
- **Codon Context Bias**: Measures preferences for specific adjacent codon pairs

These advanced features can provide additional insights into the organism's codon usage patterns and may improve prediction accuracy.

### Taxonomy Features Using NCBI Taxonomy IDs

DeepMu v1.1.1 adds support for automatically retrieving taxonomy information using NCBI taxonomy IDs (taxids) without requiring taxonomy files. This feature uses the ETE3 library to lookup the full lineage for each taxid.

You can specify taxonomy IDs in two ways:

1. **Default taxid for all genomes**:
```bash
python -m deepmu.features \
    --cds-dir data/cds \
    --output-dir data/features \
    --genome-list data/genome_list.txt \
    --default-taxid 562  # E. coli taxid
```

2. **Mapping file for different taxids**:
```bash
python -m deepmu.features \
    --cds-dir data/cds \
    --output-dir data/features \
    --genome-list data/genome_list.txt \
    --taxid-map data/genome_to_taxid.tsv
```

Where `genome_to_taxid.tsv` is a tab-separated file with genome IDs and their corresponding NCBI taxonomy IDs:
```
GCF_*********.2	562
GCF_000006945.2	1280
GCF_000007445.1	1423
```

This eliminates the need to create and maintain a separate taxonomy directory with taxonomy files for each genome.

### Output Formats

By default, features are saved as NPZ files (NumPy compressed format), one file per genome. You can change the output format to TSV (tab-separated values) for easier viewing and processing:

```bash
python -m deepmu.features \
    --cds-dir data/cds \
    --output-dir data/features \
    --scan-directory \
    --output-format tsv
```

This will create a combined `combined_features.tsv` file with all genomes' features.

### Processing Specific Genomes

To process only specific genomes, provide a list of genome IDs:

```bash
python -m deepmu.features \
    --cds-dir data/cds \
    --output-dir data/features \
    --genome-list my_genomes.txt
```

Where `my_genomes.txt` contains one genome ID per line.

### Feature Extraction API

You can also use the feature extraction API in your Python code:

```python
from deepmu.features import extract_features, extract_features_for_genome

# Extract features for a single genome
features = extract_features_for_genome(
    genome_id="GCF_*********.2",
    cds_file="data/cds/GCF_*********.2_cds.fna",
    ko_file="data/ko/GCF_*********.2_ko.tsv",
    advanced_codon_features=True
)

# Extract features for all genomes in a directory
extract_features(
    cds_dir="data/cds",
    ko_dir="data/ko",
    output_dir="data/features",
    advanced_codon_features=True
)
```

## Input Formats

### Genome Files

DeepMu accepts genome files in FASTA format:

```
>contig1
ATGCATGCATGCATGCATGCATGCATGCATGCATGCATGCATGCATGCATGCATGCAT
GCATGCATGCATGCATGCATGCATGCATGCATGCATGCATGCATGCATGCATGCATGCAT
>contig2
ATGCATGCATGCATGCATGCATGCATGCATGCATGCATGCATGCATGCATGCATGCAT
GCATGCATGCATGCATGCATGCATGCATGCATGCATGCATGCATGCATGCATGCATGCAT
```

### Protein Files

Protein files should be in FASTA format:

```
>protein1
MKVLWAALLVTFLAGCQAKVEQAVETEPEPELRGTDEGPSKIKQTLEEQLGPVTQEFWDNLE
KETEGLRQEMSKDLEEVKAKVQPYLDDFQKKWQEEMELYRQKVEPLRAELQEGARQKLHELE
>protein2
MKVLWAALLVTFLAGCQAKVEQAVETEPEPELRGTDEGPSKIKQTLEEQLGPVTQEFWDNLE
KETEGLRQEMSKDLEEVKAKVQPYLDDFQKKWQEEMELYRQKVEPLRAELQEGARQKLHELE
```

### KO Annotation Files

KO annotation files should be in TSV format:

```
sequence_id  ko_id   threshold   score   e-value   definition
protein1     K01977  0.0         100.0   1e-50     DNA polymerase I
protein2     K01985  0.0         95.5    1e-45     3'-5' exonuclease
```

### Coverage Files

Coverage files for community analysis should be in TSV format:

```
sequence_id  coverage
contig1      10.5
contig2      5.2
contig3      2.1
```

### Taxonomy Files

Taxonomy files should be in TSV format:

```
organism_id  taxonomy_string
org1         1224|1236|91347|543|570
org2         1224|1236|91347|543|571
```

## Output Formats

### Single Organism Prediction

The output for single organism prediction is a JSON file:

```json
{
  "growth_rate": 0.85,
  "optimal_temperature": 37.2,
  "input_file": "genome.fna",
  "timestamp": "2023-06-15T12:34:56",
  "model_version": "1.1.1",
  "features": {
    "CUB": 0.45,
    "CPB": 0.32,
    "Consistency": 0.78,
    "RemoteInteraction": 0.56,
    "HEG_CUB": 0.67,
    "HEG_CPB": 0.43,
    "HEG_Consistency": 0.89,
    "HEG_RemoteInteraction": 0.65,
    "BG_CUB": 0.42,
    "BG_CPB": 0.30,
    "BG_Consistency": 0.75,
    "BG_RemoteInteraction": 0.54,
    "delta_CUB": 0.25,
    "delta_CPB": 0.13,
    "delta_Consistency": 0.14,
    "delta_RemoteInteraction": 0.11,
    "CAI_HEG": 0.78,
    "CAI_BP": 0.65,
    "RSCU_diff": 0.13,
    "codon_bias_HEG_BP": 0.22,
    "pathway_completeness_ko00010": 0.85,
    "pathway_completeness_ko00020": 0.92,
    "pathway_completeness_ko00030": 0.76
  }
}
```

### Community Prediction

The output for community prediction is a JSON file:

```json
{
  "community_growth_rate": 0.65,
  "community_optimal_temperature": 35.8,
  "organism_predictions": [
    {
      "organism_id": "org1",
      "growth_rate": 0.85,
      "optimal_temperature": 37.2,
      "abundance": 0.6
    },
    {
      "organism_id": "org2",
      "growth_rate": 0.45,
      "optimal_temperature": 33.5,
      "abundance": 0.4
    }
  ],
  "input_file": "metagenome.fna",
  "timestamp": "2023-06-15T12:34:56",
  "model_version": "1.1.1"
}
```

### Batch Processing

The output for batch processing is a JSON file:

```json
{
  "predictions": [
    {
      "file": "genome1.fna",
      "growth_rate": 0.85,
      "optimal_temperature": 37.2
    },
    {
      "file": "genome2.fna",
      "growth_rate": 0.45,
      "optimal_temperature": 33.5
    }
  ],
  "timestamp": "2023-06-15T12:34:56",
  "model_version": "1.1.1"
}
```

## Tutorials

### Tutorial 1: Predicting Growth Rate for E. coli

In this tutorial, we'll predict the growth rate and optimal temperature for E. coli K-12 MG1655.

1. **Download the genome**:
   ```bash
   wget https://ftp.ncbi.nlm.nih.gov/genomes/all/GCF/000/005/845/GCF_*********.2_ASM584v2/GCF_*********.2_ASM584v2_genomic.fna.gz
   gunzip GCF_*********.2_ASM584v2_genomic.fna.gz
   mv GCF_*********.2_ASM584v2_genomic.fna ecoli.fna
   ```

2. **Preprocess the genome**:
   ```bash
   python deepmu_cli.py preprocess ecoli.fna \
       --output-dir ecoli_processed/ \
       --kofamscan-db /path/to/kofamscan_db \
       --genetic-code 11
   ```

3. **Make the prediction**:
   ```bash
   python deepmu_cli.py single ecoli_processed/ecoli_cds.faa \
       --ko-file ecoli_processed/ecoli_ko.tsv \
       --taxid 511145 \
       --output ecoli_result.json
   ```

4. **View the results**:
   ```bash
   cat ecoli_result.json
   ```

### Tutorial 2: Community Analysis

In this tutorial, we'll analyze a simple microbial community.

1. **Create a sample metagenome**:
   ```bash
   # Combine multiple genomes
   cat ecoli.fna bacillus.fna pseudomonas.fna > community.fna

   # Create a coverage file
   echo -e "sequence_id\tcoverage" > coverage.tsv
   echo -e "ecoli_contig1\t10.5" >> coverage.tsv
   echo -e "ecoli_contig2\t10.2" >> coverage.tsv
   echo -e "bacillus_contig1\t5.3" >> coverage.tsv
   echo -e "bacillus_contig2\t5.1" >> coverage.tsv
   echo -e "pseudomonas_contig1\t2.2" >> coverage.tsv
   echo -e "pseudomonas_contig2\t2.0" >> coverage.tsv
   ```

2. **Create an organism-to-sequence mapping**:
   ```bash
   echo -e "organism_id\tsequence_id" > org_to_seq.tsv
   echo -e "ecoli\tecoli_contig1" >> org_to_seq.tsv
   echo -e "ecoli\tecoli_contig2" >> org_to_seq.tsv
   echo -e "bacillus\tbacillus_contig1" >> org_to_seq.tsv
   echo -e "bacillus\tbacillus_contig2" >> org_to_seq.tsv
   echo -e "pseudomonas\tpseudomonas_contig1" >> org_to_seq.tsv
   echo -e "pseudomonas\tpseudomonas_contig2" >> org_to_seq.tsv
   ```

3. **Create a taxonomy file**:
   ```bash
   echo -e "organism_id\ttaxonomy_string" > taxonomy.tsv
   echo -e "ecoli\t1224|1236|91347|543|561" >> taxonomy.tsv
   echo -e "bacillus\t1239|91061|1385|186817|1386" >> taxonomy.tsv
   echo -e "pseudomonas\t1224|1236|72274|135621|286" >> taxonomy.tsv
   ```

4. **Make the prediction**:
   ```bash
   python deepmu_cli.py community community.fna \
       --coverage coverage.tsv \
       --org-to-seq org_to_seq.tsv \
       --taxonomy-file taxonomy.tsv \
       --output community_result.json
   ```

5. **View the results**:
   ```bash
   cat community_result.json
   ```

### Tutorial 3: Batch Processing

In this tutorial, we'll process multiple genomes in batch mode.

1. **Create a directory with multiple genomes**:
   ```bash
   mkdir genomes
   cp ecoli.fna genomes/
   cp bacillus.fna genomes/
   cp pseudomonas.fna genomes/
   ```

2. **Make the prediction**:
   ```bash
   python deepmu_cli.py batch genomes/ \
       --is-genome \
       --kofamscan-db /path/to/kofamscan_db \
       --output-dir processed/ \
       --output batch_results.json
   ```

3. **View the results**:
   ```bash
   cat batch_results.json
   ```

## Troubleshooting

### Common Issues and Solutions

1. **Gene Prediction Failures**
   - **Problem**: Prodigal fails to predict genes
   - **Solution**:
     - Check genetic code parameter (`--genetic-code`)
     - Ensure input file is in correct format
     - Try different Prodigal parameters (`--meta` for metagenomes)

2. **KO Annotation Issues**
   - **Problem**: KofamScan fails to annotate genes
   - **Solution**:
     - Verify KofamScan database path
     - Check database version compatibility
     - Ensure sufficient memory allocation

3. **Taxonomy Processing Errors**
   - **Problem**: Invalid taxonomy string format
   - **Solution**:
     - Verify taxonomy string format (pipe-separated IDs)
     - Check taxonomy IDs against NCBI database
     - Make sure ETE3 is installed for automatic taxonomy retrieval
     - Use `--verbose` for detailed error information

4. **Memory Issues**
   - **Problem**: Out of memory during processing
   - **Solution**:
     - Use `--batch-size` parameter
     - Process in smaller chunks
     - Increase system memory or use swap

5. **Model Loading Errors**
   - **Problem**: Failed to load model
   - **Solution**:
     - Check model file integrity
     - Verify model version compatibility
     - Reinstall dependencies

### Debugging Tips

1. **Verbose Output**
   ```bash
   python deepmu_cli.py single genome.fna --verbose
   ```

2. **Log File Generation**
   ```bash
   python deepmu_cli.py single genome.fna --log-file debug.log
   ```

## Training with Feature Selection

DeepMu v1.1.1 introduces feature selection capabilities to identify the most important features for prediction and create simplified models.

### Feature Selection with HEG/BP and Pathway Completeness

```bash
python train_models_enhanced.py \
    --metadata training_data/metadata.tsv \
    --feature-dir training_data/features_updated \
    --ko-dir training_data/kegg/ko_files \
    --kegg-path training_data/kegg/pathway_mapping.txt \
    --pathway-ids training_data/kegg/important_pathways.txt \
    --output-dir models/feature_selected \
    --metrics-dir metrics/feature_selected \
    --checkpoint-dir checkpoints/feature_selected \
    --heg-ko-list training_data/kegg/heg_ko_list.txt \
    --batch-size 16 \
    --num-epochs 50 \
    --learning-rate 0.0005 \
    --weight-decay 1e-4 \
    --patience 7 \
    --seed 42 \
    --use-heg-features \
    --use-feature-selection \
    --feature-selection-method random_forest \
    --important-features 20
```

### Feature Selection Methods

DeepMu supports multiple feature selection methods:

1. **Mutual Information** (`mutual_info`): Selects features based on mutual information with the target variable
2. **Random Forest** (`random_forest`): Uses random forest feature importance for selection
3. **Correlation** (`correlation`): Selects features based on correlation with the target

You can specify the method using the `--feature-selection-method` parameter.

### Number of Important Features

Control the number of features to select with the `--important-features` parameter. A good starting point is 10-20 features, but you may need to experiment to find the optimal number for your dataset.

### Feature Normalization

DeepMu v1.1.1 includes automatic feature normalization to improve training stability. This process standardizes all features to have zero mean and unit variance, which helps prevent features with large magnitudes from dominating the learning process.

Feature normalization is automatically applied during training when using the enhanced training script:

```bash
./run_enhanced_training.sh
```

This script includes a step that normalizes all features before training, which helps the optimizer converge faster and more stably.