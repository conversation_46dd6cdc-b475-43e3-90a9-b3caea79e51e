# DeepMu Features

DeepMu uses a variety of genomic features to predict microbial growth rates and optimal growth temperatures. This document describes the features used by DeepMu.

## Codon Usage Features

Codon usage features are calculated from the coding sequences (CDS) of a genome. These features capture the codon usage bias of the genome, which is known to be correlated with growth rate.

- **CUB (Codon Usage Bias)**: Measures the deviation from uniform codon usage.
- **CPB (Codon Pair Bias)**: Measures the bias in adjacent codon pairs.
- **CAI (Codon Adaptation Index)**: Measures the adaptation of codon usage to a reference set of highly expressed genes.
- **ENC (Effective Number of Codons)**: Measures the effective number of codons used in a genome.
- **RSCU (Relative Synonymous Codon Usage)**: Measures the relative usage of synonymous codons.
- **MILC (Measure Independent of Length and Composition)**: A measure of codon usage bias that is independent of gene length and amino acid composition.
- **Context_bias**: Measures the bias in codon context (adjacent codons).
- **Consistency**: Measures the consistency of codon usage across genes.

## HEG-Specific Features

HEG (Highly Expressed Gene) features are calculated specifically for genes that are known to be highly expressed in most organisms. These features capture the codon usage bias of highly expressed genes, which is often more pronounced than the bias in the rest of the genome.

- **HEG_CUB**: Codon usage bias in highly expressed genes.
- **HEG_CPB**: Codon pair bias in highly expressed genes.
- **HEG_CAI**: Codon adaptation index in highly expressed genes.
- **HEG_ENC**: Effective number of codons in highly expressed genes.
- **HEG_RSCU**: Relative synonymous codon usage in highly expressed genes.
- **HEG_MILC**: MILC in highly expressed genes.
- **HEG_Consistency**: Consistency of codon usage in highly expressed genes.
- **codon_bias_HEG_BP**: Codon bias in highly expressed genes compared to the background.
- **aa_bias_HEG_BP**: Amino acid bias in highly expressed genes compared to the background.

## Amino Acid Features

Amino acid features are calculated from the protein sequences of a genome. These features capture the amino acid composition of the genome, which is known to be correlated with optimal growth temperature.

- **aa_freq_X**: Frequency of amino acid X in the genome.
- **HEG_aa_freq_X**: Frequency of amino acid X in highly expressed genes.
- **aa_carbon_total**: Total number of carbon atoms in the amino acid side chains.
- **aa_nitrogen_total**: Total number of nitrogen atoms in the amino acid side chains.
- **aa_c_arsc**: Average number of carbon atoms per amino acid residue side chain.
- **aa_n_arsc**: Average number of nitrogen atoms per amino acid residue side chain.
- **HEG_aa_carbon_total**: Total number of carbon atoms in the amino acid side chains of highly expressed genes.
- **HEG_aa_nitrogen_total**: Total number of nitrogen atoms in the amino acid side chains of highly expressed genes.
- **HEG_aa_c_arsc**: Average number of carbon atoms per amino acid residue side chain in highly expressed genes.
- **HEG_aa_n_arsc**: Average number of nitrogen atoms per amino acid residue side chain in highly expressed genes.

## Protein Isoelectric Point (pI) Features

Protein isoelectric point (pI) features are calculated from the protein sequences of a genome. These features capture the distribution of isoelectric points across the proteome, which can be related to adaptation to different environmental conditions.

- **protein_pi_mean**: Mean isoelectric point of all proteins in the genome.
- **protein_pi_median**: Median isoelectric point of all proteins in the genome.
- **protein_pi_std**: Standard deviation of isoelectric points across the proteome.
- **protein_pi_min**: Minimum isoelectric point observed in the proteome.
- **protein_pi_max**: Maximum isoelectric point observed in the proteome.
- **protein_pi_range**: Range of isoelectric points (max - min) in the proteome.
- **protein_pi_acidic_fraction**: Fraction of proteins with acidic pI (< 7.0).
- **protein_pi_basic_fraction**: Fraction of proteins with basic pI (> 7.0).
- **protein_pi_neutral_fraction**: Fraction of proteins with neutral pI (6.5-7.5).
- **protein_pi_bimodality**: Measure of bimodality in the pI distribution, calculated as the distance between the two most prominent peaks in the pI histogram.

## Genomic Features

Genomic features are calculated from the genome sequence. These features capture the overall composition and structure of the genome.

- **genome_size**: Size of the genome in base pairs.
- **gc_content**: GC content of the genome.
- **dinucleotide_freq_XX**: Frequency of dinucleotide XX in the genome.
- **skewness_X**: Skewness of nucleotide X distribution along the genome.
- **skewness_change_point_X**: Position of the change point in skewness of nucleotide X.
- **skewness_change_point_X_normalized**: Position of the change point in skewness of nucleotide X, normalized by genome size.

## RNA Features

RNA features are calculated from the tRNA and rRNA annotations of a genome. These features capture the tRNA and rRNA content of the genome, which is known to be correlated with growth rate.

- **tRNA_count**: Total number of tRNAs in the genome.
- **tRNA_count_X**: Number of tRNAs for amino acid X.
- **rRNA_count**: Total number of rRNAs in the genome.
- **rRNA_16S_count**: Number of 16S rRNAs in the genome.
- **rRNA_23S_count**: Number of 23S rRNAs in the genome.
- **rRNA_5S_count**: Number of 5S rRNAs in the genome.
- **tAI**: tRNA Adaptation Index, a measure of the adaptation of codon usage to tRNA abundance.

## Pathway Features

Pathway features are calculated from the KEGG pathway annotations of a genome. These features capture the metabolic capabilities of the genome.

- **pathway_X**: Completeness of KEGG pathway X in the genome.
- **pathway_category_X**: Average completeness of KEGG pathways in category X.

## Taxonomy Features

Taxonomy features are calculated from the NCBI taxonomy ID of a genome. These features capture the taxonomic classification of the genome.

- **taxonomy_X**: Binary feature indicating whether the genome belongs to taxonomic group X.
- **taxonomy_level_X**: Taxonomic level X of the genome (e.g., phylum, class, order, family, genus).

## Feature Importance

The importance of different features for predicting growth rate and optimal growth temperature varies. In general, codon usage features (especially in highly expressed genes) are most important for growth rate prediction, while amino acid composition features are most important for temperature prediction.

For growth rate prediction, the most important features are typically:
1. codon_bias_HEG_BP
2. Context_bias
3. RSCU_diff
4. delta_CUB
5. HEG_Consistency

For temperature prediction, the most important features are typically:
1. HEG_aa_carbon_total
2. HEG_aa_c_arsc
3. HEG_aa_freq_Y
4. HEG_aa_freq_S
5. RSCU_diff

## References

For more information on the features used by DeepMu, see the following references:

1. Sharp, P. M., & Li, W. H. (1987). The codon adaptation index--a measure of directional synonymous codon usage bias, and its potential applications. Nucleic acids research, 15(3), 1281-1295.
2. Wright, F. (1990). The 'effective number of codons' used in a gene. Gene, 87(1), 23-29.
3. Suzuki, H., Brown, C. J., Forney, L. J., & Top, E. M. (2008). Comparison of correspondence analysis methods for synonymous codon usage in bacteria. DNA research, 15(6), 357-365.
4. Vieira-Silva, S., & Rocha, E. P. (2010). The systemic imprint of growth and its uses in ecological (meta)genomics. PLoS genetics, 6(1), e1000808.
5. Weissman, J. L., Dogra, S., Javadi, K., Bolten, S., Flint, R., Davati, C., Beattie, J., Diaz, K., Gurung, S., Gonzalez, M., Koontz, D., Nguyen, K., Ramirez, S., Yuki, K., Zeng, Y., Vieira-Silva, S., Sarmiento, F., Chien, P., Weiss, S. J., Murray, J. I., & Polz, M. F. (2021). Weissman et al. reply. Nature, 595(7865), E18-E19.
