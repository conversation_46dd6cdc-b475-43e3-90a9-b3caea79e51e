# Transformer Model for Temperature Prediction

This document describes the Transformer model implemented in DeepMu for microbial optimal temperature prediction.

## Overview

The Transformer model is a powerful deep learning architecture that:

1. **Uses self-attention mechanisms** to capture complex relationships between features
2. **Processes different feature types** through dedicated branches
3. **Fuses information** from multiple feature sources
4. **Excels at capturing complex patterns** in the data

This approach leverages the strengths of Transformer architectures for temperature prediction, achieving superior performance (R² = 0.875).

![Transformer Architecture](../assets/transformer_model.txt)

## Key Components

### Transformer Architecture

The Transformer model consists of the following components:

- **Feature Branches**: Process different feature types through dedicated branches
- **Transformer Fusion**: Models complex interactions between features using self-attention
- **Fusion Network**: Processes fused features through multiple layers
- **Output Layer**: Single neuron for temperature prediction

### Self-Attention Mechanism

The core of the Transformer model is the self-attention mechanism:

- **Query, Key, Value Projections**: Transform input features into query, key, and value representations
- **Attention Scores**: Compute compatibility between queries and keys
- **Weighted Sum**: Aggregate values based on attention scores
- **Multi-Head Attention**: Apply attention in parallel with different projections

### Hyperparameters

The Transformer model has several key hyperparameters:

- **hidden_dims**: Hidden dimensions for transformer layers (default: [256, 256, 256])
- **dropout_rates**: Dropout rates for regularization (default: [0.2, 0.3, 0.4])
- **transformer_heads**: Number of attention heads (default: 4)
- **transformer_layers**: Number of transformer layers (default: 2)
- **use_batch_norm**: Whether to use batch normalization (default: True)
- **use_residual**: Whether to use residual connections (default: True)
- **use_positional_encoding**: Whether to use positional encoding (default: True)

## Advantages of Transformer

The Transformer model offers several advantages for temperature prediction:

1. **Attention Mechanism**: Captures dependencies between different feature types
2. **Parallelization**: Allows efficient training compared to recurrent models
3. **Feature Interaction**: Models complex interactions between genomic features
4. **Hierarchical Representation**: Builds increasingly abstract representations through layers
5. **State-of-the-Art Performance**: Achieves superior performance for temperature prediction

## Performance Comparison

| Metric | Multi-Branch Advanced | Transformer | Combined DNN-Transformer | Hybrid RF-Transformer | Random Forest |
|--------|----------------------|-------------|--------------------------|------------------------|----------------|
| Growth Rate R² | 0.182 | 0.259 | 0.714 | 0.773 | 0.293 |
| Growth Rate RMSE | 3.78 | 3.82 | 2.06 | 2.12 | 3.39 |
| Temperature R² | 0.772 | 0.875 | 0.744 | -0.021 | - |
| Temperature RMSE | 6.80 | 5.97 | 7.80 | 19.07 | - |

The Transformer model achieves the best performance for temperature prediction with an R² of 0.875 and RMSE of 5.97.

## Feature Types

The model supports the following feature types:

1. **Codon Features**: Codon usage bias, codon pair bias, CAI, etc.
2. **Amino Acid Features**: Amino acid composition, physicochemical properties, etc.
3. **Genomic Features**: Genome size, GC content, dinucleotide frequencies, etc.
4. **RNA Features**: tRNA and rRNA counts, tAI, etc.
5. **Protein pI Features**: Isoelectric point features
6. **Taxonomy Features**: Taxonomic classification features

## Training Process

The Transformer model is trained using PyTorch:

1. **Data Preparation**:
   - Features are extracted from genomic data
   - Features are categorized by type (codon, amino acid, genomic, etc.)
   - Data is split into training and testing sets

2. **Model Training**:
   - Model is trained using Adam optimizer
   - Learning rate scheduler is used to adjust learning rate
   - Early stopping is applied based on validation loss
   - Batch normalization and dropout are used for regularization

3. **Evaluation**:
   - Model is evaluated on the test set
   - Performance is measured using R² and RMSE metrics

## Uncertainty Estimation

The Transformer model provides uncertainty estimation through:

1. **Monte Carlo Dropout**: Running multiple forward passes with dropout enabled
2. **Prediction Variance**: Calculating variance across multiple stochastic predictions
3. **Confidence Intervals**: Deriving confidence intervals from prediction distribution

## Usage

### Evaluation

To evaluate the Transformer model for temperature prediction, you would typically use a script similar to:

```bash
python evaluate_transformer_model.py \
    --feature-file features_all/combined_features.tsv \
    --metadata training_data/metadata.tsv \
    --transformer-model models/transformer/best_model.pt \
    --output-dir models/transformer_evaluation
```

### Prediction

To make predictions using the Transformer model for temperature prediction, you would typically use a script similar to:

```bash
python predict_transformer.py \
    --feature-file features_all/combined_features.tsv \
    --transformer-model models/transformer/best_model.pt \
    --transformer-config models/transformer/config.json \
    --output-file predictions/transformer_predictions.tsv
```

## Implementation Details

The implementation is available in the following files:

- `deepmu/models/transformer_network.py`: Implementation of the Transformer model
- `train_transformer.py`: Training script for the Transformer model

## References

1. Vaswani, A., et al. (2017). Attention is all you need. *Advances in neural information processing systems*.
2. Devlin, J., et al. (2018). BERT: Pre-training of deep bidirectional transformers for language understanding. *arXiv preprint arXiv:1810.04805*.
