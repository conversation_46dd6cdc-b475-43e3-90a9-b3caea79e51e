# Improved Random Forest Model for Growth Rate Prediction

This document describes the improved Random Forest model implemented in DeepMu for microbial growth rate prediction.

## Overview

The improved Random Forest model is a powerful ensemble learning method that:

1. **Incorporates taxonomy information** to capture phylogenetic signals
2. **Uses entropy-based breakpoint features** to detect genomic patterns
3. **Emphasizes important codon features** to improve prediction accuracy
4. **Provides detailed feature importance analysis** for interpretability
5. **Is robust to overfitting and outliers**

This approach leverages the strengths of Random Forest for tabular data and regression tasks like growth rate prediction.

![Improved Random Forest Architecture](../assets/improved_rf_model.txt)

## Key Components

### Random Forest Architecture

The improved Random Forest model consists of the following components:

- **Decision Trees**: Multiple decision trees trained on bootstrap samples of the data
- **Feature Sampling**: Random subset of features considered at each split
- **Ensemble Prediction**: Average of predictions from all trees
- **Feature Importance**: Measure of feature contribution to prediction accuracy

### Feature Enhancement

The model includes several feature enhancement techniques:

1. **Taxonomy Features**: Simple features derived from taxonomy IDs
2. **Breakpoint Features**: Features related to genomic skew and change points
3. **Emphasized Codon Features**: Important codon features are emphasized by doubling their values

### Hyperparameters

The improved Random Forest model has several key hyperparameters:

- **n_estimators**: Number of trees in the forest (default: 200)
- **max_depth**: Maximum depth of trees (default: None for unlimited)
- **min_samples_split**: Minimum samples required to split a node (default: 2)
- **min_samples_leaf**: Minimum samples required at a leaf node (default: 1)

## Advantages of Improved Random Forest

The improved Random Forest model offers several advantages for growth rate prediction:

1. **Robustness**: Resistant to overfitting and handles noisy data well
2. **Feature Importance**: Provides insights into which features are most predictive
3. **No Scaling Required**: Works well with features on different scales without normalization
4. **Handles Non-linearity**: Captures complex non-linear relationships between features
5. **Efficiency**: Faster to train than deep learning models and can be parallelized
6. **Few Hyperparameters**: Relatively easy to tune compared to deep learning models
7. **Taxonomy Integration**: Captures phylogenetic signals through taxonomy features
8. **Breakpoint Detection**: Identifies important genomic patterns through breakpoint features

## Performance Comparison

| Metric | Multi-Branch Advanced | Transformer | Combined DNN-Transformer | Hybrid RF-Transformer | Random Forest | Improved RF |
|--------|----------------------|-------------|--------------------------|------------------------|----------------|-------------|
| Growth Rate R² | 0.182 | 0.259 | 0.714 | 0.773 | 0.293 | 0.773 |
| Growth Rate RMSE | 3.78 | 3.82 | 2.06 | 2.12 | 3.39 | 2.12 |

The improved Random Forest model achieves significantly better performance than the basic Random Forest model, matching the performance of the hybrid RF-Transformer model.

## Feature Types

The model supports the following feature types:

1. **Codon Features**: Codon usage bias, codon pair bias, CAI, etc.
2. **Amino Acid Features**: Amino acid composition, physicochemical properties, etc.
3. **Genomic Features**: Genome size, GC content, dinucleotide frequencies, etc.
4. **RNA Features**: tRNA and rRNA counts, tAI, etc.
5. **Protein pI Features**: Isoelectric point features
6. **Taxonomy Features**: Features derived from taxonomy IDs
7. **Breakpoint Features**: Genomic skew and change point features

## Training Process

The improved Random Forest model is trained using scikit-learn's RandomForestRegressor:

1. **Data Preparation**:
   - Features are extracted from genomic data
   - Features are categorized by type (codon, amino acid, genomic, etc.)
   - Taxonomy features are created from taxonomy IDs
   - Breakpoint features are created from genomic skew and change point data
   - Data is split into training and testing sets

2. **Feature Enhancement**:
   - Important codon features are emphasized by doubling their values
   - Taxonomy features are created from taxonomy IDs
   - Breakpoint features are created from genomic skew and change point data

3. **Model Training**:
   - Random Forest is trained on the enhanced features
   - Bootstrap samples are created for each tree
   - Random feature subsets are considered at each split

4. **Evaluation**:
   - Model is evaluated on the test set
   - Performance is measured using R² and RMSE metrics
   - Feature importance is calculated

## Uncertainty Estimation

The improved Random Forest model provides built-in uncertainty estimation:

1. **Variance Across Trees**: The variance of predictions across all trees in the ensemble
2. **Prediction Intervals**: Can be calculated based on the distribution of predictions
3. **Out-of-Bag Error**: Estimated using samples not included in bootstrap training sets

## Usage

### Training

To train an improved Random Forest model, use the `train_improved_rf.py` script:

```bash
python train_improved_rf.py \
    --feature-file features_all/combined_features.tsv \
    --metadata training_data/metadata.tsv \
    --output-dir models/improved_rf \
    --metrics-dir metrics/improved_rf \
    --n-estimators 200 \
    --max-depth None
```

### Evaluation

To evaluate the improved Random Forest model, use the same script with the `--output-dir` parameter pointing to the model directory:

```bash
python train_improved_rf.py \
    --feature-file features_all/combined_features.tsv \
    --metadata training_data/metadata.tsv \
    --output-dir models/improved_rf_evaluation \
    --metrics-dir metrics/improved_rf_evaluation
```

### Prediction

To make predictions using the improved Random Forest model, use the model's `predict` method:

```python
from deepmu.models.improved_rf import ImprovedRandomForest

# Load model
model = ImprovedRandomForest()
model.load('models/improved_rf')

# Make predictions
predictions = model.predict(features, taxonomy_features, breakpoint_features)
```

## Implementation Details

The implementation is available in the following files:

- `deepmu/models/improved_rf.py`: Core implementation of the improved Random Forest model
- `train_improved_rf.py`: Training script for the improved Random Forest model

## References

1. Breiman, L. (2001). Random forests. *Machine learning*, 45(1), 5-32.
2. Geurts, P., Ernst, D., & Wehenkel, L. (2006). Extremely randomized trees. *Machine learning*, 63(1), 3-42.
