# Combined DNN-Transformer Architecture

This document describes the combined DNN-Transformer architecture implemented in DeepMu for microbial growth rate and optimal growth temperature prediction.

## Overview

The combined DNN-Transformer architecture leverages the strengths of both basic DNN and transformer approaches:

1. **Basic DNN Path**: Used for growth rate prediction, as DNNs have shown better performance for this task
2. **Transformer Path**: Used for temperature prediction, as transformers excel at capturing complex patterns for this task
3. **Shared Feature Extraction**: Common feature extraction for both paths to leverage shared representations

This specialized approach allows each prediction task to benefit from the architecture that works best for it.

![Combined DNN-Transformer Architecture](../assets/combined_architecture.png)

## Key Components

### 1. Feature Branches

Each feature type is processed through a dedicated branch with the following components:

- **Input Layer**: Takes features of a specific type (codon, amino acid, genomic, etc.)
- **Hidden Layers**: Two fully-connected layers with batch normalization, ReLU activation, and dropout
- **Residual Connections**: Skip connections to improve gradient flow
- **Output Layer**: Projects to a common embedding dimension

### 2. Growth Rate Prediction Path (DNN)

The growth rate prediction path uses a basic DNN architecture:

- **Input**: Concatenated features from all branches
- **Hidden Layers**: Multiple fully-connected layers with batch normalization, ReLU activation, and dropout
- **Output Layer**: Single neuron for growth rate prediction

### 3. Temperature Prediction Path (Transformer)

The temperature prediction path uses a transformer-based architecture:

- **Input**: Features from different branches
- **Transformer Fusion**: Models complex interactions between features using self-attention
- **Fusion Network**: Processes fused features through multiple layers
- **Output Layer**: Single neuron for temperature prediction

## DNN Path Details

The DNN path is a simple feed-forward neural network with multiple hidden layers:

1. **Input**: Concatenated features from all branches [batch_size, total_feature_dim]
2. **Hidden Layers**: Multiple fully-connected layers with batch normalization, ReLU activation, and dropout
3. **Output**: Growth rate prediction [batch_size]

## Transformer Path Details

The transformer path uses a transformer encoder to model complex interactions between features:

1. **Input**: Features from different branches [batch_size, num_branches, embed_dim]
2. **Positional Encoding**: Adds positional information to feature embeddings (optional)
3. **Transformer Encoder**: Processes features through multiple transformer layers
   - **Multi-Head Self-Attention**: Allows each branch to attend to all other branches
   - **Feed-Forward Network**: Applies non-linear transformations to attention outputs
   - **Layer Normalization**: Normalizes outputs for stable training
   - **Residual Connections**: Improves gradient flow
4. **Fusion Network**: Processes fused features through multiple layers
5. **Output**: Temperature prediction [batch_size]

## Advantages of the Combined Approach

The combined DNN-Transformer architecture offers several advantages:

1. **Specialized Architectures**: Each prediction task uses the architecture that works best for it
2. **Shared Feature Extraction**: Common feature extraction reduces redundancy and leverages shared representations
3. **Improved Performance**: Better performance on both growth rate and temperature prediction tasks
4. **Balanced Complexity**: Simpler architecture for growth rate prediction, more complex for temperature prediction

## Feature Types

The model supports the following feature types:

1. **Codon Features**: Codon usage bias, codon pair bias, CAI, etc.
2. **Amino Acid Features**: Amino acid composition, physicochemical properties, etc.
3. **Genomic Features**: Genome size, GC content, dinucleotide frequencies, etc.
4. **RNA Features**: tRNA and rRNA counts, tAI, etc.
5. **Protein pI Features**: Isoelectric point features
6. **Taxonomy Features**: Taxonomic classification features

## Training

The model is trained using the following components:

- **Loss Function**: Mean Squared Error (MSE) for regression
- **Multi-Task Learning**: Weighted sum of growth rate and temperature losses
- **Optimizer**: AdamW with weight decay
- **Learning Rate Scheduler**: ReduceLROnPlateau
- **Early Stopping**: Based on validation loss
- **Gradient Clipping**: Prevents exploding gradients during training

## Usage

To train a combined DNN-Transformer model, use the `train_combined_model.py` script:

```bash
python train_combined_model.py \
    --feature-file features_all/combined_features.tsv \
    --metadata training_data/metadata.tsv \
    --output-dir models/combined \
    --metrics-dir metrics/combined \
    --batch-size 32 \
    --epochs 100 \
    --branch-hidden-dim 256 \
    --dnn-hidden-dims 256 128 64 \
    --transformer-hidden-dim 256 \
    --fusion-hidden-dim 128 \
    --dropout-rates 0.2 0.3 0.4 \
    --transformer-heads 4 \
    --transformer-layers 2
```

## Hyperparameter Tuning

The combined DNN-Transformer architecture has several hyperparameters that can be tuned:

- **branch_hidden_dim**: Hidden dimension for feature branches
- **dnn_hidden_dims**: Hidden dimensions for the DNN path
- **transformer_hidden_dim**: Hidden dimension for the transformer path
- **fusion_hidden_dim**: Hidden dimension for the fusion network
- **dropout_rates**: Dropout rates for different parts of the network
- **transformer_heads**: Number of attention heads in transformer
- **transformer_layers**: Number of transformer layers
- **use_positional_encoding**: Whether to use positional encoding in transformer

## Implementation Details

The implementation is available in the following files:

- `deepmu/models/combined_dnn_transformer.py`: Core implementation of the combined DNN-Transformer architecture
- `train_combined_model.py`: Training script for the combined model

## References

1. He, K., et al. (2016). Deep residual learning for image recognition. *Proceedings of the IEEE conference on computer vision and pattern recognition*.
2. Vaswani, A., et al. (2017). Attention is all you need. *Advances in neural information processing systems*.
3. Kendall, A., et al. (2018). Multi-task learning using uncertainty to weigh losses for scene geometry and semantics. *Proceedings of the IEEE conference on computer vision and pattern recognition*.
