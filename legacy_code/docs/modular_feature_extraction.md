# Modular Feature Extraction for DeepMu

This document describes the modular feature extraction framework for DeepMu, which separates different types of features into separate modules for better organization and maintainability.

## Feature Types

The modular feature extraction framework includes the following feature types:

1. **Codon Features**: Features based on codon usage patterns in coding sequences (CDS)
   - Basic codon usage metrics (CUB, CPB, etc.)
   - Advanced codon metrics (CAI, ENC, etc.)
   - HEG-specific codon metrics (comparing highly expressed genes to background)

2. **Amino Acid Features**: Features based on amino acid composition and properties
   - Amino acid frequencies
   - Physicochemical properties (hydrophobicity, charge, etc.)
   - Carbon and nitrogen content (C-ARSC and N-ARSC)
   - HEG-specific amino acid metrics (comparing highly expressed genes to background)

3. **Genomic Features**: Features based on genomic sequences
   - Genome size, GC content
   - Skew metrics (GC skew, AT skew, etc.)
   - Dinucleotide frequencies and skews
   - Change point detection in skew metrics

4. **Taxonomy Features**: Features based on NCBI taxonomy IDs
   - Raw taxonomy IDs for each taxonomic level (phylum, class, order, family, genus)
   - Normalized taxonomy IDs (log-scaled) for machine learning models

5. **Pathway Features**: Features based on KEGG pathway annotations
   - Pathway completeness for all individual pathways (578 pathways)
   - Pathway category metrics

## Directory Structure

The modular feature extraction framework is organized as follows:

```
deepmu/features/
├── amino_acid_features.py  # Amino acid feature calculator
├── codon_features.py       # Codon feature calculator
├── genomic_features.py     # Genomic feature calculator
├── modular_feature_extraction.py  # Main feature extraction module
├── pathway_features.py     # Pathway feature calculator
├── sequence_features.py    # Sequence feature utilities
└── taxonomy_features.py    # Taxonomy feature calculator
```

## Integration Summary

In DeepMu v1.1.1, the feature extraction codebase has been significantly refactored to improve maintainability and reduce redundancy. This section summarizes the integration of `feature_extraction.py` into `modular_feature_extraction.py`.

### Changes Made

#### 1. Moved Common Functions to `modular_feature_extraction.py`

- Added `load_heg_genes` function from `feature_extraction.py` to `modular_feature_extraction.py`
- Added `calculate_heg_features` function from `feature_extraction.py` to `modular_feature_extraction.py`
- Updated the `extract_codon_features` function in `modular_feature_extraction.py` to use these functions

#### 2. Updated `feature_extraction.py` for Backward Compatibility

- Modified `feature_extraction.py` to import and use functions from `modular_feature_extraction.py`
- Updated `extract_features_for_genome` to use the modular version
- Added `genetic_code` parameter to functions for consistency
- Added deprecation notice to indicate that new code should use `modular_feature_extraction.py`

#### 3. Improved Code Quality

- Fixed unused variable warnings
- Improved error handling
- Added better logging
- Updated documentation

#### 4. Created Test Script

- Created `test_feature_integration.py` to test both the original and modular feature extraction modules
- The script can be used to verify that both implementations produce the same results

### Benefits

1. **Reduced Redundancy**: Eliminated duplicate code between the two modules
2. **Better Organization**: Clearer separation of concerns with modular approach
3. **Backward Compatibility**: Existing code using `feature_extraction.py` will continue to work
4. **Improved Maintainability**: Easier to maintain a single implementation
5. **Better Documentation**: Added clear documentation about which module to use

### Testing

To test the integration, run:

```bash
python test_feature_integration.py --cds-dir training_data/cds_ffn --ko-dir training_data/kegg/ko_files --metadata training_data/metadata.tsv
```

This will run both implementations and verify they produce similar results.

## Usage

### Command-Line Interface

The modular feature extraction framework can be used from the command line using the `run_modular_feature_extraction.py` script:

```bash
./run_modular_feature_extraction.py --metadata training_data/metadata.tsv
```

This will extract features for all genomes in the metadata file and save them to the `features` directory.

### Options

The following options are available:

```
--metadata METADATA       Path to metadata file (required)
--genome-dir GENOME_DIR   Directory containing genome files (default: training_data/genomes)
--cds-dir CDS_DIR         Directory containing CDS files (default: training_data/cds_ffn)
--faa-dir FAA_DIR         Directory containing protein files (default: training_data/cds_faa)
--ko-dir KO_DIR           Directory containing KO files (default: training_data/kegg/ko_files)
--output-dir OUTPUT_DIR   Directory to save feature files (default: features)
--output-tsv OUTPUT_TSV   Path to output TSV file (default: features/combined_features.tsv)
--kegg-map KEGG_MAP       Path to KEGG mapping file (default: training_data/kegg/pathway_mapping.txt)
--heg-ko-list HEG_KO_LIST Path to HEG KO list file (default: training_data/kegg/heg_ko_list.txt)
--advanced-codon-features Calculate advanced codon features (default: False)

Note: When --heg-ko-list is provided, both HEG-specific codon features and HEG-specific amino acid features will be calculated.
--num-processes NUM       Number of processes to use (default: number of CPU cores)
--skip-existing           Skip genomes with existing feature files (default: False)
```

### Metadata File Format

The metadata file should be a tab-separated file with the following columns:

- `genome_id`: Unique identifier for each genome
- `growth_rate`: Growth rate of the organism (optional)
- `optimal_temperature`: Optimal growth temperature of the organism (optional)
- `taxid`: NCBI taxonomy ID of the organism (optional)
- `genetic_code`: Genetic code used by the organism (optional)

### File Naming Conventions

The modular feature extraction framework expects the following file naming conventions:

- Genome files: `{genome_id}.fna` in the genome directory
- CDS files: `{genome_id}_cds.ffn` in the CDS directory
- Protein files: `{genome_id}_cds.faa` in the protein directory
- KO files: `{genome_id}.ko` in the KO directory

### Output Files

The modular feature extraction framework produces the following output files:

- Feature files: `{genome_id}_features.npz` in the output directory
- Combined features: `combined_features.tsv` in the output directory

## Programmatic Usage

The modular feature extraction framework can also be used programmatically:

```python
from deepmu.features.modular_feature_extraction import extract_features_batch, combine_feature_files

# Extract features for all genomes in the metadata file
extract_features_batch(
    metadata_file="training_data/metadata.tsv",
    output_dir="features",
    genome_dir="training_data/genomes",
    cds_dir="training_data/cds_ffn",
    faa_dir="training_data/cds_faa",
    ko_dir="training_data/kegg/ko_files",
    kegg_map_file="data/kegg/ko00001.keg",
    heg_ko_list="data/heg_ko_list.txt",
    advanced_codon_features=True,
    num_processes=4
)

# Combine feature files into a single TSV file
combine_feature_files("features", "features/combined_features.tsv")
```

## Additional Utilities

### C-ARSC and N-ARSC Calculator

A standalone script `calculate_nc_arsc.py` is provided to calculate C-ARSC and N-ARSC values for a set of protein sequences:

```bash
./calculate_nc_arsc.py --input_dir training_data/cds_faa --output_file results/nc_arsc.tsv
```

This script processes all protein FASTA files in the input directory and outputs the C-ARSC and N-ARSC values to a TSV file. The output file contains the following columns:

- `genome_id`: Genome ID
- `C-ARSC`: Carbon atoms per amino acid residue side chain
- `N-ARSC`: Nitrogen atoms per amino acid residue side chain

## Adding New Features

To add new features to the modular feature extraction framework:

1. Create a new feature calculator class in the appropriate module
2. Add the new feature calculator to the `extract_features_for_genome` function in `modular_feature_extraction.py`
3. Update the command-line interface in `run_modular_feature_extraction.py` if necessary

## Feature Details

### Codon Features

#### Basic Codon Features

| Feature | Description |
|---------|-------------|
| `CUB` | Codon Usage Bias - Measures bias in synonymous codon choice |
| `CPB` | Codon Pair Bias - Analyzes preferences for adjacent codon pairs |
| `Consistency` | Measures how consistently preferred codons are used |
| `RemoteInteraction` | Novel metric for position-aware codon relationship analysis |

#### Advanced Codon Features

| Feature | Description |
|---------|-------------|
| `CAI` | Codon Adaptation Index - Measures adaptation to optimal codon usage |
| `ENC` | Effective Number of Codons - Measures codon usage evenness (20-61 range) |
| `GC_content` | Overall GC content of coding sequences |
| `GC1` | GC content at first codon position |
| `GC2` | GC content at second codon position |
| `GC3` | GC content at third codon position |
| `Context_bias` | Measures bias in codon context beyond individual codon frequencies |

#### HEG-specific Codon Features

| Feature | Description |
|---------|-------------|
| `HEG_CUB` | Codon Usage Bias in Highly Expressed Genes (HEGs) |
| `HEG_CPB` | Codon Pair Bias in HEGs |
| `HEG_Consistency` | Consistency of codon usage in HEGs |
| `HEG_RemoteInteraction` | Remote codon interaction in HEGs |
| `BG_CUB` | Codon Usage Bias in background genes |
| `BG_CPB` | Codon Pair Bias in background genes |
| `BG_Consistency` | Consistency of codon usage in background genes |
| `BG_RemoteInteraction` | Remote codon interaction in background genes |
| `delta_CUB` | Difference in CUB between HEGs and background genes |
| `delta_CPB` | Difference in CPB between HEGs and background genes |
| `delta_Consistency` | Difference in consistency between HEGs and background genes |
| `delta_RemoteInteraction` | Difference in remote interaction between HEGs and background genes |
| `CAI_HEG` | CAI using HEG as reference |
| `CAI_BP` | CAI using background genes as reference |
| `ENC_HEG` | Effective Number of Codons for Highly Expressed Genes (range 20-61, lower values indicate stronger bias) |
| `ENC_BP` | Effective Number of Codons for Background genes (range 20-61, lower values indicate stronger bias) |
| `RSCU_diff` | Difference in RSCU between HEGs and background genes |
| `codon_bias_HEG_BP` | Codon bias between HEGs and background genes |

### Amino Acid Features

#### Amino Acid Composition

| Feature | Description |
|---------|-------------|
| `aa_freq_A` to `aa_freq_Y` | Frequency of each amino acid (20 features) |

#### Physicochemical Properties

| Feature | Description |
|---------|-------------|
| `aa_prop_hydrophobic` | Proportion of hydrophobic amino acids |
| `aa_prop_polar` | Proportion of polar amino acids |
| `aa_prop_charged` | Proportion of charged amino acids |
| `aa_prop_positive` | Proportion of positively charged amino acids |
| `aa_prop_negative` | Proportion of negatively charged amino acids |
| `aa_charge_ratio` | Ratio of positive to negative charges |
| `aa_hydrophobicity_ratio` | Ratio of hydrophobic to polar amino acids |

#### Carbon and Nitrogen Content

| Feature | Description |
|---------|-------------|
| `aa_carbon_total` | Average total carbon atoms per amino acid |
| `aa_nitrogen_total` | Average total nitrogen atoms per amino acid |
| `aa_c_arsc` | Average carbon atoms per amino acid residue side chain (C-ARSC) |
| `aa_n_arsc` | Average nitrogen atoms per amino acid residue side chain (N-ARSC) |
| `aa_carbon_nitrogen_ratio` | Ratio of total carbon to total nitrogen atoms |
| `aa_c_arsc_n_arsc_ratio` | Ratio of C-ARSC to N-ARSC |

#### HEG-specific Amino Acid Features

| Feature | Description |
|---------|-------------|
| `HEG_aa_freq_A` to `HEG_aa_freq_Y` | Frequency of each amino acid in Highly Expressed Genes (HEGs) |
| `BG_aa_freq_A` to `BG_aa_freq_Y` | Frequency of each amino acid in background genes |
| `delta_aa_freq_A` to `delta_aa_freq_Y` | Difference in amino acid frequencies between HEGs and background genes |
| `HEG_aa_prop_hydrophobic` | Proportion of hydrophobic amino acids in HEGs |
| `BG_aa_prop_hydrophobic` | Proportion of hydrophobic amino acids in background genes |
| `delta_aa_prop_hydrophobic` | Difference in hydrophobic amino acid proportion between HEGs and background genes |
| `HEG_aa_c_arsc` | Average C-ARSC in HEGs |
| `BG_aa_c_arsc` | Average C-ARSC in background genes |
| `delta_aa_c_arsc` | Difference in C-ARSC between HEGs and background genes |
| `HEG_aa_n_arsc` | Average N-ARSC in HEGs |
| `BG_aa_n_arsc` | Average N-ARSC in background genes |
| `delta_aa_n_arsc` | Difference in N-ARSC between HEGs and background genes |
| `AAAI` | Amino Acid Adaptation Index - measures how well background genes match the amino acid usage bias in HEGs |
| `aa_bias_HEG_BP` | Euclidean distance between amino acid frequency vectors of HEGs and background genes |

### Genomic Features

#### Basic Genomic Features

| Feature | Description |
|---------|-------------|
| `genome_size` | Total size of the genome in base pairs |
| `gc_content` | Overall GC content of the genome |

#### Skew Metrics

| Feature | Description |
|---------|-------------|
| `gc_skew` | (G-C)/(G+C) - Measure of strand bias in G vs C |
| `at_skew` | (A-T)/(A+T) - Measure of strand bias in A vs T |
| `purine_pyrimidine_skew` | (A+G)-(C+T)/(A+G+C+T) - Purine vs pyrimidine bias |
| `keto_amino_skew` | (G+T)-(A+C)/(G+T+A+C) - Keto vs amino group bias |
| `strong_weak_skew` | (G+C)-(A+T)/(G+C+A+T) - Strong vs weak H-bond bias |

### Taxonomy Features

| Feature | Description |
|---------|-------------|
| `taxid_phylum` | Raw taxonomy ID of the phylum |
| `taxid_class` | Raw taxonomy ID of the class |
| `taxid_order` | Raw taxonomy ID of the order |
| `taxid_family` | Raw taxonomy ID of the family |
| `taxid_genus` | Raw taxonomy ID of the genus |
| `taxid_phylum_norm` | Normalized (log-scaled) taxonomy ID of the phylum |
| `taxid_class_norm` | Normalized (log-scaled) taxonomy ID of the class |
| `taxid_order_norm` | Normalized (log-scaled) taxonomy ID of the order |
| `taxid_family_norm` | Normalized (log-scaled) taxonomy ID of the family |
| `taxid_genus_norm` | Normalized (log-scaled) taxonomy ID of the genus |

### Pathway Features

#### Pathway Completeness

| Feature | Description |
|---------|-------------|
| `ko00010` to `ko09191` | Completeness of each KEGG pathway (578 pathways in total) with sigmoid-scaled normalization |

#### Pathway Category Metrics

| Feature | Description |
|---------|-------------|
| `category_carbohydrate_mean` | Mean completeness of carbohydrate metabolism pathways |
| `category_energy_mean` | Mean completeness of energy metabolism pathways |
| `category_lipid_mean` | Mean completeness of lipid metabolism pathways |
| `category_nucleotide_mean` | Mean completeness of nucleotide metabolism pathways |
| `category_amino_acid_mean` | Mean completeness of amino acid metabolism pathways |
| `category_glycan_mean` | Mean completeness of glycan metabolism pathways |
| `category_cofactor_mean` | Mean completeness of cofactor metabolism pathways |
| `category_xenobiotics_mean` | Mean completeness of xenobiotics biodegradation pathways |
| `category_terpenoid_mean` | Mean completeness of terpenoid metabolism pathways |
| `category_secondary_metabolites_mean` | Mean completeness of secondary metabolite biosynthesis pathways |


#### Analysis of Results with 100 Genomes
1. Growth Rate Prediction
**Top Codon Features for Growth Rate:**
- codon_bias_HEG_BP (1st overall, importance: 0.572) - This is the codon bias difference between highly expressed genes and background genes
- RSCU_diff (2nd overall, importance: 0.112) - Relative Synonymous Codon Usage difference between HEG and background genes
- HEG_CUB (3rd overall, importance: 0.058) - Codon Usage Bias in highly expressed genes
- delta_aa_freq_K (4th overall, importance: 0.018) - Difference in lysine frequency between HEG and background genes
- HEG_Consistency (5th overall, importance: 0.011) - Codon consistency in highly expressed genes
- delta_CUB (6th overall, importance: 0.009) - Difference in Codon Usage Bias between HEG and background genes
**Model Performance:**
- Training R²: 0.972, RMSE: 0.618
- Test R²: 0.837, RMSE: 1.587
The model shows good performance on both training and test sets, indicating that the features are indeed predictive of growth rate.
2. Optimal Temperature Prediction
**Top Codon Features for Temperature:**
- HEG_aa_freq_S (1st overall, importance: 0.254) - Serine frequency in highly expressed genes
- HEG_aa_freq_Y (2nd overall, importance: 0.127) - Tyrosine frequency in highly expressed genes
- HEG_aa_carbon_total (3rd overall, importance: 0.078) - Total carbon content in amino acids of highly expressed genes
- HEG_aa_c_arsc (5th overall, importance: 0.059) - Carbon atoms per amino acid residue side chain in highly expressed genes
- HEG_aa_freq_Q (6th overall, importance: 0.047) - Glutamine frequency in highly expressed genes
- BG_aa_freq_Q (7th overall, importance: 0.043) - Glutamine frequency in background genes
- aa_freq_Q (8th overall, importance: 0.026) - Overall glutamine frequency
**Model Performance:**
- Training R²: 0.941, RMSE: 3.698
- Test R²: 0.791, RMSE: 7.758
The model shows good performance, though slightly lower than for growth rate prediction.
3. Conclusions
- Codon Features Importance: The codon usage bias metrics now rank at the top of the feature importance list for both growth rate and optimal temperature prediction. This confirms that our fixes to the feature extraction have successfully addressed the issues with the codon features.
- HEG vs. Background Differences: The difference between highly expressed genes (HEG) and background genes (BG) is particularly important for growth rate prediction, with codon_bias_HEG_BP and RSCU_diff being the top two features. This aligns with the biological expectation that codon adaptation in highly expressed genes is a key determinant of growth rate.
- Amino Acid Composition: For temperature prediction, amino acid composition in highly expressed genes (especially serine, tyrosine, and glutamine) appears to be the most important factor. This suggests that the amino acid content of proteins, particularly in highly expressed genes, plays a crucial role in adaptation to different temperature environments.
- Carbon Content: The carbon content of amino acids in highly expressed genes (HEG_aa_carbon_total and HEG_aa_c_arsc) is an important predictor of optimal temperature, suggesting that the elemental composition of proteins is related to thermal adaptation.
- Model Performance: Both models show good performance, with R² values of 0.837 for growth rate and 0.791 for temperature on the test set. This indicates that the features we've extracted are indeed predictive of these phenotypes.
These results confirm that our fixes to the feature extraction have successfully addressed the issues with the codon features, and they now rank at the top of the feature importance list as expected based on previous studies. The difference between highly expressed genes and background genes is particularly important, highlighting the role of codon adaptation in microbial growth rate and temperature adaptation.


#### Analysis of Pathway Features with 100 Genomes

1. Pathway Features in Growth Rate Prediction:
- Pathway features do appear in the top 50 features for growth rate prediction, but they have relatively low importance values compared to codon features.
- The highest-ranked pathway feature for growth rate is ko00930 (Caprolactam degradation) with an importance of 0.0078, which is much lower than the top codon features (e.g., codon_bias_HEG_BP with 0.572).
- Other important pathways for growth rate include ko03010 (Ribosome), ko01232 (Nucleotide metabolism), and ko04152 (AMPK signaling pathway).
2. Pathway Features in Temperature Prediction:
- Pathway features appear to be more important for temperature prediction than for growth rate prediction.
- The highest-ranked pathway feature for temperature is ko03430 (Mismatch repair) with an importance of 0.060, which is the 4th most important feature overall.
- Other important pathways for temperature include ko05171 (Coronavirus disease), ko01320 (Aging), and ko01230 (Biosynthesis of amino acids).
3. Biological Interpretation:
- The higher importance of mismatch repair (ko03430) for temperature prediction makes biological sense, as DNA repair mechanisms are crucial for organisms living at high temperatures where DNA damage is more frequent.
- The presence of ribosome (ko03010) in the growth rate features also aligns with biological expectations, as ribosome abundance and efficiency are directly related to growth rate.
4. Relative Importance:
- While pathway features are present in both models, they are generally less important than codon features, especially for growth rate prediction.
- For growth rate, the top 6 features are all codon-related, with pathway features only appearing from the 7th position onwards.
- For temperature, while one pathway feature (ko03430) ranks 4th, the top 3 features are still codon-related.
5. Conclusion:
- The difference in pathway feature importance between growth rate and temperature prediction suggests that different biological mechanisms may be involved in these two adaptations.
- Temperature adaptation may rely more on specific metabolic and repair pathways, while growth rate is more directly influenced by codon optimization and translation efficiency.
- The fact that pathway features are present in both models, albeit with different importance, indicates that our feature extraction is working correctly for both types of features.
This analysis confirms that our model is capturing both codon-related and pathway-related features, with codon features being more important overall, especially for growth rate prediction. The biological relevance of the top pathway features (e.g., mismatch repair for temperature, ribosome for growth rate) further validates our approach.


## References

For more information on the features used in DeepMu, see the following references:

- Codon usage bias: Sharp PM, Li WH. The codon adaptation index--a measure of directional synonymous codon usage bias, and its potential applications. Nucleic Acids Res. 1987;15(3):1281-1295.
- Genomic features: Lobry JR. Asymmetric substitution patterns in the two DNA strands of bacteria. Mol Biol Evol. 1996;13(5):660-665.
- Pathway analysis: Kanehisa M, Goto S. KEGG: Kyoto Encyclopedia of Genes and Genomes. Nucleic Acids Res. 2000;28(1):27-30.
- C-ARSC and N-ARSC: Bragg JG, Hyder CL. Nitrogen versus carbon use in prokaryotic genomes and proteomes. Proc Biol Sci. 2004;271 Suppl 5(Suppl 5):S374-S377.


### CAI and tAI
Based on my analysis of the codebase, I can see that both CAI (Codon Adaptation Index) and tAI (tRNA Adaptation Index) are already implemented in the DeepMu features. Let me summarize the implementation status of each:

#### CAI (Codon Adaptation Index) Implementation:
- Implementation Status: ✅ Fully implemented
- Location:  deepmu/features/codon_features.py
- Key Methods:
   calculate_cai() (lines 1448-1516): Main method for calculating CAI
   _calculate_cai_inline() (lines 1865-1914): Fallback implementation
   _get_reference_weights() (lines 2485-2517): Gets reference weights for CAI calculation
   _calculate_weights_from_reference() (lines 2519-2605): Calculates weights from reference sequences
   _get_weights_from_rscu() (lines 2690-2724): Converts RSCU values to weights for CAI
- Usage in Features:
   - CAI is included in the advanced codon features
   - HEG-specific CAI features are calculated (CAI_HEG, CAI_BP)
   - CAI is used in the feature extraction pipeline
#### tAI (tRNA Adaptation Index) Implementation:
- Implementation Status: ✅ Fully implemented
- Location:  deepmu/features/rna_features.py
- Key Methods:
   calculate_tai() (lines 228-291): Main method for calculating tAI
   _calculate_effective_counts() (lines 415-429): Calculates effective tRNA counts
   _calculate_relative_adaptiveness() (lines 432-456): Calculates relative adaptiveness values
   _calculate_tai_for_gene() (lines 458-486): Calculates tAI for a single gene
- Usage in Features:
   tAI is included in the RNA features
   tAI statistics (mean, median, std, min, max) are calculated
   tAI is used in the feature extraction pipeline when tRNA files are available
- Conclusion:
Both CAI and tAI are already properly implemented in the DeepMu codebase. The implementations follow standard methods from the literature:
- CAI Implementation: Follows the Sharp & Li (1987) method, calculating the geometric mean of relative adaptiveness values for codons in a gene.
- tAI Implementation: Follows the dos Reis et al. (2004) method, considering tRNA gene copy numbers and wobble base pairing rules.
These features are already being used in the feature extraction pipeline and should be included in the model training. No additional implementation is needed for these features.

