# DeepMu v1.1.1: Advanced Microbial Phenotype Predictor

DeepMu is a comprehensive toolkit for predicting microbial growth rates and optimal growth temperatures from genomic data. Version 1.1.1 introduces significant enhancements to the core architecture and feature extraction capabilities:

- **Improved Hybrid Ensemble Architecture**: Combines Random Forest, XGBoost, LightGBM, and Neural Networks for growth rate prediction with Transformer models for temperature prediction
- **Advanced Regularization Techniques**: Implements L1/L2 regularization, dropout strategies, and early stopping for improved model stability
- **Multi-Branch Neural Networks**: Specialized feature processing pathways for different feature types
- **Enhanced Codon Features**: Improved analysis of codon usage patterns with HEG/Background gene differentiation
- **Pathway-Clustered Features**: KEGG pathway-based genomic feature engineering with correlation analysis
- **Protein Isoelectric Point Features**: Proteome-wide pI distribution analysis for improved predictions
- **Automated Feature Selection**: Pearson/Spearman correlation ranking with target-specific optimization
- **Comprehensive Documentation**: Reorganized documentation with detailed guides for each component

## Key Features

For detailed explanations of the core features, see our [Technical Documentation](docs/) directory.

- **Enhanced Hybrid Ensemble Architecture**: Combines four models (RF, XGBoost, LightGBM, and DNN) for growth rate prediction with Transformer for temperature prediction
- **Multi-Branch Neural Networks**: Specialized feature processing with target-specific branches
- **Deep Neural Network Integration**: Regularized DNN component in the growth rate ensemble for improved prediction accuracy
- **Advanced Regularization**: L1/L2 regularization, dropout strategies, and early stopping
- **Dual-Target Architecture**: Simultaneous growth rate and temperature prediction
- **Pathway Clustering**: KEGG-based feature engineering with cluster analysis
- **HEG/BP Comparison**: Enhanced codon adaptation metrics between highly expressed and background genes
- **Automated Feature Selection**: Pearson/Spearman correlation ranking for target optimization
- **Enhanced Codon Adaptation**: CAI optimization with HEG/BP prioritization
- **Pathway-Clustered Features**: KEGG pathway-based genomic feature engineering
- **Protein Isoelectric Point Features**: Proteome-wide pI distribution analysis
### Correlation-Based Feature Selection

The dual-target architecture employs sophisticated feature selection techniques:

```bash
# Feature selection workflow
python deepmu_cli.py feature select \
    --metadata metadata.tsv \
    --feature-dir features/ \
    --output-dir selected_features/ \
    --growth-rate-features 30 \
    --optimal-temperature-features 30 \
    --correlation-method pearson \
    --kegg-map pathway_mapping.txt \
    --heg-ko-list heg_ko_list.txt
```

- **Pathway-aware selection**: Features ranked within KEGG pathway clusters
- **Dual-target optimization**: Separate feature sets for growth rate and temperature prediction
- **Statistical validation**: Features validated through permutation testing
- **Cluster prioritization**: Pathway clusters ranked by average feature correlation
- **Hierarchical Taxonomy Support**: Multi-level taxonomic feature integration
- **KO Profile Similarity**: Community analysis through KO similarity matrices
- **Automated Preprocessing**: Integrated gene prediction and KO annotation
- **Robust Error Handling**: Comprehensive validation and fallback mechanisms

## Quick Start

### Installation

```bash
# Clone the repository
git clone https://github.com/username/DeepMu.git
cd DeepMu

# Create and activate a virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install core dependencies
pip install -r requirements.txt
pip install matplotlib-venn scikit-learn-extra  # For pathway clustering visualization

# Install Prodigal and KofamScan
# Prodigal: https://github.com/hyattpd/Prodigal
# KofamScan: https://github.com/takaram/kofam_scan

# Install training extensions
pip install -r requirements-train.txt

# Optional: Install ETE3 for taxonomy lookup
pip install ete3

# Prepare NCBI taxonomy database (required for taxonomy features)
python prepare_ncbi_taxonomy.py
```

### Preparing the NCBI Taxonomy Database

Before running feature extraction with taxonomy features, you need to prepare the NCBI taxonomy database:

```bash
# Download and prepare the NCBI taxonomy database
python prepare_ncbi_taxonomy.py

# Optionally specify a custom location for the database
python prepare_ncbi_taxonomy.py --output-dir /path/to/custom/location
```

This is a one-time process that downloads and prepares the NCBI taxonomy database for use with DeepMu. The database is quite large (several hundred MB), so the download and preparation can take some time.

### Basic Training

```bash
# Dual-target training with pathway clustering features
bash run_dual_target_training.sh \
  --growth-rate-features 30 \
  --optimal-temperature-features 30 \
  --correlation-method pearson \
  --hidden-dim 128 \
  --num-layers 2 \
  --kegg-map ./training_data/kegg/pathway_mapping.txt \
  --heg-ko-list ./training_data/kegg/heg_ko_list.txt

# Mixed-precision training with cluster features
bash run_dual_target_training.sh \
  --growth-rate-features 40 \
  --optimal-temperature-features 25 \
  --correlation-method spearman \
  --amp \
  --batch-size 256

# Mixed-precision training
python train_model.py --target both --amp --batch-size 256
```

### Basic Usage

DeepMu now features a streamlined workflow with enhanced preprocessing and prediction capabilities:

1. **Preprocess your genome** (optional but recommended):
```bash
# For a single genome with pathway clustering
python deepmu_cli.py preprocess genome.fna \
    --output-dir processed/ \
    --kofamscan-db /path/to/kofamscan_db \
    --kegg-map /path/to/pathway_mapping.txt \
    --heg-ko-list /path/to/heg_ko_list.txt \
    --genetic-code 11

# For multiple genomes
python deepmu_cli.py preprocess genomes_dir/ \
    --output-dir processed/ \
    --kofamscan-db /path/to/kofamscan_db \
    --batch

# For metagenomes
python deepmu_cli.py preprocess metagenome.fna \
    --output-dir processed/ \
    --kofamscan-db /path/to/kofamscan_db \
    --meta-mode
```

2. **Extract features** (optional, for training custom models):
```bash
# Extract features for multiple genomes
python deepmu_cli.py feature batch \
    --metadata metadata.tsv \
    --genome-dir genomes/ \
    --cds-dir cds_ffn/ \
    --faa-dir cds_faa/ \
    --ko-dir ko_files/ \
    --output-dir features/ \
    --combine \
    --advanced-codon-features
# The faa-dir parameter is used for both amino acid composition and protein isoelectric point (pI) features

# Example for feature batch
(venv) (DeepMu-env) shengwei@MacBookPro2022:~/GitHub/DeepMu-v1.1.1$ DeepMu feature batch --metadata ./training_data/metadata_10.tsv --genome-dir ./training_data/genomes/ --cds-dir ./training_data/cds_ffn/ --faa-dir ./training_data/cds_faa/ --ko-dir ./training_data/kegg/ko_files/ --output-dir ./results --combine --output-tsv ./results/combined_features.tsv --kegg-map ./training_data/kegg/pathway_mapping.txt --heg-ko-list ./training_data/kegg/heg_ko_list.txt  --advanced-codon-features --num-processes 8 --skip-existing


# Extract features for a single genome
python deepmu_cli.py feature single \
    --genome-id GCA_000219855.1 \
    --cds-file cds_ffn/GCA_000219855.1_cds.ffn \
    --faa-file cds_faa/GCA_000219855.1_cds.faa \
    --ko-file ko_files/GCA_000219855.1.tsv \
    --output-dir features/ \
    --output-tsv features/GCA_000219855.1_features.tsv \
    --advanced-codon-features
# The faa-file parameter is used for both amino acid composition and protein isoelectric point (pI) features

# Example for feature single
(venv) (DeepMu-env) shengwei@MacBookPro2022:~/GitHub/DeepMu-v1.1.1$ DeepMu feature single --genome-id GCA_000219855.1 --genome-file ./training_data/genomes/GCA_000219855.1.fna --cds-file ./training_data/cds_ffn/GCA_000219855.1_cds.ffn --faa-file ./training_data/cds_faa/GCA_000219855.1_cds.faa --ko-file ./training_data/kegg/ko_files/GCA_000219855.1_ko.tsv --taxid 1051632 --output-dir results/ --output-tsv GCA_000219855.1_features.tsv --kegg-map ./training_data/kegg/pathway_mapping.txt --heg-ko-list ./training_data/kegg/heg_ko_list.txt --advanced-codon-features
```

3. **Run predictions**:

#### Single Organism Prediction
```bash
# Using clustered pathway features
python deepmu_cli.py single processed/genome_cds.faa \
    --ko-file processed/genome_ko.tsv \
    --kegg-map /path/to/pathway_mapping.txt \
    --heg-ko-list /path/to/heg_ko_list.txt \
    --taxid 2 \
    --temp 37 \
    --correlation-method pearson \
    --output result.json

# Direct genome analysis
python deepmu_cli.py single genome.fna \
    --is-genome \
    --kofamscan-db /path/to/kofamscan_db \
    --output-dir processed/ \
    --temp 37 \
    --output result.json

# Using a pre-trained model with HEG features
python deepmu_cli.py single genome.fna \
    --is-genome \
    --kofamscan-db /path/to/kofamscan_db \
    --heg-ko-list /path/to/heg_ko_list.txt \
    --model-path /path/to/trained_model.pt \
    --output result.json

# With HEG analysis and pathway information
python deepmu_cli.py single genome.fna \
    --is-genome \
    --kofamscan-db /path/to/kofamscan_db \
    --heg-ko-list heg_ko_list.txt \
    --kegg-path /path/to/kegg.txt \
    --analyze \
    --output result.json
```

#### Community Prediction
```bash
# Basic community analysis
python deepmu_cli.py community metagenome.fna \
    --coverage coverage.tsv \
    --temp 37 \
    --output community_result.json

# With metagenomic assembly and KO annotation
python deepmu_cli.py community metagenome.fna \
    --coverage coverage.tsv \
    --contigs contigs.fna \
    --kofamscan-db /path/to/kofamscan_db \
    --output-dir processed/ \
    --output result.json

# With organism mapping and taxonomy
python deepmu_cli.py community metagenome.fna \
    --coverage coverage.tsv \
    --org-to-seq org_to_seq.tsv \
    --taxonomy-file taxonomy.tsv \
    --output mapped_community_result.json
```

#### Batch Processing
```bash
# Process multiple genomes
python deepmu_cli.py batch genomes_dir/ \
    --temp 37 \
    --output batch_results.json

# With preprocessing and taxonomy
python deepmu_cli.py batch genomes_dir/ \
    --is-genome \
    --kofamscan-db /path/to/kofamscan_db \
    --taxonomy-dir taxonomy_dir/ \
    --output-dir processed/ \
    --output results.json

# With KO directory for pathway analysis
python deepmu_cli.py batch genomes_dir/ \
    --ko-dir ko_files/ \
    --heg-ko-list heg_ko_list.txt \
    --kegg-path /path/to/kegg.txt \
    --output batch_results_heg.json
```

## Advanced Features

### Hybrid RF-Transformer Architecture

DeepMu v1.1.1 introduces a hybrid architecture that combines:

1. **Random Forest** for growth rate prediction (optimized for this specific task)
2. **Transformer** for temperature prediction (leverages sequence patterns effectively)

This specialized approach allows each prediction task to benefit from the architecture that works best for it. For more details, see the [Hybrid RF-Transformer Architecture](docs/hybrid_rf_transformer_architecture.md) documentation.

### Advanced Regularization Techniques

The new version implements sophisticated regularization strategies:

- **L1/L2 Regularization**: Prevents overfitting while maintaining feature importance
- **Dropout Strategies**: Adaptive dropout rates based on layer depth and feature type
- **Early Stopping**: Prevents overfitting by monitoring validation performance
- **Gradient Clipping**: Stabilizes training for improved convergence

For more information, see [Advanced Regularization Techniques](docs/advanced_regularization_techniques.md).

### Multi-Branch Neural Networks

The multi-branch architecture provides specialized processing pathways:

- **Codon Branch**: Processes codon usage patterns with dedicated layers
- **Phylogenetic Branch**: Handles taxonomic information with graph neural networks
- **Sequence Branch**: Analyzes k-mer patterns with convolutional layers
- **Pathway Branch**: Processes metabolic pathway information

Learn more in [Multi-Branch Neural Network](docs/multi_branch_neural_network.md).

### Enhanced Codon Features

DeepMu v1.1.1 includes advanced codon feature analysis:

- **Codon Adaptation Index (CAI)**: Measures how well adapted a gene's codon usage is to the most expressed genes
- **Effective Number of Codons (ENC)**: Measures the evenness of codon usage (range 20-61)
- **GC Content at Different Positions**: Measures GC content at 1st, 2nd, and 3rd codon positions
- **Codon Context Bias**: Measures non-random associations between adjacent codons
- **HEG-specific Metrics**: Calculates all the above metrics specifically for highly expressed genes

For details, see [Advanced Codon Features](docs/advanced_codon_features.md).

### Training Custom Models

DeepMu supports training custom models with different feature sets:

#### Train a Random Forest model:
```bash
python evaluate_rf_model.py --feature-file features_all/combined_features.tsv --metadata training_data/metadata.tsv --rf-model models/hybrid_rf_transformer/rf_model.joblib --output-dir models/rf_evaluation
```

#### Train a Transformer model:
```bash
python train_transformer.py --feature-file features_all/combined_features.tsv --metadata training_data/metadata.tsv --output-dir models/transformer --metrics-dir metrics/transformer
```

#### Train a hybrid RF-Transformer model:
```bash
python train_hybrid_rf_transformer.py --feature-file features_all/combined_features.tsv --metadata training_data/metadata.tsv --output-dir models/hybrid_rf_transformer --metrics-dir metrics/hybrid_rf_transformer
```

#### Train a basic model without hierarchical taxonomy or HEG features:
```bash
python train_models.py --metadata training_data/metadata.tsv \
                     --cds-dir training_data/cds_ffn \
                     --ko-dir training_data/ko_files \
                     --output models/basic_model.pt \
                     --batch-size 32 \
                     --epochs 100
```

The trained models can be used with the `--model-path` parameter in the prediction command.

### Hierarchical Taxonomy Support
DeepMu now supports hierarchical taxonomy integration through the `--taxid` parameter, which accepts an NCBI taxonomy ID and automatically retrieves the full lineage using ETE3:
```bash
--taxid 511145  # E. coli K-12 MG1655 taxonomy ID
```

Alternatively, you can provide a pipe-separated string of taxonomy IDs directly:
```bash
--taxid "1224|1236|91347|543|570"  # phylum|class|order|family|genus
```

### KO Profile Similarity
The enhanced community analysis includes KO profile similarity calculations:
```bash
python deepmu_cli.py community metagenome.fna \
    --coverage coverage.tsv \
    --ko-file ko_annotations.tsv \
    --output community_result.json
```

### HEG-based Feature Analysis
Highly Expressed Gene (HEG) analysis can be enabled using:
```bash
--heg-ko-list heg_ko_list.txt
```

## Specialized Hybrid Models

The specialized hybrid models are an advanced implementation that uses separate models for growth rate and optimal temperature prediction. Each model is optimized specifically for its target variable.

### Latest Improvements

We've implemented several key improvements to the specialized models:

1. **Cross-target feature selection** - Each model uses features that are relevant to its target while maintaining awareness of the other target (configurable weight)
2. **Enhanced architectures**:
   - Growth Rate Model: Wider architecture with multiple residual connections and advanced regularization
   - Temperature Model: Deep architecture with enhanced multi-head attention mechanisms and GELU activations
3. **Advanced training techniques**:
   - Stochastic Weight Averaging for better generalization
   - Gradient clipping to prevent exploding gradients
   - Batch normalization throughout the networks
4. **Model stacking** - Meta-models that learn from base model predictions and taxonomic information

### Performance

Our best specialized models achieve:
- Growth Rate Prediction: R² up to 0.70
- Optimal Temperature Prediction: R² up to 0.90
- Enhanced resistance to overfitting with more stable training

### Usage

Train the specialized models:
```bash
./run_specialized_models.sh
```

Run a quick test with fewer epochs:
```bash
./run_specialized_models.sh --test
```

Make predictions with trained models:
```bash
./predict_with_specialized_models.py --feature_file [path_to_features.tsv] --output_file predictions.tsv
```

Compare performance with joint model:
```bash
python compare_specialized_vs_joint.py --joint_model_dir ./models/optimized_tabular --specialized_model_dir ./models/specialized_hybrid
```

### Further Development

For detailed recommendations on further improving these models, see `specialized_hybrid_models_recommendations.md`.

## Command-Line Interface

DeepMu provides a comprehensive command-line interface with several subcommands for different tasks. Below is a detailed guide to all available subcommands and their options.

### Global Options

These options apply to most subcommands:

- `-v`, `--verbose`: Enable verbose logging for detailed output
- `--output`: Path to save the output results in JSON format
- `--output-dir`: Directory to store output files (default: ./results)

### `preprocess` Subcommand

Preprocess genome sequences with gene prediction and KO annotation.

```bash
python deepmu_cli.py preprocess INPUT [options]
```

**Arguments:**
- `INPUT`: Path to input genome FASTA file or directory containing multiple genomes

**Options:**
- `-o`, `--output-dir`: Directory to store output files
- `-k`, `--kofamscan-db`: Path to KofamScan database
- `-g`, `--genetic-code`: NCBI genetic code ID (default: 11 for bacterial)
- `-m`, `--meta-mode`: Use metagenome mode for gene prediction
- `-b`, `--batch`: Process multiple genomes in batch mode
- `-c`, `--cpu`: Number of CPU cores to use for KofamScan
- `--log-level`: Set the logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)

**Examples:**
```bash
# Preprocess a single genome
python deepmu_cli.py preprocess genome.fna --output-dir processed/ --kofamscan-db /path/to/kofamscan_db

# Preprocess multiple genomes in batch mode
python deepmu_cli.py preprocess genomes_dir/ --batch --output-dir processed/ --kofamscan-db /path/to/kofamscan_db

# Preprocess a metagenome with 8 CPU cores
python deepmu_cli.py preprocess metagenome.fna --meta-mode --cpu 8 --output-dir processed/ --kofamscan-db /path/to/kofamscan_db
```

### `single` Subcommand

Predict growth rate and optimal temperature for a single organism.

```bash
python deepmu_cli.py single CDS_FILE KO_FILE [options]
```

**Arguments:**
- `CDS_FILE`: Path to CDS sequences in FASTA format
- `KO_FILE`: Path to KO annotations file

**Options:**
- `-t`, `--temp`: Growth temperature in Celsius (default: 37.0)
- `--no-temp-predict`: Disable optimal temperature prediction
- `-o`, `--output-dir`: Directory to store output files
- `-k`, `--kofamscan-db`: Path to KofamScan database for hierarchical taxonomy features
- `--taxid`: NCBI taxonomy ID or taxonomy string in pipe-separated format
- `--output`: Path to output JSON file
- `--heg-ko-list`: Path to file containing list of KO IDs for highly expressed genes
- `--model-path`: Path to pretrained model file
- `--advanced-features`: Use advanced codon features for analysis

**Examples:**
```bash
# Basic prediction with taxonomy information
python deepmu_cli.py single genome_cds.faa genome_ko.tsv --taxid 511145 --temp 37.0 --output results.json

# Using a custom model with advanced features
python deepmu_cli.py single genome_cds.faa genome_ko.tsv --model-path models/custom_model.pt --advanced-features --output results.json

# Disable temperature prediction
python deepmu_cli.py single genome_cds.faa genome_ko.tsv --no-temp-predict --output growth_only_results.json
```

### `batch` Subcommand

Process multiple genomes in batch mode.

```bash
python deepmu_cli.py batch INPUT_DIR [options]
```

**Arguments:**
- `INPUT_DIR`: Directory containing input genome files

**Options:**
- `-o`, `--output-dir`: Directory to store output files
- `-k`, `--kofamscan-db`: Path to KofamScan database
- `-m`, `--meta-mode`: Use metagenome mode for gene prediction
- `-t`, `--temp`: Growth temperature in Celsius (default: 37.0)
- `--no-temp-predict`: Disable optimal temperature prediction
- `--taxid`: NCBI taxonomy ID or taxonomy string in pipe-separated format
- `--heg-ko-list`: Path to file containing list of KO IDs for highly expressed genes
- `--model-path`: Path to pretrained model file
- `--advanced-features`: Use advanced codon features for analysis

**Examples:**
```bash
# Process multiple genomes with default parameters
python deepmu_cli.py batch genomes_dir/ --output-dir results/

# Process genomes with custom model and HEG features
python deepmu_cli.py batch genomes_dir/ --model-path models/custom_model.pt --heg-ko-list heg_ko_list.txt --output-dir results/

# Process genomes with metagenome mode
python deepmu_cli.py batch metagenomes_dir/ --meta-mode --output-dir results/ --kofamscan-db /path/to/kofamscan_db
```

### `community` Subcommand

Predict growth rates for a microbial community.

```bash
python deepmu_cli.py community INPUT [options]
```

**Arguments:**
- `INPUT`: Path to input metagenome FASTA file

**Options:**
- `--coverage`: Path to coverage file for abundance information
- `--contigs`: Path to contigs file
- `--taxonomy-file`: Path to taxonomy mapping file
- `-o`, `--output-dir`: Directory to store output files
- `-k`, `--kofamscan-db`: Path to KofamScan database
- `--ko-file`: Path to pre-computed KO annotations
- `--ko-lookup`: Path to KO lookup file
- `--org-to-seq`: Path to organism to sequence mapping file
- `--heg-ko-list`: Path to HEG KO list file
- `--temp`: Temperature for growth rate prediction
- `--no-temp-predict`: Disable temperature prediction
- `--model-path`: Path to pretrained model file
- `--output`: Path to output JSON file
- `--advanced-features`: Use advanced codon features for analysis

**Examples:**
```bash
# Basic community prediction with coverage information
python deepmu_cli.py community metagenome.fna --coverage coverage.tsv --output community_results.json

# Community prediction with taxonomy mapping
python deepmu_cli.py community metagenome.fna --coverage coverage.tsv --taxonomy-file taxonomy.tsv --output community_results.json

# Community prediction with pre-computed KO annotations
python deepmu_cli.py community metagenome.fna --coverage coverage.tsv --ko-file ko_annotations.tsv --output community_results.json
```

### `feature` Subcommand

Extract and analyze features from genome sequences.

```bash
python deepmu_cli.py feature [options]
```

**Options:**
Refer to the feature extraction documentation for detailed options.

**Examples:**
```bash
# Extract codon usage features
python deepmu_cli.py feature codon genome_cds.faa --output codon_features.json

# Extract taxonomic features
python deepmu_cli.py feature taxonomy --taxid 511145 --output taxonomy_features.json

# Extract KO similarity features
python deepmu_cli.py feature ko genome_ko.tsv --output ko_features.json
```

### HEG Analysis

DeepMu automatically analyzes Highly Expressed Genes (HEGs) during feature extraction. When you run the feature extraction command with the `--heg-ko-list` parameter, DeepMu will:

1. Identify HEG sequences based on KO annotations
2. Calculate the ratio of HEG sequences to total sequences
3. Add HEG analysis results as features for model training

These HEG analysis features include:
- `heg_total_sequences`: Total number of sequences in the genome
- `heg_count`: Number of HEG sequences identified
- `bg_count`: Number of background sequences
- `heg_ratio`: Ratio of HEG sequences to total sequences

This analysis is performed automatically as part of the feature extraction process, so you don't need to run a separate command.

## Troubleshooting Guide

### Common Issues and Solutions

1. **Gene Prediction Failures**
   - **Problem**: Prodigal fails to predict genes
   - **Solution**:
     - Check genetic code parameter (`--genetic-code`)
     - Ensure input file is in correct format
     - Try different Prodigal parameters (`--meta` for metagenomes)

2. **KO Annotation Issues**
   - **Problem**: KofamScan fails to annotate genes
   - **Solution**:
     - Verify KofamScan database path
     - Check database version compatibility
     - Ensure sufficient memory allocation

3. **Taxonomy Processing Errors**
   - **Problem**: Invalid taxonomy string format
   - **Solution**:
     - Verify taxonomy string format (pipe-separated IDs)
     - Check taxonomy IDs against NCBI database
     - Make sure ETE3 is installed for automatic taxonomy retrieval
     - Use `--verbose` for detailed error information

4. **Memory Issues**
   - **Problem**: Out of memory during processing
   - **Solution**:
     - Use `--batch-size` parameter
     - Process in smaller chunks
     - Increase system memory or use swap

5. **Model Loading Errors**
   - **Problem**: Failed to load model
   - **Solution**:
     - Check model file integrity
     - Verify model version compatibility
     - Reinstall dependencies

### Debugging Tips

1. **Verbose Output**
   ```bash
   python deepmu_cli.py single genome.fna --verbose
   ```

2. **Log File Generation**
   ```bash
   python deepmu_cli.py single genome.fna --log-file debug.log
   ```

3. **Intermediate File Retention**
   ```bash
   python deepmu_cli.py single genome.fna --keep-temp
   ```

4. **Memory Profiling**
   ```bash
   python deepmu_cli.py single genome.fna --profile-memory
   ```

## Error Handling

DeepMu provides comprehensive error handling for various scenarios:

```python
try:
    result = predictor.predict_single("genome.fna")
except InputValidationError as e:
    print(f"Input validation failed: {e}")
except ModelLoadError as e:
    print(f"Failed to load model: {e}")
except FeatureCalculationError as e:
    print(f"Feature calculation error: {e}")
except SequenceProcessingError as e:
    print(f"Failed to process sequences: {e}")
except GenePredictionError as e:
    print(f"Failed to predict genes: {e}")
except KOAnnotationError as e:
    print(f"Failed to annotate KO terms: {e}")
except PreprocessingError as e:
    print(f"Preprocessing failed: {e}")
except PredictionError as e:
    print(f"Prediction failed: {e}")
except DeepMuError as e:
    print(f"DeepMu error: {e}")
```

## Documentation

DeepMu v1.1.1 provides comprehensive documentation organized in the `docs/` directory:

- [Documentation Guide](docs/README.md): Overview of all available documentation
- [User Guide](docs/user_guide.md): Detailed instructions for using DeepMu
- [Developer Guide](docs/developer_guide.md): Information for developers who want to understand or extend DeepMu
- [Wiki](WIKI.md): Additional documentation and examples

### Feature Extraction Documentation
- [Feature Extraction Overview](docs/feature_extraction.md): Comprehensive guide to feature extraction in DeepMu
- [Modular Feature Extraction](docs/modular_feature_extraction.md): Details on the modular approach in v1.1.1
- [Genomic Features](docs/genomic_features.md): Guide to genomic feature extraction and troubleshooting

### Model Documentation
- [Hybrid RF-Transformer Model](docs/hybrid_rf_transformer.md): Details on the hybrid model architecture in v1.1.1
- [Random Forest Model](docs/random_forest_model.md): Information about the Random Forest component
- [Transformer Model](docs/transformer_model.md): Information about the Transformer component
- [Ensemble Approach](docs/ensemble_approach.md): Details on model ensembling techniques
- [Advanced Regularization](docs/advanced_regularization.md): Information about regularization techniques
- [Multi-Branch Architecture](docs/multi_branch_architecture.md): Details on the multi-branch neural network architecture

## Citation

If you use DeepMu in your research, please cite:

```bibtex
@article{deepmu2024,
  title={DeepMu: Enhanced Microbial Growth Rate and Temperature Predictor with Hybrid RF-Transformer Architecture},
  author={Your Name},
  journal={Journal Name},
  volume={},
  pages={},
  year={2024},
  publisher={},
  doi={}
}
```

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Model Architecture

DeepMu uses a hybrid approach that combines:

1. **DNN (Deep Neural Network)** for growth rate prediction
   - Uses log2 transformation for growth rate values
   - Features selected using both correlation and model-based importance methods
   - Optimized architecture with regularization to prevent overfitting

2. **Random Forest** for temperature prediction
   - Uses raw temperature values (no transformation)
   - Features selected specifically for temperature prediction
   - Optimized hyperparameters for best performance

This hybrid approach allows each component to specialize in its prediction task, resulting in overall better performance.

## Performance

- Growth Rate: R² > 0.95
- Temperature: R² > 0.85

## Training

To train the model with the recommended configuration:

```bash
./train_deepmu_final.sh
```

## Usage

```python
from rf_temp_hybrid_model import HybridModel

# Load a trained model
model = HybridModel.load(model_dir="models/deepmu_final_model")

# Make predictions
growth_rates, temperatures = model.predict(features)
```
