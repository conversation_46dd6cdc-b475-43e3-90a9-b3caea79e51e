# Improved Hybrid Model for Microbial Growth Rate and Optimal Temperature Prediction

This repository contains an improved hybrid model for predicting microbial growth rate and optimal temperature from genomic features.

## Overview

The improved hybrid model addresses the limitations of the original hybrid model, which had negative R² values for both growth rate and optimal temperature prediction. The new model uses a combination of advanced feature selection techniques and specialized models for each target variable.

## Components

The improved hybrid model consists of the following components:

1. **Improved Feature Selection (`improved_feature_selection.py`)**
   - Implements multiple feature selection methods (variance, importance, RFE, mutual information)
   - Evaluates and compares different feature selection methods
   - Selects the optimal features for each target variable
   - Provides visualization of feature importance

2. **Improved Growth Rate Model (`improved_growth_model.py`)**
   - Uses a stacking ensemble approach with bidirectional learning
   - Combines Random Forest, XGBoost, and LightGBM as base models
   - Creates meta-features with model interactions for better predictions
   - Implements cross-validation for robust model training
   - Provides comprehensive evaluation metrics and visualizations

3. **Improved Temperature Model (`improved_temperature_model.py`)**
   - Uses an optimized Random Forest model for temperature prediction
   - Implements feature scaling for better performance
   - Provides hyperparameter optimization
   - Includes comprehensive evaluation metrics and visualizations

4. **Combined Hybrid Model (`improved_hybrid_model.py`)**
   - Integrates the feature selection, growth rate model, and temperature model
   - Prepares data specifically for each target variable
   - Trains and evaluates models separately for each target
   - Provides comprehensive evaluation and visualization

## Usage

To run the improved hybrid model, use the following command:

```bash
./run_improved_hybrid_model.sh
```

This will:
1. Load the feature and metadata files
2. Perform feature selection for each target variable
3. Train the growth rate and temperature models
4. Evaluate the models on test data
5. Save the models, metrics, and visualizations to the output directory

## Comparison with Original Model

To compare the performance of the improved hybrid model with the original model, use the following command:

```bash
python compare_models_performance.py
```

This will:
1. Load the metrics from both models
2. Create a comparison table
3. Generate comparison plots
4. Save the results to the output directory

## Results

The improved hybrid model significantly outperforms the original hybrid model:

- **Growth Rate Prediction**:
  - Original Model: R² = -0.1605, RMSE = 4.1658
  - Improved Model: R² = 0.3637, RMSE = 5.1505

- **Optimal Temperature Prediction**:
  - Original Model: R² = -0.0746, RMSE = 12.2842
  - Improved Model: R² = 0.8415, RMSE = 4.8873

## Future Improvements

Potential future improvements include:

1. **Deep Learning Integration**: Incorporate deep learning models for feature extraction
2. **Transfer Learning**: Use pre-trained models from related domains
3. **Ensemble Diversity**: Increase the diversity of base models in the ensemble
4. **Feature Engineering**: Create more advanced feature interactions
5. **Hyperparameter Optimization**: Use more advanced optimization techniques like Bayesian optimization

## Requirements

- Python 3.8+
- scikit-learn
- XGBoost
- LightGBM
- pandas
- numpy
- matplotlib
- seaborn
- shap

## License

This project is licensed under the MIT License - see the LICENSE file for details.
