# Hybrid RF-Transformer Model

## Overview

DeepMu v1.1.1 introduces a hybrid model architecture that combines the strengths of Random Forest (RF) and Transformer models for microbial phenotype prediction. This approach leverages:

- **Random Forest** for growth rate prediction - Excels with tabular data and captures non-linear relationships
- **Transformer architecture** for temperature prediction - Provides superior performance with complex pattern recognition

## Architecture

The hybrid model consists of two specialized components:

### 1. Random Forest Component

The Random Forest model is specifically optimized for growth rate prediction:

- Uses ensemble of decision trees (default: 200 trees)
- Handles feature importance naturally
- Robust to outliers and noise in the data
- Works well with the mixed feature types in DeepMu

Configuration parameters:
- `rf-n-estimators`: Number of trees in the forest (default: 200)
- `rf-max-depth`: Maximum depth of the trees (default: 20)
- `rf-min-samples-split`: Minimum samples required to split a node (default: 2)
- `rf-min-samples-leaf`: Minimum samples required at a leaf node (default: 1)

### 2. Transformer Component

The Transformer model is specialized for optimal temperature prediction:

- Multi-head self-attention mechanism
- Positional encoding for feature ordering
- Residual connections and layer normalization
- Batch normalization for improved training stability

Configuration parameters:
- `transformer-hidden-dims`: Dimensions of hidden layers (default: [256, 128, 64])
- `transformer-dropout-rates`: Dropout rates for regularization (default: [0.2, 0.3, 0.4])
- `transformer-heads`: Number of attention heads (default: 4)
- `transformer-layers`: Number of transformer layers (default: 2)

## Implementation Details

The hybrid model implementation is found in the following files:

- `deepmu/models/hybrid_rf_transformer.py`: Core model implementation
- `train_hybrid_rf_transformer.py`: Training script

The model integrates both approaches through a unified API while maintaining separate training processes optimized for each target variable.

### Feature Processing

The hybrid model uses different feature subsets for each target:

1. **Growth Rate Features**: Selected based on correlation with growth rate
   - Codon usage features
   - KEGG pathway completeness
   - Taxonomic features
   
2. **Temperature Features**: Selected based on correlation with optimal temperature
   - Amino acid composition features
   - Protein isoelectric point distribution
   - GC content and genomic features

## Performance Advantages

The hybrid approach offers several advantages:

1. **Specialized Architecture**: Each target variable benefits from the most suitable algorithm
2. **Improved Accuracy**: Better performance compared to using either approach alone
3. **Explainability**: Random Forest provides feature importance scores for growth rate
4. **Flexibility**: Easy to adjust parameters for each component independently

## Usage

To train a hybrid RF-Transformer model, use the `train_hybrid_rf_transformer.py` script:

```bash
python train_hybrid_rf_transformer.py \
    --feature-file features_all/combined_features.tsv \
    --metadata training_data/metadata.tsv \
    --output-dir models/hybrid_rf_transformer \
    --metrics-dir metrics/hybrid_rf_transformer \
    --rf-n-estimators 200 \
    --transformer-hidden-dims 256 128 64 \
    --transformer-heads 4
```

### Hyperparameter Tuning

For optimal performance, we recommend tuning the following parameters:

- Random Forest: `rf-n-estimators`, `rf-max-depth`
- Transformer: `transformer-hidden-dims`, `transformer-heads`, `learning-rate`

## Metrics and Evaluation

The hybrid model is evaluated using multiple metrics:

- **Growth Rate**: R², MSE, MAE, RMSE
- **Optimal Temperature**: R², MSE, MAE, RMSE

Performance metrics are saved to the specified metrics directory during training.

## Model Integration

The hybrid model seamlessly integrates with the existing DeepMu prediction workflow:

```python
from deepmu.models.hybrid_rf_transformer import HybridRFTransformer

# Load trained model
model = HybridRFTransformer.load("models/hybrid_rf_transformer")

# Make predictions
growth_rate, temperature = model.predict(features)
```

## Conclusion

The hybrid RF-Transformer approach represents a significant advancement in microbial phenotype prediction, providing improved accuracy and flexibility compared to single-model approaches. By leveraging the strengths of both Random Forest and Transformer architectures, DeepMu v1.1.1 delivers state-of-the-art prediction performance.

## References

- `train_hybrid_rf_transformer.py`: Training script for the hybrid model
- `deepmu/models/hybrid_rf_transformer.py`: Model implementation
- `docs/random_forest_model.md`: Details on the RF model component
- `docs/transformer_model.md`: Details on the Transformer model component
