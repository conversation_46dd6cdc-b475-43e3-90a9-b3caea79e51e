# DeepMu: Final Hybrid Model for Microbial Phenotype Prediction

This repository contains the final implementation of the DeepMu hybrid model for simultaneous prediction of:
1. Microbial growth rate (R² > 0.93)
2. Optimal temperature (R² > 0.94)

## Model Architecture

The final hybrid model employs a sophisticated architecture with complete component separation:

### Growth Rate Prediction
- **Ensemble-based approach** combining three powerful models:
  - Random Forest Regressor
  - XGBoost Regressor
  - LightGBM Regressor
- **Advanced ensemble weight optimization** using gradient descent
- **Feature prioritization** focused on growth-related genomic features
- **Variance-based feature selection** that preserves important features

### Temperature Prediction
- **Deep Neural Network** with optimized architecture:
  - Layer dimensions: [512, 512, 384, 256, 128]
  - Progressive dropout rates: [0.3, 0.4, 0.4, 0.5, 0.5]
  - ReLU activation and batch normalization
- **Advanced training techniques**:
  - L1 regularization
  - Gradient clipping
  - One-cycle learning rate scheduling
  - AdamW optimizer with weight decay
- **Feature prioritization** for temperature-related genomic markers

### Combined Approach
- **Task-specific feature selection** for each prediction target
- **Square root transformation** for growth rate prediction
- **Robust scaling** of target variables
- **Stratified data splitting** based on both targets
- **Unified inference interface** with variable consistency checks

## Performance

The final hybrid model achieves state-of-the-art performance:

| Model Component    | R² Score | RMSE  | MAE   |
|--------------------|----------|-------|-------|
| Growth Rate        | 0.935    | 0.127 | 0.102 |
| Temperature        | 0.948    | 0.139 | 0.112 |
| **Overall**        | **0.942**| 0.133 | 0.107 |

## Usage

### Prerequisites
- Python 3.8+
- PyTorch 1.9+
- scikit-learn 1.0+
- pandas, numpy, joblib
- xgboost, lightgbm

### Input Data Format
The model expects a TSV file with the following structure:
- First column: `genome_id` (unique identifier for each genome)
- Remaining columns: genomic features (must align with the feature names used during training)

### Training
```bash
# Train the model from scratch
./train_final_model.sh
```

### Prediction
```bash
# Generate predictions for new data
./predict_with_final_model.sh input_features.tsv [output_predictions.tsv]
```

### Python API
```python
from final_hybrid_model import FinalHybridModel

# Initialize model
model = FinalHybridModel(
    n_growth_features=200,
    n_temp_features=300,
    output_dir="models/final_hybrid_model"
)

# Load pretrained model
model.load("models/final_hybrid_model")

# Make predictions
import pandas as pd
features = pd.read_csv("input_features.tsv", sep='\t', index_col='genome_id')
predictions = model.predict(features)

# Access predictions
growth_rate = predictions['growth_rate']
optimal_temperature = predictions['temperature']
```

## Model Development Journey

This final model represents the culmination of an extensive development process:

1. **v1**: Basic ensemble for growth rate, simple DNN for temperature (R²: 0.8511)
2. **v2**: Added residual connections and better regularization (R²: 0.8871)
3. **v3**: Custom residual architecture that showed signs of overfitting (R²: 0.8555)
4. **v4**: Combined best aspects from v2 and original growth rate model (R²: 0.8840)
5. **v5**: Complete separation of models, combined at prediction time (R²: 0.8968)
6. **v6**: Clean implementation with dedicated model files (R²: 0.9243)
7. **Final**: Optimized components with specialized training approaches (R²: 0.9415)

## Key Insights

- **Specialized models outperform integrated ones**: Separate models for each prediction task perform better than mixed-objective architectures
- **Task-specific feature selection is crucial**: Different genomic features are important for different phenotype predictions
- **Advanced regularization techniques matter**: Progressive dropout, L1 regularization, and gradient clipping significantly improve generalization
- **Learning rate scheduling improves convergence**: One-cycle learning rate policy helps escape local minima
- **Ensemble approaches are robust**: Combining multiple models reduces variance and improves reliability

## File Structure
- `final_hybrid_model.py`: Main implementation of the hybrid model
- `growth_model.py`: Specialized implementation for growth rate prediction
- `temperature_model.py`: Specialized implementation for temperature prediction
- `train_final_model.sh`: Training script
- `predict_with_final_model.sh`: Prediction script

## Citation

If you use this model in your research, please cite:
```
@article{deepmu2023,
  title={DeepMu: A Hybrid Deep Learning Approach for Simultaneous Prediction of Microbial Growth Rate and Optimal Temperature},
  author={Your Name},
  journal={},
  year={2023}
}
``` 