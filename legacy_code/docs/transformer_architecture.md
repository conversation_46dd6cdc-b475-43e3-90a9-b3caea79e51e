# Hybrid Multi-Branch Transformer Architecture

This document describes the hybrid multi-branch transformer architecture implemented in DeepMu for microbial growth rate and optimal growth temperature prediction.

## Overview

The hybrid multi-branch transformer architecture combines the interpretability of separate branches for different feature types with the powerful modeling capabilities of transformers for feature fusion. This approach leverages the strengths of transformers for modeling complex feature interactions while maintaining the efficiency and interpretability of the multi-branch structure.

![Hybrid Transformer Architecture](../assets/transformer_architecture.png)

## Key Components

### 1. Feature Branches

Each feature type is processed through a dedicated branch with the following components:

- **Input Layer**: Takes features of a specific type (codon, amino acid, genomic, etc.)
- **Hidden Layers**: Two fully-connected layers with batch normalization, ReLU activation, and dropout
- **Residual Connections**: Skip connections to improve gradient flow
- **Output Layer**: Projects to a common embedding dimension

### 2. Transformer Fusion

Features from different branches are fused using a transformer encoder:

- **Positional Encoding**: Adds positional information to feature embeddings (optional)
- **Multi-Head Self-Attention**: Models complex interactions between features from different branches
- **Layer Normalization**: Normalizes the transformer output
- **Residual Connection**: Adds the original features to the transformer output
- **Branch Importance Weights**: Learnable weights for each branch

### 3. Fusion Network

The fused features are processed through a fusion network:

- **Multiple Layers**: Several fully-connected layers with batch normalization, ReLU activation, and dropout
- **Decreasing Dimensions**: Gradually reduces the dimension to the output size

### 4. Output Heads

For multi-task learning, separate output heads are used:

- **Growth Rate Head**: Predicts microbial growth rate
- **Temperature Head**: Predicts optimal growth temperature

## Transformer Fusion Details

The transformer fusion module is the key innovation in this architecture. It uses a transformer encoder to model complex interactions between features from different branches:

1. **Input**: Features from different branches [batch_size, num_branches, embed_dim]
2. **Positional Encoding**: Adds positional information to feature embeddings (optional)
3. **Transformer Encoder**: Processes features through multiple transformer layers
   - **Multi-Head Self-Attention**: Allows each branch to attend to all other branches
   - **Feed-Forward Network**: Applies non-linear transformations to attention outputs
   - **Layer Normalization**: Normalizes outputs for stable training
   - **Residual Connections**: Improves gradient flow
4. **Branch Weighting**: Applies learnable weights to each branch
5. **Output**: Weighted sum of transformer outputs [batch_size, embed_dim]

## Advantages Over Multi-Branch Architecture

The hybrid transformer architecture offers several advantages over the standard multi-branch architecture:

1. **More Powerful Feature Interaction Modeling**: The transformer can capture complex, higher-order interactions between features from different branches.
2. **Dynamic Feature Importance**: The self-attention mechanism dynamically weights the importance of different features based on the input.
3. **Global Context**: The transformer can model global dependencies without regard to feature order.
4. **Improved Performance**: The transformer fusion typically leads to better performance on both growth rate and temperature prediction tasks.

## Feature Types

The model supports the following feature types:

1. **Codon Features**: Codon usage bias, codon pair bias, CAI, etc.
2. **Amino Acid Features**: Amino acid composition, physicochemical properties, etc.
3. **Genomic Features**: Genome size, GC content, dinucleotide frequencies, etc.
4. **RNA Features**: tRNA and rRNA counts, tAI, etc.
5. **Protein pI Features**: Isoelectric point features
6. **Taxonomy Features**: Taxonomic classification features

## Training

The model is trained using the following components:

- **Loss Function**: Mean Squared Error (MSE) for regression
- **Multi-Task Learning**: Weighted sum of growth rate and temperature losses
- **Optimizer**: AdamW with weight decay
- **Learning Rate Scheduler**: ReduceLROnPlateau
- **Early Stopping**: Based on validation loss

## Usage

To train a hybrid transformer model, use the `train_transformer.py` script:

```bash
python train_transformer.py \
    --feature-file features_all/combined_features.tsv \
    --metadata training_data/metadata.tsv \
    --output-dir models/transformer \
    --metrics-dir metrics/transformer \
    --batch-size 32 \
    --epochs 100 \
    --hidden-dims 256 128 64 \
    --dropout-rates 0.2 0.3 0.4 \
    --transformer-heads 4 \
    --transformer-layers 2 \
    --fusion-layers 2
```

## Hyperparameter Tuning

The hybrid transformer architecture has several hyperparameters that can be tuned:

- **hidden_dims**: Dimensions of hidden layers in branches and fusion network
- **dropout_rates**: Dropout rates for different parts of the network
- **transformer_heads**: Number of attention heads in transformer
- **transformer_layers**: Number of transformer layers
- **use_positional_encoding**: Whether to use positional encoding in transformer
- **fusion_layers**: Number of layers in the fusion network

## Implementation Details

The implementation is available in the following files:

- `deepmu/models/transformer_network.py`: Core implementation of the hybrid transformer architecture
- `train_transformer.py`: Training script for the hybrid transformer model

## References

1. Vaswani, A., et al. (2017). Attention is all you need. *Advances in neural information processing systems*.
2. Devlin, J., et al. (2018). BERT: Pre-training of deep bidirectional transformers for language understanding. *arXiv preprint arXiv:1810.04805*.
3. He, K., et al. (2016). Deep residual learning for image recognition. *Proceedings of the IEEE conference on computer vision and pattern recognition*.
4. Kendall, A., et al. (2018). Multi-task learning using uncertainty to weigh losses for scene geometry and semantics. *Proceedings of the IEEE conference on computer vision and pattern recognition*.
