# DeepMu v1.1.1: Advanced Microbial Phenotype Predictor

DeepMu is a comprehensive toolkit for predicting microbial growth rates and optimal growth temperatures from genomic data. Version 1.1.1 introduces significant enhancements to the core architecture and feature extraction capabilities:

- **Hybrid RF-Transformer Architecture**: Combines Random Forest for growth rate prediction with Transformer models for temperature prediction
- **Advanced Regularization Techniques**: Implements L1/L2 regularization, dropout strategies, and early stopping for improved model stability
- **Multi-Branch Neural Networks**: Specialized feature processing pathways for different feature types
- **Enhanced Codon Features**: Improved analysis of codon usage patterns with HEG/Background gene differentiation
- **Pathway-Clustered Features**: KEGG pathway-based genomic feature engineering with correlation analysis
- **Protein Isoelectric Point Features**: Proteome-wide pI distribution analysis for improved predictions
- **Automated Feature Selection**: Pearson/Spearman correlation ranking with target-specific optimization
- **Comprehensive Documentation**: Reorganized documentation with detailed guides for each component

## Key Features

For detailed explanations of the core features, see our [Technical Documentation](docs/) directory.

- **Hybrid RF-Transformer Architecture**: Combines Random Forest for growth rate prediction with Transformer for temperature prediction
- **Multi-Branch Neural Networks**: Specialized feature processing with target-specific branches
- **Advanced Regularization**: L1/L2 regularization, dropout strategies, and early stopping
- **Dual-Target Architecture**: Simultaneous growth rate and temperature prediction
- **Pathway Clustering**: KEGG-based feature engineering with cluster analysis
- **HEG/BP Comparison**: Enhanced codon adaptation metrics between highly expressed and background genes
- **Automated Feature Selection**: Pearson/Spearman correlation ranking for target optimization
- **Enhanced Codon Adaptation**: CAI optimization with HEG/BP prioritization
- **Pathway-Clustered Features**: KEGG pathway-based genomic feature engineering
- **Protein Isoelectric Point Features**: Proteome-wide pI distribution analysis

### Correlation-Based Feature Selection

The dual-target architecture employs sophisticated feature selection techniques:

```bash
# Feature selection workflow
python deepmu_cli.py feature select \
    --metadata metadata.tsv \
    --feature-dir features/ \
    --output-dir selected_features/ \
    --growth-rate-features 30 \
    --optimal-temperature-features 30 \
    --correlation-method pearson \
    --kegg-map pathway_mapping.txt \
    --heg-ko-list heg_ko_list.txt
```

- **Pathway-aware selection**: Features ranked within KEGG pathway clusters
- **Dual-target optimization**: Separate feature sets for growth rate and temperature prediction
- **Statistical validation**: Features validated through permutation testing
- **Cluster prioritization**: Pathway clusters ranked by average feature correlation
- **Hierarchical Taxonomy Support**: Multi-level taxonomic feature integration
- **KO Profile Similarity**: Community analysis through KO similarity matrices
- **Automated Preprocessing**: Integrated gene prediction and KO annotation
- **Robust Error Handling**: Comprehensive validation and fallback mechanisms

## Quick Start

### Installation

```bash
# Clone the repository
git clone https://github.com/username/DeepMu.git
cd DeepMu

# Create and activate a virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install core dependencies
pip install -r requirements.txt

# Install Prodigal and KofamScan
# Prodigal: https://github.com/hyattpd/Prodigal
# KofamScan: https://github.com/takaram/kofam_scan

# Optional: Install ETE3 for taxonomy lookup
pip install ete3

# Prepare NCBI taxonomy database (required for taxonomy features)
python prepare_ncbi_taxonomy.py
```

### Preparing the NCBI Taxonomy Database

Before running feature extraction with taxonomy features, you need to prepare the NCBI taxonomy database:

```bash
# Download and prepare the NCBI taxonomy database
python prepare_ncbi_taxonomy.py

# Optionally specify a custom location for the database
python prepare_ncbi_taxonomy.py --output-dir /path/to/custom/location
```

This is a one-time process that downloads and prepares the NCBI taxonomy database for use with DeepMu.

### Basic Usage

DeepMu features a streamlined workflow with enhanced preprocessing and prediction capabilities:

1. **Preprocess your genome** (optional but recommended):
```bash
# For a single genome with pathway clustering
python deepmu_cli.py preprocess genome.fna \
    --output-dir processed/ \
    --kofamscan-db /path/to/kofamscan_db \
    --kegg-map /path/to/pathway_mapping.txt \
    --heg-ko-list /path/to/heg_ko_list.txt \
    --genetic-code 11

# For multiple genomes
python deepmu_cli.py preprocess genomes_dir/ \
    --output-dir processed/ \
    --kofamscan-db /path/to/kofamscan_db \
    --batch
```

2. **Extract features** (optional, for training custom models):
```bash
# Extract features for multiple genomes
python deepmu_cli.py feature batch \
    --metadata metadata.tsv \
    --genome-dir genomes/ \
    --cds-dir cds_ffn/ \
    --faa-dir cds_faa/ \
    --ko-dir ko_files/ \
    --output-dir features/ \
    --combine \
    --advanced-codon-features
```

3. **Run predictions**:

#### Single Organism Prediction
```bash
# Using clustered pathway features
python deepmu_cli.py single processed/genome_cds.faa \
    --ko-file processed/genome_ko.tsv \
    --kegg-map /path/to/pathway_mapping.txt \
    --heg-ko-list /path/to/heg_ko_list.txt \
    --taxid 2 \
    --temp 37 \
    --output result.json

# Direct genome analysis
python deepmu_cli.py single genome.fna \
    --is-genome \
    --kofamscan-db /path/to/kofamscan_db \
    --output-dir processed/ \
    --temp 37 \
    --output result.json
```

#### Community Prediction
```bash
# Basic community analysis
python deepmu_cli.py community metagenome.fna \
    --coverage coverage.tsv \
    --temp 37 \
    --output community_result.json
```

## Advanced Features

### Hybrid RF-Transformer Architecture

DeepMu v1.1.1 introduces a hybrid architecture that combines:

1. **Random Forest** for growth rate prediction (optimized for this specific task)
2. **Transformer** for temperature prediction (leverages sequence patterns effectively)

This specialized approach allows each prediction task to benefit from the architecture that works best for it. For more details, see the [Hybrid RF-Transformer](docs/hybrid_rf_transformer.md) documentation.

### Advanced Regularization Techniques

The new version implements sophisticated regularization strategies:

- **L1/L2 Regularization**: Prevents overfitting while maintaining feature importance
- **Dropout Strategies**: Adaptive dropout rates based on layer depth and feature type
- **Early Stopping**: Prevents overfitting by monitoring validation performance
- **Gradient Clipping**: Stabilizes training for improved convergence

For more information, see [Advanced Regularization](docs/advanced_regularization.md).

### Multi-Branch Neural Networks

The multi-branch architecture provides specialized processing pathways:

- **Codon Branch**: Processes codon usage patterns with dedicated layers
- **Phylogenetic Branch**: Handles taxonomic information with graph neural networks
- **Sequence Branch**: Analyzes k-mer patterns with convolutional layers
- **Pathway Branch**: Processes metabolic pathway information

Learn more in [Multi-Branch Architecture](docs/multi_branch_architecture.md).

### Enhanced Codon Features

DeepMu v1.1.1 includes advanced codon feature analysis:

- **Codon Adaptation Index (CAI)**: Measures how well adapted a gene's codon usage is to the most expressed genes
- **Effective Number of Codons (ENC)**: Measures the evenness of codon usage (range 20-61)
- **GC Content at Different Positions**: Measures GC content at 1st, 2nd, and 3rd codon positions
- **Codon Context Bias**: Measures non-random associations between adjacent codons
- **HEG-specific Metrics**: Calculates all the above metrics specifically for highly expressed genes

For details, see [Codon Feature Improvements](docs/codon_feature_improvements.md).

## Training Custom Models

DeepMu supports training custom models with different architectures:

### Train a Hybrid RF-Transformer Model

```bash
python train_hybrid_rf_transformer.py \
    --feature-file features_all/combined_features.tsv \
    --metadata training_data/metadata.tsv \
    --output-dir models/hybrid_rf_transformer \
    --metrics-dir metrics/hybrid_rf_transformer
```

### Train a Random Forest Model with Advanced Features

```bash
python train_rf_hybrid_model.py \
    --feature-file features_all/combined_features.tsv \
    --metadata training_data/metadata.tsv \
    --output-dir models/rf_hybrid \
    --metrics-dir metrics/rf_hybrid
```

### Train a Multi-Branch Neural Network

```bash
python train_multi_branch.py \
    --metadata training_data/metadata.tsv \
    --feature-dir training_data/features \
    --ko-dir training_data/ko_files \
    --output-dir models/multi_branch \
    --metrics-dir metrics/multi_branch
```

## Documentation

DeepMu v1.1.1 includes comprehensive documentation:

- [User Guide](docs/user_guide.md): Step-by-step guide for using DeepMu
- [Developer Guide](docs/developer_guide.md): Information for extending DeepMu
- [Feature Extraction](docs/feature_extraction.md): Details on feature extraction
- [Model Architecture](docs/hybrid_rf_transformer.md): Information on model architecture
- [Advanced Regularization](docs/advanced_regularization.md): Details on regularization techniques

## Citation

If you use DeepMu in your research, please cite our paper:

```
@article{deepmu2023,
  title={DeepMu: Predicting Microbial Growth Rates and Optimal Temperatures with Deep Learning},
  author={Author, A. and Author, B.},
  journal={Journal Name},
  volume={X},
  number={Y},
  pages={Z},
  year={2023}
}
```

## License

DeepMu is released under the MIT License. See the LICENSE file for details.
