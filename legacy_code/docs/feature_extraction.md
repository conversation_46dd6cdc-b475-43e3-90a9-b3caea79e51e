# Feature Extraction

DeepMu extracts a variety of genomic features to predict microbial growth rates and optimal temperatures. This document describes the feature extraction process and how to use it.

## Overview

DeepMu v1.1.1 uses a modular approach to feature extraction, with the primary implementation in `modular_feature_extraction.py`. The original `feature_extraction.py` module is maintained for backward compatibility but now imports functionality from the modular implementation.

### Key Improvements in v1.1.1

1. **Reduced Code Redundancy**: Eliminated duplicate code between feature extraction modules
2. **Better Organization**: Clearer separation of concerns with modular approach
3. **Backward Compatibility**: Existing code using `feature_extraction.py` will continue to work
4. **Improved Maintainability**: Easier to maintain a single implementation
5. **Enhanced Documentation**: Clear documentation about which module to use

## Feature Types

DeepMu extracts a variety of features from genomic data:

1. **Codon Features**: Features related to codon usage and bias
   - Codon Usage Bias (CUB)
   - Codon Pair Bias (CPB)
   - Codon Adaptation Index (CAI)
   - Effective Number of Codons (ENC)
   - Relative Synonymous Codon Usage (RSCU)
   - HEG/BP differential features

2. **Amino Acid Features**: Features related to amino acid composition and properties
   - Amino acid frequencies
   - Average amino acid weight
   - Nitrogen content
   - Carbon content
   - Hydrophobicity metrics

3. **Genomic Features**: Features related to genome composition and structure
   - GC content
   - Genome size
   - Dinucleotide frequencies
   - GC skew
   - Nucleotide frequencies

4. **Taxonomy Features**: Features derived from NCBI taxonomy
   - Hierarchical taxonomy embeddings
   - Taxonomic distance metrics
   - Phylum to species level features

5. **Pathway Features**: Features related to KEGG pathways
   - Pathway completeness
   - KEGG ortholog enrichment
   - Pathway correlation clusters

6. **Protein pI Features**: Features related to protein isoelectric point distribution
   - pI distribution metrics
   - pI histogram features
   - pI ranges for functional groups

7. **RNA Features**: Features related to tRNA and rRNA
   - tRNA gene counts
   - rRNA gene counts
   - tRNA Adaptation Index (tAI)

## Usage

### Extracting Features for Multiple Genomes

```python
from deepmu.features.modular_feature_extraction import extract_features_batch

extract_features_batch(
    metadata_file="metadata.tsv",
    output_dir="features",
    genome_dir="genomes",
    cds_dir="cds_ffn",
    faa_dir="cds_faa",
    ko_dir="ko_files",
    kegg_map_file="pathway_mapping.txt",
    heg_ko_list="heg_ko_list.txt",
    advanced_codon_features=True,
    num_processes=4
)
```

### Extracting Features for a Single Genome

```python
from deepmu.features.modular_feature_extraction import extract_features_for_genome

features = extract_features_for_genome(
    genome_id="genome_1",
    genome_file="genome_1.fna",
    cds_file="genome_1.ffn",
    faa_file="genome_1.faa",
    ko_file="genome_1.ko",
    taxid="562",
    kegg_map_file="pathway_mapping.txt",
    heg_ko_list="heg_ko_list.txt",
    advanced_codon_features=True
)
```

### Command-Line Usage

```bash
# Extract features for multiple genomes
python deepmu_cli.py feature batch \
    --metadata metadata.tsv \
    --genome-dir genomes/ \
    --cds-dir cds_ffn/ \
    --faa-dir cds_faa/ \
    --ko-dir ko_files/ \
    --output-dir features/ \
    --combine \
    --kegg-map pathway_mapping.txt \
    --heg-ko-list heg_ko_list.txt \
    --advanced-codon-features

# Extract features for a single genome
python deepmu_cli.py feature single \
    --genome-id genome_1 \
    --genome-file genome_1.fna \
    --cds-file genome_1.ffn \
    --faa-file genome_1.faa \
    --ko-file genome_1.ko \
    --taxid 562 \
    --output-dir features/ \
    --kegg-map pathway_mapping.txt \
    --heg-ko-list heg_ko_list.txt \
    --advanced-codon-features
```

## Feature Selection

DeepMu also provides tools for selecting the most relevant features for growth rate and temperature prediction:

```bash
python deepmu_cli.py feature select \
    --metadata metadata.tsv \
    --feature-dir features/ \
    --output-dir selected_features/ \
    --growth-rate-features 30 \
    --optimal-temperature-features 30 \
    --correlation-method pearson \
    --kegg-map pathway_mapping.txt
```

This command selects:
- Top 30 features for growth rate prediction
- Top 30 features for optimal temperature prediction
- Uses Pearson correlation for ranking
- Groups features by KEGG pathway for enhanced interpretability

## Testing

To test the feature extraction module:

```bash
python test_feature_extraction.py \
    --cds-dir training_data/cds_ffn \
    --ko-dir training_data/kegg/ko_files \
    --metadata training_data/metadata.tsv
```

## Handling Missing Genomic Features

For genomes with missing genomic features, use the `regenerate_missing_features.py` script:

```bash
python regenerate_missing_features.py \
  --features-tsv features/combined_features.tsv \
  --metadata metadata.tsv \
  --genome-dir genomes/ \
  --cds-dir cds_ffn/ \
  --faa-dir cds_faa/ \
  --ko-dir ko_files/ \
  --output-dir features_fixed \
  --heg-ko-list heg_ko_list.txt \
  --combine \
  --output-tsv features_fixed/combined_features.tsv
```

## Preparing the NCBI Taxonomy Database

Before running feature extraction with taxonomy features, prepare the NCBI taxonomy database:

```bash
python prepare_ncbi_taxonomy.py
```

This is a one-time process that downloads and prepares the NCBI taxonomy database for use with DeepMu.
