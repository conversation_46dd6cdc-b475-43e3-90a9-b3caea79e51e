# DeepMu Design Notes

## Temperature Prediction Component

### Decision Summary
After testing multiple approaches, we've selected **Random Forest without temperature transformation** as the preferred method for temperature prediction in DeepMu v1.1.1.

### Rationale
1. **Performance Comparison**
   - RF without transformation achieved slightly better validation R² (0.8548 vs 0.8466)
   - While error metrics were marginally better with log2 transformation, the differences were minimal
   - Test R² was very similar in both approaches (0.8683 vs 0.8710)

2. **Model Simplicity**
   - Avoiding transformation reduces complexity and potential errors in data processing
   - Simpler approach is easier to maintain and explain
   - Raw temperature values are more interpretable

3. **Integration with Growth Rate Model**
   - The hybrid approach allows us to optimize each component independently
   - Growth rate continues to use log2 transformation as it shows clear benefits
   - RF for temperature complements the DNN for growth rate, leveraging strengths of both algorithms

### Alternative Approaches Considered
1. **DNN for Temperature**
   - Struggled to achieve R² > 0.85
   - Required complex regularization and architecture
   - Prone to overfitting

2. **RF with Log2 Transformation**
   - Performed well but slightly worse on validation
   - Added unnecessary complexity

3. **Single Model for Both Targets**
   - Performance compromises for one or both targets
   - Unable to optimize for each prediction task independently

### Implementation Details
The final implementation uses:
- Random Forest with 300 estimators, max depth 20, min samples leaf 2
- Feature selection specifically tuned for temperature prediction
- No transformation of temperature values (direct prediction) 