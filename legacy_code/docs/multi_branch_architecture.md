# Multi-Branch Neural Network Architecture

This document describes the multi-branch neural network architecture implemented in DeepMu for microbial growth rate and optimal growth temperature prediction.

## Overview

The multi-branch architecture processes different feature types through separate branches before fusion with attention mechanisms. This approach allows the model to learn specialized representations for each feature type while also capturing interactions between them.

![Multi-Branch Architecture](../assets/multi_branch_architecture.png)

## Key Components

### 1. Feature Branches

Each feature type is processed through a dedicated branch with the following components:

- **Input Layer**: Takes features of a specific type (codon, amino acid, genomic, etc.)
- **Hidden Layers**: Two fully-connected layers with batch normalization, ReLU activation, and dropout
- **Residual Connections**: Skip connections to improve gradient flow
- **Output Layer**: Projects to a common embedding dimension

### 2. Attention Fusion

Features from different branches are fused using a multi-head attention mechanism:

- **Self-Attention**: Each branch attends to all other branches
- **Layer Normalization**: Normalizes the attention output
- **Residual Connection**: Adds the original features to the attention output
- **Branch Importance Weights**: Learnable weights for each branch

### 3. Fusion Network

The fused features are processed through a fusion network:

- **Multiple Layers**: Several fully-connected layers with batch normalization, ReLU activation, and dropout
- **Decreasing Dimensions**: Gradually reduces the dimension to the output size

### 4. Output Heads

For multi-task learning, separate output heads are used:

- **Growth Rate Head**: Predicts microbial growth rate
- **Temperature Head**: Predicts optimal growth temperature

## Feature Types

The model supports the following feature types:

1. **Codon Features**: Codon usage bias, codon pair bias, CAI, etc.
2. **Amino Acid Features**: Amino acid composition, physicochemical properties, etc.
3. **Genomic Features**: Genome size, GC content, dinucleotide frequencies, etc.
4. **RNA Features**: tRNA and rRNA counts, tAI, etc.
5. **Protein pI Features**: Isoelectric point features
6. **Taxonomy Features**: Taxonomic classification features

## Training

The model is trained using the following components:

- **Loss Function**: Mean Squared Error (MSE) for regression
- **Multi-Task Learning**: Weighted sum of growth rate and temperature losses
- **Optimizer**: Adam with weight decay
- **Learning Rate Scheduler**: ReduceLROnPlateau
- **Early Stopping**: Based on validation loss

## Usage

To train a multi-branch model, use the `train_multi_branch.py` script:

```bash
python train_multi_branch.py \
    --feature-file features_398/combined_features.tsv \
    --metadata training_data/metadata_398.tsv \
    --output-dir models/multi_branch \
    --metrics-dir metrics/multi_branch \
    --batch-size 32 \
    --epochs 100 \
    --hidden-dims 256 128 64 \
    --dropout-rates 0.2 0.3 0.4 \
    --attention-heads 4 \
    --fusion-layers 2
```

## Performance

The multi-branch architecture offers several advantages over simpler models:

1. **Improved Performance**: Better R² and RMSE compared to single-branch models
2. **Feature Interpretability**: Attention weights reveal the importance of different feature types
3. **Robustness**: Less prone to overfitting due to the specialized branch structure
4. **Flexibility**: Can handle missing feature types by simply omitting the corresponding branch

## Implementation Details

The implementation is available in the following files:

- `deepmu/models/multi_branch_network.py`: Core implementation of the multi-branch architecture
- `train_multi_branch.py`: Training script for the multi-branch model

## References

1. Vaswani, A., et al. (2017). Attention is all you need. *Advances in neural information processing systems*.
2. He, K., et al. (2016). Deep residual learning for image recognition. *Proceedings of the IEEE conference on computer vision and pattern recognition*.
3. Kendall, A., et al. (2018). Multi-task learning using uncertainty to weigh losses for scene geometry and semantics. *Proceedings of the IEEE conference on computer vision and pattern recognition*.
