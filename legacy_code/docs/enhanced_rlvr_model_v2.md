# Enhanced RLVR Model for Growth Rate Prediction

This document describes the enhanced Reinforcement Learning with Verifiable Rewards (RLVR) model for microbial growth rate prediction in the DeepMu project.

## Overview

The enhanced RLVR model builds upon the original RLVR approach with several architectural improvements:

1. **Residual Connections**: Adds residual connections to help with gradient flow and training of deeper networks
2. **Feature Interaction Layers**: Implements explicit feature interaction mechanisms to capture complex relationships
3. **Positional Encoding**: Adds positional information to help the transformer understand feature ordering
4. **Dual-Head Policy Network**: Uses an ensemble of two policy heads for more robust predictions
5. **Improved Initialization**: Implements Kaiming initialization for better training dynamics
6. **Batch Normalization Option**: Adds the option to use batch normalization instead of layer normalization

## Architecture

The enhanced RLVR model consists of the following components:

### 1. PositionalEncoding

Adds positional information to the input features to help the transformer model understand the order of features.

```python
class PositionalEncoding(nn.Module):
    def __init__(self, d_model: int, dropout: float = 0.1, max_len: int = 100):
        # Initialize positional encoding
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # Add positional encoding to input
```

### 2. ResidualBlock

Implements a residual connection around a sequence of layers, with optional layer or batch normalization.

```python
class ResidualBlock(nn.Module):
    def __init__(self, layers: nn.Module, hidden_dim: int, use_layer_norm: bool = True, use_batch_norm: bool = False):
        # Initialize residual block
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # Apply layers with residual connection
```

### 3. FeatureInteractionLayer

Implements a mechanism for features to interact with each other, allowing the model to learn complex relationships between different feature types.

```python
class FeatureInteractionLayer(nn.Module):
    def __init__(self, hidden_dim: int, dropout: float = 0.1, use_layer_norm: bool = True):
        # Initialize feature interaction layer
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # Apply feature interaction
```

### 4. EnhancedRLVRAgent

The main model class that combines all the components.

```python
class EnhancedRLVRAgent(nn.Module):
    def __init__(self, feature_dims: Dict[str, int], hidden_dim: int = 256, ...):
        # Initialize enhanced RLVR agent
        
    def forward(self, features: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        # Forward pass through the model
```

## Hyperparameters

The enhanced RLVR model introduces several new hyperparameters:

- `use_feature_interaction`: Whether to use feature interaction layers
- `interaction_layers`: Number of feature interaction layers
- `use_batch_norm`: Whether to use batch normalization instead of layer normalization

In addition, the following hyperparameters have been optimized:

- `hidden_dim`: Increased to 512 for more capacity
- `num_heads`: Increased to 8 for better attention
- `num_layers`: Increased to 3 for deeper representation
- `dropout`: Increased to 0.3 for better regularization
- `activation`: Changed to SiLU (Swish) for better gradient flow
- `alpha`, `beta`, `gamma`: Adjusted for better reward balance
- `accuracy_scale`: Reduced to 0.5 for smoother rewards
- `lr`: Reduced to 5e-5 for more stable training
- `weight_decay`: Increased to 1e-4 for better regularization
- `batch_size`: Increased to 64 for better gradient estimates
- `n_features`: Increased to 100 for more information

## Usage

### Running the Enhanced RLVR Model

To run the enhanced RLVR model:

```bash
./run_enhanced_rlvr_model.sh
```

This script will:
1. Enhance the RLVR agent with the improved architecture
2. Train the model with optimized hyperparameters
3. Save the model and metrics

### Hyperparameter Search

To run a hyperparameter search for the enhanced RLVR model:

```bash
./run_enhanced_rlvr_hyperparam_search.py
```

This script will:
1. Run multiple training runs with different hyperparameter combinations
2. Track the best performing model
3. Save the best parameters and create a script to run the best model

### Using the Best Model

After running the hyperparameter search, you can run the best model:

```bash
bash hyperparam_search_enhanced/run_best_enhanced_model.sh
```

## Implementation Details

### Feature Encoders with Residual Connections

The feature encoders now include residual connections to help with gradient flow:

```python
def _create_feature_encoder(self, input_dim, hidden_dim, dropout, use_layer_norm, use_batch_norm):
    # First layer
    first_layer = nn.Sequential(...)
    
    # Second layer with residual connection if dimensions match
    if self.use_residual and input_dim == hidden_dim:
        second_layer = ResidualBlock(...)
        return nn.Sequential(first_layer, second_layer)
    else:
        # If dimensions don't match, use a regular layer
        second_layer = nn.Sequential(...)
        return nn.Sequential(first_layer, second_layer)
```

### Feature Interaction

The feature interaction layers allow features to interact with each other:

```python
# Apply feature interaction layers if enabled
if self.use_feature_interaction and hasattr(self, 'interaction_layers'):
    for layer in self.interaction_layers:
        transformer_out = layer(transformer_out)
```

### Dual-Head Policy Network

The model uses two policy heads and ensembles their predictions:

```python
# Apply policy networks for growth rate prediction
growth_rate1 = self.policy_net(global_features)
growth_rate2 = self.policy_net2(global_features)

# Ensemble the predictions (weighted average)
growth_rate = 0.7 * growth_rate1 + 0.3 * growth_rate2
```

### Improved Initialization

The model uses Kaiming initialization for better training dynamics:

```python
def _init_weights(self):
    for name, param in self.named_parameters():
        if 'weight' in name:
            if 'norm' not in name and len(param.shape) >= 2:
                if 'Linear' in name:
                    # Use Kaiming initialization for linear layers
                    nn.init.kaiming_normal_(param, nonlinearity='relu')
                else:
                    # Use Xavier for other layers
                    nn.init.xavier_uniform_(param)
        elif 'bias' in name:
            nn.init.zeros_(param)
```

## Performance Comparison

The enhanced RLVR model is expected to outperform the original RLVR model in the following ways:

1. **Higher R² Score**: The enhanced model should achieve a higher R² score on the test set
2. **Lower RMSE**: The enhanced model should have a lower root mean squared error
3. **Better Calibration**: The uncertainty estimates should be better calibrated
4. **Faster Convergence**: The enhanced model should converge faster during training
5. **Better Generalization**: The enhanced model should generalize better to unseen data

## References

1. He, K., Zhang, X., Ren, S., & Sun, J. (2016). Deep residual learning for image recognition. In Proceedings of the IEEE conference on computer vision and pattern recognition (pp. 770-778).

2. Vaswani, A., Shazeer, N., Parmar, N., Uszkoreit, J., Jones, L., Gomez, A. N., ... & Polosukhin, I. (2017). Attention is all you need. Advances in neural information processing systems, 30.

3. Ramachandran, P., Zoph, B., & Le, Q. V. (2017). Searching for activation functions. arXiv preprint arXiv:1710.05941.

4. He, K., Zhang, X., Ren, S., & Sun, J. (2015). Delving deep into rectifiers: Surpassing human-level performance on imagenet classification. In Proceedings of the IEEE international conference on computer vision (pp. 1026-1034).
