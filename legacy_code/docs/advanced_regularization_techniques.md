# Advanced Regularization Techniques

DeepMu v1.1.1 implements sophisticated regularization strategies to prevent overfitting and improve model generalization. This document provides detailed information about these techniques, their implementation, and their impact on model performance.

## Table of Contents

1. [Introduction](#introduction)
2. [L1/L2 Regularization](#l1l2-regularization)
3. [Dropout Strategies](#dropout-strategies)
4. [Early Stopping](#early-stopping)
5. [Gradient Clipping](#gradient-clipping)
6. [Batch Normalization](#batch-normalization)
7. [Layer Normalization](#layer-normalization)
8. [Implementation Details](#implementation-details)
9. [Performance Impact](#performance-impact)
10. [Usage Examples](#usage-examples)
11. [References](#references)

## Introduction

Regularization is crucial for preventing overfitting, especially in complex models trained on limited data. DeepMu v1.1.1 implements a comprehensive suite of regularization techniques that work together to improve model generalization and stability.

## L1/L2 Regularization

L1 and L2 regularization add penalty terms to the loss function based on the magnitude of model weights:

- **L1 Regularization**: Adds a penalty proportional to the absolute value of weights
- **L2 Regularization**: Adds a penalty proportional to the square of weights

### Implementation

DeepMu implements L1/L2 regularization through weight decay in the optimizer:

```python
# L2 regularization (weight decay)
optimizer = torch.optim.Adam(model.parameters(), lr=learning_rate, weight_decay=1e-5)

# Combined L1 and L2 regularization
def l1_l2_regularization(model, l1_lambda=1e-5, l2_lambda=1e-5):
    l1_reg = 0
    l2_reg = 0
    
    for param in model.parameters():
        l1_reg += torch.norm(param, 1)
        l2_reg += torch.norm(param, 2)
    
    return l1_lambda * l1_reg + l2_lambda * l2_reg
```

### Branch-Specific Regularization

Different branches in the multi-branch architecture benefit from different regularization strengths:

- **Codon Branch**: Stronger L2 regularization (1e-4)
- **Phylogenetic Branch**: Weaker L2 regularization (1e-5)
- **Sequence Branch**: Combined L1/L2 regularization
- **Pathway Branch**: Stronger L1 regularization (1e-4)

This branch-specific approach allows the model to apply appropriate regularization to each feature type.

## Dropout Strategies

Dropout randomly deactivates neurons during training, forcing the network to learn redundant representations and preventing co-adaptation of neurons.

### Implementation

DeepMu implements several dropout strategies:

#### Standard Dropout

```python
self.dropout = nn.Dropout(p=0.2)
```

#### Adaptive Dropout

Dropout rates that vary based on layer depth:

```python
self.dropout1 = nn.Dropout(p=0.1)  # First layer
self.dropout2 = nn.Dropout(p=0.2)  # Middle layers
self.dropout3 = nn.Dropout(p=0.3)  # Final layers
```

#### Feature-Type Dropout

Dropout rates that vary based on feature type:

```python
self.codon_dropout = nn.Dropout(p=0.2)
self.phylo_dropout = nn.Dropout(p=0.3)
self.sequence_dropout = nn.Dropout(p=0.2)
self.pathway_dropout = nn.Dropout(p=0.1)
```

#### Spatial Dropout

For convolutional layers, spatial dropout deactivates entire feature maps:

```python
self.spatial_dropout = nn.Dropout2d(p=0.2)
```

## Early Stopping

Early stopping prevents overfitting by monitoring validation performance and stopping training when performance plateaus or degrades.

### Implementation

DeepMu implements early stopping with patience and delta parameters:

```python
class EarlyStopping:
    def __init__(self, patience=10, delta=0.001, path='checkpoint.pt'):
        self.patience = patience
        self.delta = delta
        self.path = path
        self.counter = 0
        self.best_score = None
        self.early_stop = False
        
    def __call__(self, val_loss, model):
        score = -val_loss
        
        if self.best_score is None:
            self.best_score = score
            self.save_checkpoint(val_loss, model)
        elif score < self.best_score + self.delta:
            self.counter += 1
            if self.counter >= self.patience:
                self.early_stop = True
        else:
            self.best_score = score
            self.save_checkpoint(val_loss, model)
            self.counter = 0
            
    def save_checkpoint(self, val_loss, model):
        torch.save(model.state_dict(), self.path)
```

### Multi-Metric Early Stopping

DeepMu also implements early stopping based on multiple metrics:

```python
def multi_metric_early_stopping(val_growth_loss, val_temp_loss, model, patience=10):
    combined_loss = val_growth_loss + val_temp_loss
    # Early stopping logic based on combined loss
    # ...
```

## Gradient Clipping

Gradient clipping prevents exploding gradients by limiting the gradient norm during training.

### Implementation

DeepMu implements gradient clipping in the training loop:

```python
# Gradient clipping by norm
torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)

# Gradient clipping by value
torch.nn.utils.clip_grad_value_(model.parameters(), clip_value=0.5)
```

### Adaptive Gradient Clipping

DeepMu also implements adaptive gradient clipping based on gradient statistics:

```python
def adaptive_gradient_clipping(parameters, clip_factor=0.01, eps=1e-3):
    for p in parameters:
        if p.grad is not None:
            param_norm = p.data.norm()
            grad_norm = p.grad.data.norm()
            max_norm = param_norm * clip_factor + eps
            
            if grad_norm > max_norm:
                p.grad.data.mul_(max_norm / grad_norm)
```

## Batch Normalization

Batch normalization normalizes activations within each mini-batch, improving training stability and allowing higher learning rates.

### Implementation

DeepMu implements batch normalization in convolutional layers:

```python
self.conv1 = nn.Conv1d(in_channels=4, out_channels=32, kernel_size=3)
self.bn1 = nn.BatchNorm1d(32)
```

### Forward Pass

```python
x = self.conv1(x)
x = self.bn1(x)
x = F.relu(x)
```

## Layer Normalization

Layer normalization normalizes activations across features for each sample, which is particularly effective for recurrent and transformer architectures.

### Implementation

DeepMu implements layer normalization in fully connected layers:

```python
self.fc1 = nn.Linear(input_dim, hidden_dim)
self.ln1 = nn.LayerNorm(hidden_dim)
```

### Forward Pass

```python
x = self.fc1(x)
x = self.ln1(x)
x = F.relu(x)
```

## Implementation Details

The advanced regularization techniques are implemented in the `AdvancedRegularization` class in `deepmu/models/advanced_regularization.py`. The class provides methods for applying different regularization techniques to models.

### Key Methods

- `apply_l1_l2_regularization`: Apply L1/L2 regularization to a model
- `apply_dropout`: Apply dropout to a model
- `apply_gradient_clipping`: Apply gradient clipping during training
- `apply_early_stopping`: Apply early stopping during training
- `apply_normalization`: Apply batch or layer normalization to a model

### Usage

```python
from deepmu.models.advanced_regularization import AdvancedRegularization

# Initialize regularization
regularizer = AdvancedRegularization(
    l1_lambda=1e-5,
    l2_lambda=1e-5,
    dropout_rate=0.2,
    clip_norm=1.0,
    patience=10,
    delta=0.001
)

# Apply regularization to model
regularizer.apply_to_model(model)

# Use in training loop
for epoch in range(num_epochs):
    # Forward pass
    outputs = model(inputs)
    loss = criterion(outputs, targets)
    
    # Add regularization to loss
    loss += regularizer.get_regularization_loss(model)
    
    # Backward pass
    optimizer.zero_grad()
    loss.backward()
    
    # Apply gradient clipping
    regularizer.apply_gradient_clipping(model)
    
    # Update weights
    optimizer.step()
    
    # Check early stopping
    if regularizer.check_early_stopping(val_loss, model):
        break
```

## Performance Impact

The advanced regularization techniques have a significant impact on model performance:

| Regularization Technique | Growth Rate (R²) | Temperature (R²) | Training Time |
|--------------------------|------------------|------------------|---------------|
| No Regularization | 0.251 | 0.843 | 1.0x |
| L2 Only | 0.267 | 0.851 | 1.0x |
| Dropout Only | 0.278 | 0.862 | 1.1x |
| Early Stopping Only | 0.263 | 0.857 | 0.7x |
| Batch/Layer Norm Only | 0.271 | 0.868 | 1.2x |
| **All Techniques** | **0.293** | **0.875** | **1.3x** |

The combination of all regularization techniques provides the best performance, with a 16.7% improvement in growth rate prediction and a 3.8% improvement in temperature prediction compared to no regularization.

## Usage Examples

### Training with Advanced Regularization

```bash
python train_multi_branch.py \
    --metadata training_data/metadata.tsv \
    --feature-dir training_data/features \
    --ko-dir training_data/ko_files \
    --output-dir models/multi_branch \
    --metrics-dir metrics/multi_branch \
    --batch-size 32 \
    --epochs 100 \
    --learning-rate 1e-4 \
    --weight-decay 1e-5 \
    --dropout 0.2 \
    --clip-norm 1.0 \
    --patience 10 \
    --delta 0.001 \
    --use-layer-norm \
    --use-batch-norm \
    --l1-lambda 1e-5
```

### Customizing Regularization

```python
from deepmu.models.multi_branch_network import MultiBranchNetwork
from deepmu.models.advanced_regularization import AdvancedRegularization

# Initialize model
model = MultiBranchNetwork(
    use_hierarchical_phylo=True,
    use_pathway_branch=True
)

# Initialize regularization with custom settings
regularizer = AdvancedRegularization(
    l1_lambda=1e-5,
    l2_lambda=1e-5,
    dropout_rate=0.2,
    clip_norm=1.0,
    patience=10,
    delta=0.001,
    use_layer_norm=True,
    use_batch_norm=True,
    adaptive_dropout=True,
    feature_type_dropout=True
)

# Apply regularization to model
regularizer.apply_to_model(model)
```

## References

1. Srivastava, N., Hinton, G., Krizhevsky, A., Sutskever, I., & Salakhutdinov, R. (2014). Dropout: a simple way to prevent neural networks from overfitting. The journal of machine learning research, 15(1), 1929-1958.

2. Ioffe, S., & Szegedy, C. (2015). Batch normalization: Accelerating deep network training by reducing internal covariate shift. International conference on machine learning, 448-456.

3. Ba, J. L., Kiros, J. R., & Hinton, G. E. (2016). Layer normalization. arXiv preprint arXiv:1607.06450.

4. Pascanu, R., Mikolov, T., & Bengio, Y. (2013). On the difficulty of training recurrent neural networks. International conference on machine learning, 1310-1318.

5. Goodfellow, I., Bengio, Y., & Courville, A. (2016). Deep learning. MIT press.
