# Advanced Regularization Techniques in DeepMu

This document describes the advanced regularization techniques implemented in DeepMu's multi-branch neural network architecture to improve model performance and generalization.

## Overview

DeepMu now supports several state-of-the-art regularization techniques that can be enabled individually or in combination:

1. **Stochastic Weight Averaging (SWA)**
2. **Mixup Data Augmentation**
3. **Label Smoothing**
4. **Spectral Normalization**
5. **Sharpness-Aware Minimization (SAM)**
6. **Gradient Clipping**
7. **Advanced Learning Rate Schedulers**

## Techniques in Detail

### 1. Stochastic Weight Averaging (SWA)

SWA averages weights from different points in the training trajectory to find flatter minima, which often leads to better generalization.

**How it works:**
- Starts collecting model weights after a specified epoch
- Periodically averages the current model weights with the running average
- At the end of training, updates batch normalization statistics for the averaged model
- Compares the SWA model with the best model and keeps the better one

**Parameters:**
- `--use-swa`: Enable SWA
- `--swa-start`: Epoch to start SWA from (default: 10)
- `--swa-freq`: Frequency of model averaging (default: 5)

**Reference:**
- <PERSON><PERSON><PERSON>, <PERSON>, et al. (2018). [Averaging Weights Leads to Wider Optima and Better Generalization](https://arxiv.org/abs/1803.05407)

### 2. Mixup Data Augmentation

Mixup creates synthetic training examples by mixing pairs of examples and their labels, which helps prevent memorization and improves robustness.

**How it works:**
- For each batch, randomly pairs examples
- Creates new examples as weighted combinations of the pairs
- The mixing coefficient is sampled from a Beta distribution

**Parameters:**
- `--use-mixup`: Enable Mixup
- `--mixup-alpha`: Alpha parameter for Beta distribution (default: 0.2)

**Reference:**
- Zhang, H., et al. (2017). [mixup: Beyond Empirical Risk Minimization](https://arxiv.org/abs/1710.09412)

### 3. Label Smoothing

Label smoothing prevents the model from becoming overconfident by smoothing the target values, which can improve generalization.

**How it works:**
- For regression tasks, adds small Gaussian noise to target values
- The amount of noise is controlled by the smoothing parameter

**Parameters:**
- `--use-label-smoothing`: Enable label smoothing
- `--label-smoothing`: Smoothing parameter (default: 0.1)

**Reference:**
- Szegedy, C., et al. (2016). [Rethinking the Inception Architecture for Computer Vision](https://arxiv.org/abs/1512.00567)

### 4. Spectral Normalization

Spectral normalization constrains the spectral norm (largest singular value) of weight matrices, which can stabilize training and improve generalization.

**How it works:**
- Normalizes the weight matrices by their spectral norm
- Applied to linear and convolutional layers
- Uses power iteration to estimate the spectral norm

**Parameters:**
- `--use-spectral-norm`: Enable spectral normalization

**Reference:**
- Miyato, T., et al. (2018). [Spectral Normalization for Generative Adversarial Networks](https://arxiv.org/abs/1802.05957)

### 5. Sharpness-Aware Minimization (SAM)

SAM seeks parameters that lie in neighborhoods having uniformly low loss, leading to better generalization.

**How it works:**
- For each optimization step:
  1. Computes gradients at the current parameters
  2. Takes a step in the direction that would maximize the loss (ascent step)
  3. Computes gradients at the new parameters
  4. Takes a step in the direction that would minimize the loss (descent step)
  5. Returns to the original parameters

**Parameters:**
- `--use-sam`: Enable SAM
- `--sam-rho`: Neighborhood size (default: 0.05)

**Reference:**
- Foret, P., et al. (2020). [Sharpness-Aware Minimization for Efficiently Improving Generalization](https://arxiv.org/abs/2010.01412)

### 6. Gradient Clipping

Gradient clipping limits the norm of gradients to stabilize training, especially useful for deep networks.

**How it works:**
- Computes the norm of gradients
- If the norm exceeds a threshold, scales the gradients to have the threshold norm

**Parameters:**
- `--use-gradient-clipping`: Enable gradient clipping
- `--max-grad-norm`: Maximum gradient norm (default: 1.0)

### 7. Advanced Learning Rate Schedulers

DeepMu supports several learning rate schedulers to improve convergence:

#### Plateau Scheduler (Default)
Reduces the learning rate when the validation loss plateaus.

#### Cyclical Learning Rate
Cycles the learning rate between lower and upper bounds, which helps navigate saddle points and local minima.

**Parameters:**
- `--lr-scheduler=cyclic`: Use cyclical learning rate
- `--cyclic-lr-max`: Maximum learning rate
- `--cyclic-lr-mode`: Mode for cyclic scheduler (triangular, triangular2, exp_range)

**Reference:**
- Smith, L. N. (2017). [Cyclical Learning Rates for Training Neural Networks](https://arxiv.org/abs/1506.01186)

#### One Cycle Learning Rate
Learning rate first increases from base_lr to max_lr, then decreases back to a very small value.

**Parameters:**
- `--lr-scheduler=one_cycle`: Use one cycle learning rate
- `--one-cycle-max-lr`: Maximum learning rate

**Reference:**
- Smith, L. N. (2018). [A disciplined approach to neural network hyper-parameters](https://arxiv.org/abs/1803.09820)

## Usage Examples

### Basic Training with Default Settings

```bash
python train_multi_branch.py \
    --feature-file features_all/combined_features.tsv \
    --metadata training_data/metadata.tsv \
    --output-dir models/multi_branch \
    --metrics-dir metrics/multi_branch \
    --batch-size 32 \
    --epochs 100
```

### Training with SWA and Mixup

```bash
python train_multi_branch.py \
    --feature-file features_all/combined_features.tsv \
    --metadata training_data/metadata.tsv \
    --output-dir models/multi_branch_swa_mixup \
    --metrics-dir metrics/multi_branch_swa_mixup \
    --batch-size 32 \
    --epochs 100 \
    --use-swa \
    --swa-start 20 \
    --use-mixup \
    --mixup-alpha 0.2
```

### Training with SAM and Gradient Clipping

```bash
python train_multi_branch.py \
    --feature-file features_all/combined_features.tsv \
    --metadata training_data/metadata.tsv \
    --output-dir models/multi_branch_sam \
    --metrics-dir metrics/multi_branch_sam \
    --batch-size 32 \
    --epochs 100 \
    --use-sam \
    --sam-rho 0.05 \
    --use-gradient-clipping \
    --max-grad-norm 1.0
```

### Training with One Cycle Learning Rate

```bash
python train_multi_branch.py \
    --feature-file features_all/combined_features.tsv \
    --metadata training_data/metadata.tsv \
    --output-dir models/multi_branch_one_cycle \
    --metrics-dir metrics/multi_branch_one_cycle \
    --batch-size 32 \
    --epochs 100 \
    --lr-scheduler one_cycle \
    --one-cycle-max-lr 0.01
```

## Recommended Combinations

Based on empirical results, the following combinations often work well:

1. **For best overall performance:**
   - SWA + Gradient Clipping + One Cycle LR

2. **For preventing overfitting:**
   - Mixup + Label Smoothing + SWA

3. **For difficult optimization landscapes:**
   - SAM + Gradient Clipping + Cyclical LR

4. **For unstable training:**
   - Spectral Normalization + Gradient Clipping + Plateau LR

## Implementation Details

The implementation of these techniques can be found in:

- `deepmu/models/advanced_regularization.py`: Core implementation of regularization techniques
- `deepmu/models/multi_branch_network.py`: Integration with the multi-branch model
- `train_multi_branch.py`: Command-line interface for training with advanced regularization
