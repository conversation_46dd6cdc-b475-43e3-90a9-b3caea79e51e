# Advanced Codon Features in DeepMu v1.1.1

DeepMu v1.1.1 introduces a comprehensive set of advanced codon features that significantly improve the accuracy of growth rate predictions. This document provides detailed information about these features, their implementation, and their biological significance.

## Table of Contents

1. [Introduction](#introduction)
2. [Codon Adaptation Index (CAI)](#codon-adaptation-index-cai)
3. [Effective Number of Codons (ENC)](#effective-number-of-codons-enc)
4. [GC Content at Different Positions](#gc-content-at-different-positions)
5. [Codon Context Bias](#codon-context-bias)
6. [HEG-specific Metrics](#heg-specific-metrics)
7. [Implementation Details](#implementation-details)
8. [Usage Examples](#usage-examples)
9. [Performance Impact](#performance-impact)
10. [References](#references)

## Introduction

Codon usage patterns are strongly correlated with microbial growth rates. Organisms with faster growth rates typically exhibit stronger codon usage bias, particularly in highly expressed genes (HEGs). DeepMu v1.1.1 expands the analysis of codon usage patterns with a suite of advanced features that capture different aspects of codon bias and its relationship to growth rate.

## Codon Adaptation Index (CAI)

The Codon Adaptation Index (CAI) measures how well adapted a gene's codon usage is to the most expressed genes in the genome. It is calculated as the geometric mean of the relative adaptiveness of each codon in the gene.

### Implementation

DeepMu implements an enhanced CAI calculation that:

1. Uses a reference set of highly expressed genes (HEGs) identified through KO annotations
2. Calculates relative adaptiveness values for each codon
3. Computes the geometric mean of these values for each gene
4. Provides genome-wide CAI values as well as separate values for HEGs and background genes

### Biological Significance

CAI is strongly correlated with gene expression levels and translation efficiency. Organisms with faster growth rates typically have higher CAI values in their HEGs, reflecting selection for efficient translation of these genes.

## Effective Number of Codons (ENC)

The Effective Number of Codons (ENC) measures the evenness of codon usage across the genome. It ranges from 20 (extreme bias, using only one codon per amino acid) to 61 (no bias, using all codons equally).

### Implementation

DeepMu calculates ENC using Wright's formula, which:

1. Computes the frequency of each codon within its synonymous family
2. Calculates the effective number of codons for each amino acid
3. Combines these values to produce a genome-wide ENC value
4. Provides separate ENC values for HEGs and background genes

### Biological Significance

Lower ENC values indicate stronger codon bias, which is often associated with faster growth rates. Fast-growing organisms typically have lower ENC values in their HEGs compared to background genes.

## GC Content at Different Positions

GC content at different codon positions provides insights into the selective pressures acting on the genome. DeepMu calculates GC content at the 1st, 2nd, and 3rd positions of codons.

### Implementation

The implementation:

1. Counts G and C nucleotides at each position within codons
2. Calculates the percentage of GC content at each position
3. Provides separate values for HEGs and background genes
4. Calculates the difference in GC content between positions

### Biological Significance

The 3rd position of codons is often the most variable and shows the strongest bias in fast-growing organisms. The difference in GC content between positions can indicate the strength of selection for translational efficiency.

## Codon Context Bias

Codon context bias measures non-random associations between adjacent codons. These associations can affect translation efficiency and accuracy.

### Implementation

DeepMu's codon context bias analysis:

1. Analyzes pairs of adjacent codons throughout the genome
2. Calculates observed vs. expected frequencies for each codon pair
3. Computes a context bias score based on these frequencies
4. Provides separate scores for HEGs and background genes

### Biological Significance

Strong codon context bias is often associated with efficient translation and faster growth rates. Certain codon pairs may be favored or avoided to prevent ribosomal stalling or frameshifting.

## HEG-specific Metrics

DeepMu calculates all the above metrics separately for highly expressed genes (HEGs) and background genes. This differentiation is crucial because codon bias is typically stronger in HEGs, and the difference between HEG and background metrics is often more informative than the absolute values.

### Implementation

The HEG-specific analysis:

1. Identifies HEGs based on KO annotations (ribosomal proteins, translation factors, etc.)
2. Calculates all metrics separately for HEGs and background genes
3. Computes delta values (difference between HEG and background metrics)
4. Provides ratios of HEG to background metrics

### Biological Significance

The difference in codon usage between HEGs and background genes is a strong predictor of growth rate. Organisms with faster growth rates typically show larger differences in codon bias between these gene sets.

## Implementation Details

The advanced codon features are implemented in the `CodonFeatureCalculator` class in `deepmu/features/codon_features.py`. The class provides several methods for calculating these features:

- `calculate_advanced_features`: Calculates all advanced codon features from a DNA sequence
- `calculate_advanced_features_from_file`: Calculates features from a FASTA file
- `calculate_advanced_features_with_heg`: Calculates features with HEG differentiation
- `calculate_advanced_features_with_heg_from_file`: Calculates HEG-differentiated features from files

### Helper Methods

Several helper methods are used to calculate specific features:

- `_calculate_gc_at_position`: Calculates GC content at specific codon positions
- `_calculate_enc`: Calculates the Effective Number of Codons
- `_calculate_cai`: Calculates the Codon Adaptation Index
- `_get_reference_weights`: Gets reference weights for CAI calculation
- `_calculate_context_bias`: Calculates codon context bias

## Usage Examples

### Using the Command-Line Interface

```bash
# Single genome prediction with advanced features
python deepmu_cli.py single genome.fna \
    --is-genome \
    --heg-ko-list heg_ko_list.txt \
    --advanced-features \
    --output result.json

# Batch prediction with advanced features
python deepmu_cli.py batch genomes_dir/ \
    --kofamscan-db /path/to/kofamscan_db \
    --heg-ko-list heg_ko_list.txt \
    --advanced-features

# Community prediction with advanced features
python deepmu_cli.py community metagenome.fna \
    --coverage coverage.tsv \
    --heg-ko-list heg_ko_list.txt \
    --advanced-features \
    --output community_result.json
```

### Using the Python API

```python
from deepmu.predictors.predictor import MicrobialGrowthPredictor

# Initialize predictor with advanced features
predictor = MicrobialGrowthPredictor(
    use_heg_features=True,
    heg_ko_list="heg_ko_list.txt"
)

# Make prediction with advanced features
result = predictor.predict_single(
    cds_fasta_path="genome_cds.faa",
    ko_file="genome_ko.tsv",
    analyze_features=True  # Enable advanced feature analysis
)

# Access advanced feature results
advanced_metrics = result['features']['advanced_metrics']
print(f"CAI: {advanced_metrics['CAI']:.3f}")
print(f"ENC: {advanced_metrics['ENC']:.1f}")
print(f"GC3: {advanced_metrics['GC3']:.2f}")
```

## Performance Impact

The advanced codon features have a significant impact on prediction accuracy:

- **Growth Rate Prediction**: R² improved from 0.293 to 0.412 (+40.6%)
- **Temperature Prediction**: R² improved from 0.875 to 0.891 (+1.8%)

The most informative features for growth rate prediction are:

1. Delta CAI (difference between HEG and background CAI)
2. HEG ENC (effective number of codons in HEGs)
3. Delta GC3 (difference in 3rd position GC content)
4. HEG Context Bias

## References

1. Sharp, P. M., & Li, W. H. (1987). The codon adaptation index--a measure of directional synonymous codon usage bias, and its potential applications. Nucleic Acids Research, 15(3), 1281-1295.

2. Wright, F. (1990). The 'effective number of codons' used in a gene. Gene, 87(1), 23-29.

3. Ikemura, T. (1981). Correlation between the abundance of Escherichia coli transfer RNAs and the occurrence of the respective codons in its protein genes: a proposal for a synonymous codon choice that is optimal for the E. coli translational system. Journal of Molecular Biology, 151(3), 389-409.

4. Moura, G., Pinheiro, M., Silva, R., Miranda, I., Afreixo, V., Dias, G., Freitas, A., Oliveira, J. L., & Santos, M. A. (2005). Comparative context analysis of codon pairs on an ORFeome scale. Genome Biology, 6(3), R28.
