# Integrated Genomic and Codon Features

## Overview

DeepMu now integrates both genomic features extracted directly from genome sequences and codon features extracted from CDS sequences to provide a comprehensive feature set for microbial growth rate and optimal temperature prediction.

This integration combines the strengths of both feature types:

1. **Genomic Features**: Capture genome-wide patterns that may correlate with microbial growth rates and optimal temperatures
2. **Codon Features**: Capture codon usage patterns that reflect adaptation to different growth rates and temperatures

## Feature Types

### Genomic Features

Genomic features are extracted directly from genome sequences (not concatenated CDS) and include:

#### Basic Genomic Features
- `genome_size`: Total size of the genome
- `gc_content`: GC content of the genome

#### Nucleotide and Dinucleotide Features
- `freq_A`, `freq_C`, `freq_G`, `freq_T`: Nucleotide frequencies
- `dinuc_freq_XX`: Frequencies of all 16 possible dinucleotides (e.g., `dinuc_freq_AG`)

#### Skew Features
- `gc_skew`: (G-C)/(G+C)
- `at_skew`: (A-T)/(A+T)
- `purine_pyrimidine_skew`: (A+G-C-T)/(A+G+C+T)
- `keto_amino_skew`: (G+T-A-C)/(G+T+A+C)
- `strong_weak_skew`: (G+C-A-T)/(G+C+A+T)
- `dinuc_skew_N`: Various dinucleotide skew metrics

#### Skew Change Points
- `gc_skew_change_points`: Number of GC skew change points per Mb
- `at_skew_change_points`: Number of AT skew change points per Mb
- `*_skew_mean`, `*_skew_std`, `*_skew_min`, `*_skew_max`, `*_skew_range`: Statistics for each skew type
- `*_skew_sign_changes`: Number of sign changes in each skew type

### Codon Features

Codon features are extracted from CDS sequences and include:

#### Basic Codon Metrics
- `CUB`: Codon Usage Bias
- `CPB`: Codon Pair Bias
- `Consistency`: Codon usage consistency
- `RemoteInteraction`: Position-aware codon relationship analysis

#### Advanced Codon Metrics
- `CAI`: Codon Adaptation Index
- `ENC`: Effective Number of Codons
- `GC_content`: GC content of coding sequences
- `GC1`, `GC2`, `GC3`: GC content at different codon positions
- `Context_bias`: Codon context bias

#### HEG-Specific Metrics
- `HEG_*`: Metrics calculated for highly expressed genes
- `BG_*`: Metrics calculated for background genes
- `delta_*`: Difference between HEG and background metrics

### Pathway Features

Pathway features are based on KEGG pathway annotations and include:

#### Pathway Completeness
- `pathway_*`: Completeness scores for KEGG pathways (e.g., `pathway_ko00010` for glycolysis)
- Completeness is calculated as the proportion of KO terms present in a pathway

#### Pathway Clusters
- `pathway_cluster_*`: Completeness scores for clusters of related pathways
- Clusters are based on functional categories (e.g., metabolism, signaling)

## Usage

### Generating Integrated Features

To generate integrated features for your genomes, use the `generate_integrated_features.py` script:

```bash
python generate_integrated_features.py \
    --genome-dir genomes \
    --cds-dir cds \
    --ko-dir ko \
    --taxonomy-dir taxonomy \
    --metadata metadata.tsv \
    --heg-ko-list heg_ko_list.txt \
    --kegg-path kegg/ko00001.keg \
    --output-dir integrated_features \
    --combined-output integrated_features.tsv \
    --num-processes 4 \
    --use-weighted-similarity \
    --advanced-codon-features \
    --include-pathway-features
```

### Training Models with Integrated Features

To train models using the integrated features, use the `train_with_integrated_features.py` script:

```bash
python train_with_integrated_features.py \
    --feature-file integrated_features.tsv \
    --metadata metadata.tsv \
    --output-dir models/integrated \
    --metrics-dir metrics/integrated \
    --target both \
    --n-estimators 200 \
    --test-size 0.2 \
    --random-state 42
```

### Running the Complete Pipeline

To run the complete pipeline from feature generation to model training and evaluation, use the `run_integrated_features_pipeline.sh` script:

```bash
./run_integrated_features_pipeline.sh
```

## Feature Importance

The integration of genomic and codon features provides a more comprehensive view of the factors influencing microbial growth rates and optimal temperatures. Feature importance analysis reveals the relative contribution of each feature type:

### Growth Rate Prediction

For growth rate prediction, both genomic and codon features are important:

1. **Dinucleotide Frequencies**: Frequencies of specific dinucleotides (e.g., AG, GA) are highly important
2. **Genome Size**: Total genome size is a significant predictor
3. **Codon Usage Bias**: Metrics like CUB and CPB capture adaptation in codon usage
4. **HEG-Specific Metrics**: Differences between highly expressed genes and background genes provide valuable information

### Temperature Prediction

For temperature prediction, the importance of features may differ:

1. **Dinucleotide Frequencies**: Different dinucleotide patterns may be important for temperature adaptation
2. **Skew Metrics**: GC skew and other skew metrics may reflect adaptation to different temperatures
3. **Codon Usage Patterns**: Specific codon usage patterns may be associated with temperature adaptation

## Implementation Details

The integrated features implementation consists of several components:

1. **GenomicFeatureCalculator**: Extracts features directly from genome sequences
2. **CodonFeatureCalculator**: Extracts codon usage features from CDS sequences
3. **Integrated Feature Extraction**: Combines genomic and codon features
4. **Feature Importance Analysis**: Analyzes the importance of different feature types
5. **Model Training and Evaluation**: Trains and evaluates models using the integrated features

The implementation is designed to be modular and extensible, allowing for easy addition of new feature types and analysis methods.

## References

1. Vieira-Silva, S., & Rocha, E. P. (2010). The systemic imprint of growth and its uses in ecological (meta)genomics. PLoS genetics, 6(1), e1000808.
2. Weissman, J. L., Dogra, S., Javadi, K., Bolten, S., Flint, R., Davati, C., Beattie, J., & Dixit, P. D. (2021). Predicting bacterial growth rates from genomic sequences: A machine learning approach. PLoS computational biology, 17(9), e1009354.
3. Roller, B. R., Stoddard, S. F., & Schmidt, T. M. (2016). Exploiting rRNA operon copy number to investigate bacterial reproductive strategies. Nature microbiology, 1(11), 16160.
