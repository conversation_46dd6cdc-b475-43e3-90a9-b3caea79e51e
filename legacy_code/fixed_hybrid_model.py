#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
DeepMu Final Hybrid Model - Fixed Version.

This module is a fixed version of the hybrid model that properly
uses the fixed temperature model from fixed_temp_model.py.
"""

import os
import argparse
import logging
import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import RobustScaler, StandardScaler
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
import joblib
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import TensorDataset, DataLoader, random_split
from typing import Dict, List, Tuple, Optional, Any
from scipy import stats
from scipy.optimize import minimize
import copy
from collections import defaultdict
from sklearn.feature_selection import mutual_info_regression, f_regression

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Set device
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

# Import the fixed temperature model - moved after logger initialization
try:
    from fixed_temp_model import AdvancedTemperatureModel
    logger.info("Successfully imported AdvancedTemperatureModel from fixed_temp_model")
except ImportError:
    logger.warning("Could not import from fixed_temp_model, will use simplified implementation")
    
    # Define a simplified version for fallback
    class AdvancedTemperatureModel(nn.Module):
        """Simplified temperature model as fallback if import fails."""
        
        def __init__(self, input_dim):
            super().__init__()
            self.model = nn.Sequential(
                nn.LayerNorm(input_dim),
                nn.Linear(input_dim, 512),
                nn.BatchNorm1d(512),
                nn.ReLU(),
                nn.Dropout(0.3),
                nn.Linear(512, 256),
                nn.BatchNorm1d(256),
                nn.ReLU(),
                nn.Dropout(0.4), 
                nn.Linear(256, 128),
                nn.BatchNorm1d(128),
                nn.ReLU(),
                nn.Dropout(0.4),
                nn.Linear(128, 64),
                nn.BatchNorm1d(64),
                nn.ReLU(),
                nn.Dropout(0.4),
                nn.Linear(64, 1)
            )
        
        def forward(self, x):
            return self.model(x)

# Reuse functions and classes from the original file
def filter_high_missing_features(features: pd.DataFrame, threshold: float = 0.5) -> pd.DataFrame:
    """Filter out features with missing values exceeding the threshold percentage."""
    # Calculate percentage of missing values for each feature
    missing_pct = features.isna().mean()
    
    # Identify features to keep (missing values below threshold)
    features_to_keep = missing_pct[missing_pct <= threshold].index.tolist()
    
    # Log the filtering results
    initial_feature_count = features.shape[1]
    filtered_feature_count = len(features_to_keep)
    removed_count = initial_feature_count - filtered_feature_count
    
    if removed_count > 0:
        logger.info(f"Removed {removed_count} features with >{threshold*100:.1f}% missing values.")
        logger.info(f"Retained {filtered_feature_count} features out of {initial_feature_count}.")
        
        # Return the filtered features
        return features[features_to_keep]
    else:
        logger.info(f"No features exceeded the missing value threshold of {threshold*100:.1f}%.")
        return features

def load_data(feature_file: str, metadata_file: str, missing_threshold: float = 0.5) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """Load feature and metadata files."""
    logger.info(f"Loading features from {feature_file}")
    features = pd.read_csv(feature_file, sep='\t', index_col='genome_id')
    
    logger.info(f"Loading metadata from {metadata_file}")
    metadata = pd.read_csv(metadata_file, sep='\t', index_col='genome_id')
    
    # Get common samples
    common_indices = features.index.intersection(metadata.index)
    logger.info(f"Found {len(common_indices)} common samples")
    
    # Filter to common samples
    features = features.loc[common_indices]
    metadata = metadata.loc[common_indices]
    
    # Filter out features with high missing values
    features = filter_high_missing_features(features, threshold=missing_threshold)
    
    # Handle missing values
    features = features.fillna(0)
    metadata = metadata.fillna(0)
    
    return features, metadata

class FinalHybridModel:
    """Final hybrid model for predicting microbial growth rate and optimal temperature."""
    
    def __init__(self, n_growth_features: int = 250, n_temp_features: int = 300,
                 output_dir: str = "models/final_hybrid_model", temp_transform: str = "none",
                 growth_transform: str = "none", seed: int = 42):
        """Initialize the hybrid model."""
        self.n_growth_features = n_growth_features
        self.n_temp_features = n_temp_features
        self.output_dir = output_dir
        self.temp_transform = temp_transform
        self.growth_transform = growth_transform
        self.seed = seed
        
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)
        
        # Initialize components
        self.growth_model = None
        self.temp_model = None
        self.growth_scaler = None
        self.temp_scaler = None
        self.temp_feature_indices = None
        self.growth_feature_indices = None
        
        # Set up logging
        self.logger = logging.getLogger(__name__)
        file_handler = logging.FileHandler(os.path.join(output_dir, "training.log"))
        file_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
        self.logger.addHandler(file_handler)
        
        self.logger.info(f"Initialized FinalHybridModel with {n_growth_features} growth features and {n_temp_features} temperature features")
        self.logger.info(f"Temperature transformation: {temp_transform}")
        self.logger.info(f"Growth rate transformation: {growth_transform}")
    
    def _transform_temperature(self, temp: np.ndarray) -> np.ndarray:
        """Transform temperature values."""
        # Convert to numpy array if it's a pandas Series
        if hasattr(temp, 'values'):
            temp = temp.values
        # Skip transformation for now - using original values
        return temp
    
    def _inverse_transform_temperature(self, temp: np.ndarray) -> np.ndarray:
        """Inverse transform temperature values."""
        # Convert to numpy array if it's a pandas Series
        if hasattr(temp, 'values'):
            temp = temp.values
        # Skip transformation for now
        return temp
    
    def _transform_growth_rate(self, growth: np.ndarray) -> np.ndarray:
        """Transform growth rate values."""
        # Convert to numpy array if it's a pandas Series
        if hasattr(growth, 'values'):
            growth = growth.values
        
        # Handle log2 transformation
        if self.growth_transform == "log2":
            # Handle zero values
            min_nonzero = np.min(growth[growth > 0]) if np.any(growth > 0) else 1e-10
            epsilon = min(min_nonzero * 0.1, 1e-10)
            return np.log2(growth + epsilon)
        return growth
    
    def _inverse_transform_growth_rate(self, growth_transformed: np.ndarray) -> np.ndarray:
        """Inverse transform growth rate values."""
        # Convert to numpy array if it's a pandas Series
        if hasattr(growth_transformed, 'values'):
            growth_transformed = growth_transformed.values
        
        # Handle log2 transformation
        if self.growth_transform == "log2":
            min_nonzero = np.min(growth_transformed[growth_transformed > 0]) if np.any(growth_transformed > 0) else 1e-10
            epsilon = min(min_nonzero * 0.1, 1e-10)
            return np.power(2, growth_transformed) - epsilon
        return growth_transformed
    
    def fit(self, X: np.ndarray, y_growth: np.ndarray, y_temp: np.ndarray, seed: int = None):
        """Train the hybrid model."""
        # Use stored seed if none provided
        if seed is None:
            seed = self.seed
        
        # Set random seed for reproducibility inside this method
        torch.manual_seed(seed)
        np.random.seed(seed)
        if torch.cuda.is_available():
            torch.cuda.manual_seed_all(seed)
        
        # Split data
        X_train, X_val, y_growth_train, y_growth_val, y_temp_train, y_temp_val = train_test_split(
            X, y_growth, y_temp, test_size=0.2, random_state=seed
        )
        
        # Log data dimensions
        self.logger.info(f"Data split - Training: {X_train.shape[0]} samples, Validation: {X_val.shape[0]} samples")
        
        # Transform target values
        y_temp_train_transformed = self._transform_temperature(y_temp_train)
        y_temp_val_transformed = self._transform_temperature(y_temp_val)
        y_growth_train_transformed = self._transform_growth_rate(y_growth_train)
        y_growth_val_transformed = self._transform_growth_rate(y_growth_val)
        
        # Temperature feature selection
        if self.n_temp_features and X.shape[1] > self.n_temp_features:
            # Ensure we're working with numpy arrays
            X_train_np = X_train.values if hasattr(X_train, 'values') else X_train
            X_val_np = X_val.values if hasattr(X_val, 'values') else X_val
            y_temp_train_np = np.array(y_temp_train) if hasattr(y_temp_train, 'values') else y_temp_train
            
            # Select features for temperature using mutual information
            mi_values = mutual_info_regression(X_train_np, y_temp_train_np)
            temp_feature_indices = np.argsort(mi_values)[-self.n_temp_features:]
            X_train_temp = X_train_np[:, temp_feature_indices]
            X_val_temp = X_val_np[:, temp_feature_indices]
            
            self.temp_feature_indices = temp_feature_indices
            self.logger.info(f"Selected {self.n_temp_features} features for temperature prediction")
        else:
            X_train_np = X_train.values if hasattr(X_train, 'values') else X_train
            X_val_np = X_val.values if hasattr(X_val, 'values') else X_val
            X_train_temp = X_train_np
            X_val_temp = X_val_np
            self.temp_feature_indices = np.arange(X.shape[1])
        
        # Scale temperature data
        self.temp_scaler = RobustScaler()
        X_train_temp_scaled = self.temp_scaler.fit_transform(X_train_temp)
        X_val_temp_scaled = self.temp_scaler.transform(X_val_temp)
        
        # Growth rate feature selection
        if self.n_growth_features and X.shape[1] > self.n_growth_features:
            # Ensure we're working with numpy arrays
            X_train_np = X_train.values if hasattr(X_train, 'values') else X_train
            X_val_np = X_val.values if hasattr(X_val, 'values') else X_val
            y_growth_train_np = np.array(y_growth_train) if hasattr(y_growth_train, 'values') else y_growth_train
            
            mi_values = mutual_info_regression(X_train_np, y_growth_train_np)
            growth_feature_indices = np.argsort(mi_values)[-self.n_growth_features:]
            X_train_growth = X_train_np[:, growth_feature_indices]
            X_val_growth = X_val_np[:, growth_feature_indices]
            
            self.growth_feature_indices = growth_feature_indices
            self.logger.info(f"Selected {self.n_growth_features} features for growth rate prediction")
        else:
            X_train_np = X_train.values if hasattr(X_train, 'values') else X_train
            X_val_np = X_val.values if hasattr(X_val, 'values') else X_val
            X_train_growth = X_train_np
            X_val_growth = X_val_np
            self.growth_feature_indices = np.arange(X.shape[1])
        
        # Scale growth data
        self.growth_scaler = StandardScaler()
        X_train_growth_scaled = self.growth_scaler.fit_transform(X_train_growth)
        X_val_growth_scaled = self.growth_scaler.transform(X_val_growth)
        
        # Save feature indices
        joblib.dump(self.temp_feature_indices, os.path.join(self.output_dir, "temp_feature_indices.pkl"))
        joblib.dump(self.growth_feature_indices, os.path.join(self.output_dir, "growth_feature_indices.pkl"))
        
        # Initialize growth model
        if self.growth_model is None:
            growth_input_dim = X_train_growth.shape[1]
            self.logger.info(f"Initializing growth model with input dimension: {growth_input_dim}")
            self.growth_model = nn.Sequential(
                nn.Linear(growth_input_dim, 512),
                nn.BatchNorm1d(512),
                nn.GELU(),
                nn.Dropout(0.3),
                nn.Linear(512, 384),
                nn.BatchNorm1d(384),
                nn.GELU(),
                nn.Dropout(0.4),
                nn.Linear(384, 256),
                nn.BatchNorm1d(256),
                nn.GELU(),
                nn.Dropout(0.4),
                nn.Linear(256, 128),
                nn.BatchNorm1d(128),
                nn.GELU(),
                nn.Dropout(0.5),
                nn.Linear(128, 1)
            ).to(device)
        
        # Initialize temperature model using imported AdvancedTemperatureModel
        if self.temp_model is None:
            temp_input_dim = X_train_temp.shape[1]
            self.logger.info(f"Initializing temperature model with input dimension: {temp_input_dim}")
            
            # Create temperature model from the fixed implementation
            self.temp_model = AdvancedTemperatureModel(temp_input_dim).to(device)
            self.logger.info(f"Created advanced temperature model with {temp_input_dim} input features")
        
        # Create dataloaders for batch processing
        train_temp_loader = DataLoader(TensorDataset(
            torch.FloatTensor(X_train_temp_scaled).to(device),
            torch.FloatTensor(np.array(y_temp_train_transformed).reshape(-1, 1)).to(device)
        ), batch_size=32, shuffle=True)
        
        val_temp_loader = DataLoader(TensorDataset(
            torch.FloatTensor(X_val_temp_scaled).to(device),
            torch.FloatTensor(np.array(y_temp_val_transformed).reshape(-1, 1)).to(device)
        ), batch_size=32, shuffle=False)
        
        train_growth_loader = DataLoader(TensorDataset(
            torch.FloatTensor(X_train_growth_scaled).to(device),
            torch.FloatTensor(np.array(y_growth_train_transformed).reshape(-1, 1)).to(device)
        ), batch_size=32, shuffle=True)
        
        val_growth_loader = DataLoader(TensorDataset(
            torch.FloatTensor(X_val_growth_scaled).to(device),
            torch.FloatTensor(np.array(y_growth_val_transformed).reshape(-1, 1)).to(device)
        ), batch_size=32, shuffle=False)
        
        # Train models
        self.logger.info("Training models...")
        
        # Optimizers
        temp_optimizer = optim.Adam(self.temp_model.parameters(), lr=0.001)
        growth_optimizer = optim.Adam(self.growth_model.parameters(), lr=0.001)
        
        # Train for 100 epochs
        for epoch in range(100):
            # Train temperature model
            self.temp_model.train()
            temp_train_loss = 0.0
            
            for X_batch, y_batch in train_temp_loader:
                temp_optimizer.zero_grad()
                temp_pred = self.temp_model(X_batch)
                
                # Handle potential size mismatch due to the ensemble approach
                # If prediction has different batch size than target, adjust targets
                if temp_pred.size(0) != y_batch.size(0):
                    self.logger.info(f"Size mismatch in batch: pred={temp_pred.size()}, target={y_batch.size()}")
                    # Resize target to match prediction size
                    y_batch = y_batch[:temp_pred.size(0)]
                
                temp_loss = F.mse_loss(temp_pred, y_batch)
                temp_loss.backward()
                temp_optimizer.step()
                temp_train_loss += temp_loss.item() * temp_pred.size(0)  # Use actual batch size
            
            # Train growth model
            self.growth_model.train()
            growth_train_loss = 0.0
            
            for X_batch, y_batch in train_growth_loader:
                growth_optimizer.zero_grad()
                growth_pred = self.growth_model(X_batch)
                growth_loss = F.mse_loss(growth_pred, y_batch)
                growth_loss.backward()
                growth_optimizer.step()
                growth_train_loss += growth_loss.item() * X_batch.size(0)
            
            # Log every 10 epochs
            if (epoch + 1) % 10 == 0:
                # Validation pass for temperature model
                self.temp_model.eval()
                with torch.no_grad():
                    temp_val_preds = []
                    temp_val_targets = []
                    
                    for X_batch, y_batch in val_temp_loader:
                        batch_preds = self.temp_model(X_batch)
                        
                        # Handle potential size mismatch
                        if batch_preds.size(0) != y_batch.size(0):
                            y_batch = y_batch[:batch_preds.size(0)]
                        
                        temp_val_preds.extend(batch_preds.cpu().numpy().flatten())
                        temp_val_targets.extend(y_batch.cpu().numpy().flatten())
                    
                    # Calculate validation loss and metrics
                    temp_val_loss = mean_squared_error(temp_val_targets, temp_val_preds)
                
                self.logger.info(f"Epoch {epoch+1}/100")
                self.logger.info(f"Temp train loss: {temp_train_loss/len(train_temp_loader.dataset):.6f}")
                self.logger.info(f"Temp val loss: {temp_val_loss:.6f}")
                
                # Similar for growth rate if needed
                self.logger.info(f"Growth train loss: {growth_train_loss/len(train_growth_loader.dataset):.6f}")
        
        # Evaluate final models
        self.temp_model.eval()
        self.growth_model.eval()
        
        with torch.no_grad():
            # Temperature model evaluation
            temp_preds = []
            temp_targets = []
            
            # Evaluate in smaller batches to avoid memory issues
            temp_val_loader = DataLoader(
                TensorDataset(
                    torch.FloatTensor(X_val_temp_scaled).to(device),
                    torch.FloatTensor(np.array(y_temp_val_transformed).reshape(-1, 1)).to(device)
                ),
                batch_size=32, shuffle=False
            )
            
            for X_batch, y_batch in temp_val_loader:
                batch_preds = self.temp_model(X_batch).cpu().numpy()
                
                # Handle potential size mismatch
                if batch_preds.shape[0] != y_batch.size(0):
                    y_batch = y_batch[:batch_preds.shape[0]]
                
                temp_preds.extend(batch_preds.flatten())
                temp_targets.extend(y_batch.cpu().numpy().flatten())
            
            temp_r2 = r2_score(temp_targets, temp_preds)
            
            # Growth model evaluation - similar approach
            growth_preds = self.growth_model(torch.FloatTensor(X_val_growth_scaled).to(device))
            growth_preds = growth_preds.cpu().numpy().flatten()
            growth_r2 = r2_score(np.array(y_growth_val_transformed), growth_preds)
        
        self.logger.info(f"Final temperature model R²: {temp_r2:.4f}")
        self.logger.info(f"Final growth rate model R²: {growth_r2:.4f}")
        
        return self
    
    def predict(self, X: np.ndarray) -> np.ndarray:
        """Make predictions."""
        if self.temp_model is None:
            raise ValueError("Model not trained yet")
        
        # Convert to numpy array if it's a pandas DataFrame
        X_np = X.values if hasattr(X, 'values') else X
        
        # Apply feature selection
        if hasattr(self, 'temp_feature_indices') and self.temp_feature_indices is not None:
            X_temp = X_np[:, self.temp_feature_indices]
        else:
            X_temp = X_np
        
        # Scale features
        X_temp_scaled = self.temp_scaler.transform(X_temp)
        
        # Make temperature predictions
        self.temp_model.eval()
        with torch.no_grad():
            X_temp_tensor = torch.FloatTensor(X_temp_scaled).to(device)
            temp_pred_transformed = self.temp_model(X_temp_tensor).cpu().numpy().flatten()
        
        # Inverse transform predictions
        y_temp_pred = self._inverse_transform_temperature(temp_pred_transformed)
        
        return y_temp_pred
    
    def save(self, output_dir: str):
        """Save the model."""
        os.makedirs(output_dir, exist_ok=True)
        
        # Save temperature model
        if self.temp_model is not None:
            torch.save(self.temp_model.state_dict(), os.path.join(output_dir, "temp_model.pt"))
            joblib.dump(self.temp_scaler, os.path.join(output_dir, "temp_scaler.pkl"))
            joblib.dump(self.temp_feature_indices, os.path.join(output_dir, "temp_feature_indices.pkl"))
        
        # Save growth model
        if self.growth_model is not None:
            torch.save(self.growth_model.state_dict(), os.path.join(output_dir, "growth_model.pt"))
            joblib.dump(self.growth_scaler, os.path.join(output_dir, "growth_scaler.pkl"))
            joblib.dump(self.growth_feature_indices, os.path.join(output_dir, "growth_feature_indices.pkl"))
        
        self.logger.info(f"Model saved to {output_dir}")

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Train fixed hybrid model")
    parser.add_argument("--feature_file", required=True, help="Path to feature file")
    parser.add_argument("--metadata_file", required=True, help="Path to metadata file")
    parser.add_argument("--output_dir", default="models/fixed_hybrid_model", help="Output directory")
    parser.add_argument("--n_growth_features", type=int, default=250, help="Number of features for growth rate")
    parser.add_argument("--n_temp_features", type=int, default=300, help="Number of features for temperature")
    parser.add_argument("--seed", type=int, default=42, help="Random seed for reproducibility")
    
    args = parser.parse_args()
    
    # Set random seed for reproducibility
    torch.manual_seed(args.seed)
    np.random.seed(args.seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(args.seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    
    # Load data
    features, metadata = load_data(args.feature_file, args.metadata_file)
    
    # Create and train model
    model = FinalHybridModel(
        n_growth_features=args.n_growth_features,
        n_temp_features=args.n_temp_features,
        output_dir=args.output_dir,
        temp_transform="none",
        growth_transform="log2",
        seed=args.seed
    )
    
    model.fit(features, metadata['growth_rate'], metadata['optimal_temperature'], seed=args.seed)
    model.save(args.output_dir)
    
    logger.info("Training complete")

if __name__ == "__main__":
    main() 