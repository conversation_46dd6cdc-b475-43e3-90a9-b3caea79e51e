#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Training script for RL-based temperature prediction model.

This script trains a reinforcement learning model for optimal temperature prediction
using the TemperatureRLAgent and TemperatureRLTrainer classes.
"""

import os
import sys
import argparse
import json
import numpy as np
import pandas as pd
import torch
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
import logging
import joblib
from pathlib import Path
from typing import Dict, Optional

# Create logger
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import RL agent and trainer
from temperature_rl_agent import TemperatureRLAgent, VerifiableRewardFunction
from temperature_rl_trainer import TemperatureRLTrainer

# Import baseline model for comparison
from temperature_model import TemperatureModel, SimplifiedDNN

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Train RL-based temperature prediction model')
    
    # Input/output arguments
    parser.add_argument('--feature-file', type=str, default='training_data/combined_features.tsv',
                      help='Path to feature file (TSV format)')
    parser.add_argument('--metadata', type=str, default='training_data/metadata.tsv',
                      help='Path to metadata file (TSV format)')
    parser.add_argument('--output-dir', type=str, default='models/temperature_rl_model',
                      help='Directory to save model and results')
    parser.add_argument('--metrics-dir', type=str, default='metrics/temperature_rl_model',
                      help='Directory to save metrics and plots')
    parser.add_argument('--baseline-model', type=str, default=None,
                      help='Path to pre-trained baseline model (optional)')
    
    # Model parameters
    parser.add_argument('--hidden-dim', type=int, default=256,
                      help='Hidden dimension size')
    parser.add_argument('--num-heads', type=int, default=4,
                      help='Number of attention heads')
    parser.add_argument('--num-layers', type=int, default=2,
                      help='Number of transformer layers')
    parser.add_argument('--dropout', type=float, default=0.2,
                      help='Dropout rate')
    parser.add_argument('--use-layer-norm', action='store_true',
                      help='Use layer normalization')
    parser.add_argument('--use-residual', action='store_true',
                      help='Use residual connections')
    parser.add_argument('--use-value-head', action='store_true',
                      help='Use value head for uncertainty estimation')
    parser.add_argument('--activation', type=str, default='gelu', choices=['relu', 'gelu', 'silu'],
                      help='Activation function to use')
    
    # Reward function parameters
    parser.add_argument('--alpha', type=float, default=0.7,
                      help='Weight for accuracy component in reward function')
    parser.add_argument('--beta', type=float, default=0.2,
                      help='Weight for consistency component in reward function')
    parser.add_argument('--gamma', type=float, default=0.1,
                      help='Weight for improvement component in reward function')
    parser.add_argument('--accuracy-scale', type=float, default=1.0,
                      help='Scaling factor for accuracy component in reward function')
    
    # Training parameters
    parser.add_argument('--lr', type=float, default=1e-4,
                      help='Learning rate')
    parser.add_argument('--weight-decay', type=float, default=1e-5,
                      help='Weight decay for regularization')
    parser.add_argument('--batch-size', type=int, default=64,
                      help='Batch size for training')
    parser.add_argument('--epochs', type=int, default=200,
                      help='Number of epochs to train for')
    parser.add_argument('--early-stopping-patience', type=int, default=20,
                      help='Patience for early stopping')
    parser.add_argument('--no-lr-scheduler', action='store_true',
                      help='Disable learning rate scheduler')
    parser.add_argument('--no-experience-replay', action='store_true',
                      help='Disable experience replay')
    parser.add_argument('--replay-buffer-size', type=int, default=10000,
                      help='Size of experience replay buffer')
    parser.add_argument('--entropy-coef', type=float, default=0.01,
                      help='Coefficient for entropy regularization')
    parser.add_argument('--value-loss-coef', type=float, default=0.5,
                      help='Coefficient for value loss')
    parser.add_argument('--max-grad-norm', type=float, default=1.0,
                      help='Maximum gradient norm for clipping')
    parser.add_argument('--seed', type=int, default=42,
                      help='Random seed')
    parser.add_argument('--test-size', type=float, default=0.2,
                      help='Fraction of data to use for testing')
    parser.add_argument('--val-size', type=float, default=0.2,
                      help='Fraction of training data to use for validation')
    
    return parser.parse_args()

def load_data(feature_file, metadata_file):
    """
    Load feature and metadata files.
    
    Args:
        feature_file: Path to feature file (TSV format)
        metadata_file: Path to metadata file (TSV format)
        
    Returns:
        Tuple of (features_df, metadata_df)
    """
    logger.info(f"Loading features from {feature_file}")
    features_df = pd.read_csv(feature_file, sep='\t')
    
    logger.info(f"Loading metadata from {metadata_file}")
    metadata_df = pd.read_csv(metadata_file, sep='\t')
    
    # Merge on genome_id
    merged_df = pd.merge(features_df, metadata_df, on='genome_id', how='inner')
    
    logger.info(f"Loaded {len(merged_df)} samples with {len(features_df.columns)} features")
    
    return merged_df

def prepare_data(data_df, target='optimal_temperature', test_size=0.2, val_size=0.2, seed=42):
    """
    Prepare data for training.
    
    Args:
        data_df: DataFrame with features and targets
        target: Target column name
        test_size: Fraction of data to use for testing
        val_size: Fraction of training data to use for validation
        seed: Random seed
        
    Returns:
        Dictionary with train, val, and test data
    """
    # Separate features and target
    X = data_df.drop(['genome_id', 'growth_rate', 'optimal_temperature', 'taxid', 'codon_table', 'kingdom'], axis=1)
    
    # Handle NaN values
    logger.info(f"Handling NaN values in features")
    X = X.fillna(0)  # Replace NaN with 0
    
    y = data_df[target]
    
    # Split into train and test
    X_train_val, X_test, y_train_val, y_test = train_test_split(
        X, y, test_size=test_size, random_state=seed
    )
    
    # Split train into train and validation
    X_train, X_val, y_train, y_val = train_test_split(
        X_train_val, y_train_val, test_size=val_size, random_state=seed
    )
    
    # Scale features
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_val_scaled = scaler.transform(X_val)
    X_test_scaled = scaler.transform(X_test)
    
    # Convert to numpy arrays
    y_train = y_train.values
    y_val = y_val.values
    y_test = y_test.values
    
    # Create data dictionary
    data = {
        'train': {
            'features': X_train_scaled,
            'targets': y_train
        },
        'val': {
            'features': X_val_scaled,
            'targets': y_val
        },
        'test': {
            'features': X_test_scaled,
            'targets': y_test
        },
        'feature_names': X.columns.tolist(),
        'scaler': scaler
    }
    
    logger.info(f"Prepared data with {len(y_train)} training, {len(y_val)} validation, "
               f"and {len(y_test)} test samples")
    
    return data

def train_baseline_model(data, output_dir):
    """
    Train a baseline temperature model.
    
    Args:
        data: Dictionary with train, val, and test data
        output_dir: Directory to save the model
        
    Returns:
        Trained baseline model
    """
    logger.info("Training baseline temperature model")
    
    # Get data dimensions
    input_dim = data['train']['features'].shape[1]
    
    # Create model
    baseline_model = TemperatureModel(
        hidden_dims=[512, 512, 384, 256, 128],
        dropout_rates=[0.2, 0.25, 0.3, 0.35, 0.4],
        lr=0.001,
        batch_size=64,
        epochs=100,
        patience=20,
        use_batch_norm=True,
        activation='relu',
        output_dir=output_dir
    )
    
    # Convert NumPy arrays to DataFrames for compatibility
    feature_names = data['feature_names']
    X_train_df = pd.DataFrame(data['train']['features'], columns=feature_names)
    y_train_series = pd.Series(data['train']['targets'])
    X_val_df = pd.DataFrame(data['val']['features'], columns=feature_names)
    y_val_series = pd.Series(data['val']['targets'])
    
    # Train model
    baseline_model.fit(X_train_df, y_train_series, X_val_df, y_val_series)
    
    # Evaluate model
    X_test_df = pd.DataFrame(data['test']['features'], columns=feature_names)
    y_test_series = pd.Series(data['test']['targets'])
    metrics = baseline_model.evaluate(X_test_df, y_test_series)
    
    logger.info(f"Baseline model performance: R² = {metrics['r2']:.4f}, RMSE = {metrics['rmse']:.4f}, MAE = {metrics['mae']:.4f}")
    
    # Save model
    baseline_model_dir = os.path.join(output_dir, 'baseline')
    os.makedirs(baseline_model_dir, exist_ok=True)
    baseline_model.save(baseline_model_dir)
    
    return baseline_model

def train_rl_model(data, args, baseline_model=None):
    """
    Train an RL-based temperature prediction model.
    
    Args:
        data: Dictionary with train, val, and test data
        args: Command line arguments
        baseline_model: Optional baseline model for reward function
        
    Returns:
        Trained RL agent and trainer
    """
    logger.info("Training RL temperature prediction model")
    
    # Set random seed
    np.random.seed(args.seed)
    torch.manual_seed(args.seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(args.seed)
    
    # Get data dimensions
    input_dim = data['train']['features'].shape[1]
    
    # Create RL agent
    agent = TemperatureRLAgent(
        input_dim=input_dim,
        hidden_dim=args.hidden_dim,
        num_heads=args.num_heads,
        num_layers=args.num_layers,
        dropout=args.dropout,
        use_layer_norm=args.use_layer_norm,
        use_residual=args.use_residual,
        use_value_head=args.use_value_head,
        activation=args.activation
    )
    
    # Create reward function
    reward_fn = VerifiableRewardFunction(
        baseline_model=baseline_model,
        alpha=args.alpha,
        beta=args.beta,
        gamma=args.gamma,
        accuracy_scale=args.accuracy_scale
    )
    
    # Create trainer
    trainer = TemperatureRLTrainer(
        agent=agent,
        reward_fn=reward_fn,
        lr=args.lr,
        weight_decay=args.weight_decay,
        gamma=0.99,
        entropy_coef=args.entropy_coef,
        value_loss_coef=args.value_loss_coef,
        max_grad_norm=args.max_grad_norm,
        use_lr_scheduler=not args.no_lr_scheduler,
        use_experience_replay=not args.no_experience_replay,
        replay_buffer_size=args.replay_buffer_size,
        batch_size=args.batch_size
    )
    
    # Train model
    metrics = trainer.train(
        train_features=data['train']['features'],
        train_targets=data['train']['targets'],
        val_features=data['val']['features'],
        val_targets=data['val']['targets'],
        num_epochs=args.epochs,
        batch_size=args.batch_size,
        early_stopping_patience=args.early_stopping_patience,
        verbose=True,
        log_interval=5,
        save_best_model=True
    )
    
    return agent, trainer, metrics

def evaluate_model(agent, data, metrics_dir):
    """
    Evaluate the trained RL model.
    
    Args:
        agent: Trained RL agent
        data: Dictionary with train, val, and test data
        metrics_dir: Directory to save metrics and plots
        
    Returns:
        Dictionary of evaluation metrics
    """
    logger.info("Evaluating RL temperature prediction model")
    
    # Get the device that the model is on
    device = next(agent.parameters()).device
    
    # Prepare test data and move to the correct device
    test_features = torch.tensor(data['test']['features'], dtype=torch.float32).to(device)
    test_targets = data['test']['targets']
    
    # Make predictions
    agent.eval()
    with torch.no_grad():
        predictions = agent.predict(test_features)
    
    # Get temperature predictions
    test_pred = predictions['temperature']
    if isinstance(test_pred, torch.Tensor):
        test_pred = test_pred.cpu().numpy()
    
    # Calculate metrics
    test_r2 = r2_score(test_targets, test_pred)
    test_rmse = np.sqrt(mean_squared_error(test_targets, test_pred))
    test_mae = mean_absolute_error(test_targets, test_pred)
    
    logger.info(f"Test metrics:")
    logger.info(f"  R²: {test_r2:.4f}")
    logger.info(f"  RMSE: {test_rmse:.4f}")
    logger.info(f"  MAE: {test_mae:.4f}")
    
    # Create metrics dictionary
    metrics = {
        'test_r2': test_r2,
        'test_rmse': test_rmse,
        'test_mae': test_mae
    }
    
    # Save metrics
    os.makedirs(metrics_dir, exist_ok=True)
    metrics_path = os.path.join(metrics_dir, 'test_metrics.json')
    with open(metrics_path, 'w') as f:
        json.dump(metrics, f, indent=2)
    
    # Plot predictions vs. targets
    plt.figure(figsize=(10, 8))
    plt.scatter(test_targets, test_pred, alpha=0.5)
    plt.plot([min(test_targets), max(test_targets)], [min(test_targets), max(test_targets)], 'r--')
    plt.xlabel('True Optimal Temperature')
    plt.ylabel('Predicted Optimal Temperature')
    plt.title(f'RL Temperature Model Predictions (R² = {test_r2:.4f}, RMSE = {test_rmse:.4f})')
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    
    # Save plot
    plot_path = os.path.join(metrics_dir, 'predictions.png')
    plt.savefig(plot_path, dpi=300)
    plt.close()
    
    return metrics

def save_model_and_config(agent, trainer, args, output_dir):
    """
    Save model, trainer, and configuration.
    
    Args:
        agent: Trained RL agent
        trainer: RL trainer
        args: Command line arguments
        output_dir: Directory to save outputs
    """
    logger.info(f"Saving model and configuration to {output_dir}")
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Save model
    model_path = os.path.join(output_dir, 'temperature_rl_model.pt')
    trainer.save_model(model_path)
    
    # Save configuration
    config = vars(args)
    config_path = os.path.join(output_dir, 'config.json')
    with open(config_path, 'w') as f:
        json.dump(config, f, indent=2)
    
    # Save feature names
    if hasattr(agent, 'feature_importance'):
        importance = agent.get_feature_importance()
        if isinstance(importance, dict) and 'features' in importance:
            feature_importance = importance['features']
            np.save(os.path.join(output_dir, 'feature_importance.npy'), feature_importance)

def main():
    """Main function to train and evaluate the RL temperature model."""
    # Parse arguments
    args = parse_args()
    
    # Create output directories
    os.makedirs(args.output_dir, exist_ok=True)
    os.makedirs(args.metrics_dir, exist_ok=True)
    
    # Load data
    data_df = load_data(args.feature_file, args.metadata)
    
    # Prepare data
    data = prepare_data(
        data_df,
        target='optimal_temperature',
        test_size=args.test_size,
        val_size=args.val_size,
        seed=args.seed
    )
    
    # Train baseline model or load existing one
    baseline_model = None
    if args.baseline_model:
        # Load pre-trained baseline model
        try:
            baseline_model_dir = args.baseline_model
            baseline_model = TemperatureModel()
            baseline_model.load(baseline_model_dir)
            logger.info(f"Loaded baseline model from {baseline_model_dir}")
        except Exception as e:
            logger.warning(f"Failed to load baseline model: {e}")
            baseline_model = None
    else:
        # Train a new baseline model
        baseline_model = train_baseline_model(data, args.output_dir)
    
    # Train RL model
    agent, trainer, train_metrics = train_rl_model(data, args, baseline_model)
    
    # Plot training curves
    trainer.plot_training_curves(args.metrics_dir)
    
    # Evaluate model
    evaluate_model(agent, data, args.metrics_dir)
    
    # Save model and configuration
    save_model_and_config(agent, trainer, args, args.output_dir)
    
    logger.info("Training and evaluation complete")

if __name__ == "__main__":
    main() 