#!/usr/bin/env python
"""
Train a hybrid transformer neural network model for microbial growth rate and
optimal growth temperature prediction.

This script trains a hybrid model that combines a multi-branch architecture with
transformer-based fusion for modeling complex feature interactions.
"""

import os
import sys
import argparse
import logging
import json
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional, Union, Any

from deepmu.models.transformer_network import HybridTransformerDNN, TransformerTrainer
from deepmu.utils.logging import get_logger

# Set up logging
logger = get_logger()


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Train a hybrid transformer model')

    # Input data
    parser.add_argument('--feature-file', type=str, default='./training_data/combined_features.tsv',
                        help='Path to combined feature file (TSV)')
    parser.add_argument('--metadata', type=str, default='./training_data/metadata.tsv',
                        help='Path to metadata file (TSV)')

    # Output
    parser.add_argument('--output-dir', type=str, default='models/transformer',
                        help='Directory to save model and results')
    parser.add_argument('--metrics-dir', type=str, default=None,
                        help='Directory to save metrics and plots')

    # Training parameters
    parser.add_argument('--batch-size', type=int, default=32,
                        help='Batch size for training')
    parser.add_argument('--epochs', type=int, default=100,
                        help='Number of epochs to train')
    parser.add_argument('--learning-rate', type=float, default=0.001,
                        help='Learning rate for optimizer')
    parser.add_argument('--weight-decay', type=float, default=1e-5,
                        help='Weight decay for regularization')
    parser.add_argument('--patience', type=int, default=10,
                        help='Patience for early stopping')
    parser.add_argument('--test-size', type=float, default=0.2,
                        help='Test set size')
    parser.add_argument('--random-state', type=int, default=42,
                        help='Random state for reproducibility')

    # Model parameters
    parser.add_argument('--hidden-dims', type=int, nargs='+', default=[256, 128, 64],
                        help='Hidden layer dimensions')
    parser.add_argument('--dropout-rates', type=float, nargs='+', default=[0.2, 0.3, 0.4],
                        help='Dropout rates for different parts of the network')
    parser.add_argument('--transformer-heads', type=int, default=4,
                        help='Number of attention heads in transformer')
    parser.add_argument('--transformer-layers', type=int, default=2,
                        help='Number of transformer layers')
    parser.add_argument('--no-positional-encoding', action='store_true',
                        help='Disable positional encoding in transformer')
    parser.add_argument('--fusion-layers', type=int, default=2,
                        help='Number of layers in the fusion network')
    parser.add_argument('--no-batch-norm', action='store_true',
                        help='Disable batch normalization')
    parser.add_argument('--no-residual', action='store_true',
                        help='Disable residual connections')
    parser.add_argument('--single-task', action='store_true',
                        help='Train for a single task (growth rate only)')
    parser.add_argument('--target', type=str, default='both',
                        choices=['growth_rate', 'temperature', 'both'],
                        help='Target variable to predict')

    # Multi-task weights
    parser.add_argument('--growth-weight', type=float, default=0.5,
                        help='Weight for growth rate loss in multi-task learning')
    parser.add_argument('--temperature-weight', type=float, default=0.5,
                        help='Weight for temperature loss in multi-task learning')

    # Advanced training options
    parser.add_argument('--use-gradient-clipping', action='store_true',
                        help='Use gradient clipping')
    parser.add_argument('--max-grad-norm', type=float, default=1.0,
                        help='Maximum gradient norm for clipping')
    parser.add_argument('--lr-scheduler', type=str, default='plateau',
                        choices=['plateau', 'cosine', 'one_cycle', 'none'],
                        help='Learning rate scheduler type')
    parser.add_argument('--warmup-epochs', type=int, default=5,
                        help='Number of warmup epochs for learning rate schedulers')

    return parser.parse_args()


def load_data(feature_file: str, metadata_file: str) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """Load feature and metadata files.

    Args:
        feature_file: Path to feature file (TSV)
        metadata_file: Path to metadata file (TSV)

    Returns:
        Tuple of (features_df, metadata_df)
    """
    logger.info(f"Loading features from {feature_file}")
    try:
        features_df = pd.read_csv(feature_file, sep='\t', index_col='genome_id')
    except FileNotFoundError:
        logger.error(f"Feature file not found: {feature_file}")
        raise
    except Exception as e:
        logger.error(f"Error loading feature file: {str(e)}")
        raise

    logger.info(f"Loading metadata from {metadata_file}")
    try:
        metadata_df = pd.read_csv(metadata_file, sep='\t', index_col='genome_id')
    except FileNotFoundError:
        logger.error(f"Metadata file not found: {metadata_file}")
        raise
    except Exception as e:
        logger.error(f"Error loading metadata file: {str(e)}")
        raise

    # Get common genomes
    common_genomes = list(set(features_df.index) & set(metadata_df.index))
    logger.info(f"Found {len(common_genomes)} common genomes")

    if len(common_genomes) == 0:
        logger.error("No common genomes found between feature and metadata files")
        raise ValueError("No common genomes found")

    # Filter to common genomes
    features_df = features_df.loc[common_genomes]
    metadata_df = metadata_df.loc[common_genomes]

    # Check for NaN values in features
    nan_count = features_df.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in features. Filling with zeros.")
        features_df = features_df.fillna(0)

    # Check for NaN values in metadata
    nan_count = metadata_df.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in metadata. Removing affected rows.")
        metadata_df = metadata_df.dropna(subset=['growth_rate', 'optimal_temperature'])
        # Update common genomes
        common_genomes = list(set(features_df.index) & set(metadata_df.index))
        features_df = features_df.loc[common_genomes]
        metadata_df = metadata_df.loc[common_genomes]
        logger.info(f"After removing NaN values, {len(common_genomes)} common genomes remain")

    # Check for infinite values in features
    inf_count = np.isinf(features_df).sum().sum()
    if inf_count > 0:
        logger.warning(f"Found {inf_count} infinite values in features. Replacing with zeros.")
        features_df = features_df.replace([np.inf, -np.inf], 0)

    # Log data statistics
    logger.info(f"Feature statistics:")
    logger.info(f"  Shape: {features_df.shape}")
    logger.info(f"  Min: {features_df.min().min():.4f}")
    logger.info(f"  Max: {features_df.max().max():.4f}")
    logger.info(f"  Mean: {features_df.mean().mean():.4f}")
    logger.info(f"  Std: {features_df.std().mean():.4f}")
    
    logger.info(f"Metadata statistics:")
    logger.info(f"  Shape: {metadata_df.shape}")
    logger.info(f"  Growth rate range: {metadata_df['growth_rate'].min():.4f} to {metadata_df['growth_rate'].max():.4f}")
    logger.info(f"  Temperature range: {metadata_df['optimal_temperature'].min():.4f} to {metadata_df['optimal_temperature'].max():.4f}")

    return features_df, metadata_df


def categorize_features(features_df: pd.DataFrame) -> Dict[str, List[str]]:
    """Categorize features into different types.

    Args:
        features_df: DataFrame with features

    Returns:
        Dictionary mapping feature types to lists of feature names
    """
    feature_categories = {
        'codon': [],
        'aa': [],
        'genomic': [],
        'rna': [],
        'pi': [],
        'taxonomy': [],
        'pathway': []
    }

    # Print a few column names to help with debugging
    logger.info(f"Sample feature names: {list(features_df.columns[:10])}")
    logger.info(f"Total features: {len(features_df.columns)}")

    # Categorize features based on prefixes and keywords
    for feature in features_df.columns:
        # Convert to lowercase for case-insensitive matching
        feature_lower = feature.lower()

        # Codon features - updated to include renamed features
        if any(keyword in feature_lower for keyword in [
            'cub', 'cpb', 'cai', 'enc', 'rscu', 'codon', 'heg', 'bg', 'delta',
            'deviation', 'divergence', 'composite', 'context_bias'
        ]):
            feature_categories['codon'].append(feature)

        # Amino acid features
        elif any(keyword in feature_lower for keyword in [
            'aa_', 'amino', 'protein', 'arsc', 'nitrogen', 'carbon', 'c_arsc', 'n_arsc'
        ]):
            feature_categories['aa'].append(feature)

        # Genomic features
        elif any(keyword in feature_lower for keyword in [
            'genome', 'gc_', 'gc1', 'gc2', 'gc3', 'skew', 'dinuc', 'size', 'length', 'breakpoint'
        ]):
            feature_categories['genomic'].append(feature)

        # RNA features
        elif any(keyword in feature_lower for keyword in [
            'trna', 'rrna', 'tai', 'rna', 'rrna_count', 'trna_count'
        ]):
            feature_categories['rna'].append(feature)

        # Protein pI features
        elif any(keyword in feature_lower for keyword in [
            'pi_', 'isoelectric', 'ph', 'pi_mean', 'pi_median', 'pi_std'
        ]):
            feature_categories['pi'].append(feature)

        # Taxonomy features
        elif any(keyword in feature_lower for keyword in [
            'phylum', 'class', 'order', 'family', 'genus', 'taxid', 'taxonomy'
        ]):
            feature_categories['taxonomy'].append(feature)

        # Pathway features
        elif any(keyword in feature_lower for keyword in [
            'pathway', 'kegg', 'ko_', 'module', 'completeness'
        ]):
            feature_categories['pathway'].append(feature)

        # Default to codon features if not categorized
        else:
            # Check if it's likely a numeric feature (not an ID or metadata)
            if feature != 'genome_id' and not feature.endswith('_id') and not feature in ['growth_rate', 'optimal_temperature']:
                logger.info(f"Uncategorized feature: {feature} - adding to codon category")
                feature_categories['codon'].append(feature)

    # Log feature counts
    for category, features in feature_categories.items():
        logger.info(f"Found {len(features)} {category} features")
        if len(features) > 0:
            logger.info(f"Sample {category} features: {features[:5]}")

    # Check if any category is empty
    empty_categories = [category for category, features in feature_categories.items() if len(features) == 0]
    if empty_categories:
        logger.warning(f"No features found for categories: {', '.join(empty_categories)}")
        
    return feature_categories


def prepare_data(
    features_df: pd.DataFrame,
    metadata_df: pd.DataFrame,
    feature_categories: Dict[str, List[str]],
    target: str,
    test_size: float = 0.2,
    random_state: int = 42
) -> Dict[str, Dict[str, np.ndarray]]:
    """Prepare data for training.

    Args:
        features_df: Feature DataFrame
        metadata_df: Metadata DataFrame
        feature_categories: Dictionary mapping feature types to lists of feature names
        target: Target variable ('growth_rate', 'temperature', or 'both')
        test_size: Test set size
        random_state: Random state for reproducibility

    Returns:
        Dictionary with train and test data for each feature type and target
    """
    # Get target variables
    if target in ['growth_rate', 'both']:
        y_growth = metadata_df['growth_rate'].values
        logger.info(f"Growth rate statistics - Min: {y_growth.min():.4f}, Max: {y_growth.max():.4f}, Mean: {y_growth.mean():.4f}")

    if target in ['temperature', 'both']:
        y_temp = metadata_df['optimal_temperature'].values
        logger.info(f"Temperature statistics - Min: {y_temp.min():.4f}, Max: {y_temp.max():.4f}, Mean: {y_temp.mean():.4f}")

    # Split data
    if target == 'both':
        # Use the same split for both targets
        indices = np.arange(len(features_df))
        train_indices, test_indices = train_test_split(
            indices, test_size=test_size, random_state=random_state
        )
    elif target == 'growth_rate':
        X = features_df.values
        _, _, train_indices, test_indices = train_test_split(
            X, np.arange(len(X)), test_size=test_size, random_state=random_state
        )
    else:  # temperature
        X = features_df.values
        _, _, train_indices, test_indices = train_test_split(
            X, np.arange(len(X)), test_size=test_size, random_state=random_state
        )

    # Prepare data dictionary
    data = {
        'train': {
            'features': {},
            'targets': {}
        },
        'test': {
            'features': {},
            'targets': {}
        }
    }

    # Process each feature category
    for category, feature_names in feature_categories.items():
        if not feature_names:
            logger.warning(f"Skipping empty category: {category}")
            continue

        try:
            # Extract features
            X_category = features_df[feature_names].values

            # Split into train and test
            X_train = X_category[train_indices]
            X_test = X_category[test_indices]

            # Check for NaN or infinite values
            if np.isnan(X_train).any() or np.isnan(X_test).any():
                logger.warning(f"Found NaN values in {category} features. Filling with zeros.")
                X_train = np.nan_to_num(X_train, nan=0.0)
                X_test = np.nan_to_num(X_test, nan=0.0)

            if np.isinf(X_train).any() or np.isinf(X_test).any():
                logger.warning(f"Found infinite values in {category} features. Replacing with zeros.")
                X_train = np.nan_to_num(X_train, posinf=0.0, neginf=0.0)
                X_test = np.nan_to_num(X_test, posinf=0.0, neginf=0.0)

            # Scale features with error handling
            try:
                scaler = StandardScaler()
                X_train_scaled = scaler.fit_transform(X_train)
                X_test_scaled = scaler.transform(X_test)

                # Check for NaN or infinite values after scaling
                if np.isnan(X_train_scaled).any() or np.isnan(X_test_scaled).any():
                    logger.warning(f"Scaling produced NaN values in {category} features. Using robust scaling.")
                    # If standard scaling fails, try a more robust approach
                    X_train_scaled = np.nan_to_num(X_train_scaled, nan=0.0)
                    X_test_scaled = np.nan_to_num(X_test_scaled, nan=0.0)

                if np.isinf(X_train_scaled).any() or np.isinf(X_test_scaled).any():
                    logger.warning(f"Scaling produced infinite values in {category} features. Using robust scaling.")
                    X_train_scaled = np.nan_to_num(X_train_scaled, posinf=0.0, neginf=0.0)
                    X_test_scaled = np.nan_to_num(X_test_scaled, posinf=0.0, neginf=0.0)

                # Log scaling statistics
                logger.info(f"{category} features statistics after scaling:")
                logger.info(f"  Train - Min: {X_train_scaled.min():.4f}, Max: {X_train_scaled.max():.4f}, Mean: {X_train_scaled.mean():.4f}")
                logger.info(f"  Test - Min: {X_test_scaled.min():.4f}, Max: {X_test_scaled.max():.4f}, Mean: {X_test_scaled.mean():.4f}")

            except Exception as e:
                logger.warning(f"Error during scaling of {category} features: {str(e)}. Using unscaled data.")
                # If scaling fails completely, use the original data
                X_train_scaled = X_train
                X_test_scaled = X_test

            # Store in data dictionary
            data['train']['features'][category] = X_train_scaled
            data['test']['features'][category] = X_test_scaled

        except Exception as e:
            logger.error(f"Error processing {category} features: {str(e)}. Skipping this category.")
            continue

    # Add targets
    if target in ['growth_rate', 'both']:
        data['train']['targets']['growth_rate'] = y_growth[train_indices]
        data['test']['targets']['growth_rate'] = y_growth[test_indices]
        logger.info(f"Growth rate target statistics:")
        logger.info(f"  Train - Min: {data['train']['targets']['growth_rate'].min():.4f}, Max: {data['train']['targets']['growth_rate'].max():.4f}")
        logger.info(f"  Test - Min: {data['test']['targets']['growth_rate'].min():.4f}, Max: {data['test']['targets']['growth_rate'].max():.4f}")

    if target in ['temperature', 'both']:
        data['train']['targets']['temperature'] = y_temp[train_indices]
        data['test']['targets']['temperature'] = y_temp[test_indices]
        logger.info(f"Temperature target statistics:")
        logger.info(f"  Train - Min: {data['train']['targets']['temperature'].min():.4f}, Max: {data['train']['targets']['temperature'].max():.4f}")
        logger.info(f"  Test - Min: {data['test']['targets']['temperature'].min():.4f}, Max: {data['test']['targets']['temperature'].max():.4f}")

    return data


def plot_training_history(history: Dict[str, List[float]], output_dir: str):
    """Plot training history.

    Args:
        history: Dictionary with training history
        output_dir: Directory to save plots
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Plot loss
    plt.figure(figsize=(12, 8))
    plt.plot(history['loss'], label='Train Loss', linewidth=2)
    plt.plot(history['val_loss'], label='Validation Loss', linewidth=2)
    plt.xlabel('Epoch', fontsize=12)
    plt.ylabel('Loss', fontsize=12)
    plt.title('Training and Validation Loss', fontsize=14)
    plt.legend(fontsize=12)
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'loss.png'), dpi=300)
    plt.close()

    # Plot R-squared if available
    if 'val_growth_rate_r2' in history:
        plt.figure(figsize=(12, 8))
        plt.plot(history['val_growth_rate_r2'], label='Growth Rate R²', linewidth=2)
        plt.plot(history['val_temperature_r2'], label='Temperature R²', linewidth=2)
        plt.xlabel('Epoch', fontsize=12)
        plt.ylabel('R²', fontsize=12)
        plt.title('Validation R-squared', fontsize=14)
        plt.legend(fontsize=12)
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'r2.png'), dpi=300)
        plt.close()

        # Plot RMSE if available
        if 'val_growth_rate_rmse' in history:
            plt.figure(figsize=(12, 8))
            plt.plot(history['val_growth_rate_rmse'], label='Growth Rate RMSE', linewidth=2)
            plt.plot(history['val_temperature_rmse'], label='Temperature RMSE', linewidth=2)
            plt.xlabel('Epoch', fontsize=12)
            plt.ylabel('RMSE', fontsize=12)
            plt.title('Validation RMSE', fontsize=14)
            plt.legend(fontsize=12)
            plt.grid(True, alpha=0.3)
            plt.tight_layout()
            plt.savefig(os.path.join(output_dir, 'rmse.png'), dpi=300)
            plt.close()
    elif 'val_r2' in history:
        plt.figure(figsize=(12, 8))
        plt.plot(history['val_r2'], label='R²', linewidth=2)
        plt.xlabel('Epoch', fontsize=12)
        plt.ylabel('R²', fontsize=12)
        plt.title('Validation R-squared', fontsize=14)
        plt.legend(fontsize=12)
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'r2.png'), dpi=300)
        plt.close()

        # Plot RMSE if available
        if 'val_rmse' in history:
            plt.figure(figsize=(12, 8))
            plt.plot(history['val_rmse'], label='RMSE', linewidth=2)
            plt.xlabel('Epoch', fontsize=12)
            plt.ylabel('RMSE', fontsize=12)
            plt.title('Validation RMSE', fontsize=14)
            plt.legend(fontsize=12)
            plt.grid(True, alpha=0.3)
            plt.tight_layout()
            plt.savefig(os.path.join(output_dir, 'rmse.png'), dpi=300)
            plt.close()

    # Save history as CSV and TSV
    history_df = pd.DataFrame(history)
    history_df.to_csv(os.path.join(output_dir, 'training_history.csv'), index=False)
    history_df.to_csv(os.path.join(output_dir, 'training_history.tsv'), sep='\t', index=False)

    # Convert NumPy types to native Python types for JSON serialization
    history_json = {}
    for key, values in history.items():
        history_json[key] = [float(val) if hasattr(val, 'item') else val for val in values]

    # Save history as JSON for easier parsing
    with open(os.path.join(output_dir, 'training_history.json'), 'w') as f:
        json.dump(history_json, f, indent=2)


def plot_predictions(y_true: np.ndarray, y_pred: np.ndarray, output_dir: str, title: str, filename: str):
    """Plot true vs predicted values.

    Args:
        y_true: True values
        y_pred: Predicted values
        output_dir: Directory to save plot
        title: Plot title
        filename: Output filename
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Calculate metrics
    r2 = r2_score(y_true, y_pred)
    rmse = np.sqrt(mean_squared_error(y_true, y_pred))
    mae = mean_absolute_error(y_true, y_pred)

    # Plot true vs predicted
    plt.figure(figsize=(10, 8))
    plt.scatter(y_true, y_pred, alpha=0.5)

    # Add diagonal line
    min_val = min(np.min(y_true), np.min(y_pred))
    max_val = max(np.max(y_true), np.max(y_pred))
    plt.plot([min_val, max_val], [min_val, max_val], 'r--')

    # Add metrics to plot
    plt.text(0.05, 0.95, f'R² = {r2:.3f}\nRMSE = {rmse:.3f}\nMAE = {mae:.3f}',
             transform=plt.gca().transAxes, fontsize=12,
             verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.5))

    plt.xlabel('True Values', fontsize=12)
    plt.ylabel('Predicted Values', fontsize=12)
    plt.title(title, fontsize=14)
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, filename), dpi=300)
    plt.close()

    # Convert NumPy types to Python native types
    r2_float = float(r2)
    rmse_float = float(rmse)
    mae_float = float(mae)

    return r2_float, rmse_float, mae_float


def save_metrics_to_tsv(metrics: Dict[str, float], output_file: str):
    """Save metrics to a TSV file.

    Args:
        metrics: Dictionary with metrics
        output_file: Path to output file
    """
    # Convert any NumPy types to Python native types
    metrics_dict = {k: float(v) if hasattr(v, 'item') else v for k, v in metrics.items()}
    
    metrics_df = pd.DataFrame({
        'metric': list(metrics_dict.keys()),
        'value': list(metrics_dict.values())
    })

    metrics_df.to_csv(output_file, sep='\t', index=False)

    # Also save as JSON for easier parsing
    json_file = output_file.replace('.tsv', '.json')
    with open(json_file, 'w') as f:
        json.dump(metrics_dict, f, indent=2)


def main():
    """Main function."""
    # Parse arguments
    args = parse_args()

    try:
        # Create output directories
        os.makedirs(args.output_dir, exist_ok=True)

        metrics_dir = args.metrics_dir or os.path.join(args.output_dir, 'metrics')
        os.makedirs(metrics_dir, exist_ok=True)

        # Log all arguments
        logger.info("Training with the following parameters:")
        for arg, value in vars(args).items():
            logger.info(f"  {arg}: {value}")

        # Validate inputs
        for dim in args.hidden_dims:
            if dim <= 0:
                raise ValueError(f"Invalid hidden dimension: {dim}")
        
        for rate in args.dropout_rates:
            if not 0 <= rate < 1:
                raise ValueError(f"Invalid dropout rate: {rate}")
        
        if args.transformer_heads <= 0:
            raise ValueError(f"Invalid number of transformer heads: {args.transformer_heads}")
        
        if args.transformer_layers <= 0:
            raise ValueError(f"Invalid number of transformer layers: {args.transformer_layers}")

        # Load data
        features_df, metadata_df = load_data(args.feature_file, args.metadata)

        # Categorize features
        feature_categories = categorize_features(features_df)

        # Remove empty categories to avoid issues
        feature_categories = {k: v for k, v in feature_categories.items() if v}

        # Prepare data
        data = prepare_data(
            features_df,
            metadata_df,
            feature_categories,
            args.target,
            args.test_size,
            args.random_state
        )

        # Get feature dimensions
        feature_dims = {
            category: features.shape[1]
            for category, features in data['train']['features'].items()
        }

        # Log feature dimensions
        logger.info("Feature dimensions:")
        for category, dim in feature_dims.items():
            logger.info(f"  {category}: {dim}")

        # Create model
        model = HybridTransformerDNN(
            feature_dims=feature_dims,
            hidden_dims=args.hidden_dims,
            output_dim=1 if args.single_task else 2,
            dropout_rates=args.dropout_rates,
            use_batch_norm=not args.no_batch_norm,
            use_residual=not args.no_residual,
            transformer_heads=args.transformer_heads,
            transformer_layers=args.transformer_layers,
            use_positional_encoding=not args.no_positional_encoding,
            fusion_layers=args.fusion_layers,
            multi_task=(args.target == 'both' and not args.single_task)
        )

        # Log model architecture
        logger.info(f"Model architecture:\n{model}")

        # Initialize weights with Xavier/Glorot initialization
        for m in model.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.zeros_(m.bias)

        # Create trainer with available options
        trainer = TransformerTrainer(
            model=model,
            learning_rate=args.learning_rate,
            weight_decay=args.weight_decay
        )

        # Add gradient clipping to the trainer if requested
        if args.use_gradient_clipping:
            logger.info(f"Enabling gradient clipping with max norm {args.max_grad_norm}")
            
            # Cache the original train_epoch method
            original_train_epoch = trainer.train_epoch
            
            # Define a new train_epoch method with gradient clipping
            def train_epoch_with_clipping(train_loader, multi_task_weights=None):
                if multi_task_weights is None:
                    multi_task_weights = {'growth_rate': 0.5, 'temperature': 0.5}
                    
                # Set model to training mode
                trainer.model.train()
                
                total_loss = 0.0
                growth_rate_loss = 0.0
                temperature_loss = 0.0
                num_batches = 0
                
                for batch in train_loader:
                    # Get features and targets
                    features = {k: v.to(trainer.device) for k, v in batch['features'].items() if k in trainer.model.feature_dims}
                    targets = {k: v.to(trainer.device) for k, v in batch['targets'].items()}
                    
                    # Zero gradients
                    trainer.optimizer.zero_grad()
                    
                    # Forward pass
                    predictions = trainer.model(features)
                    
                    # Calculate loss
                    if trainer.model.multi_task:
                        # Multi-task loss
                        gr_loss = trainer.mse_loss(predictions['growth_rate'], targets['growth_rate'])
                        temp_loss = trainer.mse_loss(predictions['temperature'], targets['temperature'])
                        
                        # Weighted sum of losses
                        loss = multi_task_weights['growth_rate'] * gr_loss + multi_task_weights['temperature'] * temp_loss
                        
                        # Track individual losses
                        growth_rate_loss += gr_loss.item()
                        temperature_loss += temp_loss.item()
                    else:
                        # Single task loss - figure out which target we're using
                        target_key = next(iter(targets.keys()))  # Get the first (and only) target key
                        output_key = next(iter(predictions.keys()))  # Get the first output key
                        
                        # Log the keys for debugging
                        if num_batches == 0:
                            logger.info(f"Single-task mode: Using target key '{target_key}' and output key '{output_key}'")
                        
                        loss = trainer.mse_loss(predictions[output_key], targets[target_key])
                    
                    # Backward pass
                    loss.backward()
                    
                    # Apply gradient clipping before optimizer step
                    torch.nn.utils.clip_grad_norm_(trainer.model.parameters(), args.max_grad_norm)
                    
                    # Optimizer step
                    trainer.optimizer.step()
                    
                    # Track metrics
                    total_loss += loss.item()
                    num_batches += 1
                
                # Calculate average metrics
                metrics = {
                    'loss': total_loss / num_batches
                }
                
                if trainer.model.multi_task:
                    metrics.update({
                        'growth_rate_loss': growth_rate_loss / num_batches,
                        'temperature_loss': temperature_loss / num_batches
                    })
                
                return metrics
            
            # Replace the original train_epoch method with our version that includes gradient clipping
            trainer.train_epoch = train_epoch_with_clipping
            
        # Create custom datasets
        class CustomDataset(torch.utils.data.Dataset):
            def __init__(self, features, targets):
                self.features = features
                self.targets = targets
                self.length = len(next(iter(targets.values())))

            def __len__(self):
                return self.length

            def __getitem__(self, idx):
                return {
                    'features': {k: torch.tensor(v[idx], dtype=torch.float32) for k, v in self.features.items()},
                    'targets': {k: torch.tensor(v[idx], dtype=torch.float32) for k, v in self.targets.items()}
                }

        train_dataset = CustomDataset(data['train']['features'], data['train']['targets'])
        test_dataset = CustomDataset(data['test']['features'], data['test']['targets'])

        # Create dataloaders
        train_loader = DataLoader(train_dataset, batch_size=args.batch_size, shuffle=True)
        test_loader = DataLoader(test_dataset, batch_size=args.batch_size, shuffle=False)

        # Log dataset sizes
        logger.info(f"Training set size: {len(train_dataset)}")
        logger.info(f"Test set size: {len(test_dataset)}")

        # If lr_scheduler is specified and not 'plateau' (which is already in the trainer)
        if args.lr_scheduler != 'plateau' and args.lr_scheduler != 'none':
            logger.info(f"Using custom learning rate scheduler: {args.lr_scheduler}")
            # Calculate the total number of steps - train size / batch size * epochs
            train_size = len(train_dataset)
            steps_per_epoch = (train_size + args.batch_size - 1) // args.batch_size  # ceiling division
            total_steps = steps_per_epoch * args.epochs
            
            logger.info(f"Calculated {total_steps} total steps for scheduler (epochs={args.epochs}, steps_per_epoch={steps_per_epoch})")
            
            if args.lr_scheduler == 'cosine':
                # Create a cosine annealing scheduler
                scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
                    trainer.optimizer, 
                    T_max=total_steps
                )
            elif args.lr_scheduler == 'one_cycle':
                # Create a one-cycle scheduler
                scheduler = torch.optim.lr_scheduler.OneCycleLR(
                    trainer.optimizer, 
                    max_lr=args.learning_rate * 10, 
                    total_steps=total_steps
                )
            else:
                scheduler = None
            
            # If we created a custom scheduler, we need to override the trainer's step method
            if scheduler is not None:
                # Override the existing scheduler
                trainer.scheduler = scheduler

        # Set multi-task weights
        multi_task_weights = {
            'growth_rate': args.growth_weight,
            'temperature': args.temperature_weight
        }

        # Train model
        save_path = os.path.join(args.output_dir, 'best_model.pt')
        history = trainer.train(
            train_loader=train_loader,
            val_loader=test_loader,
            epochs=args.epochs,
            patience=args.patience,
            multi_task_weights=multi_task_weights,
            save_path=save_path
        )

        # Plot training history
        plot_training_history(history, metrics_dir)

        # Evaluate final model
        final_metrics = trainer.validate(test_loader, multi_task_weights)

        # Generate prediction plots
        # Get all test data
        all_test_features = {}
        all_test_targets = {}

        for batch in test_loader:
            for k, v in batch['features'].items():
                if k not in all_test_features:
                    all_test_features[k] = []
                all_test_features[k].append(v.numpy())

            for k, v in batch['targets'].items():
                if k not in all_test_targets:
                    all_test_targets[k] = []
                all_test_targets[k].append(v.numpy())

        # Concatenate batches
        for k in all_test_features:
            all_test_features[k] = np.concatenate(all_test_features[k])

        for k in all_test_targets:
            all_test_targets[k] = np.concatenate(all_test_targets[k])

        # Make predictions
        model.eval()
        with torch.no_grad():
            # Convert to tensors
            test_features_tensor = {k: torch.tensor(v, dtype=torch.float32) for k, v in all_test_features.items()}

            # Get predictions
            predictions = model(test_features_tensor)

        # Plot predictions for each target
        additional_metrics = {}

        if 'growth_rate' in all_test_targets and 'growth_rate' in predictions:
            growth_r2, growth_rmse, growth_mae = plot_predictions(
                all_test_targets['growth_rate'],
                predictions['growth_rate'].cpu().numpy(),
                metrics_dir,
                'Growth Rate Prediction',
                'growth_rate_predictions.png'
            )

            additional_metrics.update({
                'growth_rate_r2': growth_r2,
                'growth_rate_rmse': growth_rmse,
                'growth_rate_mae': growth_mae
            })

        if 'temperature' in all_test_targets and 'temperature' in predictions:
            temp_r2, temp_rmse, temp_mae = plot_predictions(
                all_test_targets['temperature'],
                predictions['temperature'].cpu().numpy(),
                metrics_dir,
                'Optimal Temperature Prediction',
                'temperature_predictions.png'
            )

            additional_metrics.update({
                'temperature_r2': temp_r2,
                'temperature_rmse': temp_rmse,
                'temperature_mae': temp_mae
            })

        # Update final metrics with additional metrics
        final_metrics.update(additional_metrics)

        # Save final metrics
        metrics_file = os.path.join(metrics_dir, 'metrics.tsv')
        save_metrics_to_tsv(final_metrics, metrics_file)

        # Save model configuration
        config = {
            'feature_dims': feature_dims,
            'hidden_dims': args.hidden_dims,
            'dropout_rates': args.dropout_rates,
            'transformer_heads': args.transformer_heads,
            'transformer_layers': args.transformer_layers,
            'use_batch_norm': not args.no_batch_norm,
            'use_residual': not args.no_residual,
            'use_positional_encoding': not args.no_positional_encoding,
            'fusion_layers': args.fusion_layers,
            'multi_task': (args.target == 'both' and not args.single_task),
            'use_gradient_clipping': args.use_gradient_clipping,
            'max_grad_norm': args.max_grad_norm,
            'lr_scheduler': args.lr_scheduler,
            'warmup_epochs': args.warmup_epochs
        }

        with open(os.path.join(args.output_dir, 'model_config.json'), 'w') as f:
            json.dump(config, f, indent=2)

        # Log final metrics
        logger.info("Final metrics:")
        for metric, value in final_metrics.items():
            logger.info(f"  {metric}: {value:.4f}")

        logger.info(f"Training complete. Best model saved to {save_path}")
        logger.info(f"Metrics and plots saved to {metrics_dir}")
        
        return 0
    
    except Exception as e:
        logger.error(f"Error during training: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return 1


if __name__ == '__main__':
    sys.exit(main())
