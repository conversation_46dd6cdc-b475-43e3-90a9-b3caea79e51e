#!/usr/bin/env python
"""
Train an improved Random Forest model for growth rate prediction.

This script trains an improved Random Forest model that incorporates:
1. Taxonomy information
2. Entropy-based breakpoint features
3. Emphasized codon features
4. Pathway completeness features
5. Codon usage divergence features
"""

import os
import argparse
import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from typing import Dict, List, Tuple, Optional, Union, Any

from deepmu.models.improved_rf import ImprovedRandomForest, ImprovedRandomForestTrainer
from deepmu.utils.logging import get_logger

# Set up logging
logger = get_logger()


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Train an improved Random Forest model')

    # Input data
    parser.add_argument('--feature-file', type=str, required=True,
                        help='Path to combined feature file (TSV)')
    parser.add_argument('--metadata', type=str, required=True,
                        help='Path to metadata file (TSV)')

    # Output
    parser.add_argument('--output-dir', type=str, required=True,
                        help='Directory to save model and results')
    parser.add_argument('--metrics-dir', type=str, default=None,
                        help='Directory to save metrics and plots')

    # Random Forest parameters
    parser.add_argument('--n-estimators', type=int, default=200,
                        help='Number of trees in random forest')
    parser.add_argument('--max-depth', type=int, default=None,
                        help='Maximum depth of trees in random forest')
    parser.add_argument('--min-samples-split', type=int, default=2,
                        help='Minimum samples required to split a node')
    parser.add_argument('--min-samples-leaf', type=int, default=1,
                        help='Minimum samples required at a leaf node')
    parser.add_argument('--random-state', type=int, default=42,
                        help='Random state for reproducibility')

    # Training parameters
    parser.add_argument('--test-size', type=float, default=0.2,
                        help='Test set size')

    return parser.parse_args()


def load_data(feature_file: str, metadata_file: str) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """Load feature and metadata files.

    Args:
        feature_file: Path to feature file (TSV)
        metadata_file: Path to metadata file (TSV)

    Returns:
        Tuple of (features_df, metadata_df)
    """
    logger.info(f"Loading features from {feature_file}")
    features_df = pd.read_csv(feature_file, sep='\t', index_col='genome_id')

    logger.info(f"Loading metadata from {metadata_file}")
    metadata_df = pd.read_csv(metadata_file, sep='\t', index_col='genome_id')

    # Get common genomes
    common_genomes = list(set(features_df.index) & set(metadata_df.index))
    logger.info(f"Found {len(common_genomes)} common genomes")

    # Filter to common genomes
    features_df = features_df.loc[common_genomes]
    metadata_df = metadata_df.loc[common_genomes]

    # Check for NaN values in features
    nan_count = features_df.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in features. Filling with zeros.")
        features_df = features_df.fillna(0)

    # Check for NaN values in metadata
    nan_count = metadata_df.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in metadata. Removing affected rows.")
        metadata_df = metadata_df.dropna(subset=['growth_rate', 'optimal_temperature'])
        # Update common genomes
        common_genomes = list(set(features_df.index) & set(metadata_df.index))
        features_df = features_df.loc[common_genomes]
        metadata_df = metadata_df.loc[common_genomes]
        logger.info(f"After removing NaN values, {len(common_genomes)} common genomes remain")

    # Check for infinite values in features
    inf_count = np.isinf(features_df).sum().sum()
    if inf_count > 0:
        logger.warning(f"Found {inf_count} infinite values in features. Replacing with zeros.")
        features_df = features_df.replace([np.inf, -np.inf], 0)

    return features_df, metadata_df


def categorize_features(features_df: pd.DataFrame) -> Dict[str, List[str]]:
    """Categorize features into different types.

    Args:
        features_df: DataFrame with features

    Returns:
        Dictionary mapping feature types to lists of feature names
    """
    feature_categories = {
        'codon': [],
        'aa': [],
        'genomic': [],
        'rna': [],
        'pi': [],
        'taxonomy': [],
        'pathway': []
    }

    # Print a few column names to help with debugging
    logger.info(f"Sample feature names: {list(features_df.columns[:10])}")
    logger.info(f"Total features: {len(features_df.columns)}")

    # Categorize features based on prefixes and keywords
    for feature in features_df.columns:
        # Convert to lowercase for case-insensitive matching
        feature_lower = feature.lower()

        # Codon features - updated to include renamed features
        if any(keyword in feature_lower for keyword in [
            'cub', 'cpb', 'cai', 'enc', 'rscu', 'codon', 'heg', 'bg', 'delta',
            'deviation', 'divergence', 'composite', 'context_bias'
        ]):
            feature_categories['codon'].append(feature)

        # Amino acid features
        elif any(keyword in feature_lower for keyword in [
            'aa_', 'amino', 'protein', 'arsc', 'nitrogen', 'carbon', 'c_arsc', 'n_arsc'
        ]):
            feature_categories['aa'].append(feature)

        # Genomic features (including breakpoint features)
        elif any(keyword in feature_lower for keyword in [
            'genome', 'gc_', 'gc1', 'gc2', 'gc3', 'dinuc', 'size', 'length', 'skew',
            '_change_points', '_sign_changes', '_mean', '_std', '_min', '_max', '_range',
            'breakpoint'
        ]):
            feature_categories['genomic'].append(feature)

        # RNA features
        elif any(keyword in feature_lower for keyword in [
            'trna', 'rrna', 'tai', 'rna', 'rrna_count', 'trna_count'
        ]):
            feature_categories['rna'].append(feature)

        # Protein pI features
        elif any(keyword in feature_lower for keyword in [
            'pi_', 'isoelectric', 'ph', 'pi_mean', 'pi_median', 'pi_std'
        ]):
            feature_categories['pi'].append(feature)

        # Taxonomy features
        elif any(keyword in feature_lower for keyword in [
            'phylum', 'class', 'order', 'family', 'genus', 'taxid', 'taxonomy'
        ]):
            feature_categories['taxonomy'].append(feature)

        # Pathway features
        elif any(keyword in feature_lower for keyword in [
            'pathway', 'kegg', 'ko_', 'module', 'completeness'
        ]) or feature.startswith('ko'):
            feature_categories['pathway'].append(feature)

        # Default to codon features if not categorized
        else:
            # Check if it's likely a numeric feature (not an ID or metadata)
            if feature != 'genome_id' and not feature.endswith('_id'):
                logger.info(f"Uncategorized feature: {feature}")
                feature_categories['codon'].append(feature)

    # Log feature counts
    for category, features in feature_categories.items():
        logger.info(f"Found {len(features)} {category} features")
        if len(features) > 0:
            logger.info(f"Sample {category} features: {features[:5]}")

    return feature_categories


def prepare_data(
    features_df: pd.DataFrame,
    metadata_df: pd.DataFrame,
    feature_categories: Dict[str, List[str]],
    test_size: float = 0.2,
    random_state: int = 42
) -> Dict[str, Dict[str, np.ndarray]]:
    """Prepare data for training.

    Args:
        features_df: Feature DataFrame
        metadata_df: Metadata DataFrame
        feature_categories: Dictionary mapping feature types to lists of feature names
        test_size: Test set size
        random_state: Random state for reproducibility

    Returns:
        Dictionary with train and test data for each feature type and target
    """
    # Get target variables
    y_growth = metadata_df['growth_rate'].values
    y_temp = metadata_df['optimal_temperature'].values

    # Combine all features into a single array
    all_feature_names = []
    for category, feature_names in feature_categories.items():
        if feature_names:
            logger.info(f"Adding {len(feature_names)} features from category '{category}'")
            all_feature_names.extend(feature_names)

    logger.info(f"Total features selected: {len(all_feature_names)}")

    # Extract features
    X = features_df[all_feature_names].values

    # Check for NaN or infinite values
    if np.isnan(X).any():
        logger.warning(f"Found NaN values in feature matrix. Filling with zeros.")
        X = np.nan_to_num(X, nan=0.0)

    if np.isinf(X).any():
        logger.warning(f"Found infinite values in feature matrix. Replacing with zeros.")
        X = np.nan_to_num(X, posinf=0.0, neginf=0.0)

    # Split data
    X_train, X_test, y_growth_train, y_growth_test, y_temp_train, y_temp_test = train_test_split(
        X, y_growth, y_temp, test_size=test_size, random_state=random_state
    )

    logger.info(f"Training set size: {len(X_train)}")
    logger.info(f"Test set size: {len(X_test)}")

    # Scale features with error handling
    try:
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)

        # Check for NaN or infinite values after scaling
        if np.isnan(X_train_scaled).any() or np.isnan(X_test_scaled).any():
            logger.warning(f"Scaling produced NaN values. Using robust scaling.")
            # If standard scaling fails, try a more robust approach
            X_train_scaled = np.nan_to_num(X_train_scaled, nan=0.0)
            X_test_scaled = np.nan_to_num(X_test_scaled, nan=0.0)

        if np.isinf(X_train_scaled).any() or np.isinf(X_test_scaled).any():
            logger.warning(f"Scaling produced infinite values. Using robust scaling.")
            X_train_scaled = np.nan_to_num(X_train_scaled, posinf=0.0, neginf=0.0)
            X_test_scaled = np.nan_to_num(X_test_scaled, posinf=0.0, neginf=0.0)

    except Exception as e:
        logger.warning(f"Error during scaling: {str(e)}. Using unscaled data.")
        # If scaling fails completely, use the original data
        X_train_scaled = X_train
        X_test_scaled = X_test

    # Prepare data dictionary
    data = {
        'train': {
            'features': X_train_scaled,
            'targets': {
                'growth_rate': y_growth_train,
                'temperature': y_temp_train
            }
        },
        'test': {
            'features': X_test_scaled,
            'targets': {
                'growth_rate': y_growth_test,
                'temperature': y_temp_test
            }
        }
    }

    return data, all_feature_names


def plot_feature_importance(importance_df: pd.DataFrame, output_dir: str, top_n: int = 20):
    """Plot feature importance.

    Args:
        importance_df: DataFrame with feature importance
        output_dir: Directory to save plot
        top_n: Number of top features to show
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Get top N features
    top_features = importance_df.head(top_n)

    # Plot feature importance
    plt.figure(figsize=(14, 10))
    bars = plt.barh(top_features['feature'], top_features['importance'], color='skyblue')
    plt.xlabel('Importance', fontsize=12)
    plt.ylabel('Feature', fontsize=12)
    plt.title(f'Top {top_n} Feature Importance for Growth Rate Prediction', fontsize=14)
    plt.grid(True, alpha=0.3, axis='x')

    # Add values to bars
    for bar in bars:
        width = bar.get_width()
        plt.text(width + 0.002, bar.get_y() + bar.get_height()/2,
                 f'{width:.4f}', ha='left', va='center', fontsize=10)

    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'feature_importance.png'), dpi=300)
    plt.close()

    # Plot feature importance by category
    try:
        # Extract category from feature name
        def get_category(feature_name):
            feature_lower = feature_name.lower()
            if any(keyword in feature_lower for keyword in ['cub', 'cpb', 'cai', 'enc', 'rscu', 'codon', 'heg', 'bg', 'delta', 'deviation', 'divergence']):
                return 'Codon'
            elif any(keyword in feature_lower for keyword in ['aa_', 'amino', 'protein', 'arsc']):
                return 'Amino Acid'
            elif any(keyword in feature_lower for keyword in ['genome', 'gc_', 'dinuc', 'skew', 'breakpoint']):
                return 'Genomic'
            elif any(keyword in feature_lower for keyword in ['trna', 'rrna', 'tai']):
                return 'RNA'
            elif any(keyword in feature_lower for keyword in ['pi_', 'isoelectric']):
                return 'Protein pI'
            elif any(keyword in feature_lower for keyword in ['phylum', 'class', 'order', 'family', 'genus', 'taxid']):
                return 'Taxonomy'
            elif any(keyword in feature_lower for keyword in ['pathway', 'kegg', 'ko_', 'module']) or feature_name.startswith('ko'):
                return 'Pathway'
            else:
                return 'Other'

        # Add category to importance DataFrame
        importance_df['category'] = importance_df['feature'].apply(get_category)

        # Group by category and sum importance
        category_importance = importance_df.groupby('category')['importance'].sum().reset_index()
        category_importance = category_importance.sort_values('importance', ascending=False)

        # Plot category importance
        plt.figure(figsize=(12, 8))
        bars = plt.bar(category_importance['category'], category_importance['importance'], color='lightgreen')
        plt.xlabel('Feature Category', fontsize=12)
        plt.ylabel('Total Importance', fontsize=12)
        plt.title('Feature Importance by Category', fontsize=14)
        plt.xticks(rotation=45, ha='right')
        plt.grid(True, alpha=0.3, axis='y')

        # Add values to bars
        for bar in bars:
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2, height + 0.01,
                     f'{height:.4f}', ha='center', va='bottom', fontsize=10)

        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'category_importance.png'), dpi=300)
        plt.close()

        # Save category importance to TSV
        category_importance.to_csv(os.path.join(output_dir, 'category_importance.tsv'), sep='\t', index=False)

        # Plot top features by category
        for category in category_importance['category']:
            cat_features = importance_df[importance_df['category'] == category].head(10)
            if len(cat_features) > 0:
                plt.figure(figsize=(12, 8))
                bars = plt.barh(cat_features['feature'], cat_features['importance'], color='lightblue')
                plt.xlabel('Importance', fontsize=12)
                plt.ylabel('Feature', fontsize=12)
                plt.title(f'Top Features in {category} Category', fontsize=14)
                plt.grid(True, alpha=0.3, axis='x')

                # Add values to bars
                for bar in bars:
                    width = bar.get_width()
                    plt.text(width + 0.002, bar.get_y() + bar.get_height()/2,
                             f'{width:.4f}', ha='left', va='center', fontsize=10)

                plt.tight_layout()
                plt.savefig(os.path.join(output_dir, f'{category.lower()}_importance.png'), dpi=300)
                plt.close()

                # Save category features to TSV
                cat_features.to_csv(os.path.join(output_dir, f'{category.lower()}_features.tsv'), sep='\t', index=False)

    except Exception as e:
        logger.warning(f"Error creating category-based feature importance plots: {str(e)}")


def plot_predictions(
    model: ImprovedRandomForest,
    test_features: np.ndarray,
    test_targets: np.ndarray,
    output_dir: str = 'metrics/improved_rf'
):
    """Plot predictions against true values.

    Args:
        model: ImprovedRandomForest model
        test_features: Test feature array
        test_targets: Test target values
        output_dir: Directory to save plots
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Predict
    predictions = model.predict(test_features)

    # Calculate metrics
    r2 = r2_score(test_targets, predictions)
    rmse = np.sqrt(mean_squared_error(test_targets, predictions))
    mae = mean_absolute_error(test_targets, predictions)

    # Create scatter plot
    plt.figure(figsize=(12, 10))
    plt.scatter(test_targets, predictions, alpha=0.7, color='blue', edgecolor='k', s=100)

    # Add identity line
    min_val = min(min(test_targets), min(predictions))
    max_val = max(max(test_targets), max(predictions))
    plt.plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2)

    plt.title('Predicted vs. True Growth Rate', fontsize=16)
    plt.xlabel('True Growth Rate', fontsize=14)
    plt.ylabel('Predicted Growth Rate', fontsize=14)

    # Add metrics text
    plt.text(0.05, 0.95, f"R² = {r2:.4f}", transform=plt.gca().transAxes, fontsize=14,
             bbox=dict(facecolor='white', alpha=0.8, edgecolor='gray', boxstyle='round,pad=0.5'))
    plt.text(0.05, 0.88, f"RMSE = {rmse:.4f}", transform=plt.gca().transAxes, fontsize=14,
             bbox=dict(facecolor='white', alpha=0.8, edgecolor='gray', boxstyle='round,pad=0.5'))
    plt.text(0.05, 0.81, f"MAE = {mae:.4f}", transform=plt.gca().transAxes, fontsize=14,
             bbox=dict(facecolor='white', alpha=0.8, edgecolor='gray', boxstyle='round,pad=0.5'))

    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'growth_rate_predictions.png'), dpi=300)
    plt.close()

    # Try to plot with uncertainty if available
    try:
        mean_predictions, std_predictions = model.predict_with_uncertainty(test_features)

        plt.figure(figsize=(12, 10))
        plt.errorbar(test_targets, mean_predictions, yerr=std_predictions, fmt='o', alpha=0.7,
                     ecolor='lightgray', capsize=5, markersize=8, markeredgecolor='k')

        # Add identity line
        plt.plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2)

        plt.title('Predicted vs. True Growth Rate (with Uncertainty)', fontsize=16)
        plt.xlabel('True Growth Rate', fontsize=14)
        plt.ylabel('Predicted Growth Rate', fontsize=14)

        # Add metrics text
        r2_mean = r2_score(test_targets, mean_predictions)
        rmse_mean = np.sqrt(mean_squared_error(test_targets, mean_predictions))
        mae_mean = mean_absolute_error(test_targets, mean_predictions)

        plt.text(0.05, 0.95, f"R² = {r2_mean:.4f}", transform=plt.gca().transAxes, fontsize=14,
                 bbox=dict(facecolor='white', alpha=0.8, edgecolor='gray', boxstyle='round,pad=0.5'))
        plt.text(0.05, 0.88, f"RMSE = {rmse_mean:.4f}", transform=plt.gca().transAxes, fontsize=14,
                 bbox=dict(facecolor='white', alpha=0.8, edgecolor='gray', boxstyle='round,pad=0.5'))
        plt.text(0.05, 0.81, f"MAE = {mae_mean:.4f}", transform=plt.gca().transAxes, fontsize=14,
                 bbox=dict(facecolor='white', alpha=0.8, edgecolor='gray', boxstyle='round,pad=0.5'))
        plt.text(0.05, 0.74, f"Mean Uncertainty = {np.mean(std_predictions):.4f}", transform=plt.gca().transAxes, fontsize=14,
                 bbox=dict(facecolor='white', alpha=0.8, edgecolor='gray', boxstyle='round,pad=0.5'))

        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'growth_rate_predictions_with_uncertainty.png'), dpi=300)
        plt.close()

        # Save predictions and uncertainty to TSV
        pred_df = pd.DataFrame({
            'true_growth_rate': test_targets,
            'predicted_growth_rate': mean_predictions,
            'uncertainty': std_predictions
        })
        pred_df.to_csv(os.path.join(output_dir, 'predictions_with_uncertainty.tsv'), sep='\t', index=False)

    except Exception as e:
        logger.warning(f"Error creating uncertainty plots: {str(e)}")


def save_metrics_to_tsv(metrics: Dict[str, float], output_file: str):
    """Save metrics to a TSV file.

    Args:
        metrics: Dictionary with metrics
        output_file: Path to output file
    """
    metrics_df = pd.DataFrame({
        'metric': list(metrics.keys()),
        'value': list(metrics.values())
    })

    metrics_df.to_csv(output_file, sep='\t', index=False)

    # Also save as JSON for easier parsing
    json_file = output_file.replace('.tsv', '.json')
    with open(json_file, 'w') as f:
        json.dump(metrics, f, indent=2)


def main():
    """Main function."""
    # Parse arguments
    args = parse_args()

    # Create output directories
    os.makedirs(args.output_dir, exist_ok=True)

    if args.metrics_dir:
        os.makedirs(args.metrics_dir, exist_ok=True)
    else:
        args.metrics_dir = os.path.join(args.output_dir, 'metrics')
        os.makedirs(args.metrics_dir, exist_ok=True)

    # Log all arguments
    logger.info("Training with the following parameters:")
    for arg, value in vars(args).items():
        logger.info(f"  {arg}: {value}")

    # Load data
    features_df, metadata_df = load_data(args.feature_file, args.metadata)

    # Categorize features
    feature_categories = categorize_features(features_df)

    # Prepare data
    data, feature_names = prepare_data(
        features_df,
        metadata_df,
        feature_categories,
        args.test_size,
        args.random_state
    )

    # Create model
    model = ImprovedRandomForest(
        n_estimators=args.n_estimators,
        max_depth=args.max_depth,
        min_samples_split=args.min_samples_split,
        min_samples_leaf=args.min_samples_leaf,
        random_state=args.random_state
    )

    # Create trainer
    trainer = ImprovedRandomForestTrainer(model=model)

    # Train model
    logger.info("Starting model training...")
    training_result = trainer.train(
        data=data,
        feature_names=feature_names,
        output_dir=args.output_dir
    )
    logger.info("Model training completed.")

    # Get feature importance
    importance_df = model.get_feature_importance()

    # Plot feature importance
    logger.info("Generating feature importance plots...")
    plot_feature_importance(importance_df, args.metrics_dir)

    # Plot predictions
    logger.info("Generating prediction plots...")
    plot_predictions(
        model=model,
        test_features=data['test']['features'],
        test_targets=data['test']['targets']['growth_rate'],
        output_dir=args.metrics_dir
    )

    # Save metrics
    metrics_file = os.path.join(args.metrics_dir, 'metrics.tsv')
    save_metrics_to_tsv(model.get_metrics(), metrics_file)

    # Save feature importance
    importance_file = os.path.join(args.metrics_dir, 'feature_importance.tsv')
    importance_df.to_csv(importance_file, sep='\t', index=False)

    # Save top 50 features
    top_features_file = os.path.join(args.metrics_dir, 'top_features.tsv')
    importance_df.head(50).to_csv(top_features_file, sep='\t', index=False)

    # Save model configuration
    config = {
        'n_estimators': args.n_estimators,
        'max_depth': args.max_depth,
        'min_samples_split': args.min_samples_split,
        'min_samples_leaf': args.min_samples_leaf,
        'random_state': args.random_state,
        'feature_count': len(feature_names),
        'training_samples': len(data['train']['features']),
        'test_samples': len(data['test']['features'])
    }

    with open(os.path.join(args.output_dir, 'model_config.json'), 'w') as f:
        json.dump(config, f, indent=2)

    # Log final metrics
    logger.info("Final metrics:")
    for metric, value in model.get_metrics().items():
        logger.info(f"  {metric}: {value:.4f}")

    # Try to get additional metrics
    try:
        # Calculate MAE
        predictions = model.predict(data['test']['features'])
        mae = mean_absolute_error(data['test']['targets']['growth_rate'], predictions)
        logger.info(f"  MAE: {mae:.4f}")

        # Update metrics with MAE
        updated_metrics = model.get_metrics()
        updated_metrics['mae'] = mae
        save_metrics_to_tsv(updated_metrics, metrics_file)
    except Exception as e:
        logger.warning(f"Error calculating additional metrics: {str(e)}")

    logger.info(f"Training complete. Model saved to {args.output_dir}")
    logger.info(f"Metrics and plots saved to {args.metrics_dir}")


if __name__ == '__main__':
    main()
