#!/usr/bin/env python3
"""
Regularized Random Forest model for growth rate prediction.
This script focuses on feature selection and regularization to improve model performance.
"""

import os
import argparse
import logging
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.preprocessing import StandardScaler
from sklearn.impute import SimpleImputer
from sklearn.feature_selection import SelectFromModel

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_data(feature_file, metadata_file):
    """
    Load feature and metadata files.
    
    Args:
        feature_file: Path to the feature file
        metadata_file: Path to the metadata file
        
    Returns:
        Tuple of (features, metadata)
    """
    # Load features
    features = pd.read_csv(feature_file, sep='\t', index_col=0)
    
    # Load metadata
    metadata = pd.read_csv(metadata_file, sep='\t', index_col=0)
    
    # Ensure the indices match
    common_indices = features.index.intersection(metadata.index)
    features = features.loc[common_indices]
    metadata = metadata.loc[common_indices]
    
    logger.info(f"Loaded {len(features)} samples with {features.shape[1]} features")
    
    return features, metadata

def preprocess_data(features, targets):
    """
    Preprocess the data for model training.
    
    Args:
        features: Feature DataFrame
        targets: Target DataFrame
        
    Returns:
        Tuple of (X_train, X_test, y_train, y_test)
    """
    # Check for NaN values
    nan_features = features.isna().sum().sum()
    nan_targets = targets.isna().sum().sum()
    
    logger.info(f"NaN values in features: {nan_features}, NaN values in targets: {nan_targets}")
    
    # Convert all features to float64
    features = features.astype(float)
    
    # Split the data
    X_train, X_test, y_train, y_test = train_test_split(
        features, targets, test_size=0.2, random_state=42
    )
    
    # Impute missing values
    imputer = SimpleImputer(strategy='mean')
    X_train = pd.DataFrame(
        imputer.fit_transform(X_train),
        columns=X_train.columns,
        index=X_train.index
    )
    X_test = pd.DataFrame(
        imputer.transform(X_test),
        columns=X_test.columns,
        index=X_test.index
    )
    
    # Scale the features
    scaler = StandardScaler()
    X_train = pd.DataFrame(
        scaler.fit_transform(X_train),
        columns=X_train.columns,
        index=X_train.index
    )
    X_test = pd.DataFrame(
        scaler.transform(X_test),
        columns=X_test.columns,
        index=X_test.index
    )
    
    logger.info(f"Preprocessed data: X_train: {X_train.shape}, X_test: {X_test.shape}")
    
    return X_train, X_test, y_train, y_test

def select_features(X_train, X_test, y_train, target_name):
    """
    Select important features using a preliminary Random Forest model.
    
    Args:
        X_train: Training features
        X_test: Testing features
        y_train: Training target
        target_name: Name of the target variable
        
    Returns:
        Tuple of (X_train_selected, X_test_selected, selected_feature_names)
    """
    # Train a preliminary Random Forest model
    preliminary_model = RandomForestRegressor(
        n_estimators=100,
        max_depth=10,  # Limit depth to prevent overfitting
        min_samples_split=5,
        min_samples_leaf=2,
        random_state=42,
        n_jobs=-1
    )
    
    logger.info(f"Training preliminary model for feature selection ({target_name})...")
    preliminary_model.fit(X_train, y_train)
    
    # Select features based on importance
    selector = SelectFromModel(
        preliminary_model,
        threshold='mean',  # Use mean feature importance as threshold
        prefit=True
    )
    
    # Transform the data
    X_train_selected = selector.transform(X_train)
    X_test_selected = selector.transform(X_test)
    
    # Get the selected feature names
    selected_indices = selector.get_support(indices=True)
    selected_feature_names = X_train.columns[selected_indices].tolist()
    
    logger.info(f"Selected {len(selected_feature_names)} features for {target_name}")
    
    # Convert back to DataFrame
    X_train_selected = pd.DataFrame(
        X_train_selected,
        columns=selected_feature_names,
        index=X_train.index
    )
    X_test_selected = pd.DataFrame(
        X_test_selected,
        columns=selected_feature_names,
        index=X_test.index
    )
    
    return X_train_selected, X_test_selected, selected_feature_names

def train_regularized_rf(X_train, X_test, y_train, y_test, target_name, output_dir):
    """
    Train and evaluate a regularized Random Forest model.
    
    Args:
        X_train: Training features
        X_test: Testing features
        y_train: Training targets
        y_test: Testing targets
        target_name: Name of the target variable
        output_dir: Directory to save outputs
        
    Returns:
        Dictionary of metrics
    """
    # Select features
    X_train_selected, X_test_selected, selected_feature_names = select_features(
        X_train, X_test, y_train, target_name
    )
    
    # Create a regularized Random Forest model
    model = RandomForestRegressor(
        n_estimators=200,
        max_depth=15,  # Limit depth to prevent overfitting
        min_samples_split=5,
        min_samples_leaf=2,
        max_features='sqrt',  # Use sqrt(n_features) for each tree
        bootstrap=True,
        oob_score=True,  # Use out-of-bag samples to estimate the generalization score
        random_state=42,
        n_jobs=-1
    )
    
    # Perform cross-validation
    cv_scores = cross_val_score(
        model, X_train_selected, y_train, 
        cv=5, scoring='neg_mean_squared_error'
    )
    cv_rmse = np.sqrt(-cv_scores.mean())
    logger.info(f"Cross-validation RMSE: {cv_rmse:.4f}")
    
    # Train the model
    logger.info(f"Training regularized Random Forest model for {target_name}...")
    model.fit(X_train_selected, y_train)
    
    # Make predictions
    y_pred_train = model.predict(X_train_selected)
    y_pred_test = model.predict(X_test_selected)
    
    # Calculate metrics
    train_mse = mean_squared_error(y_train, y_pred_train)
    test_mse = mean_squared_error(y_test, y_pred_test)
    train_rmse = np.sqrt(train_mse)
    test_rmse = np.sqrt(test_mse)
    train_r2 = r2_score(y_train, y_pred_train)
    test_r2 = r2_score(y_test, y_pred_test)
    oob_score = model.oob_score_
    
    logger.info(f"Training MSE: {train_mse:.4f}, RMSE: {train_rmse:.4f}, R²: {train_r2:.4f}")
    logger.info(f"Testing MSE: {test_mse:.4f}, RMSE: {test_rmse:.4f}, R²: {test_r2:.4f}")
    logger.info(f"Out-of-bag score: {oob_score:.4f}")
    
    # Plot actual vs predicted
    plt.figure(figsize=(10, 6))
    plt.scatter(y_test, y_pred_test, alpha=0.5)
    plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--')
    plt.xlabel(f'Actual {target_name}')
    plt.ylabel(f'Predicted {target_name}')
    plt.title(f'Regularized Random Forest: Actual vs Predicted {target_name}')
    plt.savefig(os.path.join(output_dir, f'regularized_rf_{target_name}_actual_vs_predicted.png'))
    plt.close()
    
    # Plot feature importance
    feature_importance = pd.DataFrame({
        'Feature': selected_feature_names,
        'Importance': model.feature_importances_
    }).sort_values('Importance', ascending=False)
    
    plt.figure(figsize=(12, 8))
    sns.barplot(x='Importance', y='Feature', data=feature_importance.head(20))
    plt.title(f'Top 20 Feature Importance for {target_name}')
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, f'regularized_rf_{target_name}_feature_importance.png'))
    plt.close()
    
    # Save feature importance to CSV
    feature_importance.to_csv(os.path.join(output_dir, f'regularized_rf_{target_name}_feature_importance.csv'), index=False)
    
    # Save selected features
    with open(os.path.join(output_dir, f'regularized_rf_{target_name}_selected_features.txt'), 'w') as f:
        for feature in selected_feature_names:
            f.write(f"{feature}\n")
    
    return {
        'train_mse': train_mse,
        'test_mse': test_mse,
        'train_rmse': train_rmse,
        'test_rmse': test_rmse,
        'train_r2': train_r2,
        'test_r2': test_r2,
        'oob_score': oob_score,
        'cv_rmse': cv_rmse,
        'selected_features': selected_feature_names
    }

def main():
    parser = argparse.ArgumentParser(description="Train a regularized Random Forest model for growth rate prediction")
    parser.add_argument("--feature_file", required=True, help="Path to the feature file")
    parser.add_argument("--metadata_file", required=True, help="Path to the metadata file")
    parser.add_argument("--output_dir", default="models/regularized_rf_model", help="Directory to save model outputs")
    args = parser.parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Load data
    features, metadata = load_data(args.feature_file, args.metadata_file)
    
    # Extract target variables
    targets = metadata[['growth_rate', 'optimal_temperature']]
    
    # Preprocess data
    X_train, X_test, y_train, y_test = preprocess_data(features, targets)
    
    # Train and evaluate models for each target
    results = {}
    for target in targets.columns:
        logger.info(f"Processing target: {target}")
        results[target] = train_regularized_rf(
            X_train, X_test, y_train[target], y_test[target], target, args.output_dir
        )
    
    # Save overall results
    results_df = pd.DataFrame({
        'Metric': [
            'Train MSE', 'Test MSE', 'Train RMSE', 'Test RMSE', 
            'Train R²', 'Test R²', 'OOB Score', 'CV RMSE', 'Selected Features Count'
        ],
        'Growth Rate': [
            results['growth_rate']['train_mse'],
            results['growth_rate']['test_mse'],
            results['growth_rate']['train_rmse'],
            results['growth_rate']['test_rmse'],
            results['growth_rate']['train_r2'],
            results['growth_rate']['test_r2'],
            results['growth_rate']['oob_score'],
            results['growth_rate']['cv_rmse'],
            len(results['growth_rate']['selected_features'])
        ],
        'Optimal Temperature': [
            results['optimal_temperature']['train_mse'],
            results['optimal_temperature']['test_mse'],
            results['optimal_temperature']['train_rmse'],
            results['optimal_temperature']['test_rmse'],
            results['optimal_temperature']['train_r2'],
            results['optimal_temperature']['test_r2'],
            results['optimal_temperature']['oob_score'],
            results['optimal_temperature']['cv_rmse'],
            len(results['optimal_temperature']['selected_features'])
        ]
    })
    
    results_df.to_csv(os.path.join(args.output_dir, 'regularized_rf_results.csv'), index=False)
    logger.info(f"Results saved to {os.path.join(args.output_dir, 'regularized_rf_results.csv')}")
    
    # Print summary
    logger.info("\nSummary of Results:")
    logger.info(f"Growth Rate - Test RMSE: {results['growth_rate']['test_rmse']:.4f}, Test R²: {results['growth_rate']['test_r2']:.4f}, OOB Score: {results['growth_rate']['oob_score']:.4f}")
    logger.info(f"Optimal Temperature - Test RMSE: {results['optimal_temperature']['test_rmse']:.4f}, Test R²: {results['optimal_temperature']['test_r2']:.4f}, OOB Score: {results['optimal_temperature']['oob_score']:.4f}")
    logger.info(f"Growth Rate - Selected {len(results['growth_rate']['selected_features'])} features")
    logger.info(f"Optimal Temperature - Selected {len(results['optimal_temperature']['selected_features'])} features")

if __name__ == '__main__':
    main()
