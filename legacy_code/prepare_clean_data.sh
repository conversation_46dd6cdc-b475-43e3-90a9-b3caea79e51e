#!/bin/bash

# Create output directory
OUTPUT_DIR="clean_data"
mkdir -p $OUTPUT_DIR

# Run data preparation script
python prepare_clean_data.py \
    --feature_file "./training_data/combined_features.tsv" \
    --metadata_file "./training_data/metadata.tsv" \
    --output_dir $OUTPUT_DIR \
    --use_interactions \
    --use_nonlinear \
    --use_pca \
    --use_domain \
    --n_pca_components 50 \
    --top_k_interactions 50

echo "Data preparation completed. Clean files are saved in $OUTPUT_DIR/" 
