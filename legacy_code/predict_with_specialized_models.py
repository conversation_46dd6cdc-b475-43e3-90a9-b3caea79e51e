#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Make predictions using the trained specialized hybrid models.

This script loads the trained models for growth rate and optimal temperature
and predicts values for new genomes.
"""

import os
import argparse
import logging
import numpy as np
import pandas as pd
import torch
import joblib
from pathlib import Path

from specialized_hybrid_models import GrowthRateModel, TemperatureModel, SpecializedHybridModel

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_models(model_dir, device):
    """Load the specialized models for growth rate and temperature prediction"""
    logger.info(f"Loading specialized models from {model_dir}...")
    
    # Paths to model directories
    growth_dir = os.path.join(model_dir, 'growth_rate')
    temp_dir = os.path.join(model_dir, 'optimal_temperature')
    stacking_dir = os.path.join(model_dir, 'stacking')
    
    # Check if stacking models exist
    has_stacking = os.path.exists(stacking_dir) and os.path.exists(os.path.join(stacking_dir, 'meta_model_growth.joblib'))
    
    # Load growth rate model components
    growth_components = joblib.load(os.path.join(growth_dir, 'model_components.joblib'))
    growth_feature_scaler = growth_components['feature_scaler']
    growth_target_scaler = growth_components['target_scaler']
    growth_rf_model = growth_components['rf_model']
    growth_ensemble_weight = growth_components['ensemble_weight']
    categorical_cols = growth_components['categorical_cols']
    growth_continuous_cols = growth_components['continuous_cols']
    growth_selected_features = growth_components['selected_features']
    
    # Load temperature model components
    temp_components = joblib.load(os.path.join(temp_dir, 'model_components.joblib'))
    temp_feature_scaler = temp_components['feature_scaler']
    temp_target_scaler = temp_components['target_scaler']
    temp_rf_model = temp_components['rf_model']
    temp_ensemble_weight = temp_components['ensemble_weight']
    temp_continuous_cols = temp_components['continuous_cols']
    temp_selected_features = temp_components['selected_features']
    
    # Load stacking models if available
    if has_stacking:
        logger.info("Loading stacking models...")
        meta_model_growth = joblib.load(os.path.join(stacking_dir, 'meta_model_growth.joblib'))
        meta_model_temp = joblib.load(os.path.join(stacking_dir, 'meta_model_temp.joblib'))
    else:
        meta_model_growth = None
        meta_model_temp = None
    
    # Load model state dictionaries to determine dimensions
    growth_state_dict = torch.load(os.path.join(growth_dir, 'nn_model.pth'), map_location=device)
    temp_state_dict = torch.load(os.path.join(temp_dir, 'nn_model.pth'), map_location=device)
    
    # Extract dimensions from state dictionaries
    growth_hidden_dim = growth_state_dict['layer1.weight'].size(0)
    temp_hidden_dim = temp_state_dict['layer1.weight'].size(0)
    
    # Determine the number of categories for categorical columns from state dict
    growth_cat_sizes = []
    for i in range(len(categorical_cols)):
        growth_cat_sizes.append(growth_state_dict[f'cat_embeddings.{i}.weight'].size(0))
        
    temp_cat_sizes = []
    for i in range(len(categorical_cols)):
        temp_cat_sizes.append(temp_state_dict[f'cat_embeddings.{i}.weight'].size(0))
    
    # Create neural network models with dimensions matching saved models
    growth_nn = GrowthRateModel(
        categories=growth_cat_sizes,  # Use extracted sizes
        num_continuous=len(growth_continuous_cols),
        dim=growth_hidden_dim,  # Use extracted dimension
        dropout=0.3,
        weight_decay=1e-5
    ).to(device)
    
    # Determine if temp model has attention from state dict keys
    has_attention = 'attention1.in_proj_weight' in temp_state_dict
    num_heads = 4  # Default
    
    temp_nn = TemperatureModel(
        categories=temp_cat_sizes,  # Use extracted sizes
        num_continuous=len(temp_continuous_cols),
        dim=temp_hidden_dim,  # Use extracted dimension
        dropout=0.3,
        weight_decay=1e-5,
        num_heads=num_heads
    ).to(device)
    
    # Load neural network weights
    growth_nn.load_state_dict(torch.load(os.path.join(growth_dir, 'nn_model.pth'), map_location=device))
    temp_nn.load_state_dict(torch.load(os.path.join(temp_dir, 'nn_model.pth'), map_location=device))
    
    # Create specialized hybrid models
    growth_model = SpecializedHybridModel(
        nn_model=growth_nn,
        categorical_cols=categorical_cols,
        continuous_cols=growth_continuous_cols,
        target_name="Growth Rate"
    )
    
    temp_model = SpecializedHybridModel(
        nn_model=temp_nn,
        categorical_cols=categorical_cols,
        continuous_cols=temp_continuous_cols,
        target_name="Optimal Temperature"
    )
    
    # Set RF models and ensemble weights
    growth_model.rf_model = growth_rf_model
    growth_model.ensemble_weight = growth_ensemble_weight
    
    temp_model.rf_model = temp_rf_model
    temp_model.ensemble_weight = temp_ensemble_weight
    
    return {
        'growth_model': growth_model,
        'temp_model': temp_model,
        'growth_feature_scaler': growth_feature_scaler,
        'temp_feature_scaler': temp_feature_scaler,
        'growth_target_scaler': growth_target_scaler,
        'temp_target_scaler': temp_target_scaler,
        'growth_selected_features': growth_selected_features,
        'temp_selected_features': temp_selected_features,
        'categorical_cols': categorical_cols,
        'meta_model_growth': meta_model_growth,
        'meta_model_temp': meta_model_temp,
        'has_stacking': has_stacking
    }

def prepare_input_data(features_df, metadata_df, models_info):
    """Prepare input data for prediction"""
    logger.info("Preparing input data for prediction...")
    
    # Get column information
    categorical_cols = models_info['categorical_cols']
    growth_selected_features = models_info['growth_selected_features']
    temp_selected_features = models_info['temp_selected_features']
    
    # Check if all required columns are present
    missing_growth_cols = [col for col in growth_selected_features if col not in features_df.columns]
    missing_temp_cols = [col for col in temp_selected_features if col not in features_df.columns]
    
    if missing_growth_cols or missing_temp_cols:
        logger.error(f"Missing columns in feature data: {set(missing_growth_cols + missing_temp_cols)}")
        raise ValueError("Input data is missing required columns")
    
    # Convert categorical values to match training data encoding
    for col in categorical_cols:
        if col == 'kingdom':
            # Map kingdoms to integers (this mapping should match training)
            kingdom_map = {'bac': 0, 'arc': 1, 'euk': 2}
            features_df[col] = features_df[col].map(kingdom_map)
        elif col == 'codon_table':
            # Convert codon_table to numeric
            features_df[col] = pd.to_numeric(features_df[col])
    
    # Prepare data for growth rate model
    X_growth = features_df.copy()
    growth_feature_scaler = models_info['growth_feature_scaler']
    growth_continuous_cols = [col for col in growth_selected_features if col not in categorical_cols]
    X_growth[growth_continuous_cols] = growth_feature_scaler.transform(X_growth[growth_continuous_cols])
    
    # Prepare data for temperature model
    X_temp = features_df.copy()
    temp_feature_scaler = models_info['temp_feature_scaler']
    temp_continuous_cols = [col for col in temp_selected_features if col not in categorical_cols]
    X_temp[temp_continuous_cols] = temp_feature_scaler.transform(X_temp[temp_continuous_cols])
    
    return X_growth, X_temp

def predict(features_df, metadata_df, models_info, device):
    """Make predictions using loaded models with stacking if available"""
    logger.info("Making predictions...")
    
    # Prepare input data
    X_growth, X_temp = prepare_input_data(features_df, metadata_df, models_info)
    
    # Get base model predictions
    growth_predictions = models_info['growth_model'].predict(
        X_growth, models_info['growth_target_scaler'], device
    )
    
    temp_predictions = models_info['temp_model'].predict(
        X_temp, models_info['temp_target_scaler'], device
    )
    
    # Apply stacking if available
    if models_info['has_stacking'] and models_info['meta_model_growth'] is not None:
        logger.info("Applying stacking models for improved predictions...")
        
        # Create meta-features for stacking
        meta_features_growth = np.column_stack([
            growth_predictions,  # Base model predictions
            features_df[models_info['categorical_cols'][0]].values,  # Kingdom
            features_df[models_info['categorical_cols'][1]].values   # Codon table
        ])
        
        meta_features_temp = np.column_stack([
            temp_predictions,  # Base model predictions
            features_df[models_info['categorical_cols'][0]].values,  # Kingdom
            features_df[models_info['categorical_cols'][1]].values   # Codon table
        ])
        
        # Apply meta-models
        growth_predictions = models_info['meta_model_growth'].predict(meta_features_growth)
        temp_predictions = models_info['meta_model_temp'].predict(meta_features_temp)
        
        logger.info("Stacking models applied successfully")
    
    # Create prediction DataFrame
    predictions_df = pd.DataFrame({
        'genome_id': features_df.index,
        'predicted_growth_rate': growth_predictions,
        'predicted_optimal_temperature': temp_predictions
    })
    
    # Add prediction method information
    predictions_df['growth_rate_method'] = 'stacked' if models_info['has_stacking'] else 'hybrid'
    predictions_df['temperature_method'] = 'stacked' if models_info['has_stacking'] else 'hybrid'
    
    # If metadata is provided, add actual values and calculate errors
    if metadata_df is not None and 'growth_rate' in metadata_df.columns and 'optimal_temperature' in metadata_df.columns:
        predictions_df['actual_growth_rate'] = metadata_df.loc[features_df.index, 'growth_rate'].values
        predictions_df['actual_optimal_temperature'] = metadata_df.loc[features_df.index, 'optimal_temperature'].values
        
        predictions_df['growth_rate_error'] = abs(
            predictions_df['predicted_growth_rate'] - predictions_df['actual_growth_rate']
        )
        predictions_df['optimal_temperature_error'] = abs(
            predictions_df['predicted_optimal_temperature'] - predictions_df['actual_optimal_temperature']
        )
    
    return predictions_df

def main():
    parser = argparse.ArgumentParser(description='Make predictions using specialized hybrid models')
    parser.add_argument('--feature_file', type=str, required=True,
                        help='Path to feature file for prediction (TSV)')
    parser.add_argument('--metadata_file', type=str, default=None,
                        help='Path to metadata file (optional, for evaluation)')
    parser.add_argument('--model_dir', type=str, default='./models/specialized_hybrid',
                        help='Directory containing specialized models')
    parser.add_argument('--output_file', type=str, default='predictions.tsv',
                        help='Path to output prediction file')
    parser.add_argument('--disable_stacking', action='store_true',
                       help='Disable stacking even if available')
    args = parser.parse_args()
    
    # Initialize device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"Using device: {device}")
    
    # Load models
    models_info = load_models(args.model_dir, device)
    
    # Disable stacking if requested
    if args.disable_stacking and models_info['has_stacking']:
        logger.info("Stacking models are available but disabled by user request")
        models_info['has_stacking'] = False
    
    # Load feature data
    logger.info(f"Loading feature data from {args.feature_file}")
    features_df = pd.read_csv(args.feature_file, sep='\t', index_col='genome_id')
    
    # Fill NaN values
    nan_count = features_df.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in features. Filling with 0.")
        features_df = features_df.fillna(0)
    
    # Check for infinite values
    inf_count = np.isinf(features_df.values).sum()
    if inf_count > 0:
        logger.warning(f"Found {inf_count} infinite values in features. Replacing with max values.")
        features_df = features_df.replace([np.inf, -np.inf], np.nan)
        features_df = features_df.fillna(features_df.max())
    
    # Load metadata if provided
    metadata_df = None
    if args.metadata_file:
        logger.info(f"Loading metadata from {args.metadata_file}")
        metadata_df = pd.read_csv(args.metadata_file, sep='\t', index_col='genome_id')
    
    # Make predictions
    predictions_df = predict(features_df, metadata_df, models_info, device)
    
    # Save predictions
    logger.info(f"Saving predictions to {args.output_file}")
    predictions_df.to_csv(args.output_file, sep='\t', index=False)
    
    # Print evaluation metrics if metadata is provided
    if metadata_df is not None and 'growth_rate' in metadata_df.columns and 'optimal_temperature' in metadata_df.columns:
        from sklearn.metrics import mean_squared_error, r2_score
        
        growth_rmse = np.sqrt(mean_squared_error(
            predictions_df['actual_growth_rate'], predictions_df['predicted_growth_rate']
        ))
        growth_r2 = r2_score(
            predictions_df['actual_growth_rate'], predictions_df['predicted_growth_rate']
        )
        
        temp_rmse = np.sqrt(mean_squared_error(
            predictions_df['actual_optimal_temperature'], predictions_df['predicted_optimal_temperature']
        ))
        temp_r2 = r2_score(
            predictions_df['actual_optimal_temperature'], predictions_df['predicted_optimal_temperature']
        )
        
        logger.info("\n======== EVALUATION METRICS ========")
        logger.info(f"Growth Rate - RMSE: {growth_rmse:.4f}, R²: {growth_r2:.4f}")
        logger.info(f"Optimal Temperature - RMSE: {temp_rmse:.4f}, R²: {temp_r2:.4f}")
        logger.info(f"Overall R²: {(growth_r2 + temp_r2) / 2:.4f}")
        
        # Save detailed metrics to file
        metrics_file = args.output_file.replace('.tsv', '_metrics.txt')
        with open(metrics_file, 'w') as f:
            f.write("Evaluation Metrics:\n")
            f.write(f"Growth Rate RMSE: {growth_rmse:.4f}\n")
            f.write(f"Growth Rate R²: {growth_r2:.4f}\n")
            f.write(f"Optimal Temperature RMSE: {temp_rmse:.4f}\n")
            f.write(f"Optimal Temperature R²: {temp_r2:.4f}\n")
            f.write(f"Overall R²: {(growth_r2 + temp_r2) / 2:.4f}\n\n")
            
            f.write("Prediction Method:\n")
            f.write(f"Growth Rate: {'Stacked' if models_info['has_stacking'] else 'Base Hybrid'}\n")
            f.write(f"Optimal Temperature: {'Stacked' if models_info['has_stacking'] else 'Base Hybrid'}\n")
            
        logger.info(f"Detailed metrics saved to {metrics_file}")
    
    logger.info(f"Prediction completed successfully using " + 
               ("stacked models" if models_info['has_stacking'] else "base hybrid models"))

if __name__ == '__main__':
    main() 