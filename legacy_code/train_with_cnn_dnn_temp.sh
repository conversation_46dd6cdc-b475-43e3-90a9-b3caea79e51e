#!/bin/bash

# Train the enhanced DeepMu hybrid model with improved CNN+DNN for temperature prediction
# Uses the same DNN for growth rate as the final model

echo "=========================================================="
echo "   Training Enhanced DeepMu hybrid model with CNN+DNN    "
echo "   - DNN for growth rate (log2 transform)                "
echo "   - Enhanced CNN+DNN for temperature (sqrt transform)   "
echo "   - Advanced features: residual connections, cyclical LR"
echo "   - Curriculum learning for extreme temperature values  "
echo "   - Weighted loss for better extreme value prediction   "
echo "=========================================================="

# Set output directory
OUTPUT_DIR="models/deepmu_enhanced_cnn_dnn_temp_model"

# Default feature selection method - can be changed to 'correlation' if combined method fails
FEATURE_SELECTION="correlation"

# Check if a command argument is provided
if [ "$1" == "--combined" ]; then
    FEATURE_SELECTION="combined"
    echo "Using 'combined' feature selection method"
else
    echo "Using 'correlation' feature selection method for stability"
fi

# Check for curriculum learning option
CURRICULUM_ARG="--curriculum"
CURRICULUM_PHASES=5

if [ "$2" == "--no-curriculum" ]; then
    CURRICULUM_ARG="--no-curriculum"
    echo "Disabling curriculum learning for temperature model"
elif [ "$2" == "--curriculum" ] && [ -n "$3" ]; then
    CURRICULUM_PHASES=$3
    echo "Using curriculum learning with $CURRICULUM_PHASES phases"
else
    echo "Using curriculum learning with default 5 phases"
fi

# Check for weighted loss option
WEIGHTED_LOSS_ARG="--weighted_loss"

if [ "$4" == "--no-weighted_loss" ]; then
    WEIGHTED_LOSS_ARG="--no-weighted_loss"
    echo "Disabling weighted loss for temperature model"
else
    echo "Using weighted loss for better extreme value prediction"
fi

# Run the model training script
python cnn_dnn_hybrid_model.py \
    --feature_file="./training_data/combined_features.tsv" \
    --metadata_file="./training_data/metadata.tsv" \
    --output_dir=$OUTPUT_DIR \
    --n_growth_features=250 \
    --n_temp_features=1000 \
    --feature_selection=$FEATURE_SELECTION \
    --growth_transform=log2 \
    --temp_transform=sqrt \
    --batch_size=128 \
    --epochs=300 \
    --patience=30 \
    --growth_hidden_dims=128,64,32 \
    --temp_hidden_dims=256,192,128,64,32 \
    --cnn_filters=128,96,64,32 \
    --kernel_sizes=3,5,7,9 \
    --dropout_rate=0.3 \
    --l2_reg=0.001 \
    $CURRICULUM_ARG \
    --curriculum_phases=$CURRICULUM_PHASES \
    $WEIGHTED_LOSS_ARG

echo -e "\n========================================================"
echo "   Training completed successfully!                       "
echo "=========================================================="
echo -e "\nModel saved to: $OUTPUT_DIR"
echo -e "\nThis enhanced model uses:"
echo -e "  - DNN for growth rate prediction (with log2 transformation)"
echo -e "  - Advanced CNN+DNN for temperature prediction (with sqrt transformation)"
echo -e "  - Residual connections for improved gradient flow"
echo -e "  - Feature selection method: $FEATURE_SELECTION"
echo -e "  - Cyclical learning rate for better convergence"
echo -e "  - Enhanced regularization techniques (spatial dropout, L2 regularization)"
if [ "$CURRICULUM_ARG" = "--curriculum" ]; then
    echo -e "  - Curriculum learning for temperature with $CURRICULUM_PHASES phases"
else
    echo -e "  - Standard training without curriculum learning"
fi
if [ "$WEIGHTED_LOSS_ARG" = "--weighted_loss" ]; then
    echo -e "  - Weighted loss for better extreme temperature prediction"
fi
echo -e "\nDone." 