#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Ensemble Temperature Model for DeepMu.

This module implements a combined ensemble approach using Random Forest and Neural Network
models for optimal temperature prediction.
"""

import os
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import TensorDataset, DataLoader
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import RobustScaler
from sklearn.ensemble import RandomForestRegressor
import joblib
import logging
import copy
import matplotlib.pyplot as plt
from typing import Dict, Tuple, Union, List
import time

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Use GPU if available
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

class TemperatureNN(nn.Module):
    """Neural network model for temperature prediction."""

    def __init__(self, input_dim: int, hidden_dims: List[int] = [512, 256, 128, 64]):
        super().__init__()

        # Feature normalization
        self.feature_norm = nn.LayerNorm(input_dim)

        # Main network with residual connections
        self.layers = nn.ModuleList()

        # Input layer
        self.layers.append(nn.Linear(input_dim, hidden_dims[0]))
        self.layers.append(nn.BatchNorm1d(hidden_dims[0]))
        self.layers.append(nn.ReLU())
        self.layers.append(nn.Dropout(0.3))

        # Hidden layers with residual connections
        for i in range(1, len(hidden_dims)):
            # Add linear layer
            self.layers.append(nn.Linear(hidden_dims[i-1], hidden_dims[i]))
            self.layers.append(nn.BatchNorm1d(hidden_dims[i]))
            self.layers.append(nn.ReLU())
            self.layers.append(nn.Dropout(0.3))

        # Output layer
        self.output_layer = nn.Linear(hidden_dims[-1], 1)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Forward pass through the model."""
        # Normalize input features
        x = self.feature_norm(x)

        # Process through main network with residual connections
        prev_block_output = None

        for i in range(0, len(self.layers), 4):
            if i + 3 < len(self.layers):  # Ensure we have a complete block
                # Store input to this block for residual connection
                block_input = x

                # Apply the block (linear + batch norm + activation + dropout)
                x = self.layers[i](x)       # Linear
                x = self.layers[i+1](x)     # BatchNorm
                x = self.layers[i+2](x)     # ReLU
                x = self.layers[i+3](x)     # Dropout

                # Add residual connection if dimensions match
                if prev_block_output is not None and prev_block_output.shape == x.shape:
                    x = x + prev_block_output

                # Store this block's output for the next residual connection
                prev_block_output = x

        # Final output layer
        return self.output_layer(x)

class EnsembleTemperatureTrainer:
    """Trainer class for ensemble temperature prediction model."""

    def __init__(self, output_dir: str = "models/ensemble_temp_model", ensemble_weights: Dict[str, float] = None):
        """Initialize the trainer.

        Args:
            output_dir: Directory to save model outputs
            ensemble_weights: Dictionary of weights for each model {'nn': 0.5, 'rf': 0.5}
                             If None, weights will be determined during training.
        """
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)

        # Create plots directory
        self.plots_dir = os.path.join(output_dir, "plots")
        os.makedirs(self.plots_dir, exist_ok=True)

        # Set up logging to file
        file_handler = logging.FileHandler(os.path.join(output_dir, "training.log"))
        file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
        logger.addHandler(file_handler)

        # Initialize model components
        self.nn_model = None
        self.rf_model = None
        self.scaler = None
        self.feature_indices = None

        # Initialize ensemble weights
        self.ensemble_weights = ensemble_weights if ensemble_weights else {'nn': 0.5, 'rf': 0.5}

        # Training history
        self.history = {
            "train_loss": [],
            "val_loss": [],
            "train_r2": [],
            "val_r2": [],
            "nn_train_r2": [],
            "nn_val_r2": [],
            "rf_train_r2": [],
            "rf_val_r2": [],
        }

    def prepare_data(self, features: pd.DataFrame, temperatures: pd.Series,
                    n_features: int = 800) -> Dict:
        """Prepare data for model training."""
        # Select top features by variance if needed
        if n_features and features.shape[1] > n_features:
            logger.info(f"Selecting top {n_features} features by variance")
            feature_variance = features.var().sort_values(ascending=False)
            selected_features = feature_variance.index[:n_features].tolist()
            self.feature_indices = selected_features
            features = features[selected_features]
        else:
            self.feature_indices = features.columns.tolist()

        # Split data
        X_train, X_val, y_train, y_val = train_test_split(
            features, temperatures, test_size=0.2, random_state=42
        )

        logger.info(f"Training set: {X_train.shape[0]} samples, Validation set: {X_val.shape[0]} samples")

        # Scale features for neural network only
        self.scaler = RobustScaler()
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_val_scaled = self.scaler.transform(X_val)

        # Create datasets for neural network
        train_dataset = TensorDataset(
            torch.FloatTensor(X_train_scaled).to(device),
            torch.FloatTensor(y_train.values.reshape(-1, 1)).to(device)
        )

        val_dataset = TensorDataset(
            torch.FloatTensor(X_val_scaled).to(device),
            torch.FloatTensor(y_val.values.reshape(-1, 1)).to(device)
        )

        return {
            "X_train": X_train,
            "X_val": X_val,
            "y_train": y_train,
            "y_val": y_val,
            "X_train_scaled": X_train_scaled,
            "X_val_scaled": X_val_scaled,
            "train_dataset": train_dataset,
            "val_dataset": val_dataset
        }

    def train_random_forest(self, X_train: pd.DataFrame, y_train: pd.Series,
                           X_val: pd.DataFrame, y_val: pd.Series,
                           n_estimators: int = 200, max_depth: int = 20) -> Dict:
        """Train the random forest model component."""
        logger.info(f"Training Random Forest with {n_estimators} trees, max_depth={max_depth}")
        start_time = time.time()

        # Initialize and train the model
        self.rf_model = RandomForestRegressor(
            n_estimators=n_estimators,
            max_depth=max_depth,
            min_samples_split=5,
            min_samples_leaf=2,
            n_jobs=-1,  # Use all cores
            random_state=42
        )

        self.rf_model.fit(X_train, y_train)

        # Evaluate the model
        train_preds = self.rf_model.predict(X_train)
        val_preds = self.rf_model.predict(X_val)

        train_r2 = r2_score(y_train, train_preds)
        val_r2 = r2_score(y_val, val_preds)

        train_rmse = np.sqrt(mean_squared_error(y_train, train_preds))
        val_rmse = np.sqrt(mean_squared_error(y_val, val_preds))

        # Log results
        logger.info(f"Random Forest trained in {time.time() - start_time:.2f} seconds")
        logger.info(f"RF Training R²: {train_r2:.6f}, RMSE: {train_rmse:.6f}")
        logger.info(f"RF Validation R²: {val_r2:.6f}, RMSE: {val_rmse:.6f}")

        # Feature importance analysis
        feature_importance = self.rf_model.feature_importances_
        feature_names = X_train.columns
        importance_df = pd.DataFrame({'Feature': feature_names, 'Importance': feature_importance})
        importance_df = importance_df.sort_values('Importance', ascending=False).head(20)

        # Plot feature importance
        plt.figure(figsize=(10, 8))
        plt.barh(importance_df['Feature'][::-1], importance_df['Importance'][::-1])
        plt.xlabel('Importance')
        plt.title('Top 20 Most Important Features (Random Forest)')
        plt.tight_layout()
        plt.savefig(os.path.join(self.plots_dir, 'rf_feature_importance.png'))
        plt.close()

        return {
            "train_r2": train_r2,
            "val_r2": val_r2,
            "train_preds": train_preds,
            "val_preds": val_preds
        }

    def train_neural_network(self, train_dataset, val_dataset, X_train_scaled, X_val_scaled,
                             y_train, y_val, batch_size: int = 64, epochs: int = 200,
                             patience: int = 30, lr: float = 0.001) -> Dict:
        """Train the neural network model component."""
        logger.info(f"Training Neural Network with {epochs} max epochs, patience={patience}")
        start_time = time.time()

        # Create data loaders
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size=batch_size*2)

        # Initialize model
        input_dim = X_train_scaled.shape[1]
        self.nn_model = TemperatureNN(input_dim=input_dim).to(device)

        # Create optimizer
        optimizer = optim.Adam(self.nn_model.parameters(), lr=lr, weight_decay=1e-6)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, mode='min', factor=0.5, patience=5
        )
        # Note: verbose parameter removed as it's not supported in this PyTorch version

        # Set up early stopping
        early_stopping_counter = 0
        best_val_loss = float('inf')
        best_model_state = None

        # Training loop
        for epoch in range(epochs):
            # Training phase
            self.nn_model.train()
            train_loss = 0.0

            for X_batch, y_batch in train_loader:
                optimizer.zero_grad()

                # Forward pass
                y_pred = self.nn_model(X_batch)
                loss = F.mse_loss(y_pred, y_batch)

                # Backward pass
                loss.backward()

                # Gradient clipping
                torch.nn.utils.clip_grad_norm_(self.nn_model.parameters(), max_norm=1.0)

                optimizer.step()
                train_loss += loss.item() * len(y_batch)

            train_loss /= len(train_loader.dataset)

            # Validation phase
            self.nn_model.eval()
            val_loss = 0.0
            val_preds = []
            val_targets = []

            with torch.no_grad():
                for X_batch, y_batch in val_loader:
                    # Forward pass
                    y_pred = self.nn_model(X_batch)
                    loss = F.mse_loss(y_pred, y_batch)

                    val_loss += loss.item() * len(y_batch)

                    # Store predictions and targets for metrics calculation
                    val_preds.extend(y_pred.cpu().numpy().flatten())
                    val_targets.extend(y_batch.cpu().numpy().flatten())

            val_loss /= len(val_loader.dataset)

            # Update scheduler
            scheduler.step(val_loss)

            # Store metrics in history
            self.history["train_loss"].append(train_loss)
            self.history["val_loss"].append(val_loss)

            # Log progress every 10 epochs
            if (epoch + 1) % 10 == 0:
                # Get training predictions for R² calculation
                train_preds = []
                train_targets = []

                self.nn_model.eval()
                with torch.no_grad():
                    for X_batch, y_batch in train_loader:
                        y_pred = self.nn_model(X_batch)
                        train_preds.extend(y_pred.cpu().numpy().flatten())
                        train_targets.extend(y_batch.cpu().numpy().flatten())

                train_r2 = r2_score(train_targets, train_preds)
                val_r2 = r2_score(val_targets, val_preds)

                self.history["train_r2"].append(train_r2)
                self.history["val_r2"].append(val_r2)

                logger.info(f"Epoch {epoch+1}/{epochs}")
                logger.info(f"  Train Loss: {train_loss:.6f}, R²: {train_r2:.6f}")
                logger.info(f"  Val Loss: {val_loss:.6f}, R²: {val_r2:.6f}")
                # Log learning rate changes manually since verbose=True is not supported
                logger.info(f"  Current LR: {optimizer.param_groups[0]['lr']:.6f}")

            # Check for improvement
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                best_model_state = copy.deepcopy(self.nn_model.state_dict())
                early_stopping_counter = 0
                logger.info(f"  Validation loss improved to {val_loss:.6f}")
            else:
                early_stopping_counter += 1

                if early_stopping_counter >= patience:
                    logger.info(f"Early stopping triggered after {epoch+1} epochs")
                    break

        # Load best model
        self.nn_model.load_state_dict(best_model_state)

        # Final predictions for the best model
        self.nn_model.eval()
        with torch.no_grad():
            train_preds = self.nn_model(torch.FloatTensor(X_train_scaled).to(device)).cpu().numpy().flatten()
            val_preds = self.nn_model(torch.FloatTensor(X_val_scaled).to(device)).cpu().numpy().flatten()

        # Calculate final metrics
        train_r2 = r2_score(y_train, train_preds)
        val_r2 = r2_score(y_val, val_preds)

        logger.info(f"Neural Network trained in {time.time() - start_time:.2f} seconds")
        logger.info(f"NN Training R²: {train_r2:.6f}")
        logger.info(f"NN Validation R²: {val_r2:.6f}")

        return {
            "train_r2": train_r2,
            "val_r2": val_r2,
            "train_preds": train_preds,
            "val_preds": val_preds
        }

    def optimize_ensemble_weights(self, nn_val_preds, rf_val_preds, y_val) -> Dict[str, float]:
        """Optimize the ensemble weights based on validation performance."""
        logger.info("Optimizing ensemble weights...")

        best_r2 = 0
        best_weights = {'nn': 0.5, 'rf': 0.5}

        # Grid search for weights
        for nn_weight in np.arange(0, 1.01, 0.05):
            rf_weight = 1 - nn_weight

            # Calculate ensemble predictions
            ensemble_preds = (nn_weight * nn_val_preds) + (rf_weight * rf_val_preds)

            # Calculate R²
            ensemble_r2 = r2_score(y_val, ensemble_preds)

            if ensemble_r2 > best_r2:
                best_r2 = ensemble_r2
                best_weights = {'nn': nn_weight, 'rf': rf_weight}

        logger.info(f"Optimal weights: NN = {best_weights['nn']:.2f}, RF = {best_weights['rf']:.2f}")
        logger.info(f"Ensemble validation R² with optimal weights: {best_r2:.6f}")

        self.ensemble_weights = best_weights
        return best_weights

    def train(self, features: pd.DataFrame, temperatures: pd.Series,
             batch_size: int = 64, nn_epochs: int = 200, patience: int = 30,
             rf_estimators: int = 200, rf_max_depth: int = 20, n_features: int = 800) -> None:
        """Train both models and create an ensemble."""
        start_time = time.time()

        logger.info(f"Starting ensemble model training with {features.shape[1]} features")
        logger.info(f"Using {n_features} top features selected by variance")

        # Prepare data
        data = self.prepare_data(features, temperatures, n_features)

        # Train Random Forest
        rf_results = self.train_random_forest(
            data["X_train"],
            data["y_train"],
            data["X_val"],
            data["y_val"],
            n_estimators=rf_estimators,
            max_depth=rf_max_depth
        )

        # Train Neural Network
        nn_results = self.train_neural_network(
            data["train_dataset"],
            data["val_dataset"],
            data["X_train_scaled"],
            data["X_val_scaled"],
            data["y_train"],
            data["y_val"],
            batch_size=batch_size,
            epochs=nn_epochs,
            patience=patience
        )

        # Store individual model metrics
        self.history["rf_train_r2"] = rf_results["train_r2"]
        self.history["rf_val_r2"] = rf_results["val_r2"]
        self.history["nn_train_r2"] = nn_results["train_r2"]
        self.history["nn_val_r2"] = nn_results["val_r2"]

        # Optimize ensemble weights
        self.optimize_ensemble_weights(
            nn_results["val_preds"],
            rf_results["val_preds"],
            data["y_val"]
        )

        # Calculate ensemble predictions
        train_ensemble_preds = (
            self.ensemble_weights['nn'] * nn_results["train_preds"] +
            self.ensemble_weights['rf'] * rf_results["train_preds"]
        )

        val_ensemble_preds = (
            self.ensemble_weights['nn'] * nn_results["val_preds"] +
            self.ensemble_weights['rf'] * rf_results["val_preds"]
        )

        # Calculate ensemble metrics
        ensemble_train_r2 = r2_score(data["y_train"], train_ensemble_preds)
        ensemble_val_r2 = r2_score(data["y_val"], val_ensemble_preds)

        ensemble_train_rmse = np.sqrt(mean_squared_error(data["y_train"], train_ensemble_preds))
        ensemble_val_rmse = np.sqrt(mean_squared_error(data["y_val"], val_ensemble_preds))

        logger.info("Final Ensemble Model Metrics:")
        logger.info(f"Training R²: {ensemble_train_r2:.6f}, RMSE: {ensemble_train_rmse:.6f}")
        logger.info(f"Validation R²: {ensemble_val_r2:.6f}, RMSE: {ensemble_val_rmse:.6f}")

        # Plot comparison
        self._plot_model_comparison(
            data["y_val"],
            nn_results["val_preds"],
            rf_results["val_preds"],
            val_ensemble_preds
        )

        # Plot predictions
        self._plot_predictions(
            data["y_train"],
            train_ensemble_preds,
            data["y_val"],
            val_ensemble_preds
        )

        # Save model and artifacts
        self.save()

        logger.info(f"Total training time: {time.time() - start_time:.2f} seconds")

    def _plot_model_comparison(self, y_true, nn_preds, rf_preds, ensemble_preds):
        """Create visualizations comparing the different models."""
        # Calculate metrics
        nn_r2 = r2_score(y_true, nn_preds)
        rf_r2 = r2_score(y_true, rf_preds)
        ensemble_r2 = r2_score(y_true, ensemble_preds)

        # Scatter plots for each model
        fig, axes = plt.subplots(1, 3, figsize=(18, 6))

        # Neural Network
        axes[0].scatter(y_true, nn_preds, alpha=0.5)
        axes[0].plot([0, 100], [0, 100], 'r--')
        axes[0].set_xlabel('True Temperature (°C)')
        axes[0].set_ylabel('Predicted Temperature (°C)')
        axes[0].set_title(f'Neural Network (R² = {nn_r2:.4f})')
        axes[0].set_xlim(0, 100)
        axes[0].set_ylim(0, 100)

        # Random Forest
        axes[1].scatter(y_true, rf_preds, alpha=0.5)
        axes[1].plot([0, 100], [0, 100], 'r--')
        axes[1].set_xlabel('True Temperature (°C)')
        axes[1].set_ylabel('Predicted Temperature (°C)')
        axes[1].set_title(f'Random Forest (R² = {rf_r2:.4f})')
        axes[1].set_xlim(0, 100)
        axes[1].set_ylim(0, 100)

        # Ensemble
        axes[2].scatter(y_true, ensemble_preds, alpha=0.5)
        axes[2].plot([0, 100], [0, 100], 'r--')
        axes[2].set_xlabel('True Temperature (°C)')
        axes[2].set_ylabel('Predicted Temperature (°C)')
        axes[2].set_title(f'Ensemble (R² = {ensemble_r2:.4f})')
        axes[2].set_xlim(0, 100)
        axes[2].set_ylim(0, 100)

        plt.tight_layout()
        plt.savefig(os.path.join(self.plots_dir, 'model_comparison.png'))
        plt.close()

        # Error distribution comparison
        plt.figure(figsize=(12, 6))

        nn_errors = y_true - nn_preds
        rf_errors = y_true - rf_preds
        ensemble_errors = y_true - ensemble_preds

        plt.hist(nn_errors, alpha=0.3, bins=30, label=f'Neural Network (MAE={np.mean(np.abs(nn_errors)):.2f})')
        plt.hist(rf_errors, alpha=0.3, bins=30, label=f'Random Forest (MAE={np.mean(np.abs(rf_errors)):.2f})')
        plt.hist(ensemble_errors, alpha=0.3, bins=30, label=f'Ensemble (MAE={np.mean(np.abs(ensemble_errors)):.2f})')

        plt.axvline(x=0, color='r', linestyle='--')
        plt.title('Error Distribution (True - Predicted)')
        plt.xlabel('Error (°C)')
        plt.ylabel('Frequency')
        plt.legend()
        plt.savefig(os.path.join(self.plots_dir, 'error_comparison.png'))
        plt.close()

        logger.info(f"Model comparison plots saved to {self.plots_dir}")

    def _plot_predictions(self, y_train, train_preds, y_val, val_preds):
        """Create visualizations of ensemble model performance."""
        # Scatter plots
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 7))

        # Training set
        train_r2 = r2_score(y_train, train_preds)
        train_rmse = np.sqrt(mean_squared_error(y_train, train_preds))

        ax1.scatter(y_train, train_preds, alpha=0.5, c='blue')
        ax1.plot([0, 100], [0, 100], 'r--')
        ax1.set_xlabel('True Temperature (°C)')
        ax1.set_ylabel('Predicted Temperature (°C)')
        ax1.set_title(f'Training Set (R² = {train_r2:.4f}, RMSE = {train_rmse:.2f}°C)')
        ax1.set_xlim(0, 100)
        ax1.set_ylim(0, 100)

        # Validation set
        val_r2 = r2_score(y_val, val_preds)
        val_rmse = np.sqrt(mean_squared_error(y_val, val_preds))

        ax2.scatter(y_val, val_preds, alpha=0.5, c='green')
        ax2.plot([0, 100], [0, 100], 'r--')
        ax2.set_xlabel('True Temperature (°C)')
        ax2.set_ylabel('Predicted Temperature (°C)')
        ax2.set_title(f'Validation Set (R² = {val_r2:.4f}, RMSE = {val_rmse:.2f}°C)')
        ax2.set_xlim(0, 100)
        ax2.set_ylim(0, 100)

        plt.tight_layout()
        plt.savefig(os.path.join(self.plots_dir, 'ensemble_predictions.png'))
        plt.close()

        # Error analysis
        plt.figure(figsize=(10, 6))
        val_errors = y_val - val_preds

        plt.scatter(y_val, val_errors, alpha=0.5, c='green')
        plt.axhline(y=0, color='r', linestyle='--')
        plt.title('Prediction Error vs. True Temperature')
        plt.xlabel('True Temperature (°C)')
        plt.ylabel('Error (°C)')
        plt.savefig(os.path.join(self.plots_dir, 'ensemble_error_by_temperature.png'))
        plt.close()

        # Error distribution
        plt.figure(figsize=(10, 6))

        plt.hist(val_errors, bins=30, alpha=0.7)
        plt.axvline(x=0, color='r', linestyle='--')
        plt.title('Error Distribution (True - Predicted)')
        plt.xlabel('Error (°C)')
        plt.ylabel('Frequency')
        plt.savefig(os.path.join(self.plots_dir, 'ensemble_error_distribution.png'))
        plt.close()

        logger.info(f"Ensemble prediction plots saved to {self.plots_dir}")

    def save(self) -> None:
        """Save models and artifacts."""
        # Save neural network model
        if self.nn_model:
            nn_dir = os.path.join(self.output_dir, "nn_model")
            os.makedirs(nn_dir, exist_ok=True)
            torch.save(self.nn_model.state_dict(), os.path.join(nn_dir, "model.pt"))

        # Save random forest model
        if self.rf_model:
            rf_dir = os.path.join(self.output_dir, "rf_model")
            os.makedirs(rf_dir, exist_ok=True)
            joblib.dump(self.rf_model, os.path.join(rf_dir, "model.pkl"))

        # Save scaler
        if self.scaler:
            joblib.dump(self.scaler, os.path.join(self.output_dir, "scaler.pkl"))

        # Save feature indices
        if self.feature_indices:
            joblib.dump(self.feature_indices, os.path.join(self.output_dir, "feature_indices.pkl"))

        # Save ensemble weights
        joblib.dump(self.ensemble_weights, os.path.join(self.output_dir, "ensemble_weights.pkl"))

        # Save model info
        model_info = {
            "input_dim": len(self.feature_indices) if self.feature_indices else None,
            "ensemble_weights": self.ensemble_weights,
            "history": self.history
        }
        joblib.dump(model_info, os.path.join(self.output_dir, "model_info.pkl"))

        logger.info(f"Models and artifacts saved to {self.output_dir}")

    def load(self, model_dir: str) -> None:
        """Load trained models."""
        # Load model info
        model_info = joblib.load(os.path.join(model_dir, "model_info.pkl"))

        # Load feature indices
        try:
            self.feature_indices = joblib.load(os.path.join(model_dir, "feature_indices.pkl"))
        except:
            self.feature_indices = None

        # Load ensemble weights
        try:
            self.ensemble_weights = joblib.load(os.path.join(model_dir, "ensemble_weights.pkl"))
        except:
            pass

        # Load scaler
        try:
            self.scaler = joblib.load(os.path.join(model_dir, "scaler.pkl"))
        except:
            pass

        # Load neural network model
        nn_model_path = os.path.join(model_dir, "nn_model", "model.pt")
        if os.path.exists(nn_model_path):
            input_dim = model_info["input_dim"]
            self.nn_model = TemperatureNN(input_dim=input_dim).to(device)
            self.nn_model.load_state_dict(torch.load(nn_model_path))

        # Load random forest model
        rf_model_path = os.path.join(model_dir, "rf_model", "model.pkl")
        if os.path.exists(rf_model_path):
            self.rf_model = joblib.load(rf_model_path)

        logger.info(f"Models loaded from {model_dir}")

    def predict(self, features: pd.DataFrame) -> np.ndarray:
        """Make predictions with the ensemble model."""
        if self.nn_model is None or self.rf_model is None:
            raise ValueError("Models not trained yet")

        # Apply feature selection if needed
        if self.feature_indices:
            features = features[self.feature_indices]

        # Get neural network predictions
        X_scaled = self.scaler.transform(features)
        X_tensor = torch.FloatTensor(X_scaled).to(device)

        self.nn_model.eval()
        with torch.no_grad():
            nn_preds = self.nn_model(X_tensor).cpu().numpy().flatten()

        # Get random forest predictions
        rf_preds = self.rf_model.predict(features)

        # Combine predictions using ensemble weights
        ensemble_preds = (
            self.ensemble_weights['nn'] * nn_preds +
            self.ensemble_weights['rf'] * rf_preds
        )

        return ensemble_preds

def main():
    """Main function to train the ensemble model."""
    # Parse command line arguments
    import argparse

    parser = argparse.ArgumentParser(description="Train ensemble temperature model")
    parser.add_argument("--features", type=str, default="training_data/combined_features.tsv",
                       help="Path to feature file (TSV format)")
    parser.add_argument("--metadata", type=str, default="training_data/metadata.tsv",
                       help="Path to metadata file (TSV format)")
    parser.add_argument("--output-dir", type=str, default="models/ensemble_temp_model",
                       help="Directory to save model outputs")
    parser.add_argument("--n-features", type=int, default=800,
                       help="Number of features to use")
    parser.add_argument("--batch-size", type=int, default=64,
                       help="Batch size for neural network training")
    parser.add_argument("--nn-epochs", type=int, default=200,
                       help="Maximum epochs for neural network training")
    parser.add_argument("--patience", type=int, default=30,
                       help="Early stopping patience")
    parser.add_argument("--rf-estimators", type=int, default=200,
                       help="Number of estimators for random forest")
    parser.add_argument("--rf-max-depth", type=int, default=20,
                       help="Maximum depth for random forest trees")

    args = parser.parse_args()

    # Load data
    logger.info(f"Loading features from {args.features}")
    features = pd.read_csv(args.features, sep='\t', index_col='genome_id')

    logger.info(f"Loading metadata from {args.metadata}")
    metadata = pd.read_csv(args.metadata, sep='\t', index_col='genome_id')

    # Get common samples
    common_indices = features.index.intersection(metadata.index)
    logger.info(f"Found {len(common_indices)} common samples")

    features = features.loc[common_indices]
    temperatures = metadata.loc[common_indices, 'optimal_temperature']

    # Handle missing values
    features = features.fillna(0)

    # Create trainer
    trainer = EnsembleTemperatureTrainer(output_dir=args.output_dir)

    # Train model
    trainer.train(
        features,
        temperatures,
        batch_size=args.batch_size,
        nn_epochs=args.nn_epochs,
        patience=args.patience,
        rf_estimators=args.rf_estimators,
        rf_max_depth=args.rf_max_depth,
        n_features=args.n_features
    )

    logger.info("Training complete!")

if __name__ == "__main__":
    import matplotlib
    matplotlib.use('Agg')  # Use non-interactive backend
    from matplotlib import pyplot as plt
    import seaborn as sns
    sns.set(style='whitegrid', font_scale=1.2)

    main()