#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Enhanced feature selection module for microbial growth rate prediction.
This module combines multiple feature selection methods to identify the most important features.
"""

import os
import numpy as np
import pandas as pd
import logging
from typing import List, Dict, Tuple, Set, Optional
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.feature_selection import SelectFromModel, RFE, VarianceThreshold, mutual_info_regression
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import cross_val_score
from sklearn.metrics import r2_score
import matplotlib.pyplot as plt
import seaborn as sns
import xgboost as xgb
import lightgbm as lgb
import shap

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def select_by_variance(X: pd.DataFrame, threshold: float = 0.01) -> List[str]:
    """
    Select features based on variance threshold.

    Args:
        X: DataFrame containing features
        threshold: Variance threshold for feature selection

    Returns:
        List of selected feature names
    """
    # Get numeric columns
    numeric_cols = X.select_dtypes(include=[np.number]).columns
    X_numeric = X[numeric_cols]

    # Apply variance threshold
    selector = VarianceThreshold(threshold=threshold)
    selector.fit(X_numeric)

    # Get selected feature indices
    selected_indices = selector.get_support(indices=True)
    selected_features = X_numeric.columns[selected_indices].tolist()

    logger.info(f"Selected {len(selected_features)} features by variance threshold")

    return selected_features

def select_by_random_forest(X: pd.DataFrame, y: pd.Series, n_features: int = 100) -> List[str]:
    """
    Select features based on random forest importance.

    Args:
        X: DataFrame containing features
        y: Target variable
        n_features: Number of features to select

    Returns:
        List of selected feature names
    """
    # Scale features
    scaler = StandardScaler()
    X_scaled = pd.DataFrame(
        scaler.fit_transform(X),
        columns=X.columns,
        index=X.index
    )

    # Train a Random Forest model
    rf = RandomForestRegressor(
        n_estimators=200,
        max_depth=20,
        min_samples_split=5,
        min_samples_leaf=2,
        max_features='sqrt',
        n_jobs=-1,
        random_state=42
    )
    rf.fit(X_scaled, y)

    # Get feature importance scores
    importance_scores = pd.DataFrame({
        'feature': X_scaled.columns,
        'importance': rf.feature_importances_
    })

    # Sort features by importance
    importance_scores = importance_scores.sort_values('importance', ascending=False)

    # Select top n_features
    selected_features = importance_scores.head(n_features)['feature'].tolist()

    logger.info(f"Selected {len(selected_features)} features by Random Forest importance")

    return selected_features

def select_by_xgboost(X: pd.DataFrame, y: pd.Series, n_features: int = 100) -> List[str]:
    """
    Select features based on XGBoost importance.

    Args:
        X: DataFrame containing features
        y: Target variable
        n_features: Number of features to select

    Returns:
        List of selected feature names
    """
    # Scale features
    scaler = StandardScaler()
    X_scaled = pd.DataFrame(
        scaler.fit_transform(X),
        columns=X.columns,
        index=X.index
    )

    # Train an XGBoost model
    xgb_model = xgb.XGBRegressor(
        n_estimators=200,
        learning_rate=0.05,
        max_depth=6,
        subsample=0.8,
        colsample_bytree=0.8,
        reg_alpha=0.1,
        reg_lambda=1.0,
        random_state=42
    )
    xgb_model.fit(X_scaled, y)

    # Get feature importance scores
    importance_scores = pd.DataFrame({
        'feature': X_scaled.columns,
        'importance': xgb_model.feature_importances_
    })

    # Sort features by importance
    importance_scores = importance_scores.sort_values('importance', ascending=False)

    # Select top n_features
    selected_features = importance_scores.head(n_features)['feature'].tolist()

    logger.info(f"Selected {len(selected_features)} features by XGBoost importance")

    return selected_features

def select_by_lightgbm(X: pd.DataFrame, y: pd.Series, n_features: int = 100) -> List[str]:
    """
    Select features based on LightGBM importance.

    Args:
        X: DataFrame containing features
        y: Target variable
        n_features: Number of features to select

    Returns:
        List of selected feature names
    """
    # Scale features
    scaler = StandardScaler()
    X_scaled = pd.DataFrame(
        scaler.fit_transform(X),
        columns=X.columns,
        index=X.index
    )

    # Train a LightGBM model
    lgb_model = lgb.LGBMRegressor(
        n_estimators=200,
        learning_rate=0.05,
        num_leaves=31,
        max_depth=6,
        subsample=0.8,
        colsample_bytree=0.8,
        reg_alpha=0.1,
        reg_lambda=1.0,
        random_state=42
    )
    lgb_model.fit(X_scaled, y)

    # Get feature importance scores
    importance_scores = pd.DataFrame({
        'feature': X_scaled.columns,
        'importance': lgb_model.feature_importances_
    })

    # Sort features by importance
    importance_scores = importance_scores.sort_values('importance', ascending=False)

    # Select top n_features
    selected_features = importance_scores.head(n_features)['feature'].tolist()

    logger.info(f"Selected {len(selected_features)} features by LightGBM importance")

    return selected_features

def select_by_mutual_info(X: pd.DataFrame, y: pd.Series, n_features: int = 100) -> List[str]:
    """
    Select features using mutual information.

    Args:
        X: DataFrame containing features
        y: Target variable
        n_features: Number of features to select

    Returns:
        List of selected feature names
    """
    # Check for NaN values
    if X.isna().any().any():
        logger.warning("Found NaN values in features for mutual information. Filling with 0.")
        X = X.fillna(0)

    # Check for infinite values
    if np.isinf(X.values).any():
        logger.warning("Found infinite values in features for mutual information. Replacing with max/min values.")
        X = X.replace([np.inf, -np.inf], np.nan)
        X = X.fillna(X.max())

    # Scale features
    scaler = StandardScaler()
    X_scaled = pd.DataFrame(
        scaler.fit_transform(X),
        columns=X.columns,
        index=X.index
    )

    # Calculate mutual information
    try:
        mi_scores = mutual_info_regression(X_scaled, y, random_state=42)
    except Exception as e:
        logger.warning(f"Error calculating mutual information: {e}")
        logger.warning("Falling back to correlation-based selection")
        # Use correlation as a fallback
        corr_scores = X_scaled.corrwith(y).abs()
        mi_df = pd.DataFrame({
            'feature': X_scaled.columns,
            'mi_score': corr_scores
        })
    else:
        # Create DataFrame with scores
        mi_df = pd.DataFrame({
            'feature': X_scaled.columns,
            'mi_score': mi_scores
        })

    # Sort by mutual information score
    mi_df = mi_df.sort_values('mi_score', ascending=False)

    # Select top n_features
    selected_features = mi_df.head(n_features)['feature'].tolist()

    logger.info(f"Selected {len(selected_features)} features by mutual information")

    return selected_features

def select_by_rfe(X: pd.DataFrame, y: pd.Series, n_features: int = 100, step: float = 0.3) -> List[str]:
    """
    Select features using Recursive Feature Elimination (RFE).

    Args:
        X: DataFrame containing features
        y: Target variable
        n_features: Number of features to select
        step: Step size for RFE (fraction of features to remove at each iteration)

    Returns:
        List of selected feature names
    """
    # Scale features
    scaler = StandardScaler()
    X_scaled = pd.DataFrame(
        scaler.fit_transform(X),
        columns=X.columns,
        index=X.index
    )

    # Calculate step size
    step_size = max(1, int(X_scaled.shape[1] * step))

    # Create RFE with Random Forest
    rf = RandomForestRegressor(
        n_estimators=100,
        max_depth=10,
        min_samples_split=5,
        min_samples_leaf=2,
        max_features='sqrt',
        n_jobs=-1,
        random_state=42
    )
    rfe = RFE(estimator=rf, n_features_to_select=n_features, step=step_size, verbose=0)

    # Fit RFE
    rfe.fit(X_scaled, y)

    # Get selected feature indices
    selected_indices = rfe.get_support(indices=True)
    selected_features = X_scaled.columns[selected_indices].tolist()

    logger.info(f"Selected {len(selected_features)} features by RFE")

    return selected_features

def select_by_shap(X: pd.DataFrame, y: pd.Series, n_features: int = 100) -> List[str]:
    """
    Select features based on SHAP values.

    Args:
        X: DataFrame containing features
        y: Target variable
        n_features: Number of features to select

    Returns:
        List of selected feature names
    """
    # Scale features
    scaler = StandardScaler()
    X_scaled = pd.DataFrame(
        scaler.fit_transform(X),
        columns=X.columns,
        index=X.index
    )

    # Train a Random Forest model
    rf = RandomForestRegressor(
        n_estimators=100,
        max_depth=10,
        min_samples_split=5,
        min_samples_leaf=2,
        max_features='sqrt',
        n_jobs=-1,
        random_state=42
    )
    rf.fit(X_scaled, y)

    # Calculate SHAP values
    explainer = shap.TreeExplainer(rf)
    shap_values = explainer.shap_values(X_scaled)

    # Get mean absolute SHAP values
    shap_importance = np.abs(shap_values).mean(axis=0)

    # Create DataFrame with scores
    shap_df = pd.DataFrame({
        'feature': X_scaled.columns,
        'importance': shap_importance
    })

    # Sort by importance
    shap_df = shap_df.sort_values('importance', ascending=False)

    # Select top n_features
    selected_features = shap_df.head(n_features)['feature'].tolist()

    logger.info(f"Selected {len(selected_features)} features by SHAP values")

    return selected_features

def select_by_gradient_boosting(X: pd.DataFrame, y: pd.Series, n_features: int = 100) -> List[str]:
    """
    Select features based on Gradient Boosting importance.

    Args:
        X: DataFrame containing features
        y: Target variable
        n_features: Number of features to select

    Returns:
        List of selected feature names
    """
    # Scale features
    scaler = StandardScaler()
    X_scaled = pd.DataFrame(
        scaler.fit_transform(X),
        columns=X.columns,
        index=X.index
    )

    # Train a Gradient Boosting model
    gb = GradientBoostingRegressor(
        n_estimators=200,
        learning_rate=0.05,
        max_depth=6,
        subsample=0.8,
        random_state=42
    )
    gb.fit(X_scaled, y)

    # Get feature importance scores
    importance_scores = pd.DataFrame({
        'feature': X_scaled.columns,
        'importance': gb.feature_importances_
    })

    # Sort features by importance
    importance_scores = importance_scores.sort_values('importance', ascending=False)

    # Select top n_features
    selected_features = importance_scores.head(n_features)['feature'].tolist()

    logger.info(f"Selected {len(selected_features)} features by Gradient Boosting importance")

    return selected_features

def evaluate_feature_set(X_train: pd.DataFrame, y_train: pd.Series,
                     X_val: pd.DataFrame, y_val: pd.Series,
                     features: List[str]) -> float:
    """
    Evaluate a feature set using a separate validation set to prevent data leakage.

    Args:
        X_train: Training features
        y_train: Training target
        X_val: Validation features
        y_val: Validation target
        features: List of feature names to evaluate

    Returns:
        Validation R² score
    """
    # Get selected features
    X_train_selected = X_train[features]
    X_val_selected = X_val[features]

    # Scale features using only training data
    scaler = StandardScaler()
    scaler.fit(X_train_selected)

    X_train_scaled = pd.DataFrame(
        scaler.transform(X_train_selected),
        columns=X_train_selected.columns,
        index=X_train_selected.index
    )

    X_val_scaled = pd.DataFrame(
        scaler.transform(X_val_selected),
        columns=X_val_selected.columns,
        index=X_val_selected.index
    )

    # Train on training data, evaluate on validation data
    rf = RandomForestRegressor(
        n_estimators=200,
        max_depth=10,  # Reduced to prevent overfitting
        min_samples_split=10,
        min_samples_leaf=5,
        max_features='sqrt',
        n_jobs=-1,
        random_state=42
    )

    # Train on training data
    rf.fit(X_train_scaled, y_train)

    # Evaluate on validation data
    val_r2 = r2_score(y_val, rf.predict(X_val_scaled))

    # Return validation R² score
    return val_r2

def combine_feature_selections(feature_sets: Dict[str, List[str]], n_features: int = 100) -> List[str]:
    """
    Combine multiple feature selections by voting.

    Args:
        feature_sets: Dictionary mapping method names to lists of selected features
        n_features: Number of features to select

    Returns:
        List of selected feature names
    """
    # Count votes for each feature
    feature_votes = {}
    for method, features in feature_sets.items():
        for i, feature in enumerate(features):
            if feature not in feature_votes:
                feature_votes[feature] = 0
            # Give more weight to features ranked higher
            feature_votes[feature] += len(features) - i

    # Sort features by votes
    sorted_features = sorted(feature_votes.items(), key=lambda x: x[1], reverse=True)

    # Select top n_features
    selected_features = [feature for feature, votes in sorted_features[:n_features]]

    logger.info(f"Selected {len(selected_features)} features by combining multiple methods")

    return selected_features

def select_features_ensemble(X_train: pd.DataFrame, y_train: pd.Series,
                           n_features: int = 100,
                           output_dir: Optional[str] = None,
                           X_val: Optional[pd.DataFrame] = None,
                           y_val: Optional[pd.Series] = None) -> List[str]:
    """
    Select features using an ensemble of methods with proper validation.

    Args:
        X_train: Training features
        y_train: Training target
        n_features: Number of features to select
        output_dir: Directory to save outputs
        X_val: Validation features (optional)
        y_val: Validation target (optional)

    Returns:
        List of selected feature names
    """
    logger.info(f"Selecting features using ensemble of methods...")

    # If validation data is not provided, create a validation split from training data
    if X_val is None or y_val is None:
        from sklearn.model_selection import train_test_split
        X_train_subset, X_val_subset, y_train_subset, y_val_subset = train_test_split(
            X_train, y_train, test_size=0.25, random_state=42
        )
        logger.info("Created internal validation split for feature selection")
    else:
        X_train_subset, X_val_subset = X_train, X_val
        y_train_subset, y_val_subset = y_train, y_val
        logger.info("Using provided validation data for feature selection")

    # Apply different feature selection methods using only training data
    feature_sets = {
        'Variance': select_by_variance(X_train_subset),
        'RandomForest': select_by_random_forest(X_train_subset, y_train_subset, n_features),
        'XGBoost': select_by_xgboost(X_train_subset, y_train_subset, n_features),
        'LightGBM': select_by_lightgbm(X_train_subset, y_train_subset, n_features),
        'MutualInfo': select_by_mutual_info(X_train_subset, y_train_subset, n_features),
        'RFE': select_by_rfe(X_train_subset, y_train_subset, n_features),
        'SHAP': select_by_shap(X_train_subset, y_train_subset, n_features),
        'GradientBoosting': select_by_gradient_boosting(X_train_subset, y_train_subset, n_features)
    }

    # Combine feature selections
    combined_features = combine_feature_selections(feature_sets, n_features)

    # Evaluate combined feature set on validation data
    combined_r2 = evaluate_feature_set(
        X_train_subset, y_train_subset,
        X_val_subset, y_val_subset,
        combined_features
    )
    logger.info(f"Combined feature set validation R²: {combined_r2:.4f}")

    # Evaluate individual feature sets on validation data
    results = {}
    for method, features in feature_sets.items():
        r2 = evaluate_feature_set(
            X_train_subset, y_train_subset,
            X_val_subset, y_val_subset,
            features
        )
        results[method] = r2
        logger.info(f"Method: {method}, validation R²: {r2:.4f}, Features: {len(features)}")

    # Find best method
    best_method = max(results, key=results.get)
    best_r2 = results[best_method]
    logger.info(f"Best method: {best_method}, validation R²: {best_r2:.4f}")

    # Choose best feature set
    if combined_r2 >= best_r2:
        logger.info(f"Using combined feature set (validation R²: {combined_r2:.4f})")
        selected_features = combined_features
    else:
        logger.info(f"Using {best_method} feature set (validation R²: {best_r2:.4f})")
        selected_features = feature_sets[best_method]

    # Save results if output_dir is provided
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)

        # Save selected features
        with open(os.path.join(output_dir, 'selected_features.txt'), 'w') as f:
            for feature in selected_features:
                f.write(f"{feature}\n")

        # Save evaluation results
        results_df = pd.DataFrame({
            'Method': list(results.keys()) + ['Combined'],
            'R2': list(results.values()) + [combined_r2]
        })
        results_df = results_df.sort_values('R2', ascending=False)
        results_df.to_csv(os.path.join(output_dir, 'feature_selection_results.csv'), index=False)

        # Create bar plot
        plt.figure(figsize=(12, 8))
        sns.barplot(x='R2', y='Method', data=results_df)
        plt.title('Feature Selection Method Comparison')
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'feature_selection_comparison.png'))

        # Save feature importance for best method
        if best_method == 'RandomForest':
            rf = RandomForestRegressor(
                n_estimators=200,
                max_depth=15,
                min_samples_split=5,
                min_samples_leaf=2,
                max_features='sqrt',
                n_jobs=-1,
                random_state=42
            )
            rf.fit(X_train[selected_features], y_train)
            importance_df = pd.DataFrame({
                'Feature': selected_features,
                'Importance': rf.feature_importances_
            }).sort_values('Importance', ascending=False)
            importance_df.to_csv(os.path.join(output_dir, 'feature_importance.csv'), index=False)

            # Create feature importance plot
            plt.figure(figsize=(12, 8))
            sns.barplot(x='Importance', y='Feature', data=importance_df.head(30))
            plt.title('Top 30 Feature Importance')
            plt.tight_layout()
            plt.savefig(os.path.join(output_dir, 'feature_importance.png'))

    return selected_features

if __name__ == "__main__":
    # This module is not meant to be run directly
    logger.info("This module provides enhanced feature selection functions.")
