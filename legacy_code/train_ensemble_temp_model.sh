#!/bin/bash

# DeepMu: Training Script for Ensemble Temperature Model
# This script trains an ensemble model that combines:
# 1. A DNN-based temperature prediction model
# 2. A Random Forest-based temperature prediction model

# Set a clean output directory
OUTPUT_DIR="models/deepmu_ensemble_temp_model"

# Create output directory
mkdir -p "$OUTPUT_DIR"

# Define color codes for output formatting
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Display initial message
echo -e "${BLUE}${BOLD}========================================================${NC}"
echo -e "${BLUE}${BOLD}   DeepMu - Ensemble Temperature Model   ${NC}"
echo -e "${BLUE}${BOLD}========================================================${NC}"
echo -e "${GREEN}This script trains an ensemble model that combines:${NC}"
echo -e "  1. ${YELLOW}A DNN-based temperature prediction model${NC}"
echo -e "  2. ${YELLOW}A Random Forest-based temperature prediction model${NC}"
echo -e ""
echo -e "${YELLOW}Output will be saved to:${NC} ${OUTPUT_DIR}"
echo ""

# Check if prerequisites are available
echo -e "${YELLOW}Checking prerequisites...${NC}"
if [ ! -f "./training_data/combined_features.tsv" ]; then
    echo -e "${RED}Error: Feature file not found. Please ensure './training_data/combined_features.tsv' exists.${NC}"
    exit 1
fi

if [ ! -f "./training_data/metadata.tsv" ]; then
    echo -e "${RED}Error: Metadata file not found. Please ensure './training_data/metadata.tsv' exists.${NC}"
    exit 1
fi

echo -e "${GREEN}All prerequisites found.${NC}"
echo ""

# Step 1: Filter the dataset to remove unreliable temperature data
echo -e "${YELLOW}Step 1: Filtering dataset to remove unreliable temperature data...${NC}"
python improved_temp_data_filter.py \
    --feature-file "./training_data/combined_features.tsv" \
    --metadata-file "./training_data/metadata.tsv" \
    --output-dir "$OUTPUT_DIR/filtered_data" \
    --reliability-threshold 0.6 \
    --prediction-error-threshold 2.0

# Check if filtering was successful
if [ $? -eq 0 ]; then
    echo -e "${GREEN}Dataset filtering completed. Using filtered datasets for training.${NC}"
    FEATURES_FILE="${OUTPUT_DIR}/filtered_data/filtered_features.tsv"
    METADATA_FILE="${OUTPUT_DIR}/filtered_data/filtered_metadata.tsv"
else
    echo -e "${RED}Error: Dataset filtering failed. Exiting.${NC}"
    exit 1
fi

# Step 2: Train the ensemble temperature model
echo -e "${YELLOW}Step 2: Training ensemble temperature model...${NC}"

# Set parameters for ensemble model
N_TEMP_FEATURES=250  # Number of features for temperature prediction
N_GROWTH_FEATURES=250  # Number of features for growth rate prediction
BATCH_SIZE=64    # Batch size for neural network training
NN_EPOCHS=300    # Maximum epochs for neural network training
PATIENCE=40      # Early stopping patience
RF_ESTIMATORS=300  # Number of estimators for random forest
RF_MAX_DEPTH=20    # Maximum depth for random forest trees

# Run the ensemble temperature model training script
python create_ensemble_models.py \
    --feature_file "$FEATURES_FILE" \
    --metadata_file "$METADATA_FILE" \
    --output_dir "$OUTPUT_DIR" \
    --n_temp_features $N_TEMP_FEATURES \
    --n_growth_features $N_GROWTH_FEATURES

# Check if training was successful
if [ $? -eq 0 ]; then
    echo ""
    echo -e "${GREEN}${BOLD}========================================================${NC}"
    echo -e "${GREEN}${BOLD}   Training completed successfully!   ${NC}"
    echo -e "${GREEN}${BOLD}========================================================${NC}"
    echo ""
    echo -e "${GREEN}Ensemble model saved to:${NC} ${OUTPUT_DIR}"
    
else
    echo ""
    echo -e "${RED}${BOLD}========================================================${NC}"
    echo -e "${RED}${BOLD}   Training failed!   ${NC}"
    echo -e "${RED}${BOLD}========================================================${NC}"
    echo ""
    echo -e "${RED}Please check the error messages above for details.${NC}"
fi

# Step 3: Create a hybrid model that uses:
# - The ensemble model for temperature prediction
# - The DNN model for growth rate prediction
echo -e "${YELLOW}Step 3: Creating hybrid model with ensemble temperature prediction...${NC}"

# Set parameters for hybrid model
N_GROWTH_FEATURES=150  # Number of features for growth rate prediction
GROWTH_TRANSFORM="log2"  # Transformation for growth rate
BATCH_SIZE=64  # Batch size for neural network training
EPOCHS=300  # Maximum epochs for neural network training
PATIENCE=40  # Early stopping patience
HIDDEN_DIMS="640,512,384,256,192"  # Hidden layer dimensions for growth rate DNN
DROPOUT_RATES="0.2,0.25,0.3,0.35,0.4"  # Dropout rates for growth rate DNN
LEARNING_RATE=0.0008  # Learning rate for growth rate DNN
WEIGHT_DECAY=2e-5  # Weight decay for growth rate DNN

# Create a hybrid model script
cat > "$OUTPUT_DIR/hybrid_model.py" << 'EOF'
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Hybrid Model with Ensemble Temperature Prediction.

This module implements a hybrid model that uses:
1. The ensemble model for temperature prediction
2. A DNN model for growth rate prediction
"""

import os
import argparse
import logging
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from typing import Dict, List, Tuple, Union, Optional

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import the ensemble temperature model
from ensemble_temp_model import EnsembleTemperatureTrainer

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Hybrid Model with Ensemble Temperature Prediction')
    parser.add_argument('--features', type=str, required=True, help='Path to feature file')
    parser.add_argument('--metadata', type=str, required=True, help='Path to metadata file')
    parser.add_argument('--output-dir', type=str, required=True, help='Directory to save the model')
    parser.add_argument('--ensemble-model-dir', type=str, required=True, help='Directory containing the ensemble temperature model')
    parser.add_argument('--n-growth-features', type=int, default=150, help='Number of features for growth rate prediction')
    parser.add_argument('--growth-transform', type=str, default='log2', help='Transformation for growth rate')
    parser.add_argument('--batch-size', type=int, default=64, help='Batch size for neural network training')
    parser.add_argument('--epochs', type=int, default=300, help='Maximum epochs for neural network training')
    parser.add_argument('--patience', type=int, default=40, help='Early stopping patience')
    parser.add_argument('--hidden-dims', type=str, default='640,512,384,256,192', help='Hidden layer dimensions for growth rate DNN')
    parser.add_argument('--dropout-rates', type=str, default='0.2,0.25,0.3,0.35,0.4', help='Dropout rates for growth rate DNN')
    parser.add_argument('--learning-rate', type=float, default=0.0008, help='Learning rate for growth rate DNN')
    parser.add_argument('--weight-decay', type=float, default=2e-5, help='Weight decay for growth rate DNN')
    
    args = parser.parse_args()
    
    # Load data
    logger.info(f"Loading features from {args.features}")
    features = pd.read_csv(args.features, sep='\t', index_col='genome_id')
    
    logger.info(f"Loading metadata from {args.metadata}")
    metadata = pd.read_csv(args.metadata, sep='\t', index_col='genome_id')
    
    # Get common samples
    common_indices = features.index.intersection(metadata.index)
    logger.info(f"Found {len(common_indices)} common samples")
    
    features = features.loc[common_indices]
    metadata = metadata.loc[common_indices]
    
    # Handle missing values
    features = features.fillna(0)
    
    # Load ensemble temperature model
    logger.info(f"Loading ensemble temperature model from {args.ensemble_model_dir}")
    ensemble_model = EnsembleTemperatureTrainer(output_dir=args.ensemble_model_dir)
    ensemble_model.load(args.ensemble_model_dir)
    
    # Make temperature predictions
    logger.info("Making temperature predictions with ensemble model")
    temp_preds = ensemble_model.predict(features)
    
    # TODO: Implement growth rate prediction with DNN
    
    logger.info("Hybrid model implementation complete!")

if __name__ == "__main__":
    main()
EOF

chmod +x "$OUTPUT_DIR/hybrid_model.py"

echo ""
echo -e "${GREEN}Done.${NC}"
