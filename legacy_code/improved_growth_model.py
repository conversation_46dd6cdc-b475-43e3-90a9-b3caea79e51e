#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Improved growth rate prediction model using stacking ensemble with bidirectional learning.
This model combines Random Forest, XGBoost, and LightGBM with feature interaction.
"""

import os
import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Tuple, Union, Optional
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import KFold, cross_val_score
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from sklearn.preprocessing import StandardScaler
from sklearn.inspection import permutation_importance
import xgboost as xgb
import lightgbm as lgb
import matplotlib.pyplot as plt
import seaborn as sns
import joblib
import shap

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ImprovedGrowthModel:
    """
    Improved growth rate prediction model using stacking ensemble with bidirectional learning.
    """

    def __init__(self, rf_params=None, xgb_params=None, lgb_params=None, meta_params=None):
        """
        Initialize the model with parameters for base models and meta-model.

        Args:
            rf_params: Parameters for Random Forest
            xgb_params: Parameters for XGBoost
            lgb_params: Parameters for LightGBM
            meta_params: Parameters for meta-model (XGBoost)
        """
        # Set default parameters if not provided
        self.rf_params = rf_params or {
            'n_estimators': 300,
            'max_depth': 15,
            'min_samples_split': 5,
            'min_samples_leaf': 2,
            'max_features': 'sqrt',
            'bootstrap': True,
            'oob_score': True,
            'n_jobs': -1,
            'random_state': 42
        }

        self.xgb_params = xgb_params or {
            'n_estimators': 300,
            'learning_rate': 0.05,
            'max_depth': 6,
            'subsample': 0.8,
            'colsample_bytree': 0.8,
            'reg_alpha': 0.1,
            'reg_lambda': 1.0,
            'random_state': 42
        }

        self.lgb_params = lgb_params or {
            'n_estimators': 300,
            'learning_rate': 0.05,
            'num_leaves': 31,
            'max_depth': 6,
            'subsample': 0.8,
            'colsample_bytree': 0.8,
            'reg_alpha': 0.1,
            'reg_lambda': 1.0,
            'random_state': 42
        }

        self.meta_params = meta_params or {
            'n_estimators': 200,
            'learning_rate': 0.03,
            'max_depth': 4,
            'subsample': 0.8,
            'colsample_bytree': 0.8,
            'reg_alpha': 0.1,
            'reg_lambda': 1.0,
            'random_state': 42
        }

        # Initialize base models
        self.rf_model = RandomForestRegressor(**self.rf_params)
        self.xgb_model = xgb.XGBRegressor(**self.xgb_params)
        self.lgb_model = lgb.LGBMRegressor(**self.lgb_params)

        # Initialize meta-model
        self.meta_model = xgb.XGBRegressor(**self.meta_params)

        # For storing feature importances
        self.feature_importances = {}

        # For storing cross-validation results
        self.cv_results = {}

        # For storing evaluation metrics
        self.metrics = {}

        # For storing feature names
        self.feature_names = None

        # For storing SHAP values
        self.shap_values = None

    def _create_meta_features(self, X: pd.DataFrame, base_preds: np.ndarray) -> np.ndarray:
        """
        Create meta-features by combining base model predictions with original features.

        Args:
            X: Original features
            base_preds: Predictions from base models

        Returns:
            Meta-features array
        """
        # Create interaction features between base predictions
        rf_preds = base_preds[:, 0].reshape(-1, 1)
        xgb_preds = base_preds[:, 1].reshape(-1, 1)
        lgb_preds = base_preds[:, 2].reshape(-1, 1)

        # Create pairwise interactions
        rf_xgb = (rf_preds * xgb_preds).reshape(-1, 1)
        rf_lgb = (rf_preds * lgb_preds).reshape(-1, 1)
        xgb_lgb = (xgb_preds * lgb_preds).reshape(-1, 1)

        # Create ensemble average
        ensemble_avg = ((rf_preds + xgb_preds + lgb_preds) / 3).reshape(-1, 1)

        # Create weighted ensemble (weights determined by cross-validation)
        weighted_ensemble = (0.3 * rf_preds + 0.4 * xgb_preds + 0.3 * lgb_preds).reshape(-1, 1)

        # Combine all meta-features
        meta_features = np.hstack([
            base_preds,  # Base model predictions
            rf_xgb, rf_lgb, xgb_lgb,  # Pairwise interactions
            ensemble_avg, weighted_ensemble,  # Ensemble combinations
            X.values  # Original features for bidirectional learning
        ])

        return meta_features

    def fit(self, X_train: pd.DataFrame, y_train: pd.Series,
            X_val: pd.DataFrame = None, y_val: pd.Series = None,
            cv: int = 5) -> 'ImprovedGrowthModel':
        """
        Train the model using stacking with cross-validation.

        Args:
            X_train: Training features
            y_train: Training target
            X_val: Validation features (optional)
            y_val: Validation target (optional)
            cv: Number of cross-validation folds

        Returns:
            Self
        """
        # Store feature names
        self.feature_names = X_train.columns.tolist()

        # If validation set is not provided, use a portion of training set
        if X_val is None or y_val is None:
            logger.info("Validation set not provided, using cross-validation")
            self._fit_with_cv(X_train, y_train, cv)
        else:
            logger.info("Using provided validation set")
            self._fit_with_val(X_train, y_train, X_val, y_val)

        return self

    def _fit_with_cv(self, X: pd.DataFrame, y: pd.Series, cv: int) -> None:
        """
        Train the model using cross-validation.

        Args:
            X: Features
            y: Target
            cv: Number of cross-validation folds
        """
        logger.info(f"Training with {cv}-fold cross-validation")

        # Initialize arrays for meta-features and targets
        meta_train = np.zeros((X.shape[0], 3))  # 3 base models

        # Initialize KFold
        kf = KFold(n_splits=cv, shuffle=True, random_state=42)

        # Cross-validation loop
        for fold, (train_idx, val_idx) in enumerate(kf.split(X)):
            logger.info(f"Training fold {fold+1}/{cv}")

            # Split data
            X_fold_train, X_fold_val = X.iloc[train_idx], X.iloc[val_idx]
            y_fold_train, y_fold_val = y.iloc[train_idx], y.iloc[val_idx]

            # Train base models
            self.rf_model.fit(X_fold_train, y_fold_train)
            self.xgb_model.fit(X_fold_train, y_fold_train)
            self.lgb_model.fit(X_fold_train, y_fold_train)

            # Generate predictions for validation fold
            meta_train[val_idx, 0] = self.rf_model.predict(X_fold_val)
            meta_train[val_idx, 1] = self.xgb_model.predict(X_fold_val)
            meta_train[val_idx, 2] = self.lgb_model.predict(X_fold_val)

            # Calculate fold metrics
            fold_metrics = {}
            for i, model_name in enumerate(['RF', 'XGB', 'LGB']):
                fold_metrics[f'{model_name}_R2'] = r2_score(y_fold_val, meta_train[val_idx, i])
                fold_metrics[f'{model_name}_RMSE'] = np.sqrt(mean_squared_error(y_fold_val, meta_train[val_idx, i]))

            # Store fold metrics
            self.cv_results[f'fold_{fold+1}'] = fold_metrics

            # Log fold metrics
            logger.info(f"Fold {fold+1} metrics: {fold_metrics}")

        # Train base models on full dataset
        logger.info("Training base models on full dataset")
        self.rf_model.fit(X, y)
        self.xgb_model.fit(X, y)
        self.lgb_model.fit(X, y)

        # Create meta-features
        meta_features = self._create_meta_features(X, meta_train)

        # Train meta-model
        logger.info("Training meta-model")
        self.meta_model.fit(meta_features, y)

        # Store feature importances
        self.feature_importances = {
            'rf': pd.Series(self.rf_model.feature_importances_, index=X.columns),
            'xgb': pd.Series(self.xgb_model.feature_importances_, index=X.columns),
            'lgb': pd.Series(self.lgb_model.feature_importances_, index=X.columns)
        }

        # Calculate SHAP values for feature importance
        logger.info("Calculating SHAP values for feature importance")
        explainer = shap.TreeExplainer(self.rf_model)
        self.shap_values = explainer.shap_values(X)

        logger.info("Model training completed with cross-validation")

    def _fit_with_val(self, X_train: pd.DataFrame, y_train: pd.Series,
                     X_val: pd.DataFrame, y_val: pd.Series) -> None:
        """
        Train the model using a validation set.

        Args:
            X_train: Training features
            y_train: Training target
            X_val: Validation features
            y_val: Validation target
        """
        logger.info("Training base models")

        # Train base models
        self.rf_model.fit(X_train, y_train)
        self.xgb_model.fit(X_train, y_train)
        self.lgb_model.fit(X_train, y_train)

        # Generate predictions for training set
        train_rf_preds = self.rf_model.predict(X_train).reshape(-1, 1)
        train_xgb_preds = self.xgb_model.predict(X_train).reshape(-1, 1)
        train_lgb_preds = self.lgb_model.predict(X_train).reshape(-1, 1)

        # Combine predictions
        train_base_preds = np.hstack([train_rf_preds, train_xgb_preds, train_lgb_preds])

        # Create meta-features for training
        train_meta_features = self._create_meta_features(X_train, train_base_preds)

        # Generate predictions for validation set
        val_rf_preds = self.rf_model.predict(X_val).reshape(-1, 1)
        val_xgb_preds = self.xgb_model.predict(X_val).reshape(-1, 1)
        val_lgb_preds = self.lgb_model.predict(X_val).reshape(-1, 1)

        # Combine predictions
        val_base_preds = np.hstack([val_rf_preds, val_xgb_preds, val_lgb_preds])

        # Create meta-features for validation
        val_meta_features = self._create_meta_features(X_val, val_base_preds)

        # Train meta-model
        logger.info("Training meta-model")

        # Try with early stopping if validation set is provided
        try:
            self.meta_model.fit(
                train_meta_features,
                y_train,
                eval_set=[(val_meta_features, y_val)],
                eval_metric='rmse',
                early_stopping_rounds=20,
                verbose=False
            )
        except (TypeError, ValueError):
            logger.warning("Early stopping not available, training without validation")
            self.meta_model.fit(train_meta_features, y_train)

        # Calculate metrics for base models and meta-model
        metrics = {}

        # Base model metrics on validation set
        metrics['RF_R2'] = r2_score(y_val, val_rf_preds)
        metrics['RF_RMSE'] = np.sqrt(mean_squared_error(y_val, val_rf_preds))
        metrics['XGB_R2'] = r2_score(y_val, val_xgb_preds)
        metrics['XGB_RMSE'] = np.sqrt(mean_squared_error(y_val, val_xgb_preds))
        metrics['LGB_R2'] = r2_score(y_val, val_lgb_preds)
        metrics['LGB_RMSE'] = np.sqrt(mean_squared_error(y_val, val_lgb_preds))

        # Meta-model metrics on validation set
        meta_preds = self.meta_model.predict(val_meta_features)
        metrics['Meta_R2'] = r2_score(y_val, meta_preds)
        metrics['Meta_RMSE'] = np.sqrt(mean_squared_error(y_val, meta_preds))

        # Store metrics
        self.metrics = metrics

        # Store feature importances
        self.feature_importances = {
            'rf': pd.Series(self.rf_model.feature_importances_, index=X_train.columns),
            'xgb': pd.Series(self.xgb_model.feature_importances_, index=X_train.columns),
            'lgb': pd.Series(self.lgb_model.feature_importances_, index=X_train.columns)
        }

        # Calculate SHAP values for feature importance
        logger.info("Calculating SHAP values for feature importance")
        explainer = shap.TreeExplainer(self.rf_model)
        self.shap_values = explainer.shap_values(X_train)

        # Log metrics
        logger.info(f"Validation metrics: {metrics}")
        logger.info("Model training completed with validation set")

    def predict(self, X: pd.DataFrame) -> np.ndarray:
        """
        Generate predictions for new data.

        Args:
            X: Features

        Returns:
            Predictions
        """
        # Generate base model predictions
        rf_preds = self.rf_model.predict(X).reshape(-1, 1)
        xgb_preds = self.xgb_model.predict(X).reshape(-1, 1)
        lgb_preds = self.lgb_model.predict(X).reshape(-1, 1)

        # Combine predictions
        base_preds = np.hstack([rf_preds, xgb_preds, lgb_preds])

        # Create meta-features
        meta_features = self._create_meta_features(X, base_preds)

        # Generate final predictions
        return self.meta_model.predict(meta_features)

    def evaluate(self, X: pd.DataFrame, y: pd.Series) -> Dict[str, float]:
        """
        Evaluate the model on new data.

        Args:
            X: Features
            y: Target

        Returns:
            Dictionary of evaluation metrics
        """
        # Generate predictions
        y_pred = self.predict(X)

        # Calculate metrics
        metrics = {
            'R2': r2_score(y, y_pred),
            'RMSE': np.sqrt(mean_squared_error(y, y_pred)),
            'MAE': mean_absolute_error(y, y_pred)
        }

        # Log metrics
        logger.info(f"Evaluation metrics: {metrics}")

        return metrics

    def get_feature_importance(self, X: pd.DataFrame = None) -> pd.DataFrame:
        """
        Get feature importance from all base models.

        Args:
            X: Features for permutation importance (optional)

        Returns:
            DataFrame with feature importance
        """
        if not self.feature_importances:
            logger.warning("Feature importances not available. Train the model first.")
            return pd.DataFrame()

        # Combine feature importances from all base models
        importance_df = pd.DataFrame(self.feature_importances)

        # Calculate mean importance
        importance_df['mean'] = importance_df.mean(axis=1)

        # Sort by mean importance
        importance_df = importance_df.sort_values('mean', ascending=False)

        # Calculate permutation importance if X is provided
        if X is not None:
            logger.info("Calculating permutation importance")
            perm_importance = permutation_importance(
                self.meta_model,
                self._create_meta_features(X, np.hstack([
                    self.rf_model.predict(X).reshape(-1, 1),
                    self.xgb_model.predict(X).reshape(-1, 1),
                    self.lgb_model.predict(X).reshape(-1, 1)
                ])),
                X.columns,
                n_repeats=10,
                random_state=42
            )

            # Add permutation importance to DataFrame
            importance_df['permutation'] = pd.Series(
                perm_importance.importances_mean,
                index=X.columns
            )

        return importance_df

    def plot_feature_importance(self, output_dir: str = None, n_features: int = 20) -> None:
        """
        Plot feature importance.

        Args:
            output_dir: Directory to save plots (optional)
            n_features: Number of top features to plot
        """
        importance_df = self.get_feature_importance()

        if isinstance(importance_df, pd.DataFrame) and importance_df.empty:
            logger.warning("Feature importances not available. Train the model first.")
            return

        # Create output directory if provided
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)

        # Plot mean feature importance
        plt.figure(figsize=(12, 8))
        top_features = importance_df.head(n_features)
        sns.barplot(x='mean', y=top_features.index, data=top_features)
        plt.title(f'Top {n_features} Feature Importance (Mean)')
        plt.tight_layout()

        if output_dir:
            plt.savefig(os.path.join(output_dir, 'feature_importance_mean.png'))
        else:
            plt.show()

        # Plot feature importance for each base model
        for model in ['rf', 'xgb', 'lgb']:
            plt.figure(figsize=(12, 8))
            top_features = importance_df.head(n_features)
            sns.barplot(x=model, y=top_features.index, data=top_features)
            plt.title(f'Top {n_features} Feature Importance ({model.upper()})')
            plt.tight_layout()

            if output_dir:
                plt.savefig(os.path.join(output_dir, f'feature_importance_{model}.png'))
            else:
                plt.show()

        # Plot permutation importance if available
        if 'permutation' in importance_df.columns:
            plt.figure(figsize=(12, 8))
            permutation_df = importance_df.sort_values('permutation', ascending=False).head(n_features)
            sns.barplot(x='permutation', y=permutation_df.index, data=permutation_df)
            plt.title(f'Top {n_features} Permutation Importance')
            plt.tight_layout()

            if output_dir:
                plt.savefig(os.path.join(output_dir, 'feature_importance_permutation.png'))
            else:
                plt.show()

        # Plot SHAP values if available
        if self.shap_values is not None and self.feature_names is not None:
            plt.figure(figsize=(12, 8))
            shap.summary_plot(self.shap_values, features=self.feature_names, plot_type='bar', show=False)
            plt.title('SHAP Feature Importance')
            plt.tight_layout()

            if output_dir:
                plt.savefig(os.path.join(output_dir, 'feature_importance_shap.png'))
            else:
                plt.show()

    def plot_predictions(self, X: pd.DataFrame, y: pd.Series, output_dir: str = None) -> None:
        """
        Plot predictions vs actual values.

        Args:
            X: Features
            y: Target
            output_dir: Directory to save plots (optional)
        """
        # Generate predictions
        y_pred = self.predict(X)

        # Calculate metrics
        r2 = r2_score(y, y_pred)
        rmse = np.sqrt(mean_squared_error(y, y_pred))

        # Create scatter plot
        plt.figure(figsize=(10, 6))
        plt.scatter(y, y_pred, alpha=0.5)
        plt.plot([y.min(), y.max()], [y.min(), y.max()], 'r--')
        plt.xlabel('Actual Growth Rate')
        plt.ylabel('Predicted Growth Rate')
        plt.title(f'Growth Rate Predictions (R² = {r2:.4f}, RMSE = {rmse:.4f})')
        plt.tight_layout()

        if output_dir:
            os.makedirs(output_dir, exist_ok=True)
            plt.savefig(os.path.join(output_dir, 'predictions_scatter.png'))
        else:
            plt.show()

        # Create residual plot
        residuals = y - y_pred
        plt.figure(figsize=(10, 6))
        plt.scatter(y_pred, residuals, alpha=0.5)
        plt.axhline(y=0, color='r', linestyle='--')
        plt.xlabel('Predicted Growth Rate')
        plt.ylabel('Residuals')
        plt.title('Residual Plot')
        plt.tight_layout()

        if output_dir:
            plt.savefig(os.path.join(output_dir, 'predictions_residuals.png'))
        else:
            plt.show()

    def save(self, output_dir: str) -> None:
        """
        Save the model to disk.

        Args:
            output_dir: Directory to save the model
        """
        os.makedirs(output_dir, exist_ok=True)

        # Save base models
        joblib.dump(self.rf_model, os.path.join(output_dir, 'rf_model.joblib'))
        joblib.dump(self.xgb_model, os.path.join(output_dir, 'xgb_model.joblib'))
        joblib.dump(self.lgb_model, os.path.join(output_dir, 'lgb_model.joblib'))

        # Save meta-model
        joblib.dump(self.meta_model, os.path.join(output_dir, 'meta_model.joblib'))

        # Save feature importances
        if self.feature_importances:
            pd.DataFrame(self.feature_importances).to_csv(
                os.path.join(output_dir, 'feature_importances.csv')
            )

        # Save metrics
        if self.metrics:
            pd.DataFrame([self.metrics]).to_csv(
                os.path.join(output_dir, 'metrics.csv'), index=False
            )

        # Save CV results
        if self.cv_results:
            pd.DataFrame(self.cv_results).to_csv(
                os.path.join(output_dir, 'cv_results.csv')
            )

        # Save feature names
        if self.feature_names:
            with open(os.path.join(output_dir, 'feature_names.txt'), 'w') as f:
                for feature in self.feature_names:
                    f.write(f"{feature}\n")

        logger.info(f"Model saved to {output_dir}")

    def load(self, input_dir: str) -> 'ImprovedGrowthModel':
        """
        Load the model from disk.

        Args:
            input_dir: Directory to load the model from

        Returns:
            Self
        """
        # Load base models
        self.rf_model = joblib.load(os.path.join(input_dir, 'rf_model.joblib'))
        self.xgb_model = joblib.load(os.path.join(input_dir, 'xgb_model.joblib'))
        self.lgb_model = joblib.load(os.path.join(input_dir, 'lgb_model.joblib'))

        # Load meta-model
        self.meta_model = joblib.load(os.path.join(input_dir, 'meta_model.joblib'))

        # Load feature importances if available
        feature_importances_path = os.path.join(input_dir, 'feature_importances.csv')
        if os.path.exists(feature_importances_path):
            self.feature_importances = pd.read_csv(feature_importances_path, index_col=0).to_dict()

        # Load metrics if available
        metrics_path = os.path.join(input_dir, 'metrics.csv')
        if os.path.exists(metrics_path):
            self.metrics = pd.read_csv(metrics_path).iloc[0].to_dict()

        # Load CV results if available
        cv_results_path = os.path.join(input_dir, 'cv_results.csv')
        if os.path.exists(cv_results_path):
            self.cv_results = pd.read_csv(cv_results_path, index_col=0).to_dict()

        # Load feature names if available
        feature_names_path = os.path.join(input_dir, 'feature_names.txt')
        if os.path.exists(feature_names_path):
            with open(feature_names_path, 'r') as f:
                self.feature_names = [line.strip() for line in f.readlines()]

        logger.info(f"Model loaded from {input_dir}")

        return self

if __name__ == "__main__":
    # This module is not meant to be run directly
    logger.info("This module provides the ImprovedGrowthModel class for growth rate prediction.")
