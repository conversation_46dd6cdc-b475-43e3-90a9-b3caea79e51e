#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Improved temperature prediction model using Random Forest with optimized hyperparameters.
"""

import os
import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Tuple, Union, Optional
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import KFold, cross_val_score, GridSearchCV
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt
import seaborn as sns
import joblib

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ImprovedTemperatureModel:
    """
    Improved temperature prediction model using Random Forest with optimized hyperparameters.
    """

    def __init__(self, params=None):
        """
        Initialize the model with parameters.

        Args:
            params: Parameters for Random Forest
        """
        # Set default parameters if not provided
        self.params = params or {
            'n_estimators': 500,
            'max_depth': 20,
            'min_samples_split': 5,
            'min_samples_leaf': 2,
            'max_features': 'sqrt',
            'bootstrap': True,
            'oob_score': True,
            'n_jobs': -1,
            'random_state': 42
        }

        # Initialize model
        self.model = RandomForestRegressor(**self.params)

        # For storing feature importances
        self.feature_importances = None

        # For storing evaluation metrics
        self.metrics = {}

        # For storing feature names
        self.feature_names = None

        # For storing scaler
        self.scaler = StandardScaler()

    def fit(self, X_train: pd.DataFrame, y_train: pd.Series,
            X_val: pd.DataFrame = None, y_val: pd.Series = None) -> 'ImprovedTemperatureModel':
        """
        Train the model.

        Args:
            X_train: Training features
            y_train: Training target
            X_val: Validation features (optional)
            y_val: Validation target (optional)

        Returns:
            Self
        """
        # Store feature names
        self.feature_names = X_train.columns.tolist()

        # Scale features
        X_train_scaled = pd.DataFrame(
            self.scaler.fit_transform(X_train),
            columns=X_train.columns,
            index=X_train.index
        )

        logger.info("Training Random Forest model for temperature prediction...")

        # Train the model
        self.model.fit(X_train_scaled, y_train)

        # Calculate training metrics
        train_preds = self.model.predict(X_train_scaled)
        train_r2 = r2_score(y_train, train_preds)
        train_rmse = np.sqrt(mean_squared_error(y_train, train_preds))
        train_mae = mean_absolute_error(y_train, train_preds)

        self.metrics['train_r2'] = train_r2
        self.metrics['train_rmse'] = train_rmse
        self.metrics['train_mae'] = train_mae

        logger.info(f"Training metrics - R²: {train_r2:.4f}, RMSE: {train_rmse:.4f}, MAE: {train_mae:.4f}")

        # Calculate validation metrics if validation set is provided
        if X_val is not None and y_val is not None:
            X_val_scaled = pd.DataFrame(
                self.scaler.transform(X_val),
                columns=X_val.columns,
                index=X_val.index
            )

            val_preds = self.model.predict(X_val_scaled)
            val_r2 = r2_score(y_val, val_preds)
            val_rmse = np.sqrt(mean_squared_error(y_val, val_preds))
            val_mae = mean_absolute_error(y_val, val_preds)

            self.metrics['val_r2'] = val_r2
            self.metrics['val_rmse'] = val_rmse
            self.metrics['val_mae'] = val_mae

            logger.info(f"Validation metrics - R²: {val_r2:.4f}, RMSE: {val_rmse:.4f}, MAE: {val_mae:.4f}")

        # Store feature importances
        self.feature_importances = pd.Series(
            self.model.feature_importances_,
            index=X_train.columns
        ).sort_values(ascending=False)

        return self

    def optimize_hyperparameters(self, X: pd.DataFrame, y: pd.Series, cv: int = 5) -> Dict:
        """
        Optimize hyperparameters using grid search.

        Args:
            X: Features
            y: Target
            cv: Number of cross-validation folds

        Returns:
            Dictionary of best parameters
        """
        logger.info("Optimizing hyperparameters for temperature prediction...")

        # Scale features
        X_scaled = pd.DataFrame(
            self.scaler.fit_transform(X),
            columns=X.columns,
            index=X.index
        )

        # Define parameter grid
        param_grid = {
            'n_estimators': [200, 500, 1000],
            'max_depth': [10, 20, 30, None],
            'min_samples_split': [2, 5, 10],
            'min_samples_leaf': [1, 2, 4],
            'max_features': ['sqrt', 'log2', None]
        }

        # Initialize grid search
        grid_search = GridSearchCV(
            RandomForestRegressor(random_state=42, n_jobs=-1),
            param_grid=param_grid,
            cv=cv,
            scoring='r2',
            n_jobs=-1,
            verbose=1
        )

        # Fit grid search
        grid_search.fit(X_scaled, y)

        # Get best parameters
        best_params = grid_search.best_params_

        logger.info(f"Best parameters: {best_params}")
        logger.info(f"Best R²: {grid_search.best_score_:.4f}")

        # Update model parameters
        self.params.update(best_params)
        self.model = RandomForestRegressor(**self.params)

        return best_params

    def predict(self, X: pd.DataFrame) -> np.ndarray:
        """
        Generate predictions for new data.

        Args:
            X: Features

        Returns:
            Predictions
        """
        # Scale features
        X_scaled = pd.DataFrame(
            self.scaler.transform(X),
            columns=X.columns,
            index=X.index
        )

        # Generate predictions
        return self.model.predict(X_scaled)

    def evaluate(self, X: pd.DataFrame, y: pd.Series) -> Dict[str, float]:
        """
        Evaluate the model on new data.

        Args:
            X: Features
            y: Target

        Returns:
            Dictionary of evaluation metrics
        """
        # Generate predictions
        y_pred = self.predict(X)

        # Calculate metrics
        metrics = {
            'R2': r2_score(y, y_pred),
            'RMSE': np.sqrt(mean_squared_error(y, y_pred)),
            'MAE': mean_absolute_error(y, y_pred)
        }

        # Log metrics
        logger.info(f"Evaluation metrics - R²: {metrics['R2']:.4f}, RMSE: {metrics['RMSE']:.4f}, MAE: {metrics['MAE']:.4f}")

        return metrics

    def plot_feature_importance(self, output_dir: str = None, n_features: int = 20) -> None:
        """
        Plot feature importance.

        Args:
            output_dir: Directory to save plots (optional)
            n_features: Number of top features to plot
        """
        if self.feature_importances is None or not isinstance(self.feature_importances, pd.Series):
            logger.warning("Feature importances not available. Train the model first.")
            return

        # Create output directory if provided
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)

        # Plot feature importance
        plt.figure(figsize=(12, 8))
        self.feature_importances.head(n_features).plot(kind='barh')
        plt.title(f'Top {n_features} Feature Importance for Temperature Prediction')
        plt.tight_layout()

        if output_dir:
            plt.savefig(os.path.join(output_dir, 'temperature_feature_importance.png'))
        else:
            plt.show()

    def plot_predictions(self, X: pd.DataFrame, y: pd.Series, output_dir: str = None) -> None:
        """
        Plot predictions vs actual values.

        Args:
            X: Features
            y: Target
            output_dir: Directory to save plots (optional)
        """
        # Generate predictions
        y_pred = self.predict(X)

        # Calculate metrics
        r2 = r2_score(y, y_pred)
        rmse = np.sqrt(mean_squared_error(y, y_pred))

        # Create scatter plot
        plt.figure(figsize=(10, 6))
        plt.scatter(y, y_pred, alpha=0.5)
        plt.plot([y.min(), y.max()], [y.min(), y.max()], 'r--')
        plt.xlabel('Actual Temperature')
        plt.ylabel('Predicted Temperature')
        plt.title(f'Temperature Predictions (R² = {r2:.4f}, RMSE = {rmse:.4f})')
        plt.tight_layout()

        if output_dir:
            os.makedirs(output_dir, exist_ok=True)
            plt.savefig(os.path.join(output_dir, 'temperature_predictions_scatter.png'))
        else:
            plt.show()

        # Create residual plot
        residuals = y - y_pred
        plt.figure(figsize=(10, 6))
        plt.scatter(y_pred, residuals, alpha=0.5)
        plt.axhline(y=0, color='r', linestyle='--')
        plt.xlabel('Predicted Temperature')
        plt.ylabel('Residuals')
        plt.title('Residual Plot for Temperature Predictions')
        plt.tight_layout()

        if output_dir:
            plt.savefig(os.path.join(output_dir, 'temperature_predictions_residuals.png'))
        else:
            plt.show()

    def save(self, output_dir: str) -> None:
        """
        Save the model to disk.

        Args:
            output_dir: Directory to save the model
        """
        os.makedirs(output_dir, exist_ok=True)

        # Save model
        joblib.dump(self.model, os.path.join(output_dir, 'temperature_model.joblib'))

        # Save scaler
        joblib.dump(self.scaler, os.path.join(output_dir, 'temperature_scaler.joblib'))

        # Save feature importances
        if self.feature_importances is not None:
            self.feature_importances.to_csv(os.path.join(output_dir, 'temperature_feature_importances.csv'))

        # Save metrics
        if self.metrics:
            pd.DataFrame([self.metrics]).to_csv(os.path.join(output_dir, 'temperature_metrics.csv'), index=False)

        # Save feature names
        if self.feature_names:
            with open(os.path.join(output_dir, 'temperature_feature_names.txt'), 'w') as f:
                for feature in self.feature_names:
                    f.write(f"{feature}\n")

        logger.info(f"Temperature model saved to {output_dir}")

    def load(self, input_dir: str) -> 'ImprovedTemperatureModel':
        """
        Load the model from disk.

        Args:
            input_dir: Directory to load the model from

        Returns:
            Self
        """
        # Load model
        self.model = joblib.load(os.path.join(input_dir, 'temperature_model.joblib'))

        # Load scaler
        self.scaler = joblib.load(os.path.join(input_dir, 'temperature_scaler.joblib'))

        # Load feature importances if available
        feature_importances_path = os.path.join(input_dir, 'temperature_feature_importances.csv')
        if os.path.exists(feature_importances_path):
            self.feature_importances = pd.read_csv(feature_importances_path, index_col=0, squeeze=True)

        # Load metrics if available
        metrics_path = os.path.join(input_dir, 'temperature_metrics.csv')
        if os.path.exists(metrics_path):
            self.metrics = pd.read_csv(metrics_path).iloc[0].to_dict()

        # Load feature names if available
        feature_names_path = os.path.join(input_dir, 'temperature_feature_names.txt')
        if os.path.exists(feature_names_path):
            with open(feature_names_path, 'r') as f:
                self.feature_names = [line.strip() for line in f.readlines()]

        logger.info(f"Temperature model loaded from {input_dir}")

        return self

if __name__ == "__main__":
    # This module is not meant to be run directly
    logger.info("This module provides the ImprovedTemperatureModel class for temperature prediction.")
