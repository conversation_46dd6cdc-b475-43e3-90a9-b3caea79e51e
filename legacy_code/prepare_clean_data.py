#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Prepare Clean Feature and Metadata Files.

This script handles data cleaning, preprocessing, and feature engineering
to prepare clean feature and metadata files for model training.
"""

import os
import logging
import numpy as np
import pandas as pd
from sklearn.preprocessing import StandardScaler
from typing import <PERSON>ple, Optional
from feature_engineering import create_interaction_features, create_nonlinear_features
from temperature_feature_engineering import create_domain_specific_features, create_pca_features

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_data(feature_file: str, metadata_file: str) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    Load feature and metadata files.
    
    Args:
        feature_file: Path to the feature file
        metadata_file: Path to the metadata file
        
    Returns:
        Tuple of (features DataFrame, metadata DataFrame)
    """
    logger.info(f"Loading feature file: {feature_file}")
    if not os.path.exists(feature_file):
        raise FileNotFoundError(f"Feature file not found: {feature_file}")
    
    features_df = pd.read_csv(feature_file, index_col=0, sep='\t')
    logger.info(f"Loaded features shape: {features_df.shape}")
    logger.info(f"Feature columns: {features_df.columns.tolist()[:5]}...")
    
    logger.info(f"Loading metadata file: {metadata_file}")
    if not os.path.exists(metadata_file):
        raise FileNotFoundError(f"Metadata file not found: {metadata_file}")
    
    metadata_df = pd.read_csv(metadata_file, index_col=0, sep='\t')
    logger.info(f"Loaded metadata shape: {metadata_df.shape}")
    logger.info(f"Metadata columns: {metadata_df.columns.tolist()[:5]}...")
    
    return features_df, metadata_df

def clean_features(X: pd.DataFrame) -> pd.DataFrame:
    """
    Clean feature data by handling missing values and outliers.
    
    Args:
        X: DataFrame containing features
        
    Returns:
        Cleaned DataFrame
    """
    logger.info("Cleaning feature data...")
    logger.info(f"Input features shape: {X.shape}")
    
    X_clean = X.copy()
    
    # Replace infinities with NaN
    X_clean = X_clean.replace([np.inf, -np.inf], np.nan)
    logger.info(f"Number of NaN values after replacing infinities: {X_clean.isna().sum().sum()}")
    
    # Replace NaNs with column medians
    X_clean = X_clean.fillna(X_clean.median())
    logger.info(f"Number of NaN values after filling with medians: {X_clean.isna().sum().sum()}")
    
    # Clip extreme values (beyond 5 std from mean) for each column
    for col in X_clean.columns:
        mean_val = X_clean[col].mean()
        std_val = X_clean[col].std()
        if std_val > 0:  # Avoid division by zero
            X_clean[col] = X_clean[col].clip(mean_val - 5 * std_val, mean_val + 5 * std_val)
    
    logger.info(f"Output features shape: {X_clean.shape}")
    return X_clean

def clean_metadata(metadata_df: pd.DataFrame) -> pd.DataFrame:
    """
    Clean metadata by handling missing values and categorical variables.
    
    Args:
        metadata_df: DataFrame containing metadata
        
    Returns:
        Cleaned metadata DataFrame
    """
    logger.info("Cleaning metadata...")
    logger.info(f"Input metadata shape: {metadata_df.shape}")
    
    metadata_clean = metadata_df.copy()
    
    # Handle missing values in categorical columns
    categorical_cols = metadata_clean.select_dtypes(include=['object']).columns
    logger.info(f"Found {len(categorical_cols)} categorical columns")
    for col in categorical_cols:
        metadata_clean[col] = metadata_clean[col].fillna('unknown')
    
    # Handle missing values in numerical columns
    numerical_cols = metadata_clean.select_dtypes(include=[np.number]).columns
    logger.info(f"Found {len(numerical_cols)} numerical columns")
    for col in numerical_cols:
        metadata_clean[col] = metadata_clean[col].fillna(metadata_clean[col].median())
    
    logger.info(f"Output metadata shape: {metadata_clean.shape}")
    return metadata_clean

def prepare_clean_data(
    feature_file: str,
    metadata_file: str,
    output_dir: str,
    use_interactions: bool = True,
    use_nonlinear: bool = True,
    use_pca: bool = True,
    use_domain: bool = True,
    n_pca_components: int = 50,
    top_k_interactions: int = 50
) -> None:
    """
    Prepare clean feature and metadata files with comprehensive preprocessing.
    
    Args:
        feature_file: Path to the feature file
        metadata_file: Path to the metadata file
        output_dir: Directory to save the clean files
        use_interactions: Whether to create interaction features
        use_nonlinear: Whether to create nonlinear features
        use_pca: Whether to create PCA features
        use_domain: Whether to create domain-specific features
        n_pca_components: Number of PCA components to create
        top_k_interactions: Number of top features to consider for interactions
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Load data
    features_df, metadata_df = load_data(feature_file, metadata_file)
    
    # Clean data
    features_clean = clean_features(features_df)
    metadata_clean = clean_metadata(metadata_df)
    
    # Apply feature engineering
    if use_domain:
        logger.info("Creating domain-specific features...")
        features_clean = create_domain_specific_features(features_clean, metadata_clean)
        logger.info(f"Shape after domain features: {features_clean.shape}")
    
    if use_nonlinear:
        logger.info("Creating nonlinear features...")
        features_clean = create_nonlinear_features(features_clean)
        logger.info(f"Shape after nonlinear features: {features_clean.shape}")
    
    if use_interactions:
        logger.info("Creating interaction features...")
        features_clean = create_interaction_features(features_clean, top_k=top_k_interactions)
        logger.info(f"Shape after interaction features: {features_clean.shape}")
    
    if use_pca:
        logger.info("Creating PCA features...")
        if features_clean.empty:
            raise ValueError("No features available for PCA. Check if feature engineering steps are working correctly.")
        features_clean = create_pca_features(features_clean, n_components=n_pca_components)
        logger.info(f"Shape after PCA features: {features_clean.shape}")
    
    # Save clean files
    features_output = os.path.join(output_dir, 'clean_features.csv')
    metadata_output = os.path.join(output_dir, 'clean_metadata.csv')
    
    logger.info(f"Saving clean features to: {features_output}")
    features_clean.to_csv(features_output)
    
    logger.info(f"Saving clean metadata to: {metadata_output}")
    metadata_clean.to_csv(metadata_output)
    
    logger.info("Data preparation completed successfully!")

def main():
    """Main function to run the data preparation script."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Prepare clean feature and metadata files')
    parser.add_argument('--feature_file', required=True, help='Path to the feature file')
    parser.add_argument('--metadata_file', required=True, help='Path to the metadata file')
    parser.add_argument('--output_dir', required=True, help='Directory to save the clean files')
    parser.add_argument('--use_interactions', action='store_true', help='Create interaction features')
    parser.add_argument('--use_nonlinear', action='store_true', help='Create nonlinear features')
    parser.add_argument('--use_pca', action='store_true', help='Create PCA features')
    parser.add_argument('--use_domain', action='store_true', help='Create domain-specific features')
    parser.add_argument('--n_pca_components', type=int, default=50, help='Number of PCA components')
    parser.add_argument('--top_k_interactions', type=int, default=50, help='Number of top features for interactions')
    
    args = parser.parse_args()
    
    prepare_clean_data(
        feature_file=args.feature_file,
        metadata_file=args.metadata_file,
        output_dir=args.output_dir,
        use_interactions=args.use_interactions,
        use_nonlinear=args.use_nonlinear,
        use_pca=args.use_pca,
        use_domain=args.use_domain,
        n_pca_components=args.n_pca_components,
        top_k_interactions=args.top_k_interactions
    )

if __name__ == '__main__':
    main() 