#!/bin/bash
# Training script for DeepMu with stacking RF model using bi-directional multi-target learning

# Set paths
METADATA="training_data/metadata.tsv"
FEATURE_FILE="./features/combined_features.tsv"  # Use the combined features TSV file
OUTPUT_DIR="models/stacking_bidirectional"
METRICS_DIR="metrics/stacking_bidirectional"

# Create necessary directories
mkdir -p $METRICS_DIR
mkdir -p $OUTPUT_DIR

# Set Random Forest parameters
RF_N_ESTIMATORS=300  # Increased from default
RF_MIN_SAMPLES_SPLIT=2
RF_MIN_SAMPLES_LEAF=1

# Set XGBoost parameters
XGB_N_ESTIMATORS=200
XGB_LEARNING_RATE=0.05  # Reduced from default
XGB_MAX_DEPTH=6
XGB_SUBSAMPLE=0.8

# Set LightGBM parameters
LGB_N_ESTIMATORS=250
LGB_LEARNING_RATE=0.05
LGB_MAX_DEPTH=7
LGB_SUBSAMPLE=0.8
LGB_MIN_CHILD_SAMPLES=15
LGB_REG_ALPHA=0.05
LGB_REG_LAMBDA=0.1

# Set feature selection parameters
RFE_STEP=0.2  # Remove 20% of features at each step
GROWTH_RATE_FEATURES=100  # Number of features to select for growth rate
TEMP_FEATURES=80  # Number of features to select for optimal temperature
INTERACTION_DEPTH=3  # Create interactions up to 3-way
MAX_INTERACTIONS=75  # Increased number of interaction features
CV_FOLDS=5  # Number of cross-validation folds
SEED=42

# Make the script executable
chmod +x train_stacking_rf_transformer.py

# Run the training
echo "Training bi-directional multi-target stacking RF model..."
python train_stacking_rf_transformer.py \
    --metadata "$METADATA" \
    --feature-file "$FEATURE_FILE" \
    --output-dir "$OUTPUT_DIR" \
    --metrics-dir "$METRICS_DIR" \
    --rf-n-estimators "$RF_N_ESTIMATORS" \
    --rf-min-samples-split "$RF_MIN_SAMPLES_SPLIT" \
    --rf-min-samples-leaf "$RF_MIN_SAMPLES_LEAF" \
    --xgb-n-estimators "$XGB_N_ESTIMATORS" \
    --xgb-learning-rate "$XGB_LEARNING_RATE" \
    --xgb-max-depth "$XGB_MAX_DEPTH" \
    --xgb-subsample "$XGB_SUBSAMPLE" \
    --lgb-n-estimators "$LGB_N_ESTIMATORS" \
    --lgb-learning-rate "$LGB_LEARNING_RATE" \
    --lgb-max-depth "$LGB_MAX_DEPTH" \
    --lgb-subsample "$LGB_SUBSAMPLE" \
    --lgb-min-child-samples "$LGB_MIN_CHILD_SAMPLES" \
    --lgb-reg-alpha "$LGB_REG_ALPHA" \
    --lgb-reg-lambda "$LGB_REG_LAMBDA" \
    --growth-rate-features "$GROWTH_RATE_FEATURES" \
    --temperature-features "$TEMP_FEATURES" \
    --use-rfe \
    --rfe-step "$RFE_STEP" \
    --use-advanced-interactions \
    --interaction-depth "$INTERACTION_DEPTH" \
    --max-interactions "$MAX_INTERACTIONS" \
    --cv-folds "$CV_FOLDS" \
    --seed "$SEED" \
    --target "both"

echo "Training complete!"

# Generate comparison report
echo "Generating comparison report..."
python -c "
import os
import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('comparison_report')

# Define metrics directories
metrics_dirs = {
    'RF Hybrid Model': 'metrics/rf_hybrid_model',
    'Stacking RF': 'metrics/stacking_hybrid',
    'Bi-directional Stacking RF': 'metrics/stacking_bidirectional'
}

# Collect metrics
comparison_data = {}

for model_name, metrics_dir in metrics_dirs.items():
    # Try to load combined_results.json first
    results_file = Path(metrics_dir) / 'combined_results.json'
    if results_file.exists():
        try:
            with open(results_file, 'r') as f:
                metrics = json.load(f)
            
            model_metrics = {}
            
            # Extract growth rate metrics
            if 'growth_rate' in metrics:
                gr_metrics = metrics['growth_rate']
                for key, value in gr_metrics.items():
                    if key.startswith('test_'):
                        model_metrics[f'growth_rate_{key[5:]}'] = value
            
            # Extract temperature metrics
            if 'optimal_temperature' in metrics:
                temp_metrics = metrics['optimal_temperature']
                for key, value in temp_metrics.items():
                    if key.startswith('test_'):
                        model_metrics[f'optimal_temperature_{key[5:]}'] = value
            
            comparison_data[model_name] = model_metrics
        except Exception as e:
            logger.warning(f'Error loading combined metrics for {model_name}: {e}')
    
    # If combined_results.json not found or is incomplete, try individual metric files
    if model_name not in comparison_data or not comparison_data[model_name]:
        model_metrics = {}
        
        # Try to load growth rate metrics
        gr_file = Path(metrics_dir) / 'growth_rate_metrics.json'
        if gr_file.exists():
            try:
                with open(gr_file, 'r') as f:
                    gr_metrics = json.load(f)
                
                for key, value in gr_metrics.items():
                    if key.startswith('test_'):
                        model_metrics[f'growth_rate_{key[5:]}'] = value
            except Exception as e:
                logger.warning(f'Error loading growth rate metrics for {model_name}: {e}')
        
        # Try to load temperature metrics
        temp_file = Path(metrics_dir) / 'optimal_temperature_metrics.json'
        if temp_file.exists():
            try:
                with open(temp_file, 'r') as f:
                    temp_metrics = json.load(f)
                
                for key, value in temp_metrics.items():
                    if key.startswith('test_'):
                        model_metrics[f'optimal_temperature_{key[5:]}'] = value
            except Exception as e:
                logger.warning(f'Error loading temperature metrics for {model_name}: {e}')
        
        if model_metrics:
            comparison_data[model_name] = model_metrics

# Create comparison report
if comparison_data:
    with open('$METRICS_DIR/model_comparison.md', 'w') as f:
        f.write('# Model Performance Comparison\n\n')

        # Create comparison table
        f.write('## Performance Metrics\n\n')
        f.write('| Metric | RF Hybrid Model | Stacking RF | Bi-directional Stacking RF | Improvement |\n')
        f.write('|--------|----------------|------------|---------------------------|------------|\n')

        # Add metrics to table
        metrics_to_show = ['growth_rate_r2', 'optimal_temperature_r2', 'growth_rate_rmse', 'optimal_temperature_rmse', 'growth_rate_mae', 'optimal_temperature_mae']
        metric_names = {
            'growth_rate_r2': 'Growth Rate R²',
            'optimal_temperature_r2': 'Optimal Temperature R²',
            'growth_rate_rmse': 'Growth Rate RMSE',
            'optimal_temperature_rmse': 'Optimal Temperature RMSE',
            'growth_rate_mae': 'Growth Rate MAE',
            'optimal_temperature_mae': 'Optimal Temperature MAE'
        }

        for metric in metrics_to_show:
            metric_name = metric_names.get(metric, metric)
            f.write(f'| {metric_name} |')

            # RF Hybrid Model
            if 'RF Hybrid Model' in comparison_data and metric in comparison_data['RF Hybrid Model']:
                base_value = comparison_data['RF Hybrid Model'][metric]
                if isinstance(base_value, float):
                    f.write(f' {base_value:.4f} |')
                else:
                    f.write(f' {base_value} |')
            else:
                f.write(' N/A |')
                base_value = None

            # Stacking RF
            if 'Stacking RF' in comparison_data and metric in comparison_data['Stacking RF']:
                stack_value = comparison_data['Stacking RF'][metric]
                if isinstance(stack_value, float):
                    f.write(f' {stack_value:.4f} |')
                else:
                    f.write(f' {stack_value} |')
            else:
                f.write(' N/A |')
                stack_value = None

            # Bi-directional Stacking RF
            if 'Bi-directional Stacking RF' in comparison_data and metric in comparison_data['Bi-directional Stacking RF']:
                bidir_value = comparison_data['Bi-directional Stacking RF'][metric]
                if isinstance(bidir_value, float):
                    f.write(f' {bidir_value:.4f} |')
                else:
                    f.write(f' {bidir_value} |')
            else:
                f.write(' N/A |')
                bidir_value = None

            # Calculate improvement over stacking RF
            if stack_value is not None and bidir_value is not None:
                if isinstance(stack_value, (int, float)) and isinstance(bidir_value, (int, float)):
                    if 'r2' in metric:
                        # For R², higher is better
                        improvement = ((bidir_value - stack_value) / abs(stack_value)) * 100 if stack_value != 0 else float('inf')
                        f.write(f' {improvement:.2f}% |')
                    else:
                        # For RMSE and MAE, lower is better
                        improvement = ((stack_value - bidir_value) / stack_value) * 100
                        f.write(f' {improvement:.2f}% |')
                else:
                    f.write(' N/A |')
            else:
                f.write(' N/A |')

            f.write('\n')

        # Add conclusion
        f.write('\n## Conclusion\n\n')
        f.write('The bi-directional stacking model with multi-target learning demonstrates several advantages:\n\n')
        f.write('1. **Cross-target knowledge transfer**: Using predictions from one target as features for the other target allows models to leverage correlations between growth rate and optimal temperature.\n')
        f.write('2. **Mutual reinforcement**: The bi-directional approach creates a positive feedback loop where improvements in one prediction task enhance the other.\n')
        f.write('3. **Feature space enrichment**: Adding predicted values as features effectively increases the information available to each model without requiring additional data collection.\n')
        f.write('4. **Reduced overfitting**: By using predictions from another model, we incorporate ensemble-like benefits that improve generalization.\n')
        f.write('5. **Better handling of missing data**: The approach creates additional pathways for inference when some features might be missing or noisy.\n')
        f.write('6. **Biological insight**: The approach quantifies how much one biological property (e.g., growth rate) contributes to predicting another (e.g., optimal temperature).\n')

        logger.info('Comparison report generated successfully')
else:
    logger.error('No comparison data available')
"

echo "Comparison report generated!"

echo "All tasks completed successfully!" 