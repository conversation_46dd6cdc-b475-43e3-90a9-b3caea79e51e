#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Hybrid Tabular Model for growth rate and optimal temperature prediction.
This implementation combines Random Forest with a Neural Network using a robust feature selection approach.

Key features:
- Correlation-based feature selection
- Robust scaling of features and targets
- Simple MLP architecture with batch normalization
- Random Forest integration for improved predictions
- Automatic ensemble weight optimization
"""

import os
import argparse
import logging
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.model_selection import train_test_split
import matplotlib.pyplot as plt
from pathlib import Path
import joblib

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TabularDataset(Dataset):
    def __init__(self, features, targets, categorical_cols=None, continuous_cols=None):
        self.features = features
        self.targets = targets
        self.categorical_cols = categorical_cols or []
        self.continuous_cols = continuous_cols or []
        
    def __len__(self):
        return len(self.features)
    
    def __getitem__(self, idx):
        # Handle case with no categorical columns
        if not self.categorical_cols:
            x_categ = torch.tensor([], dtype=torch.long)
        else:
        x_categ = torch.tensor(self.features[self.categorical_cols].iloc[idx].values, dtype=torch.long)
            
        x_cont = torch.tensor(self.features[self.continuous_cols].iloc[idx].values, dtype=torch.float32)
        y = torch.tensor(self.targets.iloc[idx].values, dtype=torch.float32)
        return x_categ, x_cont, y

def select_features(features_df, metadata_df, n_features=500):
    """
    Select top features based on Random Forest importance with correlation-based filtering.
    
    Args:
        features_df: DataFrame containing features
        metadata_df: DataFrame containing target variables
        n_features: Number of top features to select
        
    Returns:
        features_df with only selected features
    """
    logger.info(f"Performing feature selection to get top {n_features} features...")
    
    # Create a copy to avoid modifying original data
    features_copy = features_df.copy()
    
    # Identify categorical columns to exclude from feature selection
    categorical_cols = ['kingdom', 'codon_table']
    numeric_cols = [col for col in features_copy.columns if col not in categorical_cols]
    
    # Filter out low-variance features first (more aggressive filtering)
    var_threshold = np.percentile(features_copy[numeric_cols].var(), 25)  # Remove bottom 25% by variance
    high_var_cols = features_copy[numeric_cols].var()[features_copy[numeric_cols].var() > var_threshold].index.tolist()
    logger.info(f"Removed {len(numeric_cols) - len(high_var_cols)} low-variance features")
    
    # Calculate correlation with targets
    targets = metadata_df[['growth_rate', 'optimal_temperature']]
    correlation_scores = {}
    
    for col in high_var_cols:
        growth_corr = abs(np.corrcoef(features_copy[col], targets['growth_rate'])[0, 1])
        temp_corr = abs(np.corrcoef(features_copy[col], targets['optimal_temperature'])[0, 1])
        
        # Handle NaN correlations (can happen with constant features)
        if np.isnan(growth_corr): growth_corr = 0
        if np.isnan(temp_corr): temp_corr = 0
        
        # Weighted combination of correlations
        correlation_scores[col] = 0.5 * growth_corr + 0.5 * temp_corr
    
    # Sort features by correlation scores
    sorted_features = sorted(correlation_scores.items(), key=lambda x: x[1], reverse=True)
    correlation_features = [feat for feat, score in sorted_features if score > 0.05]
    logger.info(f"Selected {len(correlation_features)} features with correlation > 0.05")
    
    # If we need more features, add them from Random Forest importance
    if len(correlation_features) < n_features:
        # Train Random Forest on high-variance features
        rf_growth = RandomForestRegressor(
            n_estimators=100, 
            max_depth=15,
            random_state=42, 
            n_jobs=-1
        )
        rf_growth.fit(features_copy[high_var_cols], targets['growth_rate'])
        
        rf_temp = RandomForestRegressor(
            n_estimators=100,
            max_depth=15,
            random_state=42,
            n_jobs=-1
        )
        rf_temp.fit(features_copy[high_var_cols], targets['optimal_temperature'])
        
        # Combine feature importances
        growth_importance = pd.Series(rf_growth.feature_importances_, index=high_var_cols)
        temp_importance = pd.Series(rf_temp.feature_importances_, index=high_var_cols)
        combined_importance = (0.5 * growth_importance + 0.5 * temp_importance)
        
        # Get features not already selected by correlation
        remaining_features = [f for f in high_var_cols if f not in correlation_features]
        remaining_importance = combined_importance[remaining_features]
        
        # Add top remaining features by importance
        n_remaining = n_features - len(correlation_features)
        if n_remaining > 0:
            rf_features = remaining_importance.nlargest(n_remaining).index.tolist()
            logger.info(f"Added {len(rf_features)} features from Random Forest importance")
        else:
            rf_features = []
        
        # Combine feature lists
        selected_numeric = correlation_features + rf_features
    else:
        # If we have enough from correlation, take the top n_features
        selected_numeric = correlation_features[:n_features]
    
    # Add back categorical columns
    selected_features = selected_numeric + categorical_cols
    
    # Create feature importance DataFrame
    feature_importance_df = pd.DataFrame({
        'feature': selected_numeric,
        'importance': [correlation_scores.get(f, 0) for f in selected_numeric]
    }).sort_values('importance', ascending=False)
    
    logger.info(f"Selected {len(selected_features)} features ({len(selected_numeric)} numeric + {len(categorical_cols)} categorical)")
    
    return features_df[selected_features], feature_importance_df

def load_data(feature_file, metadata_file):
    """
    Load and prepare feature and metadata files.
    
    Args:
        feature_file: Path to the combined feature file (TSV)
        metadata_file: Path to the metadata file (TSV)
        
    Returns:
        Tuple of (features_df, metadata_df)
    """
    logger.info(f"Loading features from {feature_file}")
    features_df = pd.read_csv(feature_file, sep='\t', index_col='genome_id')
    
    # Check for NaN values in features
    nan_count = features_df.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in features. Filling with 0.")
        features_df = features_df.fillna(0)
    
    # Check for infinite values
    inf_count = np.isinf(features_df.values).sum()
    if inf_count > 0:
        logger.warning(f"Found {inf_count} infinite values in features. Replacing with max/min values.")
        features_df = features_df.replace([np.inf, -np.inf], np.nan)
        features_df = features_df.fillna(features_df.max())

    logger.info(f"Loading metadata from {metadata_file}")
    metadata_df = pd.read_csv(metadata_file, sep='\t', index_col='genome_id')
    
    # Check for NaN values in metadata
    nan_count = metadata_df.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in metadata. Filling with 0.")
        metadata_df = metadata_df.fillna(0)

    # Get common genomes
    common_genomes = list(set(features_df.index) & set(metadata_df.index))
    logger.info(f"Found {len(common_genomes)} common genomes")

    # Filter to common genomes
    features_df = features_df.loc[common_genomes]
    metadata_df = metadata_df.loc[common_genomes]
    
    # Merge categorical columns from metadata into features
    categorical_cols = ['kingdom', 'codon_table']
    features_df = features_df.join(metadata_df[categorical_cols])
    
    return features_df, metadata_df

def prepare_data(features_df, metadata_df):
    """
    Prepare data for FT Transformer model with improved normalization.
    The growth_rate variable is transformed using square root to improve normalization.
    
    Args:
        features_df: DataFrame containing features
        metadata_df: DataFrame containing metadata
        
    Returns:
        Tuple of (train_dataset, val_dataset, test_dataset, categorical_cols, continuous_cols, scaler)
    """
    # Identify categorical and continuous columns
    categorical_cols = ['kingdom', 'codon_table']
    
    # Ensure categorical columns exist in the features dataframe
    for col in categorical_cols:
        if col not in features_df.columns:
            logger.warning(f"Categorical column {col} not found in features. Will not use it.")
    
    # Filter to only include available categorical columns
    categorical_cols = [col for col in categorical_cols if col in features_df.columns]
    
    # Define continuous columns as all non-categorical columns
    continuous_cols = [col for col in features_df.columns if col not in categorical_cols]
    
    logger.info(f"Using {len(categorical_cols)} categorical features and {len(continuous_cols)} continuous features")
    
    # Split data
    X_train, X_temp, y_train, y_temp = train_test_split(
        features_df, metadata_df[['growth_rate', 'optimal_temperature']], 
        test_size=0.3, random_state=42, stratify=features_df['kingdom'] if 'kingdom' in features_df.columns and len(features_df['kingdom'].unique()) > 1 else None
    )
    
    X_val, X_test, y_val, y_test = train_test_split(
        X_temp, y_temp, test_size=0.5, random_state=42
    )
    
    # Scale features using RobustScaler
    feature_scaler = RobustScaler()
    X_train[continuous_cols] = feature_scaler.fit_transform(X_train[continuous_cols])
    X_val[continuous_cols] = feature_scaler.transform(X_val[continuous_cols])
    X_test[continuous_cols] = feature_scaler.transform(X_test[continuous_cols])
    
    # Apply square root transformation to growth_rate (first column only)
    y_train_transformed = y_train.copy()
    y_val_transformed = y_val.copy()
    y_test_transformed = y_test.copy()
    
    # Apply sqrt transform only to growth_rate (positive values only)
    y_train_transformed['growth_rate'] = np.sqrt(np.maximum(0, y_train_transformed['growth_rate']))
    y_val_transformed['growth_rate'] = np.sqrt(np.maximum(0, y_val_transformed['growth_rate']))
    y_test_transformed['growth_rate'] = np.sqrt(np.maximum(0, y_test_transformed['growth_rate']))
    
    # Now normalize target variables with RobustScaler
    target_scaler = RobustScaler()
    y_train_values = y_train_transformed.values
    y_train_scaled = target_scaler.fit_transform(y_train_values)
    y_train_scaled_df = pd.DataFrame(y_train_scaled, index=y_train.index, columns=y_train.columns)
    
    y_val_values = y_val_transformed.values
    y_val_scaled = target_scaler.transform(y_val_values)
    y_val_scaled_df = pd.DataFrame(y_val_scaled, index=y_val.index, columns=y_val.columns)
    
    y_test_values = y_test_transformed.values
    y_test_scaled = target_scaler.transform(y_test_values)
    y_test_scaled_df = pd.DataFrame(y_test_scaled, index=y_test.index, columns=y_test.columns)
    
    # Convert categorical columns to integer codes
    if categorical_cols:
    for col in categorical_cols:
            # Handle potential missing or NaN values
            X_train[col] = X_train[col].fillna('unknown')
            X_val[col] = X_val[col].fillna('unknown')
            X_test[col] = X_test[col].fillna('unknown')
            
        # Get all unique values from train, val, and test
        all_categories = pd.concat([X_train[col], X_val[col], X_test[col]]).unique()
            
        # Create a mapping
        category_map = {cat: i for i, cat in enumerate(all_categories)}
            
            # Apply mapping and ensure integer type
            X_train[col] = X_train[col].map(category_map).astype(int)
            X_val[col] = X_val[col].map(category_map).astype(int)
            X_test[col] = X_test[col].map(category_map).astype(int)
            
            logger.info(f"Categorical column {col} mapped to {len(category_map)} integer values")
    
    # Create datasets
    train_dataset = TabularDataset(X_train, y_train_scaled_df, categorical_cols, continuous_cols)
    val_dataset = TabularDataset(X_val, y_val_scaled_df, categorical_cols, continuous_cols)
    test_dataset = TabularDataset(X_test, y_test_scaled_df, categorical_cols, continuous_cols)
    
    return train_dataset, val_dataset, test_dataset, categorical_cols, continuous_cols, feature_scaler, target_scaler, (X_train, X_val, X_test, y_train, y_val, y_test)

def transform_targets(targets):
    """
    Apply square root transformation to growth_rate (first column) only.
    
    Args:
        targets: Target values, numpy array or DataFrame
        
    Returns:
        Transformed targets
    """
    if isinstance(targets, pd.DataFrame):
        result = targets.copy()
        result['growth_rate'] = np.sqrt(np.maximum(0, result['growth_rate']))
        return result
    else:
        result = targets.copy()
        # First column is growth_rate
        result[:, 0] = np.sqrt(np.maximum(0, result[:, 0]))
        return result

def inverse_transform_targets(targets):
    """
    Inverse transform to get back original scale for growth_rate.
    
    Args:
        targets: Transformed target values, numpy array or DataFrame
        
    Returns:
        Original scale targets
    """
    if isinstance(targets, pd.DataFrame):
        result = targets.copy()
        result['growth_rate'] = np.square(result['growth_rate'])
        return result
    else:
        result = targets.copy()
        # First column is growth_rate
        result[:, 0] = np.square(result[:, 0])
        return result

class SimpleTabularModel(nn.Module):
    """
    A simpler model for tabular data with skip connections and batch normalization
    """
    def __init__(
        self,
        categories,
        num_continuous,
        dim,
        dim_out,
        dropout=0.2
    ):
        super().__init__()
        
        # Embedding dimensions - handle empty categories case
        if categories and len(categories) > 0:
        embedding_dims = [min(50, (cat + 1) // 2) for cat in categories]
        self.cat_embeddings = nn.ModuleList([
            nn.Embedding(categories[i], embedding_dims[i])
            for i in range(len(categories))
        ])
            cat_dims_sum = sum(embedding_dims)
        else:
            self.cat_embeddings = nn.ModuleList([])
            cat_dims_sum = 0
        
        # Determine input dimension
        input_dim = cat_dims_sum + num_continuous
        
        # Create MLP layers with skip connections and batch normalization
        self.layer1 = nn.Linear(input_dim, dim)
        self.bn1 = nn.BatchNorm1d(dim)
        
        self.layer2 = nn.Linear(dim, dim)
        self.bn2 = nn.BatchNorm1d(dim)
        
        self.layer3 = nn.Linear(dim, dim // 2)
        self.bn3 = nn.BatchNorm1d(dim // 2)
        
        self.output = nn.Linear(dim // 2, dim_out)
        
        self.dropout = nn.Dropout(dropout)
        self.activation = nn.LeakyReLU(0.1)
    
    def forward(self, x_categ, x_cont):
        # Process categorical features
        embedded = []
        
        # Handle case with no categorical features
        if len(self.cat_embeddings) > 0 and x_categ.nelement() > 0:
        for i, embedding in enumerate(self.cat_embeddings):
            embedded.append(embedding(x_categ[:, i]))
        
        # Concatenate embeddings and continuous features
        if embedded:
            x_cat = torch.cat(embedded, dim=1)
            x = torch.cat([x_cat, x_cont], dim=1)
        else:
            x = x_cont
        
        # First layer with batch norm and activation
        x1 = self.layer1(x)
        x1 = self.bn1(x1)
        x1 = self.activation(x1)
        x1 = self.dropout(x1)
        
        # Second layer with skip connection
        x2 = self.layer2(x1)
        x2 = self.bn2(x2)
        x2 = self.activation(x2)
        x2 = self.dropout(x2)
        
        # Third layer
        x3 = self.layer3(x2)
        x3 = self.bn3(x3)
        x3 = self.activation(x3)
        x3 = self.dropout(x3)
        
        # Output layer
        return self.output(x3)

class HybridRFTabular(nn.Module):
    def __init__(self, tabular_model, categorical_cols, continuous_cols, n_trees=100):
        super().__init__()
        self.tabular_model = tabular_model
        self.categorical_cols = categorical_cols
        self.continuous_cols = continuous_cols
        self.n_trees = n_trees
        
        # Random Forest models (initialized during training)
        self.rf_growth = None
        self.rf_temp = None
        
        # Weights for ensemble that can be learned
        self.ensemble_weights = nn.Parameter(torch.tensor([0.5, 0.5], dtype=torch.float32))
        
    def train_rf_models(self, X_train, y_train):
        """Train Random Forest models separately"""
        logger.info("Training Random Forest components...")
        
        # Ensure X_train has correct column types
        X_train_rf = X_train[self.continuous_cols].copy()
        
        # Initialize Random Forest models
        self.rf_growth = RandomForestRegressor(
            n_estimators=self.n_trees,
            max_depth=15,
            min_samples_split=5,
            min_samples_leaf=2,
            random_state=42,
            n_jobs=-1
        )
        
        self.rf_temp = RandomForestRegressor(
            n_estimators=self.n_trees,
            max_depth=15,
            min_samples_split=5,
            min_samples_leaf=2,
            random_state=42,
            n_jobs=-1
        )
        
        # Transform growth rate before training
        y_train_transformed = y_train.copy()
        y_train_transformed.iloc[:, 0] = np.sqrt(np.maximum(0, y_train_transformed.iloc[:, 0]))
        
        # Train Random Forest models - growth rate on transformed data, temperature on original
        try:
            self.rf_growth.fit(X_train_rf, y_train_transformed.iloc[:, 0])  # transformed growth_rate
            self.rf_temp.fit(X_train_rf, y_train.iloc[:, 1])                # original optimal_temperature
        logger.info("Random Forest components trained successfully")
        except Exception as e:
            logger.error(f"Error training Random Forest models: {str(e)}")
            logger.info("Will rely on neural network for predictions")
    
    def forward(self, x_categ, x_cont):
        """
        Forward pass for neural network component
        """
        return self.tabular_model(x_categ, x_cont)
    
    def predict(self, X, target_scaler=None):
        """
        Make predictions with both models and combine them.
        This is used during inference.
        
        Args:
            X: Input features DataFrame
            target_scaler: Scaler for target variables (optional)
        
        Returns:
            Ensemble predictions
        """
        # Define device
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # Ensure categorical columns are converted to integers
        for col in self.categorical_cols:
            if col in X.columns:
                X[col] = X[col].astype(int)
        
        # Get categorical and continuous data
        x_categ = torch.tensor(X[self.categorical_cols].values, dtype=torch.long)
        x_cont = torch.tensor(X[self.continuous_cols].values, dtype=torch.float32)
        
        # Get neural network predictions
        self.eval()
        with torch.no_grad():
            nn_pred = self.tabular_model(x_categ.to(device), x_cont.to(device)).cpu().numpy()
        
        # Try to get RF predictions with error handling
        try:
            # Note: rf_growth is already trained on sqrt-transformed data
        rf_growth_pred = self.rf_growth.predict(X[self.continuous_cols]).reshape(-1, 1)
        rf_temp_pred = self.rf_temp.predict(X[self.continuous_cols]).reshape(-1, 1)
        rf_pred = np.hstack([rf_growth_pred, rf_temp_pred])
        
        # Handle scaling if provided
        if target_scaler is not None:
                # Apply scaler to RF predictions
                # No need to transform growth_rate as rf_growth already predicts in sqrt space
            rf_pred_scaled = target_scaler.transform(rf_pred)
            
                # Combine scaled predictions (nn_pred is already scaled)
            weights = torch.softmax(self.ensemble_weights, dim=0).detach().cpu().numpy()
            ensemble_pred_scaled = weights[0] * nn_pred + weights[1] * rf_pred_scaled
            
                # Inverse scale
                ensemble_pred_transformed = target_scaler.inverse_transform(ensemble_pred_scaled)
                
                # Inverse sqrt transform for growth_rate
                ensemble_pred = ensemble_pred_transformed.copy()
                ensemble_pred[:, 0] = np.square(ensemble_pred_transformed[:, 0])
        else:
                # Without scaler, direct combination
            weights = torch.softmax(self.ensemble_weights, dim=0).detach().cpu().numpy()
                ensemble_pred_transformed = weights[0] * nn_pred + weights[1] * rf_pred
                
                # Inverse sqrt transform for growth_rate
                ensemble_pred = ensemble_pred_transformed.copy()
                ensemble_pred[:, 0] = np.square(ensemble_pred_transformed[:, 0])
        except Exception as e:
            logger.warning(f"Error during RF prediction: {str(e)}")
            logger.info("Using neural network predictions only")
            
            # Use only neural network predictions
            if target_scaler is not None:
                # Inverse scale
                ensemble_pred_transformed = target_scaler.inverse_transform(nn_pred)
                
                # Inverse sqrt transform for growth_rate
                ensemble_pred = ensemble_pred_transformed.copy()
                ensemble_pred[:, 0] = np.square(ensemble_pred_transformed[:, 0])
            else:
                # Without scaler
                ensemble_pred_transformed = nn_pred
                
                # Inverse sqrt transform for growth_rate
                ensemble_pred = ensemble_pred_transformed.copy()
                ensemble_pred[:, 0] = np.square(ensemble_pred_transformed[:, 0])
        
        return ensemble_pred

def train_epoch(model, dataloader, criterion, optimizer, device):
    model.train()
    running_loss = 0.0
    
    for x_categ, x_cont, y in dataloader:
        x_categ, x_cont, y = x_categ.to(device), x_cont.to(device), y.to(device)
        
        optimizer.zero_grad()
        pred = model(x_categ, x_cont)
        loss = criterion(pred, y)
        loss.backward()
        optimizer.step()
        
        running_loss += loss.item() * x_categ.size(0)
    
    return running_loss / len(dataloader.dataset)

def validate(model, dataloader, criterion, device):
    model.eval()
    running_loss = 0.0
    all_targets = []
    all_predictions = []
    
    with torch.no_grad():
        for x_categ, x_cont, y in dataloader:
            x_categ, x_cont, y = x_categ.to(device), x_cont.to(device), y.to(device)
            
            pred = model(x_categ, x_cont)
            loss = criterion(pred, y)
            
            running_loss += loss.item() * x_categ.size(0)
            all_targets.extend(y.cpu().numpy())
            all_predictions.extend(pred.cpu().numpy())
    
    val_loss = running_loss / len(dataloader.dataset)
    val_rmse = np.sqrt(mean_squared_error(all_targets, all_predictions))
    val_r2 = r2_score(all_targets, all_predictions)
    
    return val_loss, val_rmse, val_r2, all_targets, all_predictions

def main():
    parser = argparse.ArgumentParser(description='Train Optimized FT Transformer model')
    parser.add_argument('--feature_file', type=str, default='./training_data/combined_features.tsv',
                        help='Path to feature file')
    parser.add_argument('--metadata_file', type=str, default='./training_data/metadata.tsv',
                        help='Path to metadata file')
    parser.add_argument('--n_features', type=int, default=500,
                        help='Number of features to select')
    parser.add_argument('--batch_size', type=int, default=64,
                        help='Batch size for training')
    parser.add_argument('--epochs', type=int, default=100,
                        help='Number of epochs to train')
    parser.add_argument('--lr', type=float, default=0.001,
                        help='Learning rate')
    parser.add_argument('--rf_trees', type=int, default=200,
                        help='Number of trees in Random Forest component')
    parser.add_argument('--hidden_dim', type=int, default=128,
                        help='Dimension of hidden layers')
    parser.add_argument('--dropout', type=float, default=0.3,
                        help='Dropout rate')
    parser.add_argument('--output_dir', type=str, default='./models/optimized_tabular',
                        help='Directory to save model')
    args = parser.parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Load data
    features_df, metadata_df = load_data(args.feature_file, args.metadata_file)
    
    # Perform feature selection
    features_df, feature_importance_df = select_features(features_df, metadata_df, args.n_features)
    
    # Save feature importance
    feature_importance_df.to_csv(os.path.join(args.output_dir, 'feature_importance.csv'), index=False)
    
    # Prepare data with target scaling
    train_dataset, val_dataset, test_dataset, categorical_cols, continuous_cols, feature_scaler, target_scaler, \
        (X_train, X_val, X_test, y_train, y_val, y_test) = prepare_data(features_df, metadata_df)
    
    # Create data loaders
    train_loader = DataLoader(train_dataset, batch_size=args.batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=args.batch_size)
    test_loader = DataLoader(test_dataset, batch_size=args.batch_size)
    
    # Get number of categories for each categorical column
    if categorical_cols:
    categories = [int(X_train[col].max()) + 1 for col in categorical_cols]
    logger.info(f"Categories: {categories}")
    else:
        categories = []
        logger.info("No categorical features available")
    
    # Initialize device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"Using device: {device}")
    
    # Initialize our simpler tabular model
    tabular_model = SimpleTabularModel(
        categories=categories,
        num_continuous=len(continuous_cols),
        dim=args.hidden_dim,
        dim_out=2,  # Two outputs: growth_rate and optimal_temperature
        dropout=args.dropout
    ).to(device)
    
    # Initialize hybrid model
    model = HybridRFTabular(
        tabular_model=tabular_model,
        categorical_cols=categorical_cols,
        continuous_cols=continuous_cols,
        n_trees=args.rf_trees
    ).to(device)
    
    # Initialize optimizer with weight decay
    optimizer = optim.Adam(model.parameters(), lr=args.lr, weight_decay=1e-5)
    
    # Use MSE loss since targets are now normalized
    criterion = nn.MSELoss()
    
    # Learning rate scheduler
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=0.5, patience=5
    )
    
    # Training loop
    best_val_loss = float('inf')
    epochs_no_improve = 0
    early_stopping_threshold = 10
    
    train_losses = []
    val_losses = []
    val_rmses = []
    val_r2s = []
    
    logger.info("Starting training...")
    for epoch in range(args.epochs):
        train_loss = train_epoch(model, train_loader, criterion, optimizer, device)
        val_loss, val_rmse, val_r2, _, _ = validate(model, val_loader, criterion, device)
        
        # Update learning rate
        scheduler.step(val_loss)
        
        train_losses.append(train_loss)
        val_losses.append(val_loss)
        val_rmses.append(val_rmse)
        val_r2s.append(val_r2)
        
        logger.info(f'Epoch {epoch+1}/{args.epochs}:')
        logger.info(f'Train Loss: {train_loss:.4f}')
        logger.info(f'Val Loss: {val_loss:.4f}, RMSE: {val_rmse:.4f}, R2: {val_r2:.4f}')
        
        # Save best model
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            epochs_no_improve = 0
            torch.save(model.state_dict(), os.path.join(args.output_dir, 'best_model.pth'))
            logger.info(f'Saved new best model with validation loss: {best_val_loss:.4f}')
        else:
            epochs_no_improve += 1
            
        # Early stopping check
        if epochs_no_improve >= early_stopping_threshold:
            logger.info(f"Early stopping after {epoch+1} epochs without improvement")
            break
    
    # Load best model
    model.load_state_dict(torch.load(os.path.join(args.output_dir, 'best_model.pth')))
    
    # Train Random Forest components on original data
    model.train_rf_models(X_train, y_train)
    
    # Save model components
    joblib.dump({
        'feature_scaler': feature_scaler,
        'target_scaler': target_scaler,
        'rf_growth': model.rf_growth,
        'rf_temp': model.rf_temp,
        'categorical_cols': categorical_cols,
        'continuous_cols': continuous_cols,
        'sqrt_transform_growth_rate': True  # Flag indicating we used sqrt transform
    }, os.path.join(args.output_dir, 'model_components.joblib'))
    
    # Save neural net separately
    torch.save(model.tabular_model.state_dict(), os.path.join(args.output_dir, 'nn_model.pth'))
    
    # Plot training curves
    plt.figure(figsize=(15, 5))
    
    plt.subplot(1, 3, 1)
    plt.plot(train_losses, label='Train Loss')
    plt.plot(val_losses, label='Val Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('Training and Validation Loss')
    plt.legend()
    
    plt.subplot(1, 3, 2)
    plt.plot(val_rmses)
    plt.xlabel('Epoch')
    plt.ylabel('RMSE')
    plt.title('Validation RMSE')
    
    plt.subplot(1, 3, 3)
    plt.plot(val_r2s)
    plt.xlabel('Epoch')
    plt.ylabel('R²')
    plt.title('Validation R²')
    
    plt.tight_layout()
    plt.savefig(os.path.join(args.output_dir, 'training_curves.png'))
    plt.close()
    
    # Evaluate models on test data
    logger.info("Evaluating models on test data...")
    
    # 1. Neural Network only (on normalized data)
    test_nn_loss, test_nn_rmse, test_nn_r2, test_targets_scaled, test_nn_preds_scaled = validate(model, test_loader, criterion, device)
    
    # Convert back to original scale for meaningful metrics
    test_targets = target_scaler.inverse_transform(np.array(test_targets_scaled))
    test_nn_preds = target_scaler.inverse_transform(np.array(test_nn_preds_scaled))
    
    # Apply inverse sqrt transformation to growth rate predictions
    test_targets_orig = test_targets.copy()
    test_targets_orig[:, 0] = np.square(test_targets[:, 0])
    
    test_nn_preds_orig = test_nn_preds.copy()
    test_nn_preds_orig[:, 0] = np.square(test_nn_preds[:, 0])
    
    nn_rmse = np.sqrt(mean_squared_error(test_targets_orig, test_nn_preds_orig))
    nn_r2 = r2_score(test_targets_orig, test_nn_preds_orig)
    
    nn_growth_rmse = np.sqrt(mean_squared_error(test_targets_orig[:, 0], test_nn_preds_orig[:, 0]))
    nn_growth_r2 = r2_score(test_targets_orig[:, 0], test_nn_preds_orig[:, 0])
    
    nn_temp_rmse = np.sqrt(mean_squared_error(test_targets_orig[:, 1], test_nn_preds_orig[:, 1]))
    nn_temp_r2 = r2_score(test_targets_orig[:, 1], test_nn_preds_orig[:, 1])
    
    logger.info(f'Neural Network - Overall RMSE: {nn_rmse:.4f}, R2: {nn_r2:.4f}')
    logger.info(f'Growth Rate - RMSE: {nn_growth_rmse:.4f}, R2: {nn_growth_r2:.4f}')
    logger.info(f'Optimal Temperature - RMSE: {nn_temp_rmse:.4f}, R2: {nn_temp_r2:.4f}')
    
    # 2. Random Forest only
    # RF growth rate predictions are in sqrt space, so square them to get original scale
    rf_growth_preds = model.rf_growth.predict(X_test[continuous_cols])
    rf_growth_preds_orig = np.square(rf_growth_preds)
    rf_temp_preds = model.rf_temp.predict(X_test[continuous_cols])
    rf_preds = np.column_stack([rf_growth_preds_orig, rf_temp_preds])
    test_targets_orig = y_test.values
    
    rf_rmse = np.sqrt(mean_squared_error(test_targets_orig, rf_preds))
    rf_r2 = r2_score(test_targets_orig, rf_preds)
    
    rf_growth_rmse = np.sqrt(mean_squared_error(test_targets_orig[:, 0], rf_preds[:, 0]))
    rf_growth_r2 = r2_score(test_targets_orig[:, 0], rf_preds[:, 0])
    
    rf_temp_rmse = np.sqrt(mean_squared_error(test_targets_orig[:, 1], rf_preds[:, 1]))
    rf_temp_r2 = r2_score(test_targets_orig[:, 1], rf_preds[:, 1])
    
    logger.info(f'Random Forest - Overall RMSE: {rf_rmse:.4f}, R2: {rf_r2:.4f}')
    logger.info(f'Growth Rate - RMSE: {rf_growth_rmse:.4f}, R2: {rf_growth_r2:.4f}')
    logger.info(f'Optimal Temperature - RMSE: {rf_temp_rmse:.4f}, R2: {rf_temp_r2:.4f}')
    
    # 3. Hybrid approach
    # Start with even weights
    with torch.no_grad():
        model.ensemble_weights.copy_(torch.tensor([0.5, 0.5], dtype=torch.float32).to(device))
        
    # Make predictions with hybrid model
    hybrid_preds = model.predict(X_test, target_scaler)
    
    hybrid_rmse = np.sqrt(mean_squared_error(test_targets_orig, hybrid_preds))
    hybrid_r2 = r2_score(test_targets_orig, hybrid_preds)
    
    hybrid_growth_rmse = np.sqrt(mean_squared_error(test_targets_orig[:, 0], hybrid_preds[:, 0]))
    hybrid_growth_r2 = r2_score(test_targets_orig[:, 0], hybrid_preds[:, 0])
    
    hybrid_temp_rmse = np.sqrt(mean_squared_error(test_targets_orig[:, 1], hybrid_preds[:, 1]))
    hybrid_temp_r2 = r2_score(test_targets_orig[:, 1], hybrid_preds[:, 1])
    
    logger.info(f'Hybrid (50/50) - Overall RMSE: {hybrid_rmse:.4f}, R2: {hybrid_r2:.4f}')
    logger.info(f'Growth Rate - RMSE: {hybrid_growth_rmse:.4f}, R2: {hybrid_growth_r2:.4f}')
    logger.info(f'Optimal Temperature - RMSE: {hybrid_temp_rmse:.4f}, R2: {hybrid_temp_r2:.4f}')
    
    # 4. Try RF-biased hybrid
    with torch.no_grad():
        model.ensemble_weights.copy_(torch.tensor([0.2, 0.8], dtype=torch.float32).to(device))
        
    # Make predictions with RF-biased hybrid
    rf_biased_preds = model.predict(X_test, target_scaler)
    
    rf_biased_rmse = np.sqrt(mean_squared_error(test_targets_orig, rf_biased_preds))
    rf_biased_r2 = r2_score(test_targets_orig, rf_biased_preds)
    
    rf_biased_growth_rmse = np.sqrt(mean_squared_error(test_targets_orig[:, 0], rf_biased_preds[:, 0]))
    rf_biased_growth_r2 = r2_score(test_targets_orig[:, 0], rf_biased_preds[:, 0])
    
    rf_biased_temp_rmse = np.sqrt(mean_squared_error(test_targets_orig[:, 1], rf_biased_preds[:, 1]))
    rf_biased_temp_r2 = r2_score(test_targets_orig[:, 1], rf_biased_preds[:, 1])
    
    logger.info(f'RF-Biased Hybrid (20/80) - Overall RMSE: {rf_biased_rmse:.4f}, R2: {rf_biased_r2:.4f}')
    logger.info(f'Growth Rate - RMSE: {rf_biased_growth_rmse:.4f}, R2: {rf_biased_growth_r2:.4f}')
    logger.info(f'Optimal Temperature - RMSE: {rf_biased_temp_rmse:.4f}, R2: {rf_biased_temp_r2:.4f}')
    
    # Save all metrics to file
    metrics = {
        'nn_rmse': nn_rmse,
        'nn_r2': nn_r2,
        'nn_growth_rmse': nn_growth_rmse,
        'nn_growth_r2': nn_growth_r2,
        'nn_temp_rmse': nn_temp_rmse,
        'nn_temp_r2': nn_temp_r2,
        'rf_rmse': rf_rmse,
        'rf_r2': rf_r2,
        'rf_growth_rmse': rf_growth_rmse,
        'rf_growth_r2': rf_growth_r2,
        'rf_temp_rmse': rf_temp_rmse,
        'rf_temp_r2': rf_temp_r2,
        'hybrid_rmse': hybrid_rmse,
        'hybrid_r2': hybrid_r2,
        'hybrid_growth_rmse': hybrid_growth_rmse,
        'hybrid_growth_r2': hybrid_growth_r2,
        'hybrid_temp_rmse': hybrid_temp_rmse,
        'hybrid_temp_r2': hybrid_temp_r2,
        'rf_biased_rmse': rf_biased_rmse,
        'rf_biased_r2': rf_biased_r2,
        'rf_biased_growth_rmse': rf_biased_growth_rmse,
        'rf_biased_growth_r2': rf_biased_growth_r2,
        'rf_biased_temp_rmse': rf_biased_temp_rmse,
        'rf_biased_temp_r2': rf_biased_temp_r2
    }
    
    with open(os.path.join(args.output_dir, 'metrics.txt'), 'w') as f:
        for key, value in metrics.items():
            f.write(f'{key}: {value:.4f}\n')
    
    # Determine the best model
    models = {
        'Neural Network': nn_r2,
        'Random Forest': rf_r2,
        'Hybrid (50/50)': hybrid_r2,
        'RF-Biased Hybrid (20/80)': rf_biased_r2
    }
    
    best_model = max(models.items(), key=lambda x: x[1])
    logger.info(f"Best model: {best_model[0]} with R² = {best_model[1]:.4f}")
    
    # Save best model configuration
    best_weights = None
    if best_model[0] == 'Neural Network':
        best_weights = [1.0, 0.0]
    elif best_model[0] == 'Random Forest':
        best_weights = [0.0, 1.0]
    elif best_model[0] == 'Hybrid (50/50)':
        best_weights = [0.5, 0.5]
    elif best_model[0] == 'RF-Biased Hybrid (20/80)':
        best_weights = [0.2, 0.8]
    
    # Update model weights to the best configuration
    with torch.no_grad():
        model.ensemble_weights.copy_(torch.tensor(best_weights, dtype=torch.float32).to(device))
    
    # Save the final model with best weights
    joblib.dump({
        'nn_state_dict': model.tabular_model.state_dict(),
        'feature_scaler': feature_scaler,
        'target_scaler': target_scaler,
        'rf_growth': model.rf_growth,
        'rf_temp': model.rf_temp,
        'ensemble_weights': best_weights,
        'categorical_cols': categorical_cols,
        'continuous_cols': continuous_cols,
        'model_type': best_model[0],
        'sqrt_transform_growth_rate': True  # Flag indicating we used sqrt transform
    }, os.path.join(args.output_dir, 'final_model.joblib'))
    
    # Plot predictions vs actual values for best model
    plt.figure(figsize=(12, 5))
    
    # Use the best model's predictions
    if best_model[0] == 'Neural Network':
        best_preds = test_nn_preds
    elif best_model[0] == 'Random Forest':
        best_preds = rf_preds
    elif best_model[0] == 'Hybrid (50/50)':
        best_preds = hybrid_preds
    else:  # RF-Biased
        best_preds = rf_biased_preds
    
    plt.subplot(1, 2, 1)
    plt.scatter(test_targets_orig[:, 0], best_preds[:, 0], alpha=0.5)
    plt.plot([min(test_targets_orig[:, 0]), max(test_targets_orig[:, 0])], 
             [min(test_targets_orig[:, 0]), max(test_targets_orig[:, 0])], 'r--')
    plt.xlabel('Actual Growth Rate')
    plt.ylabel('Predicted Growth Rate')
    
    # Use direct r² value instead of looking it up in the metrics dictionary
    r2_growth = r2_score(test_targets_orig[:, 0], best_preds[:, 0])
    plt.title(f'Growth Rate Prediction (R² = {r2_growth:.4f})')
    
    plt.subplot(1, 2, 2)
    plt.scatter(test_targets_orig[:, 1], best_preds[:, 1], alpha=0.5)
    plt.plot([min(test_targets_orig[:, 1]), max(test_targets_orig[:, 1])], 
             [min(test_targets_orig[:, 1]), max(test_targets_orig[:, 1])], 'r--')
    plt.xlabel('Actual Optimal Temperature')
    plt.ylabel('Predicted Optimal Temperature')
    
    # Use direct r² value instead of looking it up in the metrics dictionary
    r2_temp = r2_score(test_targets_orig[:, 1], best_preds[:, 1])
    plt.title(f'Optimal Temperature Prediction (R² = {r2_temp:.4f})')
    
    plt.tight_layout()
    plt.savefig(os.path.join(args.output_dir, 'best_model_predictions.png'))
    
    logger.info(f"Training and evaluation completed. All results saved to {args.output_dir}")
    
    # Final recommendation
    logger.info(f"✅ Final recommendation: Use the {best_model[0]} for production.")
    logger.info(f"   Overall R² = {best_model[1]:.4f}")
    
    if best_model[1] < 0.5:
        logger.info("⚠️ Note: Model performance is still not optimal. Consider:")
        logger.info("   1. Collecting more data")
        logger.info("   2. Engineering more relevant features")
        logger.info("   3. Trying separate models for growth rate and temperature")

if __name__ == '__main__':
    main() 