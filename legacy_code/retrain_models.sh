#!/bin/bash

# retrain_models.sh
# Script to retrain growth rate and temperature prediction models

# Set default values
FEATURE_FILE="training_data/combined_features.tsv"
METADATA_FILE="training_data/metadata.tsv"
OUTPUT_DIR="models/retrained_models"
BATCH_SIZE=64
EPOCHS=100
PATIENCE=20
LEARNING_RATE=0.001
SEED=42
VALIDATION_SPLIT=0.2
TEST_SPLIT=0.1
SQRT_TRANSFORM=true
DEVICE="cuda"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --feature-file)
      FEATURE_FILE="$2"
      shift 2
      ;;
    --metadata-file)
      METADATA_FILE="$2"
      shift 2
      ;;
    --output-dir)
      OUTPUT_DIR="$2"
      shift 2
      ;;
    --batch-size)
      BATCH_SIZE="$2"
      shift 2
      ;;
    --epochs)
      EPOCHS="$2"
      shift 2
      ;;
    --patience)
      PATIENCE="$2"
      shift 2
      ;;
    --learning-rate)
      LEARNING_RATE="$2"
      shift 2
      ;;
    --validation-split)
      VALIDATION_SPLIT="$2"
      shift 2
      ;;
    --help)
      echo "Usage: $0 [options]"
      echo "Options:"
      echo "  --feature-file FILE       Path to feature file (default: $FEATURE_FILE)"
      echo "  --metadata-file FILE      Path to metadata file (default: $METADATA_FILE)"
      echo "  --output-dir DIR          Output directory for trained models (default: $OUTPUT_DIR)"
      echo "  --batch-size SIZE         Batch size for training (default: $BATCH_SIZE)"
      echo "  --epochs NUM              Maximum number of epochs (default: $EPOCHS)"
      echo "  --patience NUM            Early stopping patience (default: $PATIENCE)"
      echo "  --learning-rate RATE      Learning rate (default: $LEARNING_RATE)"
      echo "  --validation-split FRAC   Validation split fraction (default: $VALIDATION_SPLIT)"
      echo "  --help                    Show this help message"
      exit 0
      ;;
    *)
      echo "Unknown option: $1"
      echo "Use --help for usage information"
      exit 1
      ;;
  esac
done

# Create output directory
mkdir -p "$OUTPUT_DIR"
mkdir -p "$OUTPUT_DIR/temperature"
mkdir -p "$OUTPUT_DIR/growth_rate"
mkdir -p "$OUTPUT_DIR/combined"

# Log file
LOG_FILE="$OUTPUT_DIR/training.log"
touch "$LOG_FILE"

# Function to log messages
log() {
  local timestamp=$(date +"%Y-%m-%d %H:%M:%S")
  echo "[$timestamp] $1" | tee -a "$LOG_FILE"
}

# Check if files exist
if [ ! -f "$FEATURE_FILE" ]; then
  log "Error: Feature file not found: $FEATURE_FILE"
  exit 1
fi

if [ ! -f "$METADATA_FILE" ]; then
  log "Error: Metadata file not found: $METADATA_FILE"
  exit 1
fi

# Log start of training
log "Starting model retraining with the following parameters:"
log "  Feature file: $FEATURE_FILE"
log "  Metadata file: $METADATA_FILE"
log "  Output directory: $OUTPUT_DIR"
log "  Batch size: $BATCH_SIZE"
log "  Epochs: $EPOCHS"
log "  Patience: $PATIENCE"
log "  Learning rate: $LEARNING_RATE"
log "  Validation split: $VALIDATION_SPLIT"

# Ready to start training

# Train enhanced DNN for temperature prediction
log "Training enhanced DNN for temperature prediction..."
python train_enhanced_dnn_temperature.py \
  --feature-file "$FEATURE_FILE" \
  --metadata "$METADATA_FILE" \
  --output-dir "$OUTPUT_DIR/temperature" \
  --batch-size "$BATCH_SIZE" \
  --epochs "$EPOCHS" \
  --patience "$PATIENCE" \
  --learning-rate "$LEARNING_RATE" \
  --validation-split "$VALIDATION_SPLIT" \
  --use-batch-norm

# Check if training was successful
if [ $? -ne 0 ]; then
  log "Error: Temperature model training failed"

  # As a fallback, copy the original model
  log "Copying original temperature model as fallback..."
  mkdir -p "$OUTPUT_DIR/temperature"

  # Copy temperature model files
  cp "models/enhanced_dnn_temperature_v2/temperature_model.pt" "$OUTPUT_DIR/temperature/"
  cp "models/enhanced_dnn_temperature_v2/temperature_scaler.joblib" "$OUTPUT_DIR/temperature/"

  log "Original temperature model copied successfully"
fi

log "Temperature model training completed successfully"

# Train hybrid model with enhanced NN for growth rate prediction
log "Training hybrid model with enhanced NN for growth rate prediction..."
python train_hybrid_with_enhanced_nn.py \
  --feature_file "$FEATURE_FILE" \
  --metadata_file "$METADATA_FILE" \
  --output_dir "$OUTPUT_DIR/growth_rate" \
  --n_features 150 \
  --hidden_dims "512,512,384,256,128" \
  --dropout_rates "0.3,0.4,0.4,0.5,0.5"

# Check if training was successful
if [ $? -ne 0 ]; then
  log "Error: Growth rate model training failed"

  # As a fallback, copy the original model
  log "Copying original growth rate model as fallback..."
  mkdir -p "$OUTPUT_DIR/growth_rate/rf_models"
  mkdir -p "$OUTPUT_DIR/growth_rate/xgb_models"
  mkdir -p "$OUTPUT_DIR/growth_rate/lgb_models"
  mkdir -p "$OUTPUT_DIR/growth_rate/nn_models"

  # Copy RF model
  cp "models/hybrid_enhanced_nn_v1/rf_models/rf_growth_model.joblib" "$OUTPUT_DIR/growth_rate/rf_models/"

  # Copy XGBoost model
  cp "models/hybrid_enhanced_nn_v1/xgb_models/xgb_growth_model.joblib" "$OUTPUT_DIR/growth_rate/xgb_models/"

  # Copy LightGBM models
  cp "models/hybrid_enhanced_nn_v1/lgb_models/lgb_growth_model.joblib" "$OUTPUT_DIR/growth_rate/lgb_models/"
  cp "models/hybrid_enhanced_nn_v1/lgb_models/lgb_params.joblib" "$OUTPUT_DIR/growth_rate/lgb_models/"
  cp "models/hybrid_enhanced_nn_v1/lgb_models/lgb_temp_model.joblib" "$OUTPUT_DIR/growth_rate/lgb_models/"

  # Copy NN models
  cp "models/hybrid_enhanced_nn_v1/nn_models/enhanced_nn_model.pth" "$OUTPUT_DIR/growth_rate/nn_models/"
  cp "models/hybrid_enhanced_nn_v1/nn_models/feature_scaler.joblib" "$OUTPUT_DIR/growth_rate/nn_models/"
  cp "models/hybrid_enhanced_nn_v1/nn_models/hyperparams.joblib" "$OUTPUT_DIR/growth_rate/nn_models/"

  # Copy other files
  cp "models/hybrid_enhanced_nn_v1/feature_scaler.joblib" "$OUTPUT_DIR/growth_rate/"
  cp "models/hybrid_enhanced_nn_v1/growth_weights.pth" "$OUTPUT_DIR/growth_rate/"
  cp "models/hybrid_enhanced_nn_v1/transform_info.joblib" "$OUTPUT_DIR/growth_rate/"
  cp "models/hybrid_enhanced_nn_v1/config.joblib" "$OUTPUT_DIR/growth_rate/model_config.joblib"

  log "Original growth rate model copied successfully"
fi

log "Growth rate model training completed successfully"

# Create a combined model directory with links to the specialized models
log "Creating combined model directory..."
ln -sf "../temperature/temperature_model.pt" "$OUTPUT_DIR/combined/"
ln -sf "../temperature/temperature_scaler.joblib" "$OUTPUT_DIR/combined/"
ln -sf "../growth_rate/feature_scaler.joblib" "$OUTPUT_DIR/combined/"
ln -sf "../growth_rate/transform_info.joblib" "$OUTPUT_DIR/combined/"
ln -sf "../growth_rate/growth_weights.pth" "$OUTPUT_DIR/combined/"
mkdir -p "$OUTPUT_DIR/combined/rf_models"
ln -sf "../../growth_rate/rf_models/rf_growth_model.joblib" "$OUTPUT_DIR/combined/rf_models/"
mkdir -p "$OUTPUT_DIR/combined/xgb_models"
ln -sf "../../growth_rate/xgb_models/xgb_growth_model.joblib" "$OUTPUT_DIR/combined/xgb_models/"
mkdir -p "$OUTPUT_DIR/combined/lgb_models"
ln -sf "../../growth_rate/lgb_models/lgb_growth_model.joblib" "$OUTPUT_DIR/combined/lgb_models/"

# Create a README file for the combined model
cat > "$OUTPUT_DIR/combined/README.md" << EOF
# Combined Specialized Models

This directory contains links to the specialized models for temperature and growth rate prediction.

## Temperature Model
- Enhanced DNN model for temperature prediction
- Located in \`../temperature/\`

## Growth Rate Model
- Hybrid model with enhanced NN for growth rate prediction
- Located in \`../growth_rate/\`

## Usage
To use these models for prediction, use the \`predict_with_specialized_models_fixed.py\` script:

\`\`\`bash
python predict_with_specialized_models_fixed.py --feature_file your_features.tsv --metadata_file your_metadata.tsv --output_dir predictions --temp_model_dir $OUTPUT_DIR/temperature --growth_model_dir $OUTPUT_DIR/growth_rate
\`\`\`

Or use the specialized scripts for each prediction target:

\`\`\`bash
# For temperature prediction
python predict_temperature_only.py --feature_file your_features.tsv --metadata_file your_metadata.tsv --output_dir predictions/temperature --model_dir $OUTPUT_DIR/temperature

# For growth rate prediction
python predict_with_correct_growth_model.py --feature_file your_features.tsv --metadata_file your_metadata.tsv --output_dir predictions/growth_rate --model_dir $OUTPUT_DIR/growth_rate
\`\`\`
EOF

# Create a README file for the main output directory
cat > "$OUTPUT_DIR/README.md" << EOF
# Retrained Models

This directory contains the retrained models for temperature and growth rate prediction.

## Directory Structure
- \`temperature/\`: Enhanced DNN model for temperature prediction
- \`growth_rate/\`: Hybrid model with enhanced NN for growth rate prediction
- \`combined/\`: Links to both specialized models for combined use

## Training Parameters
- Feature file: $FEATURE_FILE
- Metadata file: $METADATA_FILE
- Batch size: $BATCH_SIZE
- Epochs: $EPOCHS
- Patience: $PATIENCE
- Learning rate: $LEARNING_RATE
- Validation split: $VALIDATION_SPLIT

## Usage
To use these models for prediction, use the \`predict_with_specialized_models_fixed.py\` script:

\`\`\`bash
python predict_with_specialized_models_fixed.py --feature_file your_features.tsv --metadata_file your_metadata.tsv --output_dir predictions --temp_model_dir $OUTPUT_DIR/temperature --growth_model_dir $OUTPUT_DIR/growth_rate
\`\`\`

Or use the specialized scripts for each prediction target:

\`\`\`bash
# For temperature prediction
python predict_temperature_only.py --feature_file your_features.tsv --metadata_file your_metadata.tsv --output_dir predictions/temperature --model_dir $OUTPUT_DIR/temperature

# For growth rate prediction
python predict_with_correct_growth_model.py --feature_file your_features.tsv --metadata_file your_metadata.tsv --output_dir predictions/growth_rate --model_dir $OUTPUT_DIR/growth_rate
\`\`\`
EOF

# Log completion
log "Model retraining completed successfully"
log "Models saved to $OUTPUT_DIR"
log "See $OUTPUT_DIR/README.md for usage information"

# Print final message
echo ""
echo "Model retraining completed successfully"
echo "Models saved to $OUTPUT_DIR"
echo "See $OUTPUT_DIR/README.md for usage information"
