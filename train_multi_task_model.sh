#!/bin/bash

# DeepMu 多任务训练脚本 (Shell版本)
# 
# 这个脚本用于训练同时预测生长速率和最适温度的多任务模型
# 
# 作者: DeepMu 开发团队
# 版本: 2.0.0
# 日期: 2025-06-29

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_debug() {
    if [[ "$VERBOSE" == "true" ]]; then
        echo -e "${BLUE}[DEBUG]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
    fi
}

# 显示帮助信息
show_help() {
    cat << EOF
DeepMu 多任务训练脚本

用法: $0 [选项]

选项:
    -h, --help              显示此帮助信息
    -c, --config FILE       配置文件路径 (默认: configs/multi_task_config.json)
    -o, --output-dir DIR    输出目录 (默认: models/deepmu_multi_task)
    -g, --gpu ID            GPU设备ID (默认: 0)
    -v, --verbose           详细输出
    -e, --epochs NUM        训练轮数 (默认: 300)
    -b, --batch-size NUM    批次大小 (默认: 128)
    -l, --learning-rate NUM 学习率 (默认: 0.001)
    --growth-weight NUM     生长速率任务权重 (默认: 1.0)
    --temp-weight NUM       温度任务权重 (默认: 1.0)
    --dry-run              仅显示配置，不执行训练

示例:
    $0 --config my_config.json --output-dir my_models --verbose
    $0 --epochs 500 --batch-size 256 --learning-rate 0.0005
    $0 --growth-weight 1.5 --temp-weight 0.8 --gpu 1

EOF
}

# 默认参数
CONFIG_FILE="configs/multi_task_config.json"
OUTPUT_DIR="models/deepmu_multi_task"
GPU_ID=0
VERBOSE=false
EPOCHS=300
BATCH_SIZE=128
LEARNING_RATE=0.001
GROWTH_WEIGHT=1.0
TEMP_WEIGHT=1.0
DRY_RUN=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -c|--config)
            CONFIG_FILE="$2"
            shift 2
            ;;
        -o|--output-dir)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        -g|--gpu)
            GPU_ID="$2"
            shift 2
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -e|--epochs)
            EPOCHS="$2"
            shift 2
            ;;
        -b|--batch-size)
            BATCH_SIZE="$2"
            shift 2
            ;;
        -l|--learning-rate)
            LEARNING_RATE="$2"
            shift 2
            ;;
        --growth-weight)
            GROWTH_WEIGHT="$2"
            shift 2
            ;;
        --temp-weight)
            TEMP_WEIGHT="$2"
            shift 2
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查Python环境
check_python_env() {
    log_info "检查Python环境..."
    
    if ! command -v python3 &> /dev/null; then
        log_error "Python3 未找到，请安装Python3"
        exit 1
    fi
    
    # 检查必要的Python包
    local required_packages=("torch" "numpy" "pandas" "sklearn" "matplotlib" "seaborn")
    for package in "${required_packages[@]}"; do
        if ! python3 -c "import $package" &> /dev/null; then
            log_error "Python包 '$package' 未安装"
            if [[ "$package" == "sklearn" ]]; then
                log_info "请运行: pip install scikit-learn"
            else
                log_info "请运行: pip install $package"
            fi
            exit 1
        fi
    done
    
    log_info "Python环境检查通过"
}

# 检查数据文件
check_data_files() {
    log_info "检查数据文件..."
    
    local feature_file="training_data/combined_features.tsv"
    local metadata_file="training_data/metadata.tsv"
    
    if [[ ! -f "$feature_file" ]]; then
        log_error "特征文件不存在: $feature_file"
        exit 1
    fi
    
    if [[ ! -f "$metadata_file" ]]; then
        log_error "元数据文件不存在: $metadata_file"
        exit 1
    fi
    
    log_info "数据文件检查通过"
}

# 创建配置文件
create_config() {
    log_info "创建配置文件..."
    
    # 创建配置目录
    mkdir -p "$(dirname "$CONFIG_FILE")"
    
    # 生成配置文件
    cat > "$CONFIG_FILE" << EOF
{
    "data": {
        "feature_file": "training_data/combined_features.tsv",
        "metadata_file": "training_data/metadata.tsv",
        "test_size": 0.2,
        "validation_size": 0.2,
        "random_state": 42
    },
    "model": {
        "shared_dims": [512, 256],
        "growth_dims": [128, 64],
        "temp_dims": [128, 64],
        "dropout_rate": 0.3,
        "use_batch_norm": true
    },
    "training": {
        "batch_size": $BATCH_SIZE,
        "learning_rate": $LEARNING_RATE,
        "epochs": $EPOCHS,
        "patience": 30,
        "weight_decay": 1e-4,
        "gradient_clip": 1.0
    },
    "task_weights": {
        "growth": $GROWTH_WEIGHT,
        "temperature": $TEMP_WEIGHT
    }
}
EOF
    
    log_info "配置文件已创建: $CONFIG_FILE"
}

# 显示配置信息
show_config() {
    log_info "训练配置:"
    echo "  配置文件: $CONFIG_FILE"
    echo "  输出目录: $OUTPUT_DIR"
    echo "  GPU设备: $GPU_ID"
    echo "  训练轮数: $EPOCHS"
    echo "  批次大小: $BATCH_SIZE"
    echo "  学习率: $LEARNING_RATE"
    echo "  生长速率权重: $GROWTH_WEIGHT"
    echo "  温度权重: $TEMP_WEIGHT"
    echo "  详细输出: $VERBOSE"
}

# 检查GPU可用性
check_gpu() {
    log_info "检查GPU可用性..."
    
    if python3 -c "import torch; print('CUDA available:', torch.cuda.is_available())" | grep -q "True"; then
        local gpu_count=$(python3 -c "import torch; print(torch.cuda.device_count())")
        log_info "检测到 $gpu_count 个GPU设备"
        
        if [[ $GPU_ID -ge $gpu_count ]]; then
            log_warn "指定的GPU ID ($GPU_ID) 超出范围，使用GPU 0"
            GPU_ID=0
        fi
        
        local gpu_name=$(python3 -c "import torch; print(torch.cuda.get_device_name($GPU_ID))" 2>/dev/null || echo "Unknown")
        log_info "使用GPU $GPU_ID: $gpu_name"
    else
        log_warn "CUDA不可用，将使用CPU训练"
        GPU_ID=-1
    fi
}

# 创建输出目录
setup_output_dir() {
    log_info "设置输出目录..."
    
    mkdir -p "$OUTPUT_DIR"
    mkdir -p "$OUTPUT_DIR/plots"
    mkdir -p "$OUTPUT_DIR/logs"
    
    # 创建训练信息文件
    cat > "$OUTPUT_DIR/training_info.txt" << EOF
DeepMu 多任务训练信息
==================

开始时间: $(date)
配置文件: $CONFIG_FILE
输出目录: $OUTPUT_DIR
GPU设备: $GPU_ID
训练轮数: $EPOCHS
批次大小: $BATCH_SIZE
学习率: $LEARNING_RATE
生长速率权重: $GROWTH_WEIGHT
温度权重: $TEMP_WEIGHT

EOF
    
    log_info "输出目录已设置: $OUTPUT_DIR"
}

# 运行训练
run_training() {
    log_info "开始多任务模型训练..."
    
    # 构建Python命令
    local python_cmd="python3 train_multi_task_model.py"
    python_cmd="$python_cmd --config $CONFIG_FILE"
    python_cmd="$python_cmd --output-dir $OUTPUT_DIR"
    python_cmd="$python_cmd --gpu $GPU_ID"
    
    if [[ "$VERBOSE" == "true" ]]; then
        python_cmd="$python_cmd --verbose"
    fi
    
    log_debug "执行命令: $python_cmd"
    
    # 执行训练
    if eval "$python_cmd"; then
        log_info "多任务模型训练完成！"
        
        # 显示结果摘要
        if [[ -f "$OUTPUT_DIR/training_history.json" ]]; then
            log_info "训练结果摘要:"
            python3 -c "
import json
with open('$OUTPUT_DIR/training_history.json', 'r') as f:
    history = json.load(f)
if 'test_metrics' in history:
    growth_r2 = history['test_metrics']['growth']['r2']
    temp_r2 = history['test_metrics']['temperature']['r2']
    print(f'  生长速率 R²: {growth_r2:.4f}')
    print(f'  温度预测 R²: {temp_r2:.4f}')
"
        fi
        
        # 更新训练信息
        echo "结束时间: $(date)" >> "$OUTPUT_DIR/training_info.txt"
        echo "训练状态: 成功完成" >> "$OUTPUT_DIR/training_info.txt"
        
    else
        log_error "训练失败！"
        echo "结束时间: $(date)" >> "$OUTPUT_DIR/training_info.txt"
        echo "训练状态: 失败" >> "$OUTPUT_DIR/training_info.txt"
        exit 1
    fi
}

# 主函数
main() {
    log_info "开始DeepMu多任务训练流程"
    
    # 显示配置
    show_config
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "干运行模式，仅显示配置"
        exit 0
    fi
    
    # 执行检查和准备
    check_python_env
    check_data_files
    check_gpu
    setup_output_dir
    
    # 创建配置文件（如果不存在）
    if [[ ! -f "$CONFIG_FILE" ]]; then
        create_config
    else
        log_info "使用现有配置文件: $CONFIG_FILE"
    fi
    
    # 运行训练
    run_training
    
    log_info "多任务训练流程完成！"
    log_info "模型文件: $OUTPUT_DIR/multi_task_model.pt"
    log_info "训练日志: $OUTPUT_DIR/training.log"
    log_info "配置文件: $OUTPUT_DIR/config.json"
}

# 捕获中断信号
trap 'log_error "训练被中断"; exit 1' INT TERM

# 执行主函数
main "$@"
