"""
DeepMu 包安装配置脚本

DeepMu 是一个基于深度学习的微生物生长速率和最适生长温度预测工具。
该工具使用多分支神经网络架构，整合了密码子使用模式、系统发育信息、
序列特征和位置感知注意力机制，为微生物生长参数预测提供高精度解决方案。

主要功能：
- 微生物生长速率预测
- 最适生长温度预测
- 群落水平生长预测
- 多任务学习支持
- 特征提取和预处理
- 命令行和Python API接口

作者：DeepMu 开发团队
版本：1.1.1
许可证：MIT License
"""

from setuptools import setup, find_packages

# 读取长描述（如果存在README文件）
try:
    with open("README.md", "r", encoding="utf-8") as fh:
        long_description = fh.read()
except FileNotFoundError:
    long_description = "DeepMu: 基于深度学习的微生物生长速率和温度预测工具"

# 读取依赖项（如果存在requirements.txt文件）
try:
    with open("requirements.txt", "r", encoding="utf-8") as fh:
        requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]
except FileNotFoundError:
    # 如果没有requirements.txt，使用基本依赖项
    requirements = [
        "torch>=1.7.0",
        "numpy>=1.19.0",
        "biopython>=1.78",
        "scipy>=1.5.0",
        "pandas>=1.0.0",
        "scikit-learn>=0.24.0",
        "tqdm>=4.67.0",
        "matplotlib>=3.3.0",
        "seaborn>=0.12.0",
        "joblib>=0.17.0",
    ]

setup(
    name="DeepMu",
    version="1.1.1",
    description="基于深度学习的微生物生长速率和最适温度预测工具",
    long_description=long_description,
    long_description_content_type="text/markdown",
    author="DeepMu 开发团队",
    author_email="<EMAIL>",
    url="https://github.com/username/DeepMu",

    # 包配置
    packages=find_packages(),
    py_modules=['deepmu_cli'],  # 包含命令行接口模块
    package_dir={'': '.'},

    # 命令行入口点配置
    entry_points={
        'console_scripts': [
            'DeepMu = deepmu_cli:main',  # 主命令行接口
            'deepmu = deepmu_cli:main',  # 简化命令别名
        ]
    },

    # 依赖项配置
    install_requires=requirements,

    # 可选依赖项（用于特定功能）
    extras_require={
        'gpu': ['torch[cuda]'],  # GPU加速支持
        'dev': [  # 开发环境依赖
            'pytest>=7.0.0',
            'pytest-cov>=3.0.0',
            'black>=22.0.0',
            'mypy>=0.900',
        ],
        'ml': [  # 机器学习增强功能
            'lightgbm>=3.2.0',
            'xgboost>=1.3.0',
            'catboost>=1.0.0',
        ]
    },

    # 包分类信息
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Science/Research",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.7",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Topic :: Scientific/Engineering :: Bio-Informatics",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Operating System :: OS Independent",
    ],

    # Python版本要求
    python_requires=">=3.7",

    # 包含的数据文件
    include_package_data=True,

    # 项目关键词
    keywords="microbiology, growth rate, temperature, deep learning, bioinformatics",

    # 项目状态
    project_urls={
        "Bug Reports": "https://github.com/username/DeepMu/issues",
        "Source": "https://github.com/username/DeepMu",
        "Documentation": "https://github.com/username/DeepMu/wiki",
    },
)
