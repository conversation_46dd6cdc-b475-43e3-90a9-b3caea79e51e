#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
DeepMu 多任务训练脚本 - 同时预测生长速率和最适温度

这个脚本实现了一个多任务学习模型，能够同时预测微生物的生长速率和最适温度。
采用共享特征提取器和任务特定的输出头的架构。

特点：
1. 多任务学习架构
2. 任务权重平衡
3. 共享表示学习
4. 任务特定的损失函数
5. 联合优化策略

作者: DeepMu 开发团队
版本: 2.0.0
日期: 2025-06-29
"""

import os
import sys
import json
import argparse
import logging
import warnings
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union
from datetime import datetime

import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
from sklearn.model_selection import KFold, train_test_split
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
import matplotlib.pyplot as plt
import seaborn as sns

# 忽略警告
warnings.filterwarnings('ignore')

class MultiTaskGrowthModel(nn.Module):
    """多任务生长预测模型"""
    
    def __init__(self, input_dim: int, shared_dims: List[int], 
                 growth_dims: List[int], temp_dims: List[int],
                 dropout_rate: float = 0.3, use_batch_norm: bool = True):
        """
        初始化多任务模型
        
        Args:
            input_dim: 输入特征维度
            shared_dims: 共享层维度列表
            growth_dims: 生长速率任务特定层维度
            temp_dims: 温度任务特定层维度
            dropout_rate: Dropout率
            use_batch_norm: 是否使用批量归一化
        """
        super().__init__()
        
        # 共享特征提取器
        self.shared_layers = nn.ModuleList()
        dims = [input_dim] + shared_dims
        
        for i in range(len(dims) - 1):
            self.shared_layers.append(nn.Linear(dims[i], dims[i + 1]))
            if use_batch_norm:
                self.shared_layers.append(nn.BatchNorm1d(dims[i + 1]))
            self.shared_layers.append(nn.GELU())
            self.shared_layers.append(nn.Dropout(dropout_rate))
        
        # 生长速率任务头
        self.growth_head = nn.ModuleList()
        growth_input_dim = shared_dims[-1]
        growth_full_dims = [growth_input_dim] + growth_dims + [1]
        
        for i in range(len(growth_full_dims) - 1):
            self.growth_head.append(nn.Linear(growth_full_dims[i], growth_full_dims[i + 1]))
            if i < len(growth_full_dims) - 2:  # 不在最后一层添加激活函数
                if use_batch_norm:
                    self.growth_head.append(nn.BatchNorm1d(growth_full_dims[i + 1]))
                self.growth_head.append(nn.GELU())
                self.growth_head.append(nn.Dropout(dropout_rate))
        
        # 温度任务头
        self.temp_head = nn.ModuleList()
        temp_input_dim = shared_dims[-1]
        temp_full_dims = [temp_input_dim] + temp_dims + [1]
        
        for i in range(len(temp_full_dims) - 1):
            self.temp_head.append(nn.Linear(temp_full_dims[i], temp_full_dims[i + 1]))
            if i < len(temp_full_dims) - 2:  # 不在最后一层添加激活函数
                if use_batch_norm:
                    self.temp_head.append(nn.BatchNorm1d(temp_full_dims[i + 1]))
                self.temp_head.append(nn.GELU())
                self.temp_head.append(nn.Dropout(dropout_rate))
    
    def forward(self, x):
        """前向传播"""
        # 共享特征提取
        shared_features = x
        for layer in self.shared_layers:
            shared_features = layer(shared_features)
        
        # 生长速率预测
        growth_output = shared_features
        for layer in self.growth_head:
            growth_output = layer(growth_output)
        
        # 温度预测
        temp_output = shared_features
        for layer in self.temp_head:
            temp_output = layer(temp_output)
        
        return growth_output, temp_output

class MultiTaskTrainer:
    """多任务训练器"""
    
    def __init__(self, config: Dict, device: torch.device):
        """
        初始化多任务训练器
        
        Args:
            config: 训练配置
            device: 计算设备
        """
        self.config = config
        self.device = device
        self.logger = logging.getLogger(__name__)
        
        # 任务权重
        self.growth_weight = config.get('task_weights', {}).get('growth', 1.0)
        self.temp_weight = config.get('task_weights', {}).get('temperature', 1.0)
    
    def load_data(self) -> Tuple[np.ndarray, np.ndarray, np.ndarray, List[str]]:
        """
        加载多任务数据
        
        Returns:
            特征、生长速率、温度、样本ID
        """
        self.logger.info("加载多任务训练数据...")
        
        # 加载特征数据
        feature_file = self.config['data']['feature_file']
        features_df = pd.read_csv(feature_file, sep='\t', index_col=0)
        
        # 加载元数据
        metadata_file = self.config['data']['metadata_file']
        metadata_df = pd.read_csv(metadata_file, sep='\t', index_col=0)
        
        # 获取共同样本
        common_indices = features_df.index.intersection(metadata_df.index)
        features_df = features_df.loc[common_indices]
        metadata_df = metadata_df.loc[common_indices]
        
        # 提取目标变量
        growth_rates = metadata_df['growth_rate'].values
        temperatures = metadata_df['optimal_temperature'].values
        
        # 移除缺失值
        valid_mask = ~(np.isnan(growth_rates) | np.isnan(temperatures))
        
        features = features_df[valid_mask].fillna(0).values
        growth_rates = growth_rates[valid_mask]
        temperatures = temperatures[valid_mask]
        sample_ids = features_df[valid_mask].index.tolist()
        
        self.logger.info(f"有效样本数: {len(sample_ids)}")
        self.logger.info(f"特征维度: {features.shape[1]}")
        self.logger.info(f"生长速率范围: [{growth_rates.min():.4f}, {growth_rates.max():.4f}]")
        self.logger.info(f"温度范围: [{temperatures.min():.2f}, {temperatures.max():.2f}]")
        
        return features, growth_rates, temperatures, sample_ids
    
    def create_model(self, input_dim: int) -> MultiTaskGrowthModel:
        """创建多任务模型"""
        model_config = self.config['model']
        
        model = MultiTaskGrowthModel(
            input_dim=input_dim,
            shared_dims=model_config['shared_dims'],
            growth_dims=model_config['growth_dims'],
            temp_dims=model_config['temp_dims'],
            dropout_rate=model_config['dropout_rate'],
            use_batch_norm=model_config['use_batch_norm']
        )
        
        return model.to(self.device)
    
    def train(self, features: np.ndarray, growth_rates: np.ndarray, 
              temperatures: np.ndarray, sample_ids: List[str]) -> Tuple[MultiTaskGrowthModel, Dict]:
        """
        训练多任务模型
        
        Args:
            features: 特征数据
            growth_rates: 生长速率
            temperatures: 温度
            sample_ids: 样本ID
            
        Returns:
            训练好的模型和训练历史
        """
        self.logger.info("开始多任务模型训练...")
        
        # 数据标准化
        scaler = RobustScaler()
        features_scaled = scaler.fit_transform(features)
        
        # 目标变量标准化
        growth_scaler = StandardScaler()
        temp_scaler = StandardScaler()
        
        growth_scaled = growth_scaler.fit_transform(growth_rates.reshape(-1, 1)).flatten()
        temp_scaled = temp_scaler.fit_transform(temperatures.reshape(-1, 1)).flatten()
        
        # 数据分割
        X_train, X_test, y_growth_train, y_growth_test, y_temp_train, y_temp_test = train_test_split(
            features_scaled, growth_scaled, temp_scaled,
            test_size=self.config['data']['test_size'],
            random_state=self.config['data']['random_state']
        )
        
        X_train, X_val, y_growth_train, y_growth_val, y_temp_train, y_temp_val = train_test_split(
            X_train, y_growth_train, y_temp_train,
            test_size=self.config['data']['validation_size'] / (1 - self.config['data']['test_size']),
            random_state=self.config['data']['random_state']
        )
        
        self.logger.info(f"数据分割: 训练={len(X_train)}, 验证={len(X_val)}, 测试={len(X_test)}")
        
        # 创建模型
        model = self.create_model(features_scaled.shape[1])
        
        # 训练模型
        history = self._train_epoch_loop(
            model, X_train, y_growth_train, y_temp_train,
            X_val, y_growth_val, y_temp_val
        )
        
        # 测试评估
        test_metrics = self._evaluate_model(
            model, X_test, y_growth_test, y_temp_test,
            growth_scaler, temp_scaler
        )
        
        history['test_metrics'] = test_metrics
        history['scalers'] = {
            'feature_scaler': scaler,
            'growth_scaler': growth_scaler,
            'temp_scaler': temp_scaler
        }
        
        return model, history
    
    def _train_epoch_loop(self, model: MultiTaskGrowthModel, 
                         X_train: np.ndarray, y_growth_train: np.ndarray, y_temp_train: np.ndarray,
                         X_val: np.ndarray, y_growth_val: np.ndarray, y_temp_val: np.ndarray) -> Dict:
        """训练循环"""
        train_config = self.config['training']
        
        # 创建数据加载器
        train_dataset = TensorDataset(
            torch.FloatTensor(X_train),
            torch.FloatTensor(y_growth_train).unsqueeze(1),
            torch.FloatTensor(y_temp_train).unsqueeze(1)
        )
        train_loader = DataLoader(train_dataset, batch_size=train_config['batch_size'], shuffle=True)
        
        val_dataset = TensorDataset(
            torch.FloatTensor(X_val),
            torch.FloatTensor(y_growth_val).unsqueeze(1),
            torch.FloatTensor(y_temp_val).unsqueeze(1)
        )
        val_loader = DataLoader(val_dataset, batch_size=train_config['batch_size'], shuffle=False)
        
        # 优化器和损失函数
        optimizer = optim.AdamW(
            model.parameters(),
            lr=train_config['learning_rate'],
            weight_decay=train_config['weight_decay']
        )
        
        growth_criterion = nn.MSELoss()
        temp_criterion = nn.MSELoss()
        
        # 学习率调度器
        scheduler = optim.lr_scheduler.CosineAnnealingLR(
            optimizer, T_max=train_config['epochs']
        )
        
        # 训练历史
        history = {
            'train_growth_loss': [], 'train_temp_loss': [], 'train_total_loss': [],
            'val_growth_loss': [], 'val_temp_loss': [], 'val_total_loss': [],
            'train_growth_r2': [], 'train_temp_r2': [],
            'val_growth_r2': [], 'val_temp_r2': [],
            'learning_rate': []
        }
        
        best_model_state = None
        best_val_loss = float('inf')
        patience_counter = 0
        
        # 训练循环
        for epoch in range(train_config['epochs']):
            # 训练阶段
            model.train()
            train_growth_loss = 0.0
            train_temp_loss = 0.0
            train_growth_preds, train_growth_targets = [], []
            train_temp_preds, train_temp_targets = [], []
            
            for batch_x, batch_y_growth, batch_y_temp in train_loader:
                batch_x = batch_x.to(self.device)
                batch_y_growth = batch_y_growth.to(self.device)
                batch_y_temp = batch_y_temp.to(self.device)
                
                optimizer.zero_grad()
                
                growth_output, temp_output = model(batch_x)
                
                growth_loss = growth_criterion(growth_output, batch_y_growth)
                temp_loss = temp_criterion(temp_output, batch_y_temp)
                
                # 加权总损失
                total_loss = self.growth_weight * growth_loss + self.temp_weight * temp_loss
                
                total_loss.backward()
                
                # 梯度裁剪
                torch.nn.utils.clip_grad_norm_(model.parameters(), train_config['gradient_clip'])
                
                optimizer.step()
                
                train_growth_loss += growth_loss.item()
                train_temp_loss += temp_loss.item()
                
                train_growth_preds.extend(growth_output.detach().cpu().numpy())
                train_growth_targets.extend(batch_y_growth.detach().cpu().numpy())
                train_temp_preds.extend(temp_output.detach().cpu().numpy())
                train_temp_targets.extend(batch_y_temp.detach().cpu().numpy())
            
            # 验证阶段
            model.eval()
            val_growth_loss = 0.0
            val_temp_loss = 0.0
            val_growth_preds, val_growth_targets = [], []
            val_temp_preds, val_temp_targets = [], []
            
            with torch.no_grad():
                for batch_x, batch_y_growth, batch_y_temp in val_loader:
                    batch_x = batch_x.to(self.device)
                    batch_y_growth = batch_y_growth.to(self.device)
                    batch_y_temp = batch_y_temp.to(self.device)
                    
                    growth_output, temp_output = model(batch_x)
                    
                    growth_loss = growth_criterion(growth_output, batch_y_growth)
                    temp_loss = temp_criterion(temp_output, batch_y_temp)
                    
                    val_growth_loss += growth_loss.item()
                    val_temp_loss += temp_loss.item()
                    
                    val_growth_preds.extend(growth_output.cpu().numpy())
                    val_growth_targets.extend(batch_y_growth.cpu().numpy())
                    val_temp_preds.extend(temp_output.cpu().numpy())
                    val_temp_targets.extend(batch_y_temp.cpu().numpy())
            
            # 计算指标
            train_growth_loss /= len(train_loader)
            train_temp_loss /= len(train_loader)
            train_total_loss = self.growth_weight * train_growth_loss + self.temp_weight * train_temp_loss
            
            val_growth_loss /= len(val_loader)
            val_temp_loss /= len(val_loader)
            val_total_loss = self.growth_weight * val_growth_loss + self.temp_weight * val_temp_loss
            
            train_growth_r2 = r2_score(train_growth_targets, train_growth_preds)
            train_temp_r2 = r2_score(train_temp_targets, train_temp_preds)
            val_growth_r2 = r2_score(val_growth_targets, val_growth_preds)
            val_temp_r2 = r2_score(val_temp_targets, val_temp_preds)
            
            # 记录历史
            history['train_growth_loss'].append(train_growth_loss)
            history['train_temp_loss'].append(train_temp_loss)
            history['train_total_loss'].append(train_total_loss)
            history['val_growth_loss'].append(val_growth_loss)
            history['val_temp_loss'].append(val_temp_loss)
            history['val_total_loss'].append(val_total_loss)
            history['train_growth_r2'].append(train_growth_r2)
            history['train_temp_r2'].append(train_temp_r2)
            history['val_growth_r2'].append(val_growth_r2)
            history['val_temp_r2'].append(val_temp_r2)
            history['learning_rate'].append(optimizer.param_groups[0]['lr'])
            
            # 保存最佳模型
            if val_total_loss < best_val_loss:
                best_val_loss = val_total_loss
                best_model_state = model.state_dict().copy()
                patience_counter = 0
            else:
                patience_counter += 1
            
            # 早停检查
            if patience_counter >= train_config['patience']:
                self.logger.info(f"早停触发，在第 {epoch + 1} 轮停止训练")
                break
            
            # 学习率调度
            scheduler.step()
            
            # 日志输出
            if (epoch + 1) % 10 == 0:
                self.logger.info(
                    f"Epoch {epoch + 1}: "
                    f"Growth Loss: {train_growth_loss:.6f}/{val_growth_loss:.6f}, "
                    f"Temp Loss: {train_temp_loss:.6f}/{val_temp_loss:.6f}, "
                    f"Growth R²: {train_growth_r2:.4f}/{val_growth_r2:.4f}, "
                    f"Temp R²: {train_temp_r2:.4f}/{val_temp_r2:.4f}"
                )
        
        # 加载最佳模型
        if best_model_state:
            model.load_state_dict(best_model_state)
        
        return history
    
    def _evaluate_model(self, model: MultiTaskGrowthModel, X_test: np.ndarray, 
                       y_growth_test: np.ndarray, y_temp_test: np.ndarray,
                       growth_scaler, temp_scaler) -> Dict:
        """评估多任务模型"""
        model.eval()
        
        with torch.no_grad():
            X_test_tensor = torch.FloatTensor(X_test).to(self.device)
            growth_pred, temp_pred = model(X_test_tensor)
            
            growth_pred = growth_pred.cpu().numpy().flatten()
            temp_pred = temp_pred.cpu().numpy().flatten()
        
        # 反标准化
        growth_pred_orig = growth_scaler.inverse_transform(growth_pred.reshape(-1, 1)).flatten()
        growth_test_orig = growth_scaler.inverse_transform(y_growth_test.reshape(-1, 1)).flatten()
        temp_pred_orig = temp_scaler.inverse_transform(temp_pred.reshape(-1, 1)).flatten()
        temp_test_orig = temp_scaler.inverse_transform(y_temp_test.reshape(-1, 1)).flatten()
        
        # 计算指标
        growth_metrics = {
            'mse': float(mean_squared_error(growth_test_orig, growth_pred_orig)),
            'rmse': float(np.sqrt(mean_squared_error(growth_test_orig, growth_pred_orig))),
            'mae': float(mean_absolute_error(growth_test_orig, growth_pred_orig)),
            'r2': float(r2_score(growth_test_orig, growth_pred_orig))
        }
        
        temp_metrics = {
            'mse': float(mean_squared_error(temp_test_orig, temp_pred_orig)),
            'rmse': float(np.sqrt(mean_squared_error(temp_test_orig, temp_pred_orig))),
            'mae': float(mean_absolute_error(temp_test_orig, temp_pred_orig)),
            'r2': float(r2_score(temp_test_orig, temp_pred_orig))
        }
        
        self.logger.info(f"生长速率测试: R²={growth_metrics['r2']:.4f}, RMSE={growth_metrics['rmse']:.6f}")
        self.logger.info(f"温度测试: R²={temp_metrics['r2']:.4f}, RMSE={temp_metrics['rmse']:.2f}")
        
        return {
            'growth': growth_metrics,
            'temperature': temp_metrics,
            'predictions': {
                'growth': growth_pred_orig.tolist(),
                'temperature': temp_pred_orig.tolist(),
                'growth_targets': growth_test_orig.tolist(),
                'temperature_targets': temp_test_orig.tolist()
            }
        }

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='DeepMu 多任务训练脚本')
    parser.add_argument('--config', type=str, default='configs/multi_task_config.json', 
                       help='配置文件路径')
    parser.add_argument('--output-dir', type=str, default='models/deepmu_multi_task', 
                       help='输出目录')
    parser.add_argument('--verbose', action='store_true', help='详细输出')
    parser.add_argument('--gpu', type=int, default=0, help='GPU设备ID')
    
    args = parser.parse_args()
    
    # 设置日志
    log_level = logging.DEBUG if args.verbose else logging.INFO
    os.makedirs(args.output_dir, exist_ok=True)
    
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(f'{args.output_dir}/training.log')
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info("开始DeepMu多任务训练流程")
    
    # 默认配置
    default_config = {
        'data': {
            'feature_file': 'training_data/combined_features.tsv',
            'metadata_file': 'training_data/metadata.tsv',
            'test_size': 0.2,
            'validation_size': 0.2,
            'random_state': 42
        },
        'model': {
            'shared_dims': [512, 256],
            'growth_dims': [128, 64],
            'temp_dims': [128, 64],
            'dropout_rate': 0.3,
            'use_batch_norm': True
        },
        'training': {
            'batch_size': 128,
            'learning_rate': 0.001,
            'epochs': 300,
            'patience': 30,
            'weight_decay': 1e-4,
            'gradient_clip': 1.0
        },
        'task_weights': {
            'growth': 1.0,
            'temperature': 1.0
        }
    }
    
    # 加载配置文件（如果存在）
    config = default_config
    if os.path.exists(args.config):
        with open(args.config, 'r') as f:
            user_config = json.load(f)
            config.update(user_config)
    
    # 保存配置
    with open(os.path.join(args.output_dir, 'config.json'), 'w') as f:
        json.dump(config, f, indent=2)
    
    # 设置设备
    device = torch.device(f'cuda:{args.gpu}' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    try:
        # 训练多任务模型
        trainer = MultiTaskTrainer(config, device)
        features, growth_rates, temperatures, sample_ids = trainer.load_data()
        
        model, history = trainer.train(features, growth_rates, temperatures, sample_ids)
        
        # 保存模型
        model_path = os.path.join(args.output_dir, 'multi_task_model.pt')
        torch.save(model.state_dict(), model_path)
        
        # 保存标准化器
        scalers = history['scalers']
        torch.save(scalers, os.path.join(args.output_dir, 'scalers.pt'))
        
        # 保存训练历史
        history_copy = history.copy()
        del history_copy['scalers']  # 移除不能序列化的对象
        
        with open(os.path.join(args.output_dir, 'training_history.json'), 'w') as f:
            json.dump(history_copy, f, indent=2)
        
        logger.info(f"多任务模型训练完成！模型已保存到: {model_path}")
        
    except Exception as e:
        logger.error(f"训练过程中出现错误: {e}")
        raise

if __name__ == "__main__":
    main()
