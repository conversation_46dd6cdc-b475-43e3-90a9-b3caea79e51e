#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test script for protein isoelectric point (pI) feature extraction.
"""

import os
import logging
import argparse
from pathlib import Path
import pandas as pd
from deepmu.features.protein_pi_features import extract_pi_features_for_genome

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    parser = argparse.ArgumentParser(description='Test protein isoelectric point (pI) feature extraction')
    parser.add_argument('--faa-dir', required=True, help='Directory containing protein FASTA files')
    parser.add_argument('--genome-id', required=True, help='Genome ID to test')
    parser.add_argument('--output', default='protein_pi_features.tsv', help='Output TSV file')
    
    args = parser.parse_args()
    
    # Find the protein FASTA file for the specified genome
    faa_file = Path(args.faa_dir) / f"{args.genome_id}_cds.faa"
    if not faa_file.exists():
        logger.error(f"Protein FASTA file not found: {faa_file}")
        return 1
    
    # Extract protein pI features
    logger.info(f"Extracting protein pI features for {args.genome_id}")
    pi_features = extract_pi_features_for_genome(args.genome_id, str(faa_file))
    
    # Print features
    logger.info("Extracted protein pI features:")
    for key, value in pi_features.items():
        logger.info(f"  {key}: {value}")
    
    # Save features to TSV file
    df = pd.DataFrame([pi_features])
    df.to_csv(args.output, sep='\t', index=False)
    logger.info(f"Saved protein pI features to {args.output}")
    
    return 0

if __name__ == "__main__":
    main()
