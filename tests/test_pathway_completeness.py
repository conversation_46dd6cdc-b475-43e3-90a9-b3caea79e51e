import numpy as np
import os
import logging
from deepmu.features.pathway_features import PathwayFeatureCalculator, PathwayDatabase

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger()

# Monkey patch the analyze_pathway_completeness method to add debug output and force completeness calculation
original_analyze_pathway_completeness = PathwayFeatureCalculator.analyze_pathway_completeness

def debug_analyze_pathway_completeness(self, ko_terms):
    print("\n--- DEBUG: analyze_pathway_completeness ---")
    print(f"Input KO terms count: {len(ko_terms)}")

    # Convert KO terms to a set for faster lookups
    ko_set = set(ko_terms)
    print(f"Unique KO terms: {len(ko_set)}")

    # Calculate pathway features
    pathway_features = self.pathway_db.map_to_pathways(ko_terms)
    print(f"Pathway features count: {len(pathway_features)}")

    # Get all pathways instead of just enriched ones
    completeness_info = {}

    # Process all pathways, not just enriched ones
    for i, (pathway_id, score) in enumerate(pathway_features.items()):
        if i < 10:  # Only print debug for first 10 pathways
            print(f"\nProcessing pathway {i+1}/{len(pathway_features)}: {pathway_id} (score: {score})")

        # Get detailed pathway information
        pathway_data = self.pathway_db.get_pathway_info(pathway_id)
        if i < 10:
            print(f"  Pathway name: {pathway_data.get('name', 'Unknown')}")
            print(f"  KOs in pathway data: {len(pathway_data.get('kos', []))}")

        # Calculate which KOs are present/missing
        present_kos = []
        missing_kos = []

        for ko_info in pathway_data.get('kos', []):
            ko_id = ko_info['id']
            if ko_id in ko_set:
                present_kos.append(ko_info)
            else:
                missing_kos.append(ko_info)

        if i < 10:
            print(f"  Present KOs: {len(present_kos)}")
            print(f"  Missing KOs: {len(missing_kos)}")

        # Calculate completeness metrics
        total_kos = len(present_kos) + len(missing_kos)
        if i < 10:
            print(f"  Total KOs: {total_kos}")

        if total_kos > 0:
            completeness = float(len(present_kos)) / float(total_kos)
            if i < 10:
                print(f"  Completeness: {completeness}")

            # Store completeness information for all pathways
            completeness_info[pathway_id] = {
                'name': pathway_data.get('name', pathway_id),
                'category': pathway_data.get('category', 'Unknown'),
                'completeness': completeness,
                'score': float(score),
                'present_kos': present_kos,
                'missing_kos': missing_kos,
                'total_kos': total_kos
            }
        elif i < 10:
            print("  No KOs found for this pathway, skipping")

    print(f"\nFinal completeness info count: {len(completeness_info)}")
    return completeness_info

# Apply the monkey patch
PathwayFeatureCalculator.analyze_pathway_completeness = debug_analyze_pathway_completeness

# Initialize the calculator
print("Initializing calculator...")
calculator = PathwayFeatureCalculator(kegg_path='training_data/kegg/pathway_mapping.txt')

# Load KO terms
ko_terms = []
with open('training_data/kegg/ko_files/GCA_000219855.1_ko.tsv', 'r') as f:
    for line in f:
        parts = line.strip().split('\t')
        if len(parts) >= 2:
            ko = parts[1]
            ko_terms.append(ko)

print(f'Loaded {len(ko_terms)} KO terms')

# Map KO terms to pathways
print("Mapping KO terms to pathways...")
pathway_features = calculator.pathway_db.map_to_pathways(ko_terms)
print(f'Number of pathway features: {len(pathway_features)}')
print(f'Example pathway features: {list(pathway_features.items())[:3]}')

# Calculate pathway completeness
print("Calculating pathway completeness...")
try:
    completeness_info = calculator.analyze_pathway_completeness(ko_terms)
    print(f'Number of pathways with completeness info: {len(completeness_info)}')
    if completeness_info:
        example_pathway = list(completeness_info.items())[0]
        print(f'Example pathway ID: {example_pathway[0]}')
        print(f'Example pathway completeness: {example_pathway[1]["completeness"]}')
    else:
        print("No pathway completeness information found.")

except Exception as e:
    print(f'Error calculating pathway completeness: {e}')
