#!/usr/bin/env python3
"""
Test script for feature extraction.
This script tests the DeepMu feature extraction module.
"""

import os
import logging
import argparse
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_feature_extraction(
    cds_dir: str,
    ko_dir: str = None,
    metadata_file: str = None,
    output_dir: str = "test_features"
):
    """Test feature extraction.

    Args:
        cds_dir: Directory containing CDS files
        ko_dir: Directory containing KO files (optional)
        metadata_file: Path to metadata file (optional)
        output_dir: Directory to save feature files
    """
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)

    logger.info("Testing feature extraction...")
    from deepmu.features.feature_extraction import extract_features_batch

    # Extract features
    extract_features_batch(
        metadata_file=metadata_file,
        output_dir=output_dir,
        cds_dir=cds_dir,
        ko_dir=ko_dir,
        advanced_codon_features=True,
        num_processes=1
    )

    # Check results
    feature_files = list(Path(output_dir).glob("*_features.npz"))
    logger.info(f"Extracted features for {len(feature_files)} genomes")

    return len(feature_files) > 0

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Test feature extraction")
    parser.add_argument("--cds-dir", required=True, help="Directory containing CDS files")
    parser.add_argument("--ko-dir", help="Directory containing KO files")
    parser.add_argument("--metadata", help="Path to metadata file")
    parser.add_argument("--output-dir", default="test_features", help="Directory to save feature files")

    args = parser.parse_args()

    success = test_feature_extraction(
        args.cds_dir,
        args.ko_dir,
        args.metadata,
        args.output_dir
    )

    logger.info(f"Feature extraction {'succeeded' if success else 'failed'}")
