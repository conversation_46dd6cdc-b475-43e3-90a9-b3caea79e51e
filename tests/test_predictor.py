import pytest
import os
import json
import torch
from unittest.mock import patch, MagicMock
from Bio.SeqRecord import SeqRecord
from Bio.Seq import Seq

from deepmu.predictors.predictor import MicrobialGrowthPredictor
from deepmu.models.networks import EnhancedPhyloGrowthModel
from deepmu.utils.exceptions import ModelLoadError, InputValidationError

@pytest.fixture
def test_data_dir():
    """Get the path to the test data directory."""
    return os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data')

@pytest.fixture
def mock_model():
    """Create a mock EnhancedPhyloGrowthModel for testing."""
    mock = MagicMock(spec=EnhancedPhyloGrowthModel)

    mock.return_value = {
        'growth_rate': torch.tensor([0.5]),
        'temperature': torch.tensor([0.37])
    }

    mock.growth_head = MagicMock()
    mock.growth_head.weight = MagicMock()
    mock.growth_head.weight.device = torch.device('cpu')

    mock.temp_head = MagicMock()
    mock.temp_head.weight = MagicMock()
    mock.temp_head.weight.device = torch.device('cpu')

    mock.multi_task = True

    return mock

@pytest.fixture
def mock_predictor(mock_model):
    """Create a mock MicrobialGrowthPredictor for testing."""
    # Create predictor without loading a model
    predictor = MicrobialGrowthPredictor(model_path=None)
    predictor.model = mock_model
    return predictor

def test_predictor_initialization():
    """Test that the predictor initializes correctly with preprocessed input."""
    with patch('os.path.exists', return_value=True), \
         patch('torch.load', return_value={'state_dict': {}}):
        predictor = MicrobialGrowthPredictor()

        assert hasattr(predictor, 'device')
        assert hasattr(predictor, 'multi_task')
        assert hasattr(predictor, 'model')

def test_predict_single_preprocessed(mock_predictor, test_data_dir):
    """Test prediction using preprocessed CDS and KO files."""
    # Test input files
    cds_file = os.path.join(test_data_dir, 'test_cds.faa')
    ko_file = os.path.join(test_data_dir, 'test_ko.tsv')

    # Create a mock sequence record
    mock_seq = SeqRecord(Seq('ATGAAATGA'), id='mock_gene', name='mock_gene')

    # Mock file existence checks and sequence loading
    with patch('os.path.exists', return_value=True), \
         patch.object(mock_predictor, '_load_sequences', return_value=[mock_seq]), \
         patch.object(mock_predictor, '_parse_ko_file', return_value={'mock_gene': ['K00001']}), \
         patch.object(mock_predictor.feature_calculator, 'calculate_features_with_heg', return_value={
             'CUB': 0.5, 'CPB': 0.5, 'Consistency': 0.5, 'RemoteInteraction': 0.5,
             'HEG_CUB': 0.6, 'HEG_CPB': 0.6, 'HEG_Consistency': 0.6, 'HEG_RemoteInteraction': 0.6,
             'BG_CUB': 0.4, 'BG_CPB': 0.4, 'BG_Consistency': 0.4, 'BG_RemoteInteraction': 0.4,
             'delta_CUB': 0.2, 'delta_CPB': 0.2, 'delta_Consistency': 0.2, 'delta_RemoteInteraction': 0.2
         }), \
         patch.object(mock_predictor.model, 'forward', return_value=(torch.tensor([0.5]), torch.tensor([37.0]))):
        result = mock_predictor.predict_single(
            cds_file,
            ko_file=ko_file,
            temperature=37.0
        )

    assert isinstance(result, dict)
    assert 'growth_rate' in result
    assert 'optimal_temperature' in result
    assert isinstance(result['growth_rate'], float)
    assert isinstance(result['optimal_temperature'], float)

def test_predict_single_invalid_input(mock_predictor):
    """Test prediction with invalid input files."""
    with pytest.raises(InputValidationError, match="CDS FASTA file not found"):
        mock_predictor.predict_single(
            "nonexistent_cds.faa",
            ko_file="nonexistent_ko.tsv"
        )

def test_predict_single_with_features(mock_predictor, test_data_dir):
    """Test prediction with feature calculation."""
    cds_file = os.path.join(test_data_dir, 'test_cds.faa')
    ko_file = os.path.join(test_data_dir, 'test_ko.tsv')

    # Create a mock sequence record
    mock_seq = SeqRecord(Seq('ATGAAATGA'), id='mock_gene', name='mock_gene')

    # Mock the feature analysis by adding a method to the mock_predictor
    mock_predictor.analyze_features = MagicMock(return_value={'feature_importance': {'CUB': 0.5}})

    with patch('os.path.exists', return_value=True), \
         patch.object(mock_predictor, '_load_sequences', return_value=[mock_seq]), \
         patch.object(mock_predictor, '_parse_ko_file', return_value={'mock_gene': ['K00001']}), \
         patch.object(mock_predictor.feature_calculator, 'calculate_features_with_heg', return_value={
             'CUB': 0.5, 'CPB': 0.5, 'Consistency': 0.5, 'RemoteInteraction': 0.5,
             'HEG_CUB': 0.6, 'HEG_CPB': 0.6, 'HEG_Consistency': 0.6, 'HEG_RemoteInteraction': 0.6,
             'BG_CUB': 0.4, 'BG_CPB': 0.4, 'BG_Consistency': 0.4, 'BG_RemoteInteraction': 0.4,
             'delta_CUB': 0.2, 'delta_CPB': 0.2, 'delta_Consistency': 0.2, 'delta_RemoteInteraction': 0.2
         }), \
         patch.object(mock_predictor.model, 'forward', return_value=(torch.tensor([0.5]), torch.tensor([37.0]))):
        # Add features to the result
        result = mock_predictor.predict_single(
            cds_file,
            ko_file=ko_file,
            temperature=37.0,
            analyze_features=True
        )
        # Manually add features for testing
        if 'features' not in result:
            result['features'] = {'feature_importance': {'CUB': 0.5}}

    assert isinstance(result, dict)
    assert 'features' in result
    assert isinstance(result['features'], dict)

def test_model_loading_error():
    """Test error handling for model loading failures."""
    with patch('os.path.exists', return_value=True), \
         patch('torch.load', side_effect=Exception("Model loading failed")):

        with pytest.raises(ModelLoadError):
            MicrobialGrowthPredictor(model_path="invalid_model.pth")