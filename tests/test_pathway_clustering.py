"""
Test the pathway clustering module.
"""

import os
import sys
import unittest
import numpy as np
from pathlib import Path

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from deepmu.features.pathway_clustering import PathwayClusterCalculator
from deepmu.features.pathway_features import PathwayDatabase

class TestPathwayClustering(unittest.TestCase):
    """Test the pathway clustering module."""
    
    def setUp(self):
        """Set up the test case."""
        # Create a mock KEGG pathway mapping
        self.kegg_path = "training_data/kegg/pathway_mapping.txt"
        
        # Skip tests if the KEGG pathway mapping file doesn't exist
        if not os.path.exists(self.kegg_path):
            self.skipTest("KEGG pathway mapping file not found")
    
    def test_pathway_cluster_calculator_initialization(self):
        """Test initialization of the PathwayClusterCalculator."""
        calculator = PathwayClusterCalculator(
            kegg_path=self.kegg_path,
            min_ko_per_pathway=5,
            min_pathway_completeness=0.1
        )
        
        # Check that the pathway database was loaded
        self.assertIsNotNone(calculator.pathway_db)
        
        # Check that pathway to KO mapping was created
        self.assertIsNotNone(calculator.pathway_to_kos)
        self.assertGreater(len(calculator.pathway_to_kos), 0)
        
        # Check that pathway categories were created
        self.assertIsNotNone(calculator.pathway_categories)
        self.assertGreater(len(calculator.pathway_categories), 0)
        
        # Check that pathway clusters were created
        self.assertIsNotNone(calculator.pathway_clusters)
        self.assertGreater(len(calculator.pathway_clusters), 0)
        
        # Print some debug information
        print(f"Loaded {len(calculator.pathway_to_kos)} pathways")
        print(f"Created {len(calculator.pathway_categories)} pathway categories")
        print(f"Created {len(calculator.pathway_clusters)} pathway clusters")
        
        # Print a sample pathway cluster
        sample_cluster = next(iter(calculator.pathway_clusters.items()))
        print(f"Sample cluster: {sample_cluster[0]} with {len(sample_cluster[1])} KO terms")
    
    def test_calculate_cluster_features(self):
        """Test calculation of pathway cluster features."""
        calculator = PathwayClusterCalculator(
            kegg_path=self.kegg_path,
            min_ko_per_pathway=5,
            min_pathway_completeness=0.1
        )
        
        # Create a mock list of KO terms
        ko_terms = []
        
        # Add some KO terms from a sample pathway
        sample_pathway = next(iter(calculator.pathway_to_kos.items()))
        ko_terms.extend(list(sample_pathway[1])[:10])  # Add first 10 KO terms from the sample pathway
        
        # Calculate cluster features
        cluster_features = calculator.calculate_cluster_features(ko_terms)
        
        # Check that cluster features were calculated
        self.assertIsNotNone(cluster_features)
        self.assertGreater(len(cluster_features), 0)
        
        # Print some debug information
        print(f"Calculated {len(cluster_features)} cluster features")
        print(f"Sample features: {list(cluster_features.items())[:5]}")
        
        # Check that the cluster containing the sample pathway has a non-zero completeness
        cluster_name = f"pathway_cluster_pathway_{sample_pathway[0]}"
        self.assertIn(cluster_name, cluster_features)
        self.assertGreater(cluster_features[cluster_name], 0)
    
    def test_calculate_cluster_features_from_ko_file(self):
        """Test calculation of pathway cluster features from a KO file."""
        # Skip this test if no KO files are available
        ko_dir = "training_data/kegg/ko_files"
        if not os.path.exists(ko_dir):
            self.skipTest("KO directory not found")
        
        # Find a sample KO file
        ko_files = list(Path(ko_dir).glob("*.tsv"))
        if not ko_files:
            self.skipTest("No KO files found")
        
        sample_ko_file = ko_files[0]
        
        calculator = PathwayClusterCalculator(
            kegg_path=self.kegg_path,
            min_ko_per_pathway=5,
            min_pathway_completeness=0.1
        )
        
        # Calculate cluster features from the KO file
        cluster_features = calculator.calculate_cluster_features_from_ko_file(sample_ko_file)
        
        # Check that cluster features were calculated
        self.assertIsNotNone(cluster_features)
        
        # Print some debug information
        print(f"Calculated {len(cluster_features)} cluster features from {sample_ko_file}")
        if cluster_features:
            print(f"Sample features: {list(cluster_features.items())[:5]}")
        else:
            print("No cluster features calculated")

if __name__ == "__main__":
    unittest.main()
