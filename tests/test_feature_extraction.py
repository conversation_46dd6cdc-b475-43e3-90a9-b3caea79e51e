#!/usr/bin/env python3
"""
Test script for DeepMu feature extraction.

This script tests the feature extraction functionality of DeepMu.
"""

import os
import sys
import argparse
from deepmu.cli.feature import extract_features, extract_features_for_genome
# This imports from feature.py which now uses feature_extraction.py

def main():
    """Main entry point for the test script."""
    parser = argparse.ArgumentParser(description="Test DeepMu feature extraction")
    subparsers = parser.add_subparsers(dest="command")

    # Batch feature extraction
    batch_parser = subparsers.add_parser("batch", help="Extract features for multiple genomes")
    batch_parser.add_argument("--metadata", required=True, help="Path to metadata file")
    batch_parser.add_argument("--genome-dir", default="training_data/genomes", help="Directory containing genome files")
    batch_parser.add_argument("--cds-dir", default="training_data/cds_ffn", help="Directory containing CDS files")
    batch_parser.add_argument("--faa-dir", default="training_data/cds_faa", help="Directory containing protein files")
    batch_parser.add_argument("--ko-dir", default="training_data/kegg/ko_files", help="Directory containing KO files")
    batch_parser.add_argument("--output-dir", default="features", help="Directory to save feature files")
    batch_parser.add_argument("--combine", action="store_true", help="Combine feature files into a single TSV file")
    batch_parser.add_argument("--output-tsv", help="Path to output TSV file (if --combine is specified)")
    batch_parser.add_argument("--kegg-map", default="training_data/kegg/pathway_mapping.txt", help="Path to KEGG mapping file")
    batch_parser.add_argument("--heg-ko-list", default="training_data/kegg/heg_ko_list.txt", help="Path to HEG KO list file")
    batch_parser.add_argument("--advanced-codon-features", action="store_true", help="Calculate advanced codon features")
    batch_parser.add_argument("--num-processes", type=int, default=os.cpu_count(), help="Number of processes to use")
    batch_parser.add_argument("--skip-existing", action="store_true", help="Skip genomes with existing feature files")

    # Single genome feature extraction
    single_parser = subparsers.add_parser("single", help="Extract features for a single genome")
    single_parser.add_argument("--genome-id", required=True, help="Genome ID")
    single_parser.add_argument("--genome-file", help="Path to genome file in FASTA format")
    single_parser.add_argument("--cds-file", help="Path to CDS file in FASTA format")
    single_parser.add_argument("--faa-file", help="Path to protein file in FASTA format")
    single_parser.add_argument("--ko-file", help="Path to KO file")
    single_parser.add_argument("--taxid", help="NCBI taxonomy ID")
    single_parser.add_argument("--output-dir", default="features", help="Directory to save feature files")
    single_parser.add_argument("--output-tsv", help="Path to output TSV file")
    single_parser.add_argument("--kegg-map", default="training_data/kegg/pathway_mapping.txt", help="Path to KEGG mapping file")
    single_parser.add_argument("--heg-ko-list", default="training_data/kegg/heg_ko_list.txt", help="Path to HEG KO list file")
    single_parser.add_argument("--advanced-codon-features", action="store_true", help="Calculate advanced codon features")

    args = parser.parse_args()

    if args.command == "batch":
        return extract_features(args)
    elif args.command == "single":
        return extract_features_for_genome(args)
    else:
        parser.print_help()
        return 0

if __name__ == "__main__":
    sys.exit(main())
