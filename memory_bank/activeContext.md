# Active Context: DeepMu v1.1.1

## Current Work Focus
- Stabilizing the command-line interface
- Improving documentation for user adoption
- Optimizing feature extraction performance
- Enhancing community prediction capabilities

## Recent Changes
- Implemented hybrid architecture with DNN for growth rate and Random Forest for temperature prediction
- Applied log2 transformation for growth rate targets to improve prediction accuracy
- Added advanced codon usage features with HEG/Background differentiation
- Improved handling of incomplete genomes with robust feature calculations
- Enhanced error handling with graceful degradation
- Reorganized the codebase with a more modular structure
- Added support for community-level predictions with abundance weighting
- Optimized feature selection with 250 features for growth rate and 500 for temperature

## Next Steps
- Add support for uncertainty quantification in predictions
- Implement better visualization of prediction results
- Create comprehensive test suite for all components
- Add support for more input file formats
- Optimize memory usage for large-scale batch processing
- Add parallelization support for feature extraction

## Active Decisions and Considerations
- **Input Format Flexibility**: Considering support for additional input formats beyond FASTA
- **Feature Selection Methods**: Evaluating different approaches to select most relevant features
- **Ensemble Weighting**: Testing different weighting schemes for ensemble models
- **Error Metrics**: Determining the most appropriate metrics for uncertainty estimation
- **Computational Efficiency**: Balancing accuracy with performance for large-scale applications
- **User Experience**: Collecting feedback on CLI design and documentation

## Important Patterns and Preferences
- **Modular Design**: Maintaining clear separation between components
- **Robust Error Handling**: All modules should handle errors gracefully
- **Configuration Management**: Using centralized configuration for consistency
- **Feature Standardization**: Consistent handling of feature normalization
- **Command Pattern**: CLI follows consistent subcommand pattern
- **Interface Consistency**: Predictors present consistent interface regardless of underlying model

## Learnings and Project Insights
- **Feature Importance**: Codon usage features, especially HEG/background differences, are most predictive for growth rate
- **Model Selection**: Different phenotypes benefit from different model architectures
- **Data Quality Impact**: Complete genomes yield better predictions than draft genomes
- **Preprocessing Importance**: Proper gene prediction and annotation significantly impact prediction quality
- **Community Prediction Challenges**: Abundance-weighted predictions need careful handling of rare species
- **User Needs**: Users prefer simple interfaces with minimal dependencies over complex options 