# Memory Bank Instructions

## Purpose
This Memory Bank serves as a structured documentation system for the DeepMu v1.1.1 project. It provides context, requirements, and implementation details to help maintain a consistent understanding of the project across time and for different contributors.

## How to Use the Memory Bank
- **For New Contributors**: Read through these files in order to get a complete understanding of the project.
- **For Returning Contributors**: Check the activeContext.md file for the latest status and changes.
- **Before Making Changes**: Review the relevant context files to ensure your changes align with the project's architecture and goals.
- **After Making Changes**: Update the relevant files, especially progress.md and activeContext.md.

## Files in the Memory Bank

### Core Files
1. **projectbrief.md**: Overview of the project and its core features
   - Defines core requirements and goals
   - Serves as the source of truth for project scope
   - Created first and rarely changes

2. **productContext.md**: Explains why the project exists and what problems it solves
   - The "why" of the project
   - Describes user needs and problems solved
   - Outlines user experience goals

3. **systemPatterns.md**: Documents system architecture and design patterns
   - The "how" of the project
   - Records key technical decisions
   - Describes design patterns and component relationships

4. **techContext.md**: Lists technologies and development setup
   - The tools and technologies used
   - Documents development setup
   - Records technical constraints

5. **activeContext.md**: Documents current work focus and recent changes
   - The current state of the project
   - Records recent changes and next steps
   - Updated most frequently

6. **progress.md**: Tracks completed and in-progress features
   - The project status
   - Lists known issues
   - Records the evolution of project decisions

7. **memory_bank_instructions.md**: This file - how to use the Memory Bank
   - Instructions for Memory Bank
   - Stays static and doesn't change

## How to Maintain the Memory Bank

### When to Update
- After completing a milestone
- When changing technical direction
- Before and after breaks in development
- When onboarding new team members
- When encountering significant blockers

### How to Update
1. Modify relevant sections in each file
2. Keep updates concise and focused on what's changed
3. Maintain the existing formatting and structure
4. Ensure information is accurate and up-to-date

## File Dependencies and Relationships

The Memory Bank files have a hierarchical relationship:

```
projectbrief.md     (Foundation)
    |
    |--- productContext.md   (Why)
    |       |
    |       |
    |--- systemPatterns.md   (How)
    |       |
    |       |
    |--- techContext.md      (Tools)
            |
            |
            v
     activeContext.md      (Current state)
            |
            v
       progress.md         (Status tracking)
```

## Best Practices
- Use consistent terminology across all files
- Keep information factual and clear
- Organize content with appropriate headings
- Include code examples where relevant
- Update files promptly after changes
- Cross-reference between files when appropriate
- Focus on the unique purpose of each file, avoiding duplication
- Use Markdown formatting for better readability 