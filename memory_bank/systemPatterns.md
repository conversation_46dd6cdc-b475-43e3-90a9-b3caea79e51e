# System Patterns: DeepMu v1.1.1

## System Architecture
DeepMu follows a modular pipeline architecture with clear separation of concerns:

```
deepmu/
├── models/           # Neural network models
├── features/         # Feature calculation
├── predictors/       # Prediction interfaces
├── data/             # Data handling
├── utils/            # Utility functions
└── cli/              # Command-line interface
```

The system flow follows these sequential steps:
1. **Input Processing**: Parse and validate input files (FASTA, KO annotations)
2. **Preprocessing**: Gene prediction and KO annotation (if needed)
3. **Feature Extraction**: Extract genomic, codon, amino acid, and pathway features
4. **Model Prediction**: Feed features to trained models
5. **Result Processing**: Format and return prediction results

## Key Technical Decisions

### Hybrid Model Architecture
- **Decision**: Use a hybrid ensemble combining DNN for growth rate prediction with Random Forest for temperature prediction
- **Rationale**: Improved accuracy compared to single-model approaches; different phenotypes benefit from different model architectures
- **Impact**: Better prediction performance with model specialization for each target variable
- **Implementation**: DNN with log2 transformation for growth rate; Random Forest without transformation for temperature

### Modular Feature Extraction
- **Decision**: Implement a modular approach to feature extraction with separate modules for different feature types
- **Rationale**: Improved code organization, easier maintenance, and ability to selectively use feature types
- **Impact**: Better maintainability and flexibility in feature calculation

### HEG/Background Differentiation
- **Decision**: Calculate features separately for highly expressed genes (HEGs) and background genes
- **Rationale**: HEGs show stronger codon optimization, providing better signals for growth rate prediction
- **Impact**: Improved prediction accuracy with more biologically meaningful features

### Command-line Interface Design
- **Decision**: Organize CLI as a set of subcommands for different functionality
- **Rationale**: Provides intuitive, git-like interface with context-specific help
- **Impact**: Easier user adoption and clearer separation of functions

### Lazy Loading
- **Decision**: Implement lazy loading of heavy dependencies
- **Rationale**: Reduces startup time and memory usage when only a subset of functionality is needed
- **Impact**: Better user experience with faster command execution

## Design Patterns in Use

### Pipeline Pattern
- **Implementation**: Sequential processing of data through well-defined stages
- **Location**: Main workflow in deepmu_cli.py and predictor classes
- **Benefit**: Clear data flow and separation of processing stages

### Factory Pattern
- **Implementation**: Model and feature calculator factories
- **Location**: deepmu/models/__init__.py and deepmu/features/__init__.py
- **Benefit**: Centralized creation logic and simplified client code

### Strategy Pattern
- **Implementation**: Pluggable feature calculation strategies
- **Location**: Feature calculator classes in deepmu/features/
- **Benefit**: Flexible algorithm selection without changing client code

### Adapter Pattern
- **Implementation**: Uniform interface for different model types
- **Location**: Model wrapper classes in deepmu/models/
- **Benefit**: Consistent prediction interface regardless of underlying model

### Composite Pattern
- **Implementation**: Ensemble models combining multiple base models
- **Location**: Ensemble classes in deepmu/models/
- **Benefit**: Treating individual models and ensemble models uniformly

## Component Relationships

### CLI and Predictor
- CLI parses arguments and configures the predictor
- Predictor handles the core logic and returns results
- CLI formats and displays results to the user

### Predictor and Feature Extractors
- Predictor determines required features
- Feature extractors calculate features from input data
- Predictor receives calculated features for model input

### Feature Extractors and Models
- Feature extractors produce feature vectors
- Models consume feature vectors
- Models produce predictions

### Utils and All Components
- Utility functions support all other components
- Shared functionality is centralized in utils

## Critical Implementation Paths

### Feature Extraction Pipeline
1. Parse input files
2. Extract basic genomic features
3. Calculate codon usage features
4. Extract amino acid features
5. Calculate pathway completeness metrics
6. Combine all features into a unified representation

### Prediction Pipeline
1. Load pretrained models
2. Normalize input features
3. Generate predictions from each component model
4. Ensemble predictions (if using ensemble model)
5. Calculate confidence intervals
6. Return final predictions

### Error Handling and Fallback Mechanisms
- Graceful degradation if optional features can't be calculated
- Fallback to simpler models if advanced models fail
- Detailed error reporting for debugging 