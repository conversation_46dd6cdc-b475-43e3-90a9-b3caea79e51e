# Tech Context: DeepMu v1.1.1

## Technologies Used

### Core Technologies
- **Python 3.7+**: Primary programming language
- **PyTorch**: Deep learning framework for neural network models
- **scikit-learn**: Machine learning library for traditional models (Random Forest, etc.)
- **Biopython**: For biological sequence parsing and analysis
- **pandas/numpy**: Data manipulation and numerical computation
- **ete3**: For taxonomy handling and phylogenetic analysis

### Secondary Technologies
- **LightGBM/XGBoost/CatBoost**: Gradient boosting frameworks for ensemble models
- **pytorch-tabular**: Framework for tabular data modeling with PyTorch
- **tab-transformer-pytorch**: Transformer architecture for tabular data
- **joblib**: Parallel processing and model persistence
- **tqdm**: Progress bars for long-running operations
- **matplotlib/seaborn**: Data visualization

### Development Technologies
- **pytest**: Testing framework
- **coverage**: Code coverage measurement
- **black**: Code formatting
- **mypy**: Static type checking

## Development Setup

### Environment Setup
```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Install DeepMu in development mode
pip install -e .
```

### External Dependencies
- **KofamScan**: For KO annotation (optional, can use pre-annotated files)
- **Prodigal**: For gene prediction (optional, can use pre-predicted CDS)
- **tRNAscan-SE**: For tRNA identification (optional)
- **Barrnap**: For rRNA identification (optional)

### Model Files
Pre-trained models are stored in the `models/` directory:
- `models/deepmu_final_model/`: Contains the final hybrid model with:
  - **DNN Model**: Multi-layer neural network for growth rate prediction (with log2 transformation)
  - **Random Forest Model**: Tree-based ensemble (300 estimators) for temperature prediction
  - **Feature Selectors**: Optimized feature selection modules for each prediction task
  - **Scalers**: Standard scalers for feature normalization

### Model Training
The main training script is `train_deepmu_final.sh`, which configures the hybrid model with:
- 250 selected features for growth rate prediction
- 500 selected features for temperature prediction
- Log2 transformation for growth rate targets
- No transformation for temperature targets
- Random Forest with 300 trees, max depth of 20, and min samples per leaf of 2

## Technical Constraints

### Performance Constraints
- **Memory Usage**: Large genomes or batch processing may require 8+ GB RAM
- **Processing Speed**: Feature extraction is I/O and CPU intensive
- **GPU Support**: Optional for neural network models, falls back to CPU

### Input Constraints
- **Genome Size**: Designed for prokaryotic genomes (typically <10Mb)
- **File Formats**: Supports FASTA for sequences, TSV for feature files
- **Sequence Quality**: Better predictions with complete genomes; draft genomes supported but may have lower accuracy

### Output Constraints
- **Prediction Range**: Growth rates typically in 0.1-3.0 h⁻¹ range
- **Temperature Range**: Optimal temperatures typically in 10-120°C range

### Error Handling
- Fails gracefully with informative error messages
- Falls back to simpler models if advanced models fail
- Logs warnings for potential issues with input data

## Dependencies
### Required Dependencies
```
torch>=1.8.0
numpy>=1.19.0
pandas>=1.0.0
biopython>=1.79
scipy>=1.7.0
scikit-learn>=0.24.0
ete3>=3.1.2
tqdm>=4.67.0
matplotlib>=3.3.0
```

### Optional Dependencies
```
tab-transformer-pytorch>=0.1.0
pytorch-tabular>=1.0.0
seaborn>=0.12.0
joblib>=0.17.0
lightgbm>=3.2.0
xgboost>=1.3.0
catboost>=1.0.0
```

### Development Dependencies
```
pytest>=7.0.0
pytest-cov>=3.0.0
coverage>=6.0.0
black>=22.0.0
mypy>=0.900
```

## Tool Usage Patterns

### Command-line Interface
The main interface is through the `DeepMu` command:
```bash
# Basic usage
DeepMu predict features.tsv

# Community prediction
DeepMu community input_dir/ --abundance abundance.tsv

# Feature extraction
DeepMu feature input_dir/ -o output_dir/
```

### API Usage
For programmatic use, the core predictor can be imported directly:
```python
from deepmu.predictors import MicrobialGrowthPredictor

predictor = MicrobialGrowthPredictor()
results = predictor.predict_from_features("features.tsv")
```

### Feature Extraction
Features can be extracted separately using the feature extraction module:
```python
from deepmu.features import extract_features

features = extract_features(cds_file="genome.fna", genome_id="example_genome")
```

### Preprocessing Pipeline
Preprocessing can be run separately to prepare input files:
```python
from deepmu.utils.preprocess import preprocess_genome

cds_file, ko_file = preprocess_genome(
    genome_file="genome.fna",
    output_dir="output/",
    kofamscan_db_path="/path/to/kofamscan"
)
``` 