# Project Brief: DeepMu v1.1.1

## Overview
DeepMu is a comprehensive toolkit for predicting microbial growth rates and optimal growth temperatures from genomic data. It uses machine learning models trained on genomic features to predict these phenotypic traits without requiring experimental measurements.

## Core Requirements
- Accurate prediction of microbial growth rates from genome sequences
- Accurate prediction of optimal growth temperatures from genome sequences
- Support for both single genome and community-level predictions
- User-friendly command-line interface for easy use
- Comprehensive feature extraction from raw genome sequences
- Support for preprocessing raw genome sequences with gene prediction and KO annotation
- Efficient processing of multiple genomes in batch mode

## Goals
- Provide researchers with a tool to quickly estimate growth rates and optimal temperatures
- Enable community-level predictions for metagenomic datasets
- Support environmental adaptation studies with accurate temperature predictions
- Facilitate rapid phenotypic assessment for newly sequenced organisms
- Aid in the understanding of microbial ecology and evolution through phenotypic prediction

## Project Scope
### In Scope
- Feature extraction from genome sequences
- Preprocessing of raw sequences (gene prediction, annotation)
- Growth rate prediction from genomic features
- Temperature prediction from genomic features
- Community-level growth predictions with abundance weighting
- Command-line interface for all functionality
- Detailed documentation and user guides

### Out of Scope
- Web interface or GUI (command-line only)
- Prediction of other phenotypic traits beyond growth rate and temperature
- Direct integration with sequence databases (requires local sequence files)
- Training new models (only using pre-trained models)
- Metagenomic assembly (requires pre-assembled genomes) 