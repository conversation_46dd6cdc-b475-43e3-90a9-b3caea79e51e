# Core dependencies
# torch>=1.8.0
numpy>=1.19.0
pandas>=1.0.0
biopython>=1.79
scipy>=1.7.0
scikit-learn>=0.24.0
ete3>=3.1.2
tqdm>=4.67.0
matplotlib>=3.3.0
# tab-transformer-pytorch>=0.1.0
# pytorch-tabular>=1.0.0
seaborn>=0.12.0
joblib>=0.17.0
lightgbm>=3.2.0  # Optional, falls back to GradientBoostingRegressor
xgboost>=1.3.0   # Optional, falls back to RandomForestRegressor
catboost>=1.0.0  # Optional, falls back to XGBoost or RandomForest

# Development & testing
pytest>=7.0.0
pytest-cov>=3.0.0
coverage>=6.0.0
black>=22.0.0
mypy>=0.900
