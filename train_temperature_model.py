#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
DeepMu 最适温度单任务训练脚本

这个脚本专门用于训练微生物最适温度预测模型，采用深度神经网络架构，
针对温度预测任务进行了特别优化。

特点：
1. 专注于最适温度预测
2. 温度范围约束
3. 物理意义保持
4. 回归和分类混合方法
5. 温度特定的数据增强

作者: DeepMu 开发团队
版本: 2.0.0
日期: 2025-06-29
"""

import os
import sys
import json
import argparse
import logging
import warnings
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union
from datetime import datetime

import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
from sklearn.model_selection import KFold, train_test_split
from sklearn.preprocessing import StandardScaler, RobustScaler, MinMaxScaler
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from sklearn.ensemble import IsolationForest
import matplotlib.pyplot as plt
import seaborn as sns

# 忽略警告
warnings.filterwarnings('ignore')

class TemperatureModel(nn.Module):
    """最适温度预测模型"""
    
    def __init__(self, input_dim: int, hidden_dims: List[int], 
                 dropout_rates: List[float], use_batch_norm: bool = True, 
                 activation: str = 'gelu', temp_range: Tuple[float, float] = (0.0, 100.0)):
        """
        初始化温度预测模型
        
        Args:
            input_dim: 输入特征维度
            hidden_dims: 隐藏层维度列表
            dropout_rates: 每层的dropout率
            use_batch_norm: 是否使用批量归一化
            activation: 激活函数类型
            temp_range: 温度范围约束 (min_temp, max_temp)
        """
        super().__init__()
        
        self.temp_min, self.temp_max = temp_range
        self.layers = nn.ModuleList()
        
        # 选择激活函数
        if activation == 'gelu':
            self.activation = nn.GELU()
        elif activation == 'relu':
            self.activation = nn.ReLU()
        elif activation == 'leaky_relu':
            self.activation = nn.LeakyReLU(0.1)
        elif activation == 'swish':
            self.activation = nn.SiLU()
        elif activation == 'tanh':
            self.activation = nn.Tanh()
        else:
            self.activation = nn.GELU()
        
        # 构建网络层
        dims = [input_dim] + hidden_dims
        
        for i in range(len(dims) - 1):
            # 线性层
            self.layers.append(nn.Linear(dims[i], dims[i + 1]))
            
            # 批量归一化
            if use_batch_norm:
                self.layers.append(nn.BatchNorm1d(dims[i + 1]))
            
            # 激活函数
            self.layers.append(self.activation)
            
            # Dropout
            if i < len(dropout_rates):
                self.layers.append(nn.Dropout(dropout_rates[i]))
            else:
                self.layers.append(nn.Dropout(0.3))
        
        # 输出层
        self.output_layer = nn.Linear(hidden_dims[-1], 1)
        
        # 温度约束层（使用sigmoid + 线性变换）
        self.temp_constraint = True
        
        # 权重初始化
        self._initialize_weights()
    
    def _initialize_weights(self):
        """初始化网络权重"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.kaiming_normal_(module.weight, mode='fan_out', nonlinearity='relu')
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0)
            elif isinstance(module, nn.BatchNorm1d):
                nn.init.constant_(module.weight, 1)
                nn.init.constant_(module.bias, 0)
    
    def forward(self, x):
        """前向传播"""
        # 特征提取
        for layer in self.layers:
            x = layer(x)
        
        # 输出层
        x = self.output_layer(x)
        
        # 温度约束（可选）
        if self.temp_constraint:
            # 使用sigmoid将输出约束到[0,1]，然后线性变换到温度范围
            x = torch.sigmoid(x)
            x = x * (self.temp_max - self.temp_min) + self.temp_min
        
        return x

class TemperatureTrainer:
    """温度预测训练器"""
    
    def __init__(self, config: Dict, device: torch.device):
        """
        初始化训练器
        
        Args:
            config: 训练配置
            device: 计算设备
        """
        self.config = config
        self.device = device
        self.logger = logging.getLogger(__name__)
        
        # 数据处理器
        self.feature_scaler = RobustScaler()
        self.target_scaler = MinMaxScaler(feature_range=(0, 1))  # 温度使用MinMax标准化
        self.outlier_detector = IsolationForest(
            contamination=0.1, 
            random_state=config['data']['random_state']
        )
        
        # 温度范围
        self.temp_range = config['model'].get('temp_range', (0.0, 100.0))
    
    def load_and_preprocess_data(self) -> Tuple[np.ndarray, np.ndarray, List[str]]:
        """
        加载和预处理数据
        
        Returns:
            特征、温度、样本ID
        """
        self.logger.info("加载温度预测训练数据...")
        
        # 加载特征数据
        feature_file = self.config['data']['feature_file']
        features_df = pd.read_csv(feature_file, sep='\t', index_col=0)
        
        # 加载元数据
        metadata_file = self.config['data']['metadata_file']
        metadata_df = pd.read_csv(metadata_file, sep='\t', index_col=0)
        
        # 获取共同样本
        common_indices = features_df.index.intersection(metadata_df.index)
        features_df = features_df.loc[common_indices]
        metadata_df = metadata_df.loc[common_indices]
        
        # 提取温度数据
        temperatures = metadata_df['optimal_temperature'].values
        
        # 移除缺失值
        valid_mask = ~np.isnan(temperatures)
        
        features = features_df[valid_mask].fillna(0).values
        temperatures = temperatures[valid_mask]
        sample_ids = features_df[valid_mask].index.tolist()
        
        # 温度范围过滤
        temp_mask = (temperatures >= self.temp_range[0]) & (temperatures <= self.temp_range[1])
        features = features[temp_mask]
        temperatures = temperatures[temp_mask]
        sample_ids = [sample_ids[i] for i in range(len(sample_ids)) if temp_mask[i]]
        
        self.logger.info(f"有效样本数: {len(sample_ids)}")
        self.logger.info(f"特征维度: {features.shape[1]}")
        self.logger.info(f"温度统计: 均值={temperatures.mean():.2f}°C, "
                        f"标准差={temperatures.std():.2f}°C, "
                        f"范围=[{temperatures.min():.2f}, {temperatures.max():.2f}]°C")
        
        # 异常值检测和移除
        if len(features) > 50:
            outlier_mask = self.outlier_detector.fit_predict(features) == 1
            features = features[outlier_mask]
            temperatures = temperatures[outlier_mask]
            sample_ids = [sample_ids[i] for i in range(len(sample_ids)) if outlier_mask[i]]
            
            self.logger.info(f"异常值检测后剩余样本数: {len(sample_ids)}")
        
        # 特征标准化
        features_scaled = self.feature_scaler.fit_transform(features)
        
        # 温度标准化（如果需要）
        if self.config['data'].get('normalize_target', True):
            temperatures_scaled = self.target_scaler.fit_transform(
                temperatures.reshape(-1, 1)
            ).flatten()
        else:
            temperatures_scaled = temperatures
        
        return features_scaled, temperatures_scaled, sample_ids
    
    def create_model(self, input_dim: int) -> TemperatureModel:
        """创建温度预测模型"""
        model_config = self.config['model']
        
        model = TemperatureModel(
            input_dim=input_dim,
            hidden_dims=model_config['hidden_dims'],
            dropout_rates=model_config['dropout_rates'],
            use_batch_norm=model_config['use_batch_norm'],
            activation=model_config['activation'],
            temp_range=self.temp_range
        )
        
        return model.to(self.device)
    
    def train_with_cross_validation(self, features: np.ndarray, temperatures: np.ndarray, 
                                   sample_ids: List[str]) -> Tuple[TemperatureModel, Dict]:
        """
        使用交叉验证训练模型
        
        Args:
            features: 特征数据
            temperatures: 温度数据
            sample_ids: 样本ID
            
        Returns:
            最佳模型和训练历史
        """
        self.logger.info("开始交叉验证训练...")
        
        val_config = self.config['validation']
        k_folds = val_config['k_folds']
        
        kfold = KFold(
            n_splits=k_folds, 
            shuffle=True, 
            random_state=self.config['data']['random_state']
        )
        
        fold_histories = []
        fold_models = []
        fold_metrics = []
        
        for fold, (train_idx, val_idx) in enumerate(kfold.split(features)):
            self.logger.info(f"训练第 {fold + 1}/{k_folds} 折...")
            
            X_train, X_val = features[train_idx], features[val_idx]
            y_train, y_val = temperatures[train_idx], temperatures[val_idx]
            
            # 创建模型
            model = self.create_model(features.shape[1])
            
            # 训练模型
            history = self._train_single_fold(model, X_train, y_train, X_val, y_val)
            
            # 评估模型
            val_metrics = self._evaluate_model(model, X_val, y_val)
            
            fold_histories.append(history)
            fold_models.append(model)
            fold_metrics.append(val_metrics)
            
            self.logger.info(f"第 {fold + 1} 折验证结果: R²={val_metrics['r2']:.4f}, "
                           f"MAE={val_metrics['mae']:.2f}°C")
        
        # 选择最佳模型
        best_fold = max(range(k_folds), key=lambda i: fold_metrics[i]['r2'])
        best_model = fold_models[best_fold]
        
        # 计算交叉验证指标
        cv_r2_scores = [metrics['r2'] for metrics in fold_metrics]
        cv_mae_scores = [metrics['mae'] for metrics in fold_metrics]
        
        cv_metrics = {
            'r2_mean': float(np.mean(cv_r2_scores)),
            'r2_std': float(np.std(cv_r2_scores)),
            'mae_mean': float(np.mean(cv_mae_scores)),
            'mae_std': float(np.std(cv_mae_scores)),
            'r2_folds': cv_r2_scores,
            'mae_folds': cv_mae_scores
        }
        
        self.logger.info(
            f"交叉验证结果: R² = {cv_metrics['r2_mean']:.4f} ± {cv_metrics['r2_std']:.4f}, "
            f"MAE = {cv_metrics['mae_mean']:.2f} ± {cv_metrics['mae_std']:.2f}°C"
        )
        
        # 合并历史
        combined_history = {
            'fold_histories': fold_histories,
            'best_fold': best_fold + 1,
            'cv_metrics': cv_metrics,
            'fold_metrics': fold_metrics
        }
        
        return best_model, combined_history
    
    def _train_single_fold(self, model: TemperatureModel, X_train: np.ndarray, 
                          y_train: np.ndarray, X_val: np.ndarray, y_val: np.ndarray) -> Dict:
        """训练单个折叠"""
        train_config = self.config['training']
        
        # 创建数据加载器
        train_dataset = TensorDataset(
            torch.FloatTensor(X_train),
            torch.FloatTensor(y_train).unsqueeze(1)
        )
        train_loader = DataLoader(
            train_dataset, 
            batch_size=train_config['batch_size'], 
            shuffle=True
        )
        
        val_dataset = TensorDataset(
            torch.FloatTensor(X_val),
            torch.FloatTensor(y_val).unsqueeze(1)
        )
        val_loader = DataLoader(
            val_dataset, 
            batch_size=train_config['batch_size'], 
            shuffle=False
        )
        
        # 优化器和损失函数
        optimizer = optim.AdamW(
            model.parameters(),
            lr=train_config['learning_rate'],
            weight_decay=train_config['weight_decay']
        )
        
        # 温度预测使用MAE损失（对异常值更鲁棒）
        criterion = nn.L1Loss() if train_config.get('use_mae_loss', True) else nn.MSELoss()
        
        # 学习率调度器
        scheduler = optim.lr_scheduler.CosineAnnealingLR(
            optimizer, T_max=train_config['epochs']
        )
        
        # 训练历史
        history = {
            'train_loss': [],
            'val_loss': [],
            'train_r2': [],
            'val_r2': [],
            'learning_rate': []
        }
        
        best_val_loss = float('inf')
        patience_counter = 0
        
        # 训练循环
        for epoch in range(train_config['epochs']):
            # 训练阶段
            model.train()
            train_loss = 0.0
            train_predictions = []
            train_targets = []
            
            for batch_x, batch_y in train_loader:
                batch_x, batch_y = batch_x.to(self.device), batch_y.to(self.device)
                
                optimizer.zero_grad()
                outputs = model(batch_x)
                loss = criterion(outputs, batch_y)
                loss.backward()
                
                # 梯度裁剪
                torch.nn.utils.clip_grad_norm_(
                    model.parameters(), 
                    train_config['gradient_clip']
                )
                
                optimizer.step()
                
                train_loss += loss.item()
                train_predictions.extend(outputs.detach().cpu().numpy())
                train_targets.extend(batch_y.detach().cpu().numpy())
            
            # 验证阶段
            model.eval()
            val_loss = 0.0
            val_predictions = []
            val_targets = []
            
            with torch.no_grad():
                for batch_x, batch_y in val_loader:
                    batch_x, batch_y = batch_x.to(self.device), batch_y.to(self.device)
                    outputs = model(batch_x)
                    loss = criterion(outputs, batch_y)
                    
                    val_loss += loss.item()
                    val_predictions.extend(outputs.cpu().numpy())
                    val_targets.extend(batch_y.cpu().numpy())
            
            # 计算指标
            train_loss /= len(train_loader)
            val_loss /= len(val_loader)
            
            train_r2 = r2_score(train_targets, train_predictions)
            val_r2 = r2_score(val_targets, val_predictions)
            
            # 记录历史
            history['train_loss'].append(train_loss)
            history['val_loss'].append(val_loss)
            history['train_r2'].append(train_r2)
            history['val_r2'].append(val_r2)
            history['learning_rate'].append(optimizer.param_groups[0]['lr'])
            
            # 早停检查
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                patience_counter = 0
            else:
                patience_counter += 1
            
            if patience_counter >= train_config['patience']:
                break
            
            # 学习率调度
            scheduler.step()
        
        return history
    
    def _evaluate_model(self, model: TemperatureModel, X_test: np.ndarray, 
                       y_test: np.ndarray) -> Dict:
        """评估模型"""
        model.eval()
        
        with torch.no_grad():
            X_test_tensor = torch.FloatTensor(X_test).to(self.device)
            predictions = model(X_test_tensor).cpu().numpy().flatten()
        
        # 如果目标变量被标准化，需要反标准化
        if self.config['data'].get('normalize_target', True):
            predictions_orig = self.target_scaler.inverse_transform(
                predictions.reshape(-1, 1)
            ).flatten()
            y_test_orig = self.target_scaler.inverse_transform(
                y_test.reshape(-1, 1)
            ).flatten()
        else:
            predictions_orig = predictions
            y_test_orig = y_test
        
        # 计算指标
        mse = mean_squared_error(y_test_orig, predictions_orig)
        rmse = np.sqrt(mse)
        mae = mean_absolute_error(y_test_orig, predictions_orig)
        r2 = r2_score(y_test_orig, predictions_orig)
        
        # 温度特定指标
        temp_diff = np.abs(y_test_orig - predictions_orig)
        within_1c = np.mean(temp_diff <= 1.0) * 100  # 1°C内准确率
        within_2c = np.mean(temp_diff <= 2.0) * 100  # 2°C内准确率
        within_5c = np.mean(temp_diff <= 5.0) * 100  # 5°C内准确率
        
        return {
            'mse': float(mse),
            'rmse': float(rmse),
            'mae': float(mae),
            'r2': float(r2),
            'within_1c': float(within_1c),
            'within_2c': float(within_2c),
            'within_5c': float(within_5c),
            'predictions': predictions_orig.tolist(),
            'targets': y_test_orig.tolist()
        }

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='DeepMu 温度预测训练脚本')
    parser.add_argument('--config', type=str, default='configs/temperature_config.json', 
                       help='配置文件路径')
    parser.add_argument('--output-dir', type=str, default='models/deepmu_temperature', 
                       help='输出目录')
    parser.add_argument('--verbose', action='store_true', help='详细输出')
    parser.add_argument('--gpu', type=int, default=0, help='GPU设备ID')
    
    args = parser.parse_args()
    
    # 设置日志
    log_level = logging.DEBUG if args.verbose else logging.INFO
    os.makedirs(args.output_dir, exist_ok=True)
    
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(f'{args.output_dir}/training.log')
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info("开始DeepMu温度预测训练流程")
    
    # 默认配置
    default_config = {
        'data': {
            'feature_file': 'training_data/combined_features.tsv',
            'metadata_file': 'training_data/metadata.tsv',
            'normalize_target': True,
            'random_state': 42
        },
        'model': {
            'hidden_dims': [512, 256, 128, 64],
            'dropout_rates': [0.3, 0.4, 0.4, 0.5],
            'use_batch_norm': True,
            'activation': 'gelu',
            'temp_range': [0.0, 100.0]
        },
        'training': {
            'batch_size': 128,
            'learning_rate': 0.001,
            'epochs': 300,
            'patience': 30,
            'weight_decay': 1e-4,
            'gradient_clip': 1.0,
            'use_mae_loss': True
        },
        'validation': {
            'k_folds': 5
        }
    }
    
    # 加载配置文件（如果存在）
    config = default_config
    if os.path.exists(args.config):
        with open(args.config, 'r') as f:
            user_config = json.load(f)
            config.update(user_config)
    
    # 保存配置
    with open(os.path.join(args.output_dir, 'config.json'), 'w') as f:
        json.dump(config, f, indent=2)
    
    # 设置设备
    device = torch.device(f'cuda:{args.gpu}' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    try:
        # 训练温度预测模型
        trainer = TemperatureTrainer(config, device)
        features, temperatures, sample_ids = trainer.load_and_preprocess_data()
        
        model, history = trainer.train_with_cross_validation(features, temperatures, sample_ids)
        
        # 保存模型
        model_path = os.path.join(args.output_dir, 'temperature_model.pt')
        torch.save(model.state_dict(), model_path)
        
        # 保存标准化器
        scalers = {
            'feature_scaler': trainer.feature_scaler,
            'target_scaler': trainer.target_scaler
        }
        torch.save(scalers, os.path.join(args.output_dir, 'scalers.pt'))
        
        # 保存训练历史
        with open(os.path.join(args.output_dir, 'training_history.json'), 'w') as f:
            json.dump(history, f, indent=2)
        
        logger.info(f"温度预测模型训练完成！模型已保存到: {model_path}")
        
    except Exception as e:
        logger.error(f"训练过程中出现错误: {e}")
        raise

if __name__ == "__main__":
    main()
