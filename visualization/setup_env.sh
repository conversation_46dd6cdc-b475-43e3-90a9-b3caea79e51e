#!/bin/bash

# DeepMu 可视化环境设置脚本
# 
# 这个脚本用于设置DeepMu可视化所需的环境变量
# 
# 作者: DeepMu 开发团队
# 版本: 2.0.0
# 日期: 2025-06-29

# 修复MKL线程问题
export MKL_THREADING_LAYER=GNU

# 设置matplotlib后端（避免GUI问题）
export MPLBACKEND=Agg

# 设置中文字体路径（如果需要）
export FONTCONFIG_PATH=/usr/share/fonts

# 禁用一些警告
export PYTHONWARNINGS="ignore"

# 设置NumPy线程数（可选）
export OMP_NUM_THREADS=4
export NUMEXPR_NUM_THREADS=4

echo "✅ DeepMu可视化环境变量已设置"
echo "   - MKL_THREADING_LAYER=GNU"
echo "   - MPLBACKEND=Agg"
echo "   - OMP_NUM_THREADS=4"

# 验证Python环境
echo "🔍 验证Python环境..."
python3 -c "
import matplotlib
import numpy as np
import pandas as pd
import seaborn as sns
print('✅ 所有必需包都可用')
print(f'   - matplotlib: {matplotlib.__version__}')
print(f'   - numpy: {np.__version__}')
print(f'   - pandas: {pd.__version__}')
print(f'   - seaborn: {sns.__version__}')
"
