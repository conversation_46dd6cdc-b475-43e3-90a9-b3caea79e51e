#!/bin/bash

# DeepMu 预测可视化脚本
# 
# 这个脚本用于可视化DeepMu模型的预测结果
# 
# 作者: DeepMu 开发团队
# 版本: 2.0.0
# 日期: 2025-06-29

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
DeepMu 预测可视化脚本

用法: $0 [选项] <预测结果文件>

选项:
    -h, --help              显示此帮助信息
    -o, --output-dir DIR    输出目录 (默认: 预测文件目录/prediction_visualizations)
    -t, --true-values FILE  真实值文件路径 (用于准确性分析)
    --no-distribution      跳过分布图
    --no-accuracy          跳过准确性分析
    --no-summary           跳过总结报告
    -v, --verbose          详细输出

示例:
    $0 results/predictions.json
    $0 results/predictions.json --true-values data/true_values.tsv
    $0 results/predictions.json --output-dir my_pred_viz --verbose

支持的预测文件格式:
    - JSON格式 (.json)
    - 单个预测或批量预测结果

真实值文件格式:
    - TSV格式 (.tsv)
    - CSV格式 (.csv)
    - JSON格式 (.json)

EOF
}

# 默认参数
PREDICTION_FILE=""
OUTPUT_DIR=""
TRUE_VALUES_FILE=""
NO_DISTRIBUTION=false
NO_ACCURACY=false
NO_SUMMARY=false
VERBOSE=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -o|--output-dir)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        -t|--true-values)
            TRUE_VALUES_FILE="$2"
            shift 2
            ;;
        --no-distribution)
            NO_DISTRIBUTION=true
            shift
            ;;
        --no-accuracy)
            NO_ACCURACY=true
            shift
            ;;
        --no-summary)
            NO_SUMMARY=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -*)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
        *)
            if [[ -z "$PREDICTION_FILE" ]]; then
                PREDICTION_FILE="$1"
            else
                log_error "多余的参数: $1"
                show_help
                exit 1
            fi
            shift
            ;;
    esac
done

# 检查必需参数
if [[ -z "$PREDICTION_FILE" ]]; then
    log_error "请指定预测结果文件"
    show_help
    exit 1
fi

# 检查预测文件是否存在
if [[ ! -f "$PREDICTION_FILE" ]]; then
    log_error "预测结果文件不存在: $PREDICTION_FILE"
    exit 1
fi

# 检查真实值文件（如果提供）
if [[ -n "$TRUE_VALUES_FILE" && ! -f "$TRUE_VALUES_FILE" ]]; then
    log_error "真实值文件不存在: $TRUE_VALUES_FILE"
    exit 1
fi

# 设置默认输出目录
if [[ -z "$OUTPUT_DIR" ]]; then
    PRED_DIR=$(dirname "$PREDICTION_FILE")
    OUTPUT_DIR="$PRED_DIR/prediction_visualizations"
fi

# 检测预测类型
detect_prediction_type() {
    local first_line=$(head -1 "$PREDICTION_FILE")
    
    if [[ "$first_line" == "["* ]]; then
        echo "batch"
    elif [[ "$first_line" == "{"* ]]; then
        echo "single"
    else
        echo "unknown"
    fi
}

PREDICTION_TYPE=$(detect_prediction_type)

log_info "开始DeepMu预测可视化"
log_info "预测文件: $PREDICTION_FILE"
log_info "预测类型: $PREDICTION_TYPE"
log_info "输出目录: $OUTPUT_DIR"

if [[ -n "$TRUE_VALUES_FILE" ]]; then
    log_info "真实值文件: $TRUE_VALUES_FILE"
fi

# 检查Python环境
check_python_env() {
    log_info "检查Python环境..."
    
    if ! command -v python3 &> /dev/null; then
        log_error "Python3 未找到"
        exit 1
    fi
    
    # 检查必要的Python包
    local required_packages=("matplotlib" "seaborn" "pandas" "numpy" "scipy")
    for package in "${required_packages[@]}"; do
        if ! python3 -c "import $package" &> /dev/null; then
            log_error "Python包 '$package' 未安装"
            log_info "请运行: pip install $package"
            exit 1
        fi
    done
    
    log_info "Python环境检查通过"
}

# 运行可视化
run_visualization() {
    log_info "开始生成预测可视化..."
    
    # 构建Python命令
    local python_cmd="python3 visualization/prediction/prediction_visualizer.py"
    python_cmd="$python_cmd \"$PREDICTION_FILE\""
    
    if [[ -n "$OUTPUT_DIR" ]]; then
        python_cmd="$python_cmd --output-dir \"$OUTPUT_DIR\""
    fi
    
    if [[ -n "$TRUE_VALUES_FILE" ]]; then
        python_cmd="$python_cmd --true-values \"$TRUE_VALUES_FILE\""
    fi
    
    if [[ "$NO_DISTRIBUTION" == "true" ]]; then
        python_cmd="$python_cmd --no-distribution"
    fi
    
    if [[ "$NO_ACCURACY" == "true" ]]; then
        python_cmd="$python_cmd --no-accuracy"
    fi
    
    if [[ "$NO_SUMMARY" == "true" ]]; then
        python_cmd="$python_cmd --no-summary"
    fi
    
    if [[ "$VERBOSE" == "true" ]]; then
        log_info "执行命令: $python_cmd"
    fi
    
    # 执行可视化
    if eval "$python_cmd"; then
        log_info "预测可视化完成！"
        
        # 显示生成的文件
        if [[ -d "$OUTPUT_DIR" ]]; then
            log_info "生成的文件:"
            find "$OUTPUT_DIR" -name "*.png" -o -name "*.md" | while read -r file; do
                echo "  - $(basename "$file")"
            done
        fi
        
    else
        log_error "可视化失败！"
        exit 1
    fi
}

# 分析预测结果
analyze_predictions() {
    log_info "分析预测结果..."
    
    # 使用Python脚本分析预测统计
    python3 -c "
import json
import numpy as np

with open('$PREDICTION_FILE', 'r') as f:
    data = json.load(f)

if isinstance(data, list):
    print(f'预测样本数: {len(data)}')
    
    # 生长速率统计
    growth_rates = [d.get('growth_rate') for d in data if d.get('growth_rate') is not None]
    if growth_rates:
        print(f'生长速率预测数: {len(growth_rates)}')
        print(f'生长速率范围: {min(growth_rates):.4f} - {max(growth_rates):.4f}')
        print(f'生长速率均值: {np.mean(growth_rates):.4f}')
    
    # 温度统计
    temperatures = [d.get('optimal_temperature') for d in data if d.get('optimal_temperature') is not None]
    if temperatures:
        print(f'温度预测数: {len(temperatures)}')
        print(f'温度范围: {min(temperatures):.2f} - {max(temperatures):.2f} °C')
        print(f'温度均值: {np.mean(temperatures):.2f} °C')

else:
    print('单个预测结果:')
    if 'growth_rate' in data:
        print(f'生长速率: {data[\"growth_rate\"]:.4f}')
    if 'optimal_temperature' in data:
        print(f'最适温度: {data[\"optimal_temperature\"]:.2f} °C')
"
}

# 生成使用指南
generate_usage_guide() {
    log_info "生成使用指南..."
    
    local guide_file="$OUTPUT_DIR/usage_guide.md"
    
    cat > "$guide_file" << EOF
# DeepMu 预测可视化使用指南

## 生成的可视化图表

### 1. 预测分布图
- **文件**: \`*_distribution.png\`
- **用途**: 查看预测值的分布特征
- **包含**: 直方图、箱线图、统计信息

### 2. 预测准确性分析 (如果提供真实值)
- **文件**: \`*_accuracy.png\`
- **用途**: 评估预测准确性
- **包含**: 散点图、误差分布、性能指标

### 3. 预测总结报告
- **文件**: \`prediction_summary.md\`
- **用途**: 详细的预测结果分析
- **包含**: 统计信息、图表说明

## 如何解读结果

### 生长速率预测
- **单位**: h⁻¹ (每小时)
- **范围**: 通常在 0.01 - 10 之间
- **解释**: 值越大表示生长越快

### 温度预测
- **单位**: °C (摄氏度)
- **范围**: 通常在 0 - 100 之间
- **解释**: 微生物的最适生长温度

### 准确性指标
- **R²**: 决定系数，越接近1越好
- **RMSE**: 均方根误差，越小越好
- **MAE**: 平均绝对误差，越小越好

## 注意事项

1. **数据质量**: 确保输入特征的质量
2. **模型适用性**: 确认模型适用于目标微生物
3. **结果解释**: 结合生物学知识解释结果
4. **不确定性**: 考虑预测的不确定性

EOF
    
    log_info "使用指南已保存: $guide_file"
}

# 主函数
main() {
    # 检查环境
    check_python_env
    
    # 创建输出目录
    mkdir -p "$OUTPUT_DIR"
    
    # 分析预测结果
    analyze_predictions
    
    # 运行可视化
    run_visualization
    
    # 生成使用指南
    generate_usage_guide
    
    log_info "DeepMu预测可视化完成！"
    log_info "输出目录: $OUTPUT_DIR"
    
    # 提供使用建议
    if [[ "$PREDICTION_TYPE" == "batch" ]]; then
        log_info "💡 批量预测结果包含分布分析和统计信息"
    else
        log_info "💡 单个预测结果，建议进行批量预测以获得更多分析"
    fi
    
    if [[ -z "$TRUE_VALUES_FILE" ]]; then
        log_warn "💡 提供真实值文件可以进行准确性分析"
    fi
}

# 捕获中断信号
trap 'log_error "可视化被中断"; exit 1' INT TERM

# 执行主函数
main "$@"
