#!/bin/bash

# DeepMu 训练可视化脚本
# 
# 这个脚本用于可视化DeepMu模型的训练过程
# 
# 作者: DeepMu 开发团队
# 版本: 2.0.0
# 日期: 2025-06-29

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
DeepMu 训练可视化脚本

用法: $0 [选项] <模型目录>

选项:
    -h, --help              显示此帮助信息
    -o, --output-dir DIR    输出目录 (默认: 模型目录/visualizations)
    --no-curves            跳过训练曲线
    --no-performance       跳过性能总结
    --no-report            跳过综合报告
    -v, --verbose          详细输出

示例:
    $0 models/deepmu_multi_task
    $0 models/deepmu_growth_rate --output-dir my_visualizations
    $0 models/deepmu_temperature --no-curves --verbose

支持的模型类型:
    - 多任务模型 (multi_task)
    - 生长速率模型 (growth_rate)  
    - 温度预测模型 (temperature)

EOF
}

# 默认参数
MODEL_DIR=""
OUTPUT_DIR=""
NO_CURVES=false
NO_PERFORMANCE=false
NO_REPORT=false
VERBOSE=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -o|--output-dir)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        --no-curves)
            NO_CURVES=true
            shift
            ;;
        --no-performance)
            NO_PERFORMANCE=true
            shift
            ;;
        --no-report)
            NO_REPORT=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -*)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
        *)
            if [[ -z "$MODEL_DIR" ]]; then
                MODEL_DIR="$1"
            else
                log_error "多余的参数: $1"
                show_help
                exit 1
            fi
            shift
            ;;
    esac
done

# 检查必需参数
if [[ -z "$MODEL_DIR" ]]; then
    log_error "请指定模型目录"
    show_help
    exit 1
fi

# 检查模型目录是否存在
if [[ ! -d "$MODEL_DIR" ]]; then
    log_error "模型目录不存在: $MODEL_DIR"
    exit 1
fi

# 检查训练历史文件
HISTORY_FILE="$MODEL_DIR/training_history.json"
if [[ ! -f "$HISTORY_FILE" ]]; then
    log_error "训练历史文件不存在: $HISTORY_FILE"
    log_info "请确保模型已完成训练"
    exit 1
fi

# 设置默认输出目录
if [[ -z "$OUTPUT_DIR" ]]; then
    OUTPUT_DIR="$MODEL_DIR/visualizations"
fi

# 检测模型类型
detect_model_type() {
    if [[ -f "$MODEL_DIR/multi_task_model.pt" ]]; then
        echo "multi_task"
    elif [[ -f "$MODEL_DIR/growth_rate_model.pt" ]]; then
        echo "growth_rate"
    elif [[ -f "$MODEL_DIR/temperature_model.pt" ]]; then
        echo "temperature"
    else
        echo "unknown"
    fi
}

MODEL_TYPE=$(detect_model_type)

log_info "开始DeepMu训练可视化"
log_info "模型目录: $MODEL_DIR"
log_info "模型类型: $MODEL_TYPE"
log_info "输出目录: $OUTPUT_DIR"

# 检查Python环境
check_python_env() {
    log_info "检查Python环境..."
    
    if ! command -v python3 &> /dev/null; then
        log_error "Python3 未找到"
        exit 1
    fi
    
    # 检查必要的Python包
    local required_packages=("matplotlib" "seaborn" "pandas" "numpy")
    for package in "${required_packages[@]}"; do
        if ! python3 -c "import $package" &> /dev/null; then
            log_error "Python包 '$package' 未安装"
            log_info "请运行: pip install $package"
            exit 1
        fi
    done
    
    log_info "Python环境检查通过"
}

# 运行可视化
run_visualization() {
    log_info "开始生成训练可视化..."
    
    # 构建Python命令
    local python_cmd="python3 visualization/training/training_visualizer.py"
    python_cmd="$python_cmd \"$MODEL_DIR\""
    
    if [[ -n "$OUTPUT_DIR" ]]; then
        python_cmd="$python_cmd --output-dir \"$OUTPUT_DIR\""
    fi
    
    if [[ "$NO_CURVES" == "true" ]]; then
        python_cmd="$python_cmd --no-curves"
    fi
    
    if [[ "$NO_PERFORMANCE" == "true" ]]; then
        python_cmd="$python_cmd --no-performance"
    fi
    
    if [[ "$NO_REPORT" == "true" ]]; then
        python_cmd="$python_cmd --no-report"
    fi
    
    if [[ "$VERBOSE" == "true" ]]; then
        log_info "执行命令: $python_cmd"
    fi
    
    # 执行可视化
    if eval "$python_cmd"; then
        log_info "训练可视化完成！"
        
        # 显示生成的文件
        if [[ -d "$OUTPUT_DIR" ]]; then
            log_info "生成的文件:"
            find "$OUTPUT_DIR" -name "*.png" -o -name "*.md" | while read -r file; do
                echo "  - $(basename "$file")"
            done
        fi
        
    else
        log_error "可视化失败！"
        exit 1
    fi
}

# 生成总结报告
generate_summary() {
    log_info "生成可视化总结..."
    
    local summary_file="$OUTPUT_DIR/visualization_summary.txt"
    
    cat > "$summary_file" << EOF
DeepMu 训练可视化总结
==================

生成时间: $(date)
模型目录: $MODEL_DIR
模型类型: $MODEL_TYPE
输出目录: $OUTPUT_DIR

生成的文件:
EOF
    
    if [[ -d "$OUTPUT_DIR" ]]; then
        find "$OUTPUT_DIR" -name "*.png" -o -name "*.md" | sort | while read -r file; do
            echo "- $(basename "$file")" >> "$summary_file"
        done
    fi
    
    cat >> "$summary_file" << EOF

使用说明:
1. 查看训练曲线图了解训练过程
2. 查看性能总结图评估模型效果
3. 阅读综合报告获取详细分析

EOF
    
    log_info "可视化总结已保存: $summary_file"
}

# 主函数
main() {
    # 检查环境
    check_python_env
    
    # 创建输出目录
    mkdir -p "$OUTPUT_DIR"
    
    # 运行可视化
    run_visualization
    
    # 生成总结
    generate_summary
    
    log_info "DeepMu训练可视化完成！"
    log_info "输出目录: $OUTPUT_DIR"
    
    # 如果是多任务模型，提供额外信息
    if [[ "$MODEL_TYPE" == "multi_task" ]]; then
        log_info "💡 多任务模型可视化包含生长速率和温度预测两个任务的分析"
    fi
    
    # 如果有交叉验证，提供额外信息
    if [[ "$MODEL_TYPE" == "growth_rate" || "$MODEL_TYPE" == "temperature" ]]; then
        log_info "💡 单任务模型可视化包含交叉验证结果分析"
    fi
}

# 捕获中断信号
trap 'log_error "可视化被中断"; exit 1' INT TERM

# 执行主函数
main "$@"
