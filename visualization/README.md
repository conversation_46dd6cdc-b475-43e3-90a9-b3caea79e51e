# DeepMu 可视化系统

DeepMu 可视化系统提供了全面的模型训练、预测和可解释性分析可视化功能。

## 🎨 功能概览

### 1. 训练过程可视化
- **训练曲线**: 损失函数、性能指标随时间变化
- **多任务分析**: 生长速率和温度预测任务的对比
- **交叉验证**: 不同折之间的性能比较
- **学习率调度**: 学习率变化可视化

### 2. 预测结果可视化
- **分布分析**: 预测值的统计分布
- **准确性评估**: 预测值与真实值的对比
- **误差分析**: 预测误差的分布和模式
- **置信区间**: 预测不确定性分析

### 3. 可解释性分析
- **特征重要性**: 随机森林和梯度重要性分析
- **特征相关性**: 特征间的相关性热图
- **特征分布**: 重要特征的分布特征
- **生物学解释**: 结合领域知识的解释指导

## 📁 文件结构

```
visualization/
├── training/
│   └── training_visualizer.py          # 训练过程可视化器
├── prediction/
│   └── prediction_visualizer.py        # 预测结果可视化器
├── interpretability/
│   └── interpretability_analyzer.py    # 可解释性分析器
├── deepmu_visualizer.py               # 综合可视化管理器
├── visualize_training.sh              # 训练可视化Shell脚本
├── visualize_predictions.sh           # 预测可视化Shell脚本
├── analyze_interpretability.sh        # 可解释性分析Shell脚本
├── run_visualization.sh               # 总控Shell脚本
└── README.md                          # 本文档
```

## 🚀 快速开始

### 1. 环境准备

确保安装了必要的Python包：

```bash
pip install matplotlib seaborn pandas numpy torch scikit-learn scipy
```

### 2. 基本使用

#### 训练过程可视化

```bash
# 使用Shell脚本
./visualization/visualize_training.sh models/deepmu_multi_task

# 使用Python脚本
python visualization/training/training_visualizer.py models/deepmu_multi_task

# 使用总控脚本
./visualization/run_visualization.sh training models/deepmu_multi_task
```

#### 预测结果可视化

```bash
# 基本预测可视化
./visualization/visualize_predictions.sh results/predictions.json

# 包含准确性分析
./visualization/visualize_predictions.sh results/predictions.json \
    --true-values training_data/metadata.tsv

# 使用总控脚本
./visualization/run_visualization.sh prediction results/predictions.json
```

#### 可解释性分析

```bash
# 完整可解释性分析
./visualization/analyze_interpretability.sh \
    models/deepmu_multi_task \
    training_data/combined_features.tsv

# 使用总控脚本
./visualization/run_visualization.sh interpretability \
    models/deepmu_multi_task \
    training_data/combined_features.tsv
```

#### 综合分析

```bash
# 生成综合分析报告
./visualization/run_visualization.sh comprehensive models/deepmu_multi_task \
    --features training_data/combined_features.tsv \
    --prediction results/predictions.json
```

### 3. 演示模式

```bash
# 运行演示（自动选择可用模型）
./visualization/run_visualization.sh demo

# 列出可用模型
./visualization/run_visualization.sh list-models
```

## 📊 输出说明

### 训练可视化输出

```
models/deepmu_multi_task/visualizations/
├── multi_task_training_curves.png      # 多任务训练曲线
├── multi_task_performance.png          # 性能总结
├── training_visualization_report.md    # 详细报告
└── visualization_summary.txt           # 总结信息
```

### 预测可视化输出

```
prediction_visualizations/
├── multi_task_prediction_distribution.png  # 预测分布
├── multi_task_prediction_accuracy.png      # 准确性分析
├── prediction_summary.md                   # 预测总结
└── usage_guide.md                          # 使用指南
```

### 可解释性分析输出

```
models/deepmu_multi_task/interpretability/
├── growth_rate_rf_feature_importance.png   # 生长速率特征重要性
├── temperature_rf_feature_importance.png   # 温度特征重要性
├── feature_variance_importance.png         # 方差重要性
├── feature_correlation_heatmap.png         # 相关性热图
├── feature_distributions.png               # 特征分布
├── high_correlation_pairs.csv              # 高相关性特征对
├── interpretability_report.md              # 综合报告
└── interpretation_guide.md                 # 解释指导
```

## 🔧 高级用法

### 1. 自定义输出目录

```bash
# 指定自定义输出目录
./visualization/visualize_training.sh models/deepmu_multi_task \
    --output-dir my_custom_output

# 跳过某些分析
./visualization/analyze_interpretability.sh \
    models/deepmu_multi_task \
    training_data/combined_features.tsv \
    --no-correlation --no-distribution
```

### 2. 批量处理

```bash
# 批量处理所有模型
for model_dir in models/deepmu_*; do
    if [[ -d "$model_dir" ]]; then
        echo "处理模型: $model_dir"
        ./visualization/visualize_training.sh "$model_dir"
    fi
done
```

### 3. Python API使用

```python
from visualization.deepmu_visualizer import DeepMuVisualizer

# 创建可视化管理器
visualizer = DeepMuVisualizer()

# 训练可视化
visualizer.visualize_training('models/deepmu_multi_task')

# 预测可视化
visualizer.visualize_predictions(
    'results/predictions.json',
    true_values_file='training_data/metadata.tsv'
)

# 可解释性分析
visualizer.analyze_interpretability(
    'models/deepmu_multi_task',
    'training_data/combined_features.tsv'
)

# 综合分析
visualizer.create_comprehensive_analysis(
    'models/deepmu_multi_task',
    features_file='training_data/combined_features.tsv',
    prediction_file='results/predictions.json'
)
```

## 📈 可视化图表说明

### 1. 训练曲线图

- **损失曲线**: 显示训练和验证损失的变化
- **性能指标**: R²、RMSE、MAE等指标的变化
- **学习率**: 学习率调度的可视化
- **早停**: 早停触发点的标记

### 2. 预测分析图

- **散点图**: 预测值vs真实值的对比
- **分布图**: 预测值的统计分布
- **误差图**: 预测误差的分布和模式
- **箱线图**: 预测值的四分位数分析

### 3. 可解释性图表

- **重要性排序**: 特征重要性的排序条形图
- **相关性热图**: 特征间相关性的热图
- **分布直方图**: 重要特征的分布特征
- **Q-Q图**: 特征分布的正态性检验

## 🎯 最佳实践

### 1. 训练监控

- 定期运行训练可视化检查模型收敛
- 关注验证损失避免过拟合
- 监控学习率调度的效果
- 比较不同模型的训练曲线

### 2. 预测评估

- 使用真实值进行准确性分析
- 检查预测分布的合理性
- 分析预测误差的模式
- 评估模型的泛化能力

### 3. 可解释性分析

- 结合生物学知识解释重要特征
- 识别和处理高相关性特征
- 分析特征分布的异常情况
- 验证模型决策的合理性

## ⚠️ 注意事项

1. **数据质量**: 确保输入数据的质量和完整性
2. **内存使用**: 大型数据集可能需要较多内存
3. **计算时间**: 可解释性分析可能需要较长时间
4. **文件路径**: 确保所有文件路径正确
5. **权限设置**: 确保脚本有执行权限

## 🔍 故障排除

### 常见问题

1. **Python包缺失**
   ```bash
   pip install matplotlib seaborn pandas numpy torch scikit-learn scipy
   ```

2. **权限错误**
   ```bash
   chmod +x visualization/*.sh
   ```

3. **文件不存在**
   - 检查模型目录是否包含训练历史文件
   - 确认特征文件路径正确
   - 验证预测结果文件格式

4. **内存不足**
   - 减少特征数量或样本数量
   - 使用更小的图表尺寸
   - 分批处理大型数据集

### 获取帮助

```bash
# 查看总体帮助
./visualization/run_visualization.sh help

# 查看特定命令帮助
./visualization/visualize_training.sh --help
./visualization/visualize_predictions.sh --help
./visualization/analyze_interpretability.sh --help
```

## 📞 技术支持

如果遇到问题，请：

1. 检查错误日志和输出信息
2. 确认环境配置和依赖安装
3. 查看相关文档和示例
4. 联系DeepMu开发团队

---

**DeepMu开发团队** © 2025
