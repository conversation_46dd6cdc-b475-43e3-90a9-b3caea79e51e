#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
DeepMu 训练过程可视化器

这个脚本用于可视化DeepMu模型的训练过程，包括：
1. 训练和验证损失曲线
2. 性能指标变化
3. 学习率调度
4. 预测散点图
5. 交叉验证结果比较

作者: DeepMu 开发团队
版本: 2.0.0
日期: 2025-06-29
"""

import os
import sys
import json
import argparse
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import warnings

warnings.filterwarnings('ignore')

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")
sns.set_palette("husl")

class TrainingVisualizer:
    """训练过程可视化器"""
    
    def __init__(self, model_dir: str, output_dir: str = None):
        """
        初始化可视化器
        
        Args:
            model_dir: 模型目录路径
            output_dir: 输出目录路径
        """
        self.model_dir = Path(model_dir)
        self.output_dir = Path(output_dir) if output_dir else self.model_dir / "visualizations"
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 加载训练历史
        self.history = self._load_training_history()
        self.config = self._load_config()
        self.model_type = self._detect_model_type()
        
        print(f"📊 检测到模型类型: {self.model_type}")
        print(f"📁 输出目录: {self.output_dir}")
    
    def _load_training_history(self) -> Dict:
        """加载训练历史"""
        history_file = self.model_dir / "training_history.json"
        if not history_file.exists():
            raise FileNotFoundError(f"训练历史文件不存在: {history_file}")
        
        with open(history_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def _load_config(self) -> Dict:
        """加载配置文件"""
        config_file = self.model_dir / "config.json"
        if config_file.exists():
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}
    
    def _detect_model_type(self) -> str:
        """检测模型类型"""
        if 'fold_histories' in self.history:
            if 'cv_metrics' in self.history and 'mae_folds' in self.history['cv_metrics']:
                return 'temperature'
            else:
                return 'growth_rate'
        elif 'train_growth_loss' in self.history:
            return 'multi_task'
        else:
            return 'unknown'
    
    def create_training_curves(self):
        """创建训练曲线图"""
        if self.model_type == 'multi_task':
            self._plot_multi_task_curves()
        elif self.model_type in ['growth_rate', 'temperature']:
            self._plot_single_task_curves()
        else:
            print("⚠️  未知模型类型，跳过训练曲线绘制")
    
    def _plot_multi_task_curves(self):
        """绘制多任务训练曲线"""
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('DeepMu 多任务训练过程可视化', fontsize=16, fontweight='bold')
        
        epochs = range(1, len(self.history['train_total_loss']) + 1)
        
        # 总损失
        axes[0, 0].plot(epochs, self.history['train_total_loss'], 
                       label='训练总损失', linewidth=2, color='blue')
        axes[0, 0].plot(epochs, self.history['val_total_loss'], 
                       label='验证总损失', linewidth=2, color='red')
        axes[0, 0].set_title('总损失变化')
        axes[0, 0].set_xlabel('轮次')
        axes[0, 0].set_ylabel('损失')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # 生长速率损失
        axes[0, 1].plot(epochs, self.history['train_growth_loss'], 
                       label='训练生长损失', linewidth=2, color='green')
        axes[0, 1].plot(epochs, self.history['val_growth_loss'], 
                       label='验证生长损失', linewidth=2, color='orange')
        axes[0, 1].set_title('生长速率损失')
        axes[0, 1].set_xlabel('轮次')
        axes[0, 1].set_ylabel('损失')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
        
        # 温度损失
        axes[0, 2].plot(epochs, self.history['train_temp_loss'], 
                       label='训练温度损失', linewidth=2, color='purple')
        axes[0, 2].plot(epochs, self.history['val_temp_loss'], 
                       label='验证温度损失', linewidth=2, color='brown')
        axes[0, 2].set_title('温度预测损失')
        axes[0, 2].set_xlabel('轮次')
        axes[0, 2].set_ylabel('损失')
        axes[0, 2].legend()
        axes[0, 2].grid(True, alpha=0.3)
        
        # 生长速率R²
        axes[1, 0].plot(epochs, self.history['train_growth_r2'], 
                       label='训练生长R²', linewidth=2, color='green')
        axes[1, 0].plot(epochs, self.history['val_growth_r2'], 
                       label='验证生长R²', linewidth=2, color='orange')
        axes[1, 0].set_title('生长速率R²')
        axes[1, 0].set_xlabel('轮次')
        axes[1, 0].set_ylabel('R²')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
        
        # 温度R²
        axes[1, 1].plot(epochs, self.history['train_temp_r2'], 
                       label='训练温度R²', linewidth=2, color='purple')
        axes[1, 1].plot(epochs, self.history['val_temp_r2'], 
                       label='验证温度R²', linewidth=2, color='brown')
        axes[1, 1].set_title('温度预测R²')
        axes[1, 1].set_xlabel('轮次')
        axes[1, 1].set_ylabel('R²')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)
        
        # 学习率
        axes[1, 2].plot(epochs, self.history['learning_rate'], 
                       linewidth=2, color='red')
        axes[1, 2].set_title('学习率变化')
        axes[1, 2].set_xlabel('轮次')
        axes[1, 2].set_ylabel('学习率')
        axes[1, 2].set_yscale('log')
        axes[1, 2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'multi_task_training_curves.png', 
                   dpi=300, bbox_inches='tight')
        plt.close()
        
        print("✅ 多任务训练曲线已保存")
    
    def _plot_single_task_curves(self):
        """绘制单任务训练曲线（交叉验证）"""
        if 'fold_histories' not in self.history:
            print("⚠️  没有交叉验证历史，跳过绘制")
            return
        
        fold_histories = self.history['fold_histories']
        n_folds = len(fold_histories)
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        task_name = "温度预测" if self.model_type == 'temperature' else "生长速率预测"
        fig.suptitle(f'DeepMu {task_name} 交叉验证训练过程', fontsize=16, fontweight='bold')
        
        # 收集所有折的数据
        all_train_loss = []
        all_val_loss = []
        all_train_r2 = []
        all_val_r2 = []
        
        colors = plt.cm.tab10(np.linspace(0, 1, n_folds))
        
        for i, fold_history in enumerate(fold_histories):
            epochs = range(1, len(fold_history['train_loss']) + 1)
            
            # 训练和验证损失
            axes[0, 0].plot(epochs, fold_history['train_loss'], 
                           color=colors[i], alpha=0.7, linewidth=1,
                           label=f'折{i+1}训练' if i < 3 else "")
            axes[0, 1].plot(epochs, fold_history['val_loss'], 
                           color=colors[i], alpha=0.7, linewidth=1,
                           label=f'折{i+1}验证' if i < 3 else "")
            
            # R²
            axes[1, 0].plot(epochs, fold_history['train_r2'], 
                           color=colors[i], alpha=0.7, linewidth=1)
            axes[1, 1].plot(epochs, fold_history['val_r2'], 
                           color=colors[i], alpha=0.7, linewidth=1)
            
            all_train_loss.extend(fold_history['train_loss'])
            all_val_loss.extend(fold_history['val_loss'])
            all_train_r2.extend(fold_history['train_r2'])
            all_val_r2.extend(fold_history['val_r2'])
        
        # 设置图表
        axes[0, 0].set_title('训练损失')
        axes[0, 0].set_xlabel('轮次')
        axes[0, 0].set_ylabel('损失')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        axes[0, 1].set_title('验证损失')
        axes[0, 1].set_xlabel('轮次')
        axes[0, 1].set_ylabel('损失')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
        
        axes[1, 0].set_title('训练R²')
        axes[1, 0].set_xlabel('轮次')
        axes[1, 0].set_ylabel('R²')
        axes[1, 0].grid(True, alpha=0.3)
        
        axes[1, 1].set_title('验证R²')
        axes[1, 1].set_xlabel('轮次')
        axes[1, 1].set_ylabel('R²')
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(self.output_dir / f'{self.model_type}_cv_training_curves.png', 
                   dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"✅ {task_name}交叉验证训练曲线已保存")
    
    def create_performance_summary(self):
        """创建性能总结图"""
        if self.model_type == 'multi_task':
            self._plot_multi_task_performance()
        elif self.model_type in ['growth_rate', 'temperature']:
            self._plot_cv_performance()
    
    def _plot_multi_task_performance(self):
        """绘制多任务性能总结"""
        if 'test_metrics' not in self.history:
            print("⚠️  没有测试指标，跳过性能总结")
            return
        
        test_metrics = self.history['test_metrics']
        
        fig, axes = plt.subplots(1, 2, figsize=(15, 6))
        fig.suptitle('DeepMu 多任务模型测试性能', fontsize=16, fontweight='bold')
        
        # 生长速率预测散点图
        if 'growth' in test_metrics and 'predictions' in test_metrics:
            growth_targets = test_metrics['predictions']['growth_targets']
            growth_preds = test_metrics['predictions']['growth']
            
            axes[0].scatter(growth_targets, growth_preds, alpha=0.6, s=30)
            
            # 添加对角线
            min_val = min(min(growth_targets), min(growth_preds))
            max_val = max(max(growth_targets), max(growth_preds))
            axes[0].plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2)
            
            axes[0].set_xlabel('真实生长速率')
            axes[0].set_ylabel('预测生长速率')
            axes[0].set_title(f'生长速率预测 (R² = {test_metrics["growth"]["r2"]:.4f})')
            axes[0].grid(True, alpha=0.3)
        
        # 温度预测散点图
        if 'temperature' in test_metrics and 'predictions' in test_metrics:
            temp_targets = test_metrics['predictions']['temperature_targets']
            temp_preds = test_metrics['predictions']['temperature']
            
            axes[1].scatter(temp_targets, temp_preds, alpha=0.6, s=30, color='orange')
            
            # 添加对角线
            min_val = min(min(temp_targets), min(temp_preds))
            max_val = max(max(temp_targets), max(temp_preds))
            axes[1].plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2)
            
            axes[1].set_xlabel('真实温度 (°C)')
            axes[1].set_ylabel('预测温度 (°C)')
            axes[1].set_title(f'温度预测 (R² = {test_metrics["temperature"]["r2"]:.4f})')
            axes[1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'multi_task_performance.png', 
                   dpi=300, bbox_inches='tight')
        plt.close()
        
        print("✅ 多任务性能总结已保存")
    
    def _plot_cv_performance(self):
        """绘制交叉验证性能总结"""
        if 'cv_metrics' not in self.history:
            print("⚠️  没有交叉验证指标，跳过性能总结")
            return
        
        cv_metrics = self.history['cv_metrics']
        
        fig, axes = plt.subplots(1, 2, figsize=(12, 5))
        task_name = "温度预测" if self.model_type == 'temperature' else "生长速率预测"
        fig.suptitle(f'DeepMu {task_name} 交叉验证性能', fontsize=16, fontweight='bold')
        
        # R²分布
        r2_folds = cv_metrics['r2_folds']
        axes[0].bar(range(1, len(r2_folds) + 1), r2_folds, alpha=0.7, color='skyblue')
        axes[0].axhline(y=cv_metrics['r2_mean'], color='red', linestyle='--', 
                       label=f'平均值: {cv_metrics["r2_mean"]:.4f}')
        axes[0].set_xlabel('折数')
        axes[0].set_ylabel('R²')
        axes[0].set_title('各折R²分布')
        axes[0].legend()
        axes[0].grid(True, alpha=0.3)
        
        # 误差分布
        if self.model_type == 'temperature':
            error_folds = cv_metrics['mae_folds']
            error_name = 'MAE (°C)'
            error_mean = cv_metrics['mae_mean']
        else:
            error_folds = cv_metrics['rmse_folds']
            error_name = 'RMSE'
            error_mean = cv_metrics['rmse_mean']
        
        axes[1].bar(range(1, len(error_folds) + 1), error_folds, alpha=0.7, color='lightcoral')
        axes[1].axhline(y=error_mean, color='blue', linestyle='--', 
                       label=f'平均值: {error_mean:.4f}')
        axes[1].set_xlabel('折数')
        axes[1].set_ylabel(error_name)
        axes[1].set_title(f'各折{error_name}分布')
        axes[1].legend()
        axes[1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(self.output_dir / f'{self.model_type}_cv_performance.png', 
                   dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"✅ {task_name}交叉验证性能总结已保存")
    
    def create_comprehensive_report(self):
        """创建综合报告"""
        report_path = self.output_dir / 'training_visualization_report.md'
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# DeepMu 训练可视化报告\n\n")
            f.write(f"**生成时间**: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"**模型类型**: {self.model_type}\n")
            f.write(f"**模型目录**: {self.model_dir}\n\n")
            
            # 配置信息
            if self.config:
                f.write("## 训练配置\n\n")
                f.write("```json\n")
                f.write(json.dumps(self.config, indent=2, ensure_ascii=False))
                f.write("\n```\n\n")
            
            # 性能总结
            f.write("## 性能总结\n\n")
            
            if self.model_type == 'multi_task' and 'test_metrics' in self.history:
                test_metrics = self.history['test_metrics']
                f.write("### 多任务测试结果\n\n")
                f.write(f"- **生长速率 R²**: {test_metrics['growth']['r2']:.4f}\n")
                f.write(f"- **生长速率 RMSE**: {test_metrics['growth']['rmse']:.6f}\n")
                f.write(f"- **温度预测 R²**: {test_metrics['temperature']['r2']:.4f}\n")
                f.write(f"- **温度预测 MAE**: {test_metrics['temperature']['mae']:.2f}°C\n\n")
            
            elif 'cv_metrics' in self.history:
                cv_metrics = self.history['cv_metrics']
                f.write("### 交叉验证结果\n\n")
                f.write(f"- **平均 R²**: {cv_metrics['r2_mean']:.4f} ± {cv_metrics['r2_std']:.4f}\n")
                
                if self.model_type == 'temperature':
                    f.write(f"- **平均 MAE**: {cv_metrics['mae_mean']:.2f} ± {cv_metrics['mae_std']:.2f}°C\n")
                else:
                    f.write(f"- **平均 RMSE**: {cv_metrics['rmse_mean']:.6f} ± {cv_metrics['rmse_std']:.6f}\n")
                
                f.write(f"- **最佳折**: {self.history['best_fold']}\n\n")
            
            # 生成的图表
            f.write("## 生成的可视化图表\n\n")
            
            for img_file in self.output_dir.glob("*.png"):
                f.write(f"### {img_file.stem}\n\n")
                f.write(f"![{img_file.stem}]({img_file.name})\n\n")
        
        print(f"✅ 综合报告已保存: {report_path}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='DeepMu 训练可视化器')
    parser.add_argument('model_dir', type=str, help='模型目录路径')
    parser.add_argument('--output-dir', type=str, help='输出目录路径')
    parser.add_argument('--no-curves', action='store_true', help='跳过训练曲线')
    parser.add_argument('--no-performance', action='store_true', help='跳过性能总结')
    parser.add_argument('--no-report', action='store_true', help='跳过综合报告')
    
    args = parser.parse_args()
    
    try:
        # 创建可视化器
        visualizer = TrainingVisualizer(args.model_dir, args.output_dir)
        
        # 生成可视化
        if not args.no_curves:
            visualizer.create_training_curves()
        
        if not args.no_performance:
            visualizer.create_performance_summary()
        
        if not args.no_report:
            visualizer.create_comprehensive_report()
        
        print(f"\n🎉 训练可视化完成！")
        print(f"📁 输出目录: {visualizer.output_dir}")
        
    except Exception as e:
        print(f"❌ 可视化过程中出现错误: {e}")
        raise

if __name__ == "__main__":
    main()
