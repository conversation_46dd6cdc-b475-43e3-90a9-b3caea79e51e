#!/bin/bash

# DeepMu 可解释性分析脚本
# 
# 这个脚本用于分析DeepMu模型的可解释性
# 
# 作者: DeepMu 开发团队
# 版本: 2.0.0
# 日期: 2025-06-29

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
DeepMu 可解释性分析脚本

用法: $0 [选项] <模型目录> <特征文件>

选项:
    -h, --help              显示此帮助信息
    -o, --output-dir DIR    输出目录 (默认: 模型目录/interpretability)
    --no-rf                跳过随机森林分析
    --no-gradient          跳过梯度分析
    --no-correlation       跳过相关性分析
    --no-distribution      跳过分布分析
    --no-report            跳过综合报告
    -v, --verbose          详细输出

参数:
    模型目录               训练好的模型目录路径
    特征文件               特征数据文件路径 (.tsv格式)

示例:
    $0 models/deepmu_multi_task training_data/combined_features.tsv
    $0 models/deepmu_growth_rate training_data/combined_features.tsv --verbose
    $0 models/deepmu_temperature training_data/combined_features.tsv --output-dir my_analysis

分析内容:
    - 随机森林特征重要性分析
    - 特征方差重要性分析
    - 特征相关性分析
    - 特征分布分析
    - 综合可解释性报告

EOF
}

# 默认参数
MODEL_DIR=""
FEATURES_FILE=""
OUTPUT_DIR=""
NO_RF=false
NO_GRADIENT=false
NO_CORRELATION=false
NO_DISTRIBUTION=false
NO_REPORT=false
VERBOSE=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -o|--output-dir)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        --no-rf)
            NO_RF=true
            shift
            ;;
        --no-gradient)
            NO_GRADIENT=true
            shift
            ;;
        --no-correlation)
            NO_CORRELATION=true
            shift
            ;;
        --no-distribution)
            NO_DISTRIBUTION=true
            shift
            ;;
        --no-report)
            NO_REPORT=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -*)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
        *)
            if [[ -z "$MODEL_DIR" ]]; then
                MODEL_DIR="$1"
            elif [[ -z "$FEATURES_FILE" ]]; then
                FEATURES_FILE="$1"
            else
                log_error "多余的参数: $1"
                show_help
                exit 1
            fi
            shift
            ;;
    esac
done

# 检查必需参数
if [[ -z "$MODEL_DIR" ]]; then
    log_error "请指定模型目录"
    show_help
    exit 1
fi

if [[ -z "$FEATURES_FILE" ]]; then
    log_error "请指定特征文件"
    show_help
    exit 1
fi

# 检查文件和目录是否存在
if [[ ! -d "$MODEL_DIR" ]]; then
    log_error "模型目录不存在: $MODEL_DIR"
    exit 1
fi

if [[ ! -f "$FEATURES_FILE" ]]; then
    log_error "特征文件不存在: $FEATURES_FILE"
    exit 1
fi

# 设置默认输出目录
if [[ -z "$OUTPUT_DIR" ]]; then
    OUTPUT_DIR="$MODEL_DIR/interpretability"
fi

# 检测模型类型
detect_model_type() {
    if [[ -f "$MODEL_DIR/multi_task_model.pt" ]]; then
        echo "multi_task"
    elif [[ -f "$MODEL_DIR/growth_rate_model.pt" ]]; then
        echo "growth_rate"
    elif [[ -f "$MODEL_DIR/temperature_model.pt" ]]; then
        echo "temperature"
    else
        echo "unknown"
    fi
}

MODEL_TYPE=$(detect_model_type)

log_info "开始DeepMu可解释性分析"
log_info "模型目录: $MODEL_DIR"
log_info "模型类型: $MODEL_TYPE"
log_info "特征文件: $FEATURES_FILE"
log_info "输出目录: $OUTPUT_DIR"

# 检查Python环境
check_python_env() {
    log_info "检查Python环境..."
    
    if ! command -v python3 &> /dev/null; then
        log_error "Python3 未找到"
        exit 1
    fi
    
    # 检查必要的Python包
    local required_packages=("torch" "sklearn" "matplotlib" "seaborn" "pandas" "numpy")
    for package in "${required_packages[@]}"; do
        if ! python3 -c "import $package" &> /dev/null; then
            log_error "Python包 '$package' 未安装"
            log_info "请运行: pip install $package"
            exit 1
        fi
    done
    
    log_info "Python环境检查通过"
}

# 检查特征文件格式
check_features_file() {
    log_info "检查特征文件格式..."
    
    # 检查文件扩展名
    if [[ "$FEATURES_FILE" != *.tsv ]]; then
        log_warn "特征文件不是.tsv格式，请确保是制表符分隔的文件"
    fi
    
    # 检查文件内容
    local line_count=$(wc -l < "$FEATURES_FILE")
    local first_line=$(head -1 "$FEATURES_FILE")
    
    log_info "特征文件行数: $line_count"
    log_info "特征文件列数: $(echo "$first_line" | tr '\t' '\n' | wc -l)"
    
    if [[ $line_count -lt 2 ]]; then
        log_error "特征文件行数太少，至少需要2行（标题行+数据行）"
        exit 1
    fi
}

# 运行可解释性分析
run_analysis() {
    log_info "开始可解释性分析..."
    
    # 构建Python命令
    local python_cmd="python3 visualization/interpretability/interpretability_analyzer.py"
    python_cmd="$python_cmd \"$MODEL_DIR\" \"$FEATURES_FILE\""
    
    if [[ -n "$OUTPUT_DIR" ]]; then
        python_cmd="$python_cmd --output-dir \"$OUTPUT_DIR\""
    fi
    
    if [[ "$NO_RF" == "true" ]]; then
        python_cmd="$python_cmd --no-rf"
    fi
    
    if [[ "$NO_GRADIENT" == "true" ]]; then
        python_cmd="$python_cmd --no-gradient"
    fi
    
    if [[ "$NO_CORRELATION" == "true" ]]; then
        python_cmd="$python_cmd --no-correlation"
    fi
    
    if [[ "$NO_DISTRIBUTION" == "true" ]]; then
        python_cmd="$python_cmd --no-distribution"
    fi
    
    if [[ "$NO_REPORT" == "true" ]]; then
        python_cmd="$python_cmd --no-report"
    fi
    
    if [[ "$VERBOSE" == "true" ]]; then
        log_info "执行命令: $python_cmd"
    fi
    
    # 执行分析
    if eval "$python_cmd"; then
        log_info "可解释性分析完成！"
        
        # 显示生成的文件
        if [[ -d "$OUTPUT_DIR" ]]; then
            log_info "生成的文件:"
            find "$OUTPUT_DIR" -name "*.png" -o -name "*.md" -o -name "*.csv" | while read -r file; do
                echo "  - $(basename "$file")"
            done
        fi
        
    else
        log_error "分析失败！"
        exit 1
    fi
}

# 生成分析总结
generate_analysis_summary() {
    log_info "生成分析总结..."
    
    local summary_file="$OUTPUT_DIR/analysis_summary.txt"
    
    cat > "$summary_file" << EOF
DeepMu 可解释性分析总结
====================

生成时间: $(date)
模型目录: $MODEL_DIR
模型类型: $MODEL_TYPE
特征文件: $FEATURES_FILE
输出目录: $OUTPUT_DIR

分析内容:
EOF
    
    if [[ "$NO_RF" != "true" ]]; then
        echo "✓ 随机森林特征重要性分析" >> "$summary_file"
    fi
    
    if [[ "$NO_GRADIENT" != "true" ]]; then
        echo "✓ 梯度/方差重要性分析" >> "$summary_file"
    fi
    
    if [[ "$NO_CORRELATION" != "true" ]]; then
        echo "✓ 特征相关性分析" >> "$summary_file"
    fi
    
    if [[ "$NO_DISTRIBUTION" != "true" ]]; then
        echo "✓ 特征分布分析" >> "$summary_file"
    fi
    
    if [[ "$NO_REPORT" != "true" ]]; then
        echo "✓ 综合可解释性报告" >> "$summary_file"
    fi
    
    cat >> "$summary_file" << EOF

生成的文件:
EOF
    
    if [[ -d "$OUTPUT_DIR" ]]; then
        find "$OUTPUT_DIR" -name "*.png" -o -name "*.md" -o -name "*.csv" | sort | while read -r file; do
            echo "- $(basename "$file")" >> "$summary_file"
        done
    fi
    
    cat >> "$summary_file" << EOF

使用建议:
1. 查看特征重要性图识别关键特征
2. 分析特征相关性避免冗余
3. 检查特征分布了解数据特征
4. 阅读综合报告获取详细分析
5. 结合生物学知识解释结果

EOF
    
    log_info "分析总结已保存: $summary_file"
}

# 提供解释指导
provide_interpretation_guide() {
    log_info "生成解释指导..."
    
    local guide_file="$OUTPUT_DIR/interpretation_guide.md"
    
    cat > "$guide_file" << EOF
# DeepMu 可解释性分析解释指导

## 特征重要性分析

### 随机森林重要性
- **高重要性特征**: 对预测结果影响最大的特征
- **生物学意义**: 结合基因功能注释理解重要特征
- **特征选择**: 可以基于重要性进行特征筛选

### 方差重要性
- **高方差特征**: 在样本间变化较大的特征
- **信息量**: 高方差通常意味着更多信息
- **数据质量**: 检查异常高方差是否由数据错误引起

## 特征相关性分析

### 高相关性特征对
- **冗余特征**: 高相关性可能表示特征冗余
- **功能相关**: 生物学上相关的基因可能表现出高相关性
- **降维考虑**: 可以考虑移除高相关性特征之一

### 相关性热图
- **颜色深浅**: 表示相关性强弱
- **聚类模式**: 相似功能的特征可能聚集在一起
- **异常模式**: 识别意外的相关性模式

## 特征分布分析

### 分布形状
- **正态分布**: 大多数机器学习算法的理想输入
- **偏态分布**: 可能需要数据变换
- **多峰分布**: 可能表示不同的生物学状态

### 异常值
- **极端值**: 可能是测量错误或特殊样本
- **生物学意义**: 某些异常值可能有重要生物学意义
- **处理策略**: 根据具体情况决定是否移除

## 模型特定解释

### 多任务模型
- **共享特征**: 对两个任务都重要的特征
- **任务特异性**: 只对某个任务重要的特征
- **权衡分析**: 分析两个任务间的权衡关系

### 单任务模型
- **专门化特征**: 针对特定任务优化的特征
- **交叉验证**: 不同折间的特征重要性一致性
- **泛化能力**: 重要特征的稳定性

## 生物学解释建议

### 基因功能分类
- **代谢通路**: 分析重要特征涉及的代谢通路
- **调控网络**: 考虑基因调控关系
- **进化保守性**: 保守基因可能更重要

### 环境适应性
- **温度适应**: 温度相关基因的重要性
- **营养利用**: 代谢相关基因的作用
- **应激响应**: 应激相关基因的贡献

## 注意事项

1. **统计显著性**: 重要性分数的统计意义
2. **样本偏差**: 训练数据的代表性
3. **模型局限**: 模型假设的影响
4. **领域知识**: 结合生物学先验知识
5. **验证实验**: 通过实验验证重要发现

EOF
    
    log_info "解释指导已保存: $guide_file"
}

# 主函数
main() {
    # 检查环境
    check_python_env
    
    # 检查特征文件
    check_features_file
    
    # 创建输出目录
    mkdir -p "$OUTPUT_DIR"
    
    # 运行分析
    run_analysis
    
    # 生成总结
    generate_analysis_summary
    
    # 提供解释指导
    provide_interpretation_guide
    
    log_info "DeepMu可解释性分析完成！"
    log_info "输出目录: $OUTPUT_DIR"
    
    # 根据模型类型提供特定建议
    case $MODEL_TYPE in
        "multi_task")
            log_info "💡 多任务模型分析包含生长速率和温度预测的特征重要性对比"
            ;;
        "growth_rate")
            log_info "💡 生长速率模型分析专注于影响微生物生长的关键特征"
            ;;
        "temperature")
            log_info "💡 温度预测模型分析专注于影响温度适应性的关键特征"
            ;;
        *)
            log_warn "⚠️  未知模型类型，分析结果可能不完整"
            ;;
    esac
    
    log_info "📖 请查看 interpretation_guide.md 获取详细的解释指导"
}

# 捕获中断信号
trap 'log_error "分析被中断"; exit 1' INT TERM

# 执行主函数
main "$@"
