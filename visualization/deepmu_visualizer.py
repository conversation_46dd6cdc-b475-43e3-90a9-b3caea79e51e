#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
DeepMu 综合可视化管理器

这个脚本提供了一个统一的接口来管理所有DeepMu可视化功能，包括：
1. 训练过程可视化
2. 预测结果可视化  
3. 可解释性分析
4. 综合报告生成
5. 交互式仪表板

作者: DeepMu 开发团队
版本: 2.0.0
日期: 2025-06-29
"""

import os
import sys
import json
import argparse
import subprocess
from pathlib import Path
from typing import Dict, List, Optional
import warnings

warnings.filterwarnings('ignore')

class DeepMuVisualizer:
    """DeepMu综合可视化管理器"""
    
    def __init__(self, base_dir: str = "."):
        """
        初始化可视化管理器
        
        Args:
            base_dir: DeepMu项目根目录
        """
        self.base_dir = Path(base_dir)
        self.viz_dir = self.base_dir / "visualization"
        
        # 检查可视化脚本是否存在
        self._check_visualization_scripts()
        
        print("🎨 DeepMu 综合可视化管理器已初始化")
        print(f"📁 项目目录: {self.base_dir}")
        print(f"🔧 可视化工具目录: {self.viz_dir}")
    
    def _check_visualization_scripts(self):
        """检查可视化脚本是否存在"""
        required_scripts = [
            "training/training_visualizer.py",
            "prediction/prediction_visualizer.py", 
            "interpretability/interpretability_analyzer.py"
        ]
        
        missing_scripts = []
        for script in required_scripts:
            script_path = self.viz_dir / script
            if not script_path.exists():
                missing_scripts.append(script)
        
        if missing_scripts:
            print(f"⚠️  缺少可视化脚本: {missing_scripts}")
            print("请确保所有可视化脚本都已正确安装")
    
    def visualize_training(self, model_dir: str, output_dir: str = None, **kwargs):
        """
        可视化训练过程
        
        Args:
            model_dir: 模型目录路径
            output_dir: 输出目录路径
            **kwargs: 其他参数
        """
        print(f"🚀 开始训练过程可视化: {model_dir}")
        
        script_path = self.viz_dir / "training" / "training_visualizer.py"
        cmd = [sys.executable, str(script_path), model_dir]
        
        if output_dir:
            cmd.extend(["--output-dir", output_dir])
        
        # 添加其他参数
        for key, value in kwargs.items():
            if key.startswith('no_') and value:
                cmd.append(f"--{key.replace('_', '-')}")
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            print("✅ 训练可视化完成")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ 训练可视化失败: {e}")
            print(f"错误输出: {e.stderr}")
            return False
    
    def visualize_predictions(self, prediction_file: str, output_dir: str = None, 
                            true_values_file: str = None, **kwargs):
        """
        可视化预测结果
        
        Args:
            prediction_file: 预测结果文件路径
            output_dir: 输出目录路径
            true_values_file: 真实值文件路径
            **kwargs: 其他参数
        """
        print(f"📊 开始预测结果可视化: {prediction_file}")
        
        script_path = self.viz_dir / "prediction" / "prediction_visualizer.py"
        cmd = [sys.executable, str(script_path), prediction_file]
        
        if output_dir:
            cmd.extend(["--output-dir", output_dir])
        
        if true_values_file:
            cmd.extend(["--true-values", true_values_file])
        
        # 添加其他参数
        for key, value in kwargs.items():
            if key.startswith('no_') and value:
                cmd.append(f"--{key.replace('_', '-')}")
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            print("✅ 预测可视化完成")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ 预测可视化失败: {e}")
            print(f"错误输出: {e.stderr}")
            return False
    
    def analyze_interpretability(self, model_dir: str, features_file: str, 
                               output_dir: str = None, **kwargs):
        """
        可解释性分析
        
        Args:
            model_dir: 模型目录路径
            features_file: 特征文件路径
            output_dir: 输出目录路径
            **kwargs: 其他参数
        """
        print(f"🔍 开始可解释性分析: {model_dir}")
        
        script_path = self.viz_dir / "interpretability" / "interpretability_analyzer.py"
        cmd = [sys.executable, str(script_path), model_dir, features_file]
        
        if output_dir:
            cmd.extend(["--output-dir", output_dir])
        
        # 添加其他参数
        for key, value in kwargs.items():
            if key.startswith('no_') and value:
                cmd.append(f"--{key.replace('_', '-')}")
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            print("✅ 可解释性分析完成")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ 可解释性分析失败: {e}")
            print(f"错误输出: {e.stderr}")
            return False
    
    def create_comprehensive_analysis(self, model_dir: str, features_file: str = None,
                                    prediction_file: str = None, output_dir: str = None):
        """
        创建综合分析报告
        
        Args:
            model_dir: 模型目录路径
            features_file: 特征文件路径
            prediction_file: 预测结果文件路径
            output_dir: 输出目录路径
        """
        print("📋 开始创建综合分析报告...")
        
        if not output_dir:
            output_dir = Path(model_dir) / "comprehensive_analysis"
        
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 1. 训练过程可视化
        training_success = self.visualize_training(
            model_dir, 
            str(output_path / "training_analysis")
        )
        
        # 2. 可解释性分析（如果提供特征文件）
        interpretability_success = False
        if features_file:
            interpretability_success = self.analyze_interpretability(
                model_dir, 
                features_file,
                str(output_path / "interpretability_analysis")
            )
        
        # 3. 预测结果可视化（如果提供预测文件）
        prediction_success = False
        if prediction_file:
            prediction_success = self.visualize_predictions(
                prediction_file,
                str(output_path / "prediction_analysis")
            )
        
        # 4. 生成综合报告
        self._generate_comprehensive_report(
            output_path, model_dir, training_success, 
            interpretability_success, prediction_success
        )
        
        print(f"🎉 综合分析报告已生成: {output_path}")
    
    def _generate_comprehensive_report(self, output_path: Path, model_dir: str,
                                     training_success: bool, interpretability_success: bool,
                                     prediction_success: bool):
        """生成综合报告"""
        report_file = output_path / "comprehensive_report.md"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("# DeepMu 综合分析报告\n\n")
            f.write(f"**生成时间**: {self._get_current_time()}\n")
            f.write(f"**模型目录**: {model_dir}\n")
            f.write(f"**分析目录**: {output_path}\n\n")
            
            # 分析状态
            f.write("## 分析状态\n\n")
            f.write(f"- **训练过程分析**: {'✅ 完成' if training_success else '❌ 失败'}\n")
            f.write(f"- **可解释性分析**: {'✅ 完成' if interpretability_success else '⏭️ 跳过'}\n")
            f.write(f"- **预测结果分析**: {'✅ 完成' if prediction_success else '⏭️ 跳过'}\n\n")
            
            # 目录结构
            f.write("## 分析结果目录\n\n")
            f.write("```\n")
            f.write("comprehensive_analysis/\n")
            if training_success:
                f.write("├── training_analysis/          # 训练过程分析\n")
            if interpretability_success:
                f.write("├── interpretability_analysis/  # 可解释性分析\n")
            if prediction_success:
                f.write("├── prediction_analysis/        # 预测结果分析\n")
            f.write("└── comprehensive_report.md     # 本报告\n")
            f.write("```\n\n")
            
            # 使用指南
            f.write("## 使用指南\n\n")
            f.write("### 1. 训练过程分析\n")
            if training_success:
                f.write("- 查看 `training_analysis/` 目录下的训练曲线和性能图表\n")
                f.write("- 分析模型收敛情况和性能指标\n")
            else:
                f.write("- 训练分析失败，请检查模型目录和训练历史文件\n")
            
            f.write("\n### 2. 可解释性分析\n")
            if interpretability_success:
                f.write("- 查看 `interpretability_analysis/` 目录下的特征重要性分析\n")
                f.write("- 理解模型决策的关键特征\n")
                f.write("- 分析特征相关性和分布\n")
            else:
                f.write("- 可解释性分析未执行，需要提供特征文件\n")
            
            f.write("\n### 3. 预测结果分析\n")
            if prediction_success:
                f.write("- 查看 `prediction_analysis/` 目录下的预测分布和准确性分析\n")
                f.write("- 评估模型预测性能\n")
            else:
                f.write("- 预测分析未执行，需要提供预测结果文件\n")
            
            # 建议
            f.write("\n## 建议\n\n")
            f.write("1. **模型优化**: 基于训练分析结果调整模型参数\n")
            f.write("2. **特征工程**: 基于可解释性分析优化特征选择\n")
            f.write("3. **性能提升**: 基于预测分析改进模型性能\n")
            f.write("4. **领域知识**: 结合生物学知识解释分析结果\n")
    
    def _get_current_time(self):
        """获取当前时间字符串"""
        from datetime import datetime
        return datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    def list_available_models(self):
        """列出可用的模型"""
        models_dir = self.base_dir / "models"
        if not models_dir.exists():
            print("📁 models目录不存在")
            return []
        
        available_models = []
        for model_path in models_dir.iterdir():
            if model_path.is_dir():
                # 检查是否包含模型文件
                model_files = list(model_path.glob("*.pt"))
                if model_files:
                    available_models.append({
                        'name': model_path.name,
                        'path': str(model_path),
                        'model_files': [f.name for f in model_files]
                    })
        
        print("📋 可用的模型:")
        for model in available_models:
            print(f"  - {model['name']}: {model['model_files']}")
        
        return available_models

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='DeepMu 综合可视化管理器')
    parser.add_argument('--base-dir', type=str, default='.', help='DeepMu项目根目录')
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 训练可视化命令
    train_parser = subparsers.add_parser('training', help='训练过程可视化')
    train_parser.add_argument('model_dir', type=str, help='模型目录路径')
    train_parser.add_argument('--output-dir', type=str, help='输出目录路径')
    train_parser.add_argument('--no-curves', action='store_true', help='跳过训练曲线')
    train_parser.add_argument('--no-performance', action='store_true', help='跳过性能总结')
    train_parser.add_argument('--no-report', action='store_true', help='跳过综合报告')
    
    # 预测可视化命令
    pred_parser = subparsers.add_parser('prediction', help='预测结果可视化')
    pred_parser.add_argument('prediction_file', type=str, help='预测结果文件路径')
    pred_parser.add_argument('--output-dir', type=str, help='输出目录路径')
    pred_parser.add_argument('--true-values', type=str, help='真实值文件路径')
    pred_parser.add_argument('--no-distribution', action='store_true', help='跳过分布图')
    pred_parser.add_argument('--no-accuracy', action='store_true', help='跳过准确性分析')
    pred_parser.add_argument('--no-summary', action='store_true', help='跳过总结报告')
    
    # 可解释性分析命令
    interp_parser = subparsers.add_parser('interpretability', help='可解释性分析')
    interp_parser.add_argument('model_dir', type=str, help='模型目录路径')
    interp_parser.add_argument('features_file', type=str, help='特征文件路径')
    interp_parser.add_argument('--output-dir', type=str, help='输出目录路径')
    interp_parser.add_argument('--no-rf', action='store_true', help='跳过随机森林分析')
    interp_parser.add_argument('--no-gradient', action='store_true', help='跳过梯度分析')
    interp_parser.add_argument('--no-correlation', action='store_true', help='跳过相关性分析')
    interp_parser.add_argument('--no-distribution', action='store_true', help='跳过分布分析')
    interp_parser.add_argument('--no-report', action='store_true', help='跳过综合报告')
    
    # 综合分析命令
    comp_parser = subparsers.add_parser('comprehensive', help='综合分析')
    comp_parser.add_argument('model_dir', type=str, help='模型目录路径')
    comp_parser.add_argument('--features-file', type=str, help='特征文件路径')
    comp_parser.add_argument('--prediction-file', type=str, help='预测结果文件路径')
    comp_parser.add_argument('--output-dir', type=str, help='输出目录路径')
    
    # 列出模型命令
    subparsers.add_parser('list-models', help='列出可用模型')
    
    args = parser.parse_args()
    
    # 创建可视化管理器
    visualizer = DeepMuVisualizer(args.base_dir)
    
    if args.command == 'training':
        kwargs = {k: v for k, v in vars(args).items() 
                 if k.startswith('no_') and v}
        visualizer.visualize_training(args.model_dir, args.output_dir, **kwargs)
    
    elif args.command == 'prediction':
        kwargs = {k: v for k, v in vars(args).items() 
                 if k.startswith('no_') and v}
        visualizer.visualize_predictions(
            args.prediction_file, args.output_dir, 
            args.true_values, **kwargs
        )
    
    elif args.command == 'interpretability':
        kwargs = {k: v for k, v in vars(args).items() 
                 if k.startswith('no_') and v}
        visualizer.analyze_interpretability(
            args.model_dir, args.features_file, 
            args.output_dir, **kwargs
        )
    
    elif args.command == 'comprehensive':
        visualizer.create_comprehensive_analysis(
            args.model_dir, args.features_file, 
            args.prediction_file, args.output_dir
        )
    
    elif args.command == 'list-models':
        visualizer.list_available_models()
    
    else:
        parser.print_help()

if __name__ == "__main__":
    main()
