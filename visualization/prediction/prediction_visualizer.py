#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
DeepMu 预测结果可视化器

这个脚本用于可视化DeepMu模型的预测结果，包括：
1. 预测值分布
2. 预测准确性分析
3. 误差分析
4. 置信区间
5. 预测结果对比

作者: DeepMu 开发团队
版本: 2.0.0
日期: 2025-06-29
"""

import os
import sys
import json
import argparse
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union
import warnings
from scipy import stats
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error

warnings.filterwarnings('ignore')

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")
sns.set_palette("husl")

class PredictionVisualizer:
    """预测结果可视化器"""
    
    def __init__(self, prediction_file: str, output_dir: str = None):
        """
        初始化可视化器
        
        Args:
            prediction_file: 预测结果文件路径
            output_dir: 输出目录路径
        """
        self.prediction_file = Path(prediction_file)
        self.output_dir = Path(output_dir) if output_dir else self.prediction_file.parent / "prediction_visualizations"
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 加载预测结果
        self.results = self._load_prediction_results()
        self.prediction_type = self._detect_prediction_type()
        
        print(f"📊 检测到预测类型: {self.prediction_type}")
        print(f"📁 输出目录: {self.output_dir}")
    
    def _load_prediction_results(self) -> Dict:
        """加载预测结果"""
        if not self.prediction_file.exists():
            raise FileNotFoundError(f"预测结果文件不存在: {self.prediction_file}")
        
        with open(self.prediction_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def _detect_prediction_type(self) -> str:
        """检测预测类型"""
        if isinstance(self.results, list):
            # 批量预测结果
            if len(self.results) > 0 and 'optimal_temperature' in self.results[0]:
                return 'batch_multi_task'
            else:
                return 'batch_single_task'
        elif isinstance(self.results, dict):
            if 'optimal_temperature' in self.results and 'growth_rate' in self.results:
                return 'single_multi_task'
            elif 'growth_rate' in self.results:
                return 'single_growth_rate'
            elif 'optimal_temperature' in self.results:
                return 'single_temperature'
        
        return 'unknown'
    
    def create_prediction_distribution(self):
        """创建预测值分布图"""
        if 'single' in self.prediction_type:
            print("⚠️  单个预测结果，跳过分布图")
            return
        
        if self.prediction_type == 'batch_multi_task':
            self._plot_multi_task_distribution()
        else:
            self._plot_single_task_distribution()
    
    def _plot_multi_task_distribution(self):
        """绘制多任务预测分布"""
        # 提取数据
        growth_rates = [r['growth_rate'] for r in self.results if r.get('growth_rate') is not None]
        temperatures = [r['optimal_temperature'] for r in self.results 
                       if r.get('optimal_temperature') is not None]
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('DeepMu 多任务预测结果分布', fontsize=16, fontweight='bold')
        
        # 生长速率分布
        if growth_rates:
            axes[0, 0].hist(growth_rates, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
            axes[0, 0].set_title(f'生长速率分布 (n={len(growth_rates)})')
            axes[0, 0].set_xlabel('生长速率 (h⁻¹)')
            axes[0, 0].set_ylabel('频次')
            axes[0, 0].grid(True, alpha=0.3)
            
            # 添加统计信息
            mean_gr = np.mean(growth_rates)
            std_gr = np.std(growth_rates)
            axes[0, 0].axvline(mean_gr, color='red', linestyle='--', 
                              label=f'均值: {mean_gr:.4f}')
            axes[0, 0].legend()
        
        # 温度分布
        if temperatures:
            axes[0, 1].hist(temperatures, bins=30, alpha=0.7, color='lightcoral', edgecolor='black')
            axes[0, 1].set_title(f'最适温度分布 (n={len(temperatures)})')
            axes[0, 1].set_xlabel('温度 (°C)')
            axes[0, 1].set_ylabel('频次')
            axes[0, 1].grid(True, alpha=0.3)
            
            # 添加统计信息
            mean_temp = np.mean(temperatures)
            std_temp = np.std(temperatures)
            axes[0, 1].axvline(mean_temp, color='red', linestyle='--', 
                              label=f'均值: {mean_temp:.2f}°C')
            axes[0, 1].legend()
        
        # 生长速率箱线图
        if growth_rates:
            axes[1, 0].boxplot(growth_rates, vert=True)
            axes[1, 0].set_title('生长速率箱线图')
            axes[1, 0].set_ylabel('生长速率 (h⁻¹)')
            axes[1, 0].grid(True, alpha=0.3)
        
        # 温度箱线图
        if temperatures:
            axes[1, 1].boxplot(temperatures, vert=True)
            axes[1, 1].set_title('最适温度箱线图')
            axes[1, 1].set_ylabel('温度 (°C)')
            axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'multi_task_prediction_distribution.png', 
                   dpi=300, bbox_inches='tight')
        plt.close()
        
        print("✅ 多任务预测分布图已保存")
    
    def _plot_single_task_distribution(self):
        """绘制单任务预测分布"""
        # 根据预测类型提取数据
        if 'growth_rate' in self.results[0]:
            values = [r['growth_rate'] for r in self.results if r.get('growth_rate') is not None]
            title = '生长速率预测分布'
            xlabel = '生长速率 (h⁻¹)'
            filename = 'growth_rate_prediction_distribution.png'
        else:
            values = [r['optimal_temperature'] for r in self.results 
                     if r.get('optimal_temperature') is not None]
            title = '温度预测分布'
            xlabel = '温度 (°C)'
            filename = 'temperature_prediction_distribution.png'
        
        if not values:
            print("⚠️  没有有效的预测值，跳过分布图")
            return
        
        fig, axes = plt.subplots(1, 3, figsize=(18, 6))
        fig.suptitle(f'DeepMu {title}', fontsize=16, fontweight='bold')
        
        # 直方图
        axes[0].hist(values, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
        axes[0].set_title(f'分布直方图 (n={len(values)})')
        axes[0].set_xlabel(xlabel)
        axes[0].set_ylabel('频次')
        axes[0].grid(True, alpha=0.3)
        
        # 添加统计信息
        mean_val = np.mean(values)
        std_val = np.std(values)
        axes[0].axvline(mean_val, color='red', linestyle='--', 
                       label=f'均值: {mean_val:.4f}')
        axes[0].legend()
        
        # 箱线图
        axes[1].boxplot(values, vert=True)
        axes[1].set_title('箱线图')
        axes[1].set_ylabel(xlabel)
        axes[1].grid(True, alpha=0.3)
        
        # Q-Q图（正态性检验）
        stats.probplot(values, dist="norm", plot=axes[2])
        axes[2].set_title('Q-Q图 (正态性检验)')
        axes[2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(self.output_dir / filename, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"✅ {title}已保存")
    
    def create_prediction_accuracy_analysis(self, true_values_file: str = None):
        """创建预测准确性分析"""
        if true_values_file is None:
            print("⚠️  没有提供真实值文件，跳过准确性分析")
            return
        
        # 加载真实值
        true_values = self._load_true_values(true_values_file)
        
        if self.prediction_type == 'batch_multi_task':
            self._plot_multi_task_accuracy(true_values)
        else:
            self._plot_single_task_accuracy(true_values)
    
    def _load_true_values(self, true_values_file: str) -> Dict:
        """加载真实值"""
        true_file = Path(true_values_file)
        
        if true_file.suffix == '.json':
            with open(true_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        elif true_file.suffix in ['.tsv', '.csv']:
            sep = '\t' if true_file.suffix == '.tsv' else ','
            df = pd.read_csv(true_file, sep=sep, index_col=0)
            return df.to_dict('index')
        else:
            raise ValueError(f"不支持的文件格式: {true_file.suffix}")
    
    def _plot_multi_task_accuracy(self, true_values: Dict):
        """绘制多任务预测准确性"""
        # 匹配预测值和真实值
        matched_data = self._match_predictions_with_truth(true_values)
        
        if not matched_data:
            print("⚠️  没有匹配的数据，跳过准确性分析")
            return
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('DeepMu 多任务预测准确性分析', fontsize=16, fontweight='bold')
        
        # 生长速率散点图
        if 'growth_rate' in matched_data:
            gr_true = matched_data['growth_rate']['true']
            gr_pred = matched_data['growth_rate']['pred']
            
            axes[0, 0].scatter(gr_true, gr_pred, alpha=0.6, s=30)
            
            # 添加对角线
            min_val = min(min(gr_true), min(gr_pred))
            max_val = max(max(gr_true), max(gr_pred))
            axes[0, 0].plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2)
            
            # 计算指标
            r2 = r2_score(gr_true, gr_pred)
            rmse = np.sqrt(mean_squared_error(gr_true, gr_pred))
            
            axes[0, 0].set_xlabel('真实生长速率')
            axes[0, 0].set_ylabel('预测生长速率')
            axes[0, 0].set_title(f'生长速率预测 (R² = {r2:.4f}, RMSE = {rmse:.4f})')
            axes[0, 0].grid(True, alpha=0.3)
        
        # 温度散点图
        if 'optimal_temperature' in matched_data:
            temp_true = matched_data['optimal_temperature']['true']
            temp_pred = matched_data['optimal_temperature']['pred']
            
            axes[0, 1].scatter(temp_true, temp_pred, alpha=0.6, s=30, color='orange')
            
            # 添加对角线
            min_val = min(min(temp_true), min(temp_pred))
            max_val = max(max(temp_true), max(temp_pred))
            axes[0, 1].plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2)
            
            # 计算指标
            r2 = r2_score(temp_true, temp_pred)
            mae = mean_absolute_error(temp_true, temp_pred)
            
            axes[0, 1].set_xlabel('真实温度 (°C)')
            axes[0, 1].set_ylabel('预测温度 (°C)')
            axes[0, 1].set_title(f'温度预测 (R² = {r2:.4f}, MAE = {mae:.2f}°C)')
            axes[0, 1].grid(True, alpha=0.3)
        
        # 生长速率误差分布
        if 'growth_rate' in matched_data:
            gr_errors = np.array(gr_pred) - np.array(gr_true)
            axes[1, 0].hist(gr_errors, bins=30, alpha=0.7, color='lightblue', edgecolor='black')
            axes[1, 0].axvline(0, color='red', linestyle='--', linewidth=2)
            axes[1, 0].set_xlabel('预测误差')
            axes[1, 0].set_ylabel('频次')
            axes[1, 0].set_title('生长速率预测误差分布')
            axes[1, 0].grid(True, alpha=0.3)
        
        # 温度误差分布
        if 'optimal_temperature' in matched_data:
            temp_errors = np.array(temp_pred) - np.array(temp_true)
            axes[1, 1].hist(temp_errors, bins=30, alpha=0.7, color='lightcoral', edgecolor='black')
            axes[1, 1].axvline(0, color='red', linestyle='--', linewidth=2)
            axes[1, 1].set_xlabel('预测误差 (°C)')
            axes[1, 1].set_ylabel('频次')
            axes[1, 1].set_title('温度预测误差分布')
            axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'multi_task_prediction_accuracy.png', 
                   dpi=300, bbox_inches='tight')
        plt.close()
        
        print("✅ 多任务预测准确性分析已保存")
    
    def _match_predictions_with_truth(self, true_values: Dict) -> Dict:
        """匹配预测值和真实值"""
        matched_data = {}
        
        # 假设预测结果中有sample_id字段，或者按顺序匹配
        for i, pred in enumerate(self.results):
            sample_id = pred.get('sample_id', f'sample_{i}')
            
            if sample_id in true_values:
                true_val = true_values[sample_id]
                
                # 匹配生长速率
                if 'growth_rate' in pred and 'growth_rate' in true_val:
                    if 'growth_rate' not in matched_data:
                        matched_data['growth_rate'] = {'true': [], 'pred': []}
                    matched_data['growth_rate']['true'].append(true_val['growth_rate'])
                    matched_data['growth_rate']['pred'].append(pred['growth_rate'])
                
                # 匹配温度
                if 'optimal_temperature' in pred and 'optimal_temperature' in true_val:
                    if 'optimal_temperature' not in matched_data:
                        matched_data['optimal_temperature'] = {'true': [], 'pred': []}
                    matched_data['optimal_temperature']['true'].append(true_val['optimal_temperature'])
                    matched_data['optimal_temperature']['pred'].append(pred['optimal_temperature'])
        
        return matched_data
    
    def create_prediction_summary(self):
        """创建预测总结"""
        summary_path = self.output_dir / 'prediction_summary.md'
        
        with open(summary_path, 'w', encoding='utf-8') as f:
            f.write("# DeepMu 预测结果总结\n\n")
            f.write(f"**生成时间**: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"**预测类型**: {self.prediction_type}\n")
            f.write(f"**预测文件**: {self.prediction_file}\n\n")
            
            # 基本统计
            f.write("## 预测统计\n\n")
            
            if isinstance(self.results, list):
                f.write(f"- **预测样本数**: {len(self.results)}\n")
                
                # 生长速率统计
                growth_rates = [r['growth_rate'] for r in self.results 
                               if r.get('growth_rate') is not None]
                if growth_rates:
                    f.write(f"- **生长速率预测数**: {len(growth_rates)}\n")
                    f.write(f"- **生长速率范围**: {min(growth_rates):.4f} - {max(growth_rates):.4f} h⁻¹\n")
                    f.write(f"- **生长速率均值**: {np.mean(growth_rates):.4f} h⁻¹\n")
                    f.write(f"- **生长速率标准差**: {np.std(growth_rates):.4f} h⁻¹\n")
                
                # 温度统计
                temperatures = [r['optimal_temperature'] for r in self.results 
                               if r.get('optimal_temperature') is not None]
                if temperatures:
                    f.write(f"- **温度预测数**: {len(temperatures)}\n")
                    f.write(f"- **温度范围**: {min(temperatures):.2f} - {max(temperatures):.2f} °C\n")
                    f.write(f"- **温度均值**: {np.mean(temperatures):.2f} °C\n")
                    f.write(f"- **温度标准差**: {np.std(temperatures):.2f} °C\n")
            
            else:
                f.write("- **预测样本数**: 1 (单个预测)\n")
                if 'growth_rate' in self.results:
                    f.write(f"- **生长速率**: {self.results['growth_rate']:.4f} h⁻¹\n")
                if 'optimal_temperature' in self.results:
                    f.write(f"- **最适温度**: {self.results['optimal_temperature']:.2f} °C\n")
            
            f.write("\n")
            
            # 生成的图表
            f.write("## 生成的可视化图表\n\n")
            
            for img_file in self.output_dir.glob("*.png"):
                f.write(f"### {img_file.stem}\n\n")
                f.write(f"![{img_file.stem}]({img_file.name})\n\n")
        
        print(f"✅ 预测总结已保存: {summary_path}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='DeepMu 预测结果可视化器')
    parser.add_argument('prediction_file', type=str, help='预测结果文件路径')
    parser.add_argument('--output-dir', type=str, help='输出目录路径')
    parser.add_argument('--true-values', type=str, help='真实值文件路径')
    parser.add_argument('--no-distribution', action='store_true', help='跳过分布图')
    parser.add_argument('--no-accuracy', action='store_true', help='跳过准确性分析')
    parser.add_argument('--no-summary', action='store_true', help='跳过总结报告')
    
    args = parser.parse_args()
    
    try:
        # 创建可视化器
        visualizer = PredictionVisualizer(args.prediction_file, args.output_dir)
        
        # 生成可视化
        if not args.no_distribution:
            visualizer.create_prediction_distribution()
        
        if not args.no_accuracy and args.true_values:
            visualizer.create_prediction_accuracy_analysis(args.true_values)
        
        if not args.no_summary:
            visualizer.create_prediction_summary()
        
        print(f"\n🎉 预测可视化完成！")
        print(f"📁 输出目录: {visualizer.output_dir}")
        
    except Exception as e:
        print(f"❌ 可视化过程中出现错误: {e}")
        raise

if __name__ == "__main__":
    main()
