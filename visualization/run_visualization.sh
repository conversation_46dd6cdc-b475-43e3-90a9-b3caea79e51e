#!/bin/bash

# DeepMu 可视化总控脚本
# 
# 这个脚本提供了一个统一的入口来运行所有DeepMu可视化功能
# 
# 作者: DeepMu 开发团队
# 版本: 2.0.0
# 日期: 2025-06-29

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_success() {
    echo -e "${CYAN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 显示横幅
show_banner() {
    echo -e "${PURPLE}"
    cat << 'EOF'
╔══════════════════════════════════════════════════════════════╗
║                    DeepMu 可视化系统                         ║
║                                                              ║
║  🎨 训练过程可视化  📊 预测结果分析  🔍 可解释性分析          ║
║                                                              ║
║                   版本 2.0.0 - 2025-06-29                   ║
╚══════════════════════════════════════════════════════════════╝
EOF
    echo -e "${NC}"
}

# 显示帮助信息
show_help() {
    cat << EOF
DeepMu 可视化总控脚本

用法: $0 [命令] [选项]

命令:
    training <模型目录>                     训练过程可视化
    prediction <预测文件>                   预测结果可视化
    interpretability <模型目录> <特征文件>   可解释性分析
    comprehensive <模型目录>                综合分析
    list-models                            列出可用模型
    demo                                   运行演示
    help                                   显示此帮助信息

全局选项:
    -v, --verbose                          详细输出
    -q, --quiet                           静默模式
    --base-dir DIR                        项目根目录 (默认: 当前目录)

示例:
    # 训练过程可视化
    $0 training models/deepmu_multi_task

    # 预测结果可视化
    $0 prediction results/predictions.json --true-values data/metadata.tsv

    # 可解释性分析
    $0 interpretability models/deepmu_growth_rate training_data/combined_features.tsv

    # 综合分析
    $0 comprehensive models/deepmu_multi_task --features training_data/combined_features.tsv

    # 列出可用模型
    $0 list-models

    # 运行演示
    $0 demo

更多帮助:
    $0 training --help      # 训练可视化帮助
    $0 prediction --help    # 预测可视化帮助
    $0 interpretability --help  # 可解释性分析帮助

EOF
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    # 检查Python
    if ! command -v python3 &> /dev/null; then
        log_error "Python3 未找到，请安装Python3"
        exit 1
    fi
    
    # 检查必要的Python包
    local required_packages=("matplotlib" "seaborn" "pandas" "numpy" "torch" "sklearn")
    local missing_packages=()
    
    for package in "${required_packages[@]}"; do
        if ! python3 -c "import $package" &> /dev/null 2>&1; then
            missing_packages+=("$package")
        fi
    done
    
    if [[ ${#missing_packages[@]} -gt 0 ]]; then
        log_error "缺少Python包: ${missing_packages[*]}"
        log_info "请运行: pip install ${missing_packages[*]}"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 检查可视化脚本
check_visualization_scripts() {
    log_info "检查可视化脚本..."
    
    local base_dir="${BASE_DIR:-$(pwd)}"
    local viz_dir="$base_dir/visualization"
    
    local required_scripts=(
        "training/training_visualizer.py"
        "prediction/prediction_visualizer.py"
        "interpretability/interpretability_analyzer.py"
        "deepmu_visualizer.py"
    )
    
    local missing_scripts=()
    
    for script in "${required_scripts[@]}"; do
        if [[ ! -f "$viz_dir/$script" ]]; then
            missing_scripts+=("$script")
        fi
    done
    
    if [[ ${#missing_scripts[@]} -gt 0 ]]; then
        log_error "缺少可视化脚本: ${missing_scripts[*]}"
        log_info "请确保所有可视化脚本都已正确安装"
        exit 1
    fi
    
    log_success "可视化脚本检查通过"
}

# 列出可用模型
list_models() {
    log_info "扫描可用模型..."
    
    local base_dir="${BASE_DIR:-$(pwd)}"
    local models_dir="$base_dir/models"
    
    if [[ ! -d "$models_dir" ]]; then
        log_warn "models目录不存在: $models_dir"
        return
    fi
    
    echo -e "\n${BLUE}可用模型:${NC}"
    
    local found_models=false
    
    for model_dir in "$models_dir"/*; do
        if [[ -d "$model_dir" ]]; then
            local model_name=$(basename "$model_dir")
            local model_files=($(find "$model_dir" -name "*.pt" -type f))
            
            if [[ ${#model_files[@]} -gt 0 ]]; then
                found_models=true
                echo -e "  ${GREEN}📁 $model_name${NC}"
                
                for model_file in "${model_files[@]}"; do
                    echo -e "    └── $(basename "$model_file")"
                done
                
                # 检查训练历史
                if [[ -f "$model_dir/training_history.json" ]]; then
                    echo -e "    └── ${CYAN}training_history.json${NC} ✓"
                fi
                
                # 检查配置文件
                if [[ -f "$model_dir/config.json" ]]; then
                    echo -e "    └── ${CYAN}config.json${NC} ✓"
                fi
                
                echo
            fi
        fi
    done
    
    if [[ "$found_models" == false ]]; then
        log_warn "未找到任何训练好的模型"
        echo "请先运行训练脚本生成模型"
    fi
}

# 运行演示
run_demo() {
    log_info "运行DeepMu可视化演示..."
    
    local base_dir="${BASE_DIR:-$(pwd)}"
    local models_dir="$base_dir/models"
    
    # 查找第一个可用模型
    local demo_model=""
    for model_dir in "$models_dir"/*; do
        if [[ -d "$model_dir" && -f "$model_dir/training_history.json" ]]; then
            demo_model="$model_dir"
            break
        fi
    done
    
    if [[ -z "$demo_model" ]]; then
        log_error "未找到可用于演示的模型"
        log_info "请先运行训练脚本生成模型"
        return 1
    fi
    
    log_info "使用模型进行演示: $(basename "$demo_model")"
    
    # 创建演示输出目录
    local demo_output="$base_dir/demo_visualization"
    mkdir -p "$demo_output"
    
    # 1. 训练过程可视化
    log_info "🚀 演示训练过程可视化..."
    if python3 "$base_dir/visualization/deepmu_visualizer.py" training "$demo_model" \
        --output-dir "$demo_output/training_demo"; then
        log_success "训练可视化演示完成"
    else
        log_error "训练可视化演示失败"
    fi
    
    # 2. 可解释性分析（如果有特征文件）
    local features_file="$base_dir/training_data/combined_features.tsv"
    if [[ -f "$features_file" ]]; then
        log_info "🔍 演示可解释性分析..."
        if python3 "$base_dir/visualization/deepmu_visualizer.py" interpretability \
            "$demo_model" "$features_file" --output-dir "$demo_output/interpretability_demo"; then
            log_success "可解释性分析演示完成"
        else
            log_error "可解释性分析演示失败"
        fi
    else
        log_warn "未找到特征文件，跳过可解释性分析演示"
    fi
    
    log_success "演示完成！结果保存在: $demo_output"
    
    # 显示演示结果
    echo -e "\n${BLUE}演示结果:${NC}"
    find "$demo_output" -name "*.png" -o -name "*.md" | head -10 | while read -r file; do
        echo "  📄 $(basename "$file")"
    done
}

# 主函数
main() {
    # 解析全局选项
    VERBOSE=false
    QUIET=false
    BASE_DIR=""
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -q|--quiet)
                QUIET=true
                shift
                ;;
            --base-dir)
                BASE_DIR="$2"
                shift 2
                ;;
            -h|--help|help)
                show_banner
                show_help
                exit 0
                ;;
            *)
                break
                ;;
        esac
    done
    
    # 设置默认基础目录
    if [[ -z "$BASE_DIR" ]]; then
        BASE_DIR="$(pwd)"
    fi
    
    # 显示横幅（除非静默模式）
    if [[ "$QUIET" != true ]]; then
        show_banner
    fi
    
    # 检查依赖
    if [[ "$QUIET" != true ]]; then
        check_dependencies
        check_visualization_scripts
    fi
    
    # 解析命令
    if [[ $# -eq 0 ]]; then
        show_help
        exit 0
    fi
    
    local command="$1"
    shift
    
    case "$command" in
        training)
            if [[ $# -eq 0 ]]; then
                log_error "请指定模型目录"
                echo "用法: $0 training <模型目录> [选项]"
                exit 1
            fi
            exec bash "$(dirname "$0")/visualize_training.sh" "$@"
            ;;
        
        prediction)
            if [[ $# -eq 0 ]]; then
                log_error "请指定预测文件"
                echo "用法: $0 prediction <预测文件> [选项]"
                exit 1
            fi
            exec bash "$(dirname "$0")/visualize_predictions.sh" "$@"
            ;;
        
        interpretability)
            if [[ $# -lt 2 ]]; then
                log_error "请指定模型目录和特征文件"
                echo "用法: $0 interpretability <模型目录> <特征文件> [选项]"
                exit 1
            fi
            exec bash "$(dirname "$0")/analyze_interpretability.sh" "$@"
            ;;
        
        comprehensive)
            if [[ $# -eq 0 ]]; then
                log_error "请指定模型目录"
                echo "用法: $0 comprehensive <模型目录> [选项]"
                exit 1
            fi
            python3 "$BASE_DIR/visualization/deepmu_visualizer.py" comprehensive "$@"
            ;;
        
        list-models)
            list_models
            ;;
        
        demo)
            run_demo
            ;;
        
        *)
            log_error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 捕获中断信号
trap 'log_error "操作被中断"; exit 1' INT TERM

# 执行主函数
main "$@"
