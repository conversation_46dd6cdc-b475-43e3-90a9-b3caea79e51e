#!/usr/bin/env python3
"""
温度预测特征提取示例脚本

这个脚本演示如何使用ex_feature模块进行温度预测特征提取，
包括单个基因组处理、批量处理、多线程和断点恢复功能。

使用方法:
    # 单个基因组特征提取
    python extract_temperature_features.py single --genome genome.fna --cds cds.fna --output features.npz
    
    # 批量特征提取
    python extract_temperature_features.py batch --metadata genomes.tsv --output-dir features/ --threads 8
    
    # 从断点恢复
    python extract_temperature_features.py batch --metadata genomes.tsv --output-dir features/ --resume
"""

import os
import sys
import argparse
import logging
import time
from pathlib import Path

# 添加ex_feature模块到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ex_feature import TemperatureFeatureExtractor, TemperatureConfig
from ex_feature.utils.parallel import ParallelProcessor

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('temperature_feature_extraction.log')
    ]
)

logger = logging.getLogger(__name__)

def extract_single_genome(args):
    """提取单个基因组的特征"""
    logger.info("开始单个基因组特征提取")
    
    # 创建配置
    config = TemperatureConfig()
    if args.config:
        config.load_config(args.config)
    
    # 创建特征提取器
    extractor = TemperatureFeatureExtractor(
        num_threads=1,  # 单个基因组不需要多线程
        enable_checkpoint=False,
        feature_types=args.feature_types,
        validation_enabled=True
    )
    
    # 提取特征
    try:
        features = extractor.extract_single(
            genome_id=args.genome_id or Path(args.genome).stem,
            genome_file=args.genome,
            cds_file=args.cds,
            protein_file=args.protein,
            ko_file=args.ko,
            taxid=args.taxid
        )
        
        # 保存结果
        import numpy as np
        np.savez_compressed(args.output, **features)
        
        logger.info(f"特征提取完成，结果保存到: {args.output}")
        logger.info(f"提取了 {len(features)} 个特征")
        
        # 显示特征摘要
        if args.verbose:
            print_feature_summary(features)
        
    except Exception as e:
        logger.error(f"特征提取失败: {e}")
        return 1
    
    return 0

def extract_batch_genomes(args):
    """批量提取基因组特征"""
    logger.info("开始批量基因组特征提取")
    
    # 创建配置
    config = TemperatureConfig()
    if args.config:
        config.load_config(args.config)
    
    # 创建特征提取器
    extractor = TemperatureFeatureExtractor(
        num_threads=args.threads,
        enable_checkpoint=True,
        checkpoint_dir=os.path.join(args.output_dir, "checkpoints"),
        checkpoint_interval=args.checkpoint_interval,
        feature_types=args.feature_types,
        validation_enabled=True
    )
    
    # 批量提取
    try:
        start_time = time.time()
        
        extractor.extract_batch(
            metadata_file=args.metadata,
            output_dir=args.output_dir,
            genome_dir=args.genome_dir,
            cds_dir=args.cds_dir,
            protein_dir=args.protein_dir,
            ko_dir=args.ko_dir,
            resume=args.resume
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        logger.info(f"批量特征提取完成，总耗时: {duration:.2f}秒")
        
        # 生成摘要报告
        if args.generate_report:
            generate_extraction_report(args.output_dir, extractor)
        
    except Exception as e:
        logger.error(f"批量特征提取失败: {e}")
        return 1
    
    return 0

def print_feature_summary(features):
    """打印特征摘要"""
    print("\n=== 特征提取摘要 ===")
    
    # 按特征类型分组
    feature_groups = {}
    for key in features.keys():
        if key.startswith('_'):
            continue
        
        # 提取特征类型前缀
        parts = key.split('_', 1)
        if len(parts) > 1:
            group = parts[0]
            if group not in feature_groups:
                feature_groups[group] = []
            feature_groups[group].append(key)
        else:
            if 'other' not in feature_groups:
                feature_groups['other'] = []
            feature_groups['other'].append(key)
    
    # 显示各组特征数量
    for group, feature_list in feature_groups.items():
        print(f"{group}: {len(feature_list)} 个特征")
    
    # 显示一些示例特征值
    print("\n示例特征值:")
    count = 0
    for key, value in features.items():
        if key.startswith('_'):
            continue
        
        if isinstance(value, (int, float)):
            print(f"  {key}: {value:.4f}")
        else:
            print(f"  {key}: {value}")
        
        count += 1
        if count >= 10:  # 只显示前10个
            break
    
    # 显示验证信息
    if '_validation' in features:
        validation = features['_validation']
        print(f"\n验证结果:")
        print(f"  质量评分: {validation.get('quality_score', 0):.3f}")
        print(f"  有效性: {'是' if validation.get('valid', False) else '否'}")
        
        if validation.get('warnings'):
            print(f"  警告数量: {len(validation['warnings'])}")
        
        if validation.get('errors'):
            print(f"  错误数量: {len(validation['errors'])}")

def generate_extraction_report(output_dir, extractor):
    """生成提取报告"""
    logger.info("生成特征提取报告")
    
    report_file = os.path.join(output_dir, "extraction_report.txt")
    
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("温度预测特征提取报告\n")
            f.write("=" * 50 + "\n\n")
            
            # 提取器信息
            info = extractor.get_feature_info()
            f.write("提取器配置:\n")
            for key, value in info.items():
                f.write(f"  {key}: {value}\n")
            f.write("\n")
            
            # 统计输出文件
            output_files = list(Path(output_dir).glob("*.npz"))
            f.write(f"输出文件数量: {len(output_files)}\n")
            f.write(f"输出目录: {output_dir}\n\n")
            
            # 文件大小统计
            if output_files:
                total_size = sum(f.stat().st_size for f in output_files)
                avg_size = total_size / len(output_files)
                f.write(f"总文件大小: {total_size / 1024 / 1024:.2f} MB\n")
                f.write(f"平均文件大小: {avg_size / 1024:.2f} KB\n")
        
        logger.info(f"报告已保存到: {report_file}")
        
    except Exception as e:
        logger.error(f"生成报告失败: {e}")

def create_argument_parser():
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description="温度预测特征提取工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 单个基因组提取
    single_parser = subparsers.add_parser('single', help='提取单个基因组特征')
    single_parser.add_argument('--genome', required=True, help='基因组文件路径')
    single_parser.add_argument('--cds', help='CDS文件路径')
    single_parser.add_argument('--protein', help='蛋白质文件路径')
    single_parser.add_argument('--ko', help='KO注释文件路径')
    single_parser.add_argument('--taxid', help='NCBI分类学ID')
    single_parser.add_argument('--genome-id', help='基因组ID')
    single_parser.add_argument('--output', required=True, help='输出文件路径')
    single_parser.add_argument('--config', help='配置文件路径')
    single_parser.add_argument('--feature-types', nargs='+', 
                              default=['codon', 'genomic', 'protein', 'pathway'],
                              help='要提取的特征类型')
    single_parser.add_argument('--verbose', action='store_true', help='显示详细信息')
    
    # 批量提取
    batch_parser = subparsers.add_parser('batch', help='批量提取基因组特征')
    batch_parser.add_argument('--metadata', required=True, help='元数据文件路径')
    batch_parser.add_argument('--output-dir', required=True, help='输出目录')
    batch_parser.add_argument('--genome-dir', help='基因组文件目录')
    batch_parser.add_argument('--cds-dir', help='CDS文件目录')
    batch_parser.add_argument('--protein-dir', help='蛋白质文件目录')
    batch_parser.add_argument('--ko-dir', help='KO注释文件目录')
    batch_parser.add_argument('--threads', type=int, default=4, help='线程数')
    batch_parser.add_argument('--checkpoint-interval', type=int, default=100, 
                             help='断点保存间隔')
    batch_parser.add_argument('--resume', action='store_true', help='从断点恢复')
    batch_parser.add_argument('--config', help='配置文件路径')
    batch_parser.add_argument('--feature-types', nargs='+',
                             default=['codon', 'genomic', 'protein', 'pathway'],
                             help='要提取的特征类型')
    batch_parser.add_argument('--generate-report', action='store_true', 
                             help='生成提取报告')
    
    return parser

def main():
    """主函数"""
    parser = create_argument_parser()
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    logger.info(f"开始执行命令: {args.command}")
    
    try:
        if args.command == 'single':
            return extract_single_genome(args)
        elif args.command == 'batch':
            return extract_batch_genomes(args)
        else:
            logger.error(f"未知命令: {args.command}")
            return 1
    
    except KeyboardInterrupt:
        logger.info("用户中断操作")
        return 1
    except Exception as e:
        logger.error(f"执行失败: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
