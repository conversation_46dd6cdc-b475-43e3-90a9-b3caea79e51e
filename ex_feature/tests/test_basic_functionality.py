#!/usr/bin/env python3
"""
基本功能测试脚本

测试ex_feature模块的基本功能，包括：
1. 配置管理
2. 特征提取器初始化
3. 模拟数据特征提取
4. 并行处理
5. 断点管理
6. 特征验证
"""

import os
import sys
import tempfile
import unittest
from pathlib import Path
import numpy as np

# 添加ex_feature模块到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ex_feature import TemperatureFeatureExtractor, TemperatureConfig
from ex_feature.core.checkpoint import CheckpointManager
from ex_feature.utils.validation import FeatureValidator
from ex_feature.utils.parallel import ParallelProcessor
from ex_feature.features.codon_temp import CodonTemperatureFeatures
from ex_feature.features.genomic_temp import GenomicTemperatureFeatures

class TestBasicFunctionality(unittest.TestCase):
    """基本功能测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        self.config = TemperatureConfig()
    
    def tearDown(self):
        """测试后清理"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_config_management(self):
        """测试配置管理"""
        print("测试配置管理...")
        
        # 测试默认配置
        self.assertEqual(self.config.get('genetic_code'), 11)
        self.assertEqual(self.config.get('num_threads'), 4)
        
        # 测试配置设置
        self.config.set('num_threads', 8)
        self.assertEqual(self.config.get('num_threads'), 8)
        
        # 测试嵌套配置
        self.config.set('codon_features.use_heg_features', False)
        self.assertEqual(self.config.get('codon_features.use_heg_features'), False)
        
        # 测试配置验证
        validation_result = self.config.validate_config()
        self.assertTrue(validation_result['valid'])
        
        print("✅ 配置管理测试通过")
    
    def test_feature_extractor_initialization(self):
        """测试特征提取器初始化"""
        print("测试特征提取器初始化...")
        
        extractor = TemperatureFeatureExtractor(
            num_threads=2,
            enable_checkpoint=False,
            feature_types=['codon', 'genomic'],
            validation_enabled=True
        )
        
        # 检查初始化状态
        self.assertEqual(extractor.num_threads, 2)
        self.assertFalse(extractor.enable_checkpoint)
        self.assertEqual(len(extractor.extractors), 2)
        self.assertIn('codon', extractor.extractors)
        self.assertIn('genomic', extractor.extractors)
        
        # 检查特征提取器信息
        info = extractor.get_feature_info()
        self.assertEqual(info['num_threads'], 2)
        self.assertEqual(len(info['extractors']), 2)
        
        print("✅ 特征提取器初始化测试通过")
    
    def test_codon_features(self):
        """测试密码子特征提取"""
        print("测试密码子特征提取...")
        
        # 创建模拟CDS文件
        cds_content = """>gene1
ATGAAAGCGCTGAAATAA
>gene2
ATGGGCGCGCTGTAA
>gene3
ATGCTGAAAGCGTAA
"""
        cds_file = os.path.join(self.temp_dir, "test_cds.fna")
        with open(cds_file, 'w') as f:
            f.write(cds_content)
        
        # 提取密码子特征
        codon_extractor = CodonTemperatureFeatures(self.config)
        features = codon_extractor.extract_features(cds_file)
        
        # 验证特征
        self.assertIsInstance(features, dict)
        self.assertGreater(len(features), 0)
        self.assertIn('codon_diversity', features)
        self.assertIn('gc_content_pos3', features)
        self.assertIn('thermostability_ratio', features)
        
        # 验证特征值范围
        self.assertGreaterEqual(features['codon_diversity'], 0)
        self.assertLessEqual(features['gc_content_pos3'], 1)
        
        print(f"✅ 密码子特征提取测试通过，提取了 {len(features)} 个特征")
    
    def test_genomic_features(self):
        """测试基因组特征提取"""
        print("测试基因组特征提取...")
        
        # 创建模拟基因组文件
        genome_content = """>chromosome1
ATGCGCGCATGCATGCGCGCATGCATGCGCGCATGCATGCGCGCATGC
GCGCATGCATGCGCGCATGCATGCGCGCATGCATGCGCGCATGCGCGC
ATGCATGCGCGCATGCATGCGCGCATGCATGCGCGCATGCGCGCATGC
"""
        genome_file = os.path.join(self.temp_dir, "test_genome.fna")
        with open(genome_file, 'w') as f:
            f.write(genome_content)
        
        # 提取基因组特征
        genomic_extractor = GenomicTemperatureFeatures(self.config)
        features = genomic_extractor.extract_features(genome_file)
        
        # 验证特征
        self.assertIsInstance(features, dict)
        self.assertGreater(len(features), 0)
        self.assertIn('genome_size', features)
        self.assertIn('gc_content', features)
        self.assertIn('sequence_entropy', features)
        
        # 验证特征值
        self.assertGreater(features['genome_size'], 0)
        self.assertGreaterEqual(features['gc_content'], 0)
        self.assertLessEqual(features['gc_content'], 1)
        
        print(f"✅ 基因组特征提取测试通过，提取了 {len(features)} 个特征")
    
    def test_feature_validation(self):
        """测试特征验证"""
        print("测试特征验证...")
        
        validator = FeatureValidator()
        
        # 测试有效特征
        valid_features = {
            'gc_content': 0.5,
            'genome_size': 1000000,
            'codon_diversity': 3.2,
            'feature1': 0.8,
            'feature2': 1.5
        }
        
        result = validator.validate_features(valid_features)
        self.assertTrue(result['valid'])
        self.assertGreater(result['quality_score'], 0.5)
        
        # 测试无效特征
        invalid_features = {
            'gc_content': 1.5,  # 超出范围
            'genome_size': -1000,  # 负值
            'bad_feature': float('nan'),  # NaN值
            'inf_feature': float('inf')  # 无穷值
        }
        
        result = validator.validate_features(invalid_features)
        self.assertGreater(len(result['range_issues']), 0)
        self.assertGreater(len(result['type_issues']), 0)
        
        print("✅ 特征验证测试通过")
    
    def test_parallel_processing(self):
        """测试并行处理"""
        print("测试并行处理...")
        
        def dummy_task(x):
            """模拟任务"""
            return x * 2
        
        processor = ParallelProcessor(num_workers=2, mode='thread')
        
        # 准备任务
        tasks = [(dummy_task, (i,), {}) for i in range(10)]
        
        # 执行并行处理
        results = processor.process_batch(tasks, "测试任务")
        
        # 验证结果
        self.assertEqual(len(results), 10)
        for i, result in enumerate(results):
            self.assertEqual(result, i * 2)
        
        # 检查统计信息
        stats = processor.get_stats()
        self.assertEqual(stats['total_tasks'], 10)
        self.assertEqual(stats['completed_tasks'], 10)
        self.assertEqual(stats['failed_tasks'], 0)
        
        print("✅ 并行处理测试通过")
    
    def test_checkpoint_management(self):
        """测试断点管理"""
        print("测试断点管理...")
        
        checkpoint_dir = os.path.join(self.temp_dir, "checkpoints")
        manager = CheckpointManager(checkpoint_dir)
        
        # 保存断点
        test_data = {
            'processed_items': ['item1', 'item2', 'item3'],
            'current_progress': 0.6,
            'metadata': {'version': '1.0'}
        }
        
        checkpoint_file = manager.save_checkpoint("test_task", test_data)
        self.assertTrue(os.path.exists(checkpoint_file))
        
        # 加载断点
        loaded_data = manager.load_checkpoint("test_task")
        self.assertIsNotNone(loaded_data)
        self.assertEqual(loaded_data['current_progress'], 0.6)
        self.assertEqual(len(loaded_data['processed_items']), 3)
        
        # 列出断点
        checkpoints = manager.list_checkpoints("test_task")
        self.assertEqual(len(checkpoints), 1)
        
        # 删除断点
        success = manager.delete_checkpoint("test_task")
        self.assertTrue(success)
        
        print("✅ 断点管理测试通过")
    
    def test_integration(self):
        """集成测试"""
        print("测试系统集成...")
        
        # 创建测试数据
        test_files = self._create_test_data()
        
        # 创建特征提取器
        extractor = TemperatureFeatureExtractor(
            num_threads=1,
            enable_checkpoint=False,
            feature_types=['codon', 'genomic'],
            validation_enabled=True
        )
        
        # 提取特征
        features = extractor.extract_single(
            genome_id="test_genome",
            genome_file=test_files['genome'],
            cds_file=test_files['cds']
        )
        
        # 验证结果
        self.assertIsInstance(features, dict)
        self.assertGreater(len(features), 10)  # 应该有多个特征
        self.assertIn('genome_id', features)
        self.assertEqual(features['genome_id'], 'test_genome')
        
        # 检查特征前缀
        codon_features = [k for k in features.keys() if k.startswith('codon_')]
        genomic_features = [k for k in features.keys() if k.startswith('genomic_')]
        
        self.assertGreater(len(codon_features), 0)
        self.assertGreater(len(genomic_features), 0)
        
        print(f"✅ 系统集成测试通过，提取了 {len(features)} 个特征")
    
    def _create_test_data(self):
        """创建测试数据文件"""
        files = {}
        
        # 基因组文件
        genome_content = """>test_chromosome
ATGCGCGCATGCATGCGCGCATGCATGCGCGCATGCATGCGCGCATGC
GCGCATGCATGCGCGCATGCATGCGCGCATGCATGCGCGCATGCGCGC
ATGCATGCGCGCATGCATGCGCGCATGCATGCGCGCATGCGCGCATGC
GCGCATGCATGCGCGCATGCATGCGCGCATGCATGCGCGCATGCGCGC
"""
        files['genome'] = os.path.join(self.temp_dir, "test_genome.fna")
        with open(files['genome'], 'w') as f:
            f.write(genome_content)
        
        # CDS文件
        cds_content = """>gene1
ATGAAAGCGCTGAAATAA
>gene2
ATGGGCGCGCTGTAA
>gene3
ATGCTGAAAGCGTAA
>gene4
ATGGCGAAAGCGCTGTAA
"""
        files['cds'] = os.path.join(self.temp_dir, "test_cds.fna")
        with open(files['cds'], 'w') as f:
            f.write(cds_content)
        
        return files

def run_tests():
    """运行所有测试"""
    print("=" * 60)
    print("Ex_Feature 基本功能测试")
    print("=" * 60)
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestBasicFunctionality)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出结果摘要
    print("\n" + "=" * 60)
    print("测试结果摘要")
    print("=" * 60)
    print(f"运行测试: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback}")
    
    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback}")
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    print(f"\n总体结果: {'✅ 通过' if success else '❌ 失败'}")
    
    return success

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
