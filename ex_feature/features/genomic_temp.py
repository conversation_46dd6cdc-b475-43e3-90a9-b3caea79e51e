"""
基因组温度特征提取器

专门用于提取与温度适应性相关的基因组特征。
基于DeepMu论文和相关研究，某些基因组特征与微生物的温度适应性密切相关。

主要特征类型：
1. GC含量及其分布模式
2. 基因组大小和基因密度
3. 重复序列和转座元件
4. 基因组结构特征
5. 热稳定性相关的序列特征
6. 基因组组织和操纵子结构
"""

import os
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional
from collections import defaultdict, Counter
from Bio import SeqIO
from Bio.SeqUtils import GC, molecular_weight
from Bio.SeqUtils.ProtParam import ProteinAnalysis

from ..utils.config import TemperatureConfig

logger = logging.getLogger(__name__)

class GenomicTemperatureFeatures:
    """
    基因组温度特征提取器
    
    提取与温度适应性相关的基因组特征。
    """
    
    def __init__(self, config: TemperatureConfig):
        """
        初始化基因组温度特征提取器
        
        参数:
            config: 温度配置对象
        """
        self.config = config
        self.window_size = config.get('gc_window_size', 1000)  # GC含量计算窗口大小
        
        logger.info("基因组温度特征提取器初始化完成")
    
    def extract_features(self, genome_file: str) -> Dict[str, float]:
        """
        从基因组文件提取温度相关特征
        
        参数:
            genome_file: 基因组序列文件路径
            
        返回:
            基因组温度特征字典
        """
        logger.info(f"开始提取基因组温度特征: {genome_file}")
        
        # 读取基因组序列
        sequences = self._load_genome_sequences(genome_file)
        if not sequences:
            logger.warning("未找到有效的基因组序列")
            return self._get_empty_features()
        
        # 合并所有序列（如果是多个contig）
        full_genome = ''.join(sequences)
        
        if len(full_genome) == 0:
            logger.warning("基因组序列为空")
            return self._get_empty_features()
        
        # 提取各种基因组特征
        features = {}
        
        # 1. 基本基因组统计
        features.update(self._extract_basic_genome_stats(sequences, full_genome))
        
        # 2. GC含量相关特征
        features.update(self._extract_gc_features(full_genome))
        
        # 3. 核苷酸组成特征
        features.update(self._extract_nucleotide_composition(full_genome))
        
        # 4. 序列复杂度特征
        features.update(self._extract_sequence_complexity(full_genome))
        
        # 5. 重复序列特征
        features.update(self._extract_repeat_features(full_genome))
        
        # 6. 热稳定性相关特征
        features.update(self._extract_thermostability_features(full_genome))
        
        # 7. 基因组结构特征
        features.update(self._extract_structural_features(sequences))
        
        logger.info(f"提取了 {len(features)} 个基因组温度特征")
        
        return features
    
    def _load_genome_sequences(self, genome_file: str) -> List[str]:
        """加载基因组序列"""
        sequences = []
        
        try:
            for record in SeqIO.parse(genome_file, "fasta"):
                seq_str = str(record.seq).upper()
                # 过滤掉包含过多N的序列
                n_content = seq_str.count('N') / len(seq_str) if len(seq_str) > 0 else 1
                if n_content < 0.1:  # N含量小于10%
                    sequences.append(seq_str)
        except Exception as e:
            logger.error(f"读取基因组文件失败: {e}")
        
        return sequences
    
    def _extract_basic_genome_stats(self, sequences: List[str], full_genome: str) -> Dict[str, float]:
        """提取基本基因组统计特征"""
        features = {}
        
        # 基因组大小
        features['genome_size'] = len(full_genome)
        
        # contig数量
        features['contig_count'] = len(sequences)
        
        # 平均contig长度
        if sequences:
            contig_lengths = [len(seq) for seq in sequences]
            features['avg_contig_length'] = np.mean(contig_lengths)
            features['max_contig_length'] = max(contig_lengths)
            features['min_contig_length'] = min(contig_lengths)
            features['contig_length_std'] = np.std(contig_lengths)
            
            # N50统计
            features['n50'] = self._calculate_n50(contig_lengths)
        else:
            features['avg_contig_length'] = 0
            features['max_contig_length'] = 0
            features['min_contig_length'] = 0
            features['contig_length_std'] = 0
            features['n50'] = 0
        
        # N含量
        n_count = full_genome.count('N')
        features['n_content'] = n_count / len(full_genome) if len(full_genome) > 0 else 0
        
        return features
    
    def _calculate_n50(self, contig_lengths: List[int]) -> int:
        """计算N50值"""
        sorted_lengths = sorted(contig_lengths, reverse=True)
        total_length = sum(sorted_lengths)
        target_length = total_length / 2
        
        cumulative_length = 0
        for length in sorted_lengths:
            cumulative_length += length
            if cumulative_length >= target_length:
                return length
        
        return 0
    
    def _extract_gc_features(self, genome: str) -> Dict[str, float]:
        """提取GC含量相关特征"""
        features = {}
        
        # 整体GC含量
        features['gc_content'] = GC(genome) / 100.0
        
        # 滑动窗口GC含量分析
        gc_values = []
        for i in range(0, len(genome) - self.window_size + 1, self.window_size // 2):
            window = genome[i:i + self.window_size]
            if len(window) == self.window_size:
                gc_values.append(GC(window) / 100.0)
        
        if gc_values:
            features['gc_mean'] = np.mean(gc_values)
            features['gc_std'] = np.std(gc_values)
            features['gc_min'] = min(gc_values)
            features['gc_max'] = max(gc_values)
            features['gc_range'] = features['gc_max'] - features['gc_min']
            features['gc_cv'] = features['gc_std'] / features['gc_mean'] if features['gc_mean'] > 0 else 0
        else:
            features['gc_mean'] = features['gc_content']
            features['gc_std'] = 0
            features['gc_min'] = features['gc_content']
            features['gc_max'] = features['gc_content']
            features['gc_range'] = 0
            features['gc_cv'] = 0
        
        # GC偏斜分析
        features.update(self._calculate_gc_skew(genome))
        
        return features
    
    def _calculate_gc_skew(self, genome: str) -> Dict[str, float]:
        """计算GC偏斜"""
        features = {}
        
        g_count = genome.count('G')
        c_count = genome.count('C')
        a_count = genome.count('A')
        t_count = genome.count('T')
        
        # GC偏斜 = (G-C)/(G+C)
        gc_total = g_count + c_count
        if gc_total > 0:
            features['gc_skew'] = (g_count - c_count) / gc_total
        else:
            features['gc_skew'] = 0
        
        # AT偏斜 = (A-T)/(A+T)
        at_total = a_count + t_count
        if at_total > 0:
            features['at_skew'] = (a_count - t_count) / at_total
        else:
            features['at_skew'] = 0
        
        # 滑动窗口GC偏斜分析
        gc_skew_values = []
        for i in range(0, len(genome) - self.window_size + 1, self.window_size):
            window = genome[i:i + self.window_size]
            if len(window) == self.window_size:
                w_g = window.count('G')
                w_c = window.count('C')
                w_gc_total = w_g + w_c
                if w_gc_total > 0:
                    gc_skew_values.append((w_g - w_c) / w_gc_total)
        
        if gc_skew_values:
            features['gc_skew_std'] = np.std(gc_skew_values)
            features['gc_skew_range'] = max(gc_skew_values) - min(gc_skew_values)
        else:
            features['gc_skew_std'] = 0
            features['gc_skew_range'] = 0
        
        return features
    
    def _extract_nucleotide_composition(self, genome: str) -> Dict[str, float]:
        """提取核苷酸组成特征"""
        features = {}
        
        total_length = len(genome)
        if total_length == 0:
            return {'a_content': 0, 't_content': 0, 'g_content': 0, 'c_content': 0}
        
        # 单核苷酸频率
        features['a_content'] = genome.count('A') / total_length
        features['t_content'] = genome.count('T') / total_length
        features['g_content'] = genome.count('G') / total_length
        features['c_content'] = genome.count('C') / total_length
        
        # 二核苷酸频率
        dinucleotides = ['AA', 'AT', 'AG', 'AC', 'TA', 'TT', 'TG', 'TC',
                        'GA', 'GT', 'GG', 'GC', 'CA', 'CT', 'CG', 'CC']
        
        for dinuc in dinucleotides:
            count = 0
            for i in range(len(genome) - 1):
                if genome[i:i+2] == dinuc:
                    count += 1
            features[f'dinuc_{dinuc.lower()}'] = count / (total_length - 1) if total_length > 1 else 0
        
        # CpG含量（在原核生物中也有意义）
        cpg_count = 0
        for i in range(len(genome) - 1):
            if genome[i:i+2] == 'CG':
                cpg_count += 1
        features['cpg_content'] = cpg_count / (total_length - 1) if total_length > 1 else 0
        
        return features

    def _extract_sequence_complexity(self, genome: str) -> Dict[str, float]:
        """提取序列复杂度特征"""
        features = {}

        # 语言复杂度（基于k-mer多样性）
        for k in [2, 3, 4]:
            kmers = []
            for i in range(len(genome) - k + 1):
                kmer = genome[i:i+k]
                if 'N' not in kmer:
                    kmers.append(kmer)

            if kmers:
                unique_kmers = len(set(kmers))
                total_kmers = len(kmers)
                max_possible = 4 ** k

                features[f'kmer{k}_diversity'] = unique_kmers / max_possible
                features[f'kmer{k}_complexity'] = unique_kmers / total_kmers if total_kmers > 0 else 0
            else:
                features[f'kmer{k}_diversity'] = 0
                features[f'kmer{k}_complexity'] = 0

        # 序列熵
        if len(genome) > 0:
            base_counts = Counter(genome)
            total = sum(base_counts.values())
            entropy = 0
            for count in base_counts.values():
                if count > 0:
                    p = count / total
                    entropy -= p * np.log2(p)
            features['sequence_entropy'] = entropy
        else:
            features['sequence_entropy'] = 0

        return features

    def _extract_repeat_features(self, genome: str) -> Dict[str, float]:
        """提取重复序列特征"""
        features = {}

        # 简单重复序列检测
        repeat_patterns = ['A' * 10, 'T' * 10, 'G' * 10, 'C' * 10,
                          'AT' * 5, 'GC' * 5, 'AG' * 5, 'CT' * 5]

        total_repeats = 0
        for pattern in repeat_patterns:
            count = genome.count(pattern)
            total_repeats += count * len(pattern)

        features['simple_repeat_content'] = total_repeats / len(genome) if len(genome) > 0 else 0

        # 回文序列检测（简化版）
        palindrome_count = 0
        for length in [6, 8, 10]:
            for i in range(len(genome) - length + 1):
                seq = genome[i:i+length]
                if seq == seq[::-1].translate(str.maketrans('ATCG', 'TAGC')):
                    palindrome_count += 1

        features['palindrome_density'] = palindrome_count / (len(genome) / 1000) if len(genome) > 0 else 0

        return features

    def _extract_thermostability_features(self, genome: str) -> Dict[str, float]:
        """提取热稳定性相关特征"""
        features = {}

        # 氢键强度相关特征
        gc_bonds = genome.count('G') + genome.count('C')  # 3个氢键
        at_bonds = genome.count('A') + genome.count('T')  # 2个氢键

        total_bases = len(genome)
        if total_bases > 0:
            # 平均氢键强度
            avg_h_bonds = (gc_bonds * 3 + at_bonds * 2) / total_bases
            features['avg_hydrogen_bonds'] = avg_h_bonds

            # 热稳定性指数
            features['thermal_stability_index'] = gc_bonds / total_bases
        else:
            features['avg_hydrogen_bonds'] = 0
            features['thermal_stability_index'] = 0

        # 堆叠能相关特征（基于相邻碱基对）
        stacking_energy = 0
        stacking_weights = {
            'AA': -1.0, 'AT': -0.88, 'AG': -1.28, 'AC': -1.44,
            'TA': -0.58, 'TT': -1.0, 'TG': -1.45, 'TC': -1.28,
            'GA': -1.29, 'GT': -1.44, 'GG': -1.84, 'GC': -2.17,
            'CA': -1.45, 'CT': -1.29, 'CG': -2.36, 'CC': -1.84
        }

        for i in range(len(genome) - 1):
            dinuc = genome[i:i+2]
            if dinuc in stacking_weights:
                stacking_energy += stacking_weights[dinuc]

        features['avg_stacking_energy'] = stacking_energy / (len(genome) - 1) if len(genome) > 1 else 0

        return features
