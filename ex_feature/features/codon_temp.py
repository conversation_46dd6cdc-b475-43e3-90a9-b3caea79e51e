"""
密码子温度特征提取器

专门用于提取与温度适应性相关的密码子特征。
基于DeepMu论文中的发现，某些密码子使用模式与微生物的温度适应性密切相关。

主要特征类型：
1. 温度适应性密码子使用偏好
2. 热稳定性相关密码子模式
3. 密码子-反密码子相互作用强度
4. 温度敏感氨基酸的密码子使用
5. tRNA修饰相关的密码子偏好
6. 翻译效率与温度的关系
"""

import os
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional
from collections import defaultdict, Counter
from Bio import SeqIO
from Bio.SeqUtils import CodonUsage
from Bio.Data import CodonTable

from ..utils.config import TemperatureConfig

logger = logging.getLogger(__name__)

class CodonTemperatureFeatures:
    """
    密码子温度特征提取器
    
    提取与温度适应性相关的密码子使用特征。
    """
    
    def __init__(self, config: TemperatureConfig):
        """
        初始化密码子温度特征提取器
        
        参数:
            config: 温度配置对象
        """
        self.config = config
        self.genetic_code = config.get('genetic_code', 11)
        
        # 获取密码子表
        self.codon_table = CodonTable.unambiguous_dna_by_id[self.genetic_code]
        
        # 定义温度相关的氨基酸和密码子
        self._init_temperature_related_codons()
        
        logger.info("密码子温度特征提取器初始化完成")
    
    def _init_temperature_related_codons(self):
        """初始化温度相关的密码子和氨基酸"""
        
        # 热稳定性相关氨基酸（倾向于在高温环境中使用）
        self.thermostable_aa = {
            'G': 'Glycine',      # 甘氨酸 - 增加蛋白质柔性
            'P': 'Proline',      # 脯氨酸 - 增加结构刚性
            'A': 'Alanine',      # 丙氨酸 - 小侧链，稳定
            'V': 'Valine',       # 缬氨酸 - 疏水性，稳定
            'I': 'Isoleucine',   # 异亮氨酸 - 疏水性
            'L': 'Leucine'       # 亮氨酸 - 疏水性
        }
        
        # 温度敏感氨基酸（在低温环境中更常见）
        self.thermolabile_aa = {
            'Q': 'Glutamine',    # 谷氨酰胺 - 极性，氢键
            'N': 'Asparagine',   # 天冬酰胺 - 极性，氢键
            'S': 'Serine',       # 丝氨酸 - 极性，小
            'T': 'Threonine',    # 苏氨酸 - 极性
            'C': 'Cysteine',     # 半胱氨酸 - 二硫键
            'M': 'Methionine'    # 甲硫氨酸 - 含硫
        }
        
        # GC含量相关的密码子（高GC通常与高温适应相关）
        self.gc_rich_codons = []
        self.gc_poor_codons = []
        
        for codon in self.codon_table.forward_table:
            gc_content = (codon.count('G') + codon.count('C')) / 3
            if gc_content >= 2/3:  # GC含量 >= 66.7%
                self.gc_rich_codons.append(codon)
            elif gc_content <= 1/3:  # GC含量 <= 33.3%
                self.gc_poor_codons.append(codon)
        
        # 密码子-反密码子相互作用强度（基于氢键数量）
        self.codon_stability = self._calculate_codon_stability()
    
    def _calculate_codon_stability(self) -> Dict[str, float]:
        """计算密码子-反密码子相互作用的热稳定性"""
        stability = {}
        
        # 碱基配对强度 (G-C > A-T)
        base_strength = {'G': 3, 'C': 3, 'A': 2, 'T': 2}
        
        for codon in self.codon_table.forward_table:
            # 计算密码子的总氢键强度
            strength = sum(base_strength[base] for base in codon)
            stability[codon] = strength / 9.0  # 标准化到0-1
        
        return stability
    
    def extract_features(self, cds_file: str, ko_file: Optional[str] = None) -> Dict[str, float]:
        """
        从CDS文件提取密码子温度特征
        
        参数:
            cds_file: CDS序列文件路径
            ko_file: KO注释文件路径（可选）
            
        返回:
            密码子温度特征字典
        """
        logger.info(f"开始提取密码子温度特征: {cds_file}")
        
        # 读取CDS序列
        sequences = self._load_cds_sequences(cds_file)
        if not sequences:
            logger.warning("未找到有效的CDS序列")
            return self._get_empty_features()
        
        # 加载KO注释（如果提供）
        ko_annotations = {}
        if ko_file and os.path.exists(ko_file):
            ko_annotations = self._load_ko_annotations(ko_file)
        
        # 计算密码子使用频率
        codon_counts = self._count_codons(sequences)
        total_codons = sum(codon_counts.values())
        
        if total_codons == 0:
            logger.warning("未找到有效的密码子")
            return self._get_empty_features()
        
        # 提取各种温度相关特征
        features = {}
        
        # 1. 基本密码子使用特征
        features.update(self._extract_basic_codon_features(codon_counts, total_codons))
        
        # 2. 温度适应性特征
        features.update(self._extract_temperature_adaptation_features(codon_counts, total_codons))
        
        # 3. GC含量相关特征
        features.update(self._extract_gc_content_features(codon_counts, total_codons))
        
        # 4. 密码子稳定性特征
        features.update(self._extract_stability_features(codon_counts, total_codons))
        
        # 5. 氨基酸组成特征
        features.update(self._extract_amino_acid_features(codon_counts, total_codons))
        
        # 6. 同义密码子使用偏好
        features.update(self._extract_synonymous_codon_features(codon_counts))
        
        # 7. 高表达基因特征（如果有KO注释）
        if ko_annotations:
            features.update(self._extract_heg_features(sequences, ko_annotations))
        
        logger.info(f"提取了 {len(features)} 个密码子温度特征")
        
        return features
    
    def _load_cds_sequences(self, cds_file: str) -> List[str]:
        """加载CDS序列"""
        sequences = []
        
        try:
            for record in SeqIO.parse(cds_file, "fasta"):
                seq_str = str(record.seq).upper()
                # 检查序列长度是否为3的倍数
                if len(seq_str) % 3 == 0 and len(seq_str) >= 3:
                    sequences.append(seq_str)
        except Exception as e:
            logger.error(f"读取CDS文件失败: {e}")
        
        return sequences
    
    def _load_ko_annotations(self, ko_file: str) -> Dict[str, str]:
        """加载KO注释"""
        ko_annotations = {}
        
        try:
            df = pd.read_csv(ko_file, sep='\t', header=None)
            if len(df.columns) >= 2:
                ko_annotations = dict(zip(df.iloc[:, 0], df.iloc[:, 1]))
        except Exception as e:
            logger.error(f"读取KO文件失败: {e}")
        
        return ko_annotations
    
    def _count_codons(self, sequences: List[str]) -> Dict[str, int]:
        """统计密码子使用频率"""
        codon_counts = defaultdict(int)
        
        for sequence in sequences:
            # 按密码子分割序列
            for i in range(0, len(sequence) - 2, 3):
                codon = sequence[i:i+3]
                
                # 检查是否为有效密码子
                if (len(codon) == 3 and 
                    all(base in 'ATCG' for base in codon) and
                    codon in self.codon_table.forward_table):
                    codon_counts[codon] += 1
        
        return dict(codon_counts)
    
    def _extract_basic_codon_features(self, codon_counts: Dict[str, int], total_codons: int) -> Dict[str, float]:
        """提取基本密码子使用特征"""
        features = {}
        
        # 密码子多样性（Shannon熵）
        if total_codons > 0:
            entropy = 0
            for count in codon_counts.values():
                if count > 0:
                    freq = count / total_codons
                    entropy -= freq * np.log2(freq)
            features['codon_diversity'] = entropy
        else:
            features['codon_diversity'] = 0
        
        # 有效密码子数量
        features['effective_codon_number'] = len([c for c in codon_counts.values() if c > 0])
        
        # 密码子使用均匀性
        if len(codon_counts) > 0:
            max_entropy = np.log2(len(codon_counts))
            features['codon_evenness'] = features['codon_diversity'] / max_entropy if max_entropy > 0 else 0
        else:
            features['codon_evenness'] = 0
        
        return features
    
    def _extract_temperature_adaptation_features(self, codon_counts: Dict[str, int], total_codons: int) -> Dict[str, float]:
        """提取温度适应性特征"""
        features = {}
        
        # 热稳定性氨基酸的密码子使用频率
        thermostable_codons = 0
        thermolabile_codons = 0
        
        for codon, count in codon_counts.items():
            aa = self.codon_table.forward_table[codon]
            if aa in self.thermostable_aa:
                thermostable_codons += count
            elif aa in self.thermolabile_aa:
                thermolabile_codons += count
        
        if total_codons > 0:
            features['thermostable_codon_freq'] = thermostable_codons / total_codons
            features['thermolabile_codon_freq'] = thermolabile_codons / total_codons
            features['thermostability_ratio'] = (thermostable_codons / (thermolabile_codons + 1))
        else:
            features['thermostable_codon_freq'] = 0
            features['thermolabile_codon_freq'] = 0
            features['thermostability_ratio'] = 0
        
        return features

    def _extract_gc_content_features(self, codon_counts: Dict[str, int], total_codons: int) -> Dict[str, float]:
        """提取GC含量相关特征"""
        features = {}

        gc_rich_count = sum(codon_counts.get(codon, 0) for codon in self.gc_rich_codons)
        gc_poor_count = sum(codon_counts.get(codon, 0) for codon in self.gc_poor_codons)

        if total_codons > 0:
            features['gc_rich_codon_freq'] = gc_rich_count / total_codons
            features['gc_poor_codon_freq'] = gc_poor_count / total_codons
            features['gc_codon_bias'] = (gc_rich_count - gc_poor_count) / total_codons
        else:
            features['gc_rich_codon_freq'] = 0
            features['gc_poor_codon_freq'] = 0
            features['gc_codon_bias'] = 0

        # 计算密码子位置特异性GC含量
        gc_pos1, gc_pos2, gc_pos3 = 0, 0, 0
        total_pos = 0

        for codon, count in codon_counts.items():
            total_pos += count
            gc_pos1 += count if codon[0] in 'GC' else 0
            gc_pos2 += count if codon[1] in 'GC' else 0
            gc_pos3 += count if codon[2] in 'GC' else 0

        if total_pos > 0:
            features['gc_content_pos1'] = gc_pos1 / total_pos
            features['gc_content_pos2'] = gc_pos2 / total_pos
            features['gc_content_pos3'] = gc_pos3 / total_pos
            features['gc_content_wobble'] = features['gc_content_pos3']  # 摆动位置
        else:
            features['gc_content_pos1'] = 0
            features['gc_content_pos2'] = 0
            features['gc_content_pos3'] = 0
            features['gc_content_wobble'] = 0

        return features

    def _extract_stability_features(self, codon_counts: Dict[str, int], total_codons: int) -> Dict[str, float]:
        """提取密码子稳定性特征"""
        features = {}

        if total_codons > 0:
            # 加权平均稳定性
            weighted_stability = sum(
                codon_counts.get(codon, 0) * stability
                for codon, stability in self.codon_stability.items()
            ) / total_codons

            features['avg_codon_stability'] = weighted_stability

            # 高稳定性密码子比例
            high_stability_count = sum(
                codon_counts.get(codon, 0)
                for codon, stability in self.codon_stability.items()
                if stability > 0.7
            )
            features['high_stability_codon_freq'] = high_stability_count / total_codons

            # 低稳定性密码子比例
            low_stability_count = sum(
                codon_counts.get(codon, 0)
                for codon, stability in self.codon_stability.items()
                if stability < 0.5
            )
            features['low_stability_codon_freq'] = low_stability_count / total_codons

        else:
            features['avg_codon_stability'] = 0
            features['high_stability_codon_freq'] = 0
            features['low_stability_codon_freq'] = 0

        return features

    def _extract_amino_acid_features(self, codon_counts: Dict[str, int], total_codons: int) -> Dict[str, float]:
        """提取氨基酸组成特征"""
        features = {}

        # 统计氨基酸使用频率
        aa_counts = defaultdict(int)
        for codon, count in codon_counts.items():
            aa = self.codon_table.forward_table[codon]
            aa_counts[aa] += count

        if total_codons > 0:
            # 各种氨基酸类别的频率
            hydrophobic_aa = {'A', 'V', 'I', 'L', 'M', 'F', 'Y', 'W'}
            polar_aa = {'S', 'T', 'N', 'Q'}
            charged_aa = {'D', 'E', 'K', 'R', 'H'}
            special_aa = {'C', 'G', 'P'}

            hydrophobic_count = sum(aa_counts.get(aa, 0) for aa in hydrophobic_aa)
            polar_count = sum(aa_counts.get(aa, 0) for aa in polar_aa)
            charged_count = sum(aa_counts.get(aa, 0) for aa in charged_aa)
            special_count = sum(aa_counts.get(aa, 0) for aa in special_aa)

            features['hydrophobic_aa_freq'] = hydrophobic_count / total_codons
            features['polar_aa_freq'] = polar_count / total_codons
            features['charged_aa_freq'] = charged_count / total_codons
            features['special_aa_freq'] = special_count / total_codons

            # 氨基酸多样性
            aa_entropy = 0
            for count in aa_counts.values():
                if count > 0:
                    freq = count / total_codons
                    aa_entropy -= freq * np.log2(freq)
            features['amino_acid_diversity'] = aa_entropy

        else:
            features['hydrophobic_aa_freq'] = 0
            features['polar_aa_freq'] = 0
            features['charged_aa_freq'] = 0
            features['special_aa_freq'] = 0
            features['amino_acid_diversity'] = 0

        return features

    def _extract_synonymous_codon_features(self, codon_counts: Dict[str, int]) -> Dict[str, float]:
        """提取同义密码子使用偏好特征"""
        features = {}

        # 按氨基酸分组密码子
        aa_codons = defaultdict(list)
        for codon in self.codon_table.forward_table:
            aa = self.codon_table.forward_table[codon]
            aa_codons[aa].append(codon)

        # 计算同义密码子使用偏好
        rscu_values = []  # Relative Synonymous Codon Usage

        for aa, codons in aa_codons.items():
            if len(codons) > 1:  # 只考虑有同义密码子的氨基酸
                aa_total = sum(codon_counts.get(codon, 0) for codon in codons)

                if aa_total > 0:
                    expected_freq = aa_total / len(codons)
                    for codon in codons:
                        observed_freq = codon_counts.get(codon, 0)
                        rscu = observed_freq / expected_freq if expected_freq > 0 else 0
                        rscu_values.append(rscu)

        if rscu_values:
            features['rscu_mean'] = np.mean(rscu_values)
            features['rscu_std'] = np.std(rscu_values)
            features['rscu_max'] = np.max(rscu_values)
            features['rscu_min'] = np.min(rscu_values)
        else:
            features['rscu_mean'] = 0
            features['rscu_std'] = 0
            features['rscu_max'] = 0
            features['rscu_min'] = 0

        return features

    def _extract_heg_features(self, sequences: List[str], ko_annotations: Dict[str, str]) -> Dict[str, float]:
        """提取高表达基因相关特征"""
        features = {}

        # 定义高表达基因的KO ID（基于文献和数据库）
        heg_ko_ids = {
            'K02945', 'K02946', 'K02947',  # ribosomal proteins
            'K02874', 'K02875', 'K02876',  # ribosomal proteins
            'K01409', 'K01410',            # translation factors
            'K02358', 'K02359', 'K02360'   # DNA polymerase
        }

        # 分离高表达基因和背景基因的序列
        heg_sequences = []
        bg_sequences = []

        for i, seq in enumerate(sequences):
            seq_id = f"seq_{i}"  # 简化的序列ID
            if seq_id in ko_annotations and ko_annotations[seq_id] in heg_ko_ids:
                heg_sequences.append(seq)
            else:
                bg_sequences.append(seq)

        # 分别计算HEG和背景基因的密码子特征
        if heg_sequences:
            heg_codon_counts = self._count_codons(heg_sequences)
            heg_total = sum(heg_codon_counts.values())

            if heg_total > 0:
                heg_gc_rich = sum(heg_codon_counts.get(codon, 0) for codon in self.gc_rich_codons)
                features['heg_gc_rich_freq'] = heg_gc_rich / heg_total

                heg_stability = sum(
                    heg_codon_counts.get(codon, 0) * stability
                    for codon, stability in self.codon_stability.items()
                ) / heg_total
                features['heg_avg_stability'] = heg_stability

        if bg_sequences:
            bg_codon_counts = self._count_codons(bg_sequences)
            bg_total = sum(bg_codon_counts.values())

            if bg_total > 0:
                bg_gc_rich = sum(bg_codon_counts.get(codon, 0) for codon in self.gc_rich_codons)
                features['bg_gc_rich_freq'] = bg_gc_rich / bg_total

                bg_stability = sum(
                    bg_codon_counts.get(codon, 0) * stability
                    for codon, stability in self.codon_stability.items()
                ) / bg_total
                features['bg_avg_stability'] = bg_stability

        # 计算HEG和背景基因的差异
        if 'heg_gc_rich_freq' in features and 'bg_gc_rich_freq' in features:
            features['heg_bg_gc_diff'] = features['heg_gc_rich_freq'] - features['bg_gc_rich_freq']

        if 'heg_avg_stability' in features and 'bg_avg_stability' in features:
            features['heg_bg_stability_diff'] = features['heg_avg_stability'] - features['bg_avg_stability']

        return features

    def _get_empty_features(self) -> Dict[str, float]:
        """返回空特征字典"""
        return {
            'codon_diversity': 0,
            'effective_codon_number': 0,
            'codon_evenness': 0,
            'thermostable_codon_freq': 0,
            'thermolabile_codon_freq': 0,
            'thermostability_ratio': 0,
            'gc_rich_codon_freq': 0,
            'gc_poor_codon_freq': 0,
            'gc_codon_bias': 0,
            'gc_content_pos1': 0,
            'gc_content_pos2': 0,
            'gc_content_pos3': 0,
            'gc_content_wobble': 0,
            'avg_codon_stability': 0,
            'high_stability_codon_freq': 0,
            'low_stability_codon_freq': 0,
            'hydrophobic_aa_freq': 0,
            'polar_aa_freq': 0,
            'charged_aa_freq': 0,
            'special_aa_freq': 0,
            'amino_acid_diversity': 0,
            'rscu_mean': 0,
            'rscu_std': 0,
            'rscu_max': 0,
            'rscu_min': 0
        }

    def get_feature_names(self) -> List[str]:
        """获取所有特征名称"""
        return list(self._get_empty_features().keys())

    def get_feature_metadata(self) -> Dict[str, Any]:
        """获取特征元数据"""
        return {
            'feature_type': 'codon_temperature',
            'description': '密码子温度适应性特征',
            'feature_count': len(self.get_feature_names()),
            'genetic_code': self.genetic_code,
            'thermostable_aa': self.thermostable_aa,
            'thermolabile_aa': self.thermolabile_aa,
            'gc_rich_codons': len(self.gc_rich_codons),
            'gc_poor_codons': len(self.gc_poor_codons)
        }
