"""
断点管理器

提供断点恢复功能，支持大规模特征提取任务的中断恢复。

主要功能：
1. 断点数据保存和加载
2. 进度跟踪和恢复
3. 任务状态管理
4. 错误恢复机制
5. 断点数据压缩和优化
"""

import os
import json
import pickle
import logging
import gzip
from typing import Dict, Any, Optional, List
from datetime import datetime
from pathlib import Path
import threading
import time

logger = logging.getLogger(__name__)

class CheckpointManager:
    """
    断点管理器
    
    负责管理特征提取过程中的断点保存和恢复功能。
    """
    
    def __init__(self, 
                 checkpoint_dir: str = "checkpoints",
                 compression: bool = True,
                 auto_cleanup: bool = True,
                 max_checkpoints: int = 10):
        """
        初始化断点管理器
        
        参数:
            checkpoint_dir: 断点文件保存目录
            compression: 是否压缩断点文件
            auto_cleanup: 是否自动清理旧断点
            max_checkpoints: 最大保留断点数量
        """
        self.checkpoint_dir = Path(checkpoint_dir)
        self.compression = compression
        self.auto_cleanup = auto_cleanup
        self.max_checkpoints = max_checkpoints
        
        # 创建断点目录
        self.checkpoint_dir.mkdir(parents=True, exist_ok=True)
        
        # 线程锁，确保断点操作的线程安全
        self._lock = threading.Lock()
        
        logger.info(f"断点管理器初始化完成: {checkpoint_dir}")
    
    def save_checkpoint(self, 
                       task_name: str, 
                       data: Dict[str, Any],
                       metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        保存断点数据
        
        参数:
            task_name: 任务名称
            data: 要保存的数据
            metadata: 元数据（可选）
            
        返回:
            断点文件路径
        """
        with self._lock:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            checkpoint_name = f"{task_name}_{timestamp}"
            
            # 准备断点数据
            checkpoint_data = {
                'task_name': task_name,
                'timestamp': timestamp,
                'data': data,
                'metadata': metadata or {},
                'version': '1.0'
            }
            
            # 选择文件格式
            if self.compression:
                checkpoint_file = self.checkpoint_dir / f"{checkpoint_name}.pkl.gz"
                self._save_compressed(checkpoint_file, checkpoint_data)
            else:
                checkpoint_file = self.checkpoint_dir / f"{checkpoint_name}.pkl"
                self._save_uncompressed(checkpoint_file, checkpoint_data)
            
            logger.info(f"保存断点: {checkpoint_file}")
            
            # 自动清理旧断点
            if self.auto_cleanup:
                self._cleanup_old_checkpoints(task_name)
            
            return str(checkpoint_file)
    
    def load_checkpoint(self, task_name: str) -> Optional[Dict[str, Any]]:
        """
        加载最新的断点数据
        
        参数:
            task_name: 任务名称
            
        返回:
            断点数据，如果没有找到则返回None
        """
        with self._lock:
            # 查找最新的断点文件
            checkpoint_file = self._find_latest_checkpoint(task_name)
            
            if checkpoint_file is None:
                logger.info(f"未找到任务 {task_name} 的断点文件")
                return None
            
            try:
                # 加载断点数据
                if checkpoint_file.suffix == '.gz':
                    checkpoint_data = self._load_compressed(checkpoint_file)
                else:
                    checkpoint_data = self._load_uncompressed(checkpoint_file)
                
                logger.info(f"加载断点: {checkpoint_file}")
                return checkpoint_data.get('data')
                
            except Exception as e:
                logger.error(f"加载断点失败 {checkpoint_file}: {e}")
                return None
    
    def list_checkpoints(self, task_name: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        列出断点文件
        
        参数:
            task_name: 任务名称（可选，如果不指定则列出所有断点）
            
        返回:
            断点信息列表
        """
        checkpoints = []
        
        pattern = f"{task_name}_*.pkl*" if task_name else "*.pkl*"
        
        for checkpoint_file in self.checkpoint_dir.glob(pattern):
            try:
                # 解析文件名获取信息
                name_parts = checkpoint_file.stem.split('_')
                if len(name_parts) >= 3:
                    task = '_'.join(name_parts[:-2])
                    timestamp = '_'.join(name_parts[-2:])
                    
                    checkpoint_info = {
                        'file': str(checkpoint_file),
                        'task_name': task,
                        'timestamp': timestamp,
                        'size': checkpoint_file.stat().st_size,
                        'modified': datetime.fromtimestamp(checkpoint_file.stat().st_mtime)
                    }
                    checkpoints.append(checkpoint_info)
                    
            except Exception as e:
                logger.warning(f"解析断点文件信息失败 {checkpoint_file}: {e}")
        
        # 按时间戳排序
        checkpoints.sort(key=lambda x: x['timestamp'], reverse=True)
        
        return checkpoints
    
    def delete_checkpoint(self, task_name: str, timestamp: Optional[str] = None) -> bool:
        """
        删除断点文件
        
        参数:
            task_name: 任务名称
            timestamp: 时间戳（可选，如果不指定则删除最新的）
            
        返回:
            是否删除成功
        """
        with self._lock:
            if timestamp:
                # 删除指定时间戳的断点
                pattern = f"{task_name}_{timestamp}.pkl*"
                checkpoint_files = list(self.checkpoint_dir.glob(pattern))
            else:
                # 删除最新的断点
                checkpoint_file = self._find_latest_checkpoint(task_name)
                checkpoint_files = [checkpoint_file] if checkpoint_file else []
            
            deleted_count = 0
            for checkpoint_file in checkpoint_files:
                try:
                    checkpoint_file.unlink()
                    logger.info(f"删除断点: {checkpoint_file}")
                    deleted_count += 1
                except Exception as e:
                    logger.error(f"删除断点失败 {checkpoint_file}: {e}")
            
            return deleted_count > 0
    
    def _find_latest_checkpoint(self, task_name: str) -> Optional[Path]:
        """查找最新的断点文件"""
        pattern = f"{task_name}_*.pkl*"
        checkpoint_files = list(self.checkpoint_dir.glob(pattern))
        
        if not checkpoint_files:
            return None
        
        # 按修改时间排序，返回最新的
        checkpoint_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
        return checkpoint_files[0]
    
    def _save_compressed(self, file_path: Path, data: Dict[str, Any]) -> None:
        """保存压缩的断点文件"""
        with gzip.open(file_path, 'wb') as f:
            pickle.dump(data, f)
    
    def _save_uncompressed(self, file_path: Path, data: Dict[str, Any]) -> None:
        """保存未压缩的断点文件"""
        with open(file_path, 'wb') as f:
            pickle.dump(data, f)
    
    def _load_compressed(self, file_path: Path) -> Dict[str, Any]:
        """加载压缩的断点文件"""
        with gzip.open(file_path, 'rb') as f:
            return pickle.load(f)
    
    def _load_uncompressed(self, file_path: Path) -> Dict[str, Any]:
        """加载未压缩的断点文件"""
        with open(file_path, 'rb') as f:
            return pickle.load(f)
    
    def _cleanup_old_checkpoints(self, task_name: str) -> None:
        """清理旧的断点文件"""
        checkpoints = self.list_checkpoints(task_name)
        
        if len(checkpoints) > self.max_checkpoints:
            # 删除超出限制的旧断点
            old_checkpoints = checkpoints[self.max_checkpoints:]
            
            for checkpoint in old_checkpoints:
                try:
                    Path(checkpoint['file']).unlink()
                    logger.debug(f"清理旧断点: {checkpoint['file']}")
                except Exception as e:
                    logger.warning(f"清理断点失败 {checkpoint['file']}: {e}")
    
    def get_checkpoint_stats(self) -> Dict[str, Any]:
        """获取断点统计信息"""
        all_checkpoints = self.list_checkpoints()
        
        stats = {
            'total_checkpoints': len(all_checkpoints),
            'total_size': sum(cp['size'] for cp in all_checkpoints),
            'tasks': {},
            'oldest': None,
            'newest': None
        }
        
        if all_checkpoints:
            # 按任务分组统计
            for checkpoint in all_checkpoints:
                task_name = checkpoint['task_name']
                if task_name not in stats['tasks']:
                    stats['tasks'][task_name] = {
                        'count': 0,
                        'size': 0,
                        'latest': None
                    }
                
                stats['tasks'][task_name]['count'] += 1
                stats['tasks'][task_name]['size'] += checkpoint['size']
                
                if (stats['tasks'][task_name]['latest'] is None or 
                    checkpoint['timestamp'] > stats['tasks'][task_name]['latest']):
                    stats['tasks'][task_name]['latest'] = checkpoint['timestamp']
            
            # 最新和最旧的断点
            stats['oldest'] = min(cp['modified'] for cp in all_checkpoints)
            stats['newest'] = max(cp['modified'] for cp in all_checkpoints)
        
        return stats
    
    def cleanup_all(self) -> int:
        """清理所有断点文件"""
        with self._lock:
            checkpoint_files = list(self.checkpoint_dir.glob("*.pkl*"))
            deleted_count = 0
            
            for checkpoint_file in checkpoint_files:
                try:
                    checkpoint_file.unlink()
                    deleted_count += 1
                except Exception as e:
                    logger.error(f"删除断点失败 {checkpoint_file}: {e}")
            
            logger.info(f"清理了 {deleted_count} 个断点文件")
            return deleted_count
