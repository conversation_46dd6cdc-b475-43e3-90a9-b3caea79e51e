"""Enhanced Neural Network Models Module

This module implements improved neural network models for microbial growth rate
and optimal growth temperature prediction, with architectural enhancements:
1. Increased model capacity
2. Attention mechanisms
3. Residual connections
"""

import math
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Optional, List, Tuple, Union

from .networks import EnhancedPhyloGrowthModel
from .attention import PositionalAttention
from .phylo import EnhancedPhyloBranch
from ..utils.logging import get_logger, log_function_call

logger = get_logger()

class ResidualBlock(nn.Module):
    """Residual block with skip connection."""

    def __init__(self, input_dim, hidden_dim=None, dropout=0.1):
        super().__init__()
        if hidden_dim is None:
            hidden_dim = input_dim

        self.layer1 = nn.Linear(input_dim, hidden_dim)
        self.layer2 = nn.Linear(hidden_dim, input_dim)
        self.norm1 = nn.LayerNorm(hidden_dim)
        self.norm2 = nn.LayerNorm(input_dim)
        self.dropout = nn.Dropout(dropout)

    def forward(self, x):
        # First layer
        residual = x
        x = self.layer1(x)
        x = F.relu(x)
        x = self.norm1(x)
        x = self.dropout(x)

        # Second layer with residual connection
        x = self.layer2(x)
        x = self.norm2(x + residual)  # Add residual and normalize
        x = self.dropout(x)

        return x

class CrossAttention(nn.Module):
    """Cross-attention between different feature types."""
    def __init__(self, query_dim, key_dim, value_dim, heads=4, dropout=0.1):
        super().__init__()
        self.heads = heads
        self.head_dim = query_dim // heads

        # Projection matrices
        self.query_proj = nn.Linear(query_dim, query_dim)
        self.key_proj = nn.Linear(key_dim, query_dim)
        self.value_proj = nn.Linear(value_dim, query_dim)

        # Output projection
        self.output_proj = nn.Linear(query_dim, query_dim)
        self.dropout = nn.Dropout(dropout)
        self.norm = nn.LayerNorm(query_dim)

    def forward(self, query, key, value):
        batch_size = query.size(0)

        # Project inputs
        q = self.query_proj(query).view(batch_size, -1, self.heads, self.head_dim).transpose(1, 2)
        k = self.key_proj(key).view(batch_size, -1, self.heads, self.head_dim).transpose(1, 2)
        v = self.value_proj(value).view(batch_size, -1, self.heads, self.head_dim).transpose(1, 2)

        # Compute attention scores
        scores = torch.matmul(q, k.transpose(-2, -1)) / math.sqrt(self.head_dim)
        attn = F.softmax(scores, dim=-1)
        attn = self.dropout(attn)

        # Apply attention
        out = torch.matmul(attn, v)
        out = out.transpose(1, 2).contiguous().view(batch_size, -1, self.heads * self.head_dim)

        # Project output
        out = self.output_proj(out)
        out = self.dropout(out)
        out = self.norm(out + query)  # Residual connection

        return out

class CrossModalAttention(nn.Module):
    """
    Cross-modal attention between different feature types.

    This module enables direct interaction between different feature modalities
    by implementing cross-modal attention. It allows one feature type to attend
    to another, capturing dependencies and enabling more effective multi-modal learning.
    """
    def __init__(self, dim1, dim2):
        super().__init__()
        self.query_proj = nn.Linear(dim1, 64)
        self.key_proj = nn.Linear(dim2, 64)
        self.value_proj = nn.Linear(dim2, 64)
        # 注意：移除 batch_first=True 以兼容较旧版本的 PyTorch
        self.attention = nn.MultiheadAttention(64, 4)
        self.output_proj = nn.Linear(64, dim1)
        self.norm = nn.LayerNorm(dim1)

    def forward(self, x1, x2):
        """
        Apply cross-modal attention from x1 to x2.

        Args:
            x1: First feature tensor [batch_size, dim1]
            x2: Second feature tensor [batch_size, dim2]

        Returns:
            Updated feature tensor for x1 [batch_size, dim1]
        """
        batch_size = x1.size(0)

        # Project inputs and reshape for attention
        q = self.query_proj(x1).unsqueeze(1)  # [batch_size, 1, 64]
        k = self.key_proj(x2).unsqueeze(1)    # [batch_size, 1, 64]
        v = self.value_proj(x2).unsqueeze(1)  # [batch_size, 1, 64]

        # 转换为 MultiheadAttention 期望的格式 [seq_len, batch_size, embed_dim]
        q = q.transpose(0, 1)  # [1, batch_size, 64]
        k = k.transpose(0, 1)  # [1, batch_size, 64]
        v = v.transpose(0, 1)  # [1, batch_size, 64]

        # Apply attention
        attn_output, _ = self.attention(q, k, v)

        # 转换回 [batch_size, seq_len, embed_dim] 格式
        attn_output = attn_output.transpose(0, 1)  # [batch_size, 1, 64]
        attn_output = attn_output.squeeze(1)  # [batch_size, 64]

        # Project back to original dimension
        out = self.output_proj(attn_output)  # [batch_size, dim1]

        # Apply residual connection and normalization
        out = self.norm(x1 + out)

        return out

class ImprovedPathwayBranch(nn.Module):
    """Enhanced pathway branch with residual connections and self-attention."""

    def __init__(self, input_dim, hidden_dim, output_dim, num_layers=3, dropout=0.1):
        super().__init__()

        # Initial feature transformation
        self.input_transform = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.LayerNorm(hidden_dim),
            nn.Dropout(dropout)
        )

        # Residual blocks
        self.residual_blocks = nn.ModuleList([
            ResidualBlock(hidden_dim, hidden_dim, dropout)
            for _ in range(num_layers)
        ])

        # Self-attention mechanism
        # 注意：移除 batch_first=True 以兼容较旧版本的 PyTorch
        self.self_attention = nn.MultiheadAttention(
            embed_dim=hidden_dim,
            num_heads=4,
            dropout=dropout
        )

        # Output transformation
        self.output_transform = nn.Sequential(
            nn.Linear(hidden_dim, output_dim),
            nn.ReLU(),
            nn.LayerNorm(output_dim)
        )

    def forward(self, x):
        # Initial transformation
        x = self.input_transform(x)

        # Apply residual blocks
        for block in self.residual_blocks:
            x = block(x)

        # Reshape for self-attention if needed
        if len(x.shape) == 2:
            x = x.unsqueeze(1)  # Add sequence dimension

        # 转换为 MultiheadAttention 期望的格式 [seq_len, batch_size, embed_dim]
        x_transposed = x.transpose(0, 1)

        # Apply self-attention
        x_attn, _ = self.self_attention(x_transposed, x_transposed, x_transposed)

        # 转换回 [batch_size, seq_len, embed_dim] 格式
        x_attn = x_attn.transpose(0, 1)

        x = x + x_attn  # Residual connection

        # Remove sequence dimension if it was added
        if x.size(1) == 1:
            x = x.squeeze(1)

        # Output transformation
        x = self.output_transform(x)

        return x

class FeatureFusionModule(nn.Module):
    """Enhanced feature fusion with cross-attention between different feature types."""

    def __init__(self, feature_dims, fusion_dim, dropout=0.1):
        super().__init__()

        self.feature_dims = feature_dims
        self.fusion_dim = fusion_dim

        # Projection layers for each feature type
        self.projections = nn.ModuleDict({
            name: nn.Linear(dim, fusion_dim)
            for name, dim in feature_dims.items()
        })

        # Cross-modal attention between feature types
        self.cross_attentions = nn.ModuleDict()
        feature_names = list(feature_dims.keys())
        for i, name1 in enumerate(feature_names):
            for name2 in feature_names:
                if name1 != name2:
                    key = f"{name1}_to_{name2}"
                    self.cross_attentions[key] = CrossModalAttention(fusion_dim, fusion_dim)

        # Track feature importance
        self.feature_importance = nn.Parameter(
            torch.ones(len(feature_dims)) / len(feature_dims)
        )

        # Final fusion layer
        self.fusion_layer = nn.Sequential(
            nn.Linear(fusion_dim, fusion_dim),
            nn.ReLU(),
            nn.LayerNorm(fusion_dim),
            nn.Dropout(dropout)
        )

    def forward(self, features):
        # Get batch size from any feature
        batch_size = next(iter(features.values())).size(0)
        device = next(iter(features.values())).device

        # Project each feature to common dimension
        projected_features = {}
        for name, feature in features.items():
            if name in self.projections:
                # Project the feature
                projected = self.projections[name](feature)

                # Ensure the projected feature has the right shape
                if projected.dim() == 2 and projected.size(1) == self.fusion_dim:
                    projected_features[name] = projected

        # If no features were projected, return zeros
        if not projected_features:
            return torch.zeros(batch_size, self.fusion_dim, device=device)

        # Apply cross-modal attention for each pair of features
        enhanced_features = {name: feat.clone() for name, feat in projected_features.items()}
        feature_names = list(projected_features.keys())

        # Apply cross-attention between feature pairs
        try:
            for i, name1 in enumerate(feature_names):
                for name2 in feature_names:
                    if name1 != name2:
                        key = f"{name1}_to_{name2}"
                        if key in self.cross_attentions:
                            enhanced_features[name1] = self.cross_attentions[key](
                                enhanced_features[name1], projected_features[name2]
                            )
        except Exception as e:
            print(f"Error in cross-modal attention: {e}. Using original features.")
        enhanced_features = projected_features

        # Compute importance weights
        importance = F.softmax(self.feature_importance, dim=0)

        # Weighted sum of features
        fused_features = torch.zeros(batch_size, self.fusion_dim, device=device)
        for i, name in enumerate(enhanced_features.keys()):
            if i < len(importance):
                fused_features += importance[i] * enhanced_features[name]
            else:
                # Equal weight for any extra features
                fused_features += (1.0 / len(enhanced_features)) * enhanced_features[name]

        # Apply the fusion layer
        fused_features = self.fusion_layer(fused_features)

        return fused_features

    def get_feature_importance(self):
        """Get the importance weights for each feature type."""
        importance = F.softmax(self.feature_importance, dim=0)
        return {name: importance[i].item() for i, name in enumerate(self.feature_dims.keys())}

class TaxonomicEmbedder(nn.Module):
    """
    Enhanced hierarchical taxonomic embedder that processes multiple levels of taxonomy
    using attention mechanisms and learnable importance weights.
    """
    def __init__(self, level_vocab_sizes, embed_dim=32, use_attention=True, num_heads=4, dropout=0.1):
        super().__init__()
        self.embed_dim = embed_dim
        self.use_attention = use_attention
        self.taxonomy_levels = ["phylum", "class", "order", "family", "genus"]

        # Create embeddings for each taxonomy level
        self.embeddings = nn.ModuleDict()
        for level in self.taxonomy_levels:
            if level in level_vocab_sizes:
                vocab_size = level_vocab_sizes[level]
                self.embeddings[level] = nn.Embedding(vocab_size, embed_dim)

        # Enhanced attention mechanism
        if use_attention:
            if embed_dim % num_heads != 0:
                adjusted_dim = (embed_dim // num_heads) * num_heads
                print(f"Warning: embed_dim {embed_dim} not divisible by num_heads {num_heads}. "
                      f"Using adjusted dimension {adjusted_dim}.")
                embed_dim = adjusted_dim

            # 注意：移除 batch_first=True 以兼容较旧版本的 PyTorch
            self.multihead_attn = nn.MultiheadAttention(
                embed_dim=embed_dim,
                num_heads=num_heads,
                dropout=dropout
            )
            self.layer_norm = nn.LayerNorm(embed_dim)
            self.level_importance = nn.Parameter(torch.ones(len(self.taxonomy_levels)) / len(self.taxonomy_levels))

        self.combiner = nn.Linear(len(self.taxonomy_levels) * embed_dim, embed_dim)

    def forward(self, tax_data):
        all_embeddings = []
        device = next(iter(self.embeddings.values())).weight.device

        for level in self.taxonomy_levels:
            if level in tax_data and level in self.embeddings:
                level_ids = tax_data[level]
                level_embed = self.embeddings[level](level_ids)
                all_embeddings.append(level_embed)

        if not all_embeddings:
            batch_size = next(iter(tax_data.values())).size(0) if tax_data else 1
            return torch.zeros(batch_size, self.embed_dim, device=device)

        if self.use_attention and len(all_embeddings) > 1:
            stacked = torch.stack(all_embeddings, dim=1)
            # 转换为 MultiheadAttention 期望的格式 [seq_len, batch_size, embed_dim]
            stacked_transposed = stacked.transpose(0, 1)
            attn_output, attn_weights = self.multihead_attn(
                query=stacked_transposed,
                key=stacked_transposed,
                value=stacked_transposed
            )
            # 转换回 [batch_size, seq_len, embed_dim] 格式
            attn_output = attn_output.transpose(0, 1)
            normalized = self.layer_norm(attn_output + stacked)
            softmax_weights = F.softmax(self.level_importance, dim=0)
            weighted_sum = torch.einsum('blf,l->bf', normalized, softmax_weights)
            return weighted_sum
        elif len(all_embeddings) > 1:
            concat = torch.cat(all_embeddings, dim=1)
            return self.combiner(concat)
        else:
            return all_embeddings[0]

class HierarchicalGNN(nn.Module):
    """
    Hierarchical Graph Neural Network for integrating taxonomy and KO profile information.
    """
    def __init__(self, taxonomy_vocab_sizes, tax_embed_dim=32, hidden_dim=128, output_dim=128,
                 ko_weight=0.6, use_attention=True, dropout=0.3):
        super().__init__()
        self.ko_weight = ko_weight
        self.taxonomy_levels = ["phylum", "class", "order", "family", "genus"]

        # Taxonomy embedder
        self.tax_embedder = TaxonomicEmbedder(
            level_vocab_sizes=taxonomy_vocab_sizes,
            embed_dim=tax_embed_dim,
            use_attention=use_attention
        )

        # GNN layers
        self.gnn_layer1 = nn.Sequential(
            nn.Linear(tax_embed_dim, hidden_dim),
            nn.ReLU(),
            nn.LayerNorm(hidden_dim)
        )

        self.gnn_layer2 = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.LayerNorm(hidden_dim)
        )

        # Residual connection
        self.res_proj = nn.Linear(tax_embed_dim, hidden_dim)

        # Gating mechanism
        self.gate = nn.Sequential(
            nn.Linear(tax_embed_dim + hidden_dim, hidden_dim),
            nn.Sigmoid()
        )

        # Output layer
        self.output_layer = nn.Linear(hidden_dim, output_dim)
        self.dropout = nn.Dropout(dropout)

    def _build_similarity_graph(self, tax_embeddings, ko_sim_matrix=None, k=5):
        batch_size = tax_embeddings.size(0)
        device = tax_embeddings.device

        # Adjust k for small batch sizes
        adjusted_k = min(k, batch_size - 1)

        # Calculate taxonomy similarity
        tax_sim = F.cosine_similarity(
            tax_embeddings.unsqueeze(1),
            tax_embeddings.unsqueeze(0),
            dim=2
        )

        # Combine similarities
        if ko_sim_matrix is None or ko_sim_matrix.numel() == 0 or ko_sim_matrix.size(0) != batch_size:
            combined_sim = tax_sim
        else:
            ko_mean = ko_sim_matrix.mean().item()
            adaptive_weight = self.ko_weight * min(1.0, max(0.1, ko_mean * 2))
            combined_sim = adaptive_weight * ko_sim_matrix + (1 - adaptive_weight) * tax_sim

        # Ensure self-similarity is maximum
        combined_sim.fill_diagonal_(1.0)

        # Build k-NN graph
        edge_index = []
        edge_attr = []

        if batch_size == 1:
            edge_index.append([0, 0])
            edge_attr.append([1.0])
            return torch.tensor(edge_index, device=device).t(), torch.tensor(edge_attr, device=device)

        for i in range(batch_size):
            sims = combined_sim[i]
            mask = torch.ones(batch_size, dtype=torch.bool, device=device)
            mask[i] = False

            if adjusted_k > 0:
                topk_values, topk_indices = torch.topk(
                    sims[mask],
                    k=min(adjusted_k, mask.sum().item())
                )
                masked_indices = torch.nonzero(mask).squeeze(-1)
                neighbor_indices = masked_indices[topk_indices]

                for j, sim in zip(neighbor_indices, topk_values):
                    edge_index.append([i, j.item()])
                    edge_attr.append([sim.item()])

            edge_index.append([i, i])
            edge_attr.append([1.0])

        return torch.tensor(edge_index, device=device).t(), torch.tensor(edge_attr, device=device)

    def _message_passing(self, x, edge_index, edge_attr):
        sources, targets = edge_index
        messages = []

        for i in range(x.size(0)):
            mask = targets == i
            if not mask.any():
                continue

            source_indices = sources[mask]
            weights = edge_attr[mask].unsqueeze(1)
            source_features = x[source_indices]
            weighted_features = source_features * weights
            message = weighted_features.sum(dim=0)
            messages.append((i, message))

        x_out = x.clone()
        for idx, message in messages:
            x_out[idx] += message

        return x_out

    def forward(self, tax_data, ko_data=None):
        # Get taxonomy embeddings
        tax_embeddings = self.tax_embedder(tax_data)

        # Get KO similarity matrix if provided
        ko_sim_matrix = None
        if ko_data is not None and 'sim_matrix' in ko_data:
            ko_sim_matrix = ko_data['sim_matrix']

        # Build similarity graph
        edge_index, edge_attr = self._build_similarity_graph(
            tax_embeddings, ko_sim_matrix, k=5
        )

        # Initial node features
        h0 = tax_embeddings

        # First GNN layer
        h1 = self.gnn_layer1(h0)
        h1 = F.relu(h1)
        h1 = self.dropout(h1)
        h1 = self._message_passing(h1, edge_index, edge_attr)

        # Second GNN layer
        h2 = self.gnn_layer2(h1)
        h2 = F.relu(h2)
        h2 = self.dropout(h2)
        h2 = h2 + self.res_proj(h0)
        h2 = self._message_passing(h2, edge_index, edge_attr)

        # Adaptive gating
        gate = self.gate(torch.cat([h0, h2], dim=1))
        gated = gate * h2 + (1 - gate) * h1

        # Output projection
        output = self.output_layer(gated)

        return output

class FeatureImportanceAttention(nn.Module):
    """
    Advanced attention-based feature importance module.

    This module applies attention mechanisms to weight different feature types
    (taxonomy, KO, pathway) based on their importance for the prediction task.
    It also includes gating mechanisms to control information flow from each
    feature type.
    """

    def __init__(self, feature_dims, output_dim, dropout=0.1):
        """
        Initialize the feature importance attention module.

        Args:
            feature_dims: Dictionary mapping feature names to their dimensions
            output_dim: Dimension of the output features
            dropout: Dropout rate for regularization
        """
        super().__init__()
        self.feature_dims = feature_dims
        self.output_dim = output_dim

        # Create projection layers for each feature type
        self.projections = nn.ModuleDict({
            name: nn.Linear(dim, output_dim)
            for name, dim in feature_dims.items()
        })

        # Attention scoring mechanism for each feature type
        self.attention = nn.ModuleDict({
            name: nn.Sequential(
                nn.Linear(output_dim, output_dim // 2),
                nn.ReLU(),
                nn.Linear(output_dim // 2, 1)
            ) for name in feature_dims.keys()
        })

        # Feature-specific gating to control information flow
        self.gates = nn.ModuleDict({
            name: nn.Sequential(
                nn.Linear(dim, 1),
                nn.Sigmoid()
            ) for name, dim in feature_dims.items()
        })

        # Layer normalization for stability
        self.layer_norm = nn.LayerNorm(output_dim)

        # Dropout for regularization
        self.dropout = nn.Dropout(dropout)

        # Feature importance tracking
        self.register_buffer('feature_importance', torch.ones(len(feature_dims)))

    def forward(self, features):
        """
        Forward pass through the attention module.

        Args:
            features: Dictionary of feature tensors {name: tensor}

        Returns:
            Tuple of:
            - Combined feature tensor
            - Dictionary of attention scores
            - Dictionary of gate values
        """
        # Get device from first tensor
        device = next(iter(features.values())).device
        batch_size = next(iter(features.values())).size(0)

        # Project each feature type to common dimension
        projected = {}
        for name, feature in features.items():
            if name in self.projections:
                projected[name] = self.projections[name](feature)

        # If no valid features, return zeros
        if not projected:
            return (
                torch.zeros(batch_size, self.output_dim, device=device),
                {},
                {}
            )

        # Calculate attention scores for each feature type
        attention_scores = {}
        for name, proj in projected.items():
            # Raw attention score
            attention_scores[name] = self.attention[name](proj)

        # Normalize attention scores across feature types
        if len(attention_scores) > 1:
            # Concatenate scores and apply softmax across feature types
            score_names = list(attention_scores.keys())
            combined_scores = torch.cat([attention_scores[name] for name in score_names], dim=1)
            normalized_scores = F.softmax(combined_scores, dim=1)

            # Split back to individual feature types
            for i, name in enumerate(score_names):
                attention_scores[name] = normalized_scores[:, i:i+1]
        else:
            # Only one feature type, set attention to 1.0
            for name in attention_scores:
                attention_scores[name] = torch.ones_like(attention_scores[name])

        # Apply gates to control information flow
        gates = {}
        for name, feature in features.items():
            if name in self.gates:
                gates[name] = self.gates[name](feature)

        # Apply attention and gates to features
        weighted_features = []
        importance_values = {}

        for name, proj in projected.items():
            # Apply attention and gate
            if name in attention_scores and name in gates:
                # Weight by both attention and gate
                weight_factor = attention_scores[name] * gates[name]
                weighted = proj * weight_factor

                # Track feature importance
                importance = torch.mean(weight_factor).item()
                importance_values[name] = importance

                weighted_features.append(weighted)

        # Update feature importance buffer with exponential moving average
        if importance_values and len(importance_values) == len(self.feature_dims):
            new_importance = torch.tensor(importance_values, device=device)
            alpha = 0.9  # Smoothing factor
            self.feature_importance = alpha * self.feature_importance + (1 - alpha) * new_importance

        # Combine weighted features
        if weighted_features:
            combined = sum(weighted_features)

            # Apply layer normalization and dropout
            combined = self.layer_norm(combined)
            combined = self.dropout(combined)
        else:
            combined = torch.zeros(batch_size, self.output_dim, device=device)

        return combined, attention_scores, gates

    def get_feature_importance(self):
        """Get the current feature importance values."""
        feature_names = list(self.feature_dims.keys())
        return {name: float(self.feature_importance[i].item())
                for i, name in enumerate(feature_names)}

class SimpleFeatureFusion(nn.Module):
    """
    Simple feature fusion module that concatenates and processes features.

    This is a simpler alternative to the HierarchicalFeatureFusion module,
    without the complex cross-attention mechanisms.
    """

    def __init__(self, feature_dims, hidden_dim, output_dim, dropout=0.2):
        """
        Initialize the simple feature fusion module.

        Args:
            feature_dims: Dictionary mapping feature names to their dimensions
            hidden_dim: Dimension of the hidden representations
            output_dim: Dimension of the output features
            dropout: Dropout rate for regularization
        """
        super().__init__()
        self.feature_dims = feature_dims
        self.hidden_dim = hidden_dim
        self.output_dim = output_dim

        # Initial projection layers for each feature type
        self.projections = nn.ModuleDict({
            name: nn.Linear(dim, hidden_dim)
            for name, dim in feature_dims.items()
        })

        # Combined dimension after concatenation
        combined_dim = hidden_dim * len(feature_dims)

        # Integration layers
        self.integration = nn.Sequential(
            nn.Linear(combined_dim, hidden_dim * 2),
            nn.ReLU(),
            nn.LayerNorm(hidden_dim * 2),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 2, output_dim),
            nn.LayerNorm(output_dim)
        )

        # Feature importance tracking
        self.register_buffer('feature_importance',
                             torch.ones(len(feature_dims)) / len(feature_dims))
        self.feature_names = list(feature_dims.keys())

    def forward(self, features):
        """
        Forward pass through the fusion module.

        Args:
            features: Dictionary mapping feature names to feature tensors

        Returns:
            Fused feature tensor and importance values
        """
        # Get device from first feature
        device = next(iter(features.values())).device
        batch_size = next(iter(features.values())).size(0)

        # Project each feature type
        projected = {}
        for name, feature in features.items():
            if name in self.projections:
                projected[name] = self.projections[name](feature)

        # If no valid features, return zeros
        if not projected:
            return (
                torch.zeros(batch_size, self.output_dim, device=device),
                {}
            )

        # Concatenate all projected features
        feature_list = [projected[name] for name in self.feature_names if name in projected]
        if not feature_list:
            return (
                torch.zeros(batch_size, self.output_dim, device=device),
                {}
            )

        combined = torch.cat(feature_list, dim=1)

        # Apply integration layers
        output = self.integration(combined)

        # Simple importance values - equal weighting
        importance_values = {
            name: 1.0 / len(projected) for name in projected
        }

        return output, importance_values

    def get_feature_importance(self):
        """Get the current feature importance values."""
        return {name: 1.0 / len(self.feature_dims)
                for name in self.feature_names}


class HierarchicalFeatureFusion(nn.Module):
    """
    Hierarchical feature fusion with progressive integration of features.

    This approach uses a cascading fusion strategy where features are
    integrated at multiple levels of abstraction:
    1. Stage 1: Pairwise cross-attention between feature types
    2. Stage 2: Mid-level fusion with feature importance attention
    3. Stage 3: Final integration with residual blocks

    This allows the model to capture both local interactions between feature pairs
    and global interactions across all features.
    """

    def __init__(self, feature_dims, hidden_dim, output_dim, num_fusion_layers=3, dropout=0.2):
        """
        Initialize the hierarchical feature fusion module.

        Args:
            feature_dims: Dictionary mapping feature names to their dimensions
            hidden_dim: Dimension of the hidden representations
            output_dim: Dimension of the output features
            num_fusion_layers: Number of fusion layers/stages
            dropout: Dropout rate for regularization
        """
        super().__init__()
        self.feature_dims = feature_dims
        self.hidden_dim = hidden_dim
        self.output_dim = output_dim

        # Initial projection layers for each feature type
        self.projections = nn.ModuleDict({
            name: nn.Linear(dim, hidden_dim)
            for name, dim in feature_dims.items()
        })

        # Fusion stages - progressively combining features
        self.fusion_stages = nn.ModuleList()

        # Stage 1: Pairwise cross-attention (if we have at least 2 features)
        if len(feature_dims) >= 2:
            # Create feature pair attention modules
            feature_names = list(feature_dims.keys())
            self.pair_attention = nn.ModuleDict()

            # For each feature pair, create cross-attention
            for idx1, name1 in enumerate(feature_names):
                for idx2, name2 in enumerate(feature_names[idx1+1:], idx1+1):
                    pair_name = f"{name1}_{name2}"
                    self.pair_attention[pair_name] = CrossAttention(
                        query_dim=hidden_dim,
                        key_dim=hidden_dim,
                        value_dim=hidden_dim,
                        heads=4,
                        dropout=dropout
                    )

            self.fusion_stages.append(self.pair_attention)

        # Stage 2: Mid-level fusion with feature importance attention (if we have at least 3 features)
        if len(feature_dims) >= 3:
            self.mid_fusion = FeatureImportanceAttention(
                {name: hidden_dim for name in feature_dims},
                hidden_dim,
                dropout
            )
            self.fusion_stages.append(self.mid_fusion)

        # Stage 3+: Final integration residual blocks
        num_residual_blocks = max(1, num_fusion_layers - 2)
        for _ in range(num_residual_blocks):
            self.fusion_stages.append(ResidualBlock(
                hidden_dim,
                hidden_dim * 2,
                dropout
            ))

        # Final output layer
        self.output_layer = nn.Sequential(
            nn.Linear(hidden_dim, output_dim),
            nn.LayerNorm(output_dim)
        )

        # Feature importance tracking
        self.register_buffer('feature_importance', torch.ones(len(feature_dims)))
        self.feature_names = list(feature_dims.keys())

    def forward(self, features):
        """
        Forward pass through the hierarchical fusion module.

        Args:
            features: Dictionary of feature tensors {name: tensor}

        Returns:
            Fused output tensor and feature importance dictionary
        """
        # Get device and batch size from first tensor
        device = next(iter(features.values())).device
        batch_size = next(iter(features.values())).size(0)

        # Project all features to common dimension
        projected = {}
        for name, feature in features.items():
            if name in self.projections:
                projected[name] = self.projections[name](feature)

        # If no valid features, return zeros
        if not projected:
            return torch.zeros(batch_size, self.output_dim, device=device), {}

        # Apply fusion stages
        current_features = projected.copy()
        feature_names = list(projected.keys())
        importance_values = {}

        # Stage 1: Pairwise fusion with cross-attention (if available)
        stage_idx = 0
        if len(self.fusion_stages) > stage_idx and hasattr(self, 'pair_attention'):
            enriched_features = {}

            for idx1, name1 in enumerate(feature_names):
                # Collect all cross-attended versions of this feature
                attended_versions = [current_features[name1]]

                for idx2, name2 in enumerate(feature_names):
                    if idx1 != idx2:
                        pair_name = f"{name1}_{name2}"
                        rev_pair_name = f"{name2}_{name1}"

                        if pair_name in self.pair_attention:
                            attended = self.pair_attention[pair_name](
                                current_features[name1],
                                current_features[name2],
                                current_features[name2]
                            )
                            attended_versions.append(attended)
                        elif rev_pair_name in self.pair_attention:
                            attended = self.pair_attention[rev_pair_name](
                                current_features[name1],
                                current_features[name2],
                                current_features[name2]
                            )
                            attended_versions.append(attended)

                # Average all attended versions
                if len(attended_versions) > 1:
                    enriched_features[name1] = torch.stack(attended_versions).mean(dim=0)
                else:
                    enriched_features[name1] = attended_versions[0]

            current_features = enriched_features
            stage_idx += 1

        # Stage 2: Mid-level fusion with feature importance attention (if available)
        if len(self.fusion_stages) > stage_idx and hasattr(self, 'mid_fusion'):
            mid_fused, attention_scores, _ = self.mid_fusion(current_features)

            # Track feature importance
            for i, name in enumerate(self.feature_names):
                if name in attention_scores:
                    importance_values[name] = attention_scores[name].mean().item()

            # Enrich features with mid-level fusion (residual connection)
            for name in current_features:
                current_features[name] = current_features[name] + mid_fused

            stage_idx += 1

        # Combine all features for final processing
        combined = sum(current_features.values()) / len(current_features)

        # Apply remaining fusion stages (residual blocks)
        for i in range(stage_idx, len(self.fusion_stages)):
            combined = self.fusion_stages[i](combined)

        # Final output
        output = self.output_layer(combined)

        # Update feature importance with new values
        if importance_values:
            for i, name in enumerate(self.feature_names):
                if name in importance_values:
                    # Exponential moving average for stability
                    alpha = 0.9  # Smoothing factor
                    old_value = self.feature_importance[i].item()
                    new_value = alpha * old_value + (1 - alpha) * importance_values[name]
                    self.feature_importance[i] = torch.tensor(new_value, device=device)

        return output, importance_values

    def get_feature_importance(self):
        """Get the current feature importance values."""
        return {name: float(self.feature_importance[i].item())
                for i, name in enumerate(self.feature_names)}

class EnhancedPathwayModule(nn.Module):
    """
    Enhanced pathway module that treats pathway groups as independent features.

    This module:
    1. Groups pathways by biological function (central metabolism, energy, etc.)
    2. Processes each pathway group separately
    3. Applies attention between pathway groups
    4. Tracks importance of each pathway group

    This approach provides more biological interpretability and enables
    cross-attention between specific pathway groups and other features.
    """

    def __init__(self, input_dim, hidden_dim, output_dim, dropout=0.2):
        """
        Initialize the enhanced pathway module.

        Args:
            input_dim: Number of input pathways
            hidden_dim: Hidden dimension for pathway representations
            output_dim: Output dimension for the final pathway representation
            dropout: Dropout rate for regularization
        """
        super().__init__()
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.output_dim = output_dim

        # Define important pathway groups with their KEGG IDs
        self.pathway_groups = {
            'central_metabolism': ['ko00010', 'ko00020', 'ko00030', 'ko00710'],  # Glycolysis, TCA, PPP, Carbon fixation
            'energy_production': ['ko00190', 'ko00195', 'ko00196', 'ko00680', 'ko00910'],  # Oxidative phosphorylation, Methane metabolism
            'amino_acid': ['ko00260', 'ko00270', 'ko00280', 'ko00290', 'ko00300', 'ko00310', 'ko00220'],  # Amino acid metabolism
            'nucleotide': ['ko00230', 'ko00240', 'ko00250'],  # Nucleotide metabolism
            'lipid': ['ko00061', 'ko00071', 'ko00072', 'ko00073', 'ko00561', 'ko00564', 'ko00565', 'ko00600'],  # Lipid metabolism
            'carbohydrate': ['ko00500', 'ko00520', 'ko00051', 'ko00052', 'ko00053', 'ko00040', 'ko00562', 'ko00620'],  # Carbohydrate metabolism
            'cofactor': ['ko00730', 'ko00740', 'ko00750', 'ko00760', 'ko00770', 'ko00780', 'ko00790', 'ko00860']  # Vitamin/cofactor
        }

        # Create pathway group mask matrices for extracting group features
        # In the forward pass, we'll use these to extract pathways belonging to each group
        self.register_buffer('group_masks', torch.zeros(len(self.pathway_groups), input_dim))

        # Create pathway group encoders (shared architecture, separate parameters)
        self.group_encoders = nn.ModuleDict()
        for i, group_name in enumerate(self.pathway_groups.keys()):
            self.group_encoders[group_name] = nn.Sequential(
                nn.Linear(input_dim, hidden_dim//2),  # We'll use masks to focus on relevant pathways
            nn.ReLU(),
                nn.LayerNorm(hidden_dim//2),
            nn.Dropout(dropout)
        )

        # Pathway group attention for interactions between groups
        self.group_attention = nn.MultiheadAttention(
            embed_dim=hidden_dim//2,
            num_heads=2,
            dropout=dropout,
            batch_first=True
        )

        # Process pathways not in specific groups
        self.other_pathways = nn.Sequential(
            nn.Linear(input_dim, hidden_dim//2),  # We'll use masks for this too
            nn.ReLU(),
            nn.LayerNorm(hidden_dim//2),
            nn.Dropout(dropout)
        )

        # Final integration of all pathway groups
        self.integration = nn.Sequential(
            nn.Linear(hidden_dim//2 * (len(self.pathway_groups) + 1), hidden_dim),
                nn.ReLU(),
            nn.LayerNorm(hidden_dim),
                nn.Dropout(dropout),
            nn.Linear(hidden_dim, output_dim)
        )

        # Pathway importance tracking
        self.register_buffer('pathway_importance',
                             torch.ones(len(self.pathway_groups) + 1))

        # Store group names for importance tracking
        self.group_names = list(self.pathway_groups.keys()) + ['other_pathways']

    def set_pathway_mapping(self, pathway_ids):
        """
        Set the mapping between pathway indices and groups.

        Args:
            pathway_ids: List of KEGG pathway IDs corresponding to input features
        """
        # First reset all masks
        self.group_masks.zero_()

        # Create a mask for "other pathways" (all 1s initially)
        other_mask = torch.ones(1, self.input_dim, device=self.group_masks.device)

        # For each pathway group, create a mask where 1 indicates the pathway belongs to the group
        for i, (group_name, group_pathways) in enumerate(self.pathway_groups.items()):
            for j, pathway_id in enumerate(pathway_ids):
                if pathway_id in group_pathways and j < self.input_dim:
                    self.group_masks[i, j] = 1.0
                    # Remove from "other" category
                    if j < other_mask.size(1):
                        other_mask[0, j] = 0.0

        # Store the "other pathways" mask
        self.register_buffer('other_mask', other_mask)
        print(f"Pathway mapping set with {pathway_ids[:5]}... ({len(pathway_ids)} total)")

    def forward(self, pathway_features):
        """
        Process pathway features with group-specific attention.

        Args:
            pathway_features: Tensor of pathway features [batch_size, num_pathways]

        Returns:
            Integrated pathway representation
        """
        batch_size = pathway_features.size(0)
        device = pathway_features.device

        # Ensure input dimension matches expected
        if pathway_features.size(1) != self.input_dim:
            # Handle dimension mismatch by padding or truncating
            if pathway_features.size(1) < self.input_dim:
                # Pad with zeros
                padding = torch.zeros(batch_size, self.input_dim - pathway_features.size(1), device=device)
                pathway_features = torch.cat([pathway_features, padding], dim=1)
            else:
                # Truncate
                pathway_features = pathway_features[:, :self.input_dim]

        # Extract and encode each pathway group using masks
        group_features = {}
        for i, group_name in enumerate(self.pathway_groups.keys()):
            # Apply mask to focus on pathways in this group
            # The mask is multiplied element-wise with the input
            # Non-relevant pathways are zeroed out but still processed
            group_mask = self.group_masks[i].unsqueeze(0).expand(batch_size, -1)
            masked_input = pathway_features * group_mask

            # Encode this pathway group
            group_features[group_name] = self.group_encoders[group_name](masked_input)

        # Process pathways not in specific groups
        if hasattr(self, 'other_mask'):
            other_mask = self.other_mask.expand(batch_size, -1)
            masked_input = pathway_features * other_mask
            other_features = self.other_pathways(masked_input)
        else:
            # If no mapping has been set, process all pathways as "other"
            other_features = self.other_pathways(pathway_features)

        # Apply attention between pathway groups
        if len(group_features) > 1:
            # Stack features for attention [batch_size, num_groups, hidden_dim//2]
            group_names = list(group_features.keys())
            stacked_features = torch.stack([group_features[name] for name in group_names], dim=1)

            # Apply attention
            attended_features, attn_weights = self.group_attention(
                stacked_features, stacked_features, stacked_features
            )

            # Update group features with attention
            for i, group_name in enumerate(group_names):
                group_features[group_name] = attended_features[:, i]

            # Track pathway group importance from attention weights
            if attn_weights is not None:
                importance = attn_weights.mean(dim=(0, 1))
                for i, group_name in enumerate(group_names):
                    idx = self.group_names.index(group_name)
                    # Exponential moving average for stability
                    self.pathway_importance[idx] = 0.9 * self.pathway_importance[idx] + 0.1 * importance[i]

        # Concatenate all pathway representations
        all_features = list(group_features.values()) + [other_features]
        combined = torch.cat(all_features, dim=1)

        # Final integration
        output = self.integration(combined)

        return output

    def get_pathway_importance(self):
        """Get importance scores for pathway groups."""
        return {name: float(self.pathway_importance[i].item())
                for i, name in enumerate(self.group_names)}

class ImprovedPhyloGrowthModel(EnhancedPhyloGrowthModel):
    """
    Enhanced model for microbial growth rate prediction with improved architecture.

    This model extends the EnhancedPhyloGrowthModel with:
    1. Increased model capacity
    2. Hierarchical feature fusion with cross-attention
    3. Enhanced pathway-specific processing
    4. Residual connections
    5. Dynamic feature importance tracking
    6. Uncertainty estimation
    """
    def __init__(self,
                 codon_input_dim,
                 pathway_input_dim=0,
                 heg_input_dim=0,
                 seq_input_dim=0,
                 taxonomy_vocab_sizes=None,
                 hidden_dim=256,
                 output_dim=2,
                 dropout=0.2,
                 use_pathway=False,
                 use_heg=False,
                 use_hierarchical_phylo=True,
                 use_seq=False,
                 phylo_k=10,
                 attention_heads=4,
                 use_residual=True,
                 num_fusion_layers=3,
                 pathway_ids=None,
                 uncertainty_calibration=True,
                 enable_hierarchical_fusion=True,
                 enable_enhanced_pathways=True):
        # Call parent constructor
        super().__init__(
            codon_input_dim=codon_input_dim,
            pathway_input_dim=pathway_input_dim,
            heg_input_dim=heg_input_dim,
            seq_input_dim=seq_input_dim,
            taxonomy_vocab_sizes=taxonomy_vocab_sizes,
            hidden_dim=hidden_dim,
            output_dim=output_dim,
            dropout=dropout,
            use_pathway=use_pathway,
            use_heg=use_heg,
            use_hierarchical_phylo=use_hierarchical_phylo,
            use_seq=use_seq,
            phylo_k=phylo_k
        )

        # Set attributes for feature usage
        self.use_codon = codon_input_dim > 0
        self.use_pathway = use_pathway and pathway_input_dim > 0
        self.use_heg = use_heg and heg_input_dim > 0
        self.use_seq = use_seq and seq_input_dim > 0
        self.hidden_dim = hidden_dim
        self.output_dim = output_dim

        # Replace standard pathway branch with enhanced pathway module if needed
        if use_pathway and pathway_input_dim > 0 and enable_enhanced_pathways:
            self.pathway_branch = EnhancedPathwayModule(
                input_dim=pathway_input_dim,
                hidden_dim=hidden_dim * 2,  # Larger internal representation
                output_dim=hidden_dim,
                    dropout=dropout
                )

            # Set pathway mapping if IDs are provided
            if pathway_ids is not None:
                self.pathway_branch.set_pathway_mapping(pathway_ids)

        # Define feature dimensions for the hierarchical fusion
        feature_dims = {}
        if self.use_codon:
            feature_dims['codon'] = hidden_dim
        if self.use_pathway:
            feature_dims['pathway'] = hidden_dim
        if self.use_heg:
            feature_dims['heg'] = hidden_dim
        if self.use_seq:
            feature_dims['seq'] = hidden_dim
        if use_hierarchical_phylo:
            feature_dims['phylo'] = hidden_dim

        # Replace the feature fusion module with our hierarchical fusion if enabled
        if enable_hierarchical_fusion:
            self.feature_fusion = HierarchicalFeatureFusion(
                feature_dims=feature_dims,
                hidden_dim=hidden_dim,
                output_dim=hidden_dim,
                num_fusion_layers=num_fusion_layers,
                dropout=dropout
            )
        else:
            # Use a simpler fusion module if hierarchical fusion is disabled
            self.feature_fusion = SimpleFeatureFusion(
                feature_dims=feature_dims,
                hidden_dim=hidden_dim,
                output_dim=hidden_dim,
                dropout=dropout
            )

        # Add additional residual blocks if specified
        if use_residual:
            self.residual_blocks = nn.ModuleList([
                ResidualBlock(hidden_dim, hidden_dim * 2, dropout)
                for _ in range(2)  # 2 extra residual blocks
            ])
        else:
            self.residual_blocks = None

        # Create a custom layer that preserves batch dimension
        class BatchPreservingLayer(nn.Module):
            def __init__(self, input_dim, hidden_dim, output_dim, dropout):
                super().__init__()
                # Store dimensions for reference
                self.input_dim = input_dim
                self.hidden_dim = hidden_dim
                self.output_dim = output_dim

                # Create layers with explicit batch dimension preservation
                self.fc1 = nn.Linear(input_dim, hidden_dim * 2)
                self.dropout1 = nn.Dropout(dropout)
                self.fc2 = nn.Linear(hidden_dim * 2, hidden_dim)
                self.dropout2 = nn.Dropout(dropout)
                self.fc3 = nn.Linear(hidden_dim, output_dim)

            def forward(self, x):
                # Store original batch size
                batch_size = x.size(0)

                # Check input dimensions
                if x.dim() == 1:
                    # Input is [features], add batch dimension
                    x = x.unsqueeze(0)
                elif x.dim() > 2:
                    # Input has extra dimensions, flatten to [batch_size, features]
                    x = x.view(batch_size, -1)

                # Ensure input has correct feature dimension
                if x.size(1) != self.input_dim:
                    # Adjust feature dimension size
                    if x.size(1) < self.input_dim:
                        # Pad features if needed
                        padding = torch.zeros(x.size(0), self.input_dim - x.size(1),
                                             device=x.device, dtype=x.dtype)
                        x = torch.cat([x, padding], dim=1)
                    else:
                        # Truncate if too many features
                        x = x[:, :self.input_dim]

                # Apply layers with shape checking after each step
                x = self.fc1(x)
                if x.size(0) != batch_size:  # Ensure batch dimension is preserved
                    x = x.view(batch_size, -1)
                x = F.relu(x)
                x = self.dropout1(x)

                x = self.fc2(x)
                if x.size(0) != batch_size:  # Ensure batch dimension is preserved
                    x = x.view(batch_size, -1)
                x = F.relu(x)
                x = self.dropout2(x)

                x = self.fc3(x)

                # Final shape check and correction
                if self.output_dim == 1:
                    # If output is a single value, ensure shape is [batch_size]
                    if x.dim() > 1 and x.size(1) == 1:
                        x = x.squeeze(1)
                    # If output lost batch dimension, restore it
                    if x.dim() == 0:
                        x = x.unsqueeze(0).expand(batch_size)
                else:
                    # For multi-dimensional output
                    if x.dim() == 1:
                        # If output is [output_dim], reshape to [batch_size, output_dim]
                        if x.size(0) == self.output_dim:
                            x = x.unsqueeze(0).expand(batch_size, -1)
                        else:
                            # If dimensions are completely wrong, reshape
                            x = x.view(batch_size, -1)
                            if x.size(1) != self.output_dim:
                                # Adjust to correct output size
                                if x.size(1) < self.output_dim:
                                    padding = torch.zeros(batch_size, self.output_dim - x.size(1),
                                                         device=x.device, dtype=x.dtype)
                                    x = torch.cat([x, padding], dim=1)
                                else:
                                    x = x[:, :self.output_dim]
                    elif x.dim() == 0:
                        # If output is a scalar, expand to proper dimensions
                        x = x.unsqueeze(0).unsqueeze(0).expand(batch_size, self.output_dim)
                    elif x.size(0) != batch_size or x.size(1) != self.output_dim:
                        # Reshape to proper dimensions
                        x = x.view(batch_size, self.output_dim)

                return x

        # Use the custom layer for final prediction
        self.final_prediction = BatchPreservingLayer(hidden_dim, hidden_dim, output_dim, dropout)

        # Track feature importance and attention scores
        self.feature_importance = {}

        # Uncertainty calibration (optional)
        self.uncertainty_calibration = uncertainty_calibration
        if uncertainty_calibration:
            # Calibration parameters for uncertainty estimates (learned during training)
            self.register_buffer('uncertainty_scale', torch.ones(output_dim))
            self.register_buffer('uncertainty_bias', torch.zeros(output_dim))

    def set_pathway_ids(self, pathway_ids):
        """Set the pathway IDs for the enhanced pathway module."""
        if hasattr(self, 'pathway_branch') and isinstance(self.pathway_branch, EnhancedPathwayModule):
            self.pathway_branch.set_pathway_mapping(pathway_ids)

    def forward(self, x):
        """Forward pass with hierarchical feature fusion and importance tracking."""
        # Process features as in parent class
        features = {}

        # Get batch size for proper dimension handling
        batch_size = None
        # Determine batch size from available tensors
        for key in ['codon_metrics', 'pathway', 'heg', 'seq']:
            if key in x and hasattr(x[key], 'size') and len(x[key].size()) > 0:
                batch_size = x[key].size(0)
                break

        if batch_size is None:
            # Fallback to 1 if no batch dimension could be found
            batch_size = 1
            print(f"WARNING: Could not determine batch size from inputs, using default value 1")

        # Process codon features if available
        if self.use_codon and 'codon_metrics' in x:
            features['codon'] = self.codon_branch(x['codon_metrics'])

        # Process pathway features if available
        if self.use_pathway and 'pathway' in x:
            features['pathway'] = self.pathway_branch(x['pathway'])

        # Process HEG features if available
        if self.use_heg and 'heg' in x:
            features['heg'] = self.heg_branch(x['heg'])

        # Process sequence features if available
        if self.use_seq and 'seq' in x:
            features['seq'] = self.seq_branch(x['seq'])

        # Process phylogenetic features
        if self.use_hierarchical_phylo:
            if 'taxonomy' in x and 'ko' in x:
                features['phylo'] = self.phylo_branch(x['taxonomy'], x['ko'])
            else:
                # Generate a zero tensor if taxonomy and KO data are missing
                device = next(self.parameters()).device
                if batch_size > 0:
                    features['phylo'] = torch.zeros(batch_size, self.hidden_dim, device=device)
        else:
                    # Fallback if batch_size is still unknown
                    features['phylo'] = torch.zeros(1, self.hidden_dim, device=device)

        # Ensure all feature tensors have correct batch dimension
        for key in features:
            if features[key].size(0) != batch_size:
                if features[key].dim() == 1:  # Handle scalar or single-sample tensors
                    features[key] = features[key].unsqueeze(0).expand(batch_size, -1)
                elif features[key].size(0) == 1 and batch_size > 1:  # Expand single sample to batch
                    features[key] = features[key].expand(batch_size, -1)

        # Use the hierarchical feature fusion
        fused_features, importance_values = self.feature_fusion(features)

        # Update feature importance tracker
        self.feature_importance.update(importance_values)

        # Apply additional residual blocks if specified
        if self.residual_blocks is not None:
            for block in self.residual_blocks:
                fused_features = block(fused_features)

        # Ensure fused_features maintains batch dimension
        if fused_features.size(0) != batch_size:
            if fused_features.dim() == 1:
                fused_features = fused_features.unsqueeze(0).expand(batch_size, -1)
            elif fused_features.size(0) == 1 and batch_size > 1:
                fused_features = fused_features.expand(batch_size, -1)

        # Make predictions
        predictions = self.final_prediction(fused_features)

        # Ensure predictions have proper batch dimension
        if predictions.dim() == 1 and self.output_dim > 1:
            # If predictions collapsed to [output_dim], reshape to [batch_size, output_dim]
            predictions = predictions.unsqueeze(0).expand(batch_size, -1)
        elif predictions.dim() == 0:
            # If predictions collapsed to a scalar, expand to proper dimensions
            if self.output_dim == 1:
                predictions = predictions.unsqueeze(0).expand(batch_size)
            else:
                predictions = predictions.unsqueeze(0).unsqueeze(0).expand(batch_size, self.output_dim)

        # Format output based on output dimension
        if self.output_dim == 1:
            # Single task: just growth rate
            # Ensure we have a batch dimension by using view operations
            growth_pred = predictions.view(batch_size)

            return {
                'growth_rate': growth_pred
            }
        else:
            # Multi-task: growth rate and temperature
            # Extract individual predictions with proper batch dimension
            if predictions.dim() == 1:
                # Handle case where predictions is [output_dim] but should be [batch_size, output_dim]
                if predictions.size(0) == batch_size:
                    # Split single prediction into two outputs (less ideal but functional fallback)
                    growth_pred = predictions
                    temp_pred = torch.zeros_like(predictions)
                else:
                    # If dimension is completely wrong, reshape
                    predictions = predictions.view(batch_size, -1)
                    growth_pred = predictions[:, 0]
                    temp_pred = predictions[:, 1] if predictions.size(1) > 1 else torch.zeros_like(growth_pred)
            else:
                # Normal case: predictions is [batch_size, 2]
                growth_pred = predictions[:, 0]
                temp_pred = predictions[:, 1]

            return {
                'growth_rate': growth_pred,
                'optimal_temperature': temp_pred
            }

    def get_feature_importance(self):
        """Get feature importance from the fusion module."""
        importance_dict = {}

        # Get general feature importance
        if hasattr(self.feature_fusion, 'get_feature_importance'):
            importance_dict.update(self.feature_fusion.get_feature_importance())
        else:
            importance_dict.update(self.feature_importance)

        # Get pathway-specific importance if available
        if hasattr(self, 'pathway_branch') and hasattr(self.pathway_branch, 'get_pathway_importance'):
            pathway_importance = self.pathway_branch.get_pathway_importance()
            # Add prefix to avoid key collisions
            importance_dict.update({
                f"pathway_{k}": v for k, v in pathway_importance.items()
            })

        return importance_dict

    def predict_with_uncertainty(self, x, n_samples=30):
        """
        Generate predictions with uncertainty estimates using Monte Carlo Dropout.

        Args:
            x: Input features
            n_samples: Number of stochastic forward passes for uncertainty estimation

        Returns:
            Dictionary with mean predictions and uncertainty estimates (standard deviations)
        """
        # Switch to train mode to enable dropout during inference
        self.train()

        # Determine batch size for shape checking
        batch_size = None
        if isinstance(x, dict):
            for key in ['codon_metrics', 'pathway', 'heg', 'seq']:
                if key in x and hasattr(x[key], 'size') and len(x[key].size()) > 0:
                    batch_size = x[key].size(0)
                    break
        elif hasattr(x, 'size'):
            if len(x.size()) > 0:
                batch_size = x.size(0)

        if batch_size is None:
            batch_size = 1
            print(f"WARNING: Could not determine batch size from inputs, using default value 1")

        # Store predictions from multiple forward passes
        predictions = []
        feature_importances = []

        # Run multiple forward passes with dropout enabled
        for _ in range(n_samples):
            with torch.no_grad():
                # Forward pass
                pred = self.forward(x)

                # Store predictions
                if isinstance(pred, dict):
                    # Dictionary output (multi-task)
                    if 'optimal_temperature' in pred:
                        batch_pred = torch.stack([
                            pred['growth_rate'],
                            pred['optimal_temperature']], dim=1)
                    else:
                        # Single task in dict format
                        batch_pred = pred['growth_rate']
                        if batch_pred.dim() == 0:  # scalar
                            batch_pred = batch_pred.unsqueeze(0).expand(batch_size)
                        if batch_pred.dim() == 1:  # vector
                            batch_pred = batch_pred.unsqueeze(1)  # Add feature dimension
                else:
                    # Direct tensor output
                    if pred.dim() == 0:  # scalar
                        batch_pred = pred.unsqueeze(0).expand(batch_size).unsqueeze(1)
                    elif pred.dim() == 1:  # vector
                        if pred.size(0) != batch_size:
                            # Wrong batch dimension
                            batch_pred = pred.unsqueeze(0).expand(batch_size, -1)
                        else:
                            batch_pred = pred.unsqueeze(1)  # Add feature dimension
                    else:
                        # Already has batch and feature dimensions
                        batch_pred = pred

                predictions.append(batch_pred)

                # Also track feature importance if desired
                if hasattr(self, 'feature_importance') and self.feature_importance:
                    feature_importances.append(self.get_feature_importance())

        # Stack predictions and calculate statistics
        stacked_preds = torch.stack(predictions)

        # Ensure stacked_preds has correct shape [n_samples, batch_size, output_dim]
        if stacked_preds.dim() == 2:  # [n_samples, batch_size]
            stacked_preds = stacked_preds.unsqueeze(-1)  # Add output_dim

        mean_pred = stacked_preds.mean(dim=0)
        std_pred = stacked_preds.std(dim=0)

        # Apply calibration if enabled
        if self.uncertainty_calibration:
            if std_pred.size(-1) == self.uncertainty_scale.size(0):
                std_pred = std_pred * self.uncertainty_scale
        else:
                # Handle dimension mismatch
                scale = self.uncertainty_scale
                if scale.dim() == 1 and std_pred.dim() == 2:
                    if scale.size(0) == 1 and std_pred.size(1) > 1:
                        scale = scale.expand(std_pred.size(1))
                    elif scale.size(0) > 1 and std_pred.size(1) == 1:
                        std_pred = std_pred.expand(-1, scale.size(0))
                std_pred = std_pred * scale

        # Calculate coefficient of variation (relative uncertainty)
        cv = torch.zeros_like(mean_pred)
        non_zero = mean_pred != 0
        cv[non_zero] = std_pred[non_zero] / torch.abs(mean_pred[non_zero])

        # Format output to match model output format
        if self.output_dim == 1 or (mean_pred.dim() > 1 and mean_pred.size(1) == 1):
            # Single output task
            return {
                'prediction': mean_pred.squeeze(-1),
                'uncertainty': std_pred.squeeze(-1),
                'coefficient_of_variation': cv.squeeze(-1),
                'n_samples': n_samples
            }
        else:
            # Multi-task output
            return {
                'growth_rate': {
                    'prediction': mean_pred[:, 0],
                    'uncertainty': std_pred[:, 0],
                    'coefficient_of_variation': cv[:, 0]
                },
                'optimal_temperature': {
                    'prediction': mean_pred[:, 1],
                    'uncertainty': std_pred[:, 1],
                    'coefficient_of_variation': cv[:, 1]
                },
                'n_samples': n_samples
            }

    def calibrate_uncertainty(self, val_data, target_coverage=0.9):
        """
        Calibrate uncertainty estimates using validation data.

        Args:
            val_data: List of (x, y) tuples for validation
            target_coverage: Target coverage probability for uncertainty intervals

        Returns:
            Calibration statistics
        """
        if not self.uncertainty_calibration:
            print("Uncertainty calibration is disabled for this model")
            return {}

        # Collect predictions and ground truth
        predictions = []
        uncertainties = []
        targets = []

        # Generate predictions with uncertainty
        for x, y in val_data:
            # Get predictions
            pred_dict = self.predict_with_uncertainty(x, n_samples=20)

            # Extract values based on output format
            if 'growth_rate' in pred_dict:
                # Multi-task
                growth_pred = pred_dict['growth_rate']['prediction']
                growth_unc = pred_dict['growth_rate']['uncertainty']
                temp_pred = pred_dict['optimal_temperature']['prediction']
                temp_unc = pred_dict['optimal_temperature']['uncertainty']

                predictions.append(torch.stack([growth_pred, temp_pred], dim=1))
                uncertainties.append(torch.stack([growth_unc, temp_unc], dim=1))

                # Extract targets
                y_dict = {}
                if isinstance(y, dict):
                    y_dict = y
                else:
                    # Try to parse tensor
                    if y.dim() > 1 and y.size(1) >= 2:
                        y_dict = {'growth_rate': y[:, 0], 'optimal_temperature': y[:, 1]}

                if 'growth_rate' in y_dict and 'optimal_temperature' in y_dict:
                    targets.append(torch.stack([y_dict['growth_rate'], y_dict['optimal_temperature']], dim=1))
            else:
                # Single task
                predictions.append(pred_dict['prediction'].unsqueeze(-1) if pred_dict['prediction'].dim() == 1 else pred_dict['prediction'])
                uncertainties.append(pred_dict['uncertainty'].unsqueeze(-1) if pred_dict['uncertainty'].dim() == 1 else pred_dict['uncertainty'])

                if isinstance(y, dict) and 'growth_rate' in y:
                    targets.append(y['growth_rate'].unsqueeze(-1))
                else:
                    # Assume y is the target tensor
                    targets.append(y.unsqueeze(-1) if y.dim() == 1 else y)

        # Stack all data
        all_preds = torch.cat(predictions, dim=0)
        all_uncs = torch.cat(uncertainties, dim=0)
        all_targets = torch.cat(targets, dim=0)

        # Calculate calibration factors
        z_score = torch.distributions.Normal(0, 1).icdf(torch.tensor(0.5 + target_coverage / 2))

        # For each output dimension
        for dim in range(all_preds.size(1)):
            # Calculate errors
            errors = torch.abs(all_preds[:, dim] - all_targets[:, dim])

            # Expected width of prediction interval for target coverage
            target_width = z_score * all_uncs[:, dim]

            # Calculate scaling factor for calibration
            scale = torch.median(errors / target_width)

            # Update calibration parameter
            self.uncertainty_scale[dim] = scale

        # Calculate calibrated coverage
        calibrated_coverage = self._calculate_coverage(all_preds, all_uncs * self.uncertainty_scale, all_targets)

        return {
            'uncertainty_scale': self.uncertainty_scale.cpu().numpy(),
            'calibrated_coverage': calibrated_coverage
        }

    def _calculate_coverage(self, predictions, uncertainties, targets):
        """Calculate empirical coverage of prediction intervals."""
        coverage = {}

        # Calculate for each output dimension
        for dim in range(predictions.size(1)):
            # z-scores for different confidence levels
            confidence_levels = torch.tensor([0.5, 0.8, 0.9, 0.95, 0.99])
            coverage_rates = []

            for conf in confidence_levels:
                z = torch.distributions.Normal(0, 1).icdf(torch.tensor(0.5 + conf / 2))

                # Prediction intervals
                lower = predictions[:, dim] - z * uncertainties[:, dim]
                upper = predictions[:, dim] + z * uncertainties[:, dim]

                # Check if target is within interval
                in_interval = (targets[:, dim] >= lower) & (targets[:, dim] <= upper)
                coverage_rate = in_interval.float().mean().item()
                coverage_rates.append(coverage_rate)

            # Store results
            if predictions.size(1) == 1:
                coverage['overall'] = dict(zip([f"{c:.0%}" for c in confidence_levels.numpy()], coverage_rates))
            else:
                dim_name = 'growth_rate' if dim == 0 else 'optimal_temperature'
                coverage[dim_name] = dict(zip([f"{c:.0%}" for c in confidence_levels.numpy()], coverage_rates))

        return coverage

def predict_with_uncertainty(model, x, n_samples=10):
    """
    Generate predictions with uncertainty estimates using Monte Carlo Dropout.
    This is a generic function that works with any PyTorch model with dropout layers.

    Args:
        model: PyTorch model with dropout layers
        x: Input features
        n_samples: Number of stochastic forward passes

    Returns:
        Tuple of (mean prediction, standard deviation)
    """
    # Check if the model has a built-in method for uncertainty estimation
    if hasattr(model, 'predict_with_uncertainty'):
        return model.predict_with_uncertainty(x, n_samples=n_samples)

    # Determine batch size for shape checking
    batch_size = None
    if isinstance(x, dict):
        for key in ['codon_metrics', 'pathway', 'heg', 'seq']:
            if key in x and hasattr(x[key], 'size') and len(x[key].size()) > 0:
                batch_size = x[key].size(0)
                break
    elif hasattr(x, 'size'):
        if len(x.size()) > 0:
            batch_size = x.size(0)

    if batch_size is None:
        batch_size = 1
        print(f"WARNING: Could not determine batch size from inputs, using default value 1")

    # If not, use the generic implementation
    model.train()  # Enable dropout for MC Dropout
    predictions = []

    for _ in range(n_samples):
        with torch.no_grad():
            pred = model(x)

            # Handle different return formats
            if isinstance(pred, dict):
                # Get keys for outputs
                keys = list(pred.keys())

                # Remove non-tensor items from keys
                keys = [k for k in keys if isinstance(pred[k], torch.Tensor)]

                # Sort keys for consistent ordering
                keys.sort()

                # Stack all tensor outputs
                if len(keys) > 1:
                    batch_pred = torch.stack([pred[k] for k in keys], dim=1)
                else:
                    # Single output tensor
                    batch_pred = pred[keys[0]]
                    if batch_pred.dim() == 0:  # scalar
                        batch_pred = batch_pred.unsqueeze(0).expand(batch_size)
                    if batch_pred.dim() == 1:  # vector
                        batch_pred = batch_pred.unsqueeze(1)  # Add feature dimension
            else:
                # Direct tensor output
                if pred.dim() == 0:  # scalar
                    batch_pred = pred.unsqueeze(0).expand(batch_size).unsqueeze(1)
                elif pred.dim() == 1:  # vector
                    if pred.size(0) != batch_size:
                        # Wrong batch dimension
                        batch_pred = pred.unsqueeze(0).expand(batch_size, -1)
                    else:
                        batch_pred = pred.unsqueeze(1)  # Add feature dimension
                else:
                    # Already has batch and feature dimensions
                    batch_pred = pred

            predictions.append(batch_pred)

    # Stack predictions and calculate statistics
    stacked = torch.stack(predictions)

    # Ensure stacked_preds has correct shape [n_samples, batch_size, output_dim]
    if stacked.dim() == 2:  # [n_samples, batch_size]
        stacked = stacked.unsqueeze(-1)  # Add output_dim

    mean_pred = stacked.mean(dim=0)
    std_pred = stacked.std(dim=0)

    # Calculate coefficient of variation (relative uncertainty)
    cv = torch.zeros_like(mean_pred)
    non_zero = mean_pred != 0
    cv[non_zero] = std_pred[non_zero] / torch.abs(mean_pred[non_zero])

    return mean_pred, std_pred, cv
