"""Hybrid Random Forest + Transformer Model Module

This module implements a hybrid approach that combines:
1. Random Forest for growth rate prediction
2. Transformer-based neural network for optimal temperature prediction

This approach leverages the strengths of both models:
- Random Forest excels at tabular data and regression tasks like growth rate prediction
- Transformer architecture has shown superior performance for temperature prediction
"""

import os
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader
import math
import joblib
from typing import Dict, List, Optional, Tuple, Union, Any
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import r2_score, mean_squared_error

from ..utils.logging import get_logger, log_function_call
from .transformer_network import HybridTransformerDNN, TransformerTrainer

# Get logger
logger = get_logger()


class HybridRFTransformer:
    """Hybrid model combining Random Forest for growth rate and Transformer for temperature.
    
    This class implements a hybrid approach that uses:
    1. Random Forest for growth rate prediction (better with tabular data)
    2. Transformer-based neural network for optimal temperature prediction (better with complex patterns)
    
    The model handles feature preprocessing, training, and prediction for both components.
    """
    
    def __init__(
        self,
        feature_dims: Dict[str, int],
        transformer_hidden_dims: List[int] = [256, 128, 64],
        transformer_dropout_rates: List[float] = [0.2, 0.3, 0.4],
        transformer_heads: int = 4,
        transformer_layers: int = 2,
        use_batch_norm: bool = True,
        use_residual: bool = True,
        use_positional_encoding: bool = True,
        rf_n_estimators: int = 200,
        rf_max_depth: int = 20,
        rf_min_samples_split: int = 2,
        rf_min_samples_leaf: int = 1,
        device: str = 'cuda' if torch.cuda.is_available() else 'cpu'
    ):
        """Initialize the hybrid model.
        
        Args:
            feature_dims: Dictionary mapping feature types to their dimensions
                          e.g., {'codon': 64, 'aa': 32, 'genomic': 128, ...}
            transformer_hidden_dims: List of hidden dimensions for transformer
            transformer_dropout_rates: List of dropout rates for transformer
            transformer_heads: Number of attention heads in transformer
            transformer_layers: Number of transformer layers
            use_batch_norm: Whether to use batch normalization in transformer
            use_residual: Whether to use residual connections in transformer
            use_positional_encoding: Whether to use positional encoding in transformer
            rf_n_estimators: Number of trees in random forest
            rf_max_depth: Maximum depth of trees in random forest
            rf_min_samples_split: Minimum samples required to split a node
            rf_min_samples_leaf: Minimum samples required at a leaf node
            device: Device to use for transformer model ('cuda' or 'cpu')
        """
        self.feature_dims = feature_dims
        self.device = device
        
        # Initialize Random Forest for growth rate prediction
        self.rf_model = RandomForestRegressor(
            n_estimators=rf_n_estimators,
            max_depth=rf_max_depth,
            min_samples_split=rf_min_samples_split,
            min_samples_leaf=rf_min_samples_leaf,
            random_state=42,
            n_jobs=-1  # Use all available cores
        )
        
        # Initialize Transformer for temperature prediction
        self.transformer_model = HybridTransformerDNN(
            feature_dims=feature_dims,
            hidden_dims=transformer_hidden_dims,
            output_dim=1,  # Single output for temperature
            dropout_rates=transformer_dropout_rates,
            use_batch_norm=use_batch_norm,
            use_residual=use_residual,
            transformer_heads=transformer_heads,
            transformer_layers=transformer_layers,
            use_positional_encoding=use_positional_encoding,
            multi_task=False  # Single task for temperature only
        )
        
        # Move transformer to device
        self.transformer_model.to(device)
        
        # Initialize transformer trainer
        self.transformer_trainer = TransformerTrainer(
            model=self.transformer_model,
            learning_rate=0.001,
            weight_decay=1e-5,
            device=device
        )
        
        # Track if models are trained
        self.rf_trained = False
        self.transformer_trained = False
        
        # Store feature importance
        self.feature_importance = None
        
        # Store metrics
        self.metrics = {
            'growth_rate_r2': 0.0,
            'growth_rate_rmse': 0.0,
            'temperature_r2': 0.0,
            'temperature_rmse': 0.0
        }
    
    def _prepare_rf_features(self, features: Dict[str, np.ndarray]) -> np.ndarray:
        """Prepare features for Random Forest model.
        
        Args:
            features: Dictionary mapping feature types to feature arrays
            
        Returns:
            Concatenated feature array for Random Forest
        """
        # Concatenate all features into a single array
        feature_arrays = []
        for feature_type, feature_array in features.items():
            if feature_type in self.feature_dims and self.feature_dims[feature_type] > 0:
                feature_arrays.append(feature_array)
        
        if not feature_arrays:
            raise ValueError("No valid features found for Random Forest model")
        
        return np.concatenate(feature_arrays, axis=1)
    
    def _prepare_transformer_features(self, features: Dict[str, np.ndarray]) -> Dict[str, torch.Tensor]:
        """Prepare features for Transformer model.
        
        Args:
            features: Dictionary mapping feature types to feature arrays
            
        Returns:
            Dictionary mapping feature types to PyTorch tensors
        """
        transformer_features = {}
        for feature_type, feature_array in features.items():
            if feature_type in self.feature_dims and self.feature_dims[feature_type] > 0:
                transformer_features[feature_type] = torch.tensor(
                    feature_array, dtype=torch.float32, device=self.device
                )
        
        return transformer_features
    
    def train_rf(
        self,
        train_features: Dict[str, np.ndarray],
        train_targets: Dict[str, np.ndarray],
        val_features: Optional[Dict[str, np.ndarray]] = None,
        val_targets: Optional[Dict[str, np.ndarray]] = None
    ) -> Dict[str, float]:
        """Train the Random Forest model for growth rate prediction.
        
        Args:
            train_features: Dictionary mapping feature types to training feature arrays
            train_targets: Dictionary with 'growth_rate' key mapping to training targets
            val_features: Dictionary mapping feature types to validation feature arrays
            val_targets: Dictionary with 'growth_rate' key mapping to validation targets
            
        Returns:
            Dictionary with training metrics
        """
        logger.info("Training Random Forest model for growth rate prediction")
        
        # Prepare features for Random Forest
        X_train = self._prepare_rf_features(train_features)
        y_train = train_targets['growth_rate']
        
        # Train Random Forest model
        self.rf_model.fit(X_train, y_train)
        
        # Get feature importance
        self.feature_importance = self.rf_model.feature_importances_
        
        # Calculate training metrics
        train_pred = self.rf_model.predict(X_train)
        train_r2 = r2_score(y_train, train_pred)
        train_rmse = np.sqrt(mean_squared_error(y_train, train_pred))
        
        metrics = {
            'train_r2': train_r2,
            'train_rmse': train_rmse
        }
        
        # Calculate validation metrics if validation data is provided
        if val_features is not None and val_targets is not None:
            X_val = self._prepare_rf_features(val_features)
            y_val = val_targets['growth_rate']
            
            val_pred = self.rf_model.predict(X_val)
            val_r2 = r2_score(y_val, val_pred)
            val_rmse = np.sqrt(mean_squared_error(y_val, val_pred))
            
            metrics.update({
                'val_r2': val_r2,
                'val_rmse': val_rmse
            })
            
            # Store metrics
            self.metrics['growth_rate_r2'] = val_r2
            self.metrics['growth_rate_rmse'] = val_rmse
        
        # Mark as trained
        self.rf_trained = True
        
        return metrics
    
    def train_transformer(
        self,
        train_loader: DataLoader,
        val_loader: DataLoader,
        epochs: int = 100,
        patience: int = 10,
        save_path: Optional[str] = None
    ) -> Dict[str, List[float]]:
        """Train the Transformer model for temperature prediction.
        
        Args:
            train_loader: DataLoader for training data
            val_loader: DataLoader for validation data
            epochs: Number of epochs to train
            patience: Patience for early stopping
            save_path: Path to save the best model
            
        Returns:
            Dictionary with training history
        """
        logger.info("Training Transformer model for temperature prediction")
        
        # Train transformer model
        history = self.transformer_trainer.train(
            train_loader=train_loader,
            val_loader=val_loader,
            epochs=epochs,
            patience=patience,
            save_path=save_path
        )
        
        # Mark as trained
        self.transformer_trained = True
        
        # Store metrics from the last epoch
        if 'val_r2' in history:
            self.metrics['temperature_r2'] = history['val_r2'][-1]
        if 'val_rmse' in history:
            self.metrics['temperature_rmse'] = history['val_rmse'][-1]
        
        return history
    
    def predict_growth_rate(self, features: Dict[str, np.ndarray]) -> np.ndarray:
        """Predict growth rate using the Random Forest model.
        
        Args:
            features: Dictionary mapping feature types to feature arrays
            
        Returns:
            Array of growth rate predictions
        """
        if not self.rf_trained:
            raise RuntimeError("Random Forest model has not been trained yet")
        
        # Prepare features for Random Forest
        X = self._prepare_rf_features(features)
        
        # Predict growth rate
        return self.rf_model.predict(X)
    
    def predict_temperature(self, features: Dict[str, np.ndarray]) -> np.ndarray:
        """Predict temperature using the Transformer model.
        
        Args:
            features: Dictionary mapping feature types to feature arrays
            
        Returns:
            Array of temperature predictions
        """
        if not self.transformer_trained:
            raise RuntimeError("Transformer model has not been trained yet")
        
        # Prepare features for Transformer
        transformer_features = self._prepare_transformer_features(features)
        
        # Set model to evaluation mode
        self.transformer_model.eval()
        
        # Predict temperature
        with torch.no_grad():
            predictions = self.transformer_model(transformer_features)
            
            # Extract temperature predictions
            if isinstance(predictions, dict) and 'output' in predictions:
                temp_pred = predictions['output'].cpu().numpy()
            else:
                temp_pred = predictions.cpu().numpy()
        
        return temp_pred
    
    def predict(self, features: Dict[str, np.ndarray]) -> Dict[str, np.ndarray]:
        """Predict both growth rate and temperature.
        
        Args:
            features: Dictionary mapping feature types to feature arrays
            
        Returns:
            Dictionary with 'growth_rate' and 'temperature' predictions
        """
        # Predict growth rate
        growth_rate = self.predict_growth_rate(features)
        
        # Predict temperature
        temperature = self.predict_temperature(features)
        
        return {
            'growth_rate': growth_rate,
            'temperature': temperature
        }
    
    def predict_with_uncertainty(
        self,
        features: Dict[str, np.ndarray],
        n_samples: int = 30
    ) -> Dict[str, np.ndarray]:
        """Predict with uncertainty estimates.
        
        For Random Forest, uses the variance across trees.
        For Transformer, uses Monte Carlo Dropout.
        
        Args:
            features: Dictionary mapping feature types to feature arrays
            n_samples: Number of samples for uncertainty estimation
            
        Returns:
            Dictionary with mean predictions and uncertainty estimates
        """
        # Prepare features
        X_rf = self._prepare_rf_features(features)
        transformer_features = self._prepare_transformer_features(features)
        
        # Random Forest prediction with uncertainty
        # Get predictions from all trees
        rf_predictions = np.array([tree.predict(X_rf) for tree in self.rf_model.estimators_])
        growth_rate_mean = np.mean(rf_predictions, axis=0)
        growth_rate_std = np.std(rf_predictions, axis=0)
        
        # Transformer prediction with uncertainty using MC Dropout
        self.transformer_model.train()  # Set to train mode to enable dropout
        
        temperature_samples = []
        for _ in range(n_samples):
            with torch.no_grad():
                predictions = self.transformer_model(transformer_features)
                
                # Extract temperature predictions
                if isinstance(predictions, dict) and 'output' in predictions:
                    temp_pred = predictions['output'].cpu().numpy()
                else:
                    temp_pred = predictions.cpu().numpy()
                
                temperature_samples.append(temp_pred)
        
        temperature_samples = np.array(temperature_samples)
        temperature_mean = np.mean(temperature_samples, axis=0)
        temperature_std = np.std(temperature_samples, axis=0)
        
        return {
            'growth_rate_mean': growth_rate_mean,
            'growth_rate_std': growth_rate_std,
            'temperature_mean': temperature_mean,
            'temperature_std': temperature_std
        }
    
    def save(self, directory: str):
        """Save both models to disk.
        
        Args:
            directory: Directory to save models
        """
        os.makedirs(directory, exist_ok=True)
        
        # Save Random Forest model
        rf_path = os.path.join(directory, 'rf_model.joblib')
        joblib.dump(self.rf_model, rf_path)
        
        # Save Transformer model
        transformer_path = os.path.join(directory, 'transformer_model.pt')
        torch.save(self.transformer_model.state_dict(), transformer_path)
        
        # Save metrics
        metrics_path = os.path.join(directory, 'metrics.joblib')
        joblib.dump(self.metrics, metrics_path)
        
        # Save feature importance
        if self.feature_importance is not None:
            importance_path = os.path.join(directory, 'feature_importance.joblib')
            joblib.dump(self.feature_importance, importance_path)
        
        logger.info(f"Models saved to {directory}")
    
    def load(self, directory: str):
        """Load both models from disk.
        
        Args:
            directory: Directory to load models from
        """
        # Load Random Forest model
        rf_path = os.path.join(directory, 'rf_model.joblib')
        if os.path.exists(rf_path):
            self.rf_model = joblib.load(rf_path)
            self.rf_trained = True
        else:
            logger.warning(f"Random Forest model not found at {rf_path}")
        
        # Load Transformer model
        transformer_path = os.path.join(directory, 'transformer_model.pt')
        if os.path.exists(transformer_path):
            self.transformer_model.load_state_dict(torch.load(transformer_path, map_location=self.device))
            self.transformer_trained = True
        else:
            logger.warning(f"Transformer model not found at {transformer_path}")
        
        # Load metrics
        metrics_path = os.path.join(directory, 'metrics.joblib')
        if os.path.exists(metrics_path):
            self.metrics = joblib.load(metrics_path)
        
        # Load feature importance
        importance_path = os.path.join(directory, 'feature_importance.joblib')
        if os.path.exists(importance_path):
            self.feature_importance = joblib.load(importance_path)
        
        logger.info(f"Models loaded from {directory}")
    
    def get_feature_importance(self, feature_names: Optional[List[str]] = None) -> pd.DataFrame:
        """Get feature importance from the Random Forest model.
        
        Args:
            feature_names: List of feature names (optional)
            
        Returns:
            DataFrame with feature names and importance values
        """
        if self.feature_importance is None:
            raise RuntimeError("Feature importance not available. Train the model first.")
        
        # Create DataFrame with feature importance
        if feature_names is None:
            feature_names = [f"feature_{i}" for i in range(len(self.feature_importance))]
        
        importance_df = pd.DataFrame({
            'feature': feature_names,
            'importance': self.feature_importance
        })
        
        # Sort by importance
        importance_df = importance_df.sort_values('importance', ascending=False)
        
        return importance_df
    
    def get_metrics(self) -> Dict[str, float]:
        """Get model performance metrics.
        
        Returns:
            Dictionary with performance metrics
        """
        return self.metrics


class HybridRFTransformerTrainer:
    """Trainer for the HybridRFTransformer model.
    
    This class handles the training process for both the Random Forest and Transformer
    components of the hybrid model, including data preparation and evaluation.
    """
    
    def __init__(
        self,
        model: HybridRFTransformer,
        learning_rate: float = 0.001,
        weight_decay: float = 1e-5,
        device: str = 'cuda' if torch.cuda.is_available() else 'cpu'
    ):
        """Initialize the trainer.
        
        Args:
            model: HybridRFTransformer model
            learning_rate: Learning rate for transformer optimizer
            weight_decay: Weight decay for regularization
            device: Device to use for training ('cuda' or 'cpu')
        """
        self.model = model
        self.device = device
        self.learning_rate = learning_rate
        self.weight_decay = weight_decay
    
    def prepare_data_for_rf(
        self,
        data: Dict[str, Dict[str, np.ndarray]]
    ) -> Tuple[Dict[str, np.ndarray], Dict[str, np.ndarray], Dict[str, np.ndarray], Dict[str, np.ndarray]]:
        """Prepare data for Random Forest training.
        
        Args:
            data: Dictionary with 'train' and 'test' data, each containing 'features' and 'targets'
            
        Returns:
            Tuple of (train_features, train_targets, test_features, test_targets)
        """
        train_features = data['train']['features']
        train_targets = {'growth_rate': data['train']['targets']['growth_rate']}
        
        test_features = data['test']['features']
        test_targets = {'growth_rate': data['test']['targets']['growth_rate']}
        
        return train_features, train_targets, test_features, test_targets
    
    def prepare_data_for_transformer(
        self,
        data: Dict[str, Dict[str, np.ndarray]],
        batch_size: int = 32
    ) -> Tuple[DataLoader, DataLoader]:
        """Prepare data for Transformer training.
        
        Args:
            data: Dictionary with 'train' and 'test' data, each containing 'features' and 'targets'
            batch_size: Batch size for DataLoader
            
        Returns:
            Tuple of (train_loader, test_loader)
        """
        # Create custom datasets
        class CustomDataset(torch.utils.data.Dataset):
            def __init__(self, features, targets):
                self.features = features
                self.targets = {'target': targets['temperature']}  # Rename to 'target' for transformer
                self.length = len(next(iter(targets.values())))
            
            def __len__(self):
                return self.length
            
            def __getitem__(self, idx):
                return {
                    'features': {k: torch.tensor(v[idx], dtype=torch.float32) for k, v in self.features.items()},
                    'targets': {k: torch.tensor(v[idx], dtype=torch.float32) for k, v in self.targets.items()}
                }
        
        train_dataset = CustomDataset(data['train']['features'], data['train']['targets'])
        test_dataset = CustomDataset(data['test']['features'], data['test']['targets'])
        
        # Create dataloaders
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
        test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
        
        return train_loader, test_loader
    
    def train(
        self,
        data: Dict[str, Dict[str, np.ndarray]],
        rf_params: Dict[str, Any] = {},
        transformer_params: Dict[str, Any] = {},
        output_dir: Optional[str] = None
    ) -> Dict[str, Any]:
        """Train both models.
        
        Args:
            data: Dictionary with 'train' and 'test' data, each containing 'features' and 'targets'
            rf_params: Parameters for Random Forest training
            transformer_params: Parameters for Transformer training
            output_dir: Directory to save models
            
        Returns:
            Dictionary with training results
        """
        # Prepare data for Random Forest
        train_features, train_targets, test_features, test_targets = self.prepare_data_for_rf(data)
        
        # Train Random Forest
        rf_metrics = self.model.train_rf(
            train_features=train_features,
            train_targets=train_targets,
            val_features=test_features,
            val_targets=test_targets
        )
        
        # Prepare data for Transformer
        train_loader, test_loader = self.prepare_data_for_transformer(
            data, batch_size=transformer_params.get('batch_size', 32)
        )
        
        # Set up save path
        save_path = None
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)
            save_path = os.path.join(output_dir, 'transformer_model.pt')
        
        # Train Transformer
        transformer_history = self.model.train_transformer(
            train_loader=train_loader,
            val_loader=test_loader,
            epochs=transformer_params.get('epochs', 100),
            patience=transformer_params.get('patience', 10),
            save_path=save_path
        )
        
        # Save the complete model if output_dir is provided
        if output_dir:
            self.model.save(output_dir)
        
        # Return combined results
        return {
            'rf_metrics': rf_metrics,
            'transformer_history': transformer_history,
            'final_metrics': self.model.get_metrics()
        }
    
    def evaluate(
        self,
        test_features: Dict[str, np.ndarray],
        test_targets: Dict[str, np.ndarray]
    ) -> Dict[str, float]:
        """Evaluate the hybrid model.
        
        Args:
            test_features: Dictionary mapping feature types to test feature arrays
            test_targets: Dictionary with 'growth_rate' and 'temperature' keys mapping to test targets
            
        Returns:
            Dictionary with evaluation metrics
        """
        # Predict
        predictions = self.model.predict(test_features)
        
        # Calculate metrics
        growth_rate_r2 = r2_score(test_targets['growth_rate'], predictions['growth_rate'])
        growth_rate_rmse = np.sqrt(mean_squared_error(test_targets['growth_rate'], predictions['growth_rate']))
        
        temperature_r2 = r2_score(test_targets['temperature'], predictions['temperature'])
        temperature_rmse = np.sqrt(mean_squared_error(test_targets['temperature'], predictions['temperature']))
        
        metrics = {
            'growth_rate_r2': growth_rate_r2,
            'growth_rate_rmse': growth_rate_rmse,
            'temperature_r2': temperature_r2,
            'temperature_rmse': temperature_rmse
        }
        
        # Update model metrics
        self.model.metrics.update(metrics)
        
        return metrics
