"""
Feature-selected network for DeepMu.

This module provides a simplified network that uses only the most important features
directly, without complex feature fusion.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Optional, Tuple, Union

class FeatureSelectedGrowthModel(nn.Module):
    """
    A simplified model that uses only the most important features for growth rate prediction.

    This model skips complex feature fusion and directly uses the most important features
    identified through feature selection.
    """

    def __init__(
        self,
        important_features: List[str],
        hidden_dim: int = 64,
        dropout_rate: float = 0.2,
    ):
        """
        Initialize the feature-selected growth model.

        Args:
            important_features: List of important feature names
            hidden_dim: Hidden dimension size
            dropout_rate: Dropout rate
        """
        super().__init__()

        self.important_features = important_features
        self.hidden_dim = hidden_dim

        # Create a simple MLP for processing the important features
        self.mlp = nn.Sequential(
            nn.Linear(len(important_features), hidden_dim),
            nn.LayerNorm(hidden_dim),  # Use LayerNorm instead of BatchNorm for small batch sizes
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(hidden_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),  # Use LayerNorm instead of BatchNorm for small batch sizes
            nn.ReLU(),
            nn.Dropout(dropout_rate),
        )

        # Output layers
        self.growth_output = nn.Linear(hidden_dim, 1)
        self.temp_output = nn.Linear(hidden_dim, 1)

    def forward(self, features: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        Forward pass through the model.

        Args:
            features: Dictionary of input features

        Returns:
            Dictionary of output predictions
        """
        # Extract important features
        important_values = []
        batch_size = next(iter(features.values())).size(0) if features else 1
        device = next(self.parameters()).device

        for feature_name in self.important_features:
            if feature_name in features:
                # Handle different feature shapes
                feature_value = features[feature_name]

                # Ensure feature is a 2D tensor (batch_size, 1)
                if feature_value.dim() == 1:
                    feature_value = feature_value.unsqueeze(1)
                # If feature is multi-dimensional, flatten it
                elif len(feature_value.shape) > 1 and feature_value.shape[1] > 1:
                    feature_value = feature_value.mean(dim=1, keepdim=True)

                important_values.append(feature_value)
            else:
                # If feature is missing, add a zero tensor with correct batch size
                important_values.append(torch.zeros(batch_size, 1, device=device))

        # Concatenate important features
        if important_values:
            x = torch.cat(important_values, dim=1)
        else:
            # Handle case with no features
            device = next(self.parameters()).device
            x = torch.zeros(1, len(self.important_features), device=device)

        # Process through MLP
        x = self.mlp(x)

        # Generate predictions
        growth_pred = self.growth_output(x)
        temp_pred = self.temp_output(x)

        # Squeeze to match expected shape (batch_size,) instead of (batch_size, 1)
        growth_pred = growth_pred.squeeze(-1)
        temp_pred = temp_pred.squeeze(-1)

        return {
            'growth_rate': growth_pred,
            'optimal_temperature': temp_pred
        }


def create_feature_selected_model(important_features, hidden_dim=64):
    """
    Create a feature-selected model.

    Args:
        important_features: List of important feature names
        hidden_dim: Hidden dimension size

    Returns:
        FeatureSelectedGrowthModel instance
    """
    return FeatureSelectedGrowthModel(
        important_features=important_features,
        hidden_dim=hidden_dim,
    )
