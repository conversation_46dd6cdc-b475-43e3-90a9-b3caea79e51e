"""
注意力机制模块

这个模块实现了神经网络的各种注意力机制，
包括用于序列数据的位置注意力。

注意力机制类型：
1. 位置感知注意力 - 考虑序列位置信息的注意力
2. 多头自注意力 - 多个注意力头的并行计算
3. 门控注意力 - 带有门控机制的注意力整合

技术特点：
- 支持多种整合模式（加性、乘性、门控）
- 可配置的注意力头数量
- Dropout正则化防止过拟合
- 高效的卷积实现
- GPU加速支持

应用场景：
- 序列建模中的重要位置识别
- 特征重要性权重计算
- 多模态特征融合
- 长序列依赖关系建模
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math

class PositionalAttention(nn.Module):
    """
    位置感知多头自注意力机制

    这个注意力机制允许模型根据输入序列不同部分的重要性来关注它们，
    并具有位置感知能力。

    核心功能：
    - 多头注意力：并行计算多个注意力头
    - 位置感知：考虑序列中的位置信息
    - 自适应权重：根据内容动态调整注意力权重
    - 特征整合：支持多种注意力整合模式

    技术实现：
    - 使用1D卷积进行高效的查询、键、值投影
    - 支持加性、乘性和门控整合模式
    - Dropout正则化防止过拟合
    - 残差连接保持梯度流动
    """

    def __init__(
        self,
        channels: int,
        attention_dim: int = 64,
        num_heads: int = 4,
        dropout: float = 0.1,
        integration_mode: str = 'additive'
    ):
        """
        初始化位置注意力模块

        Args:
            channels (int): 输入通道数
            attention_dim (int): 注意力机制的维度
            num_heads (int): 注意力头的数量
            dropout (float): Dropout比率
            integration_mode (str): 注意力整合方式 ('additive', 'multiplicative', 'gated')
        """
        super(PositionalAttention, self).__init__()
        
        self.channels = channels
        self.attention_dim = attention_dim
        self.num_heads = num_heads
        self.integration_mode = integration_mode
        
        # Ensure attention dimension is divisible by number of heads
        assert attention_dim % num_heads == 0, "Attention dimension must be divisible by number of heads"
        
        # Attention projection layers
        self.query_proj = nn.Conv1d(channels, attention_dim, kernel_size=1)
        self.key_proj = nn.Conv1d(channels, attention_dim, kernel_size=1)
        self.value_proj = nn.Conv1d(channels, attention_dim, kernel_size=1)
        
        # Output projection
        self.output_proj = nn.Conv1d(attention_dim, channels, kernel_size=1)
        
        # Dropout for attention weights
        self.dropout = nn.Dropout(dropout)
        
        # Layer normalization
        self.layer_norm = nn.LayerNorm(channels)
        
        # Integration mode specific layers
        if integration_mode == 'gated':
            self.gate = nn.Sequential(
                nn.Conv1d(channels * 2, channels, kernel_size=1),
                nn.Sigmoid()
            )
    
    def forward(self, x, attention_matrix=None):
        """Forward pass through the attention mechanism.
        
        Args:
            x: Input tensor [batch_size, channels, seq_length]
            attention_matrix: Optional pre-computed attention matrix
                             [batch_size, seq_length, seq_length]
                             
        Returns:
            Tensor with attended features [batch_size, channels, seq_length]
        """
        batch_size, channels, seq_length = x.size()
        
        # Apply layer normalization (converting to [batch, seq_length, channels] for LayerNorm)
        x_norm = self.layer_norm(x.transpose(1, 2)).transpose(1, 2)
        
        # Project queries, keys, and values
        queries = self.query_proj(x_norm)
        keys = self.key_proj(x_norm)
        values = self.value_proj(x_norm)
        
        # Reshape for multi-head attention
        head_dim = self.attention_dim // self.num_heads
        
        queries = queries.view(batch_size, self.num_heads, head_dim, seq_length)
        keys = keys.view(batch_size, self.num_heads, head_dim, seq_length)
        values = values.view(batch_size, self.num_heads, head_dim, seq_length)
        
        # Transpose for attention computation
        queries = queries.transpose(2, 3)  # [batch, heads, seq_length, head_dim]
        keys = keys.permute(0, 1, 3, 2)    # [batch, heads, head_dim, seq_length]
        values = values.transpose(2, 3)    # [batch, heads, seq_length, head_dim]
        
        # Compute attention scores
        if attention_matrix is None:
            # Standard scaled dot-product attention
            attention_scores = torch.matmul(queries, keys) / math.sqrt(head_dim)
            attention_weights = F.softmax(attention_scores, dim=-1)
        else:
            # Use provided attention matrix
            # Ensure it has the right shape for multi-head attention
            if len(attention_matrix.shape) == 3:  # [batch, seq, seq]
                attention_matrix = attention_matrix.unsqueeze(1).expand(-1, self.num_heads, -1, -1)
            
            # Combine with computed attention
            attention_scores = torch.matmul(queries, keys) / math.sqrt(head_dim)
            attention_scores = attention_scores + attention_matrix
            attention_weights = F.softmax(attention_scores, dim=-1)
        
        # Apply dropout to attention weights
        attention_weights = self.dropout(attention_weights)
        
        # Apply attention to values
        attended_values = torch.matmul(attention_weights, values)
        
        # Reshape back to original dimensions
        attended_values = attended_values.transpose(2, 3)  # [batch, heads, head_dim, seq_length]
        attended_values = attended_values.contiguous().view(batch_size, self.attention_dim, seq_length)
        
        # Project back to channel dimension
        output = self.output_proj(attended_values)
        
        # Apply different integration modes
        if self.integration_mode == 'additive':
            # Residual connection
            output = output + x
        elif self.integration_mode == 'multiplicative':
            # Multiplicative attention
            output = output * x
        elif self.integration_mode == 'gated':
            # Gated integration
            gate_input = torch.cat([output, x], dim=1)
            gate_value = self.gate(gate_input)
            output = gate_value * output + (1 - gate_value) * x
        
        return output
