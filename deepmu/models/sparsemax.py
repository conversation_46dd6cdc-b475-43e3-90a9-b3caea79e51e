#!/usr/bin/env python
# -*- coding: utf-8 -*-

import torch
import torch.nn as nn
import torch.nn.functional as F


def sparsemax(x, dim=-1):
    """
    Sparsemax function implementation based on the paper:
    "From Softmax to Sparsemax: A Sparse Model of Attention and Multi-Label Classification"
    
    Args:
        x (torch.Tensor): Input tensor
        dim (int): Dimension along which to apply sparsemax
        
    Returns:
        torch.Tensor: Normalized sparse distribution
    """
    orig_size = x.size()
    
    # Flatten input if dim != -1
    if dim != -1:
        x = x.transpose(dim, -1)
        x_flat = x.reshape(-1, x.size(-1))
    else:
        x_flat = x.reshape(-1, x.size(-1))
    
    # Sort input in descending order
    x_sorted, _ = torch.sort(x_flat, dim=-1, descending=True)
    
    # Compute cumulative sum
    x_cumsum = torch.cumsum(x_sorted, dim=-1)
    
    # Get sequence length
    seq_len = x_flat.size(-1)
    
    # Get indices
    indices = torch.arange(1, seq_len + 1, device=x.device).float()
    
    # Compute threshold condition: x_sorted > (cumsum - 1) / k
    thresholds = (x_cumsum - 1) / indices
    thresholds = x_sorted - thresholds
    
    # Find the number of elements to keep
    mask = thresholds > 0
    max_k = mask.sum(dim=-1, keepdim=True)
    max_k = max_k.clamp(min=1)  # Ensure at least 1 element
    
    # Compute threshold value
    k = max_k.squeeze(-1)
    indices_for_tau = torch.arange(seq_len, device=x.device).view(1, -1)
    
    # Get indices for each element
    indices_for_tau = indices_for_tau.expand(x_flat.size(0), -1)
    mask_k = indices_for_tau < k.unsqueeze(-1)
    
    # Extract top-k elements and compute tau
    x_summed = torch.sum(x_sorted * mask_k.float(), dim=-1, keepdim=True)
    tau = (x_summed - 1) / k.unsqueeze(-1)
    
    # Sparsemax: max(0, x - tau)
    result = torch.clamp(x_flat - tau, min=0)
    
    # Reshape back
    if dim != -1:
        result = result.reshape(orig_size)
        result = result.transpose(dim, -1)
    else:
        result = result.reshape(orig_size)
    
    return result


def entmax15(x, dim=-1):
    """
    1.5-entmax (Tsallis Entmax) implementation based on the paper:
    "Sparse Sequence-to-Sequence Models"
    
    Args:
        x (torch.Tensor): Input tensor
        dim (int): Dimension along which to apply 1.5-entmax
        
    Returns:
        torch.Tensor: Normalized sparse distribution
    """
    orig_size = x.size()
    
    # Flatten input if dim != -1
    if dim != -1:
        x = x.transpose(dim, -1)
        x_flat = x.reshape(-1, x.size(-1))
    else:
        x_flat = x.reshape(-1, x.size(-1))
    
    # Compute tau (threshold)
    tau_flat, _ = entmax15_threshold_and_support(x_flat)
    
    # Apply 1.5-entmax: [max(0, x - tau)]^2
    output_flat = torch.clamp(x_flat - tau_flat, min=0)
    output_flat = output_flat ** 2
    
    # Reshape back
    if dim != -1:
        output = output_flat.reshape(orig_size[:-1] + (-1,))
        output = output.transpose(dim, -1)
    else:
        output = output_flat.reshape(orig_size)
    
    return output


def entmax15_threshold_and_support(x):
    """
    Compute the threshold and support for 1.5-entmax.
    
    Args:
        x (torch.Tensor): Input tensor with shape (batch_size, n_classes)
        
    Returns:
        tuple: (tau, support_size)
            - tau: Threshold values with shape (batch_size, 1)
            - support_size: Number of non-zero elements with shape (batch_size, 1)
    """
    # Sort input in descending order
    x_sorted, _ = torch.sort(x, dim=-1, descending=True)
    
    # Compute running sum
    x_running_sum = torch.cumsum(x_sorted, dim=-1)
    
    # Get sequence length
    n_classes = x.size(-1)
    
    # Initialize values
    support_size = torch.zeros(x.size(0), 1, device=x.device)
    tau = torch.zeros(x.size(0), 1, device=x.device)
    
    # Binary search
    for k in range(1, n_classes + 1):
        # Compute potential threshold
        potential_tau = (2 * k + x_running_sum[..., k - 1]) / (2 * k)
        
        # Check validity
        if k < n_classes:
            valid = x_sorted[..., k - 1] > potential_tau
            valid = valid.unsqueeze(-1)
        else:
            valid = torch.ones_like(tau, dtype=torch.bool)
        
        # Update support and tau
        support_size = torch.where(valid, k * torch.ones_like(support_size), support_size)
        tau = torch.where(valid, potential_tau, tau)
    
    return tau, support_size 