"""Simple Neural Network Models Module

This module implements simplified neural network models for microbial growth rate
and optimal growth temperature prediction, with a focus on direct feature usage
without complex fusion mechanisms.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Optional, List, Tuple, Union

from .networks import EnhancedPhyloGrowthModel
from ..utils.logging import get_logger, log_function_call

logger = get_logger()

class SimpleFeatureCombiner(nn.Module):
    """
    Simple feature combiner that concatenates features and processes them through an MLP.

    This module avoids complex fusion mechanisms and simply concatenates all features
    before processing them through a multi-layer perceptron.
    """

    def __init__(self, feature_dims, hidden_dim, output_dim, dropout=0.2):
        """
        Initialize the simple feature combiner.

        Args:
            feature_dims: Dictionary mapping feature names to their dimensions
            hidden_dim: Dimension of the hidden layer
            output_dim: Dimension of the output
            dropout: Dropout rate for regularization
        """
        super().__init__()
        self.feature_dims = feature_dims
        self.hidden_dim = hidden_dim
        self.output_dim = output_dim
        self.dropout = dropout

        # Calculate total input dimension
        self.total_dim = sum(feature_dims.values())

        # We'll create the MLP in the forward pass after we know the actual input dimension

        # Feature names for tracking
        self.feature_names = list(feature_dims.keys())

    def forward(self, features):
        """
        Forward pass through the combiner.

        Args:
            features: Dictionary mapping feature names to feature tensors

        Returns:
            Processed feature tensor
        """
        # Get device and batch size from first feature
        device = next(iter(features.values())).device
        batch_size = next(iter(features.values())).size(0)

        # Collect features in the right order
        feature_tensors = []
        for name in self.feature_names:
            if name in features:
                feature_tensors.append(features[name])
            else:
                # If a feature is missing, add zeros
                feature_dim = self.feature_dims[name]
                feature_tensors.append(torch.zeros(batch_size, feature_dim, device=device))

        # If no features were found, return zeros
        if not feature_tensors:
            return torch.zeros(batch_size, self.output_dim, device=device)

        # Concatenate all features
        combined = torch.cat(feature_tensors, dim=1)

        # Debug dimensions
        print(f"Combined shape: {combined.shape}, Expected input dim: {self.total_dim}")

        # Create MLP on the fly with the correct input dimension
        if not hasattr(self, 'mlp') or self.mlp.state_dict()['0.weight'].size(1) != combined.size(1):
            input_dim = combined.size(1)
            self.mlp = nn.Sequential(
                nn.Linear(input_dim, self.hidden_dim * 2),
                nn.ReLU(),
                nn.Dropout(self.dropout),
                nn.Linear(self.hidden_dim * 2, self.hidden_dim),
                nn.ReLU(),
                nn.Dropout(self.dropout),
                nn.Linear(self.hidden_dim, self.output_dim)
            ).to(combined.device)
            print(f"Created new MLP with input dim: {input_dim}")

        # Process through MLP
        return self.mlp(combined)

    def get_feature_importance(self):
        """
        Get feature importance (equal weighting in this simple model).
        """
        return {name: 1.0 / len(self.feature_names) for name in self.feature_names}


class SimplePhyloGrowthModel(EnhancedPhyloGrowthModel):
    """
    Simplified model for microbial growth rate prediction.

    This model uses a direct approach without complex fusion mechanisms:
    1. Process each feature type independently
    2. Concatenate all features
    3. Process through a simple MLP

    This approach is more straightforward and may work better with limited data.
    """

    def __init__(self,
                 codon_input_dim,
                 pathway_input_dim=0,
                 heg_input_dim=0,
                 seq_input_dim=0,
                 taxonomy_vocab_sizes=None,
                 hidden_dim=256,
                 output_dim=2,
                 dropout=0.2,
                 use_pathway=False,
                 use_heg=False,
                 use_hierarchical_phylo=True,
                 use_seq=False):
        """
        Initialize the simplified model.

        Args:
            codon_input_dim: Dimension of codon metrics input
            pathway_input_dim: Dimension of pathway features input
            heg_input_dim: Dimension of HEG features input
            seq_input_dim: Dimension of sequence features input
            taxonomy_vocab_sizes: Dictionary mapping taxonomy levels to vocabulary sizes
            hidden_dim: Dimension of hidden layers
            output_dim: Dimension of output (typically 2 for growth rate and temperature)
            dropout: Dropout rate for regularization
            use_pathway: Whether to use pathway features
            use_heg: Whether to use HEG features
            use_hierarchical_phylo: Whether to use hierarchical phylogenetic features
            use_seq: Whether to use sequence features
        """
        # Store feature flags and dimensions as instance variables
        self.use_pathway = use_pathway
        self.use_heg = use_heg
        self.use_hierarchical_phylo = use_hierarchical_phylo
        self.use_seq = use_seq
        self.hidden_dim = hidden_dim
        # Call parent constructor to set up the basic structure
        super().__init__(
            codon_input_dim=codon_input_dim,
            pathway_input_dim=pathway_input_dim,
            heg_input_dim=heg_input_dim,
            seq_input_dim=seq_input_dim,
            taxonomy_vocab_sizes=taxonomy_vocab_sizes,
            hidden_dim=hidden_dim,
            output_dim=output_dim,
            dropout=dropout,
            use_pathway=use_pathway,
            use_heg=use_heg,
            use_hierarchical_phylo=use_hierarchical_phylo,
            use_seq=use_seq
        )

        # Track feature dimensions for the combiner
        feature_dims = {}

        # Always include codon metrics
        feature_dims['codon'] = hidden_dim

        # Add phylogenetic features if used
        if use_hierarchical_phylo:
            feature_dims['phylo'] = hidden_dim

        # Add pathway features if used
        if use_pathway:
            feature_dims['pathway'] = hidden_dim

        # Add HEG features if used
        if use_heg:
            feature_dims['heg'] = hidden_dim

        # Add sequence features if used
        if use_seq:
            feature_dims['seq'] = hidden_dim

        # Replace the feature fusion with our simple combiner
        self.feature_fusion = SimpleFeatureCombiner(
            feature_dims=feature_dims,
            hidden_dim=hidden_dim,
            output_dim=hidden_dim,
            dropout=dropout
        )

        # Remove residual blocks for simplicity
        self.residual_blocks = None

        # Simplified output layers - note we don't add extra dimension
        self.growth_output = nn.Linear(hidden_dim, 1)
        self.temp_output = nn.Linear(hidden_dim, 1)

        # Initialize weights
        self._init_weights()

    def _init_weights(self):
        """Initialize weights with Xavier initialization."""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.zeros_(m.bias)

    def forward(self, x):
        """
        Forward pass through the model.

        Args:
            x: Dictionary of input features

        Returns:
            Dictionary with growth rate and optimal temperature predictions
        """
        # Process features directly without batch size calculation

        # Process features
        features = {}

        # Process codon metrics
        if 'codon_metrics' in x:
            features['codon'] = self.codon_branch(x['codon_metrics'])

        # Process phylogenetic features
        if self.use_hierarchical_phylo:
            if 'taxonomy' in x and 'ko' in x:
                features['phylo'] = self.phylo_branch(x['taxonomy'], x['ko'])
        elif 'phylo_id' in x:
            # Traditional embedding approach
            phylo_id = x['phylo_id']
            features['phylo'] = self.phylo_transform(self.phylo_embed(phylo_id))

        # Process pathway features if used
        if self.use_pathway and 'pathway' in x:
            # Simple linear projection for pathway features
            if not hasattr(self, 'pathway_proj'):
                pathway_dim = x['pathway'].size(1)
                self.pathway_proj = nn.Linear(pathway_dim, self.hidden_dim).to(x['pathway'].device)
            features['pathway'] = self.pathway_proj(x['pathway'])

        # Process HEG features if used
        if self.use_heg and 'heg' in x:
            # HEG features are already processed in the dataset
            features['heg'] = x['heg'].unsqueeze(1)  # Add feature dimension

        # Process sequence features if used
        if self.use_seq and 'seq' in x:
            # Simple CNN for sequence features
            if not hasattr(self, 'seq_proj'):
                # Assuming seq is one-hot encoded (4 channels)
                self.seq_proj = nn.Sequential(
                    nn.Conv1d(4, 16, kernel_size=3, padding=1),
                    nn.ReLU(),
                    nn.AdaptiveAvgPool1d(1),
                    nn.Flatten(),
                    nn.Linear(16, self.hidden_dim)
                ).to(x['seq'].device)
            features['seq'] = self.seq_proj(x['seq'].float())

        # Combine features
        combined = self.feature_fusion(features)

        # Generate predictions
        growth_pred = self.growth_output(combined)
        temp_pred = self.temp_output(combined)

        # Squeeze to match expected shape [batch_size] instead of [batch_size, 1]
        return {
            'growth_rate': growth_pred.squeeze(-1),
            'optimal_temperature': temp_pred.squeeze(-1)
        }

    def get_feature_importance(self):
        """Get feature importance from the combiner."""
        if hasattr(self.feature_fusion, 'get_feature_importance'):
            return self.feature_fusion.get_feature_importance()
        return {}
