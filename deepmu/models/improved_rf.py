"""Improved Random Forest Model Module

This module implements an improved Random Forest model for growth rate prediction
that incorporates taxonomy information and entropy-based breakpoint features.
"""

import os
import numpy as np
import pandas as pd
import torch
import joblib
from typing import Dict, List, Optional, Tuple, Union, Any
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import r2_score, mean_squared_error
from sklearn.preprocessing import StandardScaler
from pathlib import Path

from ..utils.logging import get_logger

# Get logger
logger = get_logger()


class ImprovedRandomForest:
    """Improved Random Forest model for growth rate prediction.

    This class implements an improved Random Forest model that:
    1. Incorporates taxonomy information
    2. Uses entropy-based breakpoint features
    3. Emphasizes important codon features
    4. Provides detailed feature importance analysis
    """

    def __init__(
        self,
        n_estimators: int = 200,
        max_depth: Optional[int] = None,
        min_samples_split: int = 2,
        min_samples_leaf: int = 1,
        random_state: int = 42,
        n_jobs: int = -1
    ):
        """Initialize the improved Random Forest model.

        Args:
            n_estimators: Number of trees in the forest
            max_depth: Maximum depth of trees (None for unlimited)
            min_samples_split: Minimum samples required to split a node
            min_samples_leaf: Minimum samples required at a leaf node
            random_state: Random state for reproducibility
            n_jobs: Number of jobs to run in parallel (-1 for all cores)
        """
        self.n_estimators = n_estimators
        self.max_depth = max_depth
        self.min_samples_split = min_samples_split
        self.min_samples_leaf = min_samples_leaf
        self.random_state = random_state
        self.n_jobs = n_jobs

        # Initialize model
        self.model = RandomForestRegressor(
            n_estimators=n_estimators,
            max_depth=max_depth,
            min_samples_split=min_samples_split,
            min_samples_leaf=min_samples_leaf,
            random_state=random_state,
            n_jobs=n_jobs
        )

        # Initialize scaler
        self.scaler = StandardScaler()

        # Track if model is trained
        self.trained = False

        # Store feature importance
        self.feature_importance = None

        # Store feature names
        self.feature_names = None

        # Store metrics
        self.metrics = {
            'train_r2': 0.0,
            'train_rmse': 0.0,
            'val_r2': 0.0,
            'val_rmse': 0.0
        }

    def _prepare_features(self, features: np.ndarray) -> np.ndarray:
        """Prepare features for the model.

        Args:
            features: Feature array

        Returns:
            Processed feature array
        """
        # No additional processing needed as features are already prepared
        # during the feature extraction step
        return features

    def train(
        self,
        train_features: np.ndarray,
        train_targets: np.ndarray,
        feature_names: Optional[List[str]] = None,
        val_features: Optional[np.ndarray] = None,
        val_targets: Optional[np.ndarray] = None
    ) -> Dict[str, float]:
        """Train the Random Forest model.

        Args:
            train_features: Training feature array
            train_targets: Training target values
            feature_names: List of feature names (optional)
            val_features: Validation feature array (optional)
            val_targets: Validation target values (optional)

        Returns:
            Dictionary with training metrics
        """
        logger.info("Training Improved Random Forest model for growth rate prediction")

        # Prepare features
        X_train = self._prepare_features(train_features)

        # Store feature names
        if feature_names is not None:
            self.feature_names = feature_names
        else:
            self.feature_names = [f"feature_{i}" for i in range(X_train.shape[1])]

        # Scale features
        X_train_scaled = self.scaler.fit_transform(X_train)

        # Train model
        self.model.fit(X_train_scaled, train_targets)

        # Get feature importance
        self.feature_importance = self.model.feature_importances_

        # Calculate training metrics
        train_pred = self.model.predict(X_train_scaled)
        train_r2 = r2_score(train_targets, train_pred)
        train_rmse = np.sqrt(mean_squared_error(train_targets, train_pred))

        metrics = {
            'train_r2': train_r2,
            'train_rmse': train_rmse
        }

        # Store metrics
        self.metrics.update(metrics)

        # Calculate validation metrics if validation data is provided
        if val_features is not None and val_targets is not None:
            X_val = self._prepare_features(val_features)
            X_val_scaled = self.scaler.transform(X_val)

            val_pred = self.model.predict(X_val_scaled)
            val_r2 = r2_score(val_targets, val_pred)
            val_rmse = np.sqrt(mean_squared_error(val_targets, val_pred))

            val_metrics = {
                'val_r2': val_r2,
                'val_rmse': val_rmse
            }

            metrics.update(val_metrics)
            self.metrics.update(val_metrics)

        # Mark as trained
        self.trained = True

        return metrics

    def predict(self, features: np.ndarray) -> np.ndarray:
        """Predict growth rate.

        Args:
            features: Feature array

        Returns:
            Array of growth rate predictions
        """
        if not self.trained:
            raise RuntimeError("Model has not been trained yet")

        # Prepare features
        X = self._prepare_features(features)

        # Scale features
        X_scaled = self.scaler.transform(X)

        # Predict
        return self.model.predict(X_scaled)

    def predict_with_uncertainty(self, features: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """Predict growth rate with uncertainty.

        Args:
            features: Feature array

        Returns:
            Tuple of (predictions, uncertainties)
        """
        if not self.trained:
            raise RuntimeError("Model has not been trained yet")

        # Prepare features
        X = self._prepare_features(features)

        # Scale features
        X_scaled = self.scaler.transform(X)

        # Get predictions from all trees
        predictions = np.array([tree.predict(X_scaled) for tree in self.model.estimators_])

        # Calculate mean and standard deviation
        mean_predictions = np.mean(predictions, axis=0)
        std_predictions = np.std(predictions, axis=0)

        return mean_predictions, std_predictions

    def get_feature_importance(self) -> pd.DataFrame:
        """Get feature importance.

        Returns:
            DataFrame with feature names and importance values
        """
        if not self.trained:
            raise RuntimeError("Model has not been trained yet")

        if self.feature_names is None:
            feature_names = [f"feature_{i}" for i in range(len(self.feature_importance))]
        else:
            feature_names = self.feature_names

        # Create DataFrame
        importance_df = pd.DataFrame({
            'feature': feature_names,
            'importance': self.feature_importance
        })

        # Sort by importance
        importance_df = importance_df.sort_values('importance', ascending=False)

        return importance_df

    def get_metrics(self) -> Dict[str, float]:
        """Get model performance metrics.

        Returns:
            Dictionary with performance metrics
        """
        return self.metrics

    def save(self, directory: str):
        """Save model to disk.

        Args:
            directory: Directory to save model
        """
        os.makedirs(directory, exist_ok=True)

        # Save model
        model_path = os.path.join(directory, 'rf_model.joblib')
        joblib.dump(self.model, model_path)

        # Save scaler
        scaler_path = os.path.join(directory, 'rf_scaler.joblib')
        joblib.dump(self.scaler, scaler_path)

        # Save feature importance
        if self.feature_importance is not None:
            importance_path = os.path.join(directory, 'feature_importance.joblib')
            joblib.dump(self.feature_importance, importance_path)

        # Save feature names
        if self.feature_names is not None:
            names_path = os.path.join(directory, 'feature_names.joblib')
            joblib.dump(self.feature_names, names_path)

        # Save metrics
        metrics_path = os.path.join(directory, 'metrics.joblib')
        joblib.dump(self.metrics, metrics_path)

        logger.info(f"Model saved to {directory}")

    def load(self, directory: str):
        """Load model from disk.

        Args:
            directory: Directory to load model from
        """
        # Load model
        model_path = os.path.join(directory, 'rf_model.joblib')
        if os.path.exists(model_path):
            self.model = joblib.load(model_path)
            self.trained = True
        else:
            logger.warning(f"Model not found at {model_path}")

        # Load scaler
        scaler_path = os.path.join(directory, 'rf_scaler.joblib')
        if os.path.exists(scaler_path):
            self.scaler = joblib.load(scaler_path)
        else:
            logger.warning(f"Scaler not found at {scaler_path}")

        # Load feature importance
        importance_path = os.path.join(directory, 'feature_importance.joblib')
        if os.path.exists(importance_path):
            self.feature_importance = joblib.load(importance_path)

        # Load feature names
        names_path = os.path.join(directory, 'feature_names.joblib')
        if os.path.exists(names_path):
            self.feature_names = joblib.load(names_path)

        # Load metrics
        metrics_path = os.path.join(directory, 'metrics.joblib')
        if os.path.exists(metrics_path):
            self.metrics = joblib.load(metrics_path)

        logger.info(f"Model loaded from {directory}")


class ImprovedRandomForestTrainer:
    """Trainer for the ImprovedRandomForest model.

    This class handles the training process for the ImprovedRandomForest model,
    including data preparation and evaluation.
    """

    def __init__(
        self,
        model: ImprovedRandomForest
    ):
        """Initialize the trainer.

        Args:
            model: ImprovedRandomForest model
        """
        self.model = model

    def prepare_data(
        self,
        data: Dict[str, Dict[str, Any]],
        feature_names: Optional[List[str]] = None
    ) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray, Optional[List[str]]]:
        """Prepare data for training.

        Args:
            data: Dictionary with 'train' and 'test' data, each containing 'features' and 'targets'
            feature_names: List of feature names (optional)

        Returns:
            Tuple of (train_features, train_targets, test_features, test_targets, feature_names)
        """
        train_features = data['train']['features']
        train_targets = data['train']['targets']['growth_rate']

        test_features = data['test']['features']
        test_targets = data['test']['targets']['growth_rate']

        return train_features, train_targets, test_features, test_targets, feature_names

    def train(
        self,
        data: Dict[str, Dict[str, Any]],
        feature_names: Optional[List[str]] = None,
        output_dir: Optional[str] = None
    ) -> Dict[str, Any]:
        """Train the model.

        Args:
            data: Dictionary with 'train' and 'test' data, each containing 'features' and 'targets'
            feature_names: List of feature names (optional)
            output_dir: Directory to save model (optional)

        Returns:
            Dictionary with training results
        """
        # Prepare data
        train_features, train_targets, test_features, test_targets, feature_names = self.prepare_data(
            data, feature_names
        )

        # Train model
        metrics = self.model.train(
            train_features=train_features,
            train_targets=train_targets,
            feature_names=feature_names,
            val_features=test_features,
            val_targets=test_targets
        )

        # Save model if output_dir is provided
        if output_dir:
            self.model.save(output_dir)

        return {
            'metrics': metrics,
            'feature_importance': self.model.get_feature_importance().to_dict('records')
        }

    def evaluate(
        self,
        test_features: np.ndarray,
        test_targets: np.ndarray
    ) -> Dict[str, float]:
        """Evaluate the model.

        Args:
            test_features: Test feature array
            test_targets: Test target values

        Returns:
            Dictionary with evaluation metrics
        """
        # Predict
        predictions = self.model.predict(test_features)

        # Calculate metrics
        r2 = r2_score(test_targets, predictions)
        rmse = np.sqrt(mean_squared_error(test_targets, predictions))

        metrics = {
            'r2': r2,
            'rmse': rmse
        }

        return metrics
