"""
模型配置模块

这个模块定义了DeepMu模型架构的配置参数，
确保与预训练模型的兼容性。

配置说明：
- 所有参数都经过优化以获得最佳性能
- 参数设置与预训练模型保持一致
- 支持灵活的模型架构调整
- 便于实验和超参数调优
"""

# 与预训练模型匹配的默认分类学词汇表大小
DEFAULT_TAXONOMY_VOCAB_SIZES = {
    "phylum": 100,    # 门级别词汇表大小
    "class": 100,     # 纲级别词汇表大小
    "order": 100,     # 目级别词汇表大小
    "family": 100,    # 科级别词汇表大小
    "genus": 100      # 属级别词汇表大小
}

# 模型架构参数配置
MODEL_CONFIG = {
    # 系统发育相关参数
    "phylo_vocab_size": 10000,        # 系统发育词汇表大小
    "phylo_embed_dim": 128,           # 系统发育嵌入维度

    # 序列相关参数
    "seq_length": 1000,               # 最大序列长度
    "seq_feature_dim": 128,           # 序列特征维度

    # 模型架构参数
    "multi_task": True,               # 是否启用多任务学习
    "attention_heads": 4,             # 注意力头数量
    "attention_mode": "additive",     # 注意力整合模式
    "fusion_dim": 256,                # 特征融合层维度
    "final_feature_dim": 128,         # 最终特征维度
    "dropout": 0.3,                   # Dropout比率

    # 特征分支参数
    "codon_feature_dim": 32,          # 密码子特征维度
    "pathway_feature_dim": 64,        # 代谢途径特征维度
    "pathway_hidden_dim": 128,        # 代谢途径隐藏层维度
    "pathway_input_dim": 100,         # 代谢途径输入维度

    # 功能开关
    "use_pathway_branch": True,       # 是否使用代谢途径分支
    "use_heg_features": True,         # 是否使用HEG特征
    "use_hierarchical_phylo": True,   # 是否使用层次化系统发育

    # HEG相关参数
    "codon_metrics_size": 4,          # 密码子指标数量

    # 分类学相关参数
    "taxonomy_vocab_sizes": DEFAULT_TAXONOMY_VOCAB_SIZES,  # 分类学词汇表大小
    "ko_weight": 0.6                  # KO相似性权重
}