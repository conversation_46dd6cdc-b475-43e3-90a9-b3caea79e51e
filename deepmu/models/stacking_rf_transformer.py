"""Stacking Ensemble + Transformer Model Module

This module extends the hybrid RF-Transformer approach by replacing the Random Forest
for growth rate prediction with a stacking ensemble that combines:
1. Random Forest
2. XGBoost
3. LightGBM

The stacking ensemble provides improved growth rate predictions while maintaining
the Transformer architecture for optimal temperature prediction.
"""

import os
import numpy as np
import pandas as pd
import torch
import joblib
from typing import Dict, List, Optional, Tuple, Union, Any
from sklearn.ensemble import RandomForestRegressor, StackingRegressor
from sklearn.metrics import r2_score, mean_squared_error
import xgboost as xgb
import lightgbm as lgb

from ..utils.logging import get_logger
from .hybrid_rf_transformer import HybridRFTransformer, HybridRFTransformerTrainer

# Get logger
logger = get_logger()


class StackingRFTransformer(HybridRFTransformer):
    """Hybrid model combining Stacking Ensemble for growth rate and Transformer for temperature.
    
    This class extends the HybridRFTransformer by replacing the Random Forest with a stacking ensemble
    of Random Forest, XGBoost, and LightGBM for growth rate prediction, while maintaining the
    Transformer-based neural network for optimal temperature prediction.
    """
    
    def __init__(
        self,
        feature_dims: Dict[str, int],
        transformer_hidden_dims: List[int] = [256, 128, 64],
        transformer_dropout_rates: List[float] = [0.2, 0.3, 0.4],
        transformer_heads: int = 4,
        transformer_layers: int = 2,
        use_batch_norm: bool = True,
        use_residual: bool = True,
        use_positional_encoding: bool = True,
        rf_n_estimators: int = 200,
        rf_max_depth: int = 20,
        rf_min_samples_split: int = 2,
        rf_min_samples_leaf: int = 1,
        xgb_n_estimators: int = 300,
        xgb_learning_rate: float = 0.05,
        xgb_max_depth: int = 6,
        xgb_subsample: float = 0.8,
        xgb_colsample_bytree: float = 0.8,
        lgb_n_estimators: int = 300,
        lgb_learning_rate: float = 0.05,
        lgb_num_leaves: int = 31,
        lgb_subsample: float = 0.8,
        lgb_colsample_bytree: float = 0.8,
        device: str = 'cuda' if torch.cuda.is_available() else 'cpu'
    ):
        """Initialize the stacking ensemble hybrid model.
        
        Args:
            feature_dims: Dictionary mapping feature types to their dimensions
                          e.g., {'codon': 64, 'aa': 32, 'genomic': 128, ...}
            transformer_hidden_dims: List of hidden dimensions for transformer
            transformer_dropout_rates: List of dropout rates for transformer
            transformer_heads: Number of attention heads in transformer
            transformer_layers: Number of transformer layers
            use_batch_norm: Whether to use batch normalization in transformer
            use_residual: Whether to use residual connections in transformer
            use_positional_encoding: Whether to use positional encoding in transformer
            rf_n_estimators: Number of trees in random forest
            rf_max_depth: Maximum depth of trees in random forest
            rf_min_samples_split: Minimum samples required to split a node
            rf_min_samples_leaf: Minimum samples required at a leaf node
            xgb_n_estimators: Number of trees in XGBoost
            xgb_learning_rate: Learning rate for XGBoost
            xgb_max_depth: Maximum depth of trees in XGBoost
            xgb_subsample: Subsample ratio for XGBoost
            xgb_colsample_bytree: Column subsample ratio for XGBoost
            lgb_n_estimators: Number of trees in LightGBM
            lgb_learning_rate: Learning rate for LightGBM
            lgb_num_leaves: Number of leaves in LightGBM
            lgb_subsample: Subsample ratio for LightGBM
            lgb_colsample_bytree: Column subsample ratio for LightGBM
            device: Device to use for transformer model ('cuda' or 'cpu')
        """
        # Initialize parent class
        super().__init__(
            feature_dims=feature_dims,
            transformer_hidden_dims=transformer_hidden_dims,
            transformer_dropout_rates=transformer_dropout_rates,
            transformer_heads=transformer_heads,
            transformer_layers=transformer_layers,
            use_batch_norm=use_batch_norm,
            use_residual=use_residual,
            use_positional_encoding=use_positional_encoding,
            rf_n_estimators=rf_n_estimators,
            rf_max_depth=rf_max_depth,
            rf_min_samples_split=rf_min_samples_split,
            rf_min_samples_leaf=rf_min_samples_leaf,
            device=device
        )
        
        # Store additional parameters
        self.xgb_params = {
            'n_estimators': xgb_n_estimators,
            'learning_rate': xgb_learning_rate,
            'max_depth': xgb_max_depth,
            'subsample': xgb_subsample,
            'colsample_bytree': xgb_colsample_bytree,
            'random_state': 42
        }
        
        self.lgb_params = {
            'n_estimators': lgb_n_estimators,
            'learning_rate': lgb_learning_rate,
            'num_leaves': lgb_num_leaves,
            'subsample': lgb_subsample,
            'colsample_bytree': lgb_colsample_bytree,
            'random_state': 42
        }
        
        # Create base models for stacking
        rf_model = RandomForestRegressor(
            n_estimators=rf_n_estimators,
            max_depth=rf_max_depth,
            min_samples_split=rf_min_samples_split,
            min_samples_leaf=rf_min_samples_leaf,
            random_state=42,
            n_jobs=-1
        )
        
        xgb_model = xgb.XGBRegressor(
            n_estimators=xgb_n_estimators,
            learning_rate=xgb_learning_rate,
            max_depth=xgb_max_depth,
            subsample=xgb_subsample,
            colsample_bytree=xgb_colsample_bytree,
            random_state=42,
            n_jobs=-1
        )
        
        lgb_model = lgb.LGBMRegressor(
            n_estimators=lgb_n_estimators,
            learning_rate=lgb_learning_rate,
            num_leaves=lgb_num_leaves,
            subsample=lgb_subsample,
            colsample_bytree=lgb_colsample_bytree,
            random_state=42,
            n_jobs=-1
        )
        
        # Create meta-model (Random Forest)
        meta_model = RandomForestRegressor(
            n_estimators=100,
            max_depth=None,
            random_state=42,
            n_jobs=-1
        )
        
        # Create stacking ensemble
        self.stacking_model = StackingRegressor(
            estimators=[
                ('rf', rf_model),
                ('xgb', xgb_model),
                ('lgb', lgb_model)
            ],
            final_estimator=meta_model,
            cv=5,
            n_jobs=-1
        )
        
        # Replace the RF model with the stacking ensemble
        self.rf_model = self.stacking_model
        
        # Store model types for logging
        self.model_types = {
            'growth_rate': 'Stacking Ensemble (RF + XGBoost + LightGBM)',
            'temperature': 'Transformer'
        }
        
        logger.info("Initialized StackingRFTransformer with stacking ensemble for growth rate prediction")
    
    def train_rf(
        self,
        train_features: Dict[str, np.ndarray],
        train_targets: Dict[str, np.ndarray],
        val_features: Optional[Dict[str, np.ndarray]] = None,
        val_targets: Optional[Dict[str, np.ndarray]] = None
    ) -> Dict[str, float]:
        """Train the stacking ensemble for growth rate prediction.
        
        Args:
            train_features: Dictionary mapping feature types to training feature arrays
            train_targets: Dictionary with 'growth_rate' key mapping to training targets
            val_features: Dictionary mapping feature types to validation feature arrays
            val_targets: Dictionary with 'growth_rate' key mapping to validation targets
            
        Returns:
            Dictionary with training metrics
        """
        logger.info("Training stacking ensemble for growth rate prediction")
        
        # Prepare features for stacking ensemble
        X_train = self._prepare_rf_features(train_features)
        y_train = train_targets['growth_rate']
        
        # Train stacking ensemble
        self.stacking_model.fit(X_train, y_train)
        
        # Get feature importance from the meta-model
        # Note: Stacking ensemble doesn't have feature_importances_ directly
        # We'll use the meta-model's feature importances instead
        try:
            self.feature_importance = self.stacking_model.final_estimator_.feature_importances_
        except:
            # If meta-model doesn't have feature_importances_, use the first base model
            logger.warning("Meta-model doesn't have feature_importances_, using first base model instead")
            self.feature_importance = self.stacking_model.estimators_[0].feature_importances_
        
        # Calculate training metrics
        train_pred = self.stacking_model.predict(X_train)
        train_r2 = r2_score(y_train, train_pred)
        train_rmse = np.sqrt(mean_squared_error(y_train, train_pred))
        
        metrics = {
            'train_r2': train_r2,
            'train_rmse': train_rmse
        }
        
        # Calculate validation metrics if validation data is provided
        if val_features is not None and val_targets is not None:
            X_val = self._prepare_rf_features(val_features)
            y_val = val_targets['growth_rate']
            
            val_pred = self.stacking_model.predict(X_val)
            val_r2 = r2_score(y_val, val_pred)
            val_rmse = np.sqrt(mean_squared_error(y_val, val_pred))
            
            metrics.update({
                'val_r2': val_r2,
                'val_rmse': val_rmse
            })
            
            # Store metrics
            self.metrics['growth_rate_r2'] = val_r2
            self.metrics['growth_rate_rmse'] = val_rmse
        
        # Mark as trained
        self.rf_trained = True
        
        return metrics
    
    def save(self, directory: str):
        """Save both models to disk.
        
        Args:
            directory: Directory to save models
        """
        os.makedirs(directory, exist_ok=True)
        
        # Save Stacking Ensemble
        stacking_path = os.path.join(directory, 'stacking_model.joblib')
        joblib.dump(self.stacking_model, stacking_path)
        
        # Save Transformer model
        transformer_path = os.path.join(directory, 'transformer_model.pt')
        torch.save(self.transformer_model.state_dict(), transformer_path)
        
        # Save metrics
        metrics_path = os.path.join(directory, 'metrics.joblib')
        joblib.dump(self.metrics, metrics_path)
        
        # Save feature importance
        if self.feature_importance is not None:
            importance_path = os.path.join(directory, 'feature_importance.joblib')
            joblib.dump(self.feature_importance, importance_path)
        
        # Save model parameters
        params = {
            'feature_dims': self.feature_dims,
            'model_types': self.model_types,
            'xgb_params': self.xgb_params,
            'lgb_params': self.lgb_params
        }
        params_path = os.path.join(directory, 'model_params.joblib')
        joblib.dump(params, params_path)
        
        logger.info(f"Models saved to {directory}")
    
    def load(self, directory: str):
        """Load both models from disk.
        
        Args:
            directory: Directory to load models from
        """
        # Load Stacking Ensemble
        stacking_path = os.path.join(directory, 'stacking_model.joblib')
        if os.path.exists(stacking_path):
            self.stacking_model = joblib.load(stacking_path)
            self.rf_model = self.stacking_model  # Update reference
            self.rf_trained = True
        else:
            logger.warning(f"Stacking ensemble model not found at {stacking_path}")
        
        # Load Transformer model
        transformer_path = os.path.join(directory, 'transformer_model.pt')
        if os.path.exists(transformer_path):
            self.transformer_model.load_state_dict(torch.load(transformer_path, map_location=self.device))
            self.transformer_trained = True
        else:
            logger.warning(f"Transformer model not found at {transformer_path}")
        
        # Load metrics
        metrics_path = os.path.join(directory, 'metrics.joblib')
        if os.path.exists(metrics_path):
            self.metrics = joblib.load(metrics_path)
        
        # Load feature importance
        importance_path = os.path.join(directory, 'feature_importance.joblib')
        if os.path.exists(importance_path):
            self.feature_importance = joblib.load(importance_path)
        
        # Load model parameters
        params_path = os.path.join(directory, 'model_params.joblib')
        if os.path.exists(params_path):
            params = joblib.load(params_path)
            if 'model_types' in params:
                self.model_types = params['model_types']
            if 'xgb_params' in params:
                self.xgb_params = params['xgb_params']
            if 'lgb_params' in params:
                self.lgb_params = params['lgb_params']
        
        logger.info(f"Models loaded from {directory}")
    
    def get_base_model_importances(self) -> Dict[str, pd.DataFrame]:
        """Get feature importance from each base model in the stacking ensemble.
        
        Returns:
            Dictionary mapping model names to feature importance DataFrames
        """
        if not self.rf_trained:
            raise RuntimeError("Stacking ensemble has not been trained yet")
        
        importances = {}
        feature_names = [f"feature_{i}" for i in range(self.feature_importance.shape[0])]
        
        # Try to get feature importances from each base model
        for name, model in self.stacking_model.estimators_:
            try:
                if hasattr(model, 'feature_importances_'):
                    imp_df = pd.DataFrame({
                        'feature': feature_names,
                        'importance': model.feature_importances_
                    }).sort_values('importance', ascending=False)
                    importances[name] = imp_df
            except:
                logger.warning(f"Could not get feature importances for {name} model")
        
        # Add meta-model importances
        try:
            if hasattr(self.stacking_model.final_estimator_, 'feature_importances_'):
                meta_imp_df = pd.DataFrame({
                    'feature': [f"model_{i}" for i in range(len(self.stacking_model.estimators_))],
                    'importance': self.stacking_model.final_estimator_.feature_importances_
                }).sort_values('importance', ascending=False)
                importances['meta_model'] = meta_imp_df
        except:
            logger.warning("Could not get feature importances for meta-model")
        
        return importances


class StackingRFTransformerTrainer(HybridRFTransformerTrainer):
    """Trainer for the StackingRFTransformer model.
    
    This class extends the HybridRFTransformerTrainer to handle the training process
    for the stacking ensemble and Transformer components of the hybrid model.
    """
    
    def __init__(
        self,
        model: StackingRFTransformer,
        learning_rate: float = 0.001,
        weight_decay: float = 1e-5,
        device: str = 'cuda' if torch.cuda.is_available() else 'cpu'
    ):
        """Initialize the trainer.
        
        Args:
            model: StackingRFTransformer model
            learning_rate: Learning rate for transformer optimizer
            weight_decay: Weight decay for regularization
            device: Device to use for training ('cuda' or 'cpu')
        """
        super().__init__(
            model=model,
            learning_rate=learning_rate,
            weight_decay=weight_decay,
            device=device
        )
        
        # Store model reference with correct type
        self.model = model
    
    def evaluate(
        self,
        test_features: Dict[str, np.ndarray],
        test_targets: Dict[str, np.ndarray]
    ) -> Dict[str, float]:
        """Evaluate the hybrid model.
        
        Args:
            test_features: Dictionary mapping feature types to test feature arrays
            test_targets: Dictionary with 'growth_rate' and 'temperature' keys mapping to test targets
            
        Returns:
            Dictionary with evaluation metrics
        """
        # Use parent class evaluation
        metrics = super().evaluate(test_features, test_targets)
        
        # Add model type information
        metrics['model_types'] = self.model.model_types
        
        # Get base model importances
        try:
            base_importances = self.model.get_base_model_importances()
            
            # Add meta-model weights to metrics
            if 'meta_model' in base_importances:
                meta_weights = base_importances['meta_model']
                metrics['meta_model_weights'] = meta_weights.to_dict('records')
        except Exception as e:
            logger.warning(f"Could not get base model importances: {str(e)}")
        
        return metrics


def create_stacking_rf_transformer(
    feature_dims: Dict[str, int],
    transformer_hidden_dims: List[int] = [256, 128, 64],
    transformer_dropout_rates: List[float] = [0.2, 0.3, 0.4],
    transformer_heads: int = 4,
    transformer_layers: int = 2,
    use_batch_norm: bool = True,
    use_residual: bool = True,
    use_positional_encoding: bool = True,
    rf_n_estimators: int = 200,
    rf_max_depth: int = 20,
    rf_min_samples_split: int = 2,
    rf_min_samples_leaf: int = 1,
    xgb_n_estimators: int = 300,
    xgb_learning_rate: float = 0.05,
    xgb_max_depth: int = 6,
    xgb_subsample: float = 0.8,
    xgb_colsample_bytree: float = 0.8,
    lgb_n_estimators: int = 300,
    lgb_learning_rate: float = 0.05,
    lgb_num_leaves: int = 31,
    lgb_subsample: float = 0.8,
    lgb_colsample_bytree: float = 0.8,
    device: str = 'cuda' if torch.cuda.is_available() else 'cpu'
) -> StackingRFTransformer:
    """Create a stacking ensemble hybrid model.
    
    Args:
        feature_dims: Dictionary mapping feature types to their dimensions
        transformer_hidden_dims: List of hidden dimensions for transformer
        transformer_dropout_rates: List of dropout rates for transformer
        transformer_heads: Number of attention heads in transformer
        transformer_layers: Number of transformer layers
        use_batch_norm: Whether to use batch normalization in transformer
        use_residual: Whether to use residual connections in transformer
        use_positional_encoding: Whether to use positional encoding in transformer
        rf_n_estimators: Number of trees in random forest
        rf_max_depth: Maximum depth of trees in random forest
        rf_min_samples_split: Minimum samples required to split a node
        rf_min_samples_leaf: Minimum samples required at a leaf node
        xgb_n_estimators: Number of trees in XGBoost
        xgb_learning_rate: Learning rate for XGBoost
        xgb_max_depth: Maximum depth of trees in XGBoost
        xgb_subsample: Subsample ratio for XGBoost
        xgb_colsample_bytree: Column subsample ratio for XGBoost
        lgb_n_estimators: Number of trees in LightGBM
        lgb_learning_rate: Learning rate for LightGBM
        lgb_num_leaves: Number of leaves in LightGBM
        lgb_subsample: Subsample ratio for LightGBM
        lgb_colsample_bytree: Column subsample ratio for LightGBM
        device: Device to use for transformer model ('cuda' or 'cpu')
        
    Returns:
        StackingRFTransformer
    """
    return StackingRFTransformer(
        feature_dims=feature_dims,
        transformer_hidden_dims=transformer_hidden_dims,
        transformer_dropout_rates=transformer_dropout_rates,
        transformer_heads=transformer_heads,
        transformer_layers=transformer_layers,
        use_batch_norm=use_batch_norm,
        use_residual=use_residual,
        use_positional_encoding=use_positional_encoding,
        rf_n_estimators=rf_n_estimators,
        rf_max_depth=rf_max_depth,
        rf_min_samples_split=rf_min_samples_split,
        rf_min_samples_leaf=rf_min_samples_leaf,
        xgb_n_estimators=xgb_n_estimators,
        xgb_learning_rate=xgb_learning_rate,
        xgb_max_depth=xgb_max_depth,
        xgb_subsample=xgb_subsample,
        xgb_colsample_bytree=xgb_colsample_bytree,
        lgb_n_estimators=lgb_n_estimators,
        lgb_learning_rate=lgb_learning_rate,
        lgb_num_leaves=lgb_num_leaves,
        lgb_subsample=lgb_subsample,
        lgb_colsample_bytree=lgb_colsample_bytree,
        device=device
    )
