"""
Improved hybrid model for DeepMu combining Random Forest for growth rate and neural network for temperature.

This module provides an improved hybrid model architecture that uses a Random Forest for growth rate
prediction and a neural network for optimal temperature prediction, with better feature normalization
and hyperparameter tuning.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
import logging
from typing import Dict, List, Tuple, Optional, Union, Any

# Setup logging
logger = logging.getLogger(__name__)

class ImprovedHybridModel:
    """
    Improved hybrid model with Random Forest for growth rate and neural network for optimal temperature.

    This model uses a Random Forest for growth rate prediction and a neural network for
    optimal temperature prediction, with better feature normalization and hyperparameter tuning.
    """

    def __init__(
        self,
        growth_rate_features: List[str],
        optimal_temperature_features: List[str],
        hidden_dim: int = 128,
        dropout: float = 0.3,
        num_layers: int = 2,
        use_batch_norm: bool = True,
        n_estimators: int = 200,
        max_depth: int = 15,
        min_samples_split: int = 5,
        min_samples_leaf: int = 2,
        device: torch.device = None
    ):
        """
        Initialize the improved hybrid model.

        Args:
            growth_rate_features: List of feature names for growth rate prediction
            optimal_temperature_features: List of feature names for optimal temperature prediction
            hidden_dim: Dimension of hidden layers for neural network
            dropout: Dropout probability for neural network
            num_layers: Number of hidden layers for neural network
            use_batch_norm: Whether to use batch normalization for neural network
            n_estimators: Number of trees in the Random Forest
            max_depth: Maximum depth of trees in the Random Forest
            min_samples_split: Minimum samples required to split a node in Random Forest
            min_samples_leaf: Minimum samples required in a leaf node in Random Forest
            device: Device to use for neural network (cpu or cuda)
        """
        self.growth_rate_features = growth_rate_features
        self.optimal_temperature_features = optimal_temperature_features
        self.all_features = list(set(growth_rate_features + optimal_temperature_features))
        self.feature_to_idx = {feature: i for i, feature in enumerate(self.all_features)}
        self.device = device if device is not None else torch.device('cuda' if torch.cuda.is_available() else 'cpu')

        # Create indices for each feature set
        self.growth_rate_indices = [self.feature_to_idx[f] for f in growth_rate_features]
        self.optimal_temperature_indices = [self.feature_to_idx[f] for f in optimal_temperature_features]

        # Calculate input dimensions
        self.growth_rate_input_dim = len(growth_rate_features)
        self.optimal_temperature_input_dim = len(optimal_temperature_features)

        # Create feature scalers
        self.growth_rate_scaler = StandardScaler()
        self.optimal_temperature_scaler = StandardScaler()

        # Create Random Forest for growth rate prediction
        self.growth_rate_model = RandomForestRegressor(
            n_estimators=n_estimators,
            max_depth=max_depth,
            min_samples_split=min_samples_split,
            min_samples_leaf=min_samples_leaf,
            random_state=42,
            n_jobs=-1  # Use all available cores
        )

        # Create neural network for optimal temperature prediction
        self.optimal_temperature_model = ImprovedTemperatureNN(
            input_dim=self.optimal_temperature_input_dim,
            hidden_dim=hidden_dim,
            output_dim=1,
            num_layers=num_layers,
            dropout=dropout,
            use_batch_norm=use_batch_norm
        ).to(self.device)

        # Create optimizer for neural network
        self.optimizer = torch.optim.Adam(
            self.optimal_temperature_model.parameters(),
            lr=0.001,
            weight_decay=1e-5
        )

        # Create loss function for neural network
        self.criterion = nn.MSELoss()

        # Initialize training state
        self.is_fitted = False

        logger.info(f"Initialized ImprovedHybridModel with {len(growth_rate_features)} growth rate features and {len(optimal_temperature_features)} optimal temperature features")

    def train(self, dataloader, num_epochs=50):
        """
        Train the improved hybrid model.

        Args:
            dataloader: DataLoader for training data
            num_epochs: Number of epochs to train the neural network
        """
        # Collect data for Random Forest
        X_growth = []
        y_growth = []
        
        # Collect data for neural network
        X_temp = []
        y_temp = []
        
        # First pass: collect data for both models
        logger.info("Collecting data for training...")
        for features, targets in dataloader:
            # Extract feature values for growth rate
            growth_features = self._extract_features(features, self.growth_rate_features)
            X_growth.extend(growth_features)
            
            # Extract growth rate targets
            if 'growth_rate' in targets:
                y_growth.extend(targets['growth_rate'].detach().cpu().numpy())
            
            # Extract feature values for optimal temperature
            temp_features = self._extract_features(features, self.optimal_temperature_features)
            X_temp.extend(temp_features)
            
            # Extract optimal temperature targets
            if 'optimal_temperature' in targets:
                y_temp.extend(targets['optimal_temperature'].detach().cpu().numpy())
        
        # Convert to numpy arrays
        X_growth = np.array(X_growth)
        y_growth = np.array(y_growth)
        X_temp = np.array(X_temp)
        y_temp = np.array(y_temp)
        
        # Fit scalers
        logger.info("Fitting feature scalers...")
        self.growth_rate_scaler.fit(X_growth)
        self.optimal_temperature_scaler.fit(X_temp)
        
        # Scale features
        X_growth_scaled = self.growth_rate_scaler.transform(X_growth)
        X_temp_scaled = self.optimal_temperature_scaler.transform(X_temp)
        
        # Train Random Forest
        logger.info(f"Training Random Forest with {len(X_growth_scaled)} samples...")
        self.growth_rate_model.fit(X_growth_scaled, y_growth)
        
        # Convert to PyTorch tensors for neural network
        X_temp_tensor = torch.tensor(X_temp_scaled, dtype=torch.float32).to(self.device)
        y_temp_tensor = torch.tensor(y_temp, dtype=torch.float32).to(self.device)
        
        # Create TensorDataset
        temp_dataset = torch.utils.data.TensorDataset(X_temp_tensor, y_temp_tensor)
        
        # Create DataLoader
        temp_loader = torch.utils.data.DataLoader(
            temp_dataset,
            batch_size=32,
            shuffle=True
        )
        
        # Train neural network
        logger.info(f"Training neural network for {num_epochs} epochs...")
        self.optimal_temperature_model.train()
        
        for epoch in range(num_epochs):
            epoch_loss = 0.0
            num_batches = 0
            
            for X_batch, y_batch in temp_loader:
                # Forward pass
                self.optimizer.zero_grad()
                outputs = self.optimal_temperature_model(X_batch)
                loss = self.criterion(outputs.squeeze(), y_batch)
                
                # Backward pass and optimize
                loss.backward()
                self.optimizer.step()
                
                epoch_loss += loss.item()
                num_batches += 1
            
            # Log progress
            if num_batches > 0:
                logger.info(f"Epoch {epoch+1}/{num_epochs}, Loss: {epoch_loss/num_batches:.6f}")
        
        # Mark as fitted
        self.is_fitted = True
    
    def evaluate(self, dataloader):
        """
        Evaluate the improved hybrid model.

        Args:
            dataloader: DataLoader for evaluation data

        Returns:
            Dictionary of evaluation metrics
        """
        if not self.is_fitted:
            logger.warning("Model is not fitted yet. Call train() first.")
            return {}
        
        # Collect predictions and targets
        growth_preds = []
        growth_targets = []
        temp_preds = []
        temp_targets = []
        
        # Set neural network to evaluation mode
        self.optimal_temperature_model.eval()
        
        with torch.no_grad():
            for features, targets in dataloader:
                # Make predictions
                predictions = self.predict_batch(features)
                
                # Collect growth rate predictions and targets
                if 'growth_rate' in targets and 'growth_rate' in predictions:
                    growth_preds.extend(predictions['growth_rate'])
                    growth_targets.extend(targets['growth_rate'].detach().cpu().numpy())
                
                # Collect optimal temperature predictions and targets
                if 'optimal_temperature' in targets and 'optimal_temperature' in predictions:
                    temp_preds.extend(predictions['optimal_temperature'])
                    temp_targets.extend(targets['optimal_temperature'].detach().cpu().numpy())
        
        # Calculate metrics
        metrics = {}
        
        if growth_preds and growth_targets:
            growth_preds = np.array(growth_preds)
            growth_targets = np.array(growth_targets)
            
            # Calculate MSE
            growth_mse = np.mean((growth_preds - growth_targets) ** 2)
            metrics['growth_rate_mse'] = growth_mse
            
            # Calculate R²
            growth_r2 = 1 - np.sum((growth_preds - growth_targets) ** 2) / np.sum((growth_targets - np.mean(growth_targets)) ** 2)
            metrics['growth_rate_r2'] = growth_r2
            
            # Store predictions and targets
            metrics['growth_rate_preds'] = growth_preds.tolist()
            metrics['growth_rate_targets'] = growth_targets.tolist()
        
        if temp_preds and temp_targets:
            temp_preds = np.array(temp_preds)
            temp_targets = np.array(temp_targets)
            
            # Calculate MSE
            temp_mse = np.mean((temp_preds - temp_targets) ** 2)
            metrics['optimal_temperature_mse'] = temp_mse
            
            # Calculate R²
            temp_r2 = 1 - np.sum((temp_preds - temp_targets) ** 2) / np.sum((temp_targets - np.mean(temp_targets)) ** 2)
            metrics['optimal_temperature_r2'] = temp_r2
            
            # Store predictions and targets
            metrics['optimal_temperature_preds'] = temp_preds.tolist()
            metrics['optimal_temperature_targets'] = temp_targets.tolist()
        
        return metrics
    
    def predict_batch(self, features):
        """
        Make predictions for a batch of features.

        Args:
            features: Dictionary of feature tensors

        Returns:
            Dictionary of predictions
        """
        if not self.is_fitted:
            logger.warning("Model is not fitted yet. Call train() first.")
            return {}
        
        # Extract feature values
        growth_features = self._extract_features(features, self.growth_rate_features)
        temp_features = self._extract_features(features, self.optimal_temperature_features)
        
        # Make predictions
        predictions = {}
        
        # Growth rate predictions (Random Forest)
        if growth_features.shape[0] > 0:
            # Scale features
            growth_features_scaled = self.growth_rate_scaler.transform(growth_features)
            
            # Make predictions
            growth_preds = self.growth_rate_model.predict(growth_features_scaled)
            predictions['growth_rate'] = growth_preds
        
        # Optimal temperature predictions (neural network)
        if temp_features.shape[0] > 0:
            # Scale features
            temp_features_scaled = self.optimal_temperature_scaler.transform(temp_features)
            
            # Convert to tensor
            temp_features_tensor = torch.tensor(temp_features_scaled, dtype=torch.float32).to(self.device)
            
            # Make predictions
            self.optimal_temperature_model.eval()
            with torch.no_grad():
                temp_preds = self.optimal_temperature_model(temp_features_tensor).squeeze().detach().cpu().numpy()
            
            # Handle single prediction case
            if temp_features.shape[0] == 1:
                temp_preds = np.array([temp_preds])
            
            predictions['optimal_temperature'] = temp_preds
        
        return predictions
    
    def _extract_features(self, features_dict, feature_list):
        """
        Extract feature values from a dictionary of features.

        Args:
            features_dict: Dictionary of feature tensors
            feature_list: List of feature names to extract

        Returns:
            Numpy array of feature vectors
        """
        # Get batch size
        batch_size = len(next(iter(features_dict.values())))
        
        # Create feature matrix
        feature_matrix = np.zeros((batch_size, len(feature_list)))
        
        # Fill feature matrix
        for i, feature_name in enumerate(feature_list):
            if feature_name in features_dict:
                feature_value = features_dict[feature_name]
                if isinstance(feature_value, torch.Tensor):
                    feature_value = feature_value.detach().cpu().numpy()
                feature_matrix[:, i] = feature_value
        
        return feature_matrix
    
    def save(self, path):
        """
        Save the improved hybrid model.

        Args:
            path: Path to save the model
        """
        import joblib
        import os
        
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(path), exist_ok=True)
        
        # Save Random Forest
        joblib.dump(self.growth_rate_model, f"{path}_rf.joblib")
        
        # Save neural network
        torch.save(self.optimal_temperature_model.state_dict(), f"{path}_nn.pt")
        
        # Save feature lists and scalers
        joblib.dump({
            'growth_rate_features': self.growth_rate_features,
            'optimal_temperature_features': self.optimal_temperature_features,
            'growth_rate_scaler': self.growth_rate_scaler,
            'optimal_temperature_scaler': self.optimal_temperature_scaler,
            'is_fitted': self.is_fitted
        }, f"{path}_features.joblib")
        
        logger.info(f"Model saved to {path}")
    
    def load(self, path):
        """
        Load the improved hybrid model.

        Args:
            path: Path to load the model from
        """
        import joblib
        
        # Load Random Forest
        self.growth_rate_model = joblib.load(f"{path}_rf.joblib")
        
        # Load neural network
        self.optimal_temperature_model.load_state_dict(torch.load(f"{path}_nn.pt", map_location=self.device))
        
        # Load feature lists and scalers
        features = joblib.load(f"{path}_features.joblib")
        self.growth_rate_features = features['growth_rate_features']
        self.optimal_temperature_features = features['optimal_temperature_features']
        self.growth_rate_scaler = features['growth_rate_scaler']
        self.optimal_temperature_scaler = features['optimal_temperature_scaler']
        self.is_fitted = features['is_fitted']
        
        # Update indices
        self.all_features = list(set(self.growth_rate_features + self.optimal_temperature_features))
        self.feature_to_idx = {feature: i for i, feature in enumerate(self.all_features)}
        self.growth_rate_indices = [self.feature_to_idx[f] for f in self.growth_rate_features]
        self.optimal_temperature_indices = [self.feature_to_idx[f] for f in self.optimal_temperature_features]
        
        logger.info(f"Model loaded from {path}")


class ImprovedTemperatureNN(nn.Module):
    """
    Improved neural network for optimal temperature prediction.
    """
    
    def __init__(
        self,
        input_dim: int,
        hidden_dim: int,
        output_dim: int,
        num_layers: int,
        dropout: float,
        use_batch_norm: bool
    ):
        """
        Initialize the neural network.

        Args:
            input_dim: Input dimension
            hidden_dim: Hidden dimension
            output_dim: Output dimension
            num_layers: Number of hidden layers
            dropout: Dropout probability
            use_batch_norm: Whether to use batch normalization
        """
        super().__init__()
        
        layers = []
        
        # Input layer
        if use_batch_norm:
            layers.append(nn.Linear(input_dim, hidden_dim))
            layers.append(nn.BatchNorm1d(hidden_dim))
            layers.append(nn.ReLU())
            layers.append(nn.Dropout(dropout))
        else:
            layers.append(nn.Linear(input_dim, hidden_dim))
            layers.append(nn.ReLU())
            layers.append(nn.Dropout(dropout))
        
        # Hidden layers
        for _ in range(num_layers - 1):
            if use_batch_norm:
                layers.append(nn.Linear(hidden_dim, hidden_dim))
                layers.append(nn.BatchNorm1d(hidden_dim))
                layers.append(nn.ReLU())
                layers.append(nn.Dropout(dropout))
            else:
                layers.append(nn.Linear(hidden_dim, hidden_dim))
                layers.append(nn.ReLU())
                layers.append(nn.Dropout(dropout))
        
        # Output layer
        layers.append(nn.Linear(hidden_dim, output_dim))
        
        self.model = nn.Sequential(*layers)
    
    def forward(self, x):
        """
        Forward pass through the neural network.

        Args:
            x: Input tensor

        Returns:
            Output tensor
        """
        return self.model(x)


def create_improved_hybrid_model(
    growth_rate_features: List[str],
    optimal_temperature_features: List[str],
    hidden_dim: int = 128,
    dropout: float = 0.3,
    num_layers: int = 2,
    use_batch_norm: bool = True,
    n_estimators: int = 200,
    max_depth: int = 15,
    min_samples_split: int = 5,
    min_samples_leaf: int = 2,
    device: torch.device = None
) -> ImprovedHybridModel:
    """
    Create an improved hybrid model with Random Forest for growth rate and neural network for temperature.

    Args:
        growth_rate_features: List of feature names for growth rate prediction
        optimal_temperature_features: List of feature names for optimal temperature prediction
        hidden_dim: Dimension of hidden layers for neural network
        dropout: Dropout probability for neural network
        num_layers: Number of hidden layers for neural network
        use_batch_norm: Whether to use batch normalization for neural network
        n_estimators: Number of trees in the Random Forest
        max_depth: Maximum depth of trees in the Random Forest
        min_samples_split: Minimum samples required to split a node in Random Forest
        min_samples_leaf: Minimum samples required in a leaf node in Random Forest
        device: Device to use for neural network (cpu or cuda)

    Returns:
        ImprovedHybridModel
    """
    return ImprovedHybridModel(
        growth_rate_features=growth_rate_features,
        optimal_temperature_features=optimal_temperature_features,
        hidden_dim=hidden_dim,
        dropout=dropout,
        num_layers=num_layers,
        use_batch_norm=use_batch_norm,
        n_estimators=n_estimators,
        max_depth=max_depth,
        min_samples_split=min_samples_split,
        min_samples_leaf=min_samples_leaf,
        device=device
    )
