"""
Reinforcement Learning with Verifiable Rewards (RLVR) for growth rate prediction.

This module implements RLVR for microbial growth rate prediction, enhancing
existing models by incorporating reinforcement learning techniques with
carefully designed reward functions based on prediction accuracy.
"""

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from sklearn.base import BaseEstimator
from sklearn.metrics import r2_score, mean_squared_error
from typing import Dict, List, Tuple, Optional
import logging

# Setup logging
logger = logging.getLogger(__name__)

class VerifiableRewardFunction:
    """
    Reward function for RLVR that verifies prediction accuracy.

    This class defines a reward function that combines multiple components:
    1. Accuracy: How close the prediction is to the true value
    2. Consistency: How consistent the prediction is with a baseline model
    3. Improvement: How much the prediction improves over the baseline
    4. Calibration: How well-calibrated the prediction uncertainty is

    The reward function is designed to guide the RL agent toward making
    accurate predictions while maintaining consistency with established models.
    """

    def __init__(
        self,
        baseline_model: BaseEstimator,
        alpha: float = 0.6,
        beta: float = 0.3,
        gamma: float = 0.1,
        accuracy_scale: float = 1.0,
        use_r2_component: bool = True,
        use_improvement_component: bool = True,
        clip_rewards: bool = True,
        reward_clip_range: Tuple[float, float] = (-1.0, 1.0)
    ):
        """
        Initialize the verifiable reward function.

        Args:
            baseline_model: A pre-trained model to compare predictions against
            alpha: Weight for accuracy component (default: 0.6)
            beta: Weight for consistency component (default: 0.3)
            gamma: Weight for improvement component (default: 0.1)
            accuracy_scale: Scaling factor for accuracy component (default: 1.0)
            use_r2_component: Whether to include R² in accuracy component (default: True)
            use_improvement_component: Whether to include improvement over baseline (default: True)
            clip_rewards: Whether to clip rewards to a specific range (default: True)
            reward_clip_range: Range to clip rewards to (default: (-1.0, 1.0))
        """
        self.baseline = baseline_model
        self.alpha = alpha
        self.beta = beta
        self.gamma = gamma
        self.accuracy_scale = accuracy_scale
        self.use_r2_component = use_r2_component
        self.use_improvement_component = use_improvement_component
        self.clip_rewards = clip_rewards
        self.reward_clip_range = reward_clip_range

        # Track historical performance for adaptive scaling
        self.reward_history = []
        self.baseline_errors = []

        logger.info(f"Initialized VerifiableRewardFunction with weights: "
                   f"alpha={alpha}, beta={beta}, gamma={gamma}")

    def __call__(self, y_true: np.ndarray, y_pred: np.ndarray, features: Optional[np.ndarray] = None) -> np.ndarray:
        """
        Calculate rewards based on prediction accuracy and consistency.

        Args:
            y_true: Ground truth values
            y_pred: Predicted values
            features: Optional feature array for baseline model prediction

        Returns:
            Array of rewards for each prediction
        """
        # Ensure arrays are properly shaped
        y_true = np.asarray(y_true).reshape(-1)
        y_pred = np.asarray(y_pred).reshape(-1)

        # Get baseline predictions if features are provided
        if features is not None and self.baseline is not None:
            try:
                # Extract the actual feature array if it's in a dictionary
                if isinstance(features, dict) and 'all_features' in features:
                    feature_array = features['all_features']
                else:
                    feature_array = features

                baseline_pred = self.baseline.predict(feature_array)
                baseline_pred = np.asarray(baseline_pred).reshape(-1)
                baseline_error = np.abs(y_true - baseline_pred)
                self.baseline_errors.extend(baseline_error.tolist())
            except Exception as e:
                logger.warning(f"Error getting baseline predictions: {e}")
                baseline_pred = np.zeros_like(y_true)
                baseline_error = np.abs(y_true)
        else:
            # If no features or baseline, use zeros
            baseline_pred = np.zeros_like(y_true)
            baseline_error = np.abs(y_true)

        # Calculate accuracy component
        prediction_error = np.abs(y_true - y_pred)

        # Use a more robust reward function that scales better
        # Smaller errors should give higher rewards
        # Scale the error to be in a reasonable range (0.1 to 10)
        scaled_error = np.clip(prediction_error, 0.1, 10.0)
        # Use inverse of error as reward (higher accuracy = higher reward)
        accuracy_component = 1.0 / (self.accuracy_scale * scaled_error + 1.0)

        # Add R² component if enabled
        if self.use_r2_component and len(y_true) > 1:
            try:
                r2 = r2_score(y_true, y_pred)
                # Scale R² to [0, 1] range (R² can be negative)
                r2_scaled = np.clip((r2 + 1) / 2, 0, 1)
                # Weight the accuracy component with R²
                accuracy_component = 0.7 * accuracy_component + 0.3 * r2_scaled
            except Exception as e:
                logger.warning(f"Error calculating R² score: {e}")

        # Calculate consistency component
        consistency_error = np.abs(y_pred - baseline_pred)
        # Use a similar scaling for consistency
        scaled_consistency_error = np.clip(consistency_error, 0.1, 10.0)
        consistency_component = 1.0 / (scaled_consistency_error + 1.0)

        # Calculate improvement component
        if self.use_improvement_component:
            # Positive reward for improving over baseline
            improvement = np.maximum(0, baseline_error - prediction_error)
            # Normalize improvement
            max_possible_improvement = np.maximum(baseline_error, 1e-8)  # Avoid division by zero
            improvement_component = improvement / max_possible_improvement
            # Apply a non-linear scaling to emphasize improvements
            improvement_component = np.power(improvement_component, 0.5)  # Square root to emphasize small improvements
        else:
            improvement_component = np.zeros_like(y_true)

        # Combine components
        rewards = (
            self.alpha * accuracy_component +
            self.beta * consistency_component +
            self.gamma * improvement_component
        )

        # Clip rewards if enabled
        if self.clip_rewards:
            rewards = np.clip(rewards, self.reward_clip_range[0], self.reward_clip_range[1])

        # Store rewards for history
        self.reward_history.extend(rewards.tolist())

        return rewards

    def get_reward_stats(self) -> Dict[str, float]:
        """
        Get statistics about the reward distribution.

        Returns:
            Dictionary with reward statistics
        """
        if not self.reward_history:
            return {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0, "count": 0}

        rewards = np.array(self.reward_history)
        return {
            "mean": float(np.mean(rewards)),
            "std": float(np.std(rewards)),
            "min": float(np.min(rewards)),
            "max": float(np.max(rewards)),
            "count": len(rewards)
        }

    def reset_history(self):
        """Reset the reward history."""
        self.reward_history = []
        self.baseline_errors = []

class RLVRAgent(nn.Module):
    """
    Reinforcement Learning with Verifiable Rewards (RLVR) agent for growth rate prediction.

    This agent uses a multi-branch architecture with attention mechanisms to process
    different feature types and make predictions. It is trained using reinforcement
    learning with a verifiable reward function.

    The architecture consists of:
    1. Feature-specific encoders for different feature types
    2. Multi-head attention for feature fusion
    3. Policy network for prediction
    4. Value network for uncertainty estimation
    """

    def __init__(
        self,
        feature_dims: Dict[str, int],
        hidden_dim: int = 256,
        num_heads: int = 4,
        num_layers: int = 2,
        dropout: float = 0.2,
        use_layer_norm: bool = True,
        use_residual: bool = True,
        use_value_head: bool = True,
        activation: str = 'relu'
    ):
        """
        Initialize the RLVR agent.

        Args:
            feature_dims: Dictionary mapping feature types to dimensions
            hidden_dim: Dimension of hidden layers (default: 256)
            num_heads: Number of attention heads (default: 4)
            num_layers: Number of transformer layers (default: 2)
            dropout: Dropout rate (default: 0.2)
            use_layer_norm: Whether to use layer normalization (default: True)
            use_residual: Whether to use residual connections (default: True)
            use_value_head: Whether to include a value head for uncertainty (default: True)
            activation: Activation function to use ('relu', 'gelu', or 'silu') (default: 'relu')
        """
        super().__init__()

        self.feature_dims = feature_dims
        self.hidden_dim = hidden_dim
        self.use_value_head = use_value_head
        self.use_residual = use_residual

        # Select activation function
        if activation == 'gelu':
            self.activation = nn.GELU()
        elif activation == 'silu':
            self.activation = nn.SiLU()
        else:
            self.activation = nn.ReLU()

        # Feature encoders for different feature types
        self.feature_encoders = nn.ModuleDict({
            ft: self._create_feature_encoder(dim, hidden_dim, dropout, use_layer_norm)
            for ft, dim in feature_dims.items()
        })

        # Transformer encoder for feature fusion
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=hidden_dim,
            nhead=num_heads,
            dim_feedforward=hidden_dim * 4,
            dropout=dropout,
            batch_first=True
        )
        self.transformer_encoder = nn.TransformerEncoder(
            encoder_layer=encoder_layer,
            num_layers=num_layers
        )

        # Policy network (for growth rate prediction)
        self.policy_net = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            self.activation,
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, hidden_dim // 2),
            self.activation,
            nn.Linear(hidden_dim // 2, 1)
        )

        # Value network (for uncertainty estimation)
        if use_value_head:
            self.value_net = nn.Sequential(
                nn.Linear(hidden_dim, hidden_dim),
                self.activation,
                nn.Dropout(dropout),
                nn.Linear(hidden_dim, hidden_dim // 2),
                self.activation,
                nn.Linear(hidden_dim // 2, 1)
            )

        # Feature importance tracking
        self.feature_importance = {ft: 0.0 for ft in feature_dims.keys()}

        # Initialize weights
        self._init_weights()

        logger.info(f"Initialized RLVRAgent with {len(feature_dims)} feature types, "
                   f"{hidden_dim} hidden dimensions, and {num_heads} attention heads")

    def _create_feature_encoder(self, input_dim, hidden_dim, dropout, use_layer_norm):
        """Create a feature encoder network."""
        layers = [
            nn.Linear(input_dim, hidden_dim),
            self.activation,
            nn.Dropout(dropout)
        ]

        if use_layer_norm:
            layers.append(nn.LayerNorm(hidden_dim))

        return nn.Sequential(*layers)

    def _init_weights(self):
        """Initialize weights for better training."""
        for name, param in self.named_parameters():
            if 'weight' in name:
                if 'norm' not in name and len(param.shape) >= 2:  # Skip LayerNorm weights and 1D tensors
                    nn.init.xavier_uniform_(param)
            elif 'bias' in name:
                nn.init.zeros_(param)

    def forward(self, features: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        Forward pass through the RLVR agent.

        Args:
            features: Dictionary mapping feature types to feature tensors

        Returns:
            Dictionary with 'growth_rate' prediction and optional 'uncertainty'
        """
        # Process each feature type
        encoded_features = []
        for ft, encoder in self.feature_encoders.items():
            if ft in features:
                # Apply feature encoder
                encoded = encoder(features[ft])
                encoded_features.append(encoded)
            else:
                logger.warning(f"Feature type '{ft}' not found in input features")

        if not encoded_features:
            raise ValueError("No valid features found in input")

        # Stack encoded features
        combined = torch.stack(encoded_features, dim=1)

        # Apply transformer encoder
        transformer_out = self.transformer_encoder(combined)

        # Global feature representation (mean across feature types)
        global_features = transformer_out.mean(dim=1)

        # Apply policy network for growth rate prediction
        growth_rate = self.policy_net(global_features)

        # Prepare output
        output = {'growth_rate': growth_rate}

        # Add uncertainty estimation if value head is enabled
        if self.use_value_head:
            uncertainty = torch.abs(self.value_net(global_features))
            output['uncertainty'] = uncertainty

        return output

    def predict(self, features: Dict[str, torch.Tensor]) -> Dict[str, np.ndarray]:
        """
        Make predictions with the RLVR agent.

        Args:
            features: Dictionary mapping feature types to feature tensors

        Returns:
            Dictionary with 'growth_rate' prediction and optional 'uncertainty'
        """
        self.eval()
        with torch.no_grad():
            output = self.forward(features)

            # Convert to numpy
            result = {
                'growth_rate': output['growth_rate'].cpu().numpy().flatten()
            }

            if 'uncertainty' in output:
                result['uncertainty'] = output['uncertainty'].cpu().numpy().flatten()

            return result

    def get_feature_importance(self) -> Dict[str, float]:
        """
        Get feature importance scores.

        Returns:
            Dictionary mapping feature types to importance scores
        """
        return self.feature_importance

    def update_feature_importance(self, gradients: Dict[str, torch.Tensor]):
        """
        Update feature importance based on gradients.

        Args:
            gradients: Dictionary mapping feature types to gradient tensors
        """
        for ft, grad in gradients.items():
            if ft in self.feature_importance:
                # Update importance based on gradient magnitude
                importance = torch.abs(grad).mean().item()
                # Exponential moving average
                self.feature_importance[ft] = 0.9 * self.feature_importance[ft] + 0.1 * importance


import math

class PositionalEncoding(nn.Module):
    def __init__(self, d_model: int, dropout: float = 0.1, max_len: int = 100):
        super().__init__()
        self.dropout = nn.Dropout(p=dropout)

        # Create positional encoding
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-math.log(10000.0) / d_model))
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0)

        # Register buffer (not a parameter, but part of the module)
        self.register_buffer('pe', pe)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        x = x + self.pe[:, :x.size(1), :]
        return self.dropout(x)


class ResidualBlock(nn.Module):
    def __init__(self, layers: nn.Module, hidden_dim: int, use_layer_norm: bool = True, use_batch_norm: bool = False):
        super().__init__()
        self.layers = layers
        self.use_layer_norm = use_layer_norm
        self.use_batch_norm = use_batch_norm

        if use_layer_norm:
            self.norm = nn.LayerNorm(hidden_dim)
        elif use_batch_norm:
            self.norm = nn.BatchNorm1d(hidden_dim)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # Apply layers
        out = self.layers(x)

        # Add residual connection
        out = out + x

        # Apply normalization if enabled
        if self.use_layer_norm or self.use_batch_norm:
            out = self.norm(out)

        return out


class FeatureInteractionLayer(nn.Module):
    def __init__(self, hidden_dim: int, dropout: float = 0.1, use_layer_norm: bool = True):
        super().__init__()

        # Self-attention for feature interaction
        self.self_attn = nn.MultiheadAttention(hidden_dim, num_heads=4, dropout=dropout, batch_first=True)

        # Feed-forward network
        self.feed_forward = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim * 2),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 2, hidden_dim)
        )

        # Normalization layers
        if use_layer_norm:
            self.norm1 = nn.LayerNorm(hidden_dim)
            self.norm2 = nn.LayerNorm(hidden_dim)
        else:
            self.norm1 = nn.Identity()
            self.norm2 = nn.Identity()

        # Dropout
        self.dropout = nn.Dropout(dropout)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # Self-attention with residual connection and normalization
        attn_output, _ = self.self_attn(x, x, x)
        x = x + self.dropout(attn_output)
        x = self.norm1(x)

        # Feed-forward with residual connection and normalization
        ff_output = self.feed_forward(x)
        x = x + self.dropout(ff_output)
        x = self.norm2(x)

        return x


class EnhancedRLVRAgent(nn.Module):
    def __init__(
        self,
        feature_dims: Dict[str, int],
        hidden_dim: int = 256,
        num_heads: int = 4,
        num_layers: int = 2,
        dropout: float = 0.2,
        use_layer_norm: bool = True,
        use_residual: bool = True,
        use_value_head: bool = True,
        activation: str = 'relu',
        use_feature_interaction: bool = True,
        interaction_layers: int = 2,
        use_batch_norm: bool = False
    ):
        super().__init__()

        self.feature_dims = feature_dims
        self.hidden_dim = hidden_dim
        self.use_value_head = use_value_head
        self.use_residual = use_residual
        self.use_feature_interaction = use_feature_interaction

        # Select activation function
        if activation == 'gelu':
            self.activation = nn.GELU()
        elif activation == 'silu':
            self.activation = nn.SiLU()
        else:
            self.activation = nn.ReLU()

        # Feature encoders for different feature types
        self.feature_encoders = nn.ModuleDict({
            ft: self._create_feature_encoder(dim, hidden_dim, dropout, use_layer_norm, use_batch_norm)
            for ft, dim in feature_dims.items()
        })

        # Positional encoding for transformer
        self.pos_encoder = PositionalEncoding(hidden_dim, dropout)

        # Transformer encoder for feature fusion
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=hidden_dim,
            nhead=num_heads,
            dim_feedforward=hidden_dim * 4,
            dropout=dropout,
            batch_first=True
        )
        self.transformer_encoder = nn.TransformerEncoder(
            encoder_layer=encoder_layer,
            num_layers=num_layers
        )

        # Feature interaction layers
        if use_feature_interaction:
            self.interaction_layers = nn.ModuleList([
                FeatureInteractionLayer(hidden_dim, dropout, use_layer_norm)
                for _ in range(interaction_layers)
            ])

        # Policy network (for growth rate prediction)
        self.policy_net = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            self.activation,
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, hidden_dim // 2),
            self.activation,
            nn.Linear(hidden_dim // 2, 1)
        )

        # Secondary policy head for ensemble prediction
        self.policy_net2 = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            self.activation,
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, 1)
        )

        # Value network (for uncertainty estimation)
        if use_value_head:
            self.value_net = nn.Sequential(
                nn.Linear(hidden_dim, hidden_dim),
                self.activation,
                nn.Dropout(dropout),
                nn.Linear(hidden_dim, hidden_dim // 2),
                self.activation,
                nn.Linear(hidden_dim // 2, 1)
            )

        # Feature importance tracking
        self.feature_importance = {ft: 0.0 for ft in feature_dims.keys()}

        # Initialize weights
        self._init_weights()

        logger.info(f"Initialized EnhancedRLVRAgent with {len(feature_dims)} feature types, "
                   f"{hidden_dim} hidden dimensions, and {num_heads} attention heads")

    def _create_feature_encoder(self, input_dim, hidden_dim, dropout, use_layer_norm, use_batch_norm):
        # First layer
        first_layer = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            self.activation,
            nn.Dropout(dropout)
        )

        if use_batch_norm:
            first_layer.append(nn.BatchNorm1d(hidden_dim))
        elif use_layer_norm:
            first_layer.append(nn.LayerNorm(hidden_dim))

        # Second layer with residual connection if dimensions match
        if self.use_residual and input_dim == hidden_dim:
            second_layer = ResidualBlock(
                nn.Sequential(
                    nn.Linear(hidden_dim, hidden_dim),
                    self.activation,
                    nn.Dropout(dropout),
                    nn.Linear(hidden_dim, hidden_dim)
                ),
                hidden_dim,
                use_layer_norm=use_layer_norm,
                use_batch_norm=use_batch_norm
            )
            return nn.Sequential(first_layer, second_layer)
        else:
            # If dimensions don't match, use a regular layer
            second_layer = nn.Sequential(
                nn.Linear(hidden_dim, hidden_dim),
                self.activation,
                nn.Dropout(dropout)
            )

            if use_batch_norm:
                second_layer.append(nn.BatchNorm1d(hidden_dim))
            elif use_layer_norm:
                second_layer.append(nn.LayerNorm(hidden_dim))

            return nn.Sequential(first_layer, second_layer)

    def _init_weights(self):
        for name, param in self.named_parameters():
            if 'weight' in name:
                if 'norm' not in name and len(param.shape) >= 2:  # Skip LayerNorm weights and 1D tensors
                    if 'Linear' in name:
                        # Use Kaiming initialization for linear layers
                        nn.init.kaiming_normal_(param, nonlinearity='relu')
                    else:
                        # Use Xavier for other layers
                        nn.init.xavier_uniform_(param)
            elif 'bias' in name:
                nn.init.zeros_(param)

    def forward(self, features: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        # Process each feature type
        encoded_features = []
        for ft, encoder in self.feature_encoders.items():
            if ft in features:
                # Apply feature encoder
                encoded = encoder(features[ft])
                encoded_features.append(encoded)
            else:
                logger.warning(f"Feature type '{ft}' not found in input features")

        if not encoded_features:
            raise ValueError("No valid features found in input")

        # Stack encoded features
        combined = torch.stack(encoded_features, dim=1)

        # Apply positional encoding
        combined = self.pos_encoder(combined)

        # Apply transformer encoder
        transformer_out = self.transformer_encoder(combined)

        # Apply feature interaction layers if enabled
        if self.use_feature_interaction and hasattr(self, 'interaction_layers'):
            for layer in self.interaction_layers:
                transformer_out = layer(transformer_out)

        # Global feature representation (mean across feature types)
        global_features = transformer_out.mean(dim=1)

        # Apply policy networks for growth rate prediction
        growth_rate1 = self.policy_net(global_features)
        growth_rate2 = self.policy_net2(global_features)

        # Ensemble the predictions (weighted average)
        growth_rate = 0.7 * growth_rate1 + 0.3 * growth_rate2

        # Prepare output
        output = {'growth_rate': growth_rate}

        # Add uncertainty estimation if value head is enabled
        if self.use_value_head:
            uncertainty = torch.abs(self.value_net(global_features))
            output['uncertainty'] = uncertainty

        return output

    def predict(self, features: Dict[str, torch.Tensor]) -> Dict[str, np.ndarray]:
        self.eval()
        with torch.no_grad():
            output = self.forward(features)

            # Convert to numpy
            result = {
                'growth_rate': output['growth_rate'].cpu().numpy().flatten()
            }

            if 'uncertainty' in output:
                result['uncertainty'] = output['uncertainty'].cpu().numpy().flatten()

            return result

    def get_feature_importance(self) -> Dict[str, float]:
        return self.feature_importance

    def update_feature_importance(self, gradients: Dict[str, torch.Tensor]):
        for ft, grad in gradients.items():
            if ft in self.feature_importance:
                # Update importance based on gradient magnitude
                importance = torch.abs(grad).mean().item()
                # Exponential moving average
                self.feature_importance[ft] = 0.9 * self.feature_importance[ft] + 0.1 * importance



import math

class PositionalEncoding(nn.Module):
    def __init__(self, d_model: int, dropout: float = 0.1, max_len: int = 100):
        super().__init__()
        self.dropout = nn.Dropout(p=dropout)

        # Create positional encoding
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-math.log(10000.0) / d_model))
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0)

        # Register buffer (not a parameter, but part of the module)
        self.register_buffer('pe', pe)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        x = x + self.pe[:, :x.size(1), :]
        return self.dropout(x)


class ResidualBlock(nn.Module):
    def __init__(self, layers: nn.Module, hidden_dim: int, use_layer_norm: bool = True, use_batch_norm: bool = False):
        super().__init__()
        self.layers = layers
        self.use_layer_norm = use_layer_norm
        self.use_batch_norm = use_batch_norm

        if use_layer_norm:
            self.norm = nn.LayerNorm(hidden_dim)
        elif use_batch_norm:
            self.norm = nn.BatchNorm1d(hidden_dim)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # Apply layers
        out = self.layers(x)

        # Add residual connection
        out = out + x

        # Apply normalization if enabled
        if self.use_layer_norm or self.use_batch_norm:
            out = self.norm(out)

        return out


class FeatureInteractionLayer(nn.Module):
    def __init__(self, hidden_dim: int, dropout: float = 0.1, use_layer_norm: bool = True):
        super().__init__()

        # Self-attention for feature interaction
        self.self_attn = nn.MultiheadAttention(hidden_dim, num_heads=4, dropout=dropout, batch_first=True)

        # Feed-forward network
        self.feed_forward = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim * 2),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 2, hidden_dim)
        )

        # Normalization layers
        if use_layer_norm:
            self.norm1 = nn.LayerNorm(hidden_dim)
            self.norm2 = nn.LayerNorm(hidden_dim)
        else:
            self.norm1 = nn.Identity()
            self.norm2 = nn.Identity()

        # Dropout
        self.dropout = nn.Dropout(dropout)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # Self-attention with residual connection and normalization
        attn_output, _ = self.self_attn(x, x, x)
        x = x + self.dropout(attn_output)
        x = self.norm1(x)

        # Feed-forward with residual connection and normalization
        ff_output = self.feed_forward(x)
        x = x + self.dropout(ff_output)
        x = self.norm2(x)

        return x


class EnhancedRLVRAgent(nn.Module):
    def __init__(
        self,
        feature_dims: Dict[str, int],
        hidden_dim: int = 256,
        num_heads: int = 4,
        num_layers: int = 2,
        dropout: float = 0.2,
        use_layer_norm: bool = True,
        use_residual: bool = True,
        use_value_head: bool = True,
        activation: str = 'relu',
        use_feature_interaction: bool = True,
        interaction_layers: int = 2,
        use_batch_norm: bool = False
    ):
        super().__init__()

        self.feature_dims = feature_dims
        self.hidden_dim = hidden_dim
        self.use_value_head = use_value_head
        self.use_residual = use_residual
        self.use_feature_interaction = use_feature_interaction

        # Select activation function
        if activation == 'gelu':
            self.activation = nn.GELU()
        elif activation == 'silu':
            self.activation = nn.SiLU()
        else:
            self.activation = nn.ReLU()

        # Feature encoders for different feature types
        self.feature_encoders = nn.ModuleDict({
            ft: self._create_feature_encoder(dim, hidden_dim, dropout, use_layer_norm, use_batch_norm)
            for ft, dim in feature_dims.items()
        })

        # Positional encoding for transformer
        self.pos_encoder = PositionalEncoding(hidden_dim, dropout)

        # Transformer encoder for feature fusion
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=hidden_dim,
            nhead=num_heads,
            dim_feedforward=hidden_dim * 4,
            dropout=dropout,
            batch_first=True
        )
        self.transformer_encoder = nn.TransformerEncoder(
            encoder_layer=encoder_layer,
            num_layers=num_layers
        )

        # Feature interaction layers
        if use_feature_interaction:
            self.interaction_layers = nn.ModuleList([
                FeatureInteractionLayer(hidden_dim, dropout, use_layer_norm)
                for _ in range(interaction_layers)
            ])

        # Policy network (for growth rate prediction)
        self.policy_net = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            self.activation,
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, hidden_dim // 2),
            self.activation,
            nn.Linear(hidden_dim // 2, 1)
        )

        # Secondary policy head for ensemble prediction
        self.policy_net2 = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            self.activation,
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, 1)
        )

        # Value network (for uncertainty estimation)
        if use_value_head:
            self.value_net = nn.Sequential(
                nn.Linear(hidden_dim, hidden_dim),
                self.activation,
                nn.Dropout(dropout),
                nn.Linear(hidden_dim, hidden_dim // 2),
                self.activation,
                nn.Linear(hidden_dim // 2, 1)
            )

        # Feature importance tracking
        self.feature_importance = {ft: 0.0 for ft in feature_dims.keys()}

        # Initialize weights
        self._init_weights()

        logger.info(f"Initialized EnhancedRLVRAgent with {len(feature_dims)} feature types, "
                   f"{hidden_dim} hidden dimensions, and {num_heads} attention heads")

    def _create_feature_encoder(self, input_dim, hidden_dim, dropout, use_layer_norm, use_batch_norm):
        # First layer
        first_layer = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            self.activation,
            nn.Dropout(dropout)
        )

        if use_batch_norm:
            first_layer.append(nn.BatchNorm1d(hidden_dim))
        elif use_layer_norm:
            first_layer.append(nn.LayerNorm(hidden_dim))

        # Second layer with residual connection if dimensions match
        if self.use_residual and input_dim == hidden_dim:
            second_layer = ResidualBlock(
                nn.Sequential(
                    nn.Linear(hidden_dim, hidden_dim),
                    self.activation,
                    nn.Dropout(dropout),
                    nn.Linear(hidden_dim, hidden_dim)
                ),
                hidden_dim,
                use_layer_norm=use_layer_norm,
                use_batch_norm=use_batch_norm
            )
            return nn.Sequential(first_layer, second_layer)
        else:
            # If dimensions don't match, use a regular layer
            second_layer = nn.Sequential(
                nn.Linear(hidden_dim, hidden_dim),
                self.activation,
                nn.Dropout(dropout)
            )

            if use_batch_norm:
                second_layer.append(nn.BatchNorm1d(hidden_dim))
            elif use_layer_norm:
                second_layer.append(nn.LayerNorm(hidden_dim))

            return nn.Sequential(first_layer, second_layer)

    def _init_weights(self):
        for name, param in self.named_parameters():
            if 'weight' in name:
                if 'norm' not in name and len(param.shape) >= 2:  # Skip LayerNorm weights and 1D tensors
                    if 'Linear' in name:
                        # Use Kaiming initialization for linear layers
                        nn.init.kaiming_normal_(param, nonlinearity='relu')
                    else:
                        # Use Xavier for other layers
                        nn.init.xavier_uniform_(param)
            elif 'bias' in name:
                nn.init.zeros_(param)

    def forward(self, features: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        # Process each feature type
        encoded_features = []
        for ft, encoder in self.feature_encoders.items():
            if ft in features:
                # Apply feature encoder
                encoded = encoder(features[ft])
                encoded_features.append(encoded)
            else:
                logger.warning(f"Feature type '{ft}' not found in input features")

        if not encoded_features:
            raise ValueError("No valid features found in input")

        # Stack encoded features
        combined = torch.stack(encoded_features, dim=1)

        # Apply positional encoding
        combined = self.pos_encoder(combined)

        # Apply transformer encoder
        transformer_out = self.transformer_encoder(combined)

        # Apply feature interaction layers if enabled
        if self.use_feature_interaction and hasattr(self, 'interaction_layers'):
            for layer in self.interaction_layers:
                transformer_out = layer(transformer_out)

        # Global feature representation (mean across feature types)
        global_features = transformer_out.mean(dim=1)

        # Apply policy networks for growth rate prediction
        growth_rate1 = self.policy_net(global_features)
        growth_rate2 = self.policy_net2(global_features)

        # Ensemble the predictions (weighted average)
        growth_rate = 0.7 * growth_rate1 + 0.3 * growth_rate2

        # Prepare output
        output = {'growth_rate': growth_rate}

        # Add uncertainty estimation if value head is enabled
        if self.use_value_head:
            uncertainty = torch.abs(self.value_net(global_features))
            output['uncertainty'] = uncertainty

        return output

    def predict(self, features: Dict[str, torch.Tensor]) -> Dict[str, np.ndarray]:
        self.eval()
        with torch.no_grad():
            output = self.forward(features)

            # Convert to numpy
            result = {
                'growth_rate': output['growth_rate'].cpu().numpy().flatten()
            }

            if 'uncertainty' in output:
                result['uncertainty'] = output['uncertainty'].cpu().numpy().flatten()

            return result

    def get_feature_importance(self) -> Dict[str, float]:
        return self.feature_importance

    def update_feature_importance(self, gradients: Dict[str, torch.Tensor]):
        for ft, grad in gradients.items():
            if ft in self.feature_importance:
                # Update importance based on gradient magnitude
                importance = torch.abs(grad).mean().item()
                # Exponential moving average
                self.feature_importance[ft] = 0.9 * self.feature_importance[ft] + 0.1 * importance



import math

class PositionalEncoding(nn.Module):
    def __init__(self, d_model: int, dropout: float = 0.1, max_len: int = 100):
        super().__init__()
        self.dropout = nn.Dropout(p=dropout)

        # Create positional encoding
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-math.log(10000.0) / d_model))
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0)

        # Register buffer (not a parameter, but part of the module)
        self.register_buffer('pe', pe)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        x = x + self.pe[:, :x.size(1), :]
        return self.dropout(x)


class ResidualBlock(nn.Module):
    def __init__(self, layers: nn.Module, hidden_dim: int, use_layer_norm: bool = True, use_batch_norm: bool = False):
        super().__init__()
        self.layers = layers
        self.use_layer_norm = use_layer_norm
        self.use_batch_norm = use_batch_norm

        if use_layer_norm:
            self.norm = nn.LayerNorm(hidden_dim)
        elif use_batch_norm:
            self.norm = nn.BatchNorm1d(hidden_dim)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # Apply layers
        out = self.layers(x)

        # Add residual connection
        out = out + x

        # Apply normalization if enabled
        if self.use_layer_norm or self.use_batch_norm:
            out = self.norm(out)

        return out


class FeatureInteractionLayer(nn.Module):
    def __init__(self, hidden_dim: int, dropout: float = 0.1, use_layer_norm: bool = True):
        super().__init__()

        # Self-attention for feature interaction
        self.self_attn = nn.MultiheadAttention(hidden_dim, num_heads=4, dropout=dropout, batch_first=True)

        # Feed-forward network
        self.feed_forward = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim * 2),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 2, hidden_dim)
        )

        # Normalization layers
        if use_layer_norm:
            self.norm1 = nn.LayerNorm(hidden_dim)
            self.norm2 = nn.LayerNorm(hidden_dim)
        else:
            self.norm1 = nn.Identity()
            self.norm2 = nn.Identity()

        # Dropout
        self.dropout = nn.Dropout(dropout)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # Self-attention with residual connection and normalization
        attn_output, _ = self.self_attn(x, x, x)
        x = x + self.dropout(attn_output)
        x = self.norm1(x)

        # Feed-forward with residual connection and normalization
        ff_output = self.feed_forward(x)
        x = x + self.dropout(ff_output)
        x = self.norm2(x)

        return x


class EnhancedRLVRAgent(nn.Module):
    def __init__(
        self,
        feature_dims: Dict[str, int],
        hidden_dim: int = 256,
        num_heads: int = 4,
        num_layers: int = 2,
        dropout: float = 0.2,
        use_layer_norm: bool = True,
        use_residual: bool = True,
        use_value_head: bool = True,
        activation: str = 'relu',
        use_feature_interaction: bool = True,
        interaction_layers: int = 2,
        use_batch_norm: bool = False
    ):
        super().__init__()

        self.feature_dims = feature_dims
        self.hidden_dim = hidden_dim
        self.use_value_head = use_value_head
        self.use_residual = use_residual
        self.use_feature_interaction = use_feature_interaction

        # Select activation function
        if activation == 'gelu':
            self.activation = nn.GELU()
        elif activation == 'silu':
            self.activation = nn.SiLU()
        else:
            self.activation = nn.ReLU()

        # Feature encoders for different feature types
        self.feature_encoders = nn.ModuleDict({
            ft: self._create_feature_encoder(dim, hidden_dim, dropout, use_layer_norm, use_batch_norm)
            for ft, dim in feature_dims.items()
        })

        # Positional encoding for transformer
        self.pos_encoder = PositionalEncoding(hidden_dim, dropout)

        # Transformer encoder for feature fusion
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=hidden_dim,
            nhead=num_heads,
            dim_feedforward=hidden_dim * 4,
            dropout=dropout,
            batch_first=True
        )
        self.transformer_encoder = nn.TransformerEncoder(
            encoder_layer=encoder_layer,
            num_layers=num_layers
        )

        # Feature interaction layers
        if use_feature_interaction:
            self.interaction_layers = nn.ModuleList([
                FeatureInteractionLayer(hidden_dim, dropout, use_layer_norm)
                for _ in range(interaction_layers)
            ])

        # Policy network (for growth rate prediction)
        self.policy_net = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            self.activation,
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, hidden_dim // 2),
            self.activation,
            nn.Linear(hidden_dim // 2, 1)
        )

        # Secondary policy head for ensemble prediction
        self.policy_net2 = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            self.activation,
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, 1)
        )

        # Value network (for uncertainty estimation)
        if use_value_head:
            self.value_net = nn.Sequential(
                nn.Linear(hidden_dim, hidden_dim),
                self.activation,
                nn.Dropout(dropout),
                nn.Linear(hidden_dim, hidden_dim // 2),
                self.activation,
                nn.Linear(hidden_dim // 2, 1)
            )

        # Feature importance tracking
        self.feature_importance = {ft: 0.0 for ft in feature_dims.keys()}

        # Initialize weights
        self._init_weights()

        logger.info(f"Initialized EnhancedRLVRAgent with {len(feature_dims)} feature types, "
                   f"{hidden_dim} hidden dimensions, and {num_heads} attention heads")

    def _create_feature_encoder(self, input_dim, hidden_dim, dropout, use_layer_norm, use_batch_norm):
        # First layer
        first_layer = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            self.activation,
            nn.Dropout(dropout)
        )

        if use_batch_norm:
            first_layer.append(nn.BatchNorm1d(hidden_dim))
        elif use_layer_norm:
            first_layer.append(nn.LayerNorm(hidden_dim))

        # Second layer with residual connection if dimensions match
        if self.use_residual and input_dim == hidden_dim:
            second_layer = ResidualBlock(
                nn.Sequential(
                    nn.Linear(hidden_dim, hidden_dim),
                    self.activation,
                    nn.Dropout(dropout),
                    nn.Linear(hidden_dim, hidden_dim)
                ),
                hidden_dim,
                use_layer_norm=use_layer_norm,
                use_batch_norm=use_batch_norm
            )
            return nn.Sequential(first_layer, second_layer)
        else:
            # If dimensions don't match, use a regular layer
            second_layer = nn.Sequential(
                nn.Linear(hidden_dim, hidden_dim),
                self.activation,
                nn.Dropout(dropout)
            )

            if use_batch_norm:
                second_layer.append(nn.BatchNorm1d(hidden_dim))
            elif use_layer_norm:
                second_layer.append(nn.LayerNorm(hidden_dim))

            return nn.Sequential(first_layer, second_layer)

    def _init_weights(self):
        for name, param in self.named_parameters():
            if 'weight' in name:
                if 'norm' not in name and len(param.shape) >= 2:  # Skip LayerNorm weights and 1D tensors
                    if 'Linear' in name:
                        # Use Kaiming initialization for linear layers
                        nn.init.kaiming_normal_(param, nonlinearity='relu')
                    else:
                        # Use Xavier for other layers
                        nn.init.xavier_uniform_(param)
            elif 'bias' in name:
                nn.init.zeros_(param)

    def forward(self, features: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        # Process each feature type
        encoded_features = []
        for ft, encoder in self.feature_encoders.items():
            if ft in features:
                # Apply feature encoder
                encoded = encoder(features[ft])
                encoded_features.append(encoded)
            else:
                logger.warning(f"Feature type '{ft}' not found in input features")

        if not encoded_features:
            raise ValueError("No valid features found in input")

        # Stack encoded features
        combined = torch.stack(encoded_features, dim=1)

        # Apply positional encoding
        combined = self.pos_encoder(combined)

        # Apply transformer encoder
        transformer_out = self.transformer_encoder(combined)

        # Apply feature interaction layers if enabled
        if self.use_feature_interaction and hasattr(self, 'interaction_layers'):
            for layer in self.interaction_layers:
                transformer_out = layer(transformer_out)

        # Global feature representation (mean across feature types)
        global_features = transformer_out.mean(dim=1)

        # Apply policy networks for growth rate prediction
        growth_rate1 = self.policy_net(global_features)
        growth_rate2 = self.policy_net2(global_features)

        # Ensemble the predictions (weighted average)
        growth_rate = 0.7 * growth_rate1 + 0.3 * growth_rate2

        # Prepare output
        output = {'growth_rate': growth_rate}

        # Add uncertainty estimation if value head is enabled
        if self.use_value_head:
            uncertainty = torch.abs(self.value_net(global_features))
            output['uncertainty'] = uncertainty

        return output

    def predict(self, features: Dict[str, torch.Tensor]) -> Dict[str, np.ndarray]:
        self.eval()
        with torch.no_grad():
            output = self.forward(features)

            # Convert to numpy
            result = {
                'growth_rate': output['growth_rate'].cpu().numpy().flatten()
            }

            if 'uncertainty' in output:
                result['uncertainty'] = output['uncertainty'].cpu().numpy().flatten()

            return result

    def get_feature_importance(self) -> Dict[str, float]:
        return self.feature_importance

    def update_feature_importance(self, gradients: Dict[str, torch.Tensor]):
        for ft, grad in gradients.items():
            if ft in self.feature_importance:
                # Update importance based on gradient magnitude
                importance = torch.abs(grad).mean().item()
                # Exponential moving average
                self.feature_importance[ft] = 0.9 * self.feature_importance[ft] + 0.1 * importance


class RLVRTrainer:
    """
    Trainer for the RLVR agent.

    This class implements the training process for the RLVR agent, including:
    1. Policy gradient training with verifiable rewards
    2. Experience replay for more stable learning
    3. Adaptive learning rate scheduling
    4. Comprehensive metrics tracking
    5. Early stopping based on validation performance
    """

    def __init__(
        self,
        agent: RLVRAgent,
        reward_fn: VerifiableRewardFunction,
        lr: float = 1e-4,
        weight_decay: float = 1e-5,
        gamma: float = 0.99,
        entropy_coef: float = 0.01,
        value_loss_coef: float = 0.5,
        max_grad_norm: float = 1.0,
        use_lr_scheduler: bool = True,
        use_experience_replay: bool = True,
        replay_buffer_size: int = 10000,
        batch_size: int = 32,
        device: Optional[torch.device] = None
    ):
        """
        Initialize the RLVR trainer.

        Args:
            agent: The RLVR agent to train
            reward_fn: The reward function to use
            lr: Learning rate (default: 1e-4)
            weight_decay: Weight decay for regularization (default: 1e-5)
            gamma: Discount factor for future rewards (default: 0.99)
            entropy_coef: Coefficient for entropy regularization (default: 0.01)
            value_loss_coef: Coefficient for value loss (default: 0.5)
            max_grad_norm: Maximum gradient norm for clipping (default: 1.0)
            use_lr_scheduler: Whether to use learning rate scheduling (default: True)
            use_experience_replay: Whether to use experience replay (default: True)
            replay_buffer_size: Size of the experience replay buffer (default: 10000)
            batch_size: Batch size for experience replay (default: 32)
            device: Device to use for training (default: None, uses CUDA if available)
        """
        self.agent = agent
        self.reward_fn = reward_fn
        self.gamma = gamma
        self.entropy_coef = entropy_coef
        self.value_loss_coef = value_loss_coef
        self.max_grad_norm = max_grad_norm
        self.use_experience_replay = use_experience_replay
        self.batch_size = batch_size

        # Set device
        self.device = device if device is not None else torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.agent.to(self.device)

        # Create optimizer
        self.optimizer = torch.optim.Adam(
            self.agent.parameters(),
            lr=lr,
            weight_decay=weight_decay
        )

        # Create learning rate scheduler
        if use_lr_scheduler:
            self.scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
                self.optimizer,
                mode='max',
                factor=0.5,
                patience=5
            )
        else:
            self.scheduler = None

        # Create experience replay buffer
        if use_experience_replay:
            self.replay_buffer = []
            self.replay_buffer_size = replay_buffer_size

        # Initialize metrics tracking
        self.metrics = {
            'train_loss': [],
            'train_reward': [],
            'train_policy_loss': [],
            'train_value_loss': [],
            'train_entropy': [],
            'val_loss': [],
            'val_reward': [],
            'val_r2': [],
            'val_rmse': [],
            'learning_rate': []
        }

        # Initialize best model tracking
        self.best_val_reward = -float('inf')
        self.best_model_state = None

        logger.info(f"Initialized RLVRTrainer with learning rate {lr}, "
                   f"gamma {gamma}, and entropy coefficient {entropy_coef}")

    def _prepare_batch(self, features: Dict[str, np.ndarray], targets: np.ndarray) -> Tuple[Dict[str, torch.Tensor], torch.Tensor]:
        """
        Prepare a batch for training by converting to tensors and moving to device.

        Args:
            features: Dictionary of feature arrays
            targets: Target values

        Returns:
            Tuple of (features_tensor, targets_tensor)
        """
        # Convert features to tensors
        features_tensor = {}
        for ft, feat in features.items():
            if ft in self.agent.feature_encoders:
                features_tensor[ft] = torch.tensor(feat, dtype=torch.float32).to(self.device)

        # Convert targets to tensor
        targets_tensor = torch.tensor(targets, dtype=torch.float32).to(self.device)

        return features_tensor, targets_tensor

    def _calculate_policy_gradient_loss(
        self,
        predictions: torch.Tensor,
        rewards: torch.Tensor,
        targets: Optional[torch.Tensor] = None,
        entropy: Optional[torch.Tensor] = None
    ) -> torch.Tensor:
        """
        Calculate the policy gradient loss.

        Args:
            predictions: Predicted values
            rewards: Reward values
            targets: Optional ground truth values
            entropy: Optional entropy term for regularization

        Returns:
            Policy gradient loss
        """
        # Calculate MSE loss between predictions and targets (if provided)
        if targets is not None:
            mse_loss = F.mse_loss(predictions, targets)
        else:
            # If no targets provided, use a dummy loss of 0
            mse_loss = torch.tensor(0.0, device=predictions.device)

        # Calculate policy gradient loss
        # We want to maximize reward, so we minimize negative reward * prediction
        # Use the difference between prediction and target to guide the policy
        if targets is not None:
            # Scale rewards based on prediction error
            pred_error = torch.abs(predictions - targets)
            # Higher error should lead to lower effective reward
            scaled_rewards = rewards / (1.0 + pred_error)
        else:
            scaled_rewards = rewards

        # Policy gradient loss (negative because we want to maximize reward)
        # Use a combination of MSE loss and policy gradient loss
        policy_loss = -torch.mean(scaled_rewards * predictions) + 0.5 * mse_loss

        # Add entropy regularization if provided
        if entropy is not None and self.entropy_coef > 0:
            policy_loss = policy_loss - self.entropy_coef * entropy

        return policy_loss

    def _calculate_value_loss(self, value_pred: torch.Tensor, rewards: torch.Tensor) -> torch.Tensor:
        """
        Calculate the value loss for the critic.

        Args:
            value_pred: Predicted values from the value network
            rewards: Reward values

        Returns:
            Value loss
        """
        return F.mse_loss(value_pred, rewards)

    def add_to_replay_buffer(self, features: Dict[str, np.ndarray], targets: np.ndarray):
        """
        Add experience to the replay buffer.

        Args:
            features: Dictionary of feature arrays
            targets: Target values
        """
        if not self.use_experience_replay:
            return

        # Add to replay buffer
        self.replay_buffer.append((features, targets))

        # Limit buffer size
        if len(self.replay_buffer) > self.replay_buffer_size:
            self.replay_buffer.pop(0)

    def sample_from_replay_buffer(self, batch_size: int) -> List[Tuple[Dict[str, np.ndarray], np.ndarray]]:
        """
        Sample a batch from the replay buffer.

        Args:
            batch_size: Number of samples to draw

        Returns:
            List of (features, targets) tuples
        """
        if not self.use_experience_replay or len(self.replay_buffer) == 0:
            return []

        # Sample from replay buffer
        indices = np.random.choice(len(self.replay_buffer), min(batch_size, len(self.replay_buffer)), replace=False)
        return [self.replay_buffer[i] for i in indices]

    def train_step(
        self,
        features: Dict[str, np.ndarray],
        targets: np.ndarray,
        include_replay: bool = True
    ) -> Dict[str, float]:
        """
        Perform a single training step.

        Args:
            features: Dictionary of feature arrays
            targets: Target values
            include_replay: Whether to include samples from the replay buffer

        Returns:
            Dictionary of metrics for this step
        """
        self.agent.train()

        # Prepare batch
        features_tensor, targets_tensor = self._prepare_batch(features, targets)

        # Forward pass
        output = self.agent(features_tensor)
        predictions = output['growth_rate'].squeeze()

        # Get rewards from reward function
        rewards_np = self.reward_fn(targets, predictions.detach().cpu().numpy(), features)
        rewards = torch.tensor(rewards_np, dtype=torch.float32).to(self.device)

        # Calculate policy loss with targets
        policy_loss = self._calculate_policy_gradient_loss(predictions, rewards, targets_tensor)

        # Calculate value loss if value head is enabled
        if self.agent.use_value_head and 'uncertainty' in output:
            value_pred = output['uncertainty'].squeeze()
            value_loss = self._calculate_value_loss(value_pred, rewards)
            total_loss = policy_loss + self.value_loss_coef * value_loss
        else:
            value_loss = torch.tensor(0.0)
            total_loss = policy_loss

        # Backward pass
        self.optimizer.zero_grad()
        total_loss.backward()

        # Gradient clipping
        if self.max_grad_norm > 0:
            torch.nn.utils.clip_grad_norm_(self.agent.parameters(), self.max_grad_norm)

        # Update parameters
        self.optimizer.step()

        # Update feature importance
        feature_gradients = {}
        for ft, tensor in features_tensor.items():
            if tensor.grad is not None:
                feature_gradients[ft] = tensor.grad
        self.agent.update_feature_importance(feature_gradients)

        # Add to replay buffer
        self.add_to_replay_buffer(features, targets)

        # Include experience replay if enabled
        if include_replay and self.use_experience_replay:
            replay_samples = self.sample_from_replay_buffer(self.batch_size)
            for replay_features, replay_targets in replay_samples:
                # Train on replay sample
                self.train_step(replay_features, replay_targets, include_replay=False)

        # Collect metrics
        step_metrics = {
            'loss': total_loss.item(),
            'policy_loss': policy_loss.item(),
            'value_loss': value_loss.item() if isinstance(value_loss, torch.Tensor) else 0.0,
            'reward': rewards.mean().item(),
            'predictions': predictions.detach().cpu().numpy(),
            'targets': targets
        }

        return step_metrics

    def validate(self, val_features: Dict[str, np.ndarray], val_targets: np.ndarray) -> Dict[str, float]:
        """
        Validate the agent on validation data.

        Args:
            val_features: Dictionary of validation feature arrays
            val_targets: Validation target values

        Returns:
            Dictionary of validation metrics
        """
        self.agent.eval()

        # Prepare batch
        features_tensor, targets_tensor = self._prepare_batch(val_features, val_targets)

        with torch.no_grad():
            # Forward pass
            output = self.agent(features_tensor)
            predictions = output['growth_rate'].squeeze()

            # Get rewards from reward function
            rewards_np = self.reward_fn(val_targets, predictions.cpu().numpy(), val_features)
            rewards = torch.tensor(rewards_np, dtype=torch.float32).to(self.device)

            # Calculate policy loss with targets
            policy_loss = self._calculate_policy_gradient_loss(predictions, rewards, targets_tensor)

            # Calculate value loss if value head is enabled
            if self.agent.use_value_head and 'uncertainty' in output:
                value_pred = output['uncertainty'].squeeze()
                value_loss = self._calculate_value_loss(value_pred, rewards)
                total_loss = policy_loss + self.value_loss_coef * value_loss
            else:
                value_loss = torch.tensor(0.0)
                total_loss = policy_loss

            # Calculate additional metrics
            predictions_np = predictions.cpu().numpy()
            r2 = r2_score(val_targets, predictions_np)
            rmse = np.sqrt(mean_squared_error(val_targets, predictions_np))

        # Collect metrics
        val_metrics = {
            'loss': total_loss.item(),
            'policy_loss': policy_loss.item(),
            'value_loss': value_loss.item() if isinstance(value_loss, torch.Tensor) else 0.0,
            'reward': rewards.mean().item(),
            'r2': r2,
            'rmse': rmse,
            'predictions': predictions_np,
            'targets': val_targets
        }

        return val_metrics

    def train(
        self,
        train_features: Dict[str, np.ndarray],
        train_targets: np.ndarray,
        val_features: Optional[Dict[str, np.ndarray]] = None,
        val_targets: Optional[np.ndarray] = None,
        num_epochs: int = 100,
        batch_size: int = 32,
        early_stopping_patience: int = 10,
        verbose: bool = True,
        log_interval: int = 10,
        save_best_model: bool = True
    ) -> Dict[str, List[float]]:
        """
        Train the RLVR agent.

        Args:
            train_features: Dictionary of training feature arrays
            train_targets: Training target values
            val_features: Optional dictionary of validation feature arrays
            val_targets: Optional validation target values
            num_epochs: Number of epochs to train for (default: 100)
            batch_size: Batch size for training (default: 32)
            early_stopping_patience: Number of epochs to wait for improvement (default: 10)
            verbose: Whether to print progress (default: True)
            log_interval: Interval for logging progress (default: 10)
            save_best_model: Whether to save the best model (default: True)

        Returns:
            Dictionary of training metrics
        """
        # Reset metrics
        self.metrics = {
            'train_loss': [],
            'train_reward': [],
            'train_policy_loss': [],
            'train_value_loss': [],
            'train_entropy': [],
            'val_loss': [],
            'val_reward': [],
            'val_r2': [],
            'val_rmse': [],
            'learning_rate': []
        }

        # Reset best model tracking
        self.best_val_reward = -float('inf')
        self.best_model_state = None

        # Early stopping counter
        early_stopping_counter = 0

        # Get number of samples
        num_samples = len(train_targets)

        # Calculate number of batches
        num_batches = (num_samples + batch_size - 1) // batch_size

        logger.info(f"Starting training for {num_epochs} epochs with {num_samples} samples "
                   f"({num_batches} batches per epoch)")

        for epoch in range(num_epochs):
            # Shuffle indices
            indices = np.random.permutation(num_samples)

            # Initialize epoch metrics
            epoch_loss = 0.0
            epoch_reward = 0.0
            epoch_policy_loss = 0.0
            epoch_value_loss = 0.0

            # Train on batches
            for batch_idx in range(num_batches):
                # Get batch indices
                start_idx = batch_idx * batch_size
                end_idx = min(start_idx + batch_size, num_samples)
                batch_indices = indices[start_idx:end_idx]

                # Get batch data
                batch_features = {ft: feat[batch_indices] for ft, feat in train_features.items()}
                batch_targets = train_targets[batch_indices]

                # Train on batch
                step_metrics = self.train_step(batch_features, batch_targets)

                # Update epoch metrics
                epoch_loss += step_metrics['loss']
                epoch_reward += step_metrics['reward']
                epoch_policy_loss += step_metrics['policy_loss']
                epoch_value_loss += step_metrics['value_loss']

            # Calculate average epoch metrics
            epoch_loss /= num_batches
            epoch_reward /= num_batches
            epoch_policy_loss /= num_batches
            epoch_value_loss /= num_batches

            # Update metrics
            self.metrics['train_loss'].append(epoch_loss)
            self.metrics['train_reward'].append(epoch_reward)
            self.metrics['train_policy_loss'].append(epoch_policy_loss)
            self.metrics['train_value_loss'].append(epoch_value_loss)
            self.metrics['learning_rate'].append(self.optimizer.param_groups[0]['lr'])

            # Validate if validation data is provided
            if val_features is not None and val_targets is not None:
                val_metrics = self.validate(val_features, val_targets)

                # Update metrics
                self.metrics['val_loss'].append(val_metrics['loss'])
                self.metrics['val_reward'].append(val_metrics['reward'])
                self.metrics['val_r2'].append(val_metrics['r2'])
                self.metrics['val_rmse'].append(val_metrics['rmse'])

                # Update learning rate scheduler
                if self.scheduler is not None:
                    self.scheduler.step(val_metrics['reward'])

                # Check for best model
                if val_metrics['reward'] > self.best_val_reward:
                    self.best_val_reward = val_metrics['reward']
                    if save_best_model:
                        self.best_model_state = {
                            'model_state_dict': self.agent.state_dict(),
                            'optimizer_state_dict': self.optimizer.state_dict(),
                            'epoch': epoch,
                            'metrics': val_metrics
                        }
                    early_stopping_counter = 0
                else:
                    early_stopping_counter += 1

                # Print progress
                if verbose and (epoch % log_interval == 0 or epoch == num_epochs - 1):
                    logger.info(
                        f"Epoch {epoch+1}/{num_epochs} - "
                        f"Loss: {epoch_loss:.4f}, "
                        f"Reward: {epoch_reward:.4f}, "
                        f"Val Loss: {val_metrics['loss']:.4f}, "
                        f"Val Reward: {val_metrics['reward']:.4f}, "
                        f"Val R²: {val_metrics['r2']:.4f}, "
                        f"Val RMSE: {val_metrics['rmse']:.4f}"
                    )

                # Early stopping
                if early_stopping_counter >= early_stopping_patience:
                    logger.info(f"Early stopping triggered after {epoch+1} epochs")
                    break
            else:
                # Print progress without validation
                if verbose and (epoch % log_interval == 0 or epoch == num_epochs - 1):
                    logger.info(
                        f"Epoch {epoch+1}/{num_epochs} - "
                        f"Loss: {epoch_loss:.4f}, "
                        f"Reward: {epoch_reward:.4f}"
                    )

        # Restore best model if saved
        if save_best_model and self.best_model_state is not None:
            self.agent.load_state_dict(self.best_model_state['model_state_dict'])
            logger.info(f"Restored best model from epoch {self.best_model_state['epoch']+1} "
                       f"with validation reward {self.best_val_reward:.4f}")

        return self.metrics

    def save_model(self, path: str):
        """
        Save the trained model and optimizer state.

        Args:
            path: Path to save the model to
        """
        # Create state dictionary
        state_dict = {
            'model_state_dict': self.agent.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'metrics': self.metrics,
            'best_val_reward': self.best_val_reward,
            'reward_stats': self.reward_fn.get_reward_stats()
        }

        # Save model
        torch.save(state_dict, path)
        logger.info(f"Model saved to {path}")

    def load_model(self, path: str):
        """
        Load a trained model and optimizer state.

        Args:
            path: Path to load the model from
        """
        # Load state dictionary
        state_dict = torch.load(path, map_location=self.device)

        # Load model state
        self.agent.load_state_dict(state_dict['model_state_dict'])

        # Load optimizer state
        self.optimizer.load_state_dict(state_dict['optimizer_state_dict'])

        # Load metrics
        if 'metrics' in state_dict:
            self.metrics = state_dict['metrics']

        # Load best validation reward
        if 'best_val_reward' in state_dict:
            self.best_val_reward = state_dict['best_val_reward']

        logger.info(f"Model loaded from {path}")

    def get_metrics(self) -> Dict[str, List[float]]:
        """
        Get training metrics.

        Returns:
            Dictionary of training metrics
        """
        return self.metrics