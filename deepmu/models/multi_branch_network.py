"""Multi-Branch Neural Network Models Module

This module implements a multi-branch neural network architecture for microbial growth rate
and optimal growth temperature prediction, with separate branches for different feature types:
1. Codon usage features
2. Amino acid features
3. Genomic features
4. RNA features
5. Protein pI features
6. Taxonomy features

The architecture uses self-attention for feature fusion and includes residual connections
for improved gradient flow.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Optional, Tuple, Union, Any
import copy
import math

from ..utils.logging import get_logger, log_function_call
from .advanced_regularization import (
    StochasticWeightAveraging,
    MixupAugmentation,
    LabelSmoothing,
    SpectralNormalization,
    SAM,
    GradientClipping,
    CyclicalLearningRate,
    OneCycleLR
)

logger = get_logger()


class FeatureBranch(nn.Module):
    """A single branch for processing a specific feature type."""

    def __init__(
        self,
        input_dim: int,
        hidden_dim: int,
        output_dim: int,
        dropout_rate: float = 0.2,
        use_batch_norm: bool = True,
        use_residual: bool = True
    ):
        """Initialize a feature branch.

        Args:
            input_dim: Input feature dimension
            hidden_dim: Hidden layer dimension
            output_dim: Output dimension
            dropout_rate: Dropout rate for regularization
            use_batch_norm: Whether to use batch normalization
            use_residual: Whether to use residual connections
        """
        super(FeatureBranch, self).__init__()

        self.use_residual = use_residual
        self.use_batch_norm = use_batch_norm

        # First layer
        self.fc1 = nn.Linear(input_dim, hidden_dim)
        self.bn1 = nn.BatchNorm1d(hidden_dim) if use_batch_norm else nn.Identity()
        self.dropout1 = nn.Dropout(dropout_rate)

        # Second layer
        self.fc2 = nn.Linear(hidden_dim, hidden_dim)
        self.bn2 = nn.BatchNorm1d(hidden_dim) if use_batch_norm else nn.Identity()
        self.dropout2 = nn.Dropout(dropout_rate)

        # Output layer
        self.fc_out = nn.Linear(hidden_dim, output_dim)

        # Residual connection (if input and hidden dimensions differ)
        self.residual = nn.Linear(input_dim, hidden_dim) if use_residual and input_dim != hidden_dim else None

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Forward pass through the feature branch.

        Args:
            x: Input tensor [batch_size, input_dim]

        Returns:
            Output tensor [batch_size, output_dim]
        """
        # First layer
        h1 = self.fc1(x)
        h1 = self.bn1(h1)
        h1 = F.relu(h1)
        h1 = self.dropout1(h1)

        # Apply residual connection if enabled
        if self.use_residual:
            if self.residual is not None:
                residual = self.residual(x)
            else:
                residual = x
            h1 = h1 + residual

        # Second layer
        h2 = self.fc2(h1)
        h2 = self.bn2(h2)
        h2 = F.relu(h2)
        h2 = self.dropout2(h2)

        # Apply residual connection if enabled
        if self.use_residual:
            h2 = h2 + h1

        # Output layer
        out = self.fc_out(h2)

        return out


class AttentionFusion(nn.Module):
    """Attention-based fusion module for combining features from different branches."""

    def __init__(
        self,
        embed_dim: int,
        num_heads: int = 4,
        dropout_rate: float = 0.1
    ):
        """Initialize the attention fusion module.

        Args:
            embed_dim: Embedding dimension
            num_heads: Number of attention heads
            dropout_rate: Dropout rate for attention
        """
        super(AttentionFusion, self).__init__()

        # Ensure embed_dim is divisible by num_heads
        if embed_dim % num_heads != 0:
            adjusted_dim = (embed_dim // num_heads) * num_heads
            logger.warning(f"Embedding dimension {embed_dim} not divisible by {num_heads} heads. "
                          f"Adjusting to {adjusted_dim}.")
            embed_dim = adjusted_dim

        self.embed_dim = embed_dim
        self.num_heads = num_heads

        # Multi-head attention
        # 注意：移除 batch_first=True 以兼容较旧版本的 PyTorch
        self.attention = nn.MultiheadAttention(
            embed_dim=embed_dim,
            num_heads=num_heads,
            dropout=dropout_rate
        )

        # Layer normalization
        self.layer_norm = nn.LayerNorm(embed_dim)

        # Branch importance weights (learnable)
        self.branch_weights = nn.Parameter(torch.ones(1))

    def forward(self, features: List[torch.Tensor]) -> torch.Tensor:
        """Forward pass through the attention fusion module.

        Args:
            features: List of feature tensors from different branches
                      [batch_size, embed_dim]

        Returns:
            Fused features [batch_size, embed_dim]
        """
        if len(features) == 0:
            raise ValueError("No features provided for fusion")

        if len(features) == 1:
            return features[0]

        # Stack features for attention [batch_size, num_branches, embed_dim]
        stacked_features = torch.stack(features, dim=1)

        # 转换为 MultiheadAttention 期望的格式 [num_branches, batch_size, embed_dim]
        # 因为我们移除了 batch_first=True 参数
        stacked_features_transposed = stacked_features.transpose(0, 1)

        # Apply self-attention
        attn_output, attn_weights = self.attention(
            query=stacked_features_transposed,
            key=stacked_features_transposed,
            value=stacked_features_transposed
        )

        # 转换回 [batch_size, num_branches, embed_dim] 格式
        attn_output = attn_output.transpose(0, 1)

        # Residual connection and layer normalization
        normalized = self.layer_norm(attn_output + stacked_features)

        # Weighted sum across branches
        # Initialize branch weights if needed
        if self.branch_weights.size(0) != normalized.size(1):
            self.branch_weights.data = torch.ones(normalized.size(1), device=normalized.device)
            self.branch_weights.data = self.branch_weights.data / normalized.size(1)

        # Apply softmax to get normalized weights
        branch_weights = F.softmax(self.branch_weights, dim=0)

        # Weighted sum [batch_size, embed_dim]
        weighted_sum = torch.einsum('bnf,n->bf', normalized, branch_weights)

        return weighted_sum


class MultiBranchDNN(nn.Module):
    """Multi-branch deep neural network for microbial growth rate and temperature prediction.

    This model uses separate branches for different feature types:
    1. Codon usage features
    2. Amino acid features
    3. Genomic features
    4. RNA features
    5. Protein pI features
    6. Taxonomy features

    Features are processed independently and then fused using attention mechanisms.
    """

    def __init__(
        self,
        feature_dims: Dict[str, int],
        hidden_dims: List[int],
        output_dim: int = 2,
        dropout_rates: List[float] = [0.2, 0.2, 0.3],
        use_batch_norm: bool = True,
        use_residual: bool = True,
        attention_heads: int = 4,
        fusion_layers: int = 2,
        multi_task: bool = True
    ):
        """Initialize the multi-branch DNN.

        Args:
            feature_dims: Dictionary mapping feature types to their dimensions
                          e.g., {'codon': 64, 'aa': 32, 'genomic': 128, ...}
            hidden_dims: List of hidden layer dimensions for each branch and fusion network
            output_dim: Output dimension (1 for single task, 2 for multi-task)
            dropout_rates: List of dropout rates for different parts of the network
            use_batch_norm: Whether to use batch normalization
            use_residual: Whether to use residual connections
            attention_heads: Number of attention heads for feature fusion
            fusion_layers: Number of layers in the fusion network
            multi_task: Whether to predict both growth rate and temperature
        """
        super(MultiBranchDNN, self).__init__()

        self.feature_dims = feature_dims
        self.hidden_dims = hidden_dims
        self.output_dim = output_dim
        self.multi_task = multi_task

        # Ensure we have enough hidden dimensions
        if len(hidden_dims) < 2:
            raise ValueError("At least 2 hidden dimensions required (branch_dim, fusion_dim)")

        branch_dim = hidden_dims[0]
        fusion_dim = hidden_dims[1]

        # Create feature branches
        self.branches = nn.ModuleDict()
        for feature_type, dim in feature_dims.items():
            if dim > 0:  # Only create branches for features with non-zero dimensions
                self.branches[feature_type] = FeatureBranch(
                    input_dim=dim,
                    hidden_dim=branch_dim,
                    output_dim=branch_dim,
                    dropout_rate=dropout_rates[0],
                    use_batch_norm=use_batch_norm,
                    use_residual=use_residual
                )

        # Attention fusion module
        self.attention_fusion = AttentionFusion(
            embed_dim=branch_dim,
            num_heads=attention_heads,
            dropout_rate=dropout_rates[1]
        )

        # Fusion network
        fusion_layers_list = []
        prev_dim = branch_dim

        for i in range(fusion_layers):
            next_dim = fusion_dim if i < fusion_layers - 1 else fusion_dim // 2
            fusion_layers_list.extend([
                nn.Linear(prev_dim, next_dim),
                nn.BatchNorm1d(next_dim) if use_batch_norm else nn.Identity(),
                nn.ReLU(),
                nn.Dropout(dropout_rates[min(i+2, len(dropout_rates)-1)])
            ])
            prev_dim = next_dim

        self.fusion_network = nn.Sequential(*fusion_layers_list)

        # Output heads
        if multi_task:
            self.growth_rate_head = nn.Linear(prev_dim, 1)
            self.temperature_head = nn.Linear(prev_dim, 1)
        else:
            self.output_head = nn.Linear(prev_dim, output_dim)

    def forward(self, x: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """Forward pass through the multi-branch DNN.

        Args:
            x: Dictionary mapping feature types to tensors
               e.g., {'codon': tensor, 'aa': tensor, 'genomic': tensor, ...}

        Returns:
            Dictionary with predictions:
            - 'growth_rate': Growth rate predictions [batch_size]
            - 'temperature': Temperature predictions [batch_size] (if multi_task=True)
            - 'output': Combined output [batch_size, output_dim] (if multi_task=False)
        """
        # Process each branch
        branch_outputs = []

        for feature_type, branch in self.branches.items():
            if feature_type in x and x[feature_type] is not None:
                branch_output = branch(x[feature_type])
                branch_outputs.append(branch_output)

        # If no branches were processed, return zeros
        if not branch_outputs:
            batch_size = next(iter(x.values())).size(0) if x else 1
            device = next(iter(x.values())).device if x else torch.device('cpu')

            if self.multi_task:
                return {
                    'growth_rate': torch.zeros(batch_size, device=device),
                    'temperature': torch.zeros(batch_size, device=device)
                }
            else:
                return {
                    'output': torch.zeros(batch_size, self.output_dim, device=device)
                }

        # Fuse branch outputs using attention
        fused_features = self.attention_fusion(branch_outputs)

        # Pass through fusion network
        fusion_output = self.fusion_network(fused_features)

        # Generate predictions
        if self.multi_task:
            growth_rate = self.growth_rate_head(fusion_output).squeeze(-1)
            temperature = self.temperature_head(fusion_output).squeeze(-1)

            return {
                'growth_rate': growth_rate,
                'temperature': temperature
            }
        else:
            output = self.output_head(fusion_output)

            return {
                'output': output
            }

    def predict_with_uncertainty(self, x: Dict[str, torch.Tensor], n_samples: int = 30) -> Dict[str, torch.Tensor]:
        """Generate predictions with uncertainty estimates using Monte Carlo Dropout.

        Args:
            x: Input features dictionary
            n_samples: Number of stochastic forward passes for uncertainty estimation

        Returns:
            Dictionary with mean predictions and uncertainty estimates (standard deviations)
        """
        # Switch to train mode to enable dropout during inference
        self.train()

        # Initialize lists to store predictions
        growth_rate_samples = []
        temperature_samples = []

        # Generate multiple predictions with dropout enabled
        for _ in range(n_samples):
            with torch.no_grad():
                predictions = self(x)

                if self.multi_task:
                    growth_rate_samples.append(predictions['growth_rate'].unsqueeze(0))
                    temperature_samples.append(predictions['temperature'].unsqueeze(0))
                else:
                    growth_rate_samples.append(predictions['output'].unsqueeze(0))

        # Stack predictions
        if self.multi_task:
            growth_rate_samples = torch.cat(growth_rate_samples, dim=0)
            temperature_samples = torch.cat(temperature_samples, dim=0)

            # Calculate mean and standard deviation
            growth_rate_mean = growth_rate_samples.mean(dim=0)
            growth_rate_std = growth_rate_samples.std(dim=0)

            temperature_mean = temperature_samples.mean(dim=0)
            temperature_std = temperature_samples.std(dim=0)

            return {
                'growth_rate_mean': growth_rate_mean,
                'growth_rate_std': growth_rate_std,
                'temperature_mean': temperature_mean,
                'temperature_std': temperature_std
            }
        else:
            output_samples = torch.cat(growth_rate_samples, dim=0)

            # Calculate mean and standard deviation
            output_mean = output_samples.mean(dim=0)
            output_std = output_samples.std(dim=0)

            return {
                'output_mean': output_mean,
                'output_std': output_std
            }


class MultiBranchTrainer:
    """Trainer class for the MultiBranchDNN model with advanced regularization techniques."""

    def __init__(
        self,
        model: MultiBranchDNN,
        learning_rate: float = 0.001,
        weight_decay: float = 1e-5,
        device: str = 'cuda' if torch.cuda.is_available() else 'cpu',
        # Advanced regularization options
        use_swa: bool = False,
        swa_start: int = 10,
        swa_freq: int = 5,
        use_mixup: bool = False,
        mixup_alpha: float = 0.2,
        use_label_smoothing: bool = False,
        label_smoothing: float = 0.1,
        use_spectral_norm: bool = False,
        use_sam: bool = False,
        sam_rho: float = 0.05,
        use_gradient_clipping: bool = False,
        max_grad_norm: float = 1.0,
        lr_scheduler: str = 'plateau',  # 'plateau', 'cyclic', 'one_cycle'
        cyclic_lr_max: Optional[float] = None,
        cyclic_lr_mode: str = 'triangular',
        one_cycle_max_lr: Optional[float] = None,
        one_cycle_total_steps: Optional[int] = None
    ):
        """Initialize the trainer with advanced regularization options.

        Args:
            model: MultiBranchDNN model
            learning_rate: Learning rate for optimizer
            weight_decay: Weight decay for regularization
            device: Device to use for training ('cuda' or 'cpu')
            use_swa: Whether to use Stochastic Weight Averaging
            swa_start: Epoch to start SWA from
            swa_freq: Frequency of model averaging in SWA
            use_mixup: Whether to use Mixup data augmentation
            mixup_alpha: Alpha parameter for Mixup
            use_label_smoothing: Whether to use label smoothing
            label_smoothing: Label smoothing parameter
            use_spectral_norm: Whether to use spectral normalization
            use_sam: Whether to use Sharpness-Aware Minimization
            sam_rho: Neighborhood size for SAM
            use_gradient_clipping: Whether to use gradient clipping
            max_grad_norm: Maximum gradient norm for clipping
            lr_scheduler: Learning rate scheduler type
            cyclic_lr_max: Maximum learning rate for cyclic scheduler
            cyclic_lr_mode: Mode for cyclic scheduler
            one_cycle_max_lr: Maximum learning rate for one-cycle scheduler
            one_cycle_total_steps: Total steps for one-cycle scheduler
        """
        self.model = model
        self.device = device
        self.model.to(device)
        self.learning_rate = learning_rate

        # Store regularization options
        self.use_swa = use_swa
        self.use_mixup = use_mixup
        self.use_label_smoothing = use_label_smoothing
        self.use_gradient_clipping = use_gradient_clipping
        self.lr_scheduler_type = lr_scheduler

        # Initialize regularization components
        if use_spectral_norm:
            logger.info("Applying spectral normalization to model")
            SpectralNormalization(model, n_power_iterations=1)

        # Initialize optimizer
        if use_sam:
            logger.info("Using SAM optimizer")
            self.optimizer = SAM(
                model.parameters(),
                base_optimizer=torch.optim.Adam,
                rho=sam_rho,
                adaptive=True,
                lr=learning_rate,
                weight_decay=weight_decay
            )
        else:
            self.optimizer = torch.optim.Adam(
                model.parameters(),
                lr=learning_rate,
                weight_decay=weight_decay
            )

        # Initialize learning rate scheduler
        if lr_scheduler == 'cyclic' and cyclic_lr_max is not None:
            logger.info(f"Using cyclical learning rate scheduler with max_lr={cyclic_lr_max}")
            self.scheduler = CyclicalLearningRate(
                optimizer=self.optimizer,
                base_lr=learning_rate,
                max_lr=cyclic_lr_max,
                step_size_up=2000,
                mode=cyclic_lr_mode
            )
        elif lr_scheduler == 'one_cycle' and one_cycle_max_lr is not None and one_cycle_total_steps is not None:
            logger.info(f"Using one-cycle learning rate scheduler with max_lr={one_cycle_max_lr}")
            self.scheduler = OneCycleLR(
                optimizer=self.optimizer,
                max_lr=one_cycle_max_lr,
                total_steps=one_cycle_total_steps
            )
        else:
            logger.info("Using ReduceLROnPlateau scheduler")
            self.scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
                self.optimizer,
                mode='min',
                factor=0.5,
                patience=5,
                verbose=True
            )

        # Initialize SWA if enabled
        if use_swa:
            logger.info(f"Using Stochastic Weight Averaging starting from epoch {swa_start}")
            self.swa = StochasticWeightAveraging(
                model=model,
                swa_start=swa_start,
                swa_freq=swa_freq
            )
        else:
            self.swa = None

        # Initialize Mixup if enabled
        if use_mixup:
            logger.info(f"Using Mixup data augmentation with alpha={mixup_alpha}")
            self.mixup = MixupAugmentation(alpha=mixup_alpha)
        else:
            self.mixup = None

        # Initialize Label Smoothing if enabled
        if use_label_smoothing:
            logger.info(f"Using Label Smoothing with smoothing={label_smoothing}")
            self.label_smoother = LabelSmoothing(smoothing=label_smoothing)
        else:
            self.label_smoother = None

        # Initialize Gradient Clipping if enabled
        if use_gradient_clipping:
            logger.info(f"Using Gradient Clipping with max_norm={max_grad_norm}")
            self.gradient_clipper = GradientClipping(max_norm=max_grad_norm)
        else:
            self.gradient_clipper = None

        # Loss functions
        self.mse_loss = nn.MSELoss()

    def train_epoch(
        self,
        train_loader: torch.utils.data.DataLoader,
        multi_task_weights: Dict[str, float] = {'growth_rate': 0.5, 'temperature': 0.5}
    ) -> Dict[str, float]:
        """Train the model for one epoch with advanced regularization.

        Args:
            train_loader: DataLoader for training data
            multi_task_weights: Weights for multi-task learning

        Returns:
            Dictionary with training metrics
        """
        self.model.train()

        total_loss = 0.0
        growth_rate_loss = 0.0
        temperature_loss = 0.0
        num_batches = 0

        for batch in train_loader:
            # Get features and targets
            features = {k: v.to(self.device) for k, v in batch['features'].items() if k in self.model.feature_dims}
            targets = {k: v.to(self.device) for k, v in batch['targets'].items()}

            # Apply Mixup if enabled
            if self.use_mixup and self.mixup is not None:
                features, targets = self.mixup(features, targets)

            # Apply Label Smoothing if enabled
            if self.use_label_smoothing and self.label_smoother is not None:
                targets = self.label_smoother(targets)

            # Handle SAM optimizer which requires a closure
            if isinstance(self.optimizer, SAM):
                # First forward-backward pass
                self.optimizer.zero_grad()

                # Define closure for SAM
                def closure():
                    predictions = self.model(features)

                    # Calculate loss
                    if self.model.multi_task:
                        # Multi-task loss
                        gr_loss = self.mse_loss(predictions['growth_rate'], targets['growth_rate'])
                        temp_loss = self.mse_loss(predictions['temperature'], targets['temperature'])

                        # Weighted sum of losses
                        loss = multi_task_weights['growth_rate'] * gr_loss + multi_task_weights['temperature'] * temp_loss
                    else:
                        # Single task loss
                        loss = self.mse_loss(predictions['output'], targets['target'])

                    loss.backward()
                    return loss

                # Compute ascent step
                self.optimizer.first_step(zero_grad=True)

                # Second forward-backward pass
                loss = closure()
                self.optimizer.second_step(zero_grad=True)

                # Track losses for multi-task learning
                if self.model.multi_task:
                    with torch.no_grad():
                        predictions = self.model(features)
                        gr_loss = self.mse_loss(predictions['growth_rate'], targets['growth_rate'])
                        temp_loss = self.mse_loss(predictions['temperature'], targets['temperature'])
                        growth_rate_loss += gr_loss.item()
                        temperature_loss += temp_loss.item()
            else:
                # Standard optimization
                self.optimizer.zero_grad()

                # Forward pass
                predictions = self.model(features)

                # Calculate loss
                if self.model.multi_task:
                    # Multi-task loss
                    gr_loss = self.mse_loss(predictions['growth_rate'], targets['growth_rate'])
                    temp_loss = self.mse_loss(predictions['temperature'], targets['temperature'])

                    # Weighted sum of losses
                    loss = multi_task_weights['growth_rate'] * gr_loss + multi_task_weights['temperature'] * temp_loss

                    # Track individual losses
                    growth_rate_loss += gr_loss.item()
                    temperature_loss += temp_loss.item()
                else:
                    # Single task loss
                    loss = self.mse_loss(predictions['output'], targets['target'])

                # Backward pass
                loss.backward()

                # Apply gradient clipping if enabled
                if self.use_gradient_clipping and self.gradient_clipper is not None:
                    self.gradient_clipper(self.model.parameters())

                # Optimize
                self.optimizer.step()

            # Update learning rate for cyclic and one-cycle schedulers
            if self.lr_scheduler_type in ['cyclic', 'one_cycle']:
                self.scheduler.step()

            # Track metrics
            total_loss += loss.item()
            num_batches += 1

        # Calculate average metrics
        metrics = {
            'loss': total_loss / num_batches
        }

        if self.model.multi_task:
            metrics.update({
                'growth_rate_loss': growth_rate_loss / num_batches,
                'temperature_loss': temperature_loss / num_batches
            })

        return metrics

    def validate(
        self,
        val_loader: torch.utils.data.DataLoader,
        multi_task_weights: Dict[str, float] = {'growth_rate': 0.5, 'temperature': 0.5}
    ) -> Dict[str, float]:
        """Validate the model.

        Args:
            val_loader: DataLoader for validation data
            multi_task_weights: Weights for multi-task learning

        Returns:
            Dictionary with validation metrics
        """
        self.model.eval()

        total_loss = 0.0
        growth_rate_loss = 0.0
        temperature_loss = 0.0
        num_batches = 0

        growth_rate_preds = []
        growth_rate_targets = []
        temperature_preds = []
        temperature_targets = []

        with torch.no_grad():
            for batch in val_loader:
                # Get features and targets
                features = {k: v.to(self.device) for k, v in batch['features'].items() if k in self.model.feature_dims}
                targets = {k: v.to(self.device) for k, v in batch['targets'].items()}

                # Forward pass
                predictions = self.model(features)

                # Calculate loss
                if self.model.multi_task:
                    # Multi-task loss
                    gr_loss = self.mse_loss(predictions['growth_rate'], targets['growth_rate'])
                    temp_loss = self.mse_loss(predictions['temperature'], targets['temperature'])

                    # Weighted sum of losses
                    loss = multi_task_weights['growth_rate'] * gr_loss + multi_task_weights['temperature'] * temp_loss

                    # Track individual losses
                    growth_rate_loss += gr_loss.item()
                    temperature_loss += temp_loss.item()

                    # Store predictions and targets for metrics
                    growth_rate_preds.append(predictions['growth_rate'].cpu())
                    growth_rate_targets.append(targets['growth_rate'].cpu())
                    temperature_preds.append(predictions['temperature'].cpu())
                    temperature_targets.append(targets['temperature'].cpu())
                else:
                    # Single task loss
                    loss = self.mse_loss(predictions['output'], targets['target'])

                    # Store predictions and targets for metrics
                    growth_rate_preds.append(predictions['output'].cpu())
                    growth_rate_targets.append(targets['target'].cpu())

                # Track metrics
                total_loss += loss.item()
                num_batches += 1

        # Calculate average metrics
        metrics = {
            'val_loss': total_loss / num_batches
        }

        if self.model.multi_task:
            metrics.update({
                'val_growth_rate_loss': growth_rate_loss / num_batches,
                'val_temperature_loss': temperature_loss / num_batches
            })

            # Calculate additional metrics
            growth_rate_preds = torch.cat(growth_rate_preds)
            growth_rate_targets = torch.cat(growth_rate_targets)
            temperature_preds = torch.cat(temperature_preds)
            temperature_targets = torch.cat(temperature_targets)

            # R-squared
            gr_r2 = self._r_squared(growth_rate_preds, growth_rate_targets)
            temp_r2 = self._r_squared(temperature_preds, temperature_targets)

            # RMSE
            gr_rmse = torch.sqrt(torch.mean((growth_rate_preds - growth_rate_targets) ** 2))
            temp_rmse = torch.sqrt(torch.mean((temperature_preds - temperature_targets) ** 2))

            metrics.update({
                'val_growth_rate_r2': gr_r2.item(),
                'val_temperature_r2': temp_r2.item(),
                'val_growth_rate_rmse': gr_rmse.item(),
                'val_temperature_rmse': temp_rmse.item()
            })
        else:
            # Calculate additional metrics for single task
            growth_rate_preds = torch.cat(growth_rate_preds)
            growth_rate_targets = torch.cat(growth_rate_targets)

            # R-squared
            r2 = self._r_squared(growth_rate_preds, growth_rate_targets)

            # RMSE
            rmse = torch.sqrt(torch.mean((growth_rate_preds - growth_rate_targets) ** 2))

            metrics.update({
                'val_r2': r2.item(),
                'val_rmse': rmse.item()
            })

        return metrics

    def _r_squared(self, preds: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """Calculate R-squared.

        Args:
            preds: Predictions
            targets: Ground truth targets

        Returns:
            R-squared value
        """
        target_mean = torch.mean(targets)
        ss_tot = torch.sum((targets - target_mean) ** 2)
        ss_res = torch.sum((targets - preds) ** 2)

        r2 = 1 - ss_res / (ss_tot + 1e-8)

        return r2

    def train(
        self,
        train_loader: torch.utils.data.DataLoader,
        val_loader: torch.utils.data.DataLoader,
        epochs: int = 100,
        patience: int = 10,
        multi_task_weights: Dict[str, float] = {'growth_rate': 0.5, 'temperature': 0.5},
        save_path: Optional[str] = None,
        swa_loader: Optional[torch.utils.data.DataLoader] = None
    ) -> Dict[str, List[float]]:
        """Train the model with advanced regularization techniques.

        Args:
            train_loader: DataLoader for training data
            val_loader: DataLoader for validation data
            epochs: Number of epochs to train
            patience: Patience for early stopping
            multi_task_weights: Weights for multi-task learning
            save_path: Path to save the best model
            swa_loader: DataLoader for updating batch norm statistics in SWA

        Returns:
            Dictionary with training history
        """
        # Initialize history
        history = {
            'loss': [],
            'val_loss': []
        }

        if self.model.multi_task:
            history.update({
                'growth_rate_loss': [],
                'temperature_loss': [],
                'val_growth_rate_loss': [],
                'val_temperature_loss': [],
                'val_growth_rate_r2': [],
                'val_temperature_r2': [],
                'val_growth_rate_rmse': [],
                'val_temperature_rmse': []
            })
        else:
            history.update({
                'val_r2': [],
                'val_rmse': []
            })

        # Initialize early stopping
        best_val_loss = float('inf')
        patience_counter = 0
        best_model_state = None

        # Training loop
        for epoch in range(epochs):
            # Train
            train_metrics = self.train_epoch(train_loader, multi_task_weights)

            # Validate
            val_metrics = self.validate(val_loader, multi_task_weights)

            # Update SWA model if enabled
            if self.use_swa and self.swa is not None:
                swa_updated = self.swa.update(epoch, self.optimizer)
                if swa_updated:
                    logger.info(f"  Updated SWA model (epoch {epoch+1})")

            # Update learning rate for plateau scheduler
            if self.lr_scheduler_type == 'plateau':
                self.scheduler.step(val_metrics['val_loss'])

            # Update history
            for metric, value in train_metrics.items():
                history[metric].append(value)

            for metric, value in val_metrics.items():
                history[metric].append(value)

            # Print progress
            logger.info(f"Epoch {epoch+1}/{epochs}")
            logger.info(f"  Train Loss: {train_metrics['loss']:.4f}")
            logger.info(f"  Val Loss: {val_metrics['val_loss']:.4f}")

            if self.model.multi_task:
                logger.info(f"  Val Growth Rate R²: {val_metrics['val_growth_rate_r2']:.4f}")
                logger.info(f"  Val Temperature R²: {val_metrics['val_temperature_r2']:.4f}")
            else:
                logger.info(f"  Val R²: {val_metrics['val_r2']:.4f}")

            # Check for early stopping
            if val_metrics['val_loss'] < best_val_loss:
                best_val_loss = val_metrics['val_loss']
                patience_counter = 0

                # Save best model state
                best_model_state = copy.deepcopy(self.model.state_dict())

                # Save best model
                if save_path and not self.use_swa:
                    torch.save(self.model.state_dict(), save_path)
                    logger.info(f"  Saved best model to {save_path}")
            else:
                patience_counter += 1

                if patience_counter >= patience:
                    logger.info(f"Early stopping triggered after {epoch+1} epochs")
                    break

        # Apply SWA if enabled
        if self.use_swa and self.swa is not None:
            logger.info("Applying SWA model")

            # Swap with SWA model
            original_model = self.swa.swap_swa_model()

            # Update batch norm statistics if a loader is provided
            if swa_loader is not None:
                logger.info("Updating batch normalization statistics for SWA model")
                self.swa._update_bn(self.model, swa_loader)

            # Evaluate SWA model
            swa_metrics = self.validate(val_loader, multi_task_weights)

            logger.info("SWA model performance:")
            logger.info(f"  Val Loss: {swa_metrics['val_loss']:.4f}")

            if self.model.multi_task:
                logger.info(f"  Val Growth Rate R²: {swa_metrics['val_growth_rate_r2']:.4f}")
                logger.info(f"  Val Temperature R²: {swa_metrics['val_temperature_r2']:.4f}")
            else:
                logger.info(f"  Val R²: {swa_metrics['val_r2']:.4f}")

            # Keep SWA model if it's better, otherwise revert
            if swa_metrics['val_loss'] < best_val_loss:
                logger.info("SWA model is better than best model, keeping it")
                best_val_loss = swa_metrics['val_loss']

                # Save SWA model
                if save_path:
                    torch.save(self.model.state_dict(), save_path)
                    logger.info(f"  Saved SWA model to {save_path}")
            else:
                logger.info("Best model is better than SWA model, reverting to best model")

                # Restore best model
                self.model.load_state_dict(best_model_state)

                # Save best model
                if save_path:
                    torch.save(self.model.state_dict(), save_path)
                    logger.info(f"  Saved best model to {save_path}")
        elif best_model_state is not None and save_path:
            # Restore best model if not using SWA
            self.model.load_state_dict(best_model_state)
            torch.save(self.model.state_dict(), save_path)
            logger.info(f"  Saved best model to {save_path}")

        return history
