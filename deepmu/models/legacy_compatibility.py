"""
遗留模型兼容性模块

这个模块提供了与预训练简单模型兼容的接口，
用于加载和使用现有的预训练模型文件。
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import os
import pickle
import warnings
from typing import List, Optional, Dict, Any


class SimpleSequentialModel(nn.Module):
    """
    简单的序列模型
    
    这个模型与预训练的 growth_model.pt 兼容，
    使用简单的全连接层序列进行预测。
    """
    
    def __init__(self, 
                 input_dim: int = 2500,
                 hidden_dims: List[int] = [512, 256, 128, 64],
                 dropout_rates: List[float] = [0.3, 0.4, 0.4, 0.3],
                 use_batch_norm: bool = True,
                 activation: str = 'relu'):
        """
        初始化简单模型
        
        Args:
            input_dim: 输入特征维度
            hidden_dims: 隐藏层维度列表
            dropout_rates: 每层的dropout率
            use_batch_norm: 是否使用批量归一化
            activation: 激活函数类型
        """
        super(SimpleSequentialModel, self).__init__()
        
        self.input_dim = input_dim
        self.hidden_dims = hidden_dims
        self.use_batch_norm = use_batch_norm
        
        # 选择激活函数
        if activation == 'relu':
            self.activation = nn.ReLU()
        elif activation == 'leaky_relu':
            self.activation = nn.LeakyReLU()
        elif activation == 'elu':
            self.activation = nn.ELU()
        else:
            self.activation = nn.ReLU()
        
        # 构建网络层
        layers = []
        prev_dim = input_dim
        
        for i, (hidden_dim, dropout_rate) in enumerate(zip(hidden_dims, dropout_rates)):
            # 线性层
            layers.append(nn.Linear(prev_dim, hidden_dim))
            
            # 批量归一化
            if use_batch_norm:
                layers.append(nn.BatchNorm1d(hidden_dim))
            
            # 激活函数
            layers.append(self.activation)
            
            # Dropout
            if dropout_rate > 0:
                layers.append(nn.Dropout(dropout_rate))
            
            prev_dim = hidden_dim
        
        # 输出层
        layers.append(nn.Linear(prev_dim, 1))

        # 直接将层作为模块属性，以匹配预训练模型的参数命名
        for i, layer in enumerate(layers):
            setattr(self, str(i), layer)
    
    def forward(self, x):
        """
        前向传播

        Args:
            x: 输入特征张量 [batch_size, input_dim]

        Returns:
            预测的生长速率 [batch_size, 1]
        """
        # 手动执行前向传播，使用直接的层属性
        for i in range(len(self.hidden_dims) * 4 + 1):  # 每个隐藏层有4个组件 + 输出层
            if hasattr(self, str(i)):
                layer = getattr(self, str(i))
                x = layer(x)
        return x


class LegacyModelWrapper:
    """
    遗留模型包装器
    
    这个类包装简单的预训练模型，使其与新的预测器接口兼容。
    """
    
    def __init__(self, model_path: str, device: str = 'cpu'):
        """
        初始化包装器
        
        Args:
            model_path: 预训练模型路径
            device: 计算设备
        """
        self.device = torch.device(device)
        self.model = None
        self.model_params = None
        self.model_path = model_path
        
        # 加载模型参数配置
        self._load_model_params()
        
        # 创建并加载模型
        self._create_and_load_model()
    
    def _load_model_params(self):
        """加载模型参数配置"""
        model_dir = os.path.dirname(self.model_path)
        params_path = os.path.join(model_dir, 'growth_model_params.pkl')
        
        if os.path.exists(params_path):
            try:
                with open(params_path, 'rb') as f:
                    self.model_params = pickle.load(f)
                print(f"✅ 加载模型参数配置: {self.model_params}")
            except Exception as e:
                warnings.warn(f"无法加载模型参数配置: {e}")
                self.model_params = self._get_default_params()
        else:
            warnings.warn("未找到模型参数配置文件，使用默认参数")
            self.model_params = self._get_default_params()
    
    def _get_default_params(self):
        """获取默认模型参数"""
        return {
            'input_dim': 2500,
            'hidden_dims': [512, 256, 128, 64],
            'dropout_rates': [0.3, 0.4, 0.4, 0.3],
            'use_batch_norm': True,
            'activation': 'relu'
        }
    
    def _create_and_load_model(self):
        """创建并加载模型"""
        # 首先尝试直接加载预训练模型来推断结构
        if os.path.exists(self.model_path):
            try:
                state_dict = torch.load(self.model_path, map_location=self.device)

                # 从预训练模型推断结构
                input_dim = self.model_params.get('input_dim', 250)  # 从配置文件得到的实际输入维度

                # 创建一个简单的序列模型，直接匹配预训练模型的结构
                self.model = self._create_model_from_state_dict(state_dict, input_dim)

                # 加载预训练权重
                self.model.load_state_dict(state_dict)
                print(f"✅ 成功加载预训练模型: {self.model_path}")

            except Exception as e:
                warnings.warn(f"无法加载预训练权重: {e}")
                print("⚠️  创建默认模型结构")
                # 创建默认模型
                self.model = SimpleSequentialModel(
                    input_dim=self.model_params.get('input_dim', 250),
                    hidden_dims=self.model_params.get('hidden_dims', [512, 384, 256, 128]),
                    dropout_rates=self.model_params.get('dropout_rates', [0.3, 0.4, 0.4, 0.5]),
                    use_batch_norm=self.model_params.get('use_batch_norm', True),
                    activation=self.model_params.get('activation', 'gelu')
                ).to(self.device)
        else:
            warnings.warn(f"模型文件不存在: {self.model_path}")
            # 创建默认模型
            self.model = SimpleSequentialModel(
                input_dim=self.model_params.get('input_dim', 250),
                hidden_dims=self.model_params.get('hidden_dims', [512, 384, 256, 128]),
                dropout_rates=self.model_params.get('dropout_rates', [0.3, 0.4, 0.4, 0.5]),
                use_batch_norm=self.model_params.get('use_batch_norm', True),
                activation=self.model_params.get('activation', 'gelu')
            ).to(self.device)

        self.model.eval()

    def _create_model_from_state_dict(self, state_dict, input_dim):
        """从预训练模型的状态字典创建精确匹配的模型结构"""
        import torch.nn as nn

        # 分析状态字典来推断精确的模型结构
        layer_info = {}

        # 收集所有层的信息
        for key in state_dict.keys():
            parts = key.split('.')
            if len(parts) >= 2:
                layer_idx = int(parts[0])
                param_type = '.'.join(parts[1:])

                if layer_idx not in layer_info:
                    layer_info[layer_idx] = {}

                layer_info[layer_idx][param_type] = state_dict[key].shape

        # 按索引排序
        sorted_indices = sorted(layer_info.keys())

        print(f"检测到的层结构:")
        for idx in sorted_indices:
            print(f"  层 {idx}: {layer_info[idx]}")

        # 创建一个动态模型类
        class DynamicModel(nn.Module):
            def __init__(self):
                super().__init__()

                # 根据状态字典动态创建层
                for idx in sorted_indices:
                    info = layer_info[idx]

                    if 'weight' in info and len(info['weight']) == 2:
                        # 线性层
                        out_dim, in_dim = info['weight']
                        layer = nn.Linear(in_dim, out_dim)
                        setattr(self, str(idx), layer)

                    elif 'weight' in info and len(info['weight']) == 1:
                        # 批量归一化层
                        num_features = info['weight'][0]
                        layer = nn.BatchNorm1d(num_features)
                        setattr(self, str(idx), layer)

            def forward(self, x):
                # 按索引顺序执行前向传播
                for idx in sorted_indices:
                    if hasattr(self, str(idx)):
                        layer = getattr(self, str(idx))
                        x = layer(x)

                        # 添加激活函数（基于层类型和位置）
                        if isinstance(layer, nn.Linear):
                            # 如果不是最后一层，添加激活函数
                            if idx != sorted_indices[-1]:
                                # 检查下一层是否是BatchNorm
                                next_idx = None
                                for next_i in sorted_indices:
                                    if next_i > idx:
                                        next_idx = next_i
                                        break

                                if next_idx is not None and hasattr(self, str(next_idx)):
                                    next_layer = getattr(self, str(next_idx))
                                    if not isinstance(next_layer, nn.BatchNorm1d):
                                        # 使用GELU激活函数（根据配置）
                                        x = torch.nn.functional.gelu(x)
                                else:
                                    # 最后一层之前的线性层，添加激活
                                    x = torch.nn.functional.gelu(x)

                        elif isinstance(layer, nn.BatchNorm1d):
                            # BatchNorm后添加激活函数
                            x = torch.nn.functional.gelu(x)

                return x

        # 创建模型实例
        model = DynamicModel()

        return model.to(self.device)

    def predict(self, features):
        """
        进行预测

        Args:
            features: 输入特征 [batch_size, input_dim] 或 [input_dim]

        Returns:
            预测结果
        """
        # 处理特征数据类型和格式
        if not isinstance(features, torch.Tensor):
            # 如果是numpy数组，先进行数据清理
            if hasattr(features, 'dtype') and features.dtype == object:
                print(f"警告: 检测到object类型数据，尝试转换为数值类型")
                import pandas as pd
                import numpy as np

                # 转换为DataFrame进行数据清理
                if features.ndim == 1:
                    df = pd.DataFrame(features.reshape(1, -1))
                else:
                    df = pd.DataFrame(features)

                # 尝试转换为数值类型
                for col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')

                # 填充NaN值
                df = df.fillna(0.0)

                # 转换回numpy数组
                features = df.values.astype(np.float32)
                print(f"数据清理后形状: {features.shape}, 类型: {features.dtype}")

            # 转换为张量
            try:
                features = torch.tensor(features, dtype=torch.float32)
            except Exception as e:
                print(f"张量转换失败: {e}")
                print(f"特征类型: {type(features)}, 形状: {getattr(features, 'shape', 'unknown')}")
                if hasattr(features, 'dtype'):
                    print(f"数据类型: {features.dtype}")
                raise ValueError(f"无法将特征转换为张量: {e}")

        if features.dim() == 1:
            features = features.unsqueeze(0)

        features = features.to(self.device)

        with torch.no_grad():
            output = self.model(features)
            return output.cpu().numpy()
    
    def __call__(self, x):
        """使包装器可调用"""
        return self.model(x)
    
    def eval(self):
        """设置为评估模式"""
        self.model.eval()
        return self
    
    def to(self, device):
        """移动到指定设备"""
        self.device = torch.device(device)
        self.model.to(self.device)
        return self


def check_model_compatibility(model_path: str) -> bool:
    """
    检查模型兼容性
    
    Args:
        model_path: 模型文件路径
        
    Returns:
        是否兼容简单模型格式
    """
    if not os.path.exists(model_path):
        return False
    
    try:
        # 尝试加载模型
        state_dict = torch.load(model_path, map_location='cpu')
        
        # 检查是否是简单的序列模型
        keys = list(state_dict.keys())
        has_numeric_keys = any(key.split('.')[0].isdigit() for key in keys)
        has_complex_keys = any('phylo_branch' in key or 'pathway_branch' in key for key in keys)
        
        if has_numeric_keys and not has_complex_keys:
            print("✅ 检测到简单序列模型，兼容")
            return True
        elif has_complex_keys:
            print("❌ 检测到复杂多分支模型，不兼容")
            return False
        else:
            print("❓ 未知模型类型")
            return False
            
    except Exception as e:
        print(f"❌ 模型兼容性检查失败: {e}")
        return False


def create_legacy_compatible_model(model_path: str, 
                                 multi_task: bool = False,
                                 device: str = 'cpu') -> LegacyModelWrapper:
    """
    创建与遗留模型兼容的包装器
    
    Args:
        model_path: 预训练模型路径
        multi_task: 是否多任务（当前忽略，因为预训练模型是单任务）
        device: 计算设备
        
    Returns:
        模型包装器实例
    """
    if multi_task:
        warnings.warn("预训练模型不支持多任务学习，将使用单任务模式")
    
    return LegacyModelWrapper(model_path, device)


def get_model_info(model_path: str) -> Dict[str, Any]:
    """
    获取模型信息
    
    Args:
        model_path: 模型文件路径
        
    Returns:
        模型信息字典
    """
    info = {
        'path': model_path,
        'exists': os.path.exists(model_path),
        'size_mb': 0,
        'compatible': False,
        'num_parameters': 0,
        'parameter_keys': []
    }
    
    if info['exists']:
        # 文件大小
        info['size_mb'] = os.path.getsize(model_path) / (1024 * 1024)
        
        try:
            # 加载模型检查兼容性
            state_dict = torch.load(model_path, map_location='cpu')
            info['num_parameters'] = len(state_dict)
            info['parameter_keys'] = list(state_dict.keys())[:10]  # 前10个键
            info['compatible'] = check_model_compatibility(model_path)
            
        except Exception as e:
            info['error'] = str(e)
    
    return info


if __name__ == "__main__":
    # 测试兼容性
    model_path = "models/deepmu_final_model/growth_model.pt"
    
    print("🔍 检查模型信息...")
    info = get_model_info(model_path)
    
    print(f"📁 模型路径: {info['path']}")
    print(f"📊 文件大小: {info['size_mb']:.2f} MB")
    print(f"🔢 参数数量: {info['num_parameters']}")
    print(f"🔑 参数键示例: {info['parameter_keys']}")
    print(f"✅ 兼容性: {info['compatible']}")
    
    if info['compatible']:
        print("\n🚀 创建兼容模型...")
        try:
            wrapper = create_legacy_compatible_model(model_path)
            print("✅ 成功创建兼容模型包装器")
            
        except Exception as e:
            print(f"❌ 创建模型失败: {e}")
    else:
        print("\n❌ 模型不兼容，需要使用复杂模型架构")
