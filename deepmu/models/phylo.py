"""
Enhanced phylogenetic embedding models.

This module implements enhanced phylogenetic models for DeepMu, including
hierarchical taxonomy embeddings and KO profile similarity calculations.

Note: The KOSimilarityCalculator class used in this module is imported from
deepmu.features.ko_similarity, which is the preferred and more feature-complete
implementation. Direct imports of KOSimilarityCalculator should be made from that
module for consistency and to leverage the enhanced feature set.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Set, Tuple, Optional
from scipy.sparse import csr_matrix
import math

# Import the enhanced KOSimilarityCalculator
from deepmu.features.ko_similarity import KOSimilarityCalculator


class TaxonomicEmbedder(nn.Module):
    """
    Enhanced hierarchical taxonomic embedder.

    This class embeds taxonomic IDs at multiple levels of the taxonomy
    (phylum, class, order, family, genus) and combines them using
    multi-head attention for better representation learning.
    """

    def __init__(self,
                level_vocab_sizes: Dict[str, int],
                embed_dim: int = 32,
                use_attention: bool = True,
                num_heads: int = 4,
                dropout: float = 0.1):
        """
        Initialize the taxonomic embedder.

        Args:
            level_vocab_sizes: Dictionary mapping taxonomy levels to vocabulary sizes.
                            Example: {"phylum": 100, "class": 500, ...}
            embed_dim: Dimension of embeddings for each level.
            use_attention: Whether to use attention for combining level embeddings.
            num_heads: Number of attention heads for multi-head attention.
            dropout: Dropout rate for attention mechanism.
        """
        super().__init__()
        self.embed_dim = embed_dim
        self.use_attention = use_attention
        self.taxonomy_levels = ["phylum", "class", "order", "family", "genus"]

        # Create embedding layers for each taxonomy level with padding_idx=0
        self.embeddings = nn.ModuleDict({
            level: nn.Embedding(vocab_size, embed_dim, padding_idx=0)
            for level, vocab_size in level_vocab_sizes.items()
            if level in self.taxonomy_levels
        })

        # Level-specific importance weights (learnable) - initialize this regardless of attention use
        self.level_importance = nn.Parameter(torch.ones(len(self.taxonomy_levels)) / len(self.taxonomy_levels))

        # Enhanced attention mechanism
        if use_attention:
            if embed_dim % num_heads != 0:
                # Ensure embed_dim is divisible by num_heads
                adjusted_dim = (embed_dim // num_heads) * num_heads
                print(f"Warning: embed_dim {embed_dim} not divisible by num_heads {num_heads}. "
                      f"Using adjusted dimension {adjusted_dim}.")
                embed_dim = adjusted_dim

            # Multi-head attention for better representation learning
            # 注意：移除 batch_first=True 以兼容较旧版本的 PyTorch
            self.multihead_attn = nn.MultiheadAttention(
                embed_dim=embed_dim,
                num_heads=num_heads,
                dropout=dropout
            )

            # Layer normalization for stability
            self.layer_norm = nn.LayerNorm(embed_dim)

    def forward(self, tax_data: Dict[str, torch.LongTensor]) -> torch.Tensor:
        """
        Forward pass through the embedder.

        Args:
            tax_data: Dictionary mapping taxonomy levels to tensors of taxonomy IDs.
                    Each tensor has shape [batch_size].

        Returns:
            Combined embeddings tensor of shape [batch_size, embed_dim]
        """
        all_embeddings = []
        valid_levels = []

        # Get device from first tensor in tax_data
        device = next(iter(tax_data.values())).device if tax_data else torch.device('cpu')

        # Embed each taxonomy level
        for i, level in enumerate(self.taxonomy_levels):
            if level in tax_data and level in self.embeddings:
                try:
                    level_embed = self.embeddings[level](tax_data[level])
                    all_embeddings.append(level_embed)
                    valid_levels.append(i)
                except Exception as e:
                    print(f"Error embedding level {level}: {e}")
                    # Skip this level if there's an error
            elif level in self.embeddings:
                # Handle missing taxonomy levels with a zero tensor
                batch_size = next(iter(tax_data.values())).size(0)
                zero_embed = torch.zeros(batch_size, self.embed_dim, device=device)
                all_embeddings.append(zero_embed)
                valid_levels.append(i)

        if not all_embeddings:
            # No valid embeddings, return zeros
            batch_size = next(iter(tax_data.values())).size(0) if tax_data else 1
            return torch.zeros(batch_size, self.embed_dim, device=device)

        if self.use_attention and len(all_embeddings) > 1:
            try:
                # Stack embeddings for attention
                stacked = torch.stack(all_embeddings, dim=1)  # [batch_size, n_levels, embed_dim]

                # Reshape for multi-head attention if needed
                batch_size, n_levels, embed_dim = stacked.size()

                # First try with multi-head attention
                try:
                    # Apply multi-head attention (self-attention across taxonomy levels)
                    # Reshape if needed to match expected dimensions
                    if stacked.dim() > 3:
                        # Flatten batch dimensions if needed
                        stacked_reshaped = stacked.view(-1, n_levels, embed_dim)
                    else:
                        stacked_reshaped = stacked

                    # 转换为 MultiheadAttention 期望的格式 [seq_len, batch_size, embed_dim]
                    stacked_transposed = stacked_reshaped.transpose(0, 1)

                    attn_output, attn_weights = self.multihead_attn(
                        query=stacked_transposed,
                        key=stacked_transposed,
                        value=stacked_transposed
                    )

                    # 转换回 [batch_size, seq_len, embed_dim] 格式
                    attn_output = attn_output.transpose(0, 1)

                    # Reshape back if needed
                    if stacked.dim() > 3:
                        attn_output = attn_output.view(batch_size, n_levels, embed_dim)

                    # Apply layer normalization with residual connection
                    normalized = self.layer_norm(attn_output + stacked)

                    # Apply level importance weights
                    importance = F.softmax(self.level_importance[valid_levels], dim=0)
                    weighted = normalized * importance.view(1, -1, 1)

                    # Sum across levels
                    combined = weighted.sum(dim=1)  # [batch_size, embed_dim]
                    return combined

                except Exception as e:
                    # If attention fails, fall back to weighted average
                    print(f"Multi-head attention failed: {e}. Using weighted average instead.")
                    importance = F.softmax(self.level_importance[valid_levels], dim=0)
                    weighted = stacked * importance.view(1, -1, 1)
                    combined = weighted.sum(dim=1)  # [batch_size, embed_dim]
                    return combined

            except Exception as e:
                # Fallback to simple averaging if all else fails
                print(f"Attention mechanism completely failed: {e}. Using simple averaging instead.")
                stacked = torch.stack(all_embeddings, dim=0)  # [n_levels, batch_size, embed_dim]
                combined = torch.mean(stacked, dim=0)  # [batch_size, embed_dim]
                return combined
        elif len(all_embeddings) > 1:
            # Fallback to weighted average if attention is disabled
            stacked = torch.stack(all_embeddings, dim=1)  # [batch_size, n_levels, embed_dim]
            importance = F.softmax(self.level_importance[valid_levels], dim=0)
            weighted = stacked * importance.view(1, -1, 1)
            return weighted.sum(dim=1)  # [batch_size, embed_dim]
        else:
            # Only one level available
            return all_embeddings[0]  # [batch_size, embed_dim]


class HierarchicalGNN(nn.Module):
    """
    Hierarchical Graph Neural Network for taxonomy and KO profile modeling.

    This class integrates taxonomy embeddings with KO profile similarity using
    a Graph Neural Network (GNN) architecture. It supports weighted Jaccard
    similarity calculations for KO profiles, allowing for precise control over 
    the importance of specific KO terms based on biological significance.
    
    The GNN builds a graph where nodes are genomes and edges represent both
    taxonomic and functional (KO profile) similarity. Edge weights are computed
    using a weighted combination of taxonomy embeddings and KO similarity scores.
    """

    def __init__(self,
                taxonomy_vocab_sizes: Dict[str, int],
                tax_embed_dim: int = 32,
                hidden_dim: int = 128,
                output_dim: int = 128,
                ko_weight: float = 0.6,
                use_attention: bool = True,
                dropout: float = 0.3):
        """
        Initialize the hierarchical GNN.

        Args:
            taxonomy_vocab_sizes: Dictionary mapping taxonomy levels to vocabulary sizes
            tax_embed_dim: Dimension of taxonomy embeddings
            hidden_dim: Dimension of hidden layers
            output_dim: Output dimension
            ko_weight: Weight of KO similarity vs. taxonomy similarity (0-1)
            use_attention: Whether to use attention for taxonomy embeddings
            dropout: Dropout rate
        """
        super().__init__()

        # Save parameters
        self.ko_weight = ko_weight

        # Create taxonomy embedder
        self.tax_embedder = TaxonomicEmbedder(
            level_vocab_sizes=taxonomy_vocab_sizes,
            embed_dim=tax_embed_dim,
            use_attention=use_attention
        )

        # GNN layers
        self.gnn_layer1 = nn.Sequential(
            nn.Linear(tax_embed_dim, hidden_dim),
            nn.ReLU(),
            nn.LayerNorm(hidden_dim)
        )

        self.gnn_layer2 = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.LayerNorm(hidden_dim)
        )

        # Residual connection with projection for different dimensions
        self.res_proj = nn.Linear(tax_embed_dim, hidden_dim)

        # Gating mechanism
        self.gate = nn.Sequential(
            nn.Linear(tax_embed_dim + hidden_dim, hidden_dim),
            nn.Sigmoid()
        )

        # Output layer
        self.output_layer = nn.Linear(hidden_dim, output_dim)

        # Dropout for regularization
        self.dropout = nn.Dropout(dropout)
        
        # Multi-head attention parameters
        self.num_heads = 4
        head_dim = hidden_dim // self.num_heads
        self.head_dim = max(1, head_dim)  # Ensure head_dim is at least 1
        
        # Query, key, value projections for attention
        self.query_proj = nn.Linear(hidden_dim, hidden_dim)
        self.key_proj = nn.Linear(hidden_dim, hidden_dim)
        self.value_proj = nn.Linear(hidden_dim, hidden_dim)
        
        # Output projection for attention
        self.attn_out = nn.Linear(hidden_dim, hidden_dim)

    def _build_similarity_graph(self,
                               tax_embeddings: torch.Tensor,
                               ko_sim_matrix: torch.Tensor,
                               k: int = 5) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Build a k-NN graph based on combined similarity with enhanced robustness.

        This improved implementation handles edge cases better and provides more
        robust graph construction with fallback mechanisms.

        Args:
            tax_embeddings: Taxonomy embeddings [batch_size, embed_dim]
            ko_sim_matrix: KO similarity matrix [batch_size, batch_size]
            k: Number of neighbors per node

        Returns:
            Tuple of (edge_index, edge_attr) for the graph
        """
        batch_size = tax_embeddings.size(0)
        device = tax_embeddings.device

        # Ensure valid k value (can't be larger than batch_size-1)
        k = min(k, max(1, batch_size - 1))

        try:
            # Calculate taxonomy similarity with better numerical stability
            # Normalize embeddings first to improve numerical stability
            tax_norm = F.normalize(tax_embeddings, p=2, dim=1)
            tax_sim = torch.mm(tax_norm, tax_norm.t())  # [batch_size, batch_size]
        except Exception as e:
            print(f"Error calculating taxonomy similarity: {e}")
            # Fallback to identity matrix
            tax_sim = torch.eye(batch_size, device=device)

        # Ensure KO similarity matrix has correct shape
        if ko_sim_matrix.size(0) != batch_size or ko_sim_matrix.size(1) != batch_size:
            print(f"Warning: KO similarity matrix shape {ko_sim_matrix.shape} doesn't match batch size {batch_size}")
            # Create a fallback similarity matrix if shapes don't match
            ko_sim_matrix = torch.eye(batch_size, device=device)

        # Combine similarities with adaptive weighting
        # If KO matrix has low values, rely more on taxonomy
        ko_mean = ko_sim_matrix.mean().item()
        adaptive_weight = self.ko_weight * min(1.0, max(0.1, ko_mean * 2))
        combined_sim = adaptive_weight * ko_sim_matrix + (1 - adaptive_weight) * tax_sim

        # Ensure self-similarity is maximum (1.0)
        combined_sim.fill_diagonal_(1.0)

        # Build edge lists directly
        edge_index = []
        edge_attr = []

        # Special case for batch_size=1
        if batch_size == 1:
            return torch.tensor([[0], [0]], device=device), torch.tensor([[1.0]], device=device)

        # Process each node to find its neighbors
        for i in range(batch_size):
            # Always add a self-loop with maximum similarity for stability
            edge_index.append([i, i])
            edge_attr.append([1.0])

            # Get top k similarities, excluding self
            sim_row = combined_sim[i].clone()
            sim_row[i] = -1.0  # Exclude self from topk

            try:
                # Get top k similarities
                similarities, indices = sim_row.topk(k)

                # Add edges for top k neighbors
                for j in range(len(indices)):
                    neighbor = indices[j].item()
                    sim = similarities[j].item()

                    # Only add if similarity is above threshold
                    if sim > 0.1:
                        edge_index.append([i, neighbor])
                        edge_attr.append([sim])
            except Exception as e:
                print(f"Error in topk for node {i}: {e}")
                # Fallback: connect to all other nodes with similarity values
                for j in range(batch_size):
                    if i != j:
                        sim = combined_sim[i, j].item()
                        if sim > 0.1:
                            edge_index.append([i, j])
                            edge_attr.append([sim])

        # Convert to tensors safely
        try:
            edge_index_tensor = torch.tensor(edge_index, device=device)
            edge_attr_tensor = torch.tensor(edge_attr, device=device)
            
            # Ensure edge_index has shape [2, num_edges]
            if edge_index_tensor.dim() == 2 and edge_index_tensor.size(1) == 2:
                edge_index_tensor = edge_index_tensor.t()
                
            return edge_index_tensor, edge_attr_tensor
        except Exception as e:
            print(f"Error converting edge indices to tensor: {e}")
            # Create a minimal fallback graph (self-loops)
            indices = torch.arange(batch_size, device=device)
            edge_index = torch.stack([indices, indices], dim=0)
            edge_attr = torch.ones(batch_size, 1, device=device)
            return edge_index, edge_attr

    def _message_passing(self,
                        x: torch.Tensor,
                        edge_index: torch.Tensor,
                        edge_attr: torch.Tensor) -> torch.Tensor:
        """
        Perform message passing on the graph with improved robustness.
        
        This version handles varying batch sizes and tensor shapes more effectively.

        Args:
            x: Node features [batch_size, feature_dim]
            edge_index: Edges [2, num_edges]
            edge_attr: Edge weights [num_edges, 1]

        Returns:
            Updated node features
        """
        # Reshape tensors to ensure compatibility
        batch_size = x.size(0)
        
        # Check if edge_index is empty
        if edge_index.numel() == 0:
            # No edges, return the input unchanged
            return x
            
        # Ensure edge_attr has the right shape
        if edge_attr.dim() == 1:
            edge_attr = edge_attr.unsqueeze(1)
            
        if edge_attr.size(0) != edge_index.size(1):
            # Mismatch between edge_attr and edge_index
            if edge_attr.size(0) == 1:
                # Broadcast edge_attr
                edge_attr = edge_attr.expand(edge_index.size(1), -1)
            else:
                # Truncate or pad edge_attr
                new_edge_attr = torch.ones(edge_index.size(1), 1, device=x.device)
                min_size = min(edge_attr.size(0), edge_index.size(1))
                new_edge_attr[:min_size] = edge_attr[:min_size]
                edge_attr = new_edge_attr
        
        try:
            source, target = edge_index
            
            # Create output tensor
            out = x.clone()
            
            # Track updated nodes to avoid duplicate processing
            updated_nodes = set()
            
            for idx in range(edge_index.size(1)):
                src = source[idx].item()
                tgt = target[idx].item()
                
                # Skip if source or target is out of bounds
                if src >= batch_size or tgt >= batch_size or src < 0 or tgt < 0:
                    continue
                
                # Get the edge weight
                weight = edge_attr[idx].item()
                
                # Update the target node with weighted message from source
                message = x[src] * weight
                
                if tgt not in updated_nodes:
                    out[tgt] = out[tgt] + message
                    updated_nodes.add(tgt)
                else:
                    # If node already updated, average
                    out[tgt] = (out[tgt] + message) / 2
            
            return out
            
        except Exception as e:
            print(f"Error in message passing: {e}")
            return x  # Return input unchanged on error

    def _attention_message_passing(self, x: torch.Tensor, edge_index: torch.Tensor, edge_attr: torch.Tensor) -> torch.Tensor:
        """
        Perform message passing with multi-head attention.
        
        Args:
            x: Node features [batch_size, feature_dim]
            edge_index: Edges [2, num_edges]
            edge_attr: Edge weights [num_edges, 1]
            
        Returns:
            Updated node features
        """
        batch_size = x.size(0)
        hidden_dim = x.size(1)
        device = x.device
        
        if edge_index.numel() == 0 or batch_size <= 1:
            return x
            
        try:
            # Project features to query, key, value
            query = self.query_proj(x).view(batch_size, self.num_heads, self.head_dim)
            key = self.key_proj(x).view(batch_size, self.num_heads, self.head_dim)
            value = self.value_proj(x).view(batch_size, self.num_heads, self.head_dim)
            
            # Extract source and target indices
            source, target = edge_index
            
            # Initialize output tensor
            out = torch.zeros_like(x)
            
            # Process each node
            for i in range(batch_size):
                # Find neighbors (sources where this node is the target)
                mask = (target == i)
                if not mask.any():
                    out[i] = x[i]  # No neighbors, keep original
                    continue
                    
                # Extract neighbor indices and weights
                neighbors = source[mask]
                weights = edge_attr[mask]
                
                # Skip if no valid neighbors
                if neighbors.numel() == 0:
                    out[i] = x[i]
                    continue
                
                # Extract query, keys and values for this node and its neighbors
                q = query[i:i+1]  # [1, num_heads, head_dim]
                k = key[neighbors]  # [num_neighbors, num_heads, head_dim]
                v = value[neighbors]  # [num_neighbors, num_heads, head_dim]
                
                # Calculate attention scores
                scores = torch.bmm(q, k.transpose(1, 2)) / math.sqrt(self.head_dim)  # [1, num_heads, num_neighbors]
                
                # Apply edge weights to attention scores
                if weights.dim() == 1:
                    weights = weights.unsqueeze(0).unsqueeze(0)  # [1, 1, num_neighbors]
                else:
                    weights = weights.t().unsqueeze(0)  # [1, 1, num_neighbors]
                    
                # Scale attention scores by edge weights
                scores = scores * weights
                
                # Apply softmax to get attention weights
                attn_weights = F.softmax(scores, dim=2)  # [1, num_heads, num_neighbors]
                
                # Apply attention weights to values
                attended = torch.bmm(attn_weights, v)  # [1, num_heads, head_dim]
                
                # Reshape and store result
                attended = attended.view(1, hidden_dim)
                out[i] = attended.squeeze(0)
                
            return out
        except Exception as e:
            print(f"Error in attention message passing: {e}")
            return x  # Return input unchanged on error

    def forward(self, tax_data: Dict[str, torch.Tensor], ko_data: Dict[str, any]) -> torch.Tensor:
        """
        Forward pass through the GNN.

        Args:
            tax_data: Dictionary mapping taxonomy levels to tensors of taxonomy IDs
            ko_data: Dictionary containing KO information:
                - 'ko_sets': List of sets of KO IDs for each genome
                - 'sim_matrix': Optional pre-computed similarity matrix
                - 'ko_weights': Optional dictionary of KO weights for weighted similarity

        Returns:
            Output features [batch_size, output_dim]
        """
        # Get taxonomy embeddings from tax_data
        tax_embeddings = self.tax_embedder(tax_data)  # [batch_size, embed_dim]

        # Calculate or use provided KO similarity matrix
        ko_sim_matrix = None
        batch_size = tax_embeddings.size(0)
        device = tax_embeddings.device

        # Handle different types of ko_data
        if isinstance(ko_data, dict):
            # Dictionary case
            if 'sim_matrix' in ko_data and isinstance(ko_data['sim_matrix'], torch.Tensor):
                # Use pre-computed similarity matrix
                sim_matrix = ko_data['sim_matrix']

                # Check if the shape matches the batch size
                if sim_matrix.size(0) == batch_size and sim_matrix.size(1) == batch_size:
                    # Perfect match
                    ko_sim_matrix = sim_matrix
                elif sim_matrix.size(0) == batch_size and sim_matrix.size(1) == 1:
                    # We have a vector of similarities, expand it to a matrix
                    print(f"Expanding KO similarity vector to matrix for batch size {batch_size}")
                    ko_sim_matrix = torch.zeros((batch_size, batch_size), device=device)
                    for i in range(batch_size):
                        # Self-similarity is 1.0
                        ko_sim_matrix[i, i] = 1.0
                        # Use the similarity vector for other similarities
                        for j in range(batch_size):
                            if i != j:
                                # Use the average of the two similarities
                                ko_sim_matrix[i, j] = (sim_matrix[i, 0] + sim_matrix[j, 0]) / 2.0
                else:
                    # Shape mismatch, create a default matrix
                    print(f"Warning: KO similarity matrix shape {sim_matrix.shape} doesn't match batch size {batch_size}")
                    ko_sim_matrix = torch.eye(batch_size, device=device)  # Identity matrix (self-similarity only)
            elif 'ko_sets' in ko_data and isinstance(ko_data['ko_sets'], list):
                # Calculate similarity matrix from KO sets
                ko_sim_matrix = torch.zeros((batch_size, batch_size), device=device)
        elif isinstance(ko_data, torch.Tensor):
            # Tensor case - assume it's the similarity matrix
            sim_matrix = ko_data

            # Check if the shape matches the batch size
            if sim_matrix.size(0) == batch_size and sim_matrix.size(1) == batch_size:
                # Perfect match
                ko_sim_matrix = sim_matrix
            elif sim_matrix.size(0) == batch_size and sim_matrix.size(1) == 1:
                # We have a vector of similarities, expand it to a matrix
                print(f"Expanding KO similarity vector to matrix for batch size {batch_size}")
                ko_sim_matrix = torch.zeros((batch_size, batch_size), device=device)
                for i in range(batch_size):
                    # Self-similarity is 1.0
                    ko_sim_matrix[i, i] = 1.0
                    # Use the similarity vector for other similarities
                    for j in range(batch_size):
                        if i != j:
                            # Use the average of the two similarities
                            ko_sim_matrix[i, j] = (sim_matrix[i, 0] + sim_matrix[j, 0]) / 2.0
            else:
                # Shape mismatch, create a default matrix
                print(f"Warning: KO similarity matrix shape {sim_matrix.shape} doesn't match batch size {batch_size}")
                ko_sim_matrix = torch.eye(batch_size, device=device)  # Identity matrix (self-similarity only)

        # If we still don't have a similarity matrix, create a default one
        if ko_sim_matrix is None:
            ko_sim_matrix = torch.eye(batch_size, device=device)  # Identity matrix (self-similarity only)

            # Process KO sets if available
            if isinstance(ko_data, dict):
                ko_sets = ko_data.get('ko_sets', [])
                ko_weights = ko_data.get('ko_weights', None)
                
                if ko_sets and len(ko_sets) > 0:
                    # Create a KO similarity calculator for weighted calculations
                    ko_calculator = KOSimilarityCalculator(use_weighted_similarity=True)
                    
                    # Fill similarity matrix
                    for i in range(min(batch_size, len(ko_sets))):
                        # Self-similarity is 1.0
                        ko_sim_matrix[i, i] = 1.0

                    # Calculate similarity with other genomes
                    for j in range(i + 1, min(batch_size, len(ko_sets))):
                        set_i = ko_sets[i]
                        set_j = ko_sets[j]

                        if set_i and set_j:  # Both sets must be non-empty
                            # Use weighted Jaccard similarity if weights are provided
                            sim = ko_calculator.weighted_jaccard_similarity(set_i, set_j, ko_weights)
                        else:
                            sim = 0.0

                        # Similarity matrix is symmetric
                        ko_sim_matrix[i, j] = sim
                        ko_sim_matrix[j, i] = sim

        # Build graph from embeddings and similarity matrix
        try:
            edge_index, edge_attr = self._build_similarity_graph(
                tax_embeddings, ko_sim_matrix
            )
        except Exception as e:
            # Fallback to a simple fully-connected graph if there's an error
            print(f"Error building similarity graph: {e}. Using fallback.")
            edge_index = torch.tensor([
                [i for i in range(batch_size) for _ in range(batch_size)],  # sources
                [j for _ in range(batch_size) for j in range(batch_size)]   # targets
            ], device=device)
            edge_attr = torch.ones(batch_size * batch_size, 1, device=device)

        # Initial node features are the taxonomy embeddings
        h0 = tax_embeddings  # [batch_size, embed_dim]

        # First GNN layer - feed through the sequential layer
        h1 = self.gnn_layer1(h0)  # [batch_size, hidden_dim]
        h1 = F.relu(h1)
        h1 = self.dropout(h1)
        h1 = self._message_passing(h1, edge_index, edge_attr)

        # Second GNN layer - feed through the sequential layer
        h2 = self.gnn_layer2(h1)  # [batch_size, hidden_dim]
        h2 = F.relu(h2)
        h2 = self.dropout(h2)

        # Apply residual connection with projection
        h2 = h2 + self.res_proj(h0)

        # Try attention-based message passing for the second round
        try:
            h2 = self._attention_message_passing(h2, edge_index, edge_attr)
        except Exception as e:
            print(f"Error in attention message passing: {e}. Falling back to standard message passing.")
            h2 = self._message_passing(h2, edge_index, edge_attr)

        # Adaptive gating mechanism
        gate = self.gate(torch.cat([h0, h2], dim=1))  # [batch_size, hidden_dim]

        # Combine features with gating (no extra gnn_layer1 call needed)
        gated = gate * h2 + (1 - gate) * h1  # [batch_size, hidden_dim]

        # Output projection
        output = self.output_layer(gated)  # [batch_size, output_dim]

        return output


class EnhancedPhyloBranch(nn.Module):
    """
    Enhanced phylogenetic branch for DeepMu.

    This class replaces the original phylogenetic branch with a more
    sophisticated model that uses hierarchical taxonomy and KO profile
    similarity for better phylogenetic modeling.
    """

    def __init__(self,
                taxonomy_vocab_sizes: Dict[str, int],
                output_dim: int = 128,
                tax_embed_dim: int = 32,
                hidden_dim: int = 128,
                ko_weight: float = 0.6,
                use_attention: bool = True,
                use_residual_mlp: bool = True,
                dropout: float = 0.3):
        """
        Initialize the enhanced phylogenetic branch.

        Args:
            taxonomy_vocab_sizes: Dictionary mapping taxonomy levels to vocabulary sizes
            output_dim: Output dimension
            tax_embed_dim: Dimension of taxonomy embeddings
            hidden_dim: Dimension of hidden GNN layers
            ko_weight: Weight of KO similarity vs. taxonomy similarity (0-1)
            use_attention: Whether to use attention for taxonomy embeddings
            use_residual_mlp: Whether to use a residual MLP
            dropout: Dropout rate
        """
        super().__init__()

        # Store the ko_weight as an instance attribute
        self.ko_weight = ko_weight

        # Hierarchical GNN
        self.gnn = HierarchicalGNN(
            taxonomy_vocab_sizes=taxonomy_vocab_sizes,
            tax_embed_dim=tax_embed_dim,
            hidden_dim=hidden_dim,
            output_dim=hidden_dim,
            ko_weight=ko_weight,
            use_attention=use_attention,
            dropout=dropout
        )

        # Residual MLP
        self.use_residual_mlp = use_residual_mlp
        if use_residual_mlp:
            self.mlp = nn.Sequential(
                nn.Linear(hidden_dim, hidden_dim * 2),
                nn.ReLU(),
                nn.LayerNorm(hidden_dim * 2),
                nn.Dropout(dropout),
                nn.Linear(hidden_dim * 2, output_dim)
            )
            self.skip = nn.Linear(hidden_dim, output_dim)
        else:
            self.final = nn.Linear(hidden_dim, output_dim)

    def forward(self, taxonomy_data: Dict[str, torch.Tensor], ko_data: Dict[str, any]) -> torch.Tensor:
        """
        Forward pass through the enhanced phylogenetic branch.

        Args:
            taxonomy_data: Dictionary mapping taxonomy levels to tensors of taxonomy IDs
            ko_data: Dictionary containing KO information:
                - 'ko_sets': List of sets of KO IDs for each genome
                - 'sim_matrix': Optional pre-computed similarity matrix
                - 'ko_weights': Optional dictionary of KO weights for weighted similarity

        Returns:
            Output features [batch_size, output_dim]
        """
        try:
            # Process through GNN
            gnn_output = self.gnn(taxonomy_data, ko_data)

            # Apply residual MLP if enabled
            if self.use_residual_mlp:
                return self.mlp(gnn_output) + self.skip(gnn_output)
            else:
                return self.final(gnn_output)
        except Exception as e:
            print(f"Error in EnhancedPhyloBranch forward pass: {e}")
            # Return zeros as a fallback
            batch_size = next(iter(taxonomy_data.values())).size(0) if taxonomy_data else 1
            device = next(iter(taxonomy_data.values())).device if taxonomy_data else torch.device('cpu')
            output_dim = self.mlp[-1].out_features if self.use_residual_mlp else self.final.out_features
            return torch.zeros(batch_size, output_dim, device=device)


def build_taxonomy_vocab(taxonomy_dicts: List[Dict[str, str]]) -> Dict[str, Dict[str, int]]:
    """
    Build vocabulary dictionaries for taxonomy levels.

    Args:
        taxonomy_dicts: List of taxonomy dictionaries, each mapping levels to IDs

    Returns:
        Dictionary mapping taxonomy levels to vocabulary dictionaries (ID -> index)
    """
    # Initialize vocabularies
    vocab_dicts = {
        level: {"<pad>": 0, "<unk>": 1}
        for level in ["phylum", "class", "order", "family", "genus"]
    }

    # Collect all unique IDs for each level
    for tax_dict in taxonomy_dicts:
        for level, tax_id in tax_dict.items():
            if level in vocab_dicts and tax_id not in vocab_dicts[level]:
                vocab_dicts[level][tax_id] = len(vocab_dicts[level])

    # Convert to vocabulary sizes
    vocab_sizes = {level: len(vocab) for level, vocab in vocab_dicts.items()}

    return vocab_sizes