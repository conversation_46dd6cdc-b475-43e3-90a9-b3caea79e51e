#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import time
import argparse
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
from sklearn.metrics import roc_auc_score, accuracy_score, mean_squared_error, r2_score
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler

from tabnet_model import TabNet

def parse_args():
    parser = argparse.ArgumentParser(description='Train and evaluate TabNet model')
    
    # Data parameters
    parser.add_argument('--data_dir', type=str, default='data/processed', 
                        help='Directory containing processed data')
    parser.add_argument('--results_dir', type=str, default='results/tabnet', 
                        help='Directory to save results')
    
    # Model parameters
    parser.add_argument('--model_type', type=str, default='tabnet', 
                        choices=['tabnet'], help='Model type to use')
    parser.add_argument('--n_steps', type=int, default=3, 
                        help='Number of decision steps in TabNet')
    parser.add_argument('--n_d', type=int, default=64, 
                        help='Width of the decision prediction layer')
    parser.add_argument('--n_a', type=int, default=64, 
                        help='Width of the attention embedding')
    parser.add_argument('--n_shared', type=int, default=2, 
                        help='Number of shared GLU layers in Feature Transformer')
    parser.add_argument('--n_independent', type=int, default=2, 
                        help='Number of independent GLU layers in Feature Transformer')
    parser.add_argument('--gamma', type=float, default=1.3, 
                        help='Feature reusage coefficient (gamma > 1 for feature selection sparsity)')
    parser.add_argument('--mask_type', type=str, default='sparsemax', 
                        choices=['sparsemax', 'entmax15', 'softmax'], 
                        help='Type of mask activation to use')
    parser.add_argument('--lambda_sparse', type=float, default=1e-3, 
                        help='Coefficient for sparsity regularization')
    parser.add_argument('--virtual_batch_size', type=int, default=128, 
                        help='Size of virtual batches in Ghost Batch Normalization')
    parser.add_argument('--momentum', type=float, default=0.1, 
                        help='Momentum for batch normalization')
    
    # Training parameters
    parser.add_argument('--num_epochs', type=int, default=100, 
                        help='Number of training epochs')
    parser.add_argument('--batch_size', type=int, default=1024, 
                        help='Batch size for training')
    parser.add_argument('--learning_rate', type=float, default=0.01, 
                        help='Learning rate')
    parser.add_argument('--weight_decay', type=float, default=1e-5, 
                        help='Weight decay for regularization')
    parser.add_argument('--early_stopping_patience', type=int, default=10, 
                        help='Number of epochs to wait before early stopping')
    parser.add_argument('--use_gpu', action='store_true', 
                        help='Use GPU for training if available')
    parser.add_argument('--save_model', action='store_true', 
                        help='Save the best model')
    parser.add_argument('--verbose', action='store_true', 
                        help='Print verbose output')
    
    return parser.parse_args()

def load_data(data_dir):
    """
    Load and preprocess data.
    
    Args:
        data_dir (str): Directory containing processed data
        
    Returns:
        tuple: X_train, X_val, X_test, y_train, y_val, y_test
    """
    # Load data
    train_data = pd.read_csv(os.path.join(data_dir, 'train.csv'))
    test_data = pd.read_csv(os.path.join(data_dir, 'test.csv'))
    
    # Split features and target
    y_train = train_data['target'].values
    X_train = train_data.drop('target', axis=1).values
    
    y_test = test_data['target'].values
    X_test = test_data.drop('target', axis=1).values
    
    # Split train into train and validation
    X_train, X_val, y_train, y_val = train_test_split(
        X_train, y_train, test_size=0.2, random_state=42
    )
    
    # Scale features
    scaler = StandardScaler()
    X_train = scaler.fit_transform(X_train)
    X_val = scaler.transform(X_val)
    X_test = scaler.transform(X_test)
    
    return X_train, X_val, X_test, y_train, y_val, y_test

def create_dataloaders(X_train, X_val, X_test, y_train, y_val, y_test, batch_size):
    """
    Create PyTorch DataLoaders from numpy arrays.
    
    Args:
        X_train, X_val, X_test: Feature arrays
        y_train, y_val, y_test: Target arrays
        batch_size (int): Batch size
        
    Returns:
        tuple: train_loader, val_loader, test_loader
    """
    # Convert to tensors
    X_train_tensor = torch.FloatTensor(X_train)
    y_train_tensor = torch.FloatTensor(y_train)
    
    X_val_tensor = torch.FloatTensor(X_val)
    y_val_tensor = torch.FloatTensor(y_val)
    
    X_test_tensor = torch.FloatTensor(X_test)
    y_test_tensor = torch.FloatTensor(y_test)
    
    # Create datasets
    train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
    val_dataset = TensorDataset(X_val_tensor, y_val_tensor)
    test_dataset = TensorDataset(X_test_tensor, y_test_tensor)
    
    # Create dataloaders
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size)
    test_loader = DataLoader(test_dataset, batch_size=batch_size)
    
    return train_loader, val_loader, test_loader

def train(model, train_loader, val_loader, criterion, optimizer, device, args):
    """
    Train the model.
    
    Args:
        model: PyTorch model
        train_loader: Training data loader
        val_loader: Validation data loader
        criterion: Loss function
        optimizer: Optimizer
        device: Device to train on (CPU/GPU)
        args: Command line arguments
        
    Returns:
        tuple: Best model, training history
    """
    # Initialize variables
    best_val_loss = float('inf')
    best_model = None
    patience_counter = 0
    history = {
        'train_loss': [],
        'val_loss': [],
        'train_time': []
    }
    
    # Training loop
    for epoch in range(args.num_epochs):
        start_time = time.time()
        model.train()
        train_loss = 0.0
        
        for batch_X, batch_y in train_loader:
            batch_X, batch_y = batch_X.to(device), batch_y.to(device)
            batch_y = batch_y.view(-1, 1)  # Reshape for compatibility
            
            # Forward pass
            optimizer.zero_grad()
            outputs, masks, sparsity_loss = model(batch_X)
            
            # Compute loss (prediction loss + sparsity loss)
            pred_loss = criterion(outputs, batch_y)
            loss = pred_loss + sparsity_loss
            
            # Backward pass and optimize
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item() * batch_X.size(0)
        
        # Calculate average loss
        train_loss /= len(train_loader.dataset)
        train_time = time.time() - start_time
        
        # Validation
        model.eval()
        val_loss = 0.0
        
        with torch.no_grad():
            for batch_X, batch_y in val_loader:
                batch_X, batch_y = batch_X.to(device), batch_y.to(device)
                batch_y = batch_y.view(-1, 1)  # Reshape for compatibility
                
                outputs, _, sparsity_loss = model(batch_X)
                pred_loss = criterion(outputs, batch_y)
                loss = pred_loss + sparsity_loss
                
                val_loss += loss.item() * batch_X.size(0)
        
        val_loss /= len(val_loader.dataset)
        
        # Save history
        history['train_loss'].append(train_loss)
        history['val_loss'].append(val_loss)
        history['train_time'].append(train_time)
        
        # Print progress
        if args.verbose and (epoch % 10 == 0 or epoch == args.num_epochs - 1):
            print(f'Epoch {epoch+1}/{args.num_epochs} | '
                  f'Train Loss: {train_loss:.4f} | '
                  f'Val Loss: {val_loss:.4f} | '
                  f'Time: {train_time:.2f}s')
        
        # Check for improvement
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            best_model = model.state_dict().copy()
            patience_counter = 0
        else:
            patience_counter += 1
            
        # Early stopping
        if patience_counter >= args.early_stopping_patience:
            if args.verbose:
                print(f'Early stopping at epoch {epoch+1}')
            break
    
    return best_model, history

def evaluate(model, test_loader, criterion, device, is_classification=True):
    """
    Evaluate the model on test data.
    
    Args:
        model: PyTorch model
        test_loader: Test data loader
        criterion: Loss function
        device: Device to evaluate on
        is_classification: Whether this is a classification task
        
    Returns:
        dict: Dictionary of evaluation metrics
    """
    model.eval()
    test_loss = 0.0
    all_outputs = []
    all_targets = []
    
    with torch.no_grad():
        for batch_X, batch_y in test_loader:
            batch_X, batch_y = batch_X.to(device), batch_y.to(device)
            batch_y = batch_y.view(-1, 1)
            
            outputs, _, sparsity_loss = model(batch_X)
            pred_loss = criterion(outputs, batch_y)
            loss = pred_loss + sparsity_loss
            
            test_loss += loss.item() * batch_X.size(0)
            
            all_outputs.append(outputs.cpu().numpy())
            all_targets.append(batch_y.cpu().numpy())
    
    # Calculate metrics
    test_loss /= len(test_loader.dataset)
    all_outputs = np.vstack(all_outputs).flatten()
    all_targets = np.vstack(all_targets).flatten()
    
    metrics = {'test_loss': test_loss}
    
    if is_classification:
        # Classification metrics
        pred_probs = all_outputs
        pred_classes = (pred_probs > 0.5).astype(int)
        
        metrics['accuracy'] = accuracy_score(all_targets, pred_classes)
        metrics['auc'] = roc_auc_score(all_targets, pred_probs)
    else:
        # Regression metrics
        metrics['mse'] = mean_squared_error(all_targets, all_outputs)
        metrics['r2'] = r2_score(all_targets, all_outputs)
    
    return metrics

def main():
    args = parse_args()
    
    # Create results directory if it doesn't exist
    os.makedirs(args.results_dir, exist_ok=True)
    
    # Set device
    device = torch.device('cuda' if torch.cuda.is_available() and args.use_gpu else 'cpu')
    if args.verbose:
        print(f'Using device: {device}')
    
    # Load data
    X_train, X_val, X_test, y_train, y_val, y_test = load_data(args.data_dir)
    input_dim = X_train.shape[1]
    
    # Determine if it's a classification task
    is_classification = len(np.unique(y_train)) <= 5  # Arbitrary threshold
    output_dim = 1  # Binary classification or regression
    
    # Create dataloaders
    train_loader, val_loader, test_loader = create_dataloaders(
        X_train, X_val, X_test, y_train, y_val, y_test, args.batch_size
    )
    
    # Initialize model
    model = TabNet(
        input_dim=input_dim,
        output_dim=output_dim,
        n_steps=args.n_steps,
        n_d=args.n_d,
        n_a=args.n_a,
        n_shared=args.n_shared,
        n_independent=args.n_independent,
        gamma=args.gamma,
        mask_type=args.mask_type,
        lambda_sparse=args.lambda_sparse,
        virtual_batch_size=args.virtual_batch_size,
        momentum=args.momentum
    ).to(device)
    
    # Loss function and optimizer
    criterion = nn.BCEWithLogitsLoss() if is_classification else nn.MSELoss()
    optimizer = optim.Adam(model.parameters(), lr=args.learning_rate, weight_decay=args.weight_decay)
    
    # Train model
    if args.verbose:
        print("Starting training...")
    best_model, history = train(model, train_loader, val_loader, criterion, optimizer, device, args)
    
    # Load best model
    model.load_state_dict(best_model)
    
    # Evaluate on test set
    if args.verbose:
        print("Evaluating model...")
    test_metrics = evaluate(model, test_loader, criterion, device, is_classification)
    
    # Print results
    if args.verbose:
        print("\nTest metrics:")
        for metric, value in test_metrics.items():
            print(f"{metric}: {value:.4f}")
    
    # Save results
    results = {
        'args': vars(args),
        'history': history,
        'test_metrics': test_metrics
    }
    
    # Save model
    if args.save_model:
        model_path = os.path.join(args.results_dir, 'tabnet_model.pt')
        torch.save(model.state_dict(), model_path)
        if args.verbose:
            print(f"Model saved to {model_path}")
    
    # Save results to CSV
    results_path = os.path.join(args.results_dir, 'results.csv')
    pd.DataFrame([test_metrics]).to_csv(results_path, index=False)
    if args.verbose:
        print(f"Results saved to {results_path}")

if __name__ == '__main__':
    main() 