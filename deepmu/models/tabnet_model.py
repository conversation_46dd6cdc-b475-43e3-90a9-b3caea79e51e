#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
TabNet model implementation for the DeepMu project.

TabNet: Attentive Interpretable Tabular Learning
Paper: https://arxiv.org/abs/1908.07442
"""

import os
import copy
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import TensorDataset, DataLoader
import numpy as np
import logging
import sklearn.metrics
from deepmu.models.sparsemax import sparsemax, entmax15

from deepmu.utils.logging import get_logger

logger = get_logger()

class GLUBlock(nn.Module):
    """
    Gated Linear Unit (GLU) block used in TabNet.
    
    Applies a fully-connected layer followed by a GLU activation.
    """
    
    def __init__(self, input_dim, output_dim, virtual_batch_size=128, momentum=0.1):
        """
        Initialize GLU Block.
        
        Args:
            input_dim (int): Input dimension
            output_dim (int): Output dimension
            virtual_batch_size (int): Batch size for Ghost Batch Normalization
            momentum (float): Momentum for batch normalization
        """
        super(GLUBlock, self).__init__()
        
        self.output_dim = output_dim
        self.virtual_batch_size = virtual_batch_size
        
        # Linear layer (double width to account for GLU split)
        self.fc = nn.Linear(input_dim, output_dim * 2)
        
        # Ghost Batch Normalization
        self.bn = GhostBatchNorm(output_dim * 2, virtual_batch_size, momentum)
        
    def forward(self, x):
        """
        Forward pass of GLU Block.
        
        Args:
            x (torch.Tensor): Input tensor
            
        Returns:
            torch.Tensor: Output tensor after GLU activation
        """
        # Linear layer and batch normalization
        x = self.fc(x)
        x = self.bn(x)
        
        # GLU activation
        x_linear, x_gated = torch.chunk(x, 2, dim=1)
        
        # Apply gating with sigmoid and return
        return x_linear * torch.sigmoid(x_gated)

class GhostBatchNorm(nn.Module):
    """
    Ghost Batch Normalization
    
    This extends BatchNorm1d to handle virtual batches.
    Based on the paper "Ghost Batch Normalization" (Hoffer et al. 2017).
    """
    def __init__(self, input_dim, virtual_batch_size=128, momentum=0.01):
        super(GhostBatchNorm, self).__init__()
        
        self.input_dim = input_dim
        
        # Ensure virtual_batch_size is an integer
        if isinstance(virtual_batch_size, str):
            try:
                virtual_batch_size = int(virtual_batch_size)
            except ValueError:
                print(f"Warning: Invalid virtual_batch_size '{virtual_batch_size}', using default 16")
                virtual_batch_size = 16
        self.virtual_batch_size = virtual_batch_size
        
        # Ensure momentum is a float
        if isinstance(momentum, str):
            try:
                momentum = float(momentum)
            except ValueError:
                print(f"Warning: Invalid momentum value '{momentum}', using default 0.01")
                momentum = 0.01
        
        # BatchNorm1d expects feature dimension (not batch size) as parameter
        # nn.BatchNorm1d normalizes over the batch dimension with a separate mean/variance
        # for each feature dimension
        self.bn = nn.BatchNorm1d(input_dim, momentum=momentum)
    
    def forward(self, x):
        """
        Forward pass with virtual batch normalization.
        
        Args:
            x: Input tensor of shape (batch_size, input_dim)
            
        Returns:
            Normalized tensor of the same shape
        """
        # Handle case when batch size is smaller than virtual batch size
        if x.size(0) <= self.virtual_batch_size:
            return self.bn(x)
        
        # Use chunks and apply batch norm to each chunk
        chunks = torch.split(x, self.virtual_batch_size, dim=0)
        res = []
        
        for chunk in chunks:
            res.append(self.bn(chunk))
        
        return torch.cat(res, dim=0)

class GLU(nn.Module):
    """
    Gated Linear Unit activation.
    
    Computes GLU(x) = x_1 ⊙ σ(x_2), where x is split into two halves along feature dimension,
    σ is the sigmoid function, and ⊙ is element-wise multiplication.
    """
    def __init__(self, input_dim, output_dim):
        super(GLU, self).__init__()
        
        self.output_dim = output_dim
        self.fc = nn.Linear(input_dim, output_dim * 2)
        
    def forward(self, x):
        x = self.fc(x)
        out, gate = x.chunk(2, dim=-1)
        return out * torch.sigmoid(gate)

class FeatureTransformer(nn.Module):
    """
    Feature Transformer for TabNet.
    
    Consists of multiple GLU layers with batch normalization.
    """
    def __init__(self, input_dim, output_dim, n_layers=2, shared=False, 
                 virtual_batch_size=128, momentum=0.1):
        super(FeatureTransformer, self).__init__()
        
        self.n_layers = n_layers
        self.shared = shared
        
        if shared:
            # Shared feature transformer has a list of glu_layers but only one batch_norm layer
            self.glu_layers = nn.ModuleList([
                GLU(input_dim=input_dim, output_dim=output_dim)
            ])
            self.bn_layers = nn.ModuleList([
                GhostBatchNorm(output_dim, virtual_batch_size, momentum)
            ])
        else:
            # Independent feature transformer has separate layers
            self.glu_layers = nn.ModuleList([
                GLU(input_dim=input_dim, output_dim=output_dim)
            ] + [
                GLU(input_dim=output_dim, output_dim=output_dim) for _ in range(n_layers - 1)
            ])
            self.bn_layers = nn.ModuleList([
                GhostBatchNorm(output_dim, virtual_batch_size, momentum) for _ in range(n_layers)
            ])
            
    def forward(self, x):
        # Apply first GLU layer
        x = self.glu_layers[0](x)
        x = self.bn_layers[0](x)
        
        # Apply remaining layers (if not shared)
        if not self.shared:
            for i in range(1, self.n_layers):
                x = self.glu_layers[i](x)
                x = self.bn_layers[i](x)
        
        return x
        
class AttentiveTransformer(nn.Module):
    """
    Attention module of TabNet.
    
    This module creates attention masks for feature selection.
    """
    def __init__(self, input_dim, output_dim, mask_type="sparsemax"):
        """
        Initialize AttentiveTransformer.
        
        Args:
            input_dim: Input dimension
            output_dim: Output dimension (typically equal to the number of input features)
            mask_type: Type of mask to use, either 'sparsemax' or 'entmax'
        """
        super(AttentiveTransformer, self).__init__()
        
        # Linear layer for attention
        self.linear = nn.Linear(input_dim, output_dim, bias=False)
        
        # Initialize mask activation function
        if mask_type == "sparsemax":
            self.mask_activation = Sparsemax(dim=1)
        elif mask_type == "entmax":
            self.mask_activation = Entmax15(dim=1)
        elif mask_type == "softmax":
            self.mask_activation = nn.Softmax(dim=1)
        else:
            raise ValueError(f"Unknown mask type: {mask_type}")
        
    def forward(self, a_features, prior_scales=None):
        """
        Forward pass.
        
        Args:
            a_features: Attention features
            prior_scales: Prior scale factors for feature selection
            
        Returns:
            mask: Mask of shape (batch_size, output_dim)
        """
        # Generate raw mask by passing attention features through linear layer
        x = self.linear(a_features)
        
        # Apply prior scales if provided
        if prior_scales is not None:
            # Ensure prior_scales has the right shape for broadcasting
            if prior_scales.dim() == 3 and x.dim() == 2:
                # If prior_scales is [batch, input_dim, 1] and x is [batch, input_dim]
                prior_scales = prior_scales.squeeze(-1)
            elif prior_scales.shape[1] != x.shape[1]:
                # Handle dimension mismatch
                if hasattr(self, 'input_dim') and hasattr(self, 'output_dim'):
                    # Reshape if dimensions are known
                    prior_scales = prior_scales.view(prior_scales.shape[0], -1)
                    # Ensure it has correct feature dimensions
                    if prior_scales.shape[1] != x.shape[1]:
                        # Use ones if shapes still don't match
                        prior_scales = torch.ones_like(x)
                else:
                    # Use ones as fallback
                    prior_scales = torch.ones_like(x)
                
            x = x * prior_scales
        
        # Apply mask activation to get normalized mask
        mask = self.mask_activation(x)
        
        # Ensure mask values are valid (no NaNs or infinities)
        mask = torch.nan_to_num(mask, nan=1.0/mask.shape[1], posinf=1.0, neginf=0.0)
        
        # Ensure mask sums to 1 across features
        sum_mask = mask.sum(dim=1, keepdim=True)
        # Avoid division by zero
        sum_mask = torch.where(sum_mask > 0, sum_mask, torch.ones_like(sum_mask))
        mask = mask / sum_mask
        
        return mask

class TabNetModel(nn.Module):
    def __init__(self, input_dim, output_dim=1, n_d=8, n_a=8, n_steps=3, gamma=1.3,
                 n_independent=2, n_shared=2, virtual_batch_size=128, momentum=0.02,
                 mask_type="sparsemax"):
        super(TabNetModel, self).__init__()
        
        self.input_dim = input_dim
        self.output_dim = output_dim
        self.n_d = n_d
        self.n_a = n_a
        self.n_steps = n_steps
        self.gamma = gamma
        self.virtual_batch_size = virtual_batch_size
        
        # Initial feature transformer
        self.initial_transformer = TabNetFeatureTransformer(
            input_dim,
            n_d + n_a,
            n_shared,
            n_independent,
            virtual_batch_size,
            momentum
        )
        
        # Feature transformers for each step
        self.feature_transformers = nn.ModuleList([
            TabNetFeatureTransformer(
                input_dim,
                n_d + n_a,
                n_shared,
                n_independent,
                virtual_batch_size,
                momentum
            ) for _ in range(n_steps)
        ])
        
        # Attentive transformers for each step
        self.attentive_transformers = nn.ModuleList([
            TabNetAttentiveTransformer(
                n_a,
                input_dim,
                virtual_batch_size,
                momentum,
                mask_type
            ) for _ in range(n_steps)
        ])
        
        # Final output layer
        self.final_layer = nn.Linear(n_d * n_steps, output_dim)
    
    def forward(self, x):
        """
        Forward pass of TabNet.
        
        Args:
            x: Input tensor of shape (batch_size, input_dim)
            
        Returns:
            output: Model predictions of shape (batch_size,)
        """
        batch_size = x.size(0)
        
        # Initial feature processing
        processed_features = self.initial_transformer(x)
        
        # Split features into decision and attention components
        d_features = processed_features[:, :self.n_d]
        a_features = processed_features[:, self.n_d:]
        
        # Initialize prior scales for masking
        prior_scales = torch.ones((batch_size, self.input_dim), device=x.device)
        
        # Initialize list to store all decision features
        all_d_features = []
        
        # Process each step
        for step_idx in range(self.n_steps):
            # Get current transformers
            feature_transformer = self.feature_transformers[step_idx]
            attentive_transformer = self.attentive_transformers[step_idx]
            
            # Apply attention mechanism
            mask = attentive_transformer(a_features)
            masked_x = x * mask * prior_scales
            
            # Update prior scales
            prior_scales = prior_scales * (self.gamma - mask)
            
            # Transform masked features
            step_features = feature_transformer(masked_x)
            d_features = step_features[:, :self.n_d]
            a_features = step_features[:, self.n_d:]
            
            # Store decision features
            all_d_features.append(d_features)
        
        # Concatenate all decision features
        all_d_features = torch.cat(all_d_features, dim=1)
        
        # Final prediction
        output = self.final_layer(all_d_features)
        
        # Ensure output has shape (batch_size,) for single target regression
        if self.output_dim == 1:
            output = output.squeeze(-1)
        
        return output
    
    def get_feature_importance(self, x):
        """
        Calculate feature importance scores.
        
        Args:
            x: Input tensor of shape (batch_size, input_dim)
            
        Returns:
            importance: Feature importance scores
        """
        batch_size = x.size(0)
        
        # Initial feature processing
        processed_features = self.initial_transformer(x)
        
        # Split features
        d_features = processed_features[:, :self.n_d]
        a_features = processed_features[:, self.n_d:]
        
        # Initialize importance scores
        importance = torch.zeros(self.input_dim, device=x.device)
        prior_scales = torch.ones((batch_size, self.input_dim), device=x.device)
        
        # Process each step
        for step_idx in range(self.n_steps):
            # Get current transformers
            feature_transformer = self.feature_transformers[step_idx]
            attentive_transformer = self.attentive_transformers[step_idx]
            
            # Apply attention mechanism
            mask = attentive_transformer(a_features)
            
            # Update importance scores
            importance += mask.mean(dim=0)
            
            # Update features for next step
            masked_x = x * mask * prior_scales
            prior_scales = prior_scales * (self.gamma - mask)
            
            step_features = feature_transformer(masked_x)
            d_features = step_features[:, :self.n_d]
            a_features = step_features[:, self.n_d:]
        
        # Normalize importance scores
        importance = importance / self.n_steps
        
        return importance.detach().cpu().numpy()

def calculate_sample_weights(targets, strategy='inverse'):
    """
    Calculate sample weights for imbalanced regression.
    
    Args:
        targets: Target values
        strategy: Weight calculation strategy ('inverse', 'sqrt_inverse', 'binned_inverse')
        
    Returns:
        Array of sample weights
    """
    from sklearn.preprocessing import KBinsDiscretizer
    
    # Convert to numpy if needed
    if isinstance(targets, torch.Tensor):
        targets = targets.cpu().numpy()
        
    if strategy == 'inverse':
        # Count frequency of each value (for discrete targets)
        values, counts = np.unique(targets, return_counts=True)
        value_to_weight = {value: 1.0 / count for value, count in zip(values, counts)}
        weights = np.array([value_to_weight.get(target, 1.0) for target in targets])
        
    elif strategy == 'sqrt_inverse':
        # Square root of inverse frequency (less aggressive reweighting)
        values, counts = np.unique(targets, return_counts=True)
        value_to_weight = {value: 1.0 / np.sqrt(count) for value, count in zip(values, counts)}
        weights = np.array([value_to_weight.get(target, 1.0) for target in targets])
        
    elif strategy == 'binned_inverse':
        # For continuous targets: bin the values and use inverse frequency
        n_bins = min(10, len(targets) // 50)  # Ensure at least 50 samples per bin on average
        n_bins = max(2, n_bins)  # At least 2 bins
        
        # Create bins
        kbd = KBinsDiscretizer(n_bins=n_bins, encode='ordinal', strategy='quantile')
        binned = kbd.fit_transform(targets.reshape(-1, 1)).flatten()
        
        # Calculate weights
        bin_counts = np.bincount(binned.astype(int))
        weights = 1.0 / bin_counts[binned.astype(int)]
        
    else:
        # Equal weights if unknown strategy
        weights = np.ones_like(targets)
    
    # Normalize weights to have mean = 1.0
    weights = weights / np.mean(weights)
    
    return weights

class HuberLoss(nn.Module):
    """
    Huber loss function with adjustable delta parameter.
    """
    def __init__(self, delta=1.0):
        super(HuberLoss, self).__init__()
        self.delta = delta
        
    def forward(self, y_pred, y_true):
        abs_error = torch.abs(y_pred - y_true)
        quadratic = torch.min(abs_error, torch.tensor(self.delta, device=y_pred.device))
        linear = abs_error - quadratic
        loss = 0.5 * quadratic**2 + self.delta * linear
        return torch.mean(loss)

class TabNetTrainer:
    """
    Trainer class for TabNetRegressor model.
    
    This class handles the training loop, evaluation, and prediction for TabNetRegressor.
    """
    
    def __init__(self, model, lr=0.02, weight_decay=1e-5, loss_fn='mse', delta=1.0, sample_weight_strategy=None):
        """
        Initialize the trainer with a model and training parameters.
        
        Args:
            model: TabNetRegressor model to train
            lr: Learning rate for optimizer
            weight_decay: L2 regularization coefficient
            loss_fn: Loss function ('mse', 'mae', 'huber')
            delta: Delta parameter for Huber loss
            sample_weight_strategy: Strategy for sample weighting (None, 'inverse', 'sqrt_inverse', 'binned_inverse')
        """
        self.model = model
        self.lr = lr
        self.weight_decay = weight_decay
        self.sample_weight_strategy = sample_weight_strategy
        self.delta = delta
        self.scheduler_fn = None  # Initialize scheduler_fn as None
        self.save_best = True  # Enable saving best model
        self.verbose = True  # Enable verbose output
        self.additional_metrics = {}  # Initialize empty metrics dict
        self.weighted_loss = False  # Initialize weighted loss flag
        self.lambda_sparse = 1e-3  # Initialize sparsity regularization coefficient
        
        # Set up optimizer
        self.optimizer = torch.optim.Adam(
            model.parameters(),
            lr=lr,
            weight_decay=weight_decay
        )
        
        # Set up loss function
        if loss_fn == 'mse':
            self.loss_fn = nn.MSELoss()
        elif loss_fn == 'mae':
            self.loss_fn = nn.L1Loss()
        elif loss_fn == 'huber':
            self.loss_fn = HuberLoss(delta=delta)
        else:
            raise ValueError(f"Unsupported loss function: {loss_fn}")
            
        self.history = {
            'train_loss': [],
            'val_loss': [],
            'sparsity': [],
            'test_metrics': {}
        }
    
    def fit(self, X_train, y_train, X_val=None, y_val=None, 
            batch_size=1024, epochs=100, patience=10, 
            sample_weight=None, callbacks=None):
        """
        Train the TabNet model.
        
        Args:
            X_train: Training features (can be numpy array or tensor)
            y_train: Training targets (can be numpy array or tensor)
            X_val: Validation features (can be numpy array or tensor)
            y_val: Validation targets (can be numpy array or tensor)
            batch_size: Batch size for training
            epochs: Maximum number of epochs to train
            patience: Number of epochs with no improvement for early stopping
            sample_weight: Optional weights for samples
            callbacks: List of callbacks during training
            
        Returns:
            History of training metrics
        """
        # Get device
        device = next(self.model.parameters()).device
        
        # Prepare training data
        if not isinstance(X_train, torch.Tensor):
            X_tensor = torch.FloatTensor(X_train).to(device)
        else:
            # If already a tensor, ensure it's on the correct device
            X_tensor = X_train.to(device)
            
        if not isinstance(y_train, torch.Tensor):
            y_tensor = torch.FloatTensor(y_train).to(device)
        else:
            # If already a tensor, ensure it's on the correct device
            y_tensor = y_train.to(device)
            
        # Prepare sample weights if provided
        if sample_weight is not None:
            if not isinstance(sample_weight, torch.Tensor):
                weight_tensor = torch.FloatTensor(sample_weight).to(device)
            else:
                weight_tensor = sample_weight.to(device)
        else:
            weight_tensor = None
            
        # Prepare validation data if provided
        if X_val is not None and y_val is not None:
            if not isinstance(X_val, torch.Tensor):
                X_val_tensor = torch.FloatTensor(X_val).to(device)
            else:
                X_val_tensor = X_val.to(device)
                
            if not isinstance(y_val, torch.Tensor):
                y_val_tensor = torch.FloatTensor(y_val).to(device)
            else:
                y_val_tensor = y_val.to(device)
                
            has_validation = True
        else:
            has_validation = False
            
        # Training setup
        optimizer = torch.optim.Adam(self.model.parameters(), lr=self.lr, weight_decay=self.weight_decay)
        
        # Set up learning rate scheduler
        scheduler = None
        if self.scheduler_fn is not None:
            scheduler = self.scheduler_fn(optimizer)
            
        # Create data loader for training
        train_dataset = TensorDataset(X_tensor, y_tensor) if weight_tensor is None else WeightedDataset(X_tensor, y_tensor, weight_tensor)
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
        
        # Create data loader for validation if available
        if has_validation:
            val_dataset = TensorDataset(X_val_tensor, y_val_tensor)
            val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
            
        # Initial best metrics
        best_val_loss = float('inf')
        best_epoch = 0
        no_improvement_count = 0
        
        # History of metrics
        history = {'loss': [], 'val_loss': [] if has_validation else None}
        if self.additional_metrics:
            for metric_name in self.additional_metrics:
                history[metric_name] = []
                if has_validation:
                    history[f'val_{metric_name}'] = []
        
        # Training loop
        for epoch in range(epochs):
            # Training phase
            self.model.train()
            total_loss = 0
            total_samples = 0
            
            # Setup metric tracking for this epoch
            epoch_metrics = {metric_name: 0 for metric_name in self.additional_metrics}
            
            # Process batches
            for batch_idx, batch_data in enumerate(train_loader):
                # Unpack batch
                if weight_tensor is None:
                    X_batch, y_batch = batch_data
                    batch_weight = None
                else:
                    X_batch, y_batch, batch_weight = batch_data
                
                # Forward pass
                y_pred, sparse_loss = self.model(X_batch)
                
                # Calculate loss
                if self.weighted_loss and batch_weight is not None:
                    # Apply sample weights
                    batch_loss = self.loss_fn(y_pred, y_batch, reduction='none')
                    weighted_loss = (batch_loss * batch_weight).mean()
                    loss = weighted_loss + self.lambda_sparse * sparse_loss
                else:
                    # Regular loss
                    batch_loss = self.loss_fn(y_pred, y_batch)
                    loss = batch_loss + self.lambda_sparse * sparse_loss
                
                # Backpropagation
                optimizer.zero_grad()
                loss.backward()
                optimizer.step()
                
                # Update metrics
                batch_size = y_batch.size(0)
                total_loss += loss.item() * batch_size
                total_samples += batch_size
                
                # Additional metrics
                for metric_name, metric_fn in self.additional_metrics.items():
                    # Convert predictions and targets to CPU numpy for some metrics
                    if metric_name in ['r2_score', 'explained_variance', 'mean_absolute_error']:
                        metric_value = metric_fn(y_batch.cpu().detach().numpy(), 
                                               y_pred.cpu().detach().numpy())
                    else:
                        metric_value = metric_fn(y_pred, y_batch)
                        
                    epoch_metrics[metric_name] += metric_value * batch_size
            
            # Calculate average loss and metrics for the epoch
            avg_loss = total_loss / total_samples
            history['loss'].append(avg_loss)
            
            # Calculate average metrics
            for metric_name in self.additional_metrics:
                avg_metric = epoch_metrics[metric_name] / total_samples
                history[metric_name].append(avg_metric)
            
            # Validation phase
            if has_validation:
                val_metrics = self.evaluate(val_loader)
                val_loss = val_metrics['loss']
                history['val_loss'].append(val_loss)
                
                # Additional val metrics
                for metric_name in self.additional_metrics:
                    val_metric = val_metrics[metric_name]
                    history[f'val_{metric_name}'].append(val_metric)
                
                # Check for improvement
                if val_loss < best_val_loss:
                    best_val_loss = val_loss
                    best_epoch = epoch
                    no_improvement_count = 0
                    
                    # Save the best model
                    if self.save_best:
                        self.best_model_state = copy.deepcopy(self.model.state_dict())
                else:
                    no_improvement_count += 1
            
            # Learning rate scheduling
            if scheduler is not None:
                if isinstance(scheduler, torch.optim.lr_scheduler.ReduceLROnPlateau):
                    scheduler.step(val_loss if has_validation else avg_loss)
                else:
                    scheduler.step()
            
            # Logging
            if self.verbose and (epoch % 10 == 0 or epoch == epochs - 1):
                log_msg = f"Epoch {epoch+1}/{epochs}, Loss: {avg_loss:.4f}"
                if has_validation:
                    log_msg += f", Val Loss: {val_loss:.4f}"
                print(log_msg)
            
            # Execute callbacks
            if callbacks is not None:
                for callback in callbacks:
                    kwargs = {
                        'epoch': epoch,
                        'epochs': epochs,
                        'model': self.model,
                        'optimizer': optimizer,
                        'history': history,
                        'metrics': {
                            'loss': avg_loss,
                            **(val_metrics if has_validation else {})
                        }
                    }
                    callback(**kwargs)
            
            # Early stopping
            if patience > 0 and no_improvement_count >= patience:
                if self.verbose:
                    print(f"Early stopping at epoch {epoch+1}. Best epoch: {best_epoch+1}")
                break
        
        # Restore best model if relevant
        if has_validation and self.save_best and self.best_model_state is not None:
            self.model.load_state_dict(self.best_model_state)
            
        return history
    
    def _validate(self, val_loader):
        """
        Validate the model on the validation data.
        
        Args:
            val_loader: Validation data loader
            
        Returns:
            Validation loss
        """
        self.model.eval()
        val_loss = 0.0
        batches = 0
        
        with torch.no_grad():
            for X_batch, y_batch in val_loader:
                # Forward pass
                y_pred, M_loss = self.model(X_batch)
                
                # Compute loss
                batch_loss = self.loss_fn(y_pred, y_batch)
                
                # Add M_loss for consistency with training
                total_loss = batch_loss + M_loss
                
                val_loss += total_loss.item()
                batches += 1
        
        return val_loss / batches
    
    def predict(self, X):
        """
        Make predictions with the model.
        
        Args:
            X: Input features
            
        Returns:
            Predicted values
        """
        # Convert to torch tensor if needed
        if not isinstance(X, torch.Tensor):
            X_tensor = torch.FloatTensor(X)
        else:
            X_tensor = X
        
        # Switch to evaluation mode
        self.model.eval()
        
        # Make predictions
        with torch.no_grad():
            y_pred, _ = self.model(X_tensor)
        
        return y_pred.cpu().numpy()
    
    def evaluate(self, data_loader):
        """
        Evaluate the model on a data loader.
        
        Args:
            data_loader: DataLoader containing features and targets
            
        Returns:
            Dictionary of evaluation metrics
        """
        self.model.eval()
        total_loss = 0
        total_samples = 0
        all_preds = []
        all_targets = []
        
        with torch.no_grad():
            for batch_data in data_loader:
                # Unpack batch
                if len(batch_data) == 3:  # If using weighted dataset
                    X_batch, y_batch, batch_weight = batch_data
                else:
                    X_batch, y_batch = batch_data
                    batch_weight = None
                
                # Forward pass
                y_pred, sparse_loss = self.model(X_batch)
                
                # Calculate loss
                if self.weighted_loss and batch_weight is not None:
                    batch_loss = self.loss_fn(y_pred, y_batch, reduction='none')
                    weighted_loss = (batch_loss * batch_weight).mean()
                    loss = weighted_loss + self.lambda_sparse * sparse_loss
                else:
                    batch_loss = self.loss_fn(y_pred, y_batch)
                    loss = batch_loss + self.lambda_sparse * sparse_loss
                
                # Update metrics
                batch_size = y_batch.size(0)
                total_loss += loss.item() * batch_size
                total_samples += batch_size
                
                # Store predictions and targets for additional metrics
                all_preds.append(y_pred.cpu().numpy())
                all_targets.append(y_batch.cpu().numpy())
        
        # Calculate average loss
        avg_loss = total_loss / total_samples
        
        # Combine predictions and targets
        all_preds = np.concatenate(all_preds)
        all_targets = np.concatenate(all_targets)
        
        # Calculate additional metrics
        metrics = {'loss': avg_loss}
        for metric_name, metric_fn in self.additional_metrics.items():
            metrics[metric_name] = metric_fn(all_targets, all_preds)
        
        return metrics
    
    def get_feature_importance(self, X=None):
        """
        Get feature importance from the model.
        
        Args:
            X: Input features to calculate feature importance
            
        Returns:
            Feature importance scores
        """
        return self.model.feature_importance(X)
    
    def save_model(self, filepath):
        """
        Save the model to disk.
        
        Args:
            filepath: Path to save the model
        """
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        
        # Save model
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'history': self.history,
            'model_params': {
                'input_dim': 50,  # Default value
                'output_dim': 1,   # Default for regression
                'n_d': 64,         # Default value
                'n_a': 64,         # Default value
                'n_steps': 3,      # Default value
                'gamma': 1.3,      # Default value
                'n_independent': 2,  # Default value
                'n_shared': 2,     # Default value
                'virtual_batch_size': 128,  # Default value
                'momentum': 0.02,  # Default value
                'mask_type': 'sparsemax'  # Default value
            },
            'trainer_params': {
                'lr': self.lr,
                'weight_decay': self.weight_decay,
                'sample_weight_strategy': self.sample_weight_strategy,
                'delta': self.delta
            }
        }, filepath)
    
    @classmethod
    def load_model(cls, filepath):
        """
        Load a saved model.
        
        Args:
            filepath: Path to the saved model
            
        Returns:
            Loaded model and trainer
        """
        # Load saved state
        checkpoint = torch.load(filepath)
        
        # Create model with saved parameters (with safety checks)
        params = checkpoint['model_params']
        model = TabNetRegressor(
            input_dim=params.get('input_dim', 50),
            output_dim=params.get('output_dim', 1),
            n_d=params.get('n_d', 64),
            n_a=params.get('n_a', 64),
            n_steps=params.get('n_steps', 3),
            gamma=params.get('gamma', 1.3),
            n_independent=params.get('n_independent', 2),
            n_shared=params.get('n_shared', 2),
            virtual_batch_size=params.get('virtual_batch_size', 128),
            momentum=params.get('momentum', 0.02),
            mask_type=params.get('mask_type', 'sparsemax')
        )
        
        # Load model state
        model.load_state_dict(checkpoint['model_state_dict'])
        
        # Create trainer
        trainer_params = checkpoint['trainer_params']
        trainer = cls(
            model=model,
            lr=trainer_params.get('lr', 0.01),
            weight_decay=trainer_params.get('weight_decay', 1e-5),
            sample_weight_strategy=trainer_params.get('sample_weight_strategy', None),
            delta=trainer_params.get('delta', 1.0)
        )
        
        # Load optimizer state
        trainer.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        
        # Load history
        trainer.history = checkpoint['history']
        
        return trainer

# Fix for errors in encoder implementation
class FeatTransformer(nn.Module):
    def __init__(self, input_dim, output_dim, n_glu_layers=2, 
                 virtual_batch_size=128, momentum=0.02, dropout=0.0):
        super(FeatTransformer, self).__init__()
        
        # Create GLU blocks
        self.n_glu_layers = n_glu_layers
        self.glu_layers = nn.ModuleList()
        self.dropout = nn.Dropout(dropout) if dropout > 0 else None
        
        # First GLU layer
        self.glu_layers.append(nn.ModuleDict({
            'glu': GLU(input_dim=input_dim, output_dim=output_dim),
            'bn': GhostBatchNorm(output_dim, virtual_batch_size, momentum)
        }))
        
        # Subsequent GLU layers
        for _ in range(1, self.n_glu_layers):
            self.glu_layers.append(nn.ModuleDict({
                'glu': GLU(input_dim=output_dim, output_dim=output_dim),
                'bn': GhostBatchNorm(output_dim, virtual_batch_size, momentum)
            }))
    
    def forward(self, x):
        for glu_dict in self.glu_layers:
            x = glu_dict['glu'](x)
            x = glu_dict['bn'](x)
            x = F.relu(x)
            if self.dropout is not None:
                x = self.dropout(x)
        return x 

class TabNetEncoder(nn.Module):
    """
    TabNet encoder module.
    
    Attributes:
        input_dim: Input dimension
        output_dim: Output dimension
        n_d: Dimension of decision prediction layer
        n_a: Dimension of attention embedding for each mask step
        n_steps: Number of steps in the architecture
        gamma: Relaxation parameter
        n_independent: Number of independent FC layers in feature transformer
        n_shared: Number of shared FC layers in feature transformer
        virtual_batch_size: Batch size for ghost batch normalization
        momentum: Momentum for batch normalization
        mask_type: Type of mask to use, either 'sparsemax' or 'entmax'
    """
    
    def __init__(
        self,
        input_dim,
        output_dim,
        n_d=8,
        n_a=8,
        n_steps=3,
        gamma=1.3,
        n_independent=2,
        n_shared=2,
        virtual_batch_size=128,
        momentum=0.02,
        mask_type="sparsemax"
    ):
        """
        Initialize TabNetEncoder.
        
        Args:
            input_dim: Input dimension
            output_dim: Output dimension
            n_d: Dimension of decision prediction layer
            n_a: Dimension of attention embedding for each mask step
            n_steps: Number of steps in the architecture
            gamma: Relaxation parameter
            n_independent: Number of independent FC layers in feature transformer
            n_shared: Number of shared FC layers in feature transformer
            virtual_batch_size: Batch size for ghost batch normalization
            momentum: Momentum for batch normalization
            mask_type: Type of mask to use, either 'sparsemax' or 'entmax'
        """
        super(TabNetEncoder, self).__init__()
        
        # Save parameters
        self.input_dim = input_dim
        self.output_dim = output_dim
        self.n_d = n_d
        self.n_a = n_a
        self.n_steps = n_steps
        self.gamma = gamma
        self.n_independent = n_independent
        self.n_shared = n_shared
        self.virtual_batch_size = virtual_batch_size
        self.momentum = momentum
        self.mask_type = mask_type
        
        # Initialize masks list
        self.masks = []
        
        # Initial splitting layer
        # Splits input into features for decision and attention steps
        self.initial_splitter = FeatTransformer(
            input_dim,
            n_d + n_a,
            n_independent,
            n_shared,
            virtual_batch_size,
            momentum
        )
        
        # Feature transformers for each step
        # Used to learn features for each decision step
        self.feat_transformers = nn.ModuleList([
            FeatTransformer(
                input_dim,
                n_d + n_a,
                n_independent,
                n_shared,
                virtual_batch_size,
                momentum
            )
            for _ in range(n_steps - 1)
        ])
        
        # Attentive transformers for each step
        # Used to select features for each decision step
        self.attentive_transformers = nn.ModuleList([
            AttentiveTransformer(
                n_a,
                input_dim,
                mask_type
            )
            for _ in range(n_steps - 1)
        ])

    def forward(self, x):
        """
        Forward pass through the encoder.
        
        Args:
            x: Input tensor of shape (batch_size, input_dim)
            
        Returns:
            tuple: (output, loss)
                - output: Output tensor of shape (batch_size, output_dim)
                - loss: Sparsity regularization loss
        """
        # Initialize list to store masks for feature importance
        masks = []
        
        # Initial processing - no need for initial batch norm
        x_processed = self.initial_splitter(x)
        
        # Split into decision and attention features
        d = x_processed[:, :self.n_d]
        a = x_processed[:, self.n_d:]
        
        # Initialize prior scale - no features masked yet
        prior = torch.ones(x.shape).to(x.device)
        
        # Apply steps
        step_output = d
        total_entropy = 0
        
        # Apply step-wise transformation
        for step in range(self.n_steps - 1):
            # Generate mask using the attentive transformer
            mask = self.attentive_transformers[step](a, prior)
            
            # Verify mask shape matches input shape
            if mask.size(1) == self.input_dim:
                # Only store valid masks
                masks.append(mask.detach())  # Store mask for feature importance
            
            # Apply mask to input and update prior
            masked_x = x * mask
            prior = prior * (self.gamma - mask)
            
            # Process masked input through feature transformer
            x_step = self.feat_transformers[step](masked_x)
            
            # Split processed output
            d = x_step[:, :self.n_d]
            a = x_step[:, self.n_d:]
            
            # Add to output
            step_output = step_output + d
            
            # Calculate entropy for the mask (for sparsity regularization)
            entropy = -torch.sum(mask * torch.log(mask + 1e-10), dim=1)
            total_entropy = total_entropy + entropy
            
        # Store masks for feature importance calculation
        self.masks = masks
        
        # Calculate sparsity loss
        sparse_loss = total_entropy.mean()
        
        return step_output, sparse_loss
        
        
class TabNetRegressor(nn.Module):
    """
    TabNet model for regression tasks.
    """
    def __init__(self, input_dim, output_dim=1, n_d=8, n_a=8, n_steps=3, gamma=1.3,
                 n_independent=2, n_shared=2, virtual_batch_size=128, 
                 mask_type="sparsemax", momentum=0.02, dropout=0.1):
        """
        Initialize TabNetRegressor.
        
        Args:
            input_dim: Number of input features
            output_dim: Number of output dimensions
            n_d: Dimension of the prediction layer
            n_a: Dimension of the attention layer 
            n_steps: Number of sequential steps in the feature transformer
            gamma: Parameter controlling feature reusage
            n_independent: Number of independent GLU layers
            n_shared: Number of shared GLU layers
            virtual_batch_size: Size of virtual batches
            mask_type: Type of mask to use: "sparsemax" or "entmax"
            momentum: Momentum for batch normalization
            dropout: Dropout rate for regularization
        """
        super(TabNetRegressor, self).__init__()
        
        # Store input dimension for feature importance calculation
        self.input_dim = input_dim
        
        # Ensure numeric parameters are float/int
        if isinstance(gamma, str):
            try:
                gamma = float(gamma)
            except ValueError:
                print(f"Warning: Invalid gamma value '{gamma}', using default 1.3")
                gamma = 1.3
                
        if isinstance(momentum, str):
            try:
                momentum = float(momentum)
            except ValueError:
                print(f"Warning: Invalid momentum value '{momentum}', using default 0.02")
                momentum = 0.02
        
        # Encoder
        self.encoder = TabNetEncoder(
            input_dim=input_dim,
            output_dim=output_dim,
            n_d=n_d,
            n_a=n_a,
            n_steps=n_steps,
            gamma=gamma,
            n_independent=n_independent,
            n_shared=n_shared,
            virtual_batch_size=virtual_batch_size,
            momentum=momentum,
            mask_type=mask_type
        )
        
        # Dropout for regularization
        self.dropout = nn.Dropout(dropout)
        
        # Prediction head
        self.head = nn.Linear(n_d, output_dim)
        
    def forward(self, x):
        """
        Forward pass through the network.
        
        Args:
            x: Input tensor of shape (batch_size, input_dim)
            
        Returns:
            tuple: (predictions, sparse_loss)
                - predictions: Output predictions
                - sparse_loss: Sparsity regularization loss 
        """
        # Pass through encoder
        encoded_output, sparse_loss = self.encoder(x)
        
        # Apply dropout for regularization
        output = self.dropout(encoded_output)
        
        # Apply prediction head
        predictions = self.head(output)
        
        return predictions, sparse_loss
    
    def forward_masks(self, x):
        """
        Forward pass returning prediction and masks.
        
        Args:
            x: Input features
            
        Returns:
            tuple: (predictions, masks)
        """
        masked_x, masks = self.encoder(x)
        preds = self.head(masked_x)
        self._masks = masks  # Store masks for feature importance
        return preds, masks

    def feature_importance(self, X=None):
        """
        Calculate feature importance from the learned masks.
        
        Args:
            X: Optional input data to compute masks on
                If None, will use previously stored masks
                
        Returns:
            Array of feature importance scores (higher = more important)
        """
        if X is not None:
            # If data is provided, run forward pass to generate new masks
            if not isinstance(X, torch.Tensor):
                X = torch.FloatTensor(X).to(next(self.parameters()).device)
            
            self.eval()  # Set model to evaluation mode
            with torch.no_grad():
                _, _ = self.forward(X)  # Forward pass stores masks in encoder
        
        # Check if masks are available
        if not hasattr(self.encoder, 'masks') or not self.encoder.masks:
            raise ValueError("No masks found. Please provide input data or run model first.")
        
        # Get masks from encoder
        masks = self.encoder.masks
        
        # Calculate feature importance based on masks
        # Initialize with small non-zero values to avoid all zeros
        feature_importances = torch.ones(self.input_dim, device=next(self.parameters()).device) * 1e-5
        
        # Weight by step (earlier steps more important)
        for i, mask in enumerate(masks):
            # Ensure mask is properly reshaped and has correct dimensions
            if mask.dim() < 2 or mask.shape[1] != self.input_dim:
                continue
                
            # Calculate weight based on step (decreasing importance for later steps)
            step_weight = self.encoder.gamma ** (len(masks) - i)
            
            # Average mask across all samples in batch
            avg_mask = mask.mean(dim=0)
            
            # Add weighted importance to total
            feature_importances += avg_mask * step_weight
        
        # Apply softmax to ensure non-zero values that sum to 1
        # This prevents the problem of most features having 0 importance
        feature_importances = torch.softmax(feature_importances, dim=0)
        
        # Return numpy array
        return feature_importances.cpu().numpy()

    def feature_importance_ensemble(self, X, n_permutations=5, random_state=42):
        """
        Calculate feature importance using a combination of mask-based importance
        and permutation importance for more reliable results.
        
        Args:
            X: Input data to compute feature importance on
            n_permutations: Number of permutations for the permutation importance
            random_state: Random seed for reproducibility
            
        Returns:
            Array of feature importance scores (higher = more important)
        """
        if not isinstance(X, torch.Tensor):
            X = torch.FloatTensor(X).to(next(self.parameters()).device)
        
        self.eval()  # Set model to evaluation mode
        
        # 1. Get mask-based importance
        with torch.no_grad():
            # Forward pass to get base predictions and store masks
            base_preds, _ = self.forward(X)
            mask_importance = self.feature_importance(X)
        
        # 2. Calculate permutation importance
        np.random.seed(random_state)
        n_features = X.shape[1]
        permutation_importance = np.zeros(n_features)
        
        # Make baseline prediction
        baseline_mse = torch.mean((base_preds - base_preds.mean())**2).item()
        
        # For each feature, permute it and see how prediction changes
        for feature_idx in range(n_features):
            # Create multiple permutations for more stable results
            feature_importance = 0.0
            
            for _ in range(n_permutations):
                # Create a permuted version of X
                X_permuted = X.clone()
                permuted_indices = torch.randperm(X.shape[0])
                X_permuted[:, feature_idx] = X[permuted_indices, feature_idx]
                
                # Get predictions with permuted feature
                with torch.no_grad():
                    permuted_preds, _ = self.forward(X_permuted)
                
                # Calculate how much performance dropped (higher is more important)
                permuted_mse = torch.mean((permuted_preds - base_preds)**2).item()
                feature_importance += max(0, permuted_mse - baseline_mse)  # Only count drops in performance
            
            # Average over permutations
            permutation_importance[feature_idx] = feature_importance / n_permutations
        
        # Normalize permutation importance
        if permutation_importance.sum() > 0:
            permutation_importance = permutation_importance / permutation_importance.sum()
        
        # 3. Combine both importance methods (weighted average)
        # If permutation importance is all zeros, use only mask importance
        if permutation_importance.sum() == 0:
            combined_importance = mask_importance
        else:
            # Weighted average of both methods
            combined_importance = 0.7 * mask_importance + 0.3 * permutation_importance
            # Ensure it sums to 1
            combined_importance = combined_importance / combined_importance.sum()
        
        return combined_importance

    def predict(self, X):
        """
        Make predictions using the model.
        
        Args:
            X: Dictionary with feature arrays or tensor of features
            
        Returns:
            Dictionary with predictions
        """
        self.eval()
        
        with torch.no_grad():
            # Handle different input formats
            if isinstance(X, dict):
                if 'all_features' in X:
                    input_tensor = X['all_features']
                else:
                    # Try to find the first tensor in the dict
                    for key, value in X.items():
                        if isinstance(value, torch.Tensor):
                            input_tensor = value
                            break
                    else:
                        raise ValueError("Could not find tensor in input dictionary")
            elif isinstance(X, torch.Tensor):
                input_tensor = X
            else:
                raise ValueError("Input must be a dictionary or tensor")
            
            # Forward pass
            preds, _ = self.forward(input_tensor)
            
            # Return predictions in the expected format (dict with 'growth_rate' key)
            return {'growth_rate': preds.squeeze()}

class GLULayer(nn.Module):
    """
    Gated Linear Unit layer.
    
    This implements a simplified version of GLU: GLU(x) = (W1*x + b1) ⊙ σ(W2*x + b2)
    where ⊙ is element-wise multiplication and σ is the sigmoid function.
    """
    def __init__(self, input_dim, output_dim, virtual_batch_size=128, momentum=0.02):
        """
        Initialize GLULayer.
        
        Args:
            input_dim: Input feature dimension
            output_dim: Output feature dimension
            virtual_batch_size: Size of virtual batches
            momentum: Momentum for batch normalization
        """
        super(GLULayer, self).__init__()
        
        self.input_dim = input_dim
        self.output_dim = output_dim
        
        # Ensure momentum is a float
        if isinstance(momentum, str):
            try:
                momentum = float(momentum)
            except ValueError:
                print(f"Warning: Invalid momentum value '{momentum}', using default 0.02")
                momentum = 0.02
        
        # Normalization
        self.bn = GhostBatchNorm(input_dim, virtual_batch_size, momentum)
        
        # Main path (W1*x + b1)
        self.fc = nn.Linear(input_dim, output_dim, bias=False)
        
        # Gating path (W2*x + b2)
        self.fc_gate = nn.Linear(input_dim, output_dim, bias=False)
    
    def forward(self, x):
        """
        Forward pass.
        
        Args:
            x: Input tensor of shape (batch_size, input_dim)
            
        Returns:
            torch.Tensor: Output tensor of shape (batch_size, output_dim)
        """
        # Normalize input
        x = self.bn(x)
        
        # Linear paths
        main = self.fc(x)
        gate = self.fc_gate(x)
        
        # Apply GLU mechanism
        return F.relu(main) * torch.sigmoid(gate)


class FeatTransformer(nn.Module):
    """
    Feature transformer module for TabNet.
    
    This applies a series of GLU layers to transform features.
    """
    def __init__(self, input_dim, output_dim, n_independent, n_shared,
                 virtual_batch_size=128, momentum=0.02):
        """
        Initialize FeatTransformer.
        
        Args:
            input_dim: Input feature dimension
            output_dim: Output feature dimension
            n_independent: Number of independent GLU layers
            n_shared: Number of shared GLU layers
            virtual_batch_size: Size of virtual batches
            momentum: Momentum for batch normalization
        """
        super(FeatTransformer, self).__init__()
        
        self.input_dim = input_dim
        self.output_dim = output_dim
        
        # Ensure momentum is a float
        if isinstance(momentum, str):
            try:
                momentum = float(momentum)
            except ValueError:
                print(f"Warning: Invalid momentum value '{momentum}', using default 0.02")
                momentum = 0.02
        
        # Fix: Use the correct dimensions for shared and independent layers
        
        # Shared layers
        if n_shared > 0:
            self.shared = nn.ModuleList()
            # First shared layer takes input_dim as input dimension
            first_dim = input_dim
            self.shared.append(GLULayer(first_dim, output_dim, virtual_batch_size, momentum))
            
            # Subsequent shared layers take output_dim as input dimension
            for _ in range(1, n_shared):
                self.shared.append(GLULayer(output_dim, output_dim, virtual_batch_size, momentum))
        else:
            self.shared = None
        
        # Independent layers (final layers before output)
        if n_independent > 0:
            self.independent = nn.ModuleList()
            
            # First independent layer
            # If shared exists, input is output_dim, otherwise input_dim
            first_dim = output_dim if (n_shared > 0) else input_dim
            self.independent.append(GLULayer(first_dim, output_dim, virtual_batch_size, momentum))
            
            # Subsequent independent layers take output_dim as input
            for _ in range(1, n_independent):
                self.independent.append(GLULayer(output_dim, output_dim, virtual_batch_size, momentum))
        else:
            self.independent = None
            
    def forward(self, x):
        """
        Forward pass.
        
        Args:
            x: Input tensor of shape (batch_size, input_dim)
            
        Returns:
            torch.Tensor: Output tensor of shape (batch_size, output_dim)
        """
        # Apply shared layers if any
        if self.shared is not None:
            for layer in self.shared:
                x = layer(x)
        
        # Apply independent layers if any
        if self.independent is not None:
            for layer in self.independent:
                x = layer(x)
        
        return x 

class TabNetFeatureTransformer(nn.Module):
    def __init__(self, input_dim, output_dim, n_shared=2, n_independent=2,
                 virtual_batch_size=128, momentum=0.02):
        super(TabNetFeatureTransformer, self).__init__()
        
        self.input_dim = input_dim
        self.output_dim = output_dim
        
        # Shared blocks
        self.shared = nn.ModuleList([
            GatedLinearUnit(
                input_dim if i == 0 else output_dim,
                output_dim,
                virtual_batch_size,
                momentum
            ) for i in range(n_shared)
        ])
        
        # Independent blocks for each step
        self.independent = nn.ModuleList([
            GatedLinearUnit(
                output_dim,
                output_dim,
                virtual_batch_size,
                momentum
            ) for _ in range(n_independent)
        ])
    
    def forward(self, x):
        """
        Forward pass of the feature transformer.
        
        Args:
            x: Input tensor of shape (batch_size, input_dim)
            
        Returns:
            output: Transformed features
        """
        # Apply shared blocks
        for shared_block in self.shared:
            x = shared_block(x)
        
        # Apply independent blocks
        for independent_block in self.independent:
            x = independent_block(x)
        
        return x

class TabNetAttentiveTransformer(nn.Module):
    def __init__(self, input_dim, output_dim, virtual_batch_size=128,
                 momentum=0.02, mask_type="sparsemax"):
        super(TabNetAttentiveTransformer, self).__init__()
        
        self.input_dim = input_dim
        self.output_dim = output_dim
        self.virtual_batch_size = virtual_batch_size
        self.mask_type = mask_type
        
        # Feature transformation layers
        self.transform = nn.Sequential(
            nn.Linear(input_dim, output_dim, bias=False),
            nn.BatchNorm1d(output_dim, momentum=momentum)
        )
        
        # Mask activation
        if mask_type == "sparsemax":
            self.mask_activation = Sparsemax(dim=1)
        elif mask_type == "entmax":
            self.mask_activation = Entmax15(dim=1)
        else:
            raise ValueError(f"Unknown mask type: {mask_type}")
    
    def forward(self, x):
        """
        Forward pass of the attentive transformer.
        
        Args:
            x: Input tensor of shape (batch_size, input_dim)
            
        Returns:
            mask: Attention mask
        """
        # Transform features
        transformed = self.transform(x)
        
        # Apply mask activation
        mask = self.mask_activation(transformed)
        
        return mask

class GatedLinearUnit(nn.Module):
    def __init__(self, input_dim, output_dim, virtual_batch_size=128, momentum=0.02):
        super(GatedLinearUnit, self).__init__()
        
        self.input_dim = input_dim
        self.output_dim = output_dim
        self.virtual_batch_size = virtual_batch_size
        
        # Linear layer for feature transformation
        self.linear = nn.Linear(input_dim, 2 * output_dim, bias=False)
        
        # Ghost batch normalization
        self.norm = GhostBatchNorm(2 * output_dim, virtual_batch_size, momentum)
    
    def forward(self, x):
        """
        Forward pass of the GLU.
        
        Args:
            x: Input tensor of shape (batch_size, input_dim)
            
        Returns:
            output: GLU output
        """
        # Linear transformation
        x = self.linear(x)
        
        # Ghost batch normalization
        x = self.norm(x)
        
        # Split into two parts for gating
        chunks = x.chunk(2, dim=1)
        
        # Apply GLU
        return chunks[0] * torch.sigmoid(chunks[1])

class Sparsemax(nn.Module):
    def __init__(self, dim=1):
        super(Sparsemax, self).__init__()
        self.dim = dim
    
    def forward(self, x):
        """
        Forward pass of Sparsemax.
        
        Args:
            x: Input tensor
            
        Returns:
            output: Sparsemax output tensor
        """
        # Get original size and reshape
        orig_size = x.size()
        x = x.view(x.size(0), -1)
        
        # Sort input in descending order
        sorted_input, _ = torch.sort(x, dim=1, descending=True)
        
        # Compute cumulative sum
        cumsum = sorted_input.cumsum(dim=1)
        
        # Get sequence length
        n = sorted_input.size(1)
        
        # Range from 1 to n
        range_n = torch.arange(1, n + 1, dtype=x.dtype, device=x.device)
        range_n = range_n.unsqueeze(0)
        
        # Compute mean up to current position
        mean = cumsum / range_n
        
        # Find last position where sorted_input > mean
        is_gt = sorted_input > mean
        
        # Get number of valid dimensions
        k = is_gt.sum(dim=1).unsqueeze(1)
        k = torch.max(k, torch.ones_like(k))  # Ensure k >= 1
        
        # Compute threshold
        tau = cumsum.gather(1, k - 1) / k
        
        # Compute output
        x_threshold = x - tau
        output = torch.clamp(x_threshold, min=0)
        
        # Reshape back to original size
        output = output.view(orig_size)
        
        return output

class Entmax15(nn.Module):
    def __init__(self, dim=1):
        super(Entmax15, self).__init__()
        self.dim = dim
    
    def forward(self, x):
        """
        Forward pass of Entmax15.
        
        Args:
            x: Input tensor
            
        Returns:
            output: Entmax15 output tensor
        """
        # Compute 1.5-entmax
        x_shifted = x - x.max(dim=self.dim, keepdim=True)[0]
        x_exp = torch.exp(x_shifted / 1.5)
        x_exp_sum = x_exp.sum(dim=self.dim, keepdim=True)
        p = x_exp / x_exp_sum
        
        return p

# Custom weighted dataset class
class WeightedDataset(torch.utils.data.Dataset):
    def __init__(self, features, targets, weights):
        self.features = features
        self.targets = targets
        self.weights = weights
        
    def __len__(self):
        return len(self.features)
        
    def __getitem__(self, idx):
        return self.features[idx], self.targets[idx], self.weights[idx] 