"""
神经网络模型模块

这个模块提供了用于预测微生物生长速率和最适生长温度的神经网络模型。

模型架构类型：
1. 基础模型 - 简单的神经网络架构
2. 增强模型 - 集成多种特征的复杂架构
3. 多分支模型 - 分别处理不同类型特征的多分支网络
4. Transformer模型 - 基于注意力机制的序列建模
5. 混合模型 - 结合深度学习和传统机器学习的混合架构
6. 集成模型 - 多个模型的集成预测

核心功能：
- 单任务学习：仅预测生长速率或温度
- 多任务学习：同时预测生长速率和最适温度
- 群落预测：基于宏基因组数据的群落水平预测
- 特征融合：整合多种类型的基因组特征
- 注意力机制：捕获重要的序列和特征信息

技术特点：
- 支持GPU加速训练和推理
- 模块化设计便于扩展
- 多种损失函数和优化器支持
- 正则化和dropout防止过拟合
- 批量归一化提高训练稳定性
"""

from .networks import EnhancedPhyloGrowthModel, EnhancedCommunityGrowthModel
from .enhanced_networks import ImprovedPhyloGrowthModel
from .simple_networks import SimplePhyloGrowthModel
# Attention module is missing, using PositionalEncoding from transformer_network instead
# Trainer module is also missing
from .multi_branch_network import MultiBranchDNN, MultiBranchTrainer, FeatureBranch, AttentionFusion
from .transformer_network import HybridTransformerDNN, TransformerTrainer, TransformerFusion, PositionalEncoding
from .combined_dnn_transformer import CombinedDNNTransformer, CombinedModelTrainer, BasicDNNPath
from .hybrid_rf_transformer import HybridRFTransformer, HybridRFTransformerTrainer
from .ensemble_model import EnsembleModel, EnsembleModelTrainer
from .improved_rf import ImprovedRandomForest, ImprovedRandomForestTrainer

__all__ = [
    'EnhancedPhyloGrowthModel',
    'EnhancedCommunityGrowthModel',
    'ImprovedPhyloGrowthModel',
    'SimplePhyloGrowthModel',
    # 'PositionalAttention',  # Removed as module is missing
    # 'ModelTrainer',  # Removed as module is missing
    # 'PhyloGrowthTrainer',  # Removed as module is missing
    # 'CommunityGrowthTrainer',  # Removed as module is missing
    'MultiBranchDNN',
    'MultiBranchTrainer',
    'FeatureBranch',
    'AttentionFusion',
    'HybridTransformerDNN',
    'TransformerTrainer',
    'TransformerFusion',
    'PositionalEncoding',
    'CombinedDNNTransformer',
    'CombinedModelTrainer',
    'BasicDNNPath',
    'HybridRFTransformer',
    'HybridRFTransformerTrainer',
    'EnsembleModel',
    'EnsembleModelTrainer',
    'ImprovedRandomForest',
    'ImprovedRandomForestTrainer'
]
