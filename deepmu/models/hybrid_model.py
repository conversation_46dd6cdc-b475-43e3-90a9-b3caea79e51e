"""
Hybrid model for DeepMu combining Random Forest for growth rate and neural network for temperature.

This module provides a hybrid model architecture that uses a Random Forest for growth rate
prediction and a neural network for optimal temperature prediction, leveraging the strengths
of each approach for the specific prediction tasks.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from sklearn.ensemble import RandomForestRegressor
import logging
from typing import Dict, List, Tuple, Optional, Union, Any

# Setup logging
logger = logging.getLogger(__name__)

class HybridGrowthModel:
    """
    Hybrid model with Random Forest for growth rate and neural network for optimal temperature.

    This model uses a Random Forest for growth rate prediction and a neural network for
    optimal temperature prediction, leveraging the strengths of each approach.
    """

    def __init__(
        self,
        growth_rate_features: List[str],
        optimal_temperature_features: List[str],
        hidden_dim: int = 128,
        dropout: float = 0.3,
        num_layers: int = 2,
        use_batch_norm: bool = True,
        n_estimators: int = 100,
        max_depth: int = 10,
        device: torch.device = None
    ):
        """
        Initialize the hybrid model.

        Args:
            growth_rate_features: List of feature names for growth rate prediction
            optimal_temperature_features: List of feature names for optimal temperature prediction
            hidden_dim: Dimension of hidden layers for neural network
            dropout: Dropout probability for neural network
            num_layers: Number of hidden layers for neural network
            use_batch_norm: Whether to use batch normalization for neural network
            n_estimators: Number of trees in the Random Forest
            max_depth: Maximum depth of trees in the Random Forest
            device: Device to use for neural network (cpu or cuda)
        """
        self.growth_rate_features = growth_rate_features
        self.optimal_temperature_features = optimal_temperature_features
        self.all_features = list(set(growth_rate_features + optimal_temperature_features))
        self.feature_to_idx = {feature: i for i, feature in enumerate(self.all_features)}
        self.device = device if device is not None else torch.device('cuda' if torch.cuda.is_available() else 'cpu')

        # Create indices for each feature set
        self.growth_rate_indices = [self.feature_to_idx[f] for f in growth_rate_features]
        self.optimal_temperature_indices = [self.feature_to_idx[f] for f in optimal_temperature_features]

        # Calculate input dimensions
        self.growth_rate_input_dim = len(growth_rate_features)
        self.optimal_temperature_input_dim = len(optimal_temperature_features)

        # Create Random Forest for growth rate prediction
        self.growth_rate_model = RandomForestRegressor(
            n_estimators=n_estimators,
            max_depth=max_depth,
            random_state=42,
            n_jobs=-1  # Use all available cores
        )

        # Create neural network for optimal temperature prediction
        self.optimal_temperature_model = TemperatureNN(
            input_dim=self.optimal_temperature_input_dim,
            hidden_dim=hidden_dim,
            output_dim=1,
            num_layers=num_layers,
            dropout=dropout,
            use_batch_norm=use_batch_norm
        ).to(self.device)

        # Create optimizer for neural network
        self.optimizer = torch.optim.Adam(
            self.optimal_temperature_model.parameters(),
            lr=0.001,
            weight_decay=1e-5
        )

        # Create loss function for neural network
        self.criterion = nn.MSELoss()

        logger.info(f"Initialized HybridGrowthModel with {len(growth_rate_features)} growth rate features and {len(optimal_temperature_features)} optimal temperature features")

    def train(self, dataloader, num_epochs=10):
        """
        Train the hybrid model.

        Args:
            dataloader: DataLoader for training data
            num_epochs: Number of epochs to train the neural network
        """
        # Collect data for Random Forest
        X_growth = []
        y_growth = []

        # Collect data for neural network
        self.optimal_temperature_model.train()

        # First pass: collect data for Random Forest
        logger.info("Collecting data for Random Forest...")
        for features, targets in dataloader:
            # Extract feature values for growth rate
            growth_features = self._extract_features(features, self.growth_rate_features)
            X_growth.extend(growth_features)

            # Extract growth rate targets
            if 'growth_rate' in targets:
                y_growth.extend(targets['growth_rate'].detach().cpu().numpy())

        # Train Random Forest
        logger.info(f"Training Random Forest with {len(X_growth)} samples...")
        self.growth_rate_model.fit(X_growth, y_growth)

        # Train neural network
        logger.info(f"Training neural network for {num_epochs} epochs...")
        for epoch in range(num_epochs):
            epoch_loss = 0.0
            num_batches = 0

            for features, targets in dataloader:
                # Skip if no optimal temperature targets
                if 'optimal_temperature' not in targets:
                    continue

                # Extract feature values for optimal temperature
                temp_features = self._extract_features(features, self.optimal_temperature_features)
                temp_features_tensor = torch.tensor(temp_features, dtype=torch.float32).to(self.device)

                # Extract optimal temperature targets
                temp_targets = targets['optimal_temperature'].to(self.device)

                # Forward pass
                self.optimizer.zero_grad()
                outputs = self.optimal_temperature_model(temp_features_tensor)
                loss = self.criterion(outputs.squeeze(), temp_targets)

                # Backward pass and optimize
                loss.backward()
                self.optimizer.step()

                epoch_loss += loss.item()
                num_batches += 1

            # Log progress
            if num_batches > 0:
                logger.info(f"Epoch {epoch+1}/{num_epochs}, Loss: {epoch_loss/num_batches:.6f}")

    def evaluate(self, dataloader):
        """
        Evaluate the hybrid model.

        Args:
            dataloader: DataLoader for evaluation data

        Returns:
            Dictionary of evaluation metrics
        """
        # Collect predictions and targets
        growth_preds = []
        growth_targets = []
        temp_preds = []
        temp_targets = []

        # Set neural network to evaluation mode
        self.optimal_temperature_model.eval()

        with torch.no_grad():
            for features, targets in dataloader:
                # Make predictions
                predictions = self.predict_batch(features)

                # Collect growth rate predictions and targets
                if 'growth_rate' in targets and 'growth_rate' in predictions:
                    growth_preds.extend(predictions['growth_rate'])
                    growth_targets.extend(targets['growth_rate'].detach().cpu().numpy())

                # Collect optimal temperature predictions and targets
                if 'optimal_temperature' in targets and 'optimal_temperature' in predictions:
                    temp_preds.extend(predictions['optimal_temperature'])
                    temp_targets.extend(targets['optimal_temperature'].detach().cpu().numpy())

        # Calculate metrics
        metrics = {}

        if growth_preds and growth_targets:
            growth_preds = np.array(growth_preds)
            growth_targets = np.array(growth_targets)

            # Calculate MSE
            growth_mse = np.mean((growth_preds - growth_targets) ** 2)
            metrics['growth_rate_mse'] = growth_mse

            # Calculate R²
            growth_r2 = 1 - np.sum((growth_preds - growth_targets) ** 2) / np.sum((growth_targets - np.mean(growth_targets)) ** 2)
            metrics['growth_rate_r2'] = growth_r2

            # Store predictions and targets
            metrics['growth_rate_preds'] = growth_preds.tolist()
            metrics['growth_rate_targets'] = growth_targets.tolist()

        if temp_preds and temp_targets:
            temp_preds = np.array(temp_preds)
            temp_targets = np.array(temp_targets)

            # Calculate MSE
            temp_mse = np.mean((temp_preds - temp_targets) ** 2)
            metrics['optimal_temperature_mse'] = temp_mse

            # Calculate R²
            temp_r2 = 1 - np.sum((temp_preds - temp_targets) ** 2) / np.sum((temp_targets - np.mean(temp_targets)) ** 2)
            metrics['optimal_temperature_r2'] = temp_r2

            # Store predictions and targets
            metrics['optimal_temperature_preds'] = temp_preds.tolist()
            metrics['optimal_temperature_targets'] = temp_targets.tolist()

        return metrics

    def predict_batch(self, features):
        """
        Make predictions for a batch of features.

        Args:
            features: Dictionary of feature tensors

        Returns:
            Dictionary of predictions
        """
        # Extract feature values
        growth_features = self._extract_features(features, self.growth_rate_features)
        temp_features = self._extract_features(features, self.optimal_temperature_features)

        # Make predictions
        predictions = {}

        # Growth rate predictions (Random Forest)
        if growth_features.shape[0] > 0:
            growth_preds = self.growth_rate_model.predict(growth_features)
            predictions['growth_rate'] = growth_preds

        # Optimal temperature predictions (neural network)
        if temp_features.shape[0] > 0:
            temp_features_tensor = torch.tensor(temp_features, dtype=torch.float32).to(self.device)
            self.optimal_temperature_model.eval()
            with torch.no_grad():
                temp_preds = self.optimal_temperature_model(temp_features_tensor).squeeze().detach().cpu().numpy()
            predictions['optimal_temperature'] = temp_preds

        return predictions

    def _extract_features(self, features_dict, feature_list):
        """
        Extract feature values from a dictionary of features.

        Args:
            features_dict: Dictionary of feature tensors
            feature_list: List of feature names to extract

        Returns:
            List of feature vectors
        """
        # Get batch size
        batch_size = len(next(iter(features_dict.values())))

        # Create feature matrix
        feature_matrix = np.zeros((batch_size, len(feature_list)))

        # Fill feature matrix
        for i, feature_name in enumerate(feature_list):
            if feature_name in features_dict:
                feature_value = features_dict[feature_name]
                if isinstance(feature_value, torch.Tensor):
                    feature_value = feature_value.detach().cpu().numpy()
                feature_matrix[:, i] = feature_value

        return feature_matrix

    def save(self, path):
        """
        Save the hybrid model.

        Args:
            path: Path to save the model
        """
        import joblib
        import os

        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(path), exist_ok=True)

        # Save Random Forest
        joblib.dump(self.growth_rate_model, f"{path}_rf.joblib")

        # Save neural network
        torch.save(self.optimal_temperature_model.state_dict(), f"{path}_nn.pt")

        # Save feature lists
        joblib.dump({
            'growth_rate_features': self.growth_rate_features,
            'optimal_temperature_features': self.optimal_temperature_features
        }, f"{path}_features.joblib")

        logger.info(f"Model saved to {path}")

    def load(self, path):
        """
        Load the hybrid model.

        Args:
            path: Path to load the model from
        """
        import joblib

        # Load Random Forest
        self.growth_rate_model = joblib.load(f"{path}_rf.joblib")

        # Load neural network
        self.optimal_temperature_model.load_state_dict(torch.load(f"{path}_nn.pt", map_location=self.device))

        # Load feature lists
        features = joblib.load(f"{path}_features.joblib")
        self.growth_rate_features = features['growth_rate_features']
        self.optimal_temperature_features = features['optimal_temperature_features']

        # Update indices
        self.all_features = list(set(self.growth_rate_features + self.optimal_temperature_features))
        self.feature_to_idx = {feature: i for i, feature in enumerate(self.all_features)}
        self.growth_rate_indices = [self.feature_to_idx[f] for f in self.growth_rate_features]
        self.optimal_temperature_indices = [self.feature_to_idx[f] for f in self.optimal_temperature_features]

        logger.info(f"Model loaded from {path}")


class TemperatureNN(nn.Module):
    """
    Neural network for optimal temperature prediction.
    """

    def __init__(
        self,
        input_dim: int,
        hidden_dim: int,
        output_dim: int,
        num_layers: int,
        dropout: float,
        use_batch_norm: bool
    ):
        """
        Initialize the neural network.

        Args:
            input_dim: Input dimension
            hidden_dim: Hidden dimension
            output_dim: Output dimension
            num_layers: Number of hidden layers
            dropout: Dropout probability
            use_batch_norm: Whether to use batch normalization
        """
        super().__init__()

        layers = []

        # Input layer
        if use_batch_norm:
            layers.append(nn.Linear(input_dim, hidden_dim))
            layers.append(nn.BatchNorm1d(hidden_dim))
            layers.append(nn.LeakyReLU(0.1))
            layers.append(nn.Dropout(dropout))
        else:
            layers.append(nn.Linear(input_dim, hidden_dim))
            layers.append(nn.LeakyReLU(0.1))
            layers.append(nn.Dropout(dropout))

        # Hidden layers
        for _ in range(num_layers - 1):
            if use_batch_norm:
                layers.append(nn.Linear(hidden_dim, hidden_dim))
                layers.append(nn.BatchNorm1d(hidden_dim))
                layers.append(nn.LeakyReLU(0.1))
                layers.append(nn.Dropout(dropout))
            else:
                layers.append(nn.Linear(hidden_dim, hidden_dim))
                layers.append(nn.LeakyReLU(0.1))
                layers.append(nn.Dropout(dropout))

        # Output layer
        layers.append(nn.Linear(hidden_dim, output_dim))

        self.model = nn.Sequential(*layers)

    def forward(self, x):
        """
        Forward pass through the neural network.

        Args:
            x: Input tensor

        Returns:
            Output tensor
        """
        return self.model(x)


def create_hybrid_model(
    growth_rate_features: List[str],
    optimal_temperature_features: List[str],
    hidden_dim: int = 128,
    dropout: float = 0.3,
    num_layers: int = 2,
    use_batch_norm: bool = True,
    n_estimators: int = 100,
    max_depth: int = 10,
    device: torch.device = None
) -> HybridGrowthModel:
    """
    Create a hybrid model with Random Forest for growth rate and neural network for temperature.

    Args:
        growth_rate_features: List of feature names for growth rate prediction
        optimal_temperature_features: List of feature names for optimal temperature prediction
        hidden_dim: Dimension of hidden layers for neural network
        dropout: Dropout probability for neural network
        num_layers: Number of hidden layers for neural network
        use_batch_norm: Whether to use batch normalization for neural network
        n_estimators: Number of trees in the Random Forest
        max_depth: Maximum depth of trees in the Random Forest
        device: Device to use for neural network (cpu or cuda)

    Returns:
        HybridGrowthModel
    """
    return HybridGrowthModel(
        growth_rate_features=growth_rate_features,
        optimal_temperature_features=optimal_temperature_features,
        hidden_dim=hidden_dim,
        dropout=dropout,
        num_layers=num_layers,
        use_batch_norm=use_batch_norm,
        n_estimators=n_estimators,
        max_depth=max_depth,
        device=device
    )
