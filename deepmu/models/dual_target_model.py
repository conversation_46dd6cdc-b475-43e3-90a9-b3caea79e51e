"""
Dual-target model for DeepMu with separate feature sets for growth rate and optimal temperature.

This module provides a model architecture that uses separate feature sets for
growth rate and optimal temperature prediction, allowing for more specialized
and accurate predictions for each target.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import logging
from typing import Dict, List, Tuple, Optional, Union, Any

# Setup logging
logger = logging.getLogger(__name__)

class DualTargetGrowthModel(nn.Module):
    """
    Dual-target model with separate feature sets for growth rate and optimal temperature.

    This model uses separate feature selectors for growth rate and optimal temperature,
    allowing for more specialized and accurate predictions for each target.
    """

    def __init__(
        self,
        growth_rate_features: List[str],
        optimal_temperature_features: List[str],
        hidden_dim: int = 128,
        dropout: float = 0.3,
        num_layers: int = 2,
        use_batch_norm: bool = True
    ):
        """
        Initialize the dual-target model.

        Args:
            growth_rate_features: List of feature names for growth rate prediction
            optimal_temperature_features: List of feature names for optimal temperature prediction
            hidden_dim: Dimension of hidden layers
            dropout: Dropout probability
            num_layers: Number of hidden layers
            use_batch_norm: Whether to use batch normalization
        """
        super().__init__()

        self.growth_rate_features = growth_rate_features
        self.optimal_temperature_features = optimal_temperature_features
        self.all_features = list(set(growth_rate_features + optimal_temperature_features))
        self.feature_to_idx = {feature: i for i, feature in enumerate(self.all_features)}

        # Create indices for each feature set
        self.growth_rate_indices = torch.tensor([self.feature_to_idx[f] for f in growth_rate_features], dtype=torch.long)
        self.optimal_temperature_indices = torch.tensor([self.feature_to_idx[f] for f in optimal_temperature_features], dtype=torch.long)

        # Calculate input dimensions
        self.growth_rate_input_dim = len(growth_rate_features)
        self.optimal_temperature_input_dim = len(optimal_temperature_features)

        # Create growth rate prediction network
        self.growth_rate_network = self._create_network(
            input_dim=self.growth_rate_input_dim,
            hidden_dim=hidden_dim,
            output_dim=1,
            num_layers=num_layers,
            dropout=dropout,
            use_batch_norm=use_batch_norm
        )

        # Create optimal temperature prediction network
        self.optimal_temperature_network = self._create_network(
            input_dim=self.optimal_temperature_input_dim,
            hidden_dim=hidden_dim,
            output_dim=1,
            num_layers=num_layers,
            dropout=dropout,
            use_batch_norm=use_batch_norm
        )

        logger.info(f"Initialized DualTargetGrowthModel with {len(growth_rate_features)} growth rate features and {len(optimal_temperature_features)} optimal temperature features")

    def _create_network(
        self,
        input_dim: int,
        hidden_dim: int,
        output_dim: int,
        num_layers: int,
        dropout: float,
        use_batch_norm: bool
    ) -> nn.Sequential:
        """
        Create a feed-forward neural network.

        Args:
            input_dim: Input dimension
            hidden_dim: Hidden dimension
            output_dim: Output dimension
            num_layers: Number of hidden layers
            dropout: Dropout probability
            use_batch_norm: Whether to use batch normalization

        Returns:
            Sequential neural network
        """
        layers = []

        # Input layer
        if use_batch_norm:
            layers.append(nn.Linear(input_dim, hidden_dim))
            layers.append(nn.BatchNorm1d(hidden_dim))
            layers.append(nn.ReLU())
            layers.append(nn.Dropout(dropout))
        else:
            layers.append(nn.Linear(input_dim, hidden_dim))
            layers.append(nn.ReLU())
            layers.append(nn.Dropout(dropout))

        # Hidden layers
        for _ in range(num_layers - 1):
            if use_batch_norm:
                layers.append(nn.Linear(hidden_dim, hidden_dim))
                layers.append(nn.BatchNorm1d(hidden_dim))
                layers.append(nn.ReLU())
                layers.append(nn.Dropout(dropout))
            else:
                layers.append(nn.Linear(hidden_dim, hidden_dim))
                layers.append(nn.ReLU())
                layers.append(nn.Dropout(dropout))

        # Output layer
        layers.append(nn.Linear(hidden_dim, output_dim))

        return nn.Sequential(*layers)

    def forward(self, features: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        Forward pass through the model.

        Args:
            features: Dictionary of feature tensors

        Returns:
            Dictionary of predictions
        """
        # Extract and concatenate all features
        feature_values = []
        for feature in self.all_features:
            if feature in features:
                feature_values.append(features[feature].float().view(-1, 1))
            else:
                # If feature is missing, use zeros
                feature_values.append(torch.zeros(features[list(features.keys())[0]].size(0), 1, device=next(self.parameters()).device))

        # Concatenate all features
        all_features_tensor = torch.cat(feature_values, dim=1)

        # Extract features for each target
        growth_rate_features_tensor = all_features_tensor[:, self.growth_rate_indices]
        optimal_temperature_features_tensor = all_features_tensor[:, self.optimal_temperature_indices]

        # Make predictions
        growth_rate_pred = self.growth_rate_network(growth_rate_features_tensor)
        optimal_temperature_pred = self.optimal_temperature_network(optimal_temperature_features_tensor)

        return {
            'growth_rate': growth_rate_pred.squeeze(-1),
            'optimal_temperature': optimal_temperature_pred.squeeze(-1)
        }

    def predict(self, features: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        Make predictions with the model.

        Args:
            features: Dictionary of feature tensors

        Returns:
            Dictionary of predictions
        """
        self.eval()
        with torch.no_grad():
            return self.forward(features)


def create_dual_target_model(
    growth_rate_features: List[str],
    optimal_temperature_features: List[str],
    hidden_dim: int = 128,
    dropout: float = 0.3,
    num_layers: int = 2,
    use_batch_norm: bool = True
) -> DualTargetGrowthModel:
    """
    Create a dual-target model with separate feature sets.

    Args:
        growth_rate_features: List of feature names for growth rate prediction
        optimal_temperature_features: List of feature names for optimal temperature prediction
        hidden_dim: Dimension of hidden layers
        dropout: Dropout probability
        num_layers: Number of hidden layers
        use_batch_norm: Whether to use batch normalization

    Returns:
        DualTargetGrowthModel
    """
    return DualTargetGrowthModel(
        growth_rate_features=growth_rate_features,
        optimal_temperature_features=optimal_temperature_features,
        hidden_dim=hidden_dim,
        dropout=dropout,
        num_layers=num_layers,
        use_batch_norm=use_batch_norm
    )
