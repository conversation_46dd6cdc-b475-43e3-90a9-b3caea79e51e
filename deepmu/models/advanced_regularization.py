"""Advanced Regularization Techniques Module

This module implements advanced regularization techniques for deep neural networks:
1. Stochastic Weight Averaging (SWA)
2. Mixup Augmentation
3. Label Smoothing
4. Spectral Normalization
5. Sharpness-Aware Minimization (SAM)
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.optim import Optimizer
import numpy as np
from typing import Dict, List, Tuple, Optional, Union, Callable
import copy
import math


class StochasticWeightAveraging:
    """Stochastic Weight Averaging (SWA) for improving generalization.

    SWA averages weights from different points in training trajectory to find
    flatter minima, which often leads to better generalization.

    Reference:
        <PERSON><PERSON><PERSON><PERSON>, <PERSON>, et al. (2018). Averaging Weights Leads to Wider Optima and Better Generalization.
        https://arxiv.org/abs/1803.05407
    """

    def __init__(
        self,
        model: nn.Module,
        swa_start: int = 10,
        swa_freq: int = 5,
        swa_lr: Optional[float] = None
    ):
        """Initialize SWA.

        Args:
            model: Model to apply SWA to
            swa_start: Epoch to start SWA from
            swa_freq: Frequency of model averaging
            swa_lr: Learning rate for SWA phase (if None, use current lr)
        """
        self.model = model
        self.swa_start = swa_start
        self.swa_freq = swa_freq
        self.swa_lr = swa_lr

        # Initialize SWA model with a copy of the current model
        self.swa_model = copy.deepcopy(model)
        self.swa_n = 0

    def update(self, epoch: int, optimizer: Optional[Optimizer] = None) -> bool:
        """Update SWA model if conditions are met.

        Args:
            epoch: Current epoch
            optimizer: Optimizer to update learning rate (if swa_lr is not None)

        Returns:
            Whether SWA model was updated
        """
        if epoch < self.swa_start or (epoch - self.swa_start) % self.swa_freq != 0:
            return False

        # Update learning rate if specified
        if self.swa_lr is not None and optimizer is not None:
            for param_group in optimizer.param_groups:
                param_group['lr'] = self.swa_lr

        # Update SWA model
        if self.swa_n == 0:
            # First update, just copy the model
            for swa_param, param in zip(self.swa_model.parameters(), self.model.parameters()):
                # Handle different shapes safely
                if swa_param.data.shape == param.data.shape:
                    swa_param.data.copy_(param.data)
                else:
                    print(f"Warning: Shape mismatch in SWA update. SWA param shape: {swa_param.data.shape}, model param shape: {param.data.shape}")
        else:
            # Weighted average
            for swa_param, param in zip(self.swa_model.parameters(), self.model.parameters()):
                # Handle different shapes safely
                if swa_param.data.shape == param.data.shape:
                    swa_param.data.mul_(self.swa_n / (self.swa_n + 1))
                    swa_param.data.add_(param.data / (self.swa_n + 1))
                else:
                    print(f"Warning: Shape mismatch in SWA update. SWA param shape: {swa_param.data.shape}, model param shape: {param.data.shape}")

        self.swa_n += 1
        return True

    def swap_swa_model(self) -> nn.Module:
        """Swap the current model with the SWA model.

        Returns:
            Original model
        """
        # Save original model
        original_model = copy.deepcopy(self.model)

        # Copy SWA model to current model
        for param, swa_param in zip(self.model.parameters(), self.swa_model.parameters()):
            # Handle different shapes safely
            if param.data.shape == swa_param.data.shape:
                param.data.copy_(swa_param.data)
            else:
                print(f"Warning: Shape mismatch in SWA swap. Model param shape: {param.data.shape}, SWA param shape: {swa_param.data.shape}")

        # Update batch norm statistics
        self._update_bn(self.model)

        return original_model

    def _update_bn(self, model: nn.Module, loader: Optional[torch.utils.data.DataLoader] = None):
        """Update batch normalization statistics for the SWA model.

        Args:
            model: Model to update BN statistics for
            loader: DataLoader to use for updating BN statistics
        """
        if loader is None:
            # If no loader is provided, just reset running stats
            for module in model.modules():
                if isinstance(module, (nn.BatchNorm1d, nn.BatchNorm2d, nn.BatchNorm3d)):
                    module.reset_running_stats()
            return

        # Set model to training mode to update BN running stats
        model.train()

        # Disable gradients
        with torch.no_grad():
            for batch in loader:
                if isinstance(batch, dict) and 'features' in batch:
                    # Handle dictionary input
                    features = batch['features']
                    if isinstance(features, dict):
                        # Multi-branch model
                        model(features)
                    else:
                        # Single input model
                        model(features)
                else:
                    # Handle tuple/list input
                    if isinstance(batch[0], dict):
                        model(batch[0])
                    else:
                        x = batch[0]
                        model(x)


class MixupAugmentation:
    """Mixup data augmentation for training deep neural networks.

    Mixup creates synthetic training examples by mixing pairs of examples and their labels.

    Reference:
        Zhang, H., et al. (2017). mixup: Beyond Empirical Risk Minimization.
        https://arxiv.org/abs/1710.09412
    """

    def __init__(self, alpha: float = 1.0):
        """Initialize Mixup.

        Args:
            alpha: Parameter for Beta distribution (larger values = more mixing)
        """
        self.alpha = alpha

    def __call__(
        self,
        features: Union[torch.Tensor, Dict[str, torch.Tensor]],
        targets: Union[torch.Tensor, Dict[str, torch.Tensor]]
    ) -> Tuple[Union[torch.Tensor, Dict[str, torch.Tensor]], Union[torch.Tensor, Dict[str, torch.Tensor]]]:
        """Apply mixup to features and targets.

        Args:
            features: Input features (tensor or dictionary of tensors)
            targets: Target values (tensor or dictionary of tensors)

        Returns:
            Tuple of (mixed_features, mixed_targets)
        """
        # Sample mixing parameter from Beta distribution
        if self.alpha > 0:
            lam = np.random.beta(self.alpha, self.alpha)
        else:
            lam = 1.0

        batch_size = self._get_batch_size(features)

        # Generate permutation
        index = torch.randperm(batch_size, device=self._get_device(features))

        # Mix features
        mixed_features = self._mix_features(features, index, lam)

        # Mix targets
        mixed_targets = self._mix_targets(targets, index, lam)

        return mixed_features, mixed_targets

    def _get_batch_size(self, features: Union[torch.Tensor, Dict[str, torch.Tensor]]) -> int:
        """Get batch size from features.

        Args:
            features: Input features

        Returns:
            Batch size
        """
        if isinstance(features, dict):
            # Get batch size from first tensor in dictionary
            return next(iter(features.values())).size(0)
        else:
            return features.size(0)

    def _get_device(self, features: Union[torch.Tensor, Dict[str, torch.Tensor]]) -> torch.device:
        """Get device from features.

        Args:
            features: Input features

        Returns:
            Device
        """
        if isinstance(features, dict):
            # Get device from first tensor in dictionary
            return next(iter(features.values())).device
        else:
            return features.device

    def _mix_features(
        self,
        features: Union[torch.Tensor, Dict[str, torch.Tensor]],
        index: torch.Tensor,
        lam: float
    ) -> Union[torch.Tensor, Dict[str, torch.Tensor]]:
        """Mix features.

        Args:
            features: Input features
            index: Permutation indices
            lam: Mixing parameter

        Returns:
            Mixed features
        """
        if isinstance(features, dict):
            # Mix each tensor in dictionary
            mixed_features = {}
            for key, value in features.items():
                mixed_features[key] = lam * value + (1 - lam) * value[index]
            return mixed_features
        else:
            # Mix single tensor
            return lam * features + (1 - lam) * features[index]

    def _mix_targets(
        self,
        targets: Union[torch.Tensor, Dict[str, torch.Tensor]],
        index: torch.Tensor,
        lam: float
    ) -> Union[torch.Tensor, Dict[str, torch.Tensor]]:
        """Mix targets.

        Args:
            targets: Target values
            index: Permutation indices
            lam: Mixing parameter

        Returns:
            Mixed targets
        """
        if isinstance(targets, dict):
            # Mix each tensor in dictionary
            mixed_targets = {}
            for key, value in targets.items():
                mixed_targets[key] = lam * value + (1 - lam) * value[index]
            return mixed_targets
        else:
            # Mix single tensor
            return lam * targets + (1 - lam) * targets[index]


class LabelSmoothing:
    """Label smoothing for preventing overconfidence.

    Label smoothing prevents the model from becoming overconfident by smoothing
    the target values.

    Reference:
        Szegedy, C., et al. (2016). Rethinking the Inception Architecture for Computer Vision.
        https://arxiv.org/abs/1512.00567
    """

    def __init__(self, smoothing: float = 0.1):
        """Initialize label smoothing.

        Args:
            smoothing: Smoothing parameter (0 = no smoothing, 1 = maximum smoothing)
        """
        self.smoothing = smoothing

    def __call__(
        self,
        targets: Union[torch.Tensor, Dict[str, torch.Tensor]]
    ) -> Union[torch.Tensor, Dict[str, torch.Tensor]]:
        """Apply label smoothing to targets.

        Args:
            targets: Target values

        Returns:
            Smoothed targets
        """
        if isinstance(targets, dict):
            # Apply smoothing to each tensor in dictionary
            smoothed_targets = {}
            for key, value in targets.items():
                smoothed_targets[key] = self._smooth_tensor(value)
            return smoothed_targets
        else:
            # Apply smoothing to single tensor
            return self._smooth_tensor(targets)

    def _smooth_tensor(self, tensor: torch.Tensor) -> torch.Tensor:
        """Apply label smoothing to a single tensor.

        Args:
            tensor: Target tensor

        Returns:
            Smoothed tensor
        """
        # For regression tasks, add small Gaussian noise
        noise = torch.randn_like(tensor) * self.smoothing
        return tensor * (1 - self.smoothing) + noise


class SpectralNormalization:
    """Spectral Normalization for stabilizing training.

    Spectral normalization constrains the spectral norm (largest singular value)
    of weight matrices, which can stabilize training and improve generalization.

    Reference:
        Miyato, T., et al. (2018). Spectral Normalization for Generative Adversarial Networks.
        https://arxiv.org/abs/1802.05957
    """

    def __init__(self, model: nn.Module, n_power_iterations: int = 1):
        """Apply spectral normalization to linear and convolutional layers.

        Args:
            model: Model to apply spectral normalization to
            n_power_iterations: Number of power iterations for estimating spectral norm
        """
        for name, module in model.named_modules():
            if isinstance(module, (nn.Linear, nn.Conv1d, nn.Conv2d, nn.Conv3d)):
                # Replace module with spectrally normalized version
                spectral_norm = nn.utils.spectral_norm(
                    module,
                    name='weight',
                    n_power_iterations=n_power_iterations
                )

                # Get parent module and replace child
                parent_name = name.rsplit('.', 1)[0] if '.' in name else ''
                if parent_name:
                    parent = model
                    for part in parent_name.split('.'):
                        parent = getattr(parent, part)
                    setattr(parent, name.rsplit('.', 1)[1], spectral_norm)
                else:
                    setattr(model, name, spectral_norm)


class SAM(Optimizer):
    """Sharpness-Aware Minimization (SAM) optimizer.

    SAM seeks parameters that lie in neighborhoods having uniformly low loss,
    leading to better generalization.

    Reference:
        Foret, P., et al. (2020). Sharpness-Aware Minimization for Efficiently Improving Generalization.
        https://arxiv.org/abs/2010.01412
    """

    def __init__(
        self,
        params,
        base_optimizer: Optimizer,
        rho: float = 0.05,
        adaptive: bool = False,
        **kwargs
    ):
        """Initialize SAM optimizer.

        Args:
            params: Model parameters
            base_optimizer: Base optimizer class (e.g., torch.optim.SGD)
            rho: Neighborhood size
            adaptive: Whether to use adaptive SAM
            **kwargs: Additional arguments for base optimizer
        """
        defaults = dict(rho=rho, adaptive=adaptive, **kwargs)
        super(SAM, self).__init__(params, defaults)

        # Initialize base optimizer
        self.base_optimizer = base_optimizer(self.param_groups, **kwargs)
        self.param_groups = self.base_optimizer.param_groups
        self.defaults.update(self.base_optimizer.defaults)

    @torch.no_grad()
    def first_step(self, zero_grad: bool = False):
        """Compute and apply the ascent step.

        Args:
            zero_grad: Whether to zero gradients after the step
        """
        grad_norm = self._grad_norm()

        for group in self.param_groups:
            scale = group['rho'] / (grad_norm + 1e-12)

            for p in group['params']:
                if p.grad is None:
                    continue

                # Store original parameters
                self.state[p]['old_p'] = p.data.clone()

                # Compute ascent direction
                if group['adaptive']:
                    # Adaptive SAM
                    e_w = (torch.pow(p, 2) if group['adaptive'] else 1.0) * p.grad * scale
                else:
                    # Standard SAM
                    e_w = p.grad * scale

                # Apply ascent step
                p.add_(e_w)

        if zero_grad:
            self.zero_grad()

    @torch.no_grad()
    def second_step(self, zero_grad: bool = False):
        """Compute and apply the descent step.

        Args:
            zero_grad: Whether to zero gradients after the step
        """
        for group in self.param_groups:
            for p in group['params']:
                if p.grad is None or 'old_p' not in self.state[p]:
                    continue

                # Restore original parameters
                p.data = self.state[p]['old_p']

        # Apply optimizer step
        self.base_optimizer.step()

        if zero_grad:
            self.zero_grad()

    @torch.no_grad()
    def step(self, closure=None):
        """Perform a complete SAM step.

        Args:
            closure: Function that reevaluates the model and returns the loss

        Returns:
            Loss value
        """
        if closure is None:
            raise ValueError("SAM requires a closure function to reevaluate the model")

        # First step (ascent)
        self.first_step(zero_grad=True)

        # Reevaluate the model and get the loss
        with torch.enable_grad():
            loss = closure()

        # Second step (descent)
        self.second_step(zero_grad=True)

        return loss

    def _grad_norm(self):
        """Compute the gradient norm for all parameters.

        Returns:
            Gradient norm
        """
        shared_device = self.param_groups[0]['params'][0].device
        norm = torch.norm(
            torch.stack([
                ((torch.abs(p) if group['adaptive'] else 1.0) * p.grad).norm(p=2).to(shared_device)
                for group in self.param_groups for p in group['params']
                if p.grad is not None
            ]),
            p=2
        )
        return norm

    def load_state_dict(self, state_dict):
        """Load optimizer state.

        Args:
            state_dict: Optimizer state dictionary
        """
        super().load_state_dict(state_dict)
        self.base_optimizer.param_groups = self.param_groups


class GradientClipping:
    """Gradient clipping for preventing exploding gradients.

    Gradient clipping limits the norm of gradients to stabilize training.
    """

    def __init__(self, max_norm: float = 1.0, norm_type: float = 2.0):
        """Initialize gradient clipping.

        Args:
            max_norm: Maximum norm of gradients
            norm_type: Type of norm to use (1.0 = L1, 2.0 = L2)
        """
        self.max_norm = max_norm
        self.norm_type = norm_type

    def __call__(self, parameters):
        """Clip gradients.

        Args:
            parameters: Model parameters

        Returns:
            Gradient norm before clipping
        """
        return torch.nn.utils.clip_grad_norm_(
            parameters,
            max_norm=self.max_norm,
            norm_type=self.norm_type
        )


class CyclicalLearningRate:
    """Cyclical Learning Rate scheduler.

    Cyclical learning rates cycle between lower and upper bounds, which helps
    navigate saddle points and local minima.

    Reference:
        Smith, L. N. (2017). Cyclical Learning Rates for Training Neural Networks.
        https://arxiv.org/abs/1506.01186
    """

    def __init__(
        self,
        optimizer: Optimizer,
        base_lr: float,
        max_lr: float,
        step_size_up: int = 2000,
        step_size_down: Optional[int] = None,
        mode: str = 'triangular',
        gamma: float = 1.0,
        scale_fn: Optional[Callable[[int], float]] = None,
        scale_mode: str = 'cycle',
        cycle_momentum: bool = True,
        base_momentum: float = 0.8,
        max_momentum: float = 0.9,
        last_epoch: int = -1
    ):
        """Initialize cyclical learning rate scheduler.

        Args:
            optimizer: Optimizer to schedule
            base_lr: Lower learning rate bound
            max_lr: Upper learning rate bound
            step_size_up: Number of steps in the increasing half of a cycle
            step_size_down: Number of steps in the decreasing half of a cycle
            mode: One of {'triangular', 'triangular2', 'exp_range'}
            gamma: Constant for 'exp_range' mode
            scale_fn: Custom scaling function
            scale_mode: One of {'cycle', 'iterations'}
            cycle_momentum: Whether to cycle momentum
            base_momentum: Lower momentum bound
            max_momentum: Upper momentum bound
            last_epoch: Last epoch
        """
        self.optimizer = optimizer

        # Initialize scheduler
        self.scheduler = torch.optim.lr_scheduler.CyclicLR(
            optimizer=optimizer,
            base_lr=base_lr,
            max_lr=max_lr,
            step_size_up=step_size_up,
            step_size_down=step_size_down,
            mode=mode,
            gamma=gamma,
            scale_fn=scale_fn,
            scale_mode=scale_mode,
            cycle_momentum=cycle_momentum,
            base_momentum=base_momentum,
            max_momentum=max_momentum,
            last_epoch=last_epoch
        )

    def step(self, epoch: Optional[int] = None):
        """Step the scheduler.

        Args:
            epoch: Current epoch
        """
        self.scheduler.step(epoch)

    def get_lr(self):
        """Get current learning rate.

        Returns:
            Current learning rate
        """
        return self.scheduler.get_last_lr()


class OneCycleLR:
    """One Cycle Learning Rate scheduler.

    One Cycle LR follows a specific schedule: learning rate first increases from
    base_lr to max_lr, then decreases back to a very small value.

    Reference:
        Smith, L. N. (2018). A disciplined approach to neural network hyper-parameters.
        https://arxiv.org/abs/1803.09820
    """

    def __init__(
        self,
        optimizer: Optimizer,
        max_lr: float,
        total_steps: int,
        pct_start: float = 0.3,
        div_factor: float = 25.0,
        final_div_factor: float = 10000.0,
        anneal_strategy: str = 'cos',
        cycle_momentum: bool = True,
        base_momentum: float = 0.85,
        max_momentum: float = 0.95,
        last_epoch: int = -1
    ):
        """Initialize one cycle learning rate scheduler.

        Args:
            optimizer: Optimizer to schedule
            max_lr: Maximum learning rate
            total_steps: Total number of steps
            pct_start: Percentage of cycle spent increasing the learning rate
            div_factor: Initial learning rate is max_lr / div_factor
            final_div_factor: Final learning rate is max_lr / final_div_factor
            anneal_strategy: One of {'cos', 'linear'}
            cycle_momentum: Whether to cycle momentum
            base_momentum: Maximum momentum
            max_momentum: Minimum momentum
            last_epoch: Last epoch
        """
        self.optimizer = optimizer

        # Initialize scheduler
        self.scheduler = torch.optim.lr_scheduler.OneCycleLR(
            optimizer=optimizer,
            max_lr=max_lr,
            total_steps=total_steps,
            pct_start=pct_start,
            div_factor=div_factor,
            final_div_factor=final_div_factor,
            anneal_strategy=anneal_strategy,
            cycle_momentum=cycle_momentum,
            base_momentum=base_momentum,
            max_momentum=max_momentum,
            last_epoch=last_epoch
        )

    def step(self):
        """Step the scheduler."""
        self.scheduler.step()

    def get_lr(self):
        """Get current learning rate.

        Returns:
            Current learning rate
        """
        return self.scheduler.get_last_lr()
