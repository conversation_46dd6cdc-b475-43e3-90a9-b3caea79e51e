"""Transformer-based Neural Network Models Module

This module implements a hybrid multi-branch transformer architecture for microbial growth rate
and optimal growth temperature prediction, combining:
1. Separate branches for different feature types (codon, amino acid, genomic, etc.)
2. Transformer-based fusion for modeling complex feature interactions
3. Multi-task learning for joint prediction of growth rate and temperature

The architecture leverages the power of transformers for feature fusion while maintaining
the interpretability of separate branches for different feature types.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader
import math
import numpy as np
from typing import Dict, List, Optional, Tuple, Union, Any
import copy
from sklearn.metrics import r2_score, mean_squared_error

from ..utils.logging import get_logger, log_function_call

# Get logger
logger = get_logger()


class FeatureBranch(nn.Module):
    """Feature branch for processing a specific type of features.

    Each branch consists of multiple fully-connected layers with batch normalization,
    ReLU activation, and dropout. Residual connections are used to improve gradient flow.
    """

    def __init__(
        self,
        input_dim: int,
        hidden_dim: int,
        output_dim: int,
        dropout_rate: float = 0.2,
        use_batch_norm: bool = True,
        use_residual: bool = True
    ):
        """Initialize a feature branch.

        Args:
            input_dim: Input feature dimension
            hidden_dim: Hidden layer dimension
            output_dim: Output dimension
            dropout_rate: Dropout rate for regularization
            use_batch_norm: Whether to use batch normalization
            use_residual: Whether to use residual connections
        """
        super(FeatureBranch, self).__init__()

        self.use_residual = use_residual
        self.use_batch_norm = use_batch_norm

        # First layer
        self.fc1 = nn.Linear(input_dim, hidden_dim)
        self.bn1 = nn.BatchNorm1d(hidden_dim) if use_batch_norm else nn.Identity()
        self.dropout1 = nn.Dropout(dropout_rate)

        # Second layer
        self.fc2 = nn.Linear(hidden_dim, hidden_dim)
        self.bn2 = nn.BatchNorm1d(hidden_dim) if use_batch_norm else nn.Identity()
        self.dropout2 = nn.Dropout(dropout_rate)

        # Output layer
        self.fc_out = nn.Linear(hidden_dim, output_dim)

        # Residual connection for input -> hidden if dimensions don't match
        self.residual = nn.Linear(input_dim, hidden_dim) if use_residual and input_dim != hidden_dim else None

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Forward pass through the feature branch.

        Args:
            x: Input tensor [batch_size, input_dim]

        Returns:
            Output tensor [batch_size, output_dim]
        """
        # First layer
        h1 = self.fc1(x)
        h1 = self.bn1(h1)
        h1 = F.relu(h1)
        h1 = self.dropout1(h1)

        # Apply residual connection if enabled
        if self.use_residual:
            if self.residual is not None:
                residual = self.residual(x)
            else:
                residual = x
            h1 = h1 + residual

        # Second layer
        h2 = self.fc2(h1)
        h2 = self.bn2(h2)
        h2 = F.relu(h2)
        h2 = self.dropout2(h2)

        # Apply residual connection if enabled
        if self.use_residual:
            h2 = h2 + h1

        # Output layer
        out = self.fc_out(h2)

        return out


class PositionalEncoding(nn.Module):
    """Positional encoding for transformer models.

    This module adds positional information to the input embeddings,
    allowing the transformer to utilize position information.
    """

    def __init__(self, d_model: int, dropout: float = 0.1, max_len: int = 100):
        """Initialize positional encoding.

        Args:
            d_model: Embedding dimension
            dropout: Dropout rate
            max_len: Maximum sequence length
        """
        super(PositionalEncoding, self).__init__()
        self.dropout = nn.Dropout(p=dropout)

        # Create positional encoding matrix
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-math.log(10000.0) / d_model))

        # Apply sine to even indices
        pe[:, 0::2] = torch.sin(position * div_term)

        # Apply cosine to odd indices
        pe[:, 1::2] = torch.cos(position * div_term)

        # Add batch dimension
        pe = pe.unsqueeze(0)

        # Register buffer (not a parameter, but part of the module)
        self.register_buffer('pe', pe)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Add positional encoding to input tensor.

        Args:
            x: Input tensor [batch_size, seq_len, d_model]

        Returns:
            Tensor with positional encoding added [batch_size, seq_len, d_model]
        """
        x = x + self.pe[:, :x.size(1), :]
        return self.dropout(x)


class TransformerFusion(nn.Module):
    """Transformer-based fusion module for combining features from different branches.

    This module uses a transformer encoder to model complex interactions between
    features from different branches, followed by a weighted sum to produce the
    final fused representation.
    """

    def __init__(
        self,
        embed_dim: int,
        num_heads: int = 4,
        num_layers: int = 2,
        dropout_rate: float = 0.2,
        use_positional_encoding: bool = True
    ):
        """Initialize transformer fusion module.

        Args:
            embed_dim: Embedding dimension
            num_heads: Number of attention heads
            num_layers: Number of transformer layers
            dropout_rate: Dropout rate
            use_positional_encoding: Whether to use positional encoding
        """
        super(TransformerFusion, self).__init__()

        # Positional encoding
        self.use_positional_encoding = use_positional_encoding
        if use_positional_encoding:
            self.pos_encoder = PositionalEncoding(embed_dim, dropout_rate)

        # Transformer encoder
        # 注意：移除 batch_first=True 以兼容较旧版本的 PyTorch
        encoder_layers = nn.TransformerEncoderLayer(
            d_model=embed_dim,
            nhead=num_heads,
            dim_feedforward=embed_dim * 4,
            dropout=dropout_rate
        )
        self.transformer_encoder = nn.TransformerEncoder(
            encoder_layer=encoder_layers,
            num_layers=num_layers
        )

        # Branch importance weights (learned during training)
        self.branch_weights = nn.Parameter(torch.ones(1))  # Will be resized in forward pass

        # Layer normalization
        self.layer_norm = nn.LayerNorm(embed_dim)

    def forward(self, features: List[torch.Tensor]) -> torch.Tensor:
        """Forward pass through the transformer fusion module.

        Args:
            features: List of feature tensors from different branches
                      [batch_size, embed_dim]

        Returns:
            Fused features [batch_size, embed_dim]
        """
        if len(features) == 0:
            raise ValueError("No features provided for fusion")

        if len(features) == 1:
            return features[0]

        # Stack features for transformer [batch_size, num_branches, embed_dim]
        stacked_features = torch.stack(features, dim=1)

        # Apply positional encoding if enabled
        if self.use_positional_encoding:
            stacked_features = self.pos_encoder(stacked_features)

        # 转换为 TransformerEncoder 期望的格式 [num_branches, batch_size, embed_dim]
        stacked_features_transposed = stacked_features.transpose(0, 1)

        # Apply transformer encoder
        transformer_out = self.transformer_encoder(stacked_features_transposed)

        # 转换回 [batch_size, num_branches, embed_dim] 格式
        transformer_out = transformer_out.transpose(0, 1)

        # Residual connection and layer normalization
        normalized = self.layer_norm(transformer_out + stacked_features)

        # Initialize branch weights if needed
        if self.branch_weights.size(0) != normalized.size(1):
            self.branch_weights.data = torch.ones(normalized.size(1), device=normalized.device)
            self.branch_weights.data = self.branch_weights.data / normalized.size(1)

        # Apply softmax to get normalized weights
        branch_weights = F.softmax(self.branch_weights, dim=0)

        # Weighted sum [batch_size, embed_dim]
        weighted_sum = torch.einsum('bnf,n->bf', normalized, branch_weights)

        return weighted_sum


class HybridTransformerDNN(nn.Module):
    """Hybrid multi-branch transformer neural network for microbial growth rate and temperature prediction.

    This model combines:
    1. Separate branches for different feature types
    2. Transformer-based fusion for modeling complex feature interactions
    3. Multi-task learning for joint prediction of growth rate and temperature

    The architecture leverages the power of transformers for feature fusion while maintaining
    the interpretability of separate branches for different feature types.
    """

    def __init__(
        self,
        feature_dims: Dict[str, int],
        hidden_dims: List[int],
        output_dim: int = 2,
        dropout_rates: List[float] = [0.2, 0.2, 0.3],
        use_batch_norm: bool = True,
        use_residual: bool = True,
        transformer_heads: int = 4,
        transformer_layers: int = 2,
        use_positional_encoding: bool = True,
        fusion_layers: int = 2,
        multi_task: bool = True
    ):
        """Initialize the hybrid transformer DNN.

        Args:
            feature_dims: Dictionary mapping feature types to their dimensions
                          e.g., {'codon': 64, 'aa': 32, 'genomic': 128, ...}
            hidden_dims: List of hidden layer dimensions for each branch and fusion network
            output_dim: Output dimension (1 for single task, 2 for multi-task)
            dropout_rates: List of dropout rates for different parts of the network
            use_batch_norm: Whether to use batch normalization
            use_residual: Whether to use residual connections
            transformer_heads: Number of attention heads in transformer
            transformer_layers: Number of transformer layers
            use_positional_encoding: Whether to use positional encoding in transformer
            fusion_layers: Number of layers in the fusion network
            multi_task: Whether to predict both growth rate and temperature
        """
        super(HybridTransformerDNN, self).__init__()

        self.feature_dims = feature_dims
        self.hidden_dims = hidden_dims
        self.output_dim = output_dim
        self.multi_task = multi_task

        # Ensure we have enough hidden dimensions
        if len(hidden_dims) < 2:
            raise ValueError("At least 2 hidden dimensions required (branch_dim, fusion_dim)")

        branch_dim = hidden_dims[0]
        fusion_dim = hidden_dims[1]

        # Create feature branches
        self.branches = nn.ModuleDict()
        for feature_type, dim in feature_dims.items():
            if dim > 0:  # Only create branches for features with non-zero dimensions
                self.branches[feature_type] = FeatureBranch(
                    input_dim=dim,
                    hidden_dim=branch_dim,
                    output_dim=branch_dim,
                    dropout_rate=dropout_rates[0],
                    use_batch_norm=use_batch_norm,
                    use_residual=use_residual
                )

        # Transformer fusion module
        self.transformer_fusion = TransformerFusion(
            embed_dim=branch_dim,
            num_heads=transformer_heads,
            num_layers=transformer_layers,
            dropout_rate=dropout_rates[1],
            use_positional_encoding=use_positional_encoding
        )

        # Fusion network
        fusion_layers_list = []
        prev_dim = branch_dim

        for i in range(fusion_layers):
            next_dim = fusion_dim if i < fusion_layers - 1 else fusion_dim // 2
            fusion_layers_list.extend([
                nn.Linear(prev_dim, next_dim),
                nn.BatchNorm1d(next_dim) if use_batch_norm else nn.Identity(),
                nn.ReLU(),
                nn.Dropout(dropout_rates[min(i+2, len(dropout_rates)-1)])
            ])
            prev_dim = next_dim

        self.fusion_network = nn.Sequential(*fusion_layers_list)

        # Output heads
        if multi_task:
            self.growth_rate_head = nn.Linear(prev_dim, 1)
            self.temperature_head = nn.Linear(prev_dim, 1)
        else:
            self.output_head = nn.Linear(prev_dim, output_dim)

    def forward(self, x: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """Forward pass through the hybrid transformer DNN.

        Args:
            x: Dictionary mapping feature types to tensors
               e.g., {'codon': tensor, 'aa': tensor, 'genomic': tensor, ...}

        Returns:
            Dictionary with predictions:
            - 'growth_rate': Growth rate predictions [batch_size]
            - 'temperature': Temperature predictions [batch_size] (if multi_task=True)
            - 'output': Combined output [batch_size, output_dim] (if multi_task=False)
        """
        # Process each branch
        branch_outputs = []

        for feature_type, branch in self.branches.items():
            if feature_type in x and x[feature_type] is not None:
                branch_output = branch(x[feature_type])
                branch_outputs.append(branch_output)

        # If no branches were processed, return zeros
        if not branch_outputs:
            batch_size = next(iter(x.values())).size(0) if x else 1
            device = next(iter(x.values())).device if x else torch.device('cpu')

            if self.multi_task:
                return {
                    'growth_rate': torch.zeros(batch_size, device=device),
                    'temperature': torch.zeros(batch_size, device=device)
                }
            else:
                return {
                    'output': torch.zeros(batch_size, self.output_dim, device=device)
                }

        # Fuse branch outputs using transformer
        fused_features = self.transformer_fusion(branch_outputs)

        # Pass through fusion network
        fusion_output = self.fusion_network(fused_features)

        # Generate predictions
        if self.multi_task:
            growth_rate = self.growth_rate_head(fusion_output).squeeze(-1)
            temperature = self.temperature_head(fusion_output).squeeze(-1)

            return {
                'growth_rate': growth_rate,
                'temperature': temperature
            }
        else:
            output = self.output_head(fusion_output)

            return {
                'output': output
            }

    def predict_with_uncertainty(self, x: Dict[str, torch.Tensor], n_samples: int = 30) -> Dict[str, torch.Tensor]:
        """Generate predictions with uncertainty estimates using Monte Carlo Dropout.

        Args:
            x: Input features dictionary
            n_samples: Number of stochastic forward passes for uncertainty estimation

        Returns:
            Dictionary with mean predictions and uncertainty estimates (standard deviations)
        """
        # Switch to train mode to enable dropout during inference
        self.train()

        # Initialize lists to store predictions
        growth_rate_samples = []
        temperature_samples = []

        # Generate multiple predictions with dropout enabled
        for _ in range(n_samples):
            with torch.no_grad():
                predictions = self(x)

                if self.multi_task:
                    growth_rate_samples.append(predictions['growth_rate'].unsqueeze(0))
                    temperature_samples.append(predictions['temperature'].unsqueeze(0))
                else:
                    growth_rate_samples.append(predictions['output'].unsqueeze(0))

        # Stack predictions
        if self.multi_task:
            growth_rate_samples = torch.cat(growth_rate_samples, dim=0)
            temperature_samples = torch.cat(temperature_samples, dim=0)

            # Calculate mean and standard deviation
            growth_rate_mean = growth_rate_samples.mean(dim=0)
            growth_rate_std = growth_rate_samples.std(dim=0)

            temperature_mean = temperature_samples.mean(dim=0)
            temperature_std = temperature_samples.std(dim=0)

            return {
                'growth_rate_mean': growth_rate_mean,
                'growth_rate_std': growth_rate_std,
                'temperature_mean': temperature_mean,
                'temperature_std': temperature_std
            }
        else:
            output_samples = torch.cat(growth_rate_samples, dim=0)

            # Calculate mean and standard deviation
            output_mean = output_samples.mean(dim=0)
            output_std = output_samples.std(dim=0)

            return {
                'output_mean': output_mean,
                'output_std': output_std
            }


class TransformerTrainer:
    """Trainer class for the HybridTransformerDNN model."""

    def __init__(
        self,
        model: HybridTransformerDNN,
        learning_rate: float = 0.001,
        weight_decay: float = 1e-5,
        device: str = 'cuda' if torch.cuda.is_available() else 'cpu'
    ):
        """Initialize the trainer.

        Args:
            model: HybridTransformerDNN model
            learning_rate: Learning rate for optimizer
            weight_decay: Weight decay for regularization
            device: Device to use for training ('cuda' or 'cpu')
        """
        self.model = model
        self.device = device
        self.model.to(device)

        # Optimizer
        self.optimizer = optim.AdamW(
            model.parameters(),
            lr=learning_rate,
            weight_decay=weight_decay
        )

        # Learning rate scheduler
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer,
            mode='min',
            factor=0.5,
            patience=5,
            verbose=True
        )

        # Loss functions
        self.mse_loss = nn.MSELoss()

    def train_epoch(
        self,
        train_loader: DataLoader,
        multi_task_weights: Dict[str, float] = {'growth_rate': 0.5, 'temperature': 0.5}
    ) -> Dict[str, float]:
        """Train the model for one epoch.

        Args:
            train_loader: DataLoader for training data
            multi_task_weights: Weights for multi-task learning

        Returns:
            Dictionary with training metrics
        """
        self.model.train()

        total_loss = 0.0
        growth_rate_loss = 0.0
        temperature_loss = 0.0
        num_batches = 0

        for batch in train_loader:
            # Get features and targets
            features = {k: v.to(self.device) for k, v in batch['features'].items() if k in self.model.feature_dims}
            targets = {k: v.to(self.device) for k, v in batch['targets'].items()}

            # Zero gradients
            self.optimizer.zero_grad()

            # Forward pass
            predictions = self.model(features)

            # Calculate loss
            if self.model.multi_task:
                # Multi-task loss
                gr_loss = self.mse_loss(predictions['growth_rate'], targets['growth_rate'])
                temp_loss = self.mse_loss(predictions['temperature'], targets['temperature'])

                # Weighted sum of losses
                loss = multi_task_weights['growth_rate'] * gr_loss + multi_task_weights['temperature'] * temp_loss

                # Track individual losses
                growth_rate_loss += gr_loss.item()
                temperature_loss += temp_loss.item()
            else:
                # Single task loss
                loss = self.mse_loss(predictions['output'], targets['target'])

            # Backward pass and optimize
            loss.backward()
            self.optimizer.step()

            # Track metrics
            total_loss += loss.item()
            num_batches += 1

        # Calculate average metrics
        metrics = {
            'loss': total_loss / num_batches
        }

        if self.model.multi_task:
            metrics.update({
                'growth_rate_loss': growth_rate_loss / num_batches,
                'temperature_loss': temperature_loss / num_batches
            })

        return metrics

    def validate(
        self,
        val_loader: DataLoader,
        multi_task_weights: Dict[str, float] = {'growth_rate': 0.5, 'temperature': 0.5}
    ) -> Dict[str, float]:
        """Validate the model.

        Args:
            val_loader: DataLoader for validation data
            multi_task_weights: Weights for multi-task learning

        Returns:
            Dictionary with validation metrics
        """
        self.model.eval()

        total_loss = 0.0
        growth_rate_loss = 0.0
        temperature_loss = 0.0
        num_batches = 0

        # For R² calculation
        growth_rate_preds = []
        growth_rate_targets = []
        temperature_preds = []
        temperature_targets = []

        with torch.no_grad():
            for batch in val_loader:
                # Get features and targets
                features = {k: v.to(self.device) for k, v in batch['features'].items() if k in self.model.feature_dims}
                targets = {k: v.to(self.device) for k, v in batch['targets'].items()}

                # Forward pass
                predictions = self.model(features)

                # Calculate loss
                if self.model.multi_task:
                    # Multi-task loss
                    gr_loss = self.mse_loss(predictions['growth_rate'], targets['growth_rate'])
                    temp_loss = self.mse_loss(predictions['temperature'], targets['temperature'])

                    # Weighted sum of losses
                    loss = multi_task_weights['growth_rate'] * gr_loss + multi_task_weights['temperature'] * temp_loss

                    # Track individual losses
                    growth_rate_loss += gr_loss.item()
                    temperature_loss += temp_loss.item()

                    # Store predictions and targets for R² calculation
                    growth_rate_preds.append(predictions['growth_rate'].cpu().numpy())
                    growth_rate_targets.append(targets['growth_rate'].cpu().numpy())
                    temperature_preds.append(predictions['temperature'].cpu().numpy())
                    temperature_targets.append(targets['temperature'].cpu().numpy())
                else:
                    # Single task loss
                    loss = self.mse_loss(predictions['output'], targets['target'])

                    # Store predictions and targets for R² calculation
                    growth_rate_preds.append(predictions['output'].cpu().numpy())
                    growth_rate_targets.append(targets['target'].cpu().numpy())

                # Track metrics
                total_loss += loss.item()
                num_batches += 1

        # Calculate average metrics
        metrics = {
            'val_loss': total_loss / num_batches
        }

        if self.model.multi_task:
            # Concatenate predictions and targets
            growth_rate_preds = np.concatenate(growth_rate_preds)
            growth_rate_targets = np.concatenate(growth_rate_targets)
            temperature_preds = np.concatenate(temperature_preds)
            temperature_targets = np.concatenate(temperature_targets)

            # Check for NaN or infinite values
            if np.isnan(growth_rate_preds).any() or np.isinf(growth_rate_preds).any():
                logger.warning("Found NaN or infinite values in growth rate predictions. Replacing with zeros.")
                growth_rate_preds = np.nan_to_num(growth_rate_preds, nan=0.0, posinf=0.0, neginf=0.0)

            if np.isnan(temperature_preds).any() or np.isinf(temperature_preds).any():
                logger.warning("Found NaN or infinite values in temperature predictions. Replacing with zeros.")
                temperature_preds = np.nan_to_num(temperature_preds, nan=0.0, posinf=0.0, neginf=0.0)

            # Calculate R² and RMSE with error handling
            try:
                growth_rate_r2 = r2_score(growth_rate_targets, growth_rate_preds)
                growth_rate_rmse = np.sqrt(mean_squared_error(growth_rate_targets, growth_rate_preds))
            except Exception as e:
                logger.error(f"Error calculating growth rate metrics: {str(e)}")
                growth_rate_r2 = 0.0
                growth_rate_rmse = 0.0

            try:
                temperature_r2 = r2_score(temperature_targets, temperature_preds)
                temperature_rmse = np.sqrt(mean_squared_error(temperature_targets, temperature_preds))
            except Exception as e:
                logger.error(f"Error calculating temperature metrics: {str(e)}")
                temperature_r2 = 0.0
                temperature_rmse = 0.0

            metrics.update({
                'val_growth_rate_loss': growth_rate_loss / num_batches,
                'val_temperature_loss': temperature_loss / num_batches,
                'val_growth_rate_r2': growth_rate_r2,
                'val_temperature_r2': temperature_r2,
                'val_growth_rate_rmse': growth_rate_rmse,
                'val_temperature_rmse': temperature_rmse
            })
        else:
            # Concatenate predictions and targets
            growth_rate_preds = np.concatenate(growth_rate_preds)
            growth_rate_targets = np.concatenate(growth_rate_targets)

            # Check for NaN or infinite values
            if np.isnan(growth_rate_preds).any() or np.isinf(growth_rate_preds).any():
                logger.warning("Found NaN or infinite values in predictions. Replacing with zeros.")
                growth_rate_preds = np.nan_to_num(growth_rate_preds, nan=0.0, posinf=0.0, neginf=0.0)

            # Calculate R² and RMSE with error handling
            try:
                r2 = r2_score(growth_rate_targets, growth_rate_preds)
                rmse = np.sqrt(mean_squared_error(growth_rate_targets, growth_rate_preds))
            except Exception as e:
                logger.error(f"Error calculating metrics: {str(e)}")
                r2 = 0.0
                rmse = 0.0

            metrics.update({
                'val_r2': r2,
                'val_rmse': rmse
            })

        return metrics

    def train(
        self,
        train_loader: DataLoader,
        val_loader: DataLoader,
        epochs: int = 100,
        patience: int = 10,
        multi_task_weights: Dict[str, float] = {'growth_rate': 0.5, 'temperature': 0.5},
        save_path: Optional[str] = None
    ) -> Dict[str, List[float]]:
        """Train the model.

        Args:
            train_loader: DataLoader for training data
            val_loader: DataLoader for validation data
            epochs: Number of epochs to train
            patience: Patience for early stopping
            multi_task_weights: Weights for multi-task learning
            save_path: Path to save the best model

        Returns:
            Dictionary with training history
        """
        # Initialize history
        history = {
            'loss': [],
            'val_loss': []
        }

        if self.model.multi_task:
            history.update({
                'growth_rate_loss': [],
                'temperature_loss': [],
                'val_growth_rate_loss': [],
                'val_temperature_loss': [],
                'val_growth_rate_r2': [],
                'val_temperature_r2': [],
                'val_growth_rate_rmse': [],
                'val_temperature_rmse': []
            })
        else:
            history.update({
                'val_r2': [],
                'val_rmse': []
            })

        # Initialize early stopping
        best_val_loss = float('inf')
        patience_counter = 0

        # Training loop
        for epoch in range(epochs):
            # Train
            train_metrics = self.train_epoch(train_loader, multi_task_weights)

            # Validate
            val_metrics = self.validate(val_loader, multi_task_weights)

            # Update learning rate
            self.scheduler.step(val_metrics['val_loss'])

            # Update history
            for metric, value in train_metrics.items():
                history[metric].append(value)

            for metric, value in val_metrics.items():
                history[metric].append(value)

            # Print progress
            logger.info(f"Epoch {epoch+1}/{epochs}")
            logger.info(f"  Train Loss: {train_metrics['loss']:.4f}")
            logger.info(f"  Val Loss: {val_metrics['val_loss']:.4f}")

            if self.model.multi_task:
                logger.info(f"  Val Growth Rate R²: {val_metrics['val_growth_rate_r2']:.4f}")
                logger.info(f"  Val Temperature R²: {val_metrics['val_temperature_r2']:.4f}")
            else:
                logger.info(f"  Val R²: {val_metrics['val_r2']:.4f}")

            # Check for early stopping
            if val_metrics['val_loss'] < best_val_loss:
                best_val_loss = val_metrics['val_loss']
                patience_counter = 0

                # Save best model
                if save_path:
                    torch.save(self.model.state_dict(), save_path)
                    logger.info(f"  Saved best model to {save_path}")
            else:
                patience_counter += 1

                if patience_counter >= patience:
                    logger.info(f"Early stopping triggered after {epoch+1} epochs")
                    break

        return history
