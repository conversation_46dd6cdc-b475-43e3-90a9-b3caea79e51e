"""
DeepMu 序列工具。

此模块为 DeepMu 提供处理 DNA 序列的工具。
"""

import numpy as np
from typing import List, Union, Dict, Tuple, Optional

def one_hot_encode(sequence: str, max_length: Optional[int] = None) -> np.ndarray:
    """对 DNA 序列进行独热编码。

    参数:
        sequence: 要编码的 DNA 序列
        max_length: 序列的最大长度 (默认: None)

    返回:
        独热编码的序列，作为 numpy 数组
    """
    # 定义核苷酸到索引的映射
    mapping = {'A': 0, 'C': 1, 'G': 2, 'T': 3, 'N': 4}

    # 将序列转换为大写
    sequence = sequence.upper()

    # 如果提供了 max_length，则截断或填充序列
    if max_length is not None:
        if len(sequence) > max_length:
            sequence = sequence[:max_length]
        elif len(sequence) < max_length:
            sequence = sequence + 'N' * (max_length - len(sequence))

    # 创建独热编码
    encoding = np.zeros((len(sequence), 5), dtype=np.float32)
    for i, nucleotide in enumerate(sequence):
        if nucleotide in mapping:
            encoding[i, mapping[nucleotide]] = 1.0
        else:
            encoding[i, 4] = 1.0  # 未知核苷酸

    return encoding

def reverse_complement(sequence: str) -> str:
    """获取 DNA 序列的反向互补序列。

    参数:
        sequence: DNA 序列

    返回:
        序列的反向互补序列
    """
    # 定义核苷酸到其互补碱基的映射
    complement = {'A': 'T', 'C': 'G', 'G': 'C', 'T': 'A', 'N': 'N',
                 'a': 't', 'c': 'g', 'g': 'c', 't': 'a', 'n': 'n'}

    # 获取反向互补序列
    return ''.join(complement.get(nucleotide, 'N') for nucleotide in reversed(sequence))
