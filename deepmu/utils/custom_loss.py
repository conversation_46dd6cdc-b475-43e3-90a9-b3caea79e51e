"""
Custom loss functions for DeepMu models.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np

class CombinedLoss(nn.Module):
    """
    Combined loss function that balances MSE and MAPE.
    
    This loss function helps balance absolute errors (via MSE) and 
    relative errors (via MAPE) to improve model performance across
    different scales of target values.
    """
    
    def __init__(self, mse_weight=0.5, mape_weight=0.5, epsilon=1e-8):
        """
        Initialize the combined loss function.
        
        Args:
            mse_weight: Weight for the MSE component (default: 0.5)
            mape_weight: Weight for the MAPE component (default: 0.5)
            epsilon: Small constant to avoid division by zero (default: 1e-8)
        """
        super().__init__()
        self.mse_weight = mse_weight
        self.mape_weight = mape_weight
        self.epsilon = epsilon
        self.mse_loss = nn.MSELoss(reduction='mean')
        
    def forward(self, y_pred, y_true):
        """
        Calculate the combined loss.
        
        Args:
            y_pred: Predicted values
            y_true: True values
            
        Returns:
            Combined loss value
        """
        # MSE component
        mse = self.mse_loss(y_pred, y_true)
        
        # MAPE component (with protection against division by zero)
        abs_diff = torch.abs(y_pred - y_true)
        abs_val = torch.abs(y_true)
        # Add epsilon to avoid division by zero
        mape = torch.mean(abs_diff / (abs_val + self.epsilon))
        
        # Combine the losses
        combined_loss = self.mse_weight * mse + self.mape_weight * mape
        
        return combined_loss

class LogCoshLoss(nn.Module):
    """
    Log-cosh loss function.
    
    This loss function is smooth like MSE for small errors but less 
    sensitive to outliers like MAE for large errors.
    """
    
    def __init__(self):
        """Initialize the log-cosh loss function."""
        super().__init__()
        
    def forward(self, y_pred, y_true):
        """
        Calculate the log-cosh loss.
        
        Args:
            y_pred: Predicted values
            y_true: True values
            
        Returns:
            Log-cosh loss value
        """
        diff = y_pred - y_true
        return torch.mean(torch.log(torch.cosh(diff)))

class HuberLoss(nn.Module):
    """
    Huber loss function.
    
    This loss function is less sensitive to outliers than MSE but
    provides more gradient information than MAE.
    """
    
    def __init__(self, delta=1.0):
        """
        Initialize the Huber loss function.
        
        Args:
            delta: Threshold at which to switch from quadratic to linear (default: 1.0)
        """
        super().__init__()
        self.delta = delta
        
    def forward(self, y_pred, y_true):
        """
        Calculate the Huber loss.
        
        Args:
            y_pred: Predicted values
            y_true: True values
            
        Returns:
            Huber loss value
        """
        abs_diff = torch.abs(y_pred - y_true)
        quadratic = torch.min(abs_diff, torch.tensor(self.delta))
        linear = abs_diff - quadratic
        loss = 0.5 * quadratic**2 + self.delta * linear
        return torch.mean(loss)

class LogTransformedMSELoss(nn.Module):
    """
    MSE loss on log-transformed values.
    
    This loss function focuses on relative errors by working in log space.
    """
    
    def __init__(self, epsilon=1e-8):
        """
        Initialize the log-transformed MSE loss function.
        
        Args:
            epsilon: Small constant to avoid log(0) (default: 1e-8)
        """
        super().__init__()
        self.epsilon = epsilon
        self.mse_loss = nn.MSELoss(reduction='mean')
        
    def forward(self, y_pred, y_true):
        """
        Calculate the log-transformed MSE loss.
        
        Args:
            y_pred: Predicted values
            y_true: True values
            
        Returns:
            Log-transformed MSE loss value
        """
        # Add epsilon and take log
        log_pred = torch.log(torch.abs(y_pred) + self.epsilon)
        log_true = torch.log(torch.abs(y_true) + self.epsilon)
        
        # Calculate MSE in log space
        return self.mse_loss(log_pred, log_true)
