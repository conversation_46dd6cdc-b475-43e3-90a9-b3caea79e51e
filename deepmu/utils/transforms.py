"""
Data transformation utilities for DeepMu.
"""

import torch
import numpy as np
from typing import Dict, Optional, Union, Tuple

class LogTransform:
    """
    Log transformation for target values.
    
    This transform applies a log transformation to make the model focus
    more on relative errors rather than absolute errors.
    """
    
    def __init__(self, epsilon=1e-8):
        """
        Initialize the log transform.
        
        Args:
            epsilon: Small constant to avoid log(0) (default: 1e-8)
        """
        self.epsilon = epsilon
        
    def __call__(self, x):
        """
        Apply log transformation.
        
        Args:
            x: Input tensor or array
            
        Returns:
            Log-transformed values
        """
        if isinstance(x, torch.Tensor):
            return torch.log(torch.abs(x) + self.epsilon)
        else:
            return np.log(np.abs(x) + self.epsilon)
    
    def inverse(self, x):
        """
        Apply inverse log transformation.
        
        Args:
            x: Log-transformed tensor or array
            
        Returns:
            Original scale values
        """
        if isinstance(x, torch.Tensor):
            return torch.exp(x) - self.epsilon
        else:
            return np.exp(x) - self.epsilon

class MinMaxScaler:
    """
    Min-max scaling for feature normalization.
    
    This transform scales features to a specified range, typically [0, 1].
    """
    
    def __init__(self, feature_range=(0, 1)):
        """
        Initialize the min-max scaler.
        
        Args:
            feature_range: Output range for scaled data (default: (0, 1))
        """
        self.feature_range = feature_range
        self.min_ = None
        self.scale_ = None
        
    def fit(self, x):
        """
        Compute the minimum and maximum values for scaling.
        
        Args:
            x: Input tensor or array
            
        Returns:
            Self
        """
        if isinstance(x, torch.Tensor):
            self.min_ = torch.min(x, dim=0)[0]
            self.max_ = torch.max(x, dim=0)[0]
            self.scale_ = (self.feature_range[1] - self.feature_range[0]) / (self.max_ - self.min_ + 1e-8)
        else:
            self.min_ = np.min(x, axis=0)
            self.max_ = np.max(x, axis=0)
            self.scale_ = (self.feature_range[1] - self.feature_range[0]) / (self.max_ - self.min_ + 1e-8)
        return self
    
    def transform(self, x):
        """
        Scale features to the feature range.
        
        Args:
            x: Input tensor or array
            
        Returns:
            Scaled values
        """
        if self.min_ is None or self.scale_ is None:
            raise ValueError("Scaler has not been fitted. Call fit() first.")
            
        if isinstance(x, torch.Tensor):
            return self.feature_range[0] + (x - self.min_) * self.scale_
        else:
            return self.feature_range[0] + (x - self.min_) * self.scale_
    
    def fit_transform(self, x):
        """
        Fit to data, then transform it.
        
        Args:
            x: Input tensor or array
            
        Returns:
            Scaled values
        """
        return self.fit(x).transform(x)
    
    def inverse_transform(self, x):
        """
        Undo the scaling.
        
        Args:
            x: Scaled tensor or array
            
        Returns:
            Original scale values
        """
        if self.min_ is None or self.scale_ is None:
            raise ValueError("Scaler has not been fitted. Call fit() first.")
            
        if isinstance(x, torch.Tensor):
            return self.min_ + (x - self.feature_range[0]) / self.scale_
        else:
            return self.min_ + (x - self.feature_range[0]) / self.scale_

class StandardScaler:
    """
    Standardization for feature normalization.
    
    This transform standardizes features by removing the mean and scaling to unit variance.
    """
    
    def __init__(self):
        """Initialize the standard scaler."""
        self.mean_ = None
        self.std_ = None
        
    def fit(self, x):
        """
        Compute the mean and standard deviation for scaling.
        
        Args:
            x: Input tensor or array
            
        Returns:
            Self
        """
        if isinstance(x, torch.Tensor):
            self.mean_ = torch.mean(x, dim=0)
            self.std_ = torch.std(x, dim=0) + 1e-8  # Add small epsilon to avoid division by zero
        else:
            self.mean_ = np.mean(x, axis=0)
            self.std_ = np.std(x, axis=0) + 1e-8
        return self
    
    def transform(self, x):
        """
        Standardize features.
        
        Args:
            x: Input tensor or array
            
        Returns:
            Standardized values
        """
        if self.mean_ is None or self.std_ is None:
            raise ValueError("Scaler has not been fitted. Call fit() first.")
            
        if isinstance(x, torch.Tensor):
            return (x - self.mean_) / self.std_
        else:
            return (x - self.mean_) / self.std_
    
    def fit_transform(self, x):
        """
        Fit to data, then transform it.
        
        Args:
            x: Input tensor or array
            
        Returns:
            Standardized values
        """
        return self.fit(x).transform(x)
    
    def inverse_transform(self, x):
        """
        Undo the standardization.
        
        Args:
            x: Standardized tensor or array
            
        Returns:
            Original scale values
        """
        if self.mean_ is None or self.std_ is None:
            raise ValueError("Scaler has not been fitted. Call fit() first.")
            
        if isinstance(x, torch.Tensor):
            return x * self.std_ + self.mean_
        else:
            return x * self.std_ + self.mean_
