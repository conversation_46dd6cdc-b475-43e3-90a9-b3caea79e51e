"""
Feature selection utilities for DeepMu.

This module provides functions for selecting the most important features
for microbial growth rate and optimal temperature prediction.
"""

import numpy as np
import torch
from typing import Dict, List, Tuple, Optional, Union
from sklearn.feature_selection import SelectKBest, f_regression, mutual_info_regression
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import Lasso
import logging

logger = logging.getLogger(__name__)

class FeatureSelector:
    """Feature selector for DeepMu models."""

    def __init__(self, method='mutual_info', k=10, alpha=0.01):
        """
        Initialize the feature selector.

        Args:
            method: Feature selection method ('mutual_info', 'f_regression', 'random_forest', 'lasso')
            k: Number of features to select (for filter methods)
            alpha: Regularization strength (for Lasso)
        """
        self.method = method
        self.k = k
        self.alpha = alpha
        self.selected_indices = None
        self.feature_importances = None
        self.feature_names = None

    def fit(self, X: np.ndarray, y: np.ndarray, feature_names: Optional[List[str]] = None):
        """
        Fit the feature selector to the data.

        Args:
            X: Feature matrix (n_samples, n_features)
            y: Target values (n_samples,)
            feature_names: Names of features (optional)

        Returns:
            self
        """
        if feature_names is not None:
            self.feature_names = feature_names
        else:
            self.feature_names = [f"feature_{i}" for i in range(X.shape[1])]

        # Handle different selection methods
        if self.method == 'mutual_info':
            selector = SelectKBest(mutual_info_regression, k=min(self.k, X.shape[1]))
            selector.fit(X, y)
            self.selected_indices = selector.get_support(indices=True)
            self.feature_importances = np.zeros(X.shape[1])
            self.feature_importances[self.selected_indices] = selector.scores_[self.selected_indices]

        elif self.method == 'f_regression':
            selector = SelectKBest(f_regression, k=min(self.k, X.shape[1]))
            selector.fit(X, y)
            self.selected_indices = selector.get_support(indices=True)
            self.feature_importances = np.zeros(X.shape[1])
            self.feature_importances[self.selected_indices] = selector.scores_[self.selected_indices]

        elif self.method == 'random_forest':
            model = RandomForestRegressor(n_estimators=100, random_state=42)
            model.fit(X, y)
            self.feature_importances = model.feature_importances_
            # Select top k features
            self.selected_indices = np.argsort(self.feature_importances)[-self.k:]

        elif self.method == 'lasso':
            model = Lasso(alpha=self.alpha, random_state=42)
            model.fit(X, y)
            self.feature_importances = np.abs(model.coef_)
            # Select features with non-zero coefficients
            self.selected_indices = np.where(self.feature_importances > 0)[0]
            if len(self.selected_indices) > self.k:
                # If too many features have non-zero coefficients, select top k
                top_indices = np.argsort(self.feature_importances[self.selected_indices])[-self.k:]
                self.selected_indices = self.selected_indices[top_indices]

        else:
            raise ValueError(f"Unknown feature selection method: {self.method}")

        return self

    def transform(self, X: np.ndarray) -> np.ndarray:
        """
        Transform the data by selecting only the important features.

        Args:
            X: Feature matrix (n_samples, n_features)

        Returns:
            Transformed feature matrix (n_samples, k)
        """
        if self.selected_indices is None:
            raise ValueError("Selector has not been fitted. Call fit() first.")

        return X[:, self.selected_indices]

    def fit_transform(self, X: np.ndarray, y: np.ndarray, feature_names: Optional[List[str]] = None) -> np.ndarray:
        """
        Fit the selector to the data and transform it.

        Args:
            X: Feature matrix (n_samples, n_features)
            y: Target values (n_samples,)
            feature_names: Names of features (optional)

        Returns:
            Transformed feature matrix (n_samples, k)
        """
        self.fit(X, y, feature_names)
        return self.transform(X)

    def get_selected_features(self) -> List[str]:
        """
        Get the names of the selected features.

        Returns:
            List of selected feature names
        """
        if self.selected_indices is None:
            raise ValueError("Selector has not been fitted. Call fit() first.")

        return [self.feature_names[i] for i in self.selected_indices]

    def get_feature_importances(self) -> Dict[str, float]:
        """
        Get the importance scores of all features.

        Returns:
            Dictionary mapping feature names to importance scores
        """
        if self.feature_importances is None:
            raise ValueError("Selector has not been fitted. Call fit() first.")

        return {self.feature_names[i]: self.feature_importances[i] for i in range(len(self.feature_names))}

    def get_top_features(self, n: int = 10) -> Dict[str, float]:
        """
        Get the top n most important features.

        Args:
            n: Number of top features to return

        Returns:
            Dictionary mapping feature names to importance scores
        """
        if self.feature_importances is None:
            raise ValueError("Selector has not been fitted. Call fit() first.")

        top_indices = np.argsort(self.feature_importances)[-n:]
        return {self.feature_names[i]: self.feature_importances[i] for i in top_indices}


def select_features_from_dataset(dataset, target_name='growth_rate', method='mutual_info', k=10):
    """
    Select important features from a dataset.

    Args:
        dataset: Dataset object with __getitem__ method
        target_name: Name of the target variable ('growth_rate' or 'optimal_temperature')
        method: Feature selection method
        k: Number of features to select

    Returns:
        FeatureSelector object fitted to the data
    """
    # Collect all features and targets
    all_features = []
    all_targets = []
    feature_names = []
    first_item = True
    feature_sizes = {}
    sample_count = 0

    # First pass: determine consistent feature sizes
    for i in range(len(dataset)):
        try:
            features, targets = dataset[i]

            # Check each feature's size
            for key, value in features.items():
                if isinstance(value, torch.Tensor) and value.numel() > 0:
                    # Get the size of the flattened tensor
                    size = value.numel()

                    # Update feature size tracking
                    if key not in feature_sizes:
                        feature_sizes[key] = {'size': size, 'count': 1}
                    else:
                        # If sizes don't match, mark as variable
                        if feature_sizes[key]['size'] != size:
                            feature_sizes[key]['variable'] = True
                        feature_sizes[key]['count'] += 1

            sample_count += 1
        except Exception as e:
            logger.warning(f"Error processing sample {i} during feature size analysis: {e}")

    # Filter out features with variable sizes or low occurrence
    valid_features = {}
    for key, info in feature_sizes.items():
        if not info.get('variable', False) and info['count'] >= sample_count * 0.9:  # Present in at least 90% of samples
            valid_features[key] = info['size']
            # Generate feature names
            if info['size'] == 1:
                feature_names.append(key)
            else:
                feature_names.extend([f"{key}_{j}" for j in range(info['size'])])

    logger.info(f"Found {len(valid_features)} valid features with consistent sizes")

    # Second pass: extract features with consistent sizes
    for i in range(len(dataset)):
        try:
            features, targets = dataset[i]

            # Flatten and concatenate all valid feature tensors
            flat_features = []
            for key, value in features.items():
                if key in valid_features and isinstance(value, torch.Tensor) and value.numel() > 0:
                    # Convert tensor to numpy and flatten
                    flat_value = value.detach().cpu().numpy().flatten()

                    # Ensure consistent size
                    if flat_value.size == valid_features[key]:
                        flat_features.append(flat_value)

            # Concatenate all flattened features
            if flat_features and len(flat_features) == len(valid_features):
                try:
                    feature_vector = np.concatenate(flat_features)
                    all_features.append(feature_vector)

                    # Get target value
                    if target_name in targets:
                        target_value = targets[target_name].detach().cpu().numpy()
                        all_targets.append(target_value)
                except Exception as e:
                    logger.warning(f"Error concatenating features for sample {i}: {e}")
        except Exception as e:
            logger.warning(f"Error processing sample {i} during feature extraction: {e}")

    # Check if we have enough samples
    if len(all_features) < 10:
        logger.warning(f"Not enough valid samples for feature selection: {len(all_features)}")
        # Create a dummy selector with no feature selection
        selector = FeatureSelector(method=method, k=k)
        selector.feature_names = feature_names
        selector.selected_indices = np.arange(min(k, len(feature_names)))
        selector.feature_importances = np.ones(len(feature_names))
        return selector

    # Convert to numpy arrays
    try:
        X = np.array(all_features)
        y = np.array(all_targets)

        # Verify shapes
        logger.info(f"Feature matrix shape: {X.shape}, Target vector shape: {y.shape}")

        # Create and fit feature selector
        selector = FeatureSelector(method=method, k=min(k, X.shape[1]))
        selector.fit(X, y, feature_names)

        return selector
    except Exception as e:
        logger.error(f"Error during feature selection: {e}")
        # Create a dummy selector with no feature selection
        selector = FeatureSelector(method=method, k=k)
        selector.feature_names = feature_names
        selector.selected_indices = np.arange(min(k, len(feature_names)))
        selector.feature_importances = np.ones(len(feature_names))
        return selector


def apply_feature_selection(model, selector, feature_mapping=None):
    """
    Apply feature selection to a model by modifying its feature processing.

    Args:
        model: PyTorch model
        selector: Fitted FeatureSelector object
        feature_mapping: Dictionary mapping feature names to model components

    Returns:
        Modified model
    """
    # If no feature mapping is provided, try to infer it
    if feature_mapping is None:
        feature_mapping = {}
        for name in selector.get_selected_features():
            if '_' in name:
                base_name = name.split('_')[0]
                feature_mapping[name] = base_name
            else:
                feature_mapping[name] = name

    # Store selected features in the model
    model.selected_features = selector.get_selected_features()
    model.feature_importances = selector.get_feature_importances()
    model.feature_mapping = feature_mapping

    # Get unique base feature names that should be kept
    unique_base_features = set()
    for feature_name in model.selected_features:
        base_name = model.feature_mapping.get(feature_name, feature_name)
        unique_base_features.add(base_name)

    model.unique_base_features = list(unique_base_features)

    # We don't modify the forward method directly, as it's complex and has many dependencies
    # Instead, we'll add a feature filtering method that can be called before processing

    def filter_features(self, x):
        """
        Filter input features to only include selected ones.

        Args:
            x: Dictionary of input features

        Returns:
            Filtered dictionary with only selected features
        """
        # Don't filter if no feature selection is applied
        if not hasattr(self, 'unique_base_features'):
            return x

        # Handle empty dictionaries
        if not x:
            return x

        # Keep all features that are in the unique base features list
        filtered_x = {}
        for key, value in x.items():
            if key in self.unique_base_features:
                filtered_x[key] = value

        # If filtered dictionary is empty but original had values, keep at least one feature
        if not filtered_x and x:
            # Take the first feature from the original dictionary
            first_key = next(iter(x))
            filtered_x[first_key] = x[first_key]

        return filtered_x

    # Add the filter_features method to the model
    import types
    model.filter_features = types.MethodType(filter_features, model)

    # Store the original forward method
    model._original_forward = model.forward

    # Create a new forward method that applies filtering
    def new_forward(self, x):
        # Apply feature filtering
        filtered_x = self.filter_features(x)

        # Call the original forward method with filtered features
        return self._original_forward(filtered_x)

    # Replace the forward method
    model.forward = types.MethodType(new_forward, model)

    return model


def create_feature_importance_report(selector, output_file=None):
    """
    Create a report of feature importances.

    Args:
        selector: Fitted FeatureSelector object
        output_file: Path to save the report (optional)

    Returns:
        Report string
    """
    importances = selector.get_feature_importances()
    sorted_features = sorted(importances.items(), key=lambda x: x[1], reverse=True)

    report = []
    report.append("=" * 80)
    report.append("FEATURE IMPORTANCE REPORT")
    report.append("=" * 80)
    report.append("")

    report.append("TOP FEATURES:")
    report.append("-" * 80)
    for i, (feature, importance) in enumerate(sorted_features[:20], 1):
        report.append(f"{i:2d}. {feature.ljust(30)}: {importance:.6f}")
    report.append("")

    report.append("SELECTED FEATURES:")
    report.append("-" * 80)
    selected_features = selector.get_selected_features()
    for i, feature in enumerate(selected_features, 1):
        importance = importances[feature]
        report.append(f"{i:2d}. {feature.ljust(30)}: {importance:.6f}")
    report.append("")

    report_str = "\n".join(report)

    # Save to file if requested
    if output_file:
        with open(output_file, 'w') as f:
            f.write(report_str)

    return report_str
