"""
Correlation-based feature selection for DeepMu.

This module provides functions for selecting features based on their correlation
with the target variable (growth rate or optimal temperature).
"""

import numpy as np
import pandas as pd
import logging
from typing import List, Tuple, Union
import torch
from sklearn.feature_selection import SelectKBest, f_regression

# Setup logging
logger = logging.getLogger(__name__)

class CorrelationFeatureSelector:
    """
    Feature selector based on correlation with the target variable.

    This class selects features based on their correlation (<PERSON> or Spearman)
    with the target variable, allowing for separate feature sets for different targets.
    """

    def __init__(self, method: str = 'pearson', k: int = 10):
        """
        Initialize the correlation feature selector.

        Args:
            method: Correlation method ('pearson', 'spearman', or 'f_regression')
            k: Number of features to select
        """
        self.method = method
        self.k = k
        self.feature_names = None
        self.selected_indices = None
        self.feature_importances = None
        self.selected_feature_names = None

    def fit(self, X: np.ndarray, y: np.ndarray, feature_names: List[str]) -> 'CorrelationFeatureSelector':
        """
        Fit the feature selector to the data.

        Args:
            X: Feature matrix of shape (n_samples, n_features)
            y: Target vector of shape (n_samples,)
            feature_names: List of feature names

        Returns:
            Self
        """
        self.feature_names = feature_names

        # Handle edge cases
        if X.shape[1] <= self.k:
            logger.warning(f"Number of features ({X.shape[1]}) is less than or equal to k ({self.k}). Using all features.")
            self.selected_indices = np.arange(X.shape[1])
            self.feature_importances = np.ones(X.shape[1])
            self.selected_feature_names = feature_names
            return self

        # Calculate feature importances based on correlation
        if self.method == 'pearson':
            # Calculate Pearson correlation
            correlations = np.array([np.corrcoef(X[:, i], y)[0, 1] for i in range(X.shape[1])])
            # Use absolute correlation as importance
            self.feature_importances = np.abs(correlations)
        elif self.method == 'spearman':
            # Calculate Spearman correlation
            correlations = []
            for i in range(X.shape[1]):
                # Convert to ranks
                x_ranks = pd.Series(X[:, i]).rank()
                y_ranks = pd.Series(y).rank()
                # Calculate correlation
                corr = np.corrcoef(x_ranks, y_ranks)[0, 1]
                correlations.append(corr)
            correlations = np.array(correlations)
            # Use absolute correlation as importance
            self.feature_importances = np.abs(correlations)
        elif self.method == 'f_regression':
            # Use f_regression from scikit-learn
            selector = SelectKBest(f_regression, k=self.k)
            selector.fit(X, y)
            self.feature_importances = selector.scores_
        else:
            raise ValueError(f"Unknown method: {self.method}")

        # Select top k features
        self.selected_indices = np.argsort(self.feature_importances)[-self.k:]
        self.selected_feature_names = [feature_names[i] for i in self.selected_indices]

        logger.info(f"Selected {len(self.selected_indices)} features using {self.method} correlation")

        return self

    def transform(self, X: np.ndarray) -> np.ndarray:
        """
        Transform the data by selecting only the important features.

        Args:
            X: Feature matrix of shape (n_samples, n_features)

        Returns:
            Transformed feature matrix of shape (n_samples, k)
        """
        if self.selected_indices is None:
            raise ValueError("Selector has not been fitted yet")

        return X[:, self.selected_indices]

    def fit_transform(self, X: np.ndarray, y: np.ndarray, feature_names: List[str]) -> np.ndarray:
        """
        Fit the selector and transform the data.

        Args:
            X: Feature matrix of shape (n_samples, n_features)
            y: Target vector of shape (n_samples,)
            feature_names: List of feature names

        Returns:
            Transformed feature matrix of shape (n_samples, k)
        """
        self.fit(X, y, feature_names)
        return self.transform(X)

    def get_feature_names(self) -> List[str]:
        """
        Get the names of the selected features.

        Returns:
            List of selected feature names
        """
        if self.selected_feature_names is None:
            raise ValueError("Selector has not been fitted yet")

        return self.selected_feature_names

    def get_support(self, indices: bool = False) -> Union[np.ndarray, List[int]]:
        """
        Get a mask, or integer index, of the features selected.

        Args:
            indices: If True, returns integer indices, otherwise boolean mask

        Returns:
            Boolean mask or integer indices of selected features
        """
        if self.selected_indices is None:
            raise ValueError("Selector has not been fitted yet")

        if indices:
            return self.selected_indices
        else:
            mask = np.zeros(len(self.feature_names), dtype=bool)
            mask[self.selected_indices] = True
            return mask


def select_features_for_targets(
    dataset,
    growth_rate_k: int = 10,
    optimal_temperature_k: int = 10,
    method: str = 'pearson',
    max_samples: int = 1000  # Limit the number of samples to process for efficiency
) -> Tuple[CorrelationFeatureSelector, CorrelationFeatureSelector]:
    """
    Select separate features for growth rate and optimal temperature prediction.

    Args:
        dataset: Dataset object with __getitem__ method
        growth_rate_k: Number of features to select for growth rate prediction
        optimal_temperature_k: Number of features to select for optimal temperature prediction
        method: Correlation method ('pearson', 'spearman', or 'f_regression')
        max_samples: Maximum number of samples to process for efficiency

    Returns:
        Tuple of (growth_rate_selector, optimal_temperature_selector)
    """
    # Get feature names from the dataset if available
    if hasattr(dataset, 'get_feature_names'):
        feature_names = dataset.get_feature_names()
        logger.info(f"Using {len(feature_names)} feature names from dataset")
    else:
        feature_names = None

    # Collect all features and targets
    all_features = []
    growth_rates = []
    optimal_temperatures = []

    # Limit the number of samples to process
    num_samples = min(len(dataset), max_samples)
    logger.info(f"Processing {num_samples} samples for feature selection")

    # Process samples in batches for efficiency
    batch_size = 10
    for i in range(0, num_samples, batch_size):
        batch_end = min(i + batch_size, num_samples)
        logger.info(f"Processing samples {i} to {batch_end-1}")

        for j in range(i, batch_end):
            try:
                features, targets = dataset[j]

                # Extract feature values
                if feature_names is None:
                    # Need to extract feature names from the first sample
                    if len(all_features) == 0:
                        feature_names = list(features.keys())
                        logger.info(f"Extracted {len(feature_names)} feature names from first sample")

                # Create feature vector
                feature_vector = []
                for key in feature_names:
                    if key in features:
                        value = features[key]
                        if isinstance(value, torch.Tensor):
                            # Convert tensor to numpy
                            value = value.detach().cpu().numpy()
                            if value.size > 1:
                                # If tensor has multiple elements, flatten and append each
                                for k in range(value.size):
                                    feature_vector.append(value.flatten()[k])
                            else:
                                # Single value tensor
                                feature_vector.append(value.item())
                        elif isinstance(value, (int, float, np.number)):
                            # Numeric value
                            feature_vector.append(float(value))
                        elif isinstance(value, np.ndarray) and value.dtype.kind in ['U', 'S']:
                            # Skip string arrays
                            feature_vector.append(0.0)
                        else:
                            # Try to convert to float, if not possible, use 0.0
                            try:
                                feature_vector.append(float(value))
                            except (ValueError, TypeError):
                                feature_vector.append(0.0)
                    else:
                        # Missing feature
                        feature_vector.append(0.0)

                # Add feature vector to list
                all_features.append(np.array(feature_vector))

                # Get target values
                if 'growth_rate' in targets:
                    growth_rate = targets['growth_rate'].detach().cpu().numpy().item()
                    growth_rates.append(growth_rate)

                if 'optimal_temperature' in targets:
                    optimal_temperature = targets['optimal_temperature'].detach().cpu().numpy().item()
                    optimal_temperatures.append(optimal_temperature)
            except Exception as e:
                logger.warning(f"Error processing sample {j}: {e}")

    # Convert to numpy arrays
    if len(all_features) == 0:
        logger.error("No valid features found. Check feature files for errors.")
        return None, None

    X = np.array(all_features)
    growth_rate_y = np.array(growth_rates)
    optimal_temperature_y = np.array(optimal_temperatures)

    logger.info(f"Collected {X.shape[0]} samples with {X.shape[1]} features")
    logger.info(f"Found {len(growth_rates)} growth rate targets and {len(optimal_temperatures)} optimal temperature targets")

    # Create and fit selectors
    growth_rate_selector = None
    optimal_temperature_selector = None

    if len(growth_rates) > 0:
        # Filter samples with growth rate targets
        growth_rate_indices = np.arange(len(all_features))[np.arange(len(all_features)) < len(growth_rates)]
        growth_rate_X = X[growth_rate_indices]

        # Create and fit growth rate selector
        growth_rate_selector = CorrelationFeatureSelector(method=method, k=growth_rate_k)
        growth_rate_selector.fit(growth_rate_X, growth_rate_y, feature_names)

        logger.info(f"Selected {len(growth_rate_selector.selected_indices)} features for growth rate prediction")
        logger.info(f"Top 5 features for growth rate: {growth_rate_selector.selected_feature_names[-5:]}")

    if len(optimal_temperatures) > 0:
        # Filter samples with optimal temperature targets
        optimal_temperature_indices = np.arange(len(all_features))[np.arange(len(all_features)) < len(optimal_temperatures)]
        optimal_temperature_X = X[optimal_temperature_indices]

        # Create and fit optimal temperature selector
        optimal_temperature_selector = CorrelationFeatureSelector(method=method, k=optimal_temperature_k)
        optimal_temperature_selector.fit(optimal_temperature_X, optimal_temperature_y, feature_names)

        logger.info(f"Selected {len(optimal_temperature_selector.selected_indices)} features for optimal temperature prediction")
        logger.info(f"Top 5 features for optimal temperature: {optimal_temperature_selector.selected_feature_names[-5:]}")

    return growth_rate_selector, optimal_temperature_selector
