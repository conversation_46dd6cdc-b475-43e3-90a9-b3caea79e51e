"""Preprocessing module for DeepMu.

This module provides functions for preprocessing genomic data, including
gene prediction with Prodigal and KO annotation with KofamScan.

Typical usage:
    from deepmu.utils.preprocess import preprocess_genome

    # Preprocess a single genome
    cds_file, ko_file = preprocess_genome(
        genome_file="genome.fna",
        output_dir="preprocessed",
        kofamscan_db_path="/path/to/kofamscan_db"
    )
"""

import os
import logging
import concurrent.futures
import pandas as pd
from pathlib import Path
from typing import Dict, Tuple, Optional

from .gene_annotation import GenomePreprocessor, PreprocessingError
from .trna_annotation import tRNAscanSEWrapper
from .rrna_annotation import Barrnap<PERSON>rapper

def preprocess_genome(
    genome_file: str,
    output_dir: str,
    kofamscan_db_path: Optional[str] = None,
    genetic_code: int = 11,
    meta_mode: bool = False,
    cpu: Optional[int] = None,
    identify_trnas: bool = False,
    trnascan_path: Optional[str] = None,
    identify_rrnas: bool = False,
    barrnap_path: Optional[str] = None,
    kingdom: str = 'bac',
    skip_existing: bool = False,
    metadata_file: Optional[str] = None
) -> Tuple[str, str, Optional[str], Optional[str]]:
    """
    Preprocess a genome by predicting genes, annotating with KO terms, and optionally identifying tRNAs and rRNAs.

    Args:
        genome_file: Path to the genome FASTA file
        output_dir: Directory to store output files
        kofamscan_db_path: Path to KofamScan database
        genetic_code: NCBI genetic code ID (default: 11 for bacterial)
        meta_mode: Whether to use metagenome mode for gene prediction
        cpu: Number of CPU cores to use for processing
        identify_trnas: Whether to identify tRNAs using tRNAscan-SE
        trnascan_path: Path to tRNAscan-SE executable (default: uses system PATH)
        identify_rrnas: Whether to identify rRNAs using Barrnap
        barrnap_path: Path to Barrnap executable (default: uses system PATH)
        kingdom: Kingdom for rRNA prediction (bac/arc/euk/mito, default: bac)
        skip_existing: Whether to skip processing if output files already exist (default: False)
        metadata_file: Path to metadata file containing kingdom information (optional)

    Returns:
        Tuple containing:
            - Path to the CDS sequences file (FASTA)
            - Path to the KO annotation file (TSV)
            - Path to the tRNA annotation file (if identify_trnas=True, otherwise None)
            - Path to the rRNA annotation file (if identify_rrnas=True, otherwise None)

    Raises:
        PreprocessingError: If preprocessing fails
    """
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)

    # Initialize preprocessor
    try:
        preprocessor = GenomePreprocessor(
            kofamscan_db_path=kofamscan_db_path,
            genetic_code=genetic_code,
            cpu=cpu
        )
    except PreprocessingError as e:
        raise PreprocessingError(f"Failed to initialize preprocessor: {e}")

    # Set output paths
    base_name = os.path.splitext(os.path.basename(genome_file))[0]
    cds_file = os.path.join(output_dir, f"{base_name}_cds.ffn")
    protein_file = os.path.join(output_dir, f"{base_name}_cds.faa")
    ko_file = os.path.join(output_dir, f"{base_name}_ko.tsv")
    trna_file = os.path.join(output_dir, f"{base_name}_tRNA.tsv") if identify_trnas else None
    rrna_file = os.path.join(output_dir, f"{base_name}_rrna.gff") if identify_rrnas else None

    # Get kingdom from metadata file if provided
    if metadata_file and os.path.exists(metadata_file):
        try:
            metadata_df = pd.read_csv(metadata_file, sep='\t')
            # Extract genome_id from the filename
            genome_id = base_name

            # Check if the genome_id exists in the metadata and has a kingdom column
            if 'genome_id' in metadata_df.columns and 'kingdom' in metadata_df.columns:
                genome_row = metadata_df[metadata_df['genome_id'] == genome_id]
                if not genome_row.empty and not pd.isna(genome_row['kingdom'].iloc[0]):
                    # Update kingdom from metadata
                    kingdom_from_metadata = genome_row['kingdom'].iloc[0].lower()
                    if kingdom_from_metadata in ['bac', 'arc', 'euk', 'mito']:
                        logging.info(f"Using kingdom '{kingdom_from_metadata}' from metadata for {genome_id}")
                        kingdom = kingdom_from_metadata
                    else:
                        logging.warning(f"Invalid kingdom '{kingdom_from_metadata}' in metadata for {genome_id}. Using default: {kingdom}")
        except Exception as e:
            logging.warning(f"Error reading kingdom from metadata: {e}. Using default: {kingdom}")

    # Check for existing output files and remove them if not skipping
    if not skip_existing:
        for file_path in [cds_file, protein_file, ko_file]:
            if os.path.exists(file_path):
                logging.info(f"Removing existing file: {file_path}")
                os.remove(file_path)

        # Also check for tRNA and rRNA files
        if identify_trnas:
            trna_tsv = os.path.join(output_dir, f"{base_name}_tRNA.tsv")
            trna_fasta = os.path.join(output_dir, f"{base_name}_tRNA.fasta")
            for file_path in [trna_tsv, trna_fasta]:
                if os.path.exists(file_path):
                    logging.info(f"Removing existing file: {file_path}")
                    os.remove(file_path)

        if identify_rrnas:
            rrna_gff = os.path.join(output_dir, f"{base_name}_rrna.gff")
            rrna_fasta = os.path.join(output_dir, f"{base_name}_rrna.fasta")
            for file_path in [rrna_gff, rrna_fasta]:
                if os.path.exists(file_path):
                    logging.info(f"Removing existing file: {file_path}")
                    os.remove(file_path)

    try:
        # Define functions for parallel execution
        def run_gene_prediction():
            logging.info(f"Predicting genes in {genome_file} using Prodigal")
            # Log the command that will be run
            prodigal_cmd = f"prodigal -i {genome_file} -a {protein_file} -d {cds_file} -g {genetic_code} {'-p meta' if meta_mode else ''}"
            logging.info(f"Prodigal command: {prodigal_cmd}")
            return preprocessor.predict_genes(
                genome_file=genome_file,
                protein_file=protein_file,
                cds_file=cds_file,
                meta_mode=meta_mode
            )

        def run_trna_identification():
            if not identify_trnas:
                return None

            try:
                logging.info(f"Identifying tRNAs in {genome_file} using tRNAscan-SE")
                scanner = tRNAscanSEWrapper(trnascan_path or "tRNAscan-SE")
                result = scanner.predict_tRNAs(
                    input_fasta=genome_file,
                    output_prefix=base_name,
                    kingdom=kingdom,  # Use the same kingdom parameter for both tRNA and rRNA
                    genetic_code=genetic_code,
                    output_dir=output_dir,
                    overwrite=not skip_existing  # Overwrite if not skipping existing files
                )

                if result["success"]:
                    logging.info(f"Found {len(result['predicted_tRNAs'])} tRNAs in {genome_file}")
                    return next((f for f in result["output_files"] if f.endswith("_tRNA.tsv")), None)
                else:
                    logging.warning(f"tRNAscan-SE failed: {result.get('error', 'Unknown error')}")
                    return None
            except Exception as e:
                logging.warning(f"Error identifying tRNAs: {e}")
                return None

        def run_rrna_identification():
            if not identify_rrnas:
                return None

            try:
                logging.info(f"Identifying rRNAs in {genome_file} using Barrnap")
                scanner = BarrnapWrapper(barrnap_path or "barrnap")
                result = scanner.predict_rrna(
                    input_fasta=genome_file,
                    output_gff=rrna_file,
                    output_fasta=os.path.join(output_dir, f"{base_name}_rrna.fasta"),
                    kingdom=kingdom,
                    threads=cpu or 1,
                    overwrite=not skip_existing  # Overwrite if not skipping existing files
                )

                if result["success"]:
                    rrna_counts = scanner.get_rrna_summary(result["features"])
                    logging.info(f"Found rRNAs in {genome_file}: {rrna_counts}")
                    return rrna_file
                else:
                    logging.warning(f"Barrnap failed: {result.get('error', 'Unknown error')}")
                    return None
            except Exception as e:
                logging.warning(f"Error identifying rRNAs: {e}")
                return None

        # Run gene prediction, tRNA identification, and rRNA identification in parallel
        with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
            gene_prediction_future = executor.submit(run_gene_prediction)
            trna_future = executor.submit(run_trna_identification)
            rrna_future = executor.submit(run_rrna_identification)

            # Wait for gene prediction to complete before running KO annotation
            protein_file, cds_file = gene_prediction_future.result()
            trna_file = trna_future.result()
            rrna_file = rrna_future.result()

        # Annotate with KO terms (after gene prediction is complete)
        ko_dict = preprocessor.annotate_ko(
            protein_file=protein_file,
            output_file=ko_file
        )

        return cds_file, ko_file, trna_file, rrna_file

    except Exception as e:
        logging.error(f"Error in preprocess_genome: {e}")
        raise PreprocessingError(f"Preprocessing failed: {e}")

def preprocess_batch(
    input_dir: str,
    output_dir: str,
    kofamscan_db_path: Optional[str] = None,
    genetic_code: int = 11,
    meta_mode: bool = False,
    cpu: Optional[int] = None,
    identify_trnas: bool = False,
    trnascan_path: Optional[str] = None,
    identify_rrnas: bool = False,
    barrnap_path: Optional[str] = None,
    kingdom: str = 'bac',
    skip_existing: bool = False,
    metadata_file: Optional[str] = None
) -> Dict[str, Tuple[str, str, Optional[str], Optional[str]]]:
    """
    Preprocess multiple genomes in a directory.

    Args:
        input_dir: Directory containing genome FASTA files
        output_dir: Directory to store output files
        kofamscan_db_path: Path to KofamScan database
        genetic_code: NCBI genetic code ID (default: 11 for bacterial)
        meta_mode: Whether to use metagenome mode for gene prediction
        cpu: Number of CPU cores to use for processing
        identify_trnas: Whether to identify tRNAs using tRNAscan-SE
        trnascan_path: Path to tRNAscan-SE executable (default: uses system PATH)
        identify_rrnas: Whether to identify rRNAs using Barrnap
        barrnap_path: Path to Barrnap executable (default: uses system PATH)
        kingdom: Kingdom for rRNA prediction (bac/arc/euk/mito, default: bac)
        skip_existing: Whether to skip processing if output files already exist (default: False)
        metadata_file: Path to metadata file containing kingdom information (optional)

    Returns:
        Dictionary mapping genome IDs to tuples of (cds_file, ko_file, trna_file, rrna_file) paths
    """
    results = {}

    # Create output directory
    os.makedirs(output_dir, exist_ok=True)

    # Process each genome file
    for filename in os.listdir(input_dir):
        if filename.endswith(('.fna', '.fa', '.fasta')):
            genome_id = os.path.splitext(filename)[0]
            genome_file = os.path.join(input_dir, filename)
            genome_output_dir = os.path.join(output_dir, genome_id)

            try:
                cds_file, ko_file, trna_file, rrna_file = preprocess_genome(
                    genome_file=genome_file,
                    output_dir=genome_output_dir,
                    kofamscan_db_path=kofamscan_db_path,
                    genetic_code=genetic_code,
                    meta_mode=meta_mode,
                    cpu=cpu,
                    identify_trnas=identify_trnas,
                    trnascan_path=trnascan_path,
                    identify_rrnas=identify_rrnas,
                    barrnap_path=barrnap_path,
                    kingdom=kingdom,
                    skip_existing=skip_existing,
                    metadata_file=metadata_file
                )
                results[genome_id] = (cds_file, ko_file, trna_file, rrna_file)
            except PreprocessingError as e:
                print(f"Warning: Failed to process {filename}: {e}")
                continue

    return results