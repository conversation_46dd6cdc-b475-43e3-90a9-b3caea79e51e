"""
DeepMu 日志工具。

此模块为 DeepMu 提供日志工具，包括获取日志记录器的函数
和记录函数调用的装饰器。
"""

import logging
import functools
import inspect
from typing import Callable, Any, Dict, Optional

def get_logger(name: str = "DeepMu") -> logging.Logger:
    """获取指定名称的日志记录器。

    参数:
        name: 日志记录器名称 (默认: "DeepMu")

    返回:
        日志记录器实例
    """
    return logging.getLogger(name)

def log_function_call(func: Callable) -> Callable:
    """记录函数调用的装饰器。

    参数:
        func: 要装饰的函数

    返回:
        装饰后的函数
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        logger = get_logger()

        # 获取函数签名
        sig = inspect.signature(func)

        # 获取函数名称
        func_name = func.__name__

        # 记录函数调用
        logger.debug(f"调用 {func_name}")

        # 调用函数
        result = func(*args, **kwargs)

        # 记录函数返回
        logger.debug(f"从 {func_name} 返回")

        return result

    return wrapper
