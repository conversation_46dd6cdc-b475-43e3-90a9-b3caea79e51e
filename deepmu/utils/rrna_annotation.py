"""rRNA Annotation Module for DeepMu.

This module provides functionality for identifying rRNA genes in genomic sequences
using Barrnap with proper handling of different kingdoms and genetic codes.
"""

import subprocess
import os
import logging
from pathlib import Path
from typing import List, Dict, Optional

class BarrnapWrapper:
    """Wrapper for Barrnap rRNA prediction tool with structured input/output handling."""

    def __init__(self, barrnap_path: str = "barrnap"):
        """
        Initialize wrapper with path to barrnap executable.

        Args:
            barrnap_path: Path to barrnap executable
        """
        self.executable = barrnap_path
        self.valid_kingdoms = ['bac', 'arc', 'euk', 'mito']

    def predict_rrna(
        self,
        input_fasta: str,
        output_gff: Optional[str] = None,
        output_fasta: Optional[str] = None,
        kingdom: str = 'bac',
        threads: int = 1,
        evalue: float = 1e-6,
        lencutoff: float = 0.8,
        reject: float = 0.5,
        quiet: bool = True,
        incseq: bool = False,
        tmp_dir: str = ".",
        overwrite: bool = True
    ) -> Dict:
        """
        Run Barrnap rRNA prediction with specified parameters.

        Args:
            input_fasta: Path to input FASTA file
            output_gff: Path to save GFF output (default: stdout)
            output_fasta: Path to save rRNA sequences in FASTA format
            kingdom: Target kingdom (bac/arc/euk/mito)
            threads: Number of CPU threads to use
            evalue: E-value cutoff for HMMER searches
            lencutoff: Length cutoff for partial hits (0-1)
            reject: Reject hits below this length proportion (0-1)
            quiet: Suppress progress messages
            incseq: Include full sequence in GFF output
            tmp_dir: Temporary directory for intermediate files
            overwrite: Whether to overwrite existing output files (default: True)

        Returns:
            Dictionary with:
            - 'success': Boolean indicating success
            - 'features': List of parsed rRNA features
            - 'output_files': Generated output files
        """
        # Validate inputs
        if kingdom not in self.valid_kingdoms:
            raise ValueError(f"Invalid kingdom: {kingdom}. Choose from {self.valid_kingdoms}")

        if not os.path.exists(input_fasta):
            raise FileNotFoundError(f"Input FASTA file not found: {input_fasta}")

        # Build command
        cmd = [self.executable]

        # Add options
        if quiet:
            cmd.append("--quiet")

        if output_fasta:
            cmd.extend(["--outseq", output_fasta])

        cmd.extend([
            "--kingdom", kingdom,
            "--threads", str(threads),
            "--evalue", str(evalue),
            "--lencutoff", str(lencutoff),
            "--reject", str(reject)
        ])

        if incseq:
            cmd.append("--incseq")

        # Ensure output directory exists
        tmp_dir = os.path.abspath(tmp_dir)
        os.makedirs(tmp_dir, exist_ok=True)

        # Check if output files already exist
        if (output_gff and os.path.exists(output_gff)) or (output_fasta and os.path.exists(output_fasta)):
            if not overwrite:
                # Skip running Barrnap if files exist and overwrite is False
                logging.info(f"Output files already exist and overwrite=False. Skipping Barrnap.")
                result = {"success": True, "features": [], "output_files": [], "error": None}

                # Check which files exist and add them to output_files
                if output_gff and os.path.exists(output_gff):
                    result["output_files"].append(output_gff)
                    # Parse existing GFF file
                    result["features"] = self._parse_gff_from_file(output_gff)

                if output_fasta and os.path.exists(output_fasta):
                    result["output_files"].append(output_fasta)

                return result
            else:
                # Remove existing files if overwrite is True
                logging.info(f"Removing existing output files for overwrite.")
                if output_gff and os.path.exists(output_gff):
                    os.remove(output_gff)
                if output_fasta and os.path.exists(output_fasta):
                    os.remove(output_fasta)

        # Convert input_fasta to absolute path
        abs_input_fasta = os.path.abspath(input_fasta)
        cmd.append(abs_input_fasta)

        # Run command
        result = {
            "success": False,
            "features": [],
            "output_files": [],
            "error": None
        }

        try:
            # Handle output redirection
            stdout = subprocess.PIPE
            output_file_handle = None
            if output_gff:
                output_file_handle = open(output_gff, 'w')
                stdout = output_file_handle
                result["output_files"].append(output_gff)

            cmd_str = ' '.join(cmd)
            logging.info(f"Running Barrnap command: {cmd_str}")
            logging.info(f"Working directory: {tmp_dir}")

            process = subprocess.run(
                cmd,
                stdout=stdout,
                stderr=subprocess.PIPE,
                check=True,
                text=True,
                cwd=tmp_dir
            )

            # Log the output for debugging
            if process.stderr:
                logging.debug(f"Barrnap stderr: {process.stderr}")

            # Parse GFF output if not saved to file
            if not output_gff:
                result["features"] = self._parse_gff(process.stdout.split('\n'))
            else:
                if output_file_handle:
                    output_file_handle.close()
                if os.path.exists(output_gff):
                    result["features"] = self._parse_gff_from_file(output_gff)
                else:
                    logging.warning(f"Output GFF file not found: {output_gff}")
                    result["error"] = f"Output GFF file not found: {output_gff}"
                    return result

            if output_fasta:
                if os.path.exists(output_fasta):
                    result["output_files"].append(output_fasta)
                else:
                    logging.warning(f"Output FASTA file not found: {output_fasta}")

            result["success"] = True

        except subprocess.CalledProcessError as e:
            error_msg = f"Barrnap failed (exit {e.returncode})"
            if e.stderr:
                error_msg += f"\nStderr: {e.stderr}"
            logging.error(error_msg)
            result["error"] = error_msg
        except FileNotFoundError:
            error_msg = "Barrnap executable not found. Is it installed and in PATH?"
            logging.error(error_msg)
            result["error"] = error_msg
        except Exception as e:
            error_msg = f"Runtime error: {str(e)}"
            logging.error(error_msg)
            result["error"] = error_msg
        finally:
            # Close any open file handles
            if 'output_file_handle' in locals() and output_file_handle and not output_file_handle.closed:
                output_file_handle.close()

        return result

    def _parse_gff_from_file(self, gff_path: str) -> List[Dict]:
        """Parse GFF output file into structured data."""
        with open(gff_path) as f:
            return self._parse_gff(f.readlines())

    def _parse_gff(self, lines: List[str]) -> List[Dict]:
        """Parse GFF lines into structured features."""
        features = []

        for line in lines:
            line = line.strip()
            if not line or line.startswith('#'):
                continue

            parts = line.split('\t')
            if len(parts) < 9:
                continue

            attributes = self._parse_attributes(parts[8])
            feature = {
                "seqid": parts[0],
                "source": parts[1],
                "type": parts[2],
                "start": int(parts[3]),
                "end": int(parts[4]),
                "score": float(parts[5]) if parts[5] != '.' else None,
                "strand": parts[6],
                "phase": parts[7],
                "name": attributes.get("Name"),
                "product": attributes.get("product"),
                "full_attributes": attributes
            }
            features.append(feature)

        return features

    def _parse_attributes(self, attr_str: str) -> Dict:
        """Parse GFF attribute string into dictionary."""
        attributes = {}
        for pair in attr_str.split(';'):
            if '=' in pair:
                key, value = pair.split('=', 1)
                attributes[key.strip()] = value.strip()
        return attributes

    def get_rrna_summary(self, features: List[Dict]) -> Dict[str, int]:
        """
        Get a summary of rRNA types found in the features.

        Args:
            features: List of rRNA features from predict_rrna

        Returns:
            Dictionary with counts of each rRNA type
        """
        counts = {
            '16S': 0,
            '23S': 0,
            '5S': 0,
            '12S': 0,
            'other': 0
        }

        for feat in features:
            product = feat.get("product", "").lower()
            if '16s' in product:
                counts['16S'] += 1
            elif '23s' in product:
                counts['23S'] += 1
            elif '5s' in product:
                counts['5S'] += 1
            elif '12s' in product:
                counts['12S'] += 1
            else:
                counts['other'] += 1

        return counts
