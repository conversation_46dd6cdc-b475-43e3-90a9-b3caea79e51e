"""
DeepMu 异常类。

此模块定义了 DeepMu 的自定义异常。
"""

class DeepMuError(Exception):
    """所有 DeepMu 异常的基类。"""
    pass

class ModelLoadError(DeepMuError):
    """模型无法加载时抛出的异常。"""
    pass

class InputValidationError(DeepMuError):
    """输入验证失败时抛出的异常。"""
    pass

class InvalidTemperatureError(InputValidationError):
    """提供无效温度时抛出的异常。"""
    pass

class FeatureCalculationError(DeepMuError):
    """特征计算失败时抛出的异常。"""
    pass

class SequenceProcessingError(DeepMuError):
    """序列处理失败时抛出的异常。"""
    pass

class PredictionError(DeepMuError):
    """预测失败时抛出的异常。"""
    pass

class CommunityProcessingError(DeepMuError):
    """群落处理失败时抛出的异常。"""
    pass

class InvalidSequenceError(DeepMuError):
    """提供无效序列时抛出的异常。"""
    pass

class PreprocessingError(DeepMuError):
    """预处理失败时抛出的异常。"""
    pass
