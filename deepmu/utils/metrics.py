"""
Metrics utilities for model evaluation.
"""

import numpy as np
import torch
from sklearn.metrics import r2_score, mean_absolute_error, mean_squared_error, accuracy_score
from typing import Dict, Union, List, Tuple

def calculate_regression_accuracy(y_true_np, y_pred_np, tolerance=0.1):
    """
    Calculate accuracy for regression tasks by considering predictions within
    a certain tolerance of the true value as correct.

    Args:
        y_true_np: Ground truth values (numpy array)
        y_pred_np: Predicted values (numpy array)
        tolerance: Relative tolerance for considering a prediction correct
                  (default: 0.1 = 10% of the true value)

    Returns:
        Accuracy score (float)
    """
    # Ensure numpy arrays and flatten to 1D
    y_true_np = np.array(y_true_np).flatten()
    y_pred_np = np.array(y_pred_np).flatten()

    # Calculate relative errors
    mask = np.abs(y_true_np) > 1e-8  # Avoid division by zero
    if np.sum(mask) == 0:
        return 0.0

    # For non-zero true values, use relative error
    rel_errors = np.zeros_like(y_true_np, dtype=bool)
    rel_errors[mask] = (np.abs(y_true_np[mask] - y_pred_np[mask]) / np.abs(y_true_np[mask])) <= tolerance

    # For zero true values, use absolute error with a small threshold
    abs_threshold = np.mean(np.abs(y_true_np[mask])) * tolerance if np.sum(mask) > 0 else 0.01
    rel_errors[~mask] = np.abs(y_pred_np[~mask]) <= abs_threshold

    # Calculate accuracy
    accuracy = np.mean(rel_errors)
    return float(accuracy)

def calculate_metrics(
    y_true: torch.Tensor,
    y_pred: torch.Tensor,
    prefix: str = "",
    accuracy_tolerance: float = 0.1
) -> Dict[str, float]:
    """
    Calculate various regression metrics.

    Args:
        y_true: Ground truth values
        y_pred: Predicted values
        prefix: Optional prefix for metric names (e.g., 'growth_rate_', 'temperature_')
        accuracy_tolerance: Tolerance for accuracy calculation (default: 0.1 = 10%)

    Returns:
        Dictionary of metrics
    """
    # Convert to numpy for sklearn metrics
    y_true_np = y_true.detach().cpu().numpy()
    y_pred_np = y_pred.detach().cpu().numpy()

    # Calculate metrics
    metrics = {}

    # Mean Squared Error (already used as loss)
    mse = mean_squared_error(y_true_np, y_pred_np)
    metrics[f"{prefix}mse"] = mse

    # Root Mean Squared Error
    rmse = np.sqrt(mse)
    metrics[f"{prefix}rmse"] = rmse

    # Mean Absolute Error
    mae = mean_absolute_error(y_true_np, y_pred_np)
    metrics[f"{prefix}mae"] = mae

    # R-squared
    r2 = r2_score(y_true_np, y_pred_np)
    metrics[f"{prefix}r2"] = r2

    # Accuracy (within tolerance)
    accuracy = calculate_regression_accuracy(y_true_np, y_pred_np, tolerance=accuracy_tolerance)
    metrics[f"{prefix}accuracy"] = accuracy

    # Pearson correlation coefficient
    if len(y_true_np) > 1:  # Need at least 2 samples for correlation
        correlation = np.corrcoef(y_true_np, y_pred_np)[0, 1]
        metrics[f"{prefix}correlation"] = correlation
    else:
        metrics[f"{prefix}correlation"] = 0.0

    # Mean Absolute Percentage Error (MAPE) with capping to avoid extreme values
    # Avoid division by zero and cap extreme values
    mask = np.abs(y_true_np) > 1e-8  # Use a small epsilon instead of exact zero
    if np.sum(mask) > 0:
        # Calculate percentage errors
        percentage_errors = np.abs((y_true_np[mask] - y_pred_np[mask]) / np.abs(y_true_np[mask]))

        # Cap extreme values to 5.0 (500%)
        percentage_errors = np.minimum(percentage_errors, 5.0)

        # Calculate MAPE
        mape = np.mean(percentage_errors) * 100
        metrics[f"{prefix}mape"] = mape
    else:
        metrics[f"{prefix}mape"] = 0.0

    # Symmetric Mean Absolute Percentage Error (SMAPE) - more robust to outliers
    # and small values
    denominator = np.abs(y_true_np) + np.abs(y_pred_np)
    mask = denominator > 1e-8  # Avoid division by very small numbers
    if np.sum(mask) > 0:
        smape = np.mean(2.0 * np.abs(y_pred_np[mask] - y_true_np[mask]) / denominator[mask]) * 100
        metrics[f"{prefix}smape"] = smape
    else:
        metrics[f"{prefix}smape"] = 0.0

    return metrics

def calculate_multi_target_metrics(
    targets: Dict[str, torch.Tensor],
    predictions: Dict[str, torch.Tensor]
) -> Dict[str, float]:
    """
    Calculate metrics for multiple targets.

    Args:
        targets: Dictionary of target tensors
        predictions: Dictionary of prediction tensors

    Returns:
        Dictionary of metrics for all targets
    """
    all_metrics = {}

    # Calculate metrics for each target
    for target_name, target_values in targets.items():
        if target_name in predictions:
            target_metrics = calculate_metrics(
                target_values,
                predictions[target_name],
                prefix=f"{target_name}_"
            )
            all_metrics.update(target_metrics)

    return all_metrics

def format_metrics(metrics: Dict[str, float]) -> str:
    """
    Format metrics dictionary as a string for logging.

    Args:
        metrics: Dictionary of metric values

    Returns:
        Formatted string
    """
    return ", ".join([f"{k}: {v:.4f}" for k, v in metrics.items()])
