"""
DeepMu 的分类学工具。

本模块为 DeepMu 提供处理分类学信息的工具函数。
"""

import os
import logging
from typing import Dict, List, Optional, Set, Tuple, Union

logger = logging.getLogger("DeepMu")

class TaxonomyUtils:
    """用于处理分类学信息的工具类。"""
    
    def __init__(self):
        """初始化分类学工具。"""
        self.taxonomy_levels = ['phylum', 'class', 'order', 'family', 'genus']
        
        # 尝试导入 ETE3 以进行分类学查询
        try:
            from ete3 import NCBITaxa
            self.ncbi = NCBITaxa()
            self.has_ete3 = True
            logger.debug("已初始化 ETE3 的 NCBITaxa")
        except ImportError:
            self.has_ete3 = False
            logger.warning("未安装 ETE3 包。部分分类学功能将受限。")
        except Exception as e:
            self.has_ete3 = False
            logger.warning(f"初始化 NCBI 分类学数据库失败: {e}。"
                          f"请先运行 prepare_ncbi_taxonomy.py 下载数据库。")
    
    def get_taxonomy_string_from_taxid(self, taxid: Union[str, int]) -> Optional[str]:
        """根据分类学 ID 获取分类学字符串。
        
        参数:
            taxid: NCBI 分类学 ID
            
        返回:
            管道符分隔的分类学字符串（如 "2|1224|1236|91347|543|570"）
            若未找到该分类学 ID，则返回 None
        """
        if not self.has_ete3:
            logger.warning("未安装 ETE3 包，无法根据 taxid 获取分类学字符串。")
            return None
        
        try:
            # 若 taxid 为字符串则转为 int
            if isinstance(taxid, str):
                taxid = int(taxid)
            
            # 获取进化谱系
            lineage = self.ncbi.get_lineage(taxid)
            
            # 转换为字符串
            return '|'.join(str(tid) for tid in lineage)
        except Exception as e:
            logger.warning(f"获取 taxid {taxid} 的分类学字符串失败: {e}")
            return None
    
    def parse_taxonomy_string(self, taxonomy_string: str) -> Dict[str, str]:
        """解析分类学字符串为字典。
        
        参数:
            taxonomy_string: 管道符分隔的分类学字符串（如 "2|1224|1236|91347|543|570"）
            
        返回:
            分类学层级到分类学 ID 的映射字典
        """
        if not self.has_ete3:
            logger.warning("未安装 ETE3 包，无法解析分类学字符串。")
            return {}
        
        try:
            # 拆分分类学字符串
            taxids = taxonomy_string.strip().split('|')
            
            # 获取每个 taxid 的 rank
            ranks = {}
            for taxid in taxids:
                try:
                    rank = self.ncbi.get_rank([int(taxid)])
                    if rank:
                        ranks.update(rank)
                except Exception as e:
                    logger.warning(f"获取 taxid {taxid} 的 rank 失败: {e}")
            
            # 构建层级到 taxid 的映射字典
            result = {}
            for taxid in taxids:
                try:
                    rank = ranks.get(int(taxid))
                    if rank in self.taxonomy_levels:
                        result[rank] = taxid
                except Exception as e:
                    logger.warning(f"处理 taxid {taxid} 失败: {e}")
            
            return result
        except Exception as e:
            logger.warning(f"解析分类学字符串 {taxonomy_string} 失败: {e}")
            return {}
