#!/usr/bin/env python3
"""
DeepMu CLI 特征提取模块。

此模块为 DeepMu CLI 提供特征提取功能，
允许用户从基因组数据中提取特征用于预测模型。
"""

import os
import logging
import argparse
import numpy as np
from pathlib import Path

from deepmu.features.feature_extraction import extract_features_batch, combine_feature_files

# 配置日志
logger = logging.getLogger(__name__)

def extract_features(args: argparse.Namespace) -> int:
    """为基因组提取特征。

    参数:
        args: 命令行参数

    返回:
        退出代码 (0 表示成功，非零表示失败)
    """
    try:
        # 如果输出目录不存在则创建
        os.makedirs(args.output_dir, exist_ok=True)

        # 检查 HEG KO 列表是否存在
        if not os.path.exists(args.heg_ko_list):
            logger.warning(f"HEG KO 列表 {args.heg_ko_list} 不存在。创建空文件。")
            Path(args.heg_ko_list).touch()

        # 检查 KEGG 映射文件是否存在
        if not os.path.exists(args.kegg_map):
            logger.warning(f"KEGG 映射文件 {args.kegg_map} 不存在。将不计算代谢途径特征。")

        # 提取特征
        logger.info("开始特征提取...")

        extract_features_batch(
            args.metadata,
            args.output_dir,
            args.genome_dir,
            args.cds_dir,
            args.faa_dir,
            args.ko_dir,
            args.kegg_map,
            args.heg_ko_list,
            args.advanced_codon_features,
            args.num_processes,
            args.trna_dir,
            args.rrna_dir
        )

        # Combine feature files
        if args.combine:
            output_tsv = args.output_tsv or os.path.join(args.output_dir, "combined_features.tsv")
            logger.info(f"Combining feature files into {output_tsv}...")
            combine_feature_files(args.output_dir, output_tsv)
            logger.info(f"Combined features saved to {output_tsv}")

        logger.info("Feature extraction completed successfully.")
        return 0

    except Exception as e:
        logger.error(f"Error during feature extraction: {e}")
        return 1

def extract_features_for_genome(args: argparse.Namespace) -> int:
    """Extract features for a single genome.

    Args:
        args: Command-line arguments

    Returns:
        Exit code (0 for success, non-zero for failure)
    """
    try:
        from deepmu.features.feature_extraction import extract_features_for_genome

        # Create output directory if it doesn't exist
        os.makedirs(args.output_dir, exist_ok=True)

        # Define output file
        output_file = os.path.join(args.output_dir, f"{args.genome_id}_features.npz")

        # Extract features
        logger.info(f"Extracting features for genome {args.genome_id}...")
        features = extract_features_for_genome(
            args.genome_id,
            args.genome_file,
            args.cds_file,
            args.faa_file,
            args.ko_file,
            args.taxid,
            args.kegg_map,
            args.heg_ko_list,
            args.advanced_codon_features,
            genetic_code=11,  # Default to bacterial genetic code
            trna_file=args.trna_file,
            rrna_file=args.rrna_file
        )

        # Save features
        np.savez(output_file, **features)
        logger.info(f"Features saved to {output_file}")

        # Convert to TSV if requested
        if args.output_tsv:
            import pandas as pd
            # Convert numpy arrays to lists for serialization
            features_dict = {}
            for key, value in features.items():
                if isinstance(value, np.ndarray):
                    features_dict[key] = value.tolist()
                else:
                    features_dict[key] = value
            # Create a DataFrame with the features
            df = pd.DataFrame([features_dict])
            # Save to TSV
            df.to_csv(args.output_tsv, sep='\t', index=False)
            logger.info(f"Features saved to {args.output_tsv}")

        return 0

    except Exception as e:
        logger.error(f"Error during feature extraction: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1

def add_feature_args(parser: argparse.ArgumentParser) -> None:
    """Add feature extraction arguments to parser.

    Args:
        parser: ArgumentParser to add arguments to
    """
    # Required arguments
    parser.add_argument("--metadata", required=True, help="Path to metadata file")

    # Data directories
    parser.add_argument("--genome-dir", default="training_data/genomes", help="Directory containing genome files")
    parser.add_argument("--cds-dir", default="training_data/cds_ffn", help="Directory containing CDS files")
    parser.add_argument("--faa-dir", default="training_data/cds_faa", help="Directory containing protein files")
    parser.add_argument("--ko-dir", default="training_data/kegg/ko_files", help="Directory containing KO files")
    parser.add_argument("--trna-dir", default=None, help="Directory containing tRNA files from tRNAscan-SE")
    parser.add_argument("--rrna-dir", default=None, help="Directory containing rRNA files from Barrnap")

    # Output options
    parser.add_argument("--output-dir", default="features", help="Directory to save feature files")
    parser.add_argument("--combine", action="store_true", help="Combine feature files into a single TSV file")
    parser.add_argument("--output-tsv", help="Path to output TSV file (if --combine is specified)")

    # Feature options
    parser.add_argument("--kegg-map", default="training_data/kegg/pathway_mapping.txt", help="Path to KEGG mapping file")
    parser.add_argument("--heg-ko-list", default="training_data/kegg/heg_ko_list.txt", help="Path to HEG KO list file for HEG-specific codon and amino acid features")
    parser.add_argument("--advanced-codon-features", action="store_true", help="Calculate advanced codon features")

    # Processing options
    parser.add_argument("--num-processes", type=int, default=os.cpu_count(), help="Number of processes to use")
    parser.add_argument("--skip-existing", action="store_true", help="Skip genomes with existing feature files")

def add_feature_single_args(parser: argparse.ArgumentParser) -> None:
    """Add single genome feature extraction arguments to parser.

    Args:
        parser: ArgumentParser to add arguments to
    """
    # Required arguments
    parser.add_argument("--genome-id", required=True, help="Genome ID")

    # Input files
    parser.add_argument("--genome-file", help="Path to genome file in FASTA format")
    parser.add_argument("--cds-file", help="Path to CDS file in FASTA format")
    parser.add_argument("--faa-file", help="Path to protein file in FASTA format")
    parser.add_argument("--ko-file", help="Path to KO file")
    parser.add_argument("--taxid", help="NCBI taxonomy ID")
    parser.add_argument("--trna-file", help="Path to tRNA file from tRNAscan-SE")
    parser.add_argument("--rrna-file", help="Path to rRNA file from Barrnap")

    # Output options
    parser.add_argument("--output-dir", default="features", help="Directory to save feature files")
    parser.add_argument("--output-tsv", help="Path to output TSV file")

    # Feature options
    parser.add_argument("--kegg-map", default="training_data/kegg/pathway_mapping.txt", help="Path to KEGG mapping file")
    parser.add_argument("--heg-ko-list", default="training_data/kegg/heg_ko_list.txt", help="Path to HEG KO list file for HEG-specific codon and amino acid features")
    parser.add_argument("--advanced-codon-features", action="store_true", help="Calculate advanced codon features")

def setup_feature_parser(subparsers: argparse._SubParsersAction) -> None:
    """Set up feature extraction subcommand parser.

    Args:
        subparsers: Subparsers object to add parser to
    """
    # Feature extraction command
    feature_parser = subparsers.add_parser(
        'feature',
        help='Extract features from genomic data'
    )
    feature_subparsers = feature_parser.add_subparsers(title='feature commands', dest='feature_command')

    # Batch feature extraction
    batch_parser = feature_subparsers.add_parser(
        'batch',
        help='Extract features for multiple genomes'
    )
    add_feature_args(batch_parser)
    batch_parser.set_defaults(func=extract_features)

    # Single genome feature extraction
    single_parser = feature_subparsers.add_parser(
        'single',
        help='Extract features for a single genome'
    )
    add_feature_single_args(single_parser)
    single_parser.set_defaults(func=extract_features_for_genome)
