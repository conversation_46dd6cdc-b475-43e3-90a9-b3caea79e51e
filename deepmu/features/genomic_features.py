"""
基因组特征计算器模块

这个模块实现了直接从基因组序列中进行全面基因组特征提取的功能，
包括基因组大小、GC含量、核苷酸/二核苷酸频率和各种偏斜指标，
用于微生物生长和温度预测。

主要功能：
1. 基本基因组指标计算 - 基因组大小、GC含量等
2. 核苷酸组成分析 - 单核苷酸和二核苷酸频率
3. 偏斜指标计算 - GC偏斜、AT偏斜等多种偏斜指标
4. 结构特征检测 - 偏斜变化点和其他结构特征
5. 断点特征分析 - 检测基因组结构变异的增强断点特征
6. 标准化特征 - 归一化的断点特征和偏斜指标

技术特点：
- 直接从基因组序列计算特征
- 捕获与微生物生长速率和最适生长温度相关的全基因组模式
- 支持多种偏斜指标和结构特征分析
- 提供标准化和归一化的特征输出
"""

import os
import logging
import numpy as np
from collections import Counter
from Bio import SeqIO
from typing import List, Dict, Tuple, Optional
from scipy.signal import find_peaks

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class GenomicFeatureCalculator:
    """
    基因组特征计算器

    这个类直接从基因组序列计算各种基因组特征，包括：

    基本指标：
    - 基本基因组指标 (大小、GC含量)
    - 核苷酸和二核苷酸频率
    - 各种偏斜指标 (GC偏斜、AT偏斜等)
    - 偏斜变化点和其他结构特征

    高级特征：
    - 用于检测基因组结构变异的增强断点特征
    - 二核苷酸偏斜断点和标准化断点特征
    - 嘌呤-嘧啶偏斜、酮-氨基偏斜等
    - 强-弱偏斜和其他化学性质偏斜

    应用价值：
    这些特征捕获了可能与微生物生长速率和最适生长温度相关的全基因组模式。
    通过分析基因组的组成和结构特征，可以为生长参数预测提供重要信息。
    """

    def __init__(self):
        """初始化基因组特征计算器"""
        # Define nucleotides and dinucleotides
        self.nucleotides = ['A', 'C', 'G', 'T']
        self.dinucleotides = [n1 + n2 for n1 in self.nucleotides for n2 in self.nucleotides]

        # Define skew pairs
        self.skew_pairs = [
            ('G', 'C'),  # GC skew
            ('A', 'T'),  # AT skew
            (['A', 'G'], ['C', 'T']),  # Purine-Pyrimidine skew
            (['G', 'T'], ['A', 'C']),  # Keto-Amino skew
            (['G', 'C'], ['A', 'T']),  # Strong-Weak skew
        ]

        # Add dinucleotide skew pairs
        self.skew_pairs.extend([
            (dinuc1, dinuc2) for i, dinuc1 in enumerate(self.dinucleotides)
            for j, dinuc2 in enumerate(self.dinucleotides) if i < j
        ][:11])  # Limit to 11 most informative pairs

        # Define complementary dinucleotide pairs for breakpoint detection
        self.complementary_pairs = [
            ('AA', 'TT'), ('AC', 'GT'), ('AG', 'CT'), ('AT', 'AT'),
            ('CA', 'TG'), ('CC', 'GG'), ('CG', 'CG'), ('GA', 'TC'),
            ('GC', 'GC'), ('TA', 'TA')
        ]

        # Default window size and step size for skew calculations
        self.window_size = 1000
        self.step_size = 100

        # Threshold for change point detection
        self.change_point_threshold = 0.1

    def calculate_features(self, genome_file: str) -> Dict[str, float]:
        """Calculate genomic features from a genome file.

        Args:
            genome_file: Path to genome file in FASTA format

        Returns:
            Dictionary of genomic features
        """
        logger.info(f"Calculating genomic features for {genome_file}")

        # Read genome sequences
        try:
            sequences = []
            record_count = 0
            for record in SeqIO.parse(genome_file, "fasta"):
                record_count += 1
                seq = str(record.seq).upper()
                sequences.append(seq)
                logger.debug(f"Read sequence {record.id} with length {len(seq)}")

            logger.info(f"Read {record_count} sequences from {genome_file}")

            if not sequences:
                logger.warning(f"No valid sequences found in {genome_file}")
                return {}

            # Calculate all features
            features = {}

            # Basic genome metrics
            features.update(self.calculate_basic_metrics(sequences))

            # Nucleotide and dinucleotide frequencies
            features.update(self.calculate_nucleotide_frequencies(sequences))
            features.update(self.calculate_dinucleotide_frequencies(sequences))

            # Skew metrics
            features.update(self.calculate_skew_metrics(sequences))

            # Skew change points
            features.update(self.calculate_skew_change_points(sequences))

            # Enhanced breakpoint features
            features.update(self.calculate_enhanced_breakpoint_features(sequences))

            return features

        except Exception as e:
            logger.error(f"Error calculating genomic features: {e}")
            return {}

    def calculate_basic_metrics(self, sequences: List[str]) -> Dict[str, float]:
        """Calculate basic genome metrics.

        Args:
            sequences: List of genome sequences

        Returns:
            Dictionary of basic metrics
        """
        logger.info("Calculating basic genome metrics")

        # Calculate genome size
        genome_size = sum(len(seq) for seq in sequences)
        logger.info(f"Calculated genome size: {genome_size} bp")

        # Calculate GC content
        gc_count = sum(seq.count('G') + seq.count('C') for seq in sequences)
        gc_content = gc_count / genome_size if genome_size > 0 else 0
        logger.info(f"Calculated GC content: {gc_content:.4f}")

        # Create basic metrics dictionary
        basic_metrics = {
            'genome_size': genome_size,
            'gc_content': gc_content,
        }

        logger.info(f"Basic metrics calculated: {basic_metrics}")
        return basic_metrics

    def calculate_nucleotide_frequencies(self, sequences: List[str]) -> Dict[str, float]:
        """Calculate nucleotide frequencies.

        Args:
            sequences: List of genome sequences

        Returns:
            Dictionary of nucleotide frequencies
        """
        logger.info("Calculating nucleotide frequencies")

        # Combine sequences
        combined_seq = ''.join(sequences)

        # Count nucleotides
        nucleotide_counts = Counter(combined_seq)
        total_count = sum(nucleotide_counts.get(n, 0) for n in self.nucleotides)

        # Calculate frequencies
        features = {}
        for nucleotide in self.nucleotides:
            features[f'freq_{nucleotide}'] = nucleotide_counts.get(nucleotide, 0) / total_count if total_count > 0 else 0

        return features

    def calculate_dinucleotide_frequencies(self, sequences: List[str]) -> Dict[str, float]:
        """Calculate dinucleotide frequencies.

        Args:
            sequences: List of genome sequences

        Returns:
            Dictionary of dinucleotide frequencies
        """
        logger.info("Calculating dinucleotide frequencies")

        # Combine sequences
        combined_seq = ''.join(sequences)

        # Count dinucleotides
        dinucleotide_counts = Counter()
        for i in range(len(combined_seq) - 1):
            dinucleotide = combined_seq[i:i+2]
            if all(n in 'ACGT' for n in dinucleotide):
                dinucleotide_counts[dinucleotide] += 1

        total_count = sum(dinucleotide_counts.values())

        # Calculate frequencies
        features = {}
        for dinucleotide in self.dinucleotides:
            features[f'dinuc_freq_{dinucleotide}'] = dinucleotide_counts.get(dinucleotide, 0) / total_count if total_count > 0 else 0

        return features

    def calculate_skew_metrics(self, sequences: List[str]) -> Dict[str, float]:
        """Calculate skew metrics.

        Args:
            sequences: List of genome sequences

        Returns:
            Dictionary of skew metrics
        """
        logger.info("Calculating skew metrics")

        # Combine sequences
        combined_seq = ''.join(sequences)

        features = {}

        # Calculate GC skew
        g_count = combined_seq.count('G')
        c_count = combined_seq.count('C')
        gc_total = g_count + c_count
        gc_skew = (g_count - c_count) / gc_total if gc_total > 0 else 0
        features['gc_skew'] = gc_skew

        # Calculate AT skew
        a_count = combined_seq.count('A')
        t_count = combined_seq.count('T')
        at_total = a_count + t_count
        at_skew = (a_count - t_count) / at_total if at_total > 0 else 0
        features['at_skew'] = at_skew

        # Calculate Purine-Pyrimidine skew
        purine_count = a_count + g_count
        pyrimidine_count = c_count + t_count
        purine_pyrimidine_total = purine_count + pyrimidine_count
        purine_pyrimidine_skew = (purine_count - pyrimidine_count) / purine_pyrimidine_total if purine_pyrimidine_total > 0 else 0
        features['purine_pyrimidine_skew'] = purine_pyrimidine_skew

        # Calculate Keto-Amino skew
        keto_count = g_count + t_count
        amino_count = a_count + c_count
        keto_amino_total = keto_count + amino_count
        keto_amino_skew = (keto_count - amino_count) / keto_amino_total if keto_amino_total > 0 else 0
        features['keto_amino_skew'] = keto_amino_skew

        # Calculate Strong-Weak skew
        strong_count = g_count + c_count
        weak_count = a_count + t_count
        strong_weak_total = strong_count + weak_count
        strong_weak_skew = (strong_count - weak_count) / strong_weak_total if strong_weak_total > 0 else 0
        features['strong_weak_skew'] = strong_weak_skew

        # Calculate dinucleotide skews
        for i, (pair1, pair2) in enumerate(self.skew_pairs[5:]):
            # Count occurrences of each dinucleotide
            count1 = 0
            count2 = 0

            # Properly count dinucleotides by sliding a window of size 2
            for j in range(len(combined_seq) - 1):
                dinuc = combined_seq[j:j+2]
                if dinuc == pair1:
                    count1 += 1
                elif dinuc == pair2:
                    count2 += 1

            total = count1 + count2
            skew = (count1 - count2) / total if total > 0 else 0
            features[f'dinuc_skew_{i+1}'] = skew

            # Log the counts for debugging
            logger.debug(f"Dinucleotide skew {i+1}: {pair1}/{pair2} = {count1}/{count2} = {skew}")

        return features

    def calculate_skew_along_genome(self, sequences: List[str], window_size: int = 10000) -> Dict[str, List[float]]:
        """Calculate skew along the genome.

        Args:
            sequences: List of genome sequences
            window_size: Window size for calculating skew

        Returns:
            Dictionary of skew values along the genome
        """
        logger.info(f"Calculating skew along genome with window size {window_size}")

        # Combine sequences
        combined_seq = ''.join(sequences)

        # Calculate skew along genome
        skew_values = {}

        # Initialize skew values
        for pair_name, _ in zip(['gc_skew', 'at_skew', 'purine_pyrimidine_skew', 'keto_amino_skew',
                               'strong_weak_skew'] + [f'dinuc_skew_{i+1}' for i in range(11)],
                              self.skew_pairs):
            skew_values[pair_name] = []

        # Calculate skew in windows
        for i in range(0, len(combined_seq) - window_size + 1, window_size // 2):
            window = combined_seq[i:i+window_size]

            # Count nucleotides in window
            counts = Counter(window)

            # Calculate skews
            for pair_name, pair in zip(['gc_skew', 'at_skew', 'purine_pyrimidine_skew', 'keto_amino_skew',
                                      'strong_weak_skew'] + [f'dinuc_skew_{i+1}' for i in range(11)],
                                     self.skew_pairs):
                if 'dinuc_skew' in pair_name:
                    # For dinucleotide skews, we need to count dinucleotides
                    pair_idx = int(pair_name.split('_')[2]) - 1
                    pair1, pair2 = self.skew_pairs[5 + pair_idx]

                    # Count occurrences of each dinucleotide
                    count1 = 0
                    count2 = 0

                    # Properly count dinucleotides by sliding a window of size 2
                    for j in range(len(window) - 1):
                        dinuc = window[j:j+2]
                        if dinuc == pair1:
                            count1 += 1
                        elif dinuc == pair2:
                            count2 += 1

                    # Log the counts for debugging
                    logger.debug(f"Window {i}: Dinucleotide skew {pair_name}: {pair1}/{pair2} = {count1}/{count2} = {count1 / total if total > 0 else 0}")
                elif isinstance(pair[0], list):
                    # For composite pairs like purines vs pyrimidines
                    count1 = sum(counts.get(n, 0) for n in pair[0])
                    count2 = sum(counts.get(n, 0) for n in pair[1])
                else:
                    # For simple pairs like G vs C
                    count1 = counts.get(pair[0], 0)
                    count2 = counts.get(pair[1], 0)

                total = count1 + count2
                skew = (count1 - count2) / total if total > 0 else 0
                skew_values[pair_name].append(skew)

        return skew_values

    def calculate_skew_change_points(self, sequences: List[str]) -> Dict[str, float]:
        """Calculate skew change points.

        Args:
            sequences: List of genome sequences

        Returns:
            Dictionary of skew change point features
        """
        logger.info("Calculating skew change points")

        # Calculate skew along genome
        skew_values = self.calculate_skew_along_genome(sequences)

        features = {}

        # Calculate change points for each skew
        for skew_name, values in skew_values.items():
            if not values or len(values) < 10:
                features[f'{skew_name}_change_points'] = 0
                features[f'{skew_name}_mean'] = 0
                features[f'{skew_name}_std'] = 0
                features[f'{skew_name}_min'] = 0
                features[f'{skew_name}_max'] = 0
                features[f'{skew_name}_range'] = 0
                features[f'{skew_name}_sign_changes'] = 0
                continue

            # Convert to numpy array
            values_array = np.array(values)

            # Apply smoothing to reduce noise
            window_size = min(5, len(values_array) // 10)
            if window_size > 1:
                smoothed_values = np.convolve(values_array, np.ones(window_size)/window_size, mode='valid')
            else:
                smoothed_values = values_array

            # Find peaks and valleys with prominence to avoid detecting noise
            peaks, _ = find_peaks(smoothed_values, prominence=0.05)
            valleys, _ = find_peaks(-smoothed_values, prominence=0.05)

            # Count change points
            change_points = len(peaks) + len(valleys)

            # Normalize by genome size
            genome_size = sum(len(seq) for seq in sequences)
            change_points_per_mb = change_points / (genome_size / 1e6) if genome_size > 0 else 0

            features[f'{skew_name}_change_points'] = change_points_per_mb

            # Calculate statistics
            features[f'{skew_name}_mean'] = np.mean(values_array)
            features[f'{skew_name}_std'] = np.std(values_array)
            features[f'{skew_name}_min'] = np.min(values_array)
            features[f'{skew_name}_max'] = np.max(values_array)
            features[f'{skew_name}_range'] = np.max(values_array) - np.min(values_array)

            # Calculate sign changes
            sign_changes = np.sum(np.diff(np.signbit(values_array)))
            features[f'{skew_name}_sign_changes'] = sign_changes

            # Log the number of change points for debugging
            logger.info(f"{skew_name}: {change_points} change points detected, {change_points_per_mb:.2f} per Mb")

        return features

    def detect_change_points(self, skew_values: np.ndarray, threshold: float = None) -> Tuple[int, List[int]]:
        """Detect change points in skew values.

        Args:
            skew_values: Array of skew values
            threshold: Threshold for change point detection (default: self.change_point_threshold)

        Returns:
            Tuple of (number of change points, list of change point indices)
        """
        if threshold is None:
            threshold = self.change_point_threshold

        # Calculate differences between consecutive values
        diffs = np.diff(skew_values)

        # Detect change points where the difference exceeds the threshold
        change_points = np.where(np.abs(diffs) > threshold)[0]

        return len(change_points), change_points.tolist()

    def detect_sign_changes(self, skew_values: np.ndarray) -> Tuple[int, List[int]]:
        """Detect sign changes in skew values.

        Args:
            skew_values: Array of skew values

        Returns:
            Tuple of (number of sign changes, list of sign change indices)
        """
        # Calculate signs
        signs = np.sign(skew_values)

        # Detect sign changes
        sign_changes = np.where(np.diff(signs) != 0)[0]

        return len(sign_changes), sign_changes.tolist()

    def calculate_enhanced_breakpoint_features(self, sequences: List[str]) -> Dict[str, float]:
        """Calculate enhanced breakpoint features from genome sequences.

        This method calculates additional breakpoint features beyond the basic skew change points,
        including dinucleotide skew breakpoints and normalized breakpoint features.

        Args:
            sequences: List of genome sequences

        Returns:
            Dictionary of enhanced breakpoint features
        """
        logger.info("Calculating enhanced breakpoint features")

        # Combine sequences
        combined_seq = ''.join(sequences)
        genome_size = len(combined_seq)

        # Initialize features dictionary
        features = {}

        # Calculate skew metrics for sliding windows
        gc_skew = []
        at_skew = []
        purine_pyrimidine_skew = []
        keto_amino_skew = []

        # Calculate skew for each window
        for i in range(0, len(combined_seq) - self.window_size + 1, self.step_size):
            window = combined_seq[i:i+self.window_size]

            # Count nucleotides
            g_count = window.count('G')
            c_count = window.count('C')
            a_count = window.count('A')
            t_count = window.count('T')

            # Calculate GC skew: (G - C) / (G + C)
            if g_count + c_count > 0:
                gc_skew.append((g_count - c_count) / (g_count + c_count))
            else:
                gc_skew.append(0)

            # Calculate AT skew: (A - T) / (A + T)
            if a_count + t_count > 0:
                at_skew.append((a_count - t_count) / (a_count + t_count))
            else:
                at_skew.append(0)

            # Calculate purine-pyrimidine skew: (A + G - C - T) / (A + G + C + T)
            total = a_count + g_count + c_count + t_count
            if total > 0:
                purine_pyrimidine_skew.append((a_count + g_count - c_count - t_count) / total)
            else:
                purine_pyrimidine_skew.append(0)

            # Calculate keto-amino skew: (G + T - A - C) / (G + T + A + C)
            if total > 0:
                keto_amino_skew.append((g_count + t_count - a_count - c_count) / total)
            else:
                keto_amino_skew.append(0)

        # Calculate dinucleotide skew metrics
        dinuc_skew_metrics = {}

        # Define all possible dinucleotides
        for d1, d2 in self.complementary_pairs:
            # Skip identical pairs
            if d1 == d2:
                continue

            dinuc_skew = []

            # Calculate skew for each window
            for i in range(0, len(combined_seq) - self.window_size + 1, self.step_size):
                window = combined_seq[i:i+self.window_size]

                # Count dinucleotides
                d1_count = 0
                d2_count = 0

                for j in range(len(window) - 1):
                    dinuc = window[j:j+2]
                    if dinuc == d1:
                        d1_count += 1
                    elif dinuc == d2:
                        d2_count += 1

                # Calculate skew: (d1 - d2) / (d1 + d2)
                if d1_count + d2_count > 0:
                    skew = (d1_count - d2_count) / (d1_count + d2_count)
                else:
                    skew = 0

                dinuc_skew.append(skew)

            dinuc_skew_metrics[f"{d1}_{d2}"] = np.array(dinuc_skew)

        # Process each skew metric
        for skew_type, skew_values in {
            'gc_skew_enhanced': np.array(gc_skew),
            'at_skew_enhanced': np.array(at_skew),
            'purine_pyrimidine_skew_enhanced': np.array(purine_pyrimidine_skew),
            'keto_amino_skew_enhanced': np.array(keto_amino_skew)
        }.items():
            # Calculate basic statistics
            features[f"{skew_type}_mean"] = np.mean(skew_values)
            features[f"{skew_type}_std"] = np.std(skew_values)
            features[f"{skew_type}_min"] = np.min(skew_values)
            features[f"{skew_type}_max"] = np.max(skew_values)
            features[f"{skew_type}_range"] = np.max(skew_values) - np.min(skew_values)

            # Detect change points
            n_change_points, _ = self.detect_change_points(skew_values)
            features[f"{skew_type}_change_points"] = n_change_points
            features[f"{skew_type}_change_points_per_mb"] = n_change_points / (genome_size / 1e6) if genome_size > 0 else 0

            # Detect sign changes
            n_sign_changes, _ = self.detect_sign_changes(skew_values)
            features[f"{skew_type}_sign_changes"] = n_sign_changes
            features[f"{skew_type}_sign_changes_per_mb"] = n_sign_changes / (genome_size / 1e6) if genome_size > 0 else 0

        # Process each dinucleotide skew metric
        for dinuc_type, dinuc_values in dinuc_skew_metrics.items():
            # Calculate basic statistics
            features[f"dinuc_skew_{dinuc_type}_mean"] = np.mean(dinuc_values)
            features[f"dinuc_skew_{dinuc_type}_std"] = np.std(dinuc_values)

            # Detect change points
            n_change_points, _ = self.detect_change_points(dinuc_values)
            features[f"dinuc_skew_{dinuc_type}_change_points"] = n_change_points
            features[f"dinuc_skew_{dinuc_type}_change_points_per_mb"] = n_change_points / (genome_size / 1e6) if genome_size > 0 else 0

            # Detect sign changes
            n_sign_changes, _ = self.detect_sign_changes(dinuc_values)
            features[f"dinuc_skew_{dinuc_type}_sign_changes"] = n_sign_changes
            features[f"dinuc_skew_{dinuc_type}_sign_changes_per_mb"] = n_sign_changes / (genome_size / 1e6) if genome_size > 0 else 0

            # Create additional derived features for skew metrics
            features[f"dinuc_skew_{dinuc_type}_breakpoint_mean"] = np.mean(dinuc_values)
            features[f"dinuc_skew_{dinuc_type}_breakpoint_mean_squared"] = np.mean(dinuc_values) ** 2
            features[f"dinuc_skew_{dinuc_type}_breakpoint_std"] = np.std(dinuc_values)
            features[f"dinuc_skew_{dinuc_type}_breakpoint_std_squared"] = np.std(dinuc_values) ** 2

        # Add emphasized features for important change points
        for key in list(features.keys()):
            if '_change_points' in key or '_sign_changes' in key:
                features[f"important_{key}"] = features[key] * 2.0

        return features

    def process_genome(self, genome_file: str, output_file: str) -> None:
        """Process a genome and save features to a file.

        Args:
            genome_file: Path to genome file in FASTA format
            output_file: Path to output file
        """
        logger.info(f"Processing genome {genome_file}")

        # Calculate features
        features = self.calculate_features(genome_file)

        # Save features
        if features:
            np.savez(output_file, **features)
            logger.info(f"Saved features to {output_file}")
        else:
            logger.warning(f"No features to save for {genome_file}")

    def process_genomes(self, genome_dir: str, output_dir: str, genome_ids: Optional[List[str]] = None) -> None:
        """Process multiple genomes and save features.

        Args:
            genome_dir: Directory containing genome files
            output_dir: Directory to save feature files
            genome_ids: Optional list of genome IDs to process
        """
        logger.info(f"Processing genomes in {genome_dir}")

        # Create output directory
        os.makedirs(output_dir, exist_ok=True)

        # Get genome files
        genome_files = []
        if genome_ids:
            for genome_id in genome_ids:
                # Try different naming conventions for genome files
                genome_file_paths = [
                    os.path.join(genome_dir, f"{genome_id}.fna"),
                    os.path.join(genome_dir, f"{genome_id.replace('GCF_', 'GCA_')}.fna"),
                    os.path.join(genome_dir, f"{genome_id.replace('GCA_', 'GCF_')}.fna")
                ]

                found = False
                for path in genome_file_paths:
                    if os.path.exists(path):
                        genome_files.append((genome_id, path))
                        found = True
                        logger.debug(f"Found genome file: {path}")
                        break

                if not found:
                    logger.warning(f"Genome file not found for {genome_id}. Tried: {', '.join(genome_file_paths)}")
        else:
            for filename in os.listdir(genome_dir):
                if filename.endswith(".fna"):
                    genome_id = filename.split(".fna")[0]
                    genome_file = os.path.join(genome_dir, filename)
                    genome_files.append((genome_id, genome_file))

        # Process genomes
        for genome_id, genome_file in genome_files:
            output_file = os.path.join(output_dir, f"{genome_id}_genomic_features.npz")
            if os.path.exists(output_file):
                logger.info(f"Skipping {genome_id}, features already exist")
                continue

            self.process_genome(genome_file, output_file)

        logger.info(f"Processed {len(genome_files)} genomes")

def main():
    """Main function for command-line usage."""
    import argparse

    parser = argparse.ArgumentParser(description="Calculate genomic features from genome sequences")
    parser.add_argument("--genome-dir", type=str, required=True, help="Directory containing genome files")
    parser.add_argument("--output-dir", type=str, required=True, help="Directory to save feature files")
    parser.add_argument("--genome-list", type=str, help="File containing list of genome IDs to process")

    args = parser.parse_args()

    # Get genome IDs if provided
    genome_ids = None
    if args.genome_list:
        with open(args.genome_list, 'r') as f:
            genome_ids = [line.strip() for line in f if line.strip()]

    # Process genomes
    calculator = GenomicFeatureCalculator()
    calculator.process_genomes(args.genome_dir, args.output_dir, genome_ids)

if __name__ == "__main__":
    main()
