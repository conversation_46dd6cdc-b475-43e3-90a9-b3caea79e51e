# DeepMu 特征模块

该模块为微生物生长速率和最适温度预测提供了全面的特征提取与处理能力。

## 模块结构

features 模块包含以下组件：

### 特征计算器

- `codon_features.py`：从 CDS 序列计算密码子使用特征
- `amino_acid_features.py`：从蛋白质序列计算氨基酸特征
- `genomic_features.py`：直接从基因组序列计算基因组特征
- `sequence_features.py`：计算基于序列的特征
- `pathway_features.py`：计算通路相关特征
- `taxonomy_features.py`：计算分类学相关特征
- `ko_similarity.py`：计算 KO（KEGG Orthology）谱相似性
- `ko_cooccurrence.py`：计算 KO 共现特征
- `pathway_clustering.py`：实现通路聚类

### 特征整合

- `integrated_features.py`：整合基因组和密码子特征

### 特征提取

- `feature_extraction.py`：主特征提取流程
- `__main__.py`：特征提取命令行接口

## 用法

### 基础特征提取

```python
from deepmu.features import extract_features

# 为目录下所有基因组提取特征
extract_features(
    cds_dir="data/cds",
    ko_dir="data/ko",
    metadata_file="data/metadata.tsv",
    output_dir="features"
)
```

### 整合特征提取

```python
from deepmu.features import extract_integrated_features_batch

# 为所有基因组提取整合特征
extract_integrated_features_batch(
    genome_dir="data/genomes",
    cds_dir="data/cds",
    ko_dir="data/ko",
    metadata_file="data/metadata.tsv",
    output_dir="integrated_features"
)
```

### 命令行用法

```bash
# 为所有基因组提取特征
python -m deepmu.features \
    --cds-dir data/cds \
    --ko-dir data/ko \
    --metadata data/metadata.tsv \
    --output-dir features

# 提取整合特征
python generate_integrated_features.py \
    --genome-dir data/genomes \
    --cds-dir data/cds \
    --ko-dir data/ko \
    --metadata data/metadata.tsv \
    --output-dir integrated_features
```

## 特征类型

### 密码子特征

- 密码子使用偏好（CUB）
- 密码子对偏好（CPB）
- 密码子一致性
- 远程相互作用
- 高表达基因（HEG）特异性指标

### 氨基酸特征

- 氨基酸组成
- 理化性质
- 碳氮含量（C-ARSC 和 N-ARSC）
- HEG 特异性指标
- 氨基酸适应指数（AAAI）

### 基因组特征

- 基因组大小
- GC 含量
- 单核苷酸和二核苷酸频率
- 偏斜指标（GC skew、AT skew 等）
- 偏斜变化点

### 通路特征

- 通路完整性
- 通路聚类
- 通路类别富集

### KO 特征

- KO 谱相似性
- KO 共现模式

## 实现细节

详细的实现信息请参考各模块中的文档字符串（docstring）。
