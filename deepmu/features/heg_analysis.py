"""
HEG (Highly Expressed Genes) analysis module for DeepMu.

This module provides utilities for analyzing Highly Expressed Genes (HEGs)
in genomes, including identifying HEG sequences and calculating HEG-specific
features.
"""

import os
import logging
from pathlib import Path
from typing import Dict, Set, List, Tuple, Optional
from Bio import SeqIO

# Configure logging
logger = logging.getLogger("DeepMu")

def load_heg_ko_list(heg_ko_list_file: str) -> Set[str]:
    """Load HEG KO list from file.

    Args:
        heg_ko_list_file: Path to HEG KO list file

    Returns:
        Set of HEG KO IDs
    """
    heg_kos = set()
    try:
        with open(heg_ko_list_file, 'r') as f:
            for line in f:
                ko = line.strip()
                if ko:
                    heg_kos.add(ko)
        logger.info(f"Loaded {len(heg_kos)} HEG KOs from {heg_ko_list_file}")
    except Exception as e:
        logger.error(f"Error loading HEG KO list: {e}")

    return heg_kos

def load_ko_file(ko_file: str) -> Dict[str, str]:
    """Load KO file and create a mapping from sequence IDs to KO IDs.

    Args:
        ko_file: Path to KO file

    Returns:
        Dictionary mapping sequence IDs to KO IDs
    """
    ko_map = {}
    try:
        with open(ko_file, 'r') as f:
            for line in f:
                if line.strip() and not line.startswith('#'):
                    parts = line.strip().split('\t')
                    if len(parts) >= 2:
                        seq_id = parts[0]
                        ko_id = parts[1]
                        ko_map[seq_id] = ko_id
        logger.info(f"Loaded {len(ko_map)} KO mappings from {ko_file}")
    except Exception as e:
        logger.error(f"Error loading KO file: {e}")

    return ko_map

def load_sequences(cds_file: str) -> List[Tuple[str, str]]:
    """Load sequences from FASTA file.

    Args:
        cds_file: Path to FASTA file

    Returns:
        List of (sequence ID, sequence) tuples
    """
    sequences = []
    try:
        for record in SeqIO.parse(cds_file, "fasta"):
            sequences.append((record.id, str(record.seq)))
        logger.info(f"Loaded {len(sequences)} sequences from {cds_file}")
    except Exception as e:
        logger.error(f"Error loading sequences: {e}")

    return sequences

def identify_heg_sequences(sequences: List[Tuple[str, str]], ko_map: Dict[str, str], heg_kos: Set[str]) -> Tuple[List[str], List[str]]:
    """Identify HEG sequences based on KO mapping.

    Args:
        sequences: List of (sequence ID, sequence) tuples
        ko_map: Dictionary mapping sequence IDs to KO IDs
        heg_kos: Set of HEG KO IDs

    Returns:
        Tuple of (HEG sequence IDs, background sequence IDs)
    """
    heg_seq_ids = []
    bg_seq_ids = []

    for seq_id, _ in sequences:
        if seq_id in ko_map and ko_map[seq_id] in heg_kos:
            heg_seq_ids.append(seq_id)
        else:
            bg_seq_ids.append(seq_id)

    logger.info(f"Identified {len(heg_seq_ids)} HEG sequences and {len(bg_seq_ids)} background sequences")

    return heg_seq_ids, bg_seq_ids

def create_heg_ko_map(
    cds_file: str,
    ko_file: str,
    heg_ko_list: str
) -> Dict[str, str]:
    """Create a mapping from sequence IDs to HEG status.

    Args:
        cds_file: Path to CDS file
        ko_file: Path to KO file
        heg_ko_list: Path to HEG KO list file

    Returns:
        Dictionary mapping sequence IDs to HEG status ("HEG" or None)
    """
    # Load HEG KO list
    heg_kos = load_heg_ko_list(heg_ko_list)

    # Load KO file
    ko_map = load_ko_file(ko_file)

    # Load sequences
    sequences = load_sequences(cds_file)

    # Create HEG KO map
    heg_ko_map = {}
    for seq_id, _ in sequences:
        if seq_id in ko_map and ko_map[seq_id] in heg_kos:
            heg_ko_map[seq_id] = "HEG"

    logger.info(f"Created HEG KO map with {len(heg_ko_map)} HEG sequences out of {len(sequences)} total sequences")

    return heg_ko_map

def analyze_heg_sequences(
    cds_file: str,
    ko_file: str,
    heg_ko_list: str
) -> Dict[str, int]:
    """Analyze HEG sequences in a genome.

    Args:
        cds_file: Path to CDS file
        ko_file: Path to KO file
        heg_ko_list: Path to HEG KO list file

    Returns:
        Dictionary with HEG analysis results
    """
    # Load HEG KO list
    heg_kos = load_heg_ko_list(heg_ko_list)

    # Load KO file
    ko_map = load_ko_file(ko_file)

    # Load sequences
    sequences = load_sequences(cds_file)

    # Identify HEG sequences
    heg_seq_ids, bg_seq_ids = identify_heg_sequences(sequences, ko_map, heg_kos)

    # Return analysis results
    return {
        "total_sequences": len(sequences),
        "heg_sequences": len(heg_seq_ids),
        "bg_sequences": len(bg_seq_ids),
        "ko_mappings": len(ko_map),
        "heg_ratio": len(heg_seq_ids) / len(sequences) if sequences else 0
    }
