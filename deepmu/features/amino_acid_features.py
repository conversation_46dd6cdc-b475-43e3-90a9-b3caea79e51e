"""氨基酸特征计算器模块

此模块提供从蛋白质序列计算基于氨基酸特征的功能，
包括氨基酸组成、理化性质和其他用于微生物生长预测的相关指标。
"""

import os
import numpy as np
from collections import defaultdict
from typing import Dict, List, Optional, Set, Union
from Bio import SeqIO
import logging

# 配置日志
logger = logging.getLogger(__name__)

class AminoAcidFeatureCalculator:
    """基于氨基酸特征的计算器。"""

    def __init__(self, use_heg_features=False, heg_ko_ids=None, genetic_code=11):
        """初始化氨基酸特征计算器。

        参数:
            use_heg_features (bool): 是否为高表达基因和基因组背景计算单独的指标
            heg_ko_ids (set): 被认为是高表达基因(HEG)的KO ID集合
            genetic_code (int): 用于翻译的遗传密码 (默认: 11 用于细菌)
        """
        self.amino_acids = "ACDEFGHIKLMNPQRSTVWY"
        self.use_heg_features = use_heg_features
        self.heg_ko_ids = heg_ko_ids if heg_ko_ids else set()
        self.genetic_code = genetic_code
        self.aa_properties = {
            'A': {'hydrophobic': True, 'polar': False, 'charged': False, 'charge': 0},
            'C': {'hydrophobic': False, 'polar': True, 'charged': False, 'charge': 0},
            'D': {'hydrophobic': False, 'polar': True, 'charged': True, 'charge': -1},
            'E': {'hydrophobic': False, 'polar': True, 'charged': True, 'charge': -1},
            'F': {'hydrophobic': True, 'polar': False, 'charged': False, 'charge': 0},
            'G': {'hydrophobic': True, 'polar': False, 'charged': False, 'charge': 0},
            'H': {'hydrophobic': False, 'polar': True, 'charged': True, 'charge': 1},
            'I': {'hydrophobic': True, 'polar': False, 'charged': False, 'charge': 0},
            'K': {'hydrophobic': False, 'polar': True, 'charged': True, 'charge': 1},
            'L': {'hydrophobic': True, 'polar': False, 'charged': False, 'charge': 0},
            'M': {'hydrophobic': True, 'polar': False, 'charged': False, 'charge': 0},
            'N': {'hydrophobic': False, 'polar': True, 'charged': False, 'charge': 0},
            'P': {'hydrophobic': True, 'polar': False, 'charged': False, 'charge': 0},
            'Q': {'hydrophobic': False, 'polar': True, 'charged': False, 'charge': 0},
            'R': {'hydrophobic': False, 'polar': True, 'charged': True, 'charge': 1},
            'S': {'hydrophobic': False, 'polar': True, 'charged': False, 'charge': 0},
            'T': {'hydrophobic': False, 'polar': True, 'charged': False, 'charge': 0},
            'V': {'hydrophobic': True, 'polar': False, 'charged': False, 'charge': 0},
            'W': {'hydrophobic': True, 'polar': False, 'charged': False, 'charge': 0},
            'Y': {'hydrophobic': False, 'polar': True, 'charged': False, 'charge': 0}
        }

        # Carbon and nitrogen atoms per amino acid residue side chain (C-ARSC and N-ARSC)
        # Based on NC-ARSC.txt data
        self.aa_carbon_nitrogen = {
            'A': {'carbon': 3, 'nitrogen': 1, 'c_arsc': 1, 'n_arsc': 0},
            'C': {'carbon': 3, 'nitrogen': 1, 'c_arsc': 1, 'n_arsc': 0},
            'D': {'carbon': 4, 'nitrogen': 1, 'c_arsc': 2, 'n_arsc': 0},
            'E': {'carbon': 5, 'nitrogen': 1, 'c_arsc': 3, 'n_arsc': 0},
            'F': {'carbon': 9, 'nitrogen': 1, 'c_arsc': 7, 'n_arsc': 0},
            'G': {'carbon': 2, 'nitrogen': 1, 'c_arsc': 0, 'n_arsc': 0},
            'H': {'carbon': 6, 'nitrogen': 3, 'c_arsc': 4, 'n_arsc': 2},
            'I': {'carbon': 6, 'nitrogen': 1, 'c_arsc': 4, 'n_arsc': 0},
            'K': {'carbon': 6, 'nitrogen': 2, 'c_arsc': 4, 'n_arsc': 1},
            'L': {'carbon': 6, 'nitrogen': 1, 'c_arsc': 4, 'n_arsc': 0},
            'M': {'carbon': 5, 'nitrogen': 1, 'c_arsc': 3, 'n_arsc': 0},
            'N': {'carbon': 4, 'nitrogen': 2, 'c_arsc': 2, 'n_arsc': 1},
            'P': {'carbon': 5, 'nitrogen': 1, 'c_arsc': 3, 'n_arsc': 0},
            'Q': {'carbon': 5, 'nitrogen': 2, 'c_arsc': 3, 'n_arsc': 1},
            'R': {'carbon': 6, 'nitrogen': 4, 'c_arsc': 4, 'n_arsc': 3},
            'S': {'carbon': 3, 'nitrogen': 1, 'c_arsc': 1, 'n_arsc': 0},
            'T': {'carbon': 4, 'nitrogen': 1, 'c_arsc': 2, 'n_arsc': 0},
            'V': {'carbon': 5, 'nitrogen': 1, 'c_arsc': 3, 'n_arsc': 0},
            'W': {'carbon': 11, 'nitrogen': 2, 'c_arsc': 9, 'n_arsc': 1},
            'Y': {'carbon': 9, 'nitrogen': 1, 'c_arsc': 7, 'n_arsc': 0}
        }

    def calculate_features(self, sequences: List[str]) -> Dict[str, float]:
        """Calculate amino acid features from protein sequences.

        Args:
            sequences: List of protein sequences

        Returns:
            Dictionary of amino acid features
        """
        if not sequences:
            return {}

        # Initialize counters
        aa_counts = defaultdict(int)
        total_count = 0

        # Count amino acids across all sequences
        for sequence in sequences:
            sequence = sequence.upper()
            for aa in sequence:
                if aa in self.amino_acids:
                    aa_counts[aa] += 1
                    total_count += 1

        # Calculate amino acid composition
        aa_composition = {}
        for aa in self.amino_acids:
            aa_composition[f'aa_freq_{aa}'] = aa_counts[aa] / total_count if total_count > 0 else 0.0

        # Calculate property-based features
        property_counts = {
            'hydrophobic': 0,
            'polar': 0,
            'charged': 0,
            'positive': 0,
            'negative': 0
        }

        for aa, count in aa_counts.items():
            if aa in self.aa_properties:
                props = self.aa_properties[aa]
                if props['hydrophobic']:
                    property_counts['hydrophobic'] += count
                if props['polar']:
                    property_counts['polar'] += count
                if props.get('charged', False):
                    property_counts['charged'] += count
                    if props.get('charge', 0) > 0:
                        property_counts['positive'] += count
                    elif props.get('charge', 0) < 0:
                        property_counts['negative'] += count

        property_features = {}
        for prop, count in property_counts.items():
            property_features[f'aa_prop_{prop}'] = count / total_count if total_count > 0 else 0.0

        # Calculate charge ratio
        if property_counts['positive'] > 0 and property_counts['negative'] > 0:
            property_features['aa_charge_ratio'] = property_counts['positive'] / property_counts['negative']
        else:
            property_features['aa_charge_ratio'] = 1.0

        # Calculate hydrophobicity ratio
        if property_counts['hydrophobic'] > 0 and property_counts['polar'] > 0:
            property_features['aa_hydrophobicity_ratio'] = property_counts['hydrophobic'] / property_counts['polar']
        else:
            property_features['aa_hydrophobicity_ratio'] = 1.0

        # Calculate C-ARSC and N-ARSC features
        carbon_total = 0
        nitrogen_total = 0
        carbon_arsc_total = 0
        nitrogen_arsc_total = 0

        for aa, count in aa_counts.items():
            if aa in self.aa_carbon_nitrogen:
                cn_data = self.aa_carbon_nitrogen[aa]
                carbon_total += cn_data['carbon'] * count
                nitrogen_total += cn_data['nitrogen'] * count
                carbon_arsc_total += cn_data['c_arsc'] * count
                nitrogen_arsc_total += cn_data['n_arsc'] * count

        carbon_nitrogen_features = {}
        if total_count > 0:
            carbon_nitrogen_features['aa_carbon_total'] = carbon_total / total_count
            carbon_nitrogen_features['aa_nitrogen_total'] = nitrogen_total / total_count
            carbon_nitrogen_features['aa_c_arsc'] = carbon_arsc_total / total_count
            carbon_nitrogen_features['aa_n_arsc'] = nitrogen_arsc_total / total_count

            # Calculate C/N ratio
            if nitrogen_total > 0:
                carbon_nitrogen_features['aa_carbon_nitrogen_ratio'] = carbon_total / nitrogen_total
            else:
                carbon_nitrogen_features['aa_carbon_nitrogen_ratio'] = 0.0

            # Calculate C-ARSC/N-ARSC ratio
            if nitrogen_arsc_total > 0:
                carbon_nitrogen_features['aa_c_arsc_n_arsc_ratio'] = carbon_arsc_total / nitrogen_arsc_total
            else:
                carbon_nitrogen_features['aa_c_arsc_n_arsc_ratio'] = 0.0
        else:
            carbon_nitrogen_features['aa_carbon_total'] = 0.0
            carbon_nitrogen_features['aa_nitrogen_total'] = 0.0
            carbon_nitrogen_features['aa_c_arsc'] = 0.0
            carbon_nitrogen_features['aa_n_arsc'] = 0.0
            carbon_nitrogen_features['aa_carbon_nitrogen_ratio'] = 0.0
            carbon_nitrogen_features['aa_c_arsc_n_arsc_ratio'] = 0.0

        # Combine all features
        features = {**aa_composition, **property_features, **carbon_nitrogen_features}

        return features

    def split_heg_bg_sequences(self, sequences, ko_map):
        """Split sequences into HEG and background sets based on KO mapping.

        Args:
            sequences (list): List of sequence records
            ko_map (dict): Dictionary mapping sequence IDs to KO IDs

        Returns:
            tuple: (heg_sequences, bg_sequences)
        """
        heg_sequences = []
        bg_sequences = []

        for i, record in enumerate(sequences):
            seq_id = record.id if hasattr(record, 'id') else f"seq_{i}"
            if seq_id in ko_map:
                ko_id = ko_map[seq_id]
                if ko_id in self.heg_ko_ids:
                    heg_sequences.append(record)
                else:
                    bg_sequences.append(record)
            else:
                bg_sequences.append(record)

        return heg_sequences, bg_sequences

    def calculate_features_with_heg(self, sequences, ko_map=None):
        """Calculate amino acid features with distinction between HEGs and background.

        Args:
            sequences (list): List of sequence records or amino acid strings
            ko_map (dict): Dictionary mapping sequence IDs to KO IDs

        Returns:
            dict: Dictionary with standard, HEG-specific, background, and delta metrics
        """
        # If no KO map is provided, treat all sequences as background
        if ko_map is None:
            heg_sequences = []
            bg_sequences = sequences
        else:
            heg_sequences, bg_sequences = self.split_heg_bg_sequences(sequences, ko_map)

        # Calculate metrics for the entire genomic set
        all_sequences = [str(seq.seq) if hasattr(seq, 'seq') else seq for seq in sequences]
        genomic_metrics = self.calculate_features(all_sequences)

        # If no HEG sequences, return only genomic metrics
        if not heg_sequences:
            return genomic_metrics

        # Calculate metrics for HEG and background sets
        heg_seqs = [str(seq.seq) if hasattr(seq, 'seq') else seq for seq in heg_sequences]
        bg_seqs = [str(seq.seq) if hasattr(seq, 'seq') else seq for seq in bg_sequences]

        heg_metrics = self.calculate_features(heg_seqs)
        bg_metrics = self.calculate_features(bg_seqs)

        # Rename metrics to indicate they are for HEG or background
        heg_renamed = {f"HEG_{k}": v for k, v in heg_metrics.items()}
        bg_renamed = {f"BG_{k}": v for k, v in bg_metrics.items()}

        # Calculate delta metrics (difference between HEG and background)
        delta_metrics = {}
        for key in heg_metrics.keys():
            if key in bg_metrics:
                delta_metrics[f"delta_{key}"] = heg_metrics[key] - bg_metrics[key]

        # Calculate amino acid adaptation index (AAAI) - similar to CAI but for amino acids
        aaai = self._calculate_amino_acid_adaptation_index(heg_seqs, bg_seqs)

        # Calculate amino acid usage bias between HEG and background
        aa_bias = self._calculate_aa_bias_between_sets(heg_seqs, bg_seqs)

        # Combine all metrics
        combined_metrics = {}
        combined_metrics.update(genomic_metrics)
        combined_metrics.update(heg_renamed)
        combined_metrics.update(bg_renamed)
        combined_metrics.update(delta_metrics)
        combined_metrics['AAAI'] = aaai
        combined_metrics['aa_bias_HEG_BP'] = aa_bias

        return combined_metrics

    def _calculate_amino_acid_adaptation_index(self, heg_sequences, bg_sequences):
        """Calculate Amino Acid Adaptation Index (AAAI).

        This is similar to CAI but for amino acids, measuring how well the amino acid
        usage in background genes matches the bias in highly expressed genes.

        Args:
            heg_sequences (list): List of HEG amino acid sequences
            bg_sequences (list): List of background amino acid sequences

        Returns:
            float: AAAI value
        """
        if not heg_sequences or not bg_sequences:
            return 0.0

        # Count amino acids in HEG sequences
        heg_aa_counts = defaultdict(int)
        for seq in heg_sequences:
            for aa in seq.upper():
                if aa in self.amino_acids:
                    heg_aa_counts[aa] += 1

        # Calculate relative adaptiveness of each amino acid
        total_heg_aas = sum(heg_aa_counts.values())
        if total_heg_aas == 0:
            return 0.0

        # Calculate relative frequency of each amino acid in HEG
        heg_aa_freq = {aa: count / total_heg_aas for aa, count in heg_aa_counts.items()}

        # Find the most frequent amino acid in each group
        max_freq = 0.0
        for aa, freq in heg_aa_freq.items():
            if freq > max_freq:
                max_freq = freq

        # Calculate relative adaptiveness
        if max_freq == 0.0:
            return 0.0

        relative_adaptiveness = {aa: freq / max_freq for aa, freq in heg_aa_freq.items()}

        # Calculate AAAI for background sequences
        log_sum = 0.0
        aa_count = 0

        for seq in bg_sequences:
            for aa in seq.upper():
                if aa in self.amino_acids and aa in relative_adaptiveness:
                    log_sum += np.log(relative_adaptiveness[aa])
                    aa_count += 1

        if aa_count == 0:
            return 0.0

        # AAAI is the geometric mean of relative adaptiveness values
        return np.exp(log_sum / aa_count)

    def _calculate_aa_bias_between_sets(self, heg_sequences, bg_sequences):
        """Calculate amino acid usage bias between HEG and background sequences.

        Args:
            heg_sequences (list): List of HEG amino acid sequences
            bg_sequences (list): List of background amino acid sequences

        Returns:
            float: Amino acid usage bias value
        """
        if not heg_sequences or not bg_sequences:
            return 0.0

        # Count amino acids in both sets
        heg_aa_counts = defaultdict(int)
        bg_aa_counts = defaultdict(int)

        for seq in heg_sequences:
            for aa in seq.upper():
                if aa in self.amino_acids:
                    heg_aa_counts[aa] += 1

        for seq in bg_sequences:
            for aa in seq.upper():
                if aa in self.amino_acids:
                    bg_aa_counts[aa] += 1

        # Calculate frequencies
        total_heg_aas = sum(heg_aa_counts.values())
        total_bg_aas = sum(bg_aa_counts.values())

        if total_heg_aas == 0 or total_bg_aas == 0:
            return 0.0

        heg_aa_freq = {aa: count / total_heg_aas for aa, count in heg_aa_counts.items()}
        bg_aa_freq = {aa: count / total_bg_aas for aa, count in bg_aa_counts.items()}

        # Calculate Euclidean distance between frequency vectors
        sum_squared_diff = 0.0
        for aa in self.amino_acids:
            heg_freq = heg_aa_freq.get(aa, 0.0)
            bg_freq = bg_aa_freq.get(aa, 0.0)
            sum_squared_diff += (heg_freq - bg_freq) ** 2

        return np.sqrt(sum_squared_diff)

    def calculate_features_from_file(self, file_path: str, ko_file: str = None, heg_ko_list: str = None) -> Dict[str, float]:
        """Calculate amino acid features from a FASTA file.

        Args:
            file_path: Path to FASTA file containing protein sequences
            ko_file: Path to KO file (optional)
            heg_ko_list: Path to HEG KO list file (optional)

        Returns:
            Dictionary of amino acid features
        """
        try:
            # Read sequences from file
            sequences = []
            for record in SeqIO.parse(file_path, "fasta"):
                sequences.append(record)

            # If HEG features are enabled and KO file is provided
            if self.use_heg_features and ko_file and os.path.exists(ko_file) and self.heg_ko_ids:
                # Load KO annotations
                gene_to_ko = {}
                with open(ko_file, 'r') as f:
                    for line in f:
                        parts = line.strip().split('\t')
                        if len(parts) >= 2:
                            gene_id = parts[0]
                            ko = parts[1]
                            gene_to_ko[gene_id] = ko

                # Create KO map
                ko_map = {}
                for record in sequences:
                    gene_id = record.id
                    if gene_id in gene_to_ko:
                        ko_map[gene_id] = gene_to_ko[gene_id]

                # Calculate features with HEG distinction
                return self.calculate_features_with_heg(sequences, ko_map)
            else:
                # Calculate standard features
                seq_strings = [str(record.seq) for record in sequences]
                return self.calculate_features(seq_strings)
        except Exception as e:
            logger.error(f"Error calculating amino acid features from file {file_path}: {e}")
            return {}
