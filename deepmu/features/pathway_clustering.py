"""
基于通路的KO术语聚类。

本模块提供了基于KO术语参与相同KEGG通路进行聚类的函数，在保留生物学意义的同时降低维度。
"""

import numpy as np
import logging
from typing import Dict, List, Set
from collections import defaultdict
from pathlib import Path

from deepmu.features.pathway_features import PathwayDatabase

# 设置日志
logger = logging.getLogger(__name__)

class PathwayClusterCalculator:
    """
    基于通路的KO术语聚类计算器。

    该类根据KO术语参与的KEGG通路进行分组，创建代表功能模块的通路聚类特征。
    """

    # 类级别集合，用于追踪所有基因组中可能的通路聚类
    all_pathway_clusters = set()

    def __init__(self, kegg_path: str, min_ko_per_pathway: int = 5, min_pathway_completeness: float = 0.1):
        """
        初始化通路聚类计算器。

        参数:
            kegg_path: KEGG通路映射文件路径
            min_ko_per_pathway: 一个通路被考虑所需的最小KO术语数量
            min_pathway_completeness: 通路被包含的最小完整性阈值
        """
        self.pathway_db = PathwayDatabase(kegg_path)
        self.min_ko_per_pathway = min_ko_per_pathway
        self.min_pathway_completeness = min_pathway_completeness

        # 创建通路到KO的映射
        self.pathway_to_kos = self._create_pathway_to_kos_mapping()

        # 创建通路类别
        self.pathway_categories = self._create_pathway_categories()

        # 创建更高层次的通路聚类
        self.pathway_clusters = self._create_pathway_clusters()

        # 初始化所有可能的聚类名称集合
        self._initialize_all_cluster_names()

        logger.info(f"已初始化PathwayClusterCalculator，共有{len(self.pathway_to_kos)}个通路")
        logger.info(f"创建了{len(self.pathway_categories)}个通路类别")
        logger.info(f"创建了{len(self.pathway_clusters)}个通路聚类")
        logger.info(f"所有可能的通路聚类特征总数: {len(self.__class__.all_pathway_clusters)}")

    def _initialize_all_cluster_names(self):
        """
        初始化所有可能的聚类名称集合。
        """
        # 将所有聚类名称添加到类级别集合
        for cluster_name in self.pathway_clusters.keys():
            self.__class__.all_pathway_clusters.add(f"pathway_cluster_{cluster_name}")

    def _create_pathway_to_kos_mapping(self) -> Dict[str, Set[str]]:
        """
        创建通路ID到KO术语集合的映射。

        返回:
            通路ID到KO术语集合的字典
        """
        pathway_to_kos = defaultdict(set)

        # 遍历通路数据库中的所有KO术语
        for ko_id, pathways in self.pathway_db.kegg_map.items():
            for pathway_id in pathways:
                pathway_to_kos[pathway_id].add(ko_id)

        # 过滤掉KO术语数量过少的通路
        filtered_pathways = {
            pathway_id: ko_set
            for pathway_id, ko_set in pathway_to_kos.items()
            if len(ko_set) >= self.min_ko_per_pathway
        }

        logger.info(f"创建了{len(filtered_pathways)}个通路到KO的映射")
        logger.info(f"过滤掉了{len(pathway_to_kos) - len(filtered_pathways)}个KO术语少于{self.min_ko_per_pathway}的通路")

        return filtered_pathways

    def _create_pathway_categories(self) -> Dict[str, List[str]]:
        """
        根据ID和元数据将通路分组为类别。

        返回:
            类别名称到通路ID列表的字典
        """
        # 根据KEGG层级定义通路类别
        categories = {
            'carbohydrate_metabolism': ['ko00010', 'ko00020', 'ko00030', 'ko00040', 'ko00051', 'ko00052', 'ko00053', 'ko00500', 'ko00520', 'ko00620', 'ko00630', 'ko00640', 'ko00650'],
            'energy_metabolism': ['ko00190', 'ko00195', 'ko00196', 'ko00710', 'ko00720', 'ko00680', 'ko00910', 'ko00920'],
            'lipid_metabolism': ['ko00061', 'ko00062', 'ko00071', 'ko00072', 'ko00073', 'ko00100', 'ko00120', 'ko00140', 'ko00561', 'ko00564', 'ko00565', 'ko00590', 'ko00591', 'ko00592'],
            'nucleotide_metabolism': ['ko00230', 'ko00240', 'ko00250', 'ko00260', 'ko00270', 'ko00280', 'ko00290', 'ko00300', 'ko00310', 'ko00330', 'ko00340', 'ko00350', 'ko00360', 'ko00380', 'ko00400'],
            'amino_acid_metabolism': ['ko00250', 'ko00260', 'ko00270', 'ko00280', 'ko00290', 'ko00300', 'ko00310', 'ko00330', 'ko00340', 'ko00350', 'ko00360', 'ko00380', 'ko00400'],
            'glycan_metabolism': ['ko00510', 'ko00511', 'ko00512', 'ko00513', 'ko00514', 'ko00515', 'ko00531', 'ko00532', 'ko00533', 'ko00534', 'ko00540', 'ko00550', 'ko00563', 'ko00601', 'ko00603'],
            'cofactor_metabolism': ['ko00730', 'ko00740', 'ko00750', 'ko00760', 'ko00770', 'ko00780', 'ko00785', 'ko00790', 'ko00830', 'ko00860', 'ko00130'],
            'secondary_metabolites': ['ko00900', 'ko00901', 'ko00902', 'ko00903', 'ko00904', 'ko00905', 'ko00906', 'ko00908', 'ko00909', 'ko00940', 'ko00941', 'ko00942', 'ko00943', 'ko00944', 'ko00945', 'ko00950', 'ko00960', 'ko00965', 'ko00966', 'ko00981', 'ko00982', 'ko00998'],
            'xenobiotics_metabolism': ['ko00980', 'ko00982', 'ko00983', 'ko00984', 'ko00985', 'ko00986', 'ko00988', 'ko00989'],
            'genetic_information_processing': ['ko03010', 'ko03013', 'ko03015', 'ko03018', 'ko03020', 'ko03022', 'ko03030', 'ko03040', 'ko03050', 'ko03060', 'ko03070', 'ko03410', 'ko03420', 'ko03430', 'ko03440', 'ko03450'],
            'environmental_information_processing': ['ko02010', 'ko02020', 'ko02024', 'ko02025', 'ko02026', 'ko02030', 'ko02040', 'ko04010', 'ko04011', 'ko04012', 'ko04013', 'ko04014', 'ko04015', 'ko04016', 'ko04020', 'ko04070', 'ko04071', 'ko04072', 'ko04073', 'ko04080'],
            'cellular_processes': ['ko04110', 'ko04111', 'ko04112', 'ko04113', 'ko04114', 'ko04120', 'ko04122', 'ko04130', 'ko04140', 'ko04141', 'ko04142', 'ko04144', 'ko04145', 'ko04146', 'ko04150', 'ko04151', 'ko04152', 'ko04210', 'ko04211', 'ko04212', 'ko04213', 'ko04214', 'ko04215', 'ko04216', 'ko04217', 'ko04218', 'ko04510', 'ko04520', 'ko04530', 'ko04810', 'ko04910', 'ko04920', 'ko04930', 'ko04950', 'ko04960', 'ko04970', 'ko04971', 'ko04972', 'ko04973', 'ko04974', 'ko04975', 'ko04976', 'ko04977', 'ko04978', 'ko04979']
        }

        # 只保留数据库中存在的通路
        filtered_categories = {}
        for category, pathway_ids in categories.items():
            existing_pathways = [p for p in pathway_ids if p in self.pathway_to_kos]
            if existing_pathways:
                filtered_categories[category] = existing_pathways

        return filtered_categories

    def _create_pathway_clusters(self) -> Dict[str, Set[str]]:
        """
        通过将相关通路的KO术语分组来创建通路聚类。

        返回:
            聚类名称到KO术语集合的字典
        """
        pathway_clusters = {}

        # 从通路类别创建聚类
        for category, pathway_ids in self.pathway_categories.items():
            # 合并该类别下所有通路的KO术语
            ko_set = set()
            for pathway_id in pathway_ids:
                if pathway_id in self.pathway_to_kos:
                    ko_set.update(self.pathway_to_kos[pathway_id])

            if ko_set:
                pathway_clusters[f"cluster_{category}"] = ko_set

        # 将单个通路作为聚类添加
        for pathway_id, ko_set in self.pathway_to_kos.items():
            # 使用通路ID作为聚类名称
            pathway_clusters[f"pathway_{pathway_id}"] = ko_set

        return pathway_clusters

    def calculate_cluster_features(self, ko_terms: List[str]) -> Dict[str, float]:
        """
        为一组KO术语计算通路聚类特征。

        参数:
            ko_terms: 基因组注释的KO术语列表

        返回:
            聚类名称到完整性分数的字典
        """
        # 转换为集合以加快查找速度
        ko_set = set(ko_terms)

        # 初始化所有可能聚类的特征为0.0
        cluster_features = {cluster_name: 0.0 for cluster_name in self.__class__.all_pathway_clusters}

        # 计算每个聚类的完整性
        for cluster_name, cluster_kos in self.pathway_clusters.items():
            if cluster_kos:
                # 完整性为已存在KO术语占聚类KO术语总数的比例
                completeness = len(ko_set.intersection(cluster_kos)) / len(cluster_kos)

                # 只有当完整性高于阈值时才更新
                if completeness >= self.min_pathway_completeness:
                    cluster_features[f"pathway_cluster_{cluster_name}"] = completeness

        return cluster_features

    def calculate_cluster_features_from_ko_file(self, ko_file_path: str) -> Dict[str, float]:
        """
        从包含KO术语的文件计算通路聚类特征。

        参数:
            ko_file_path: 包含KO术语的文件路径（每行一个或表格格式）

        返回:
            聚类名称到完整性分数的字典
        """
        ko_terms = []
        try:
            with open(ko_file_path, 'r') as f:
                for line in f:
                    # 处理不同的文件格式
                    parts = line.strip().split('\t')
                    if len(parts) >= 2:
                        ko_terms.append(parts[1])
        except Exception as e:
            logger.error(f"读取KO文件失败: {e}")
            return {cluster_name: 0.0 for cluster_name in self.__class__.all_pathway_clusters}

        # 使用基础方法计算特征
        cluster_features = self.calculate_cluster_features(ko_terms)

        # 确保包含所有可能的聚类
        for cluster_name in self.__class__.all_pathway_clusters:
            if cluster_name not in cluster_features:
                cluster_features[cluster_name] = 0.0

        return cluster_features

    def generate_cluster_features_for_dataset(
        self,
        ko_dir: str,
        output_dir: str,
        # 不需要feature_prefix参数，方法中已硬编码
    ) -> None:
        """
        为数据集中的所有基因组生成通路聚类特征。

        参数:
            ko_dir: 包含KO注释文件的目录
            output_dir: 保存特征文件的目录
        """
        ko_dir_path = Path(ko_dir)
        output_dir_path = Path(output_dir)
        output_dir_path.mkdir(exist_ok=True)

        # 处理每个KO文件
        ko_files = list(ko_dir_path.glob("*.tsv"))
        logger.info(f"从{ko_dir}处理{len(ko_files)}个KO文件")

        # 第一次遍历：收集所有可能的通路聚类
        logger.info("第一次遍历：收集所有可能的通路聚类...")
        for ko_file in ko_files:
            # 计算聚类特征以填充all_pathway_clusters
            _ = self.calculate_cluster_features_from_ko_file(ko_file)

        # 记录通路聚类总数
        logger.info(f"发现{len(self.__class__.all_pathway_clusters)}个唯一通路聚类特征")

        # 第二次遍历：为所有基因组生成一致的特征
        logger.info("第二次遍历：为所有基因组生成一致的特征...")
        for ko_file in ko_files:
            genome_id = ko_file.stem.split('_ko')[0]
            logger.info(f"处理{genome_id}...")

            # 计算聚类特征
            cluster_features = self.calculate_cluster_features_from_ko_file(ko_file)

            # 确保包含所有可能的聚类
            for cluster_name in self.__class__.all_pathway_clusters:
                if cluster_name not in cluster_features:
                    cluster_features[cluster_name] = 0.0

            # 如果存在，加载已有特征
            feature_file = output_dir_path / f"{genome_id}_cds_features.npz"
            if feature_file.exists():
                existing_features = dict(np.load(feature_file))

                # 添加聚类特征
                existing_features.update(cluster_features)

                # 保存更新后的特征
                np.savez(feature_file, **existing_features)
                logger.info(f"已向{feature_file}添加{len(cluster_features)}个通路聚类特征")
            else:
                logger.warning(f"未找到特征文件: {feature_file}")

        logger.info("通路聚类特征生成完成！")


def generate_pathway_cluster_features(
    ko_dir: str,
    kegg_path: str,
    output_dir: str,
    min_ko_per_pathway: int = 5,
    min_pathway_completeness: float = 0.1
) -> None:
    """
    为数据集中的所有基因组生成通路聚类特征。

    参数:
        ko_dir: 包含KO注释文件的目录
        kegg_path: KEGG通路映射文件路径
        output_dir: 保存特征文件的目录
        min_ko_per_pathway: 一个通路被考虑所需的最小KO术语数量
        min_pathway_completeness: 通路被包含的最小完整性阈值
    """
    # 初始化通路聚类计算器
    calculator = PathwayClusterCalculator(
        kegg_path=kegg_path,
        min_ko_per_pathway=min_ko_per_pathway,
        min_pathway_completeness=min_pathway_completeness
    )

    # 生成聚类特征
    calculator.generate_cluster_features_for_dataset(
        ko_dir=ko_dir,
        output_dir=output_dir
    )

    # 记录通路聚类特征数量
    logger.info(f"已生成{len(PathwayClusterCalculator.all_pathway_clusters)}个一致的通路聚类特征")
