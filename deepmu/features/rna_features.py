"""RNA Feature Calculator Module

This module provides functionality for calculating tRNA and rRNA features
from tRNA and rRNA annotation files generated by tRNAscan-SE and Barrnap.
These features include tRNA counts, tRNA Adaptation Index (tAI), and rRNA counts.
"""

import os
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Set, Tuple, Union
from Bio import SeqIO
from collections import Counter, defaultdict
import re

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RNAFeatureCalculator:
    """Calculator for tRNA and rRNA features."""

    def __init__(self, genetic_code: int = 11):
        """Initialize the RNA feature calculator.
        
        Args:
            genetic_code: Genetic code to use for codon translation (default: 11)
        """
        self.genetic_code = genetic_code
        self.nucleotides = ['A', 'C', 'G', 'T']
        self.codons = [n1 + n2 + n3 for n1 in self.nucleotides 
                      for n2 in self.nucleotides 
                      for n3 in self.nucleotides]
        
        # Define standard genetic code (can be modified based on genetic_code parameter)
        self.genetic_code_map = {
            # Standard genetic code (NCBI transl_table=11)
            'TTT': 'F', 'TTC': 'F', 'TTA': 'L', 'TTG': 'L',
            'CTT': 'L', 'CTC': 'L', 'CTA': 'L', 'CTG': 'L',
            'ATT': 'I', 'ATC': 'I', 'ATA': 'I', 'ATG': 'M',
            'GTT': 'V', 'GTC': 'V', 'GTA': 'V', 'GTG': 'V',
            'TCT': 'S', 'TCC': 'S', 'TCA': 'S', 'TCG': 'S',
            'CCT': 'P', 'CCC': 'P', 'CCA': 'P', 'CCG': 'P',
            'ACT': 'T', 'ACC': 'T', 'ACA': 'T', 'ACG': 'T',
            'GCT': 'A', 'GCC': 'A', 'GCA': 'A', 'GCG': 'A',
            'TAT': 'Y', 'TAC': 'Y', 'TAA': '*', 'TAG': '*',
            'CAT': 'H', 'CAC': 'H', 'CAA': 'Q', 'CAG': 'Q',
            'AAT': 'N', 'AAC': 'N', 'AAA': 'K', 'AAG': 'K',
            'GAT': 'D', 'GAC': 'D', 'GAA': 'E', 'GAG': 'E',
            'TGT': 'C', 'TGC': 'C', 'TGA': '*', 'TGG': 'W',
            'CGT': 'R', 'CGC': 'R', 'CGA': 'R', 'CGG': 'R',
            'AGT': 'S', 'AGC': 'S', 'AGA': 'R', 'AGG': 'R',
            'GGT': 'G', 'GGC': 'G', 'GGA': 'G', 'GGG': 'G'
        }
        
        # Define codon-anticodon pairing rules with s-values
        # Format: {codon: [(anticodon, s-value), ...]}
        self.codon_to_anticodons = self._initialize_codon_anticodon_map()

    def _initialize_codon_anticodon_map(self) -> Dict[str, List[Tuple[str, float]]]:
        """Initialize the codon-anticodon mapping with s-values.
        
        Returns:
            Dictionary mapping codons to list of (anticodon, s-value) tuples
        """
        # Create a mapping of codons to their possible anticodons with s-values
        # s-values represent the efficiency of the pairing (1.0 for perfect match, lower for wobble)
        codon_to_anticodons = {}
        
        # Define the wobble rules
        wobble_rules = {
            'A': [('U', 1.0)],                  # A pairs with U
            'C': [('G', 1.0)],                  # C pairs with G
            'G': [('C', 1.0), ('U', 0.5)],      # G pairs with C and U (wobble)
            'T': [('A', 1.0), ('G', 0.5)],      # T(U) pairs with A and G (wobble)
        }
        
        # Generate codon-anticodon mappings
        for codon in self.codons:
            if codon not in self.genetic_code_map or self.genetic_code_map[codon] == '*':
                continue  # Skip stop codons
                
            anticodons = []
            # The anticodon is the reverse complement of the codon, but with wobble rules
            # for the third position of the codon (first position of anticodon)
            base1, base2, base3 = codon
            
            # Get possible anticodon third positions based on wobble rules
            for anti_base3, s_value in wobble_rules.get(base3, []):
                # Construct the anticodon (reverse complement)
                if base1 == 'A': anti_base1 = 'U'
                elif base1 == 'C': anti_base1 = 'G'
                elif base1 == 'G': anti_base1 = 'C'
                elif base1 == 'T': anti_base1 = 'A'
                
                if base2 == 'A': anti_base2 = 'U'
                elif base2 == 'C': anti_base2 = 'G'
                elif base2 == 'G': anti_base2 = 'C'
                elif base2 == 'T': anti_base2 = 'A'
                
                anticodon = anti_base3 + anti_base2 + anti_base1
                anticodons.append((anticodon, s_value))
                
            codon_to_anticodons[codon] = anticodons
            
        return codon_to_anticodons

    def calculate_features(self, 
                          trna_file: Optional[str] = None, 
                          rrna_file: Optional[str] = None,
                          cds_file: Optional[str] = None) -> Dict[str, float]:
        """Calculate RNA features from tRNA and rRNA files.
        
        Args:
            trna_file: Path to tRNA file from tRNAscan-SE
            rrna_file: Path to rRNA file from Barrnap
            cds_file: Path to CDS file for tAI calculation
            
        Returns:
            Dictionary of RNA features
        """
        features = {}
        
        # Calculate tRNA features if tRNA file is provided
        if trna_file and os.path.exists(trna_file):
            trna_features = self.calculate_trna_features(trna_file)
            features.update(trna_features)
            
            # Calculate tRNA Adaptation Index if CDS file is provided
            if cds_file and os.path.exists(cds_file):
                tai_features = self.calculate_tai(trna_file, cds_file)
                features.update(tai_features)
        
        # Calculate rRNA features if rRNA file is provided
        if rrna_file and os.path.exists(rrna_file):
            rrna_features = self.calculate_rrna_features(rrna_file)
            features.update(rrna_features)
            
        return features
    
    def calculate_trna_features(self, trna_file: str) -> Dict[str, float]:
        """Calculate tRNA features from tRNAscan-SE output.
        
        Args:
            trna_file: Path to tRNA file from tRNAscan-SE
            
        Returns:
            Dictionary of tRNA features
        """
        logger.info(f"Calculating tRNA features from {trna_file}")
        
        features = {}
        
        try:
            # Parse tRNA file
            trna_data = self._parse_trna_file(trna_file)
            
            if not trna_data:
                logger.warning(f"No tRNA data found in {trna_file}")
                return {'trna_count': 0}
            
            # Count total tRNAs
            features['trna_count'] = len(trna_data)
            
            # Count tRNAs by type
            trna_types = Counter([trna['tRNA_type'] for trna in trna_data])
            for aa, count in trna_types.items():
                features[f'trna_{aa.lower()}_count'] = count
                
            # Count tRNAs by anticodon
            anticodon_counts = Counter([trna['anticodon'] for trna in trna_data])
            for anticodon, count in anticodon_counts.items():
                features[f'trna_anticodon_{anticodon.lower()}_count'] = count
                
            # Calculate tRNA diversity (number of unique anticodons)
            features['trna_anticodon_diversity'] = len(anticodon_counts)
            
            # Calculate average tRNA score
            if trna_data and 'score' in trna_data[0]:
                scores = [trna['score'] for trna in trna_data if 'score' in trna]
                if scores:
                    features['trna_avg_score'] = np.mean(scores)
            
            return features
            
        except Exception as e:
            logger.error(f"Error calculating tRNA features: {e}")
            return {'trna_count': 0}
    
    def calculate_rrna_features(self, rrna_file: str) -> Dict[str, float]:
        """Calculate rRNA features from Barrnap output.
        
        Args:
            rrna_file: Path to rRNA file from Barrnap
            
        Returns:
            Dictionary of rRNA features
        """
        logger.info(f"Calculating rRNA features from {rrna_file}")
        
        features = {}
        
        try:
            # Parse rRNA file
            rrna_counts = self._parse_rrna_file(rrna_file)
            
            if not rrna_counts:
                logger.warning(f"No rRNA data found in {rrna_file}")
                return {'rrna_count': 0}
            
            # Add rRNA counts to features
            features['rrna_count'] = sum(rrna_counts.values())
            
            # Add individual rRNA type counts
            for rrna_type, count in rrna_counts.items():
                features[f'rrna_{rrna_type}_count'] = count
                
            # Calculate rRNA diversity (number of unique rRNA types)
            features['rrna_type_diversity'] = len(rrna_counts)
            
            return features
            
        except Exception as e:
            logger.error(f"Error calculating rRNA features: {e}")
            return {'rrna_count': 0}
    
    def calculate_tai(self, trna_file: str, cds_file: str) -> Dict[str, float]:
        """Calculate tRNA Adaptation Index (tAI).
        
        Args:
            trna_file: Path to tRNA file from tRNAscan-SE
            cds_file: Path to CDS file
            
        Returns:
            Dictionary with tAI features
        """
        logger.info(f"Calculating tRNA Adaptation Index from {trna_file} and {cds_file}")
        
        features = {}
        
        try:
            # Parse tRNA file to get tRNA counts by anticodon
            trna_data = self._parse_trna_file(trna_file)
            
            if not trna_data:
                logger.warning(f"No tRNA data found in {trna_file}")
                return {'tai': 0.0}
            
            # Count tRNAs by anticodon
            trna_counts = Counter([trna['anticodon'] for trna in trna_data])
            
            # Calculate effective tRNA counts for each codon
            effective_counts = self._calculate_effective_counts(trna_counts)
            
            # Calculate relative adaptiveness (w) values
            w_values = self._calculate_relative_adaptiveness(effective_counts)
            
            # Parse CDS file
            cds_sequences = []
            with open(cds_file, 'r') as f:
                for record in SeqIO.parse(f, 'fasta'):
                    cds_sequences.append(str(record.seq).upper())
            
            if not cds_sequences:
                logger.warning(f"No CDS sequences found in {cds_file}")
                return {'tai': 0.0}
            
            # Calculate tAI for each gene
            tai_values = []
            for seq in cds_sequences:
                tai = self._calculate_tai_for_gene(seq, w_values)
                if tai > 0:  # Skip genes with invalid tAI
                    tai_values.append(tai)
            
            if not tai_values:
                logger.warning("No valid tAI values calculated")
                return {'tai': 0.0}
            
            # Calculate genome-wide tAI statistics
            features['tai'] = np.mean(tai_values)
            features['tai_median'] = np.median(tai_values)
            features['tai_std'] = np.std(tai_values)
            features['tai_min'] = np.min(tai_values)
            features['tai_max'] = np.max(tai_values)
            
            return features
            
        except Exception as e:
            logger.error(f"Error calculating tAI: {e}")
            return {'tai': 0.0}
    
    def _parse_trna_file(self, trna_file: str) -> List[Dict]:
        """Parse tRNAscan-SE output file.
        
        Args:
            trna_file: Path to tRNA file from tRNAscan-SE
            
        Returns:
            List of dictionaries with tRNA information
        """
        trna_data = []
        
        try:
            with open(trna_file, 'r') as f:
                # Skip header lines (first 3 lines)
                for _ in range(3):
                    next(f, None)
                
                # Parse data lines
                for line in f:
                    line = line.strip()
                    if not line or line.startswith("--------"):
                        continue  # Skip empty or separator lines
                    
                    try:
                        parts = line.split()
                        if len(parts) >= 8:  # At least 8 columns in the output
                            # Handle the case where the last column is a note (e.g., 'pseudo')
                            note = parts[8] if len(parts) > 8 else None
                            
                            trna = {
                                "sequence_name": parts[0],
                                "tRNA_number": int(parts[1]),
                                "start": int(parts[2]),
                                "end": int(parts[3]),
                                "tRNA_type": parts[4],
                                "anticodon": parts[5],
                                "intron_begin": int(parts[6]),
                                "intron_end": int(parts[7]),
                                "score": float(parts[8]) if len(parts) > 8 and parts[8] != "pseudo" else 0.0,
                                "note": note
                            }
                            trna_data.append(trna)
                    except (ValueError, IndexError) as e:
                        # Log the error but continue parsing
                        logger.warning(f"Error parsing tRNA line: {line}. Error: {str(e)}")
                        continue
            
            return trna_data
            
        except Exception as e:
            logger.error(f"Error parsing tRNA file {trna_file}: {e}")
            return []
    
    def _parse_rrna_file(self, rrna_file: str) -> Dict[str, int]:
        """Parse Barrnap output file.
        
        Args:
            rrna_file: Path to rRNA file from Barrnap
            
        Returns:
            Dictionary with rRNA type counts
        """
        # Initialize counts
        rrna_counts = {
            '5S': 0,
            '16S': 0,
            '23S': 0,
            '12S': 0,
            'other': 0
        }
        
        try:
            # Check if the file is a GFF file
            if rrna_file.endswith('.gff'):
                with open(rrna_file, 'r') as f:
                    for line in f:
                        if line.startswith('#'):
                            continue
                        
                        parts = line.strip().split('\t')
                        if len(parts) >= 9:
                            attributes = parts[8]
                            
                            # Extract rRNA type
                            match = re.search(r'Name=([^;]+)', attributes)
                            if match:
                                rrna_type = match.group(1)
                                
                                if '5S' in rrna_type:
                                    rrna_counts['5S'] += 1
                                elif '16S' in rrna_type:
                                    rrna_counts['16S'] += 1
                                elif '23S' in rrna_type:
                                    rrna_counts['23S'] += 1
                                elif '12S' in rrna_type:
                                    rrna_counts['12S'] += 1
                                else:
                                    rrna_counts['other'] += 1
            
            # Check if the file is a FASTA file
            elif rrna_file.endswith('.fasta') or rrna_file.endswith('.fa'):
                with open(rrna_file, 'r') as f:
                    for record in SeqIO.parse(f, 'fasta'):
                        header = record.description
                        
                        if '5S_rRNA' in header:
                            rrna_counts['5S'] += 1
                        elif '16S_rRNA' in header:
                            rrna_counts['16S'] += 1
                        elif '23S_rRNA' in header:
                            rrna_counts['23S'] += 1
                        elif '12S_rRNA' in header:
                            rrna_counts['12S'] += 1
                        else:
                            rrna_counts['other'] += 1
            
            return rrna_counts
            
        except Exception as e:
            logger.error(f"Error parsing rRNA file {rrna_file}: {e}")
            return {'5S': 0, '16S': 0, '23S': 0, '12S': 0, 'other': 0}
    
    def _calculate_effective_counts(self, trna_counts: Dict[str, int]) -> Dict[str, float]:
        """Calculate effective tRNA counts for each codon.
        
        Args:
            trna_counts: Dictionary mapping anticodons to their counts
            
        Returns:
            Dictionary mapping codons to their effective tRNA counts
        """
        effective_counts = defaultdict(float)
        
        for codon, anticodons in self.codon_to_anticodons.items():
            for anticodon, s_value in anticodons:
                effective_counts[codon] += trna_counts.get(anticodon, 0) * s_value
        
        return effective_counts
    
    def _calculate_relative_adaptiveness(self, effective_counts: Dict[str, float]) -> Dict[str, float]:
        """Calculate relative adaptiveness (w) values.
        
        Args:
            effective_counts: Dictionary mapping codons to their effective tRNA counts
            
        Returns:
            Dictionary mapping codons to their relative adaptiveness values
        """
        # Group codons by amino acid
        aa_to_codons = defaultdict(list)
        for codon in effective_counts:
            if codon in self.genetic_code_map:
                aa = self.genetic_code_map[codon]
                if aa != '*':  # Skip stop codons
                    aa_to_codons[aa].append(codon)
        
        # Calculate relative adaptiveness
        w_values = {}
        for aa, codons in aa_to_codons.items():
            max_eff = max(effective_counts[c] for c in codons)
            for c in codons:
                w_values[c] = effective_counts[c] / max_eff if max_eff != 0 else 0.0
        
        return w_values
    
    def _calculate_tai_for_gene(self, gene_sequence: str, w_values: Dict[str, float]) -> float:
        """Calculate tAI for a gene.
        
        Args:
            gene_sequence: DNA sequence of the gene
            w_values: Dictionary mapping codons to their relative adaptiveness values
            
        Returns:
            tAI value for the gene
        """
        import math
        
        # Split sequence into codons
        codons = [gene_sequence[i:i+3] for i in range(0, len(gene_sequence), 3)]
        
        # Filter valid codons and get their w values
        valid_ws = []
        for codon in codons:
            if len(codon) == 3 and codon in w_values:
                w = w_values[codon]
                if w > 0:  # Avoid log(0)
                    valid_ws.append(w)
        
        if not valid_ws:
            return 0.0
        
        # Calculate geometric mean
        log_sum = sum(math.log(w) for w in valid_ws)
        return math.exp(log_sum / len(valid_ws))


def extract_rna_features(
    genome_id: str,
    trna_file: Optional[str] = None,
    rrna_file: Optional[str] = None,
    cds_file: Optional[str] = None,
    genetic_code: int = 11
) -> Dict[str, float]:
    """Extract RNA features for a genome.
    
    Args:
        genome_id: Genome ID
        trna_file: Path to tRNA file from tRNAscan-SE
        rrna_file: Path to rRNA file from Barrnap
        cds_file: Path to CDS file for tAI calculation
        genetic_code: Genetic code to use for codon translation
        
    Returns:
        Dictionary of RNA features
    """
    try:
        # Initialize feature calculator
        rna_calculator = RNAFeatureCalculator(genetic_code=genetic_code)
        
        # Calculate features
        return rna_calculator.calculate_features(trna_file, rrna_file, cds_file)
        
    except Exception as e:
        logger.error(f"Error extracting RNA features for {genome_id}: {e}")
        return {}


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Extract RNA features from tRNA and rRNA files")
    parser.add_argument("--genome-id", required=True, help="Genome ID")
    parser.add_argument("--trna-file", help="Path to tRNA file from tRNAscan-SE")
    parser.add_argument("--rrna-file", help="Path to rRNA file from Barrnap")
    parser.add_argument("--cds-file", help="Path to CDS file for tAI calculation")
    parser.add_argument("--genetic-code", type=int, default=11, help="Genetic code to use for codon translation")
    parser.add_argument("--output-file", help="Path to output file")
    
    args = parser.parse_args()
    
    # Extract features
    features = extract_rna_features(
        args.genome_id,
        args.trna_file,
        args.rrna_file,
        args.cds_file,
        args.genetic_code
    )
    
    # Print features
    for key, value in sorted(features.items()):
        print(f"{key}: {value}")
    
    # Save features if output file is specified
    if args.output_file:
        np.savez(args.output_file, **features)
        print(f"Features saved to {args.output_file}")
