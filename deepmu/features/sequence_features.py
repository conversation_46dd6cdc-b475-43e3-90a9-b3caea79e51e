"""Sequence Feature Calculator Module

This module provides functionality for calculating sequence-based features
from coding sequences, including GC content, sequence length, and other
relevant metrics for microbial growth prediction.
"""

import numpy as np
from typing import Dict

class SequenceFeatureCalculator:
    """Calculator for sequence-based features."""

    def __init__(self):
        """Initialize the sequence feature calculator."""
        pass

    def calculate_features(self, sequence: str) -> Dict[str, np.ndarray]:
        """Calculate sequence features from a coding sequence.

        Args:
            sequence: DNA sequence string

        Returns:
            Dictionary of sequence features
        """
        if not sequence:
            return {}

        # Calculate GC content
        gc_content = self._calculate_gc_content(sequence)

        # Calculate sequence length
        seq_length = len(sequence)

        # Calculate dinucleotide frequencies
        dinucleotide_freqs = self._calculate_dinucleotide_freqs(sequence)

        # Combine all features
        features = {
            'gc_content': gc_content,
            'seq_length': seq_length,
        }

        # Add dinucleotide frequencies
        for dinucleotide, freq in dinucleotide_freqs.items():
            features[f'dinucleotide_{dinucleotide}'] = freq

        return features

    def _calculate_gc_content(self, sequence: str) -> float:
        """Calculate GC content of a sequence.

        Args:
            sequence: DNA sequence

        Returns:
            GC content as a fraction
        """
        sequence = sequence.upper()
        gc_count = sequence.count('G') + sequence.count('C')
        total = len(sequence)

        if total == 0:
            return 0.0

        return gc_count / total

    def _calculate_dinucleotide_freqs(self, sequence: str) -> Dict[str, float]:
        """Calculate dinucleotide frequencies in a sequence.

        Args:
            sequence: DNA sequence

        Returns:
            Dictionary mapping dinucleotides to their frequencies
        """
        nucleotides = ['A', 'C', 'G', 'T']
        dinucleotides = [n1 + n2 for n1 in nucleotides for n2 in nucleotides]

        # Initialize counts
        dinucleotide_counts = {dinuc: 0 for dinuc in dinucleotides}
        total_count = 0

        # Count dinucleotides
        sequence = sequence.upper()
        for i in range(len(sequence) - 1):
            dinuc = sequence[i:i+2]
            if dinuc in dinucleotide_counts:
                dinucleotide_counts[dinuc] += 1
                total_count += 1

        # Calculate frequencies
        dinucleotide_freqs = {}
        if total_count > 0:
            for dinuc, count in dinucleotide_counts.items():
                dinucleotide_freqs[dinuc] = count / total_count
        else:
            for dinuc in dinucleotides:
                dinucleotide_freqs[dinuc] = 0.0

        return dinucleotide_freqs
