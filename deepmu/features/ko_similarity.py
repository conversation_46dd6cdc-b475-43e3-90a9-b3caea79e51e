"""KO similarity calculator module.

This module provides functionality for calculating similarity between KO (KEGG Orthology)
profiles of different organisms.
"""

import numpy as np
from typing import Dict, List, Set, Optional, Union, Tuple
from pathlib import Path
from scipy.sparse import csr_matrix


class KOSimilarityCalculator:
    """Enhanced calculator for KO profile similarity features.

    This class provides methods for calculating similarity between KO profiles
    using weighted Jaccard similarity and other advanced metrics. It also includes
    feature selection capabilities to identify the most important KO terms.
    """

    def __init__(self, ko_vocab_size: int = 10000, use_weighted_similarity: bool = True):
        """Initialize the KO similarity calculator.

        Args:
            ko_vocab_size: Size of the KO vocabulary (default: 10000)
                           If None or 0, no limit is applied
            use_weighted_similarity: Whether to use weighted Jaccard similarity (default: True)
        """
        self.ko_vocab_size = ko_vocab_size if ko_vocab_size and ko_vocab_size > 0 else None
        self.ko_to_idx: Dict[str, int] = {}
        self.idx_to_ko: Dict[int, str] = {}
        self.next_idx = 0
        self.use_weighted_similarity = use_weighted_similarity

        # For backward compatibility with phylo.py implementation
        self.ko_index = self.ko_to_idx
        self.current_idx = self.next_idx

        # KO importance weights (will be populated based on data)
        self.ko_weights: Dict[str, float] = {}

        # Default weights for different KO categories
        self.category_weights = {
            # Essential genes get higher weights
            'essential': 2.0,
            # Metabolism-related KOs
            'metabolism': 1.5,
            # Information processing
            'information_processing': 1.3,
            # Default weight for other KOs
            'default': 1.0
        }

        # Map of KO categories (will be populated from KEGG data if available)
        self.ko_categories: Dict[str, str] = {}

    def _get_ko_idx(self, ko_id: str) -> int:
        """Get the index for a KO ID, creating a new one if not exists.

        Args:
            ko_id: The KO identifier (e.g., 'K00001')

        Returns:
            int: Index for the KO ID
        """
        if ko_id not in self.ko_to_idx:
            if self.ko_vocab_size and self.next_idx >= self.ko_vocab_size:
                raise ValueError(f"KO vocabulary size exceeded: {self.ko_vocab_size}")
            self.ko_to_idx[ko_id] = self.next_idx
            self.idx_to_ko[self.next_idx] = ko_id
            self.next_idx += 1
        return self.ko_to_idx[ko_id]

    # Alias for backward compatibility with phylo.py implementation
    def _get_index(self, ko: str) -> int:
        """Alias for _get_ko_idx for backward compatibility.

        Args:
            ko: KO ID (e.g., "K00001")

        Returns:
            Index for the KO ID
        """
        return self._get_ko_idx(ko)

    def _get_ko_weight(self, ko_id: str) -> float:
        """Get the importance weight for a KO ID.

        Args:
            ko_id: The KO identifier (e.g., 'K00001')

        Returns:
            float: Importance weight for the KO ID
        """
        # If we have a specific weight for this KO, use it
        if ko_id in self.ko_weights:
            return self.ko_weights[ko_id]

        # If we know the category, use the category weight
        if ko_id in self.ko_categories:
            category = self.ko_categories[ko_id]
            if category in self.category_weights:
                return self.category_weights[category]

        # Default weight
        return self.category_weights['default']

    def calculate_features(self, ko_file: str) -> np.ndarray:
        """Calculate KO profile features from a KO annotation file.

        Args:
            ko_file: Path to the KO annotation file (TSV format: sequence_id\tko_id)

        Returns:
            np.ndarray: KO profile features
        """
        # Initialize KO profile vector
        max_size = self.ko_vocab_size if self.ko_vocab_size else 100000
        ko_profile = np.zeros(max_size, dtype=np.float32)

        # Read KO annotations
        ko_counts: Dict[str, int] = {}
        with open(ko_file, 'r') as f:
            for line in f:
                if line.strip():
                    parts = line.strip().split('\t')
                    if len(parts) >= 2:
                        ko_id = parts[1]
                        ko_counts[ko_id] = ko_counts.get(ko_id, 0) + 1

        # Convert counts to frequencies
        total_kos = sum(ko_counts.values())
        if total_kos > 0:
            for ko_id, count in ko_counts.items():
                try:
                    idx = self._get_ko_idx(ko_id)
                    ko_profile[idx] = count / total_kos
                except ValueError:
                    # Skip if vocabulary size is exceeded
                    continue

        # Trim to actual size used
        if not self.ko_vocab_size:
            ko_profile = ko_profile[:self.next_idx]

        return ko_profile

    def parse_ko_file(self, ko_file: str) -> Set[str]:
        """Parse a KO annotation file and return a set of KO IDs.

        Args:
            ko_file: Path to the KO annotation file

        Returns:
            Set[str]: Set of KO IDs
        """
        ko_set = set()
        try:
            with open(ko_file, 'r') as f:
                for line in f:
                    if line.strip() and not line.startswith('#'):
                        parts = line.strip().split('\t')
                        if len(parts) >= 2:
                            ko_id = parts[1]
                            ko_set.add(ko_id)
        except Exception as e:
            import logging
            logging.getLogger(__name__).warning(f"Error parsing KO file {ko_file}: {e}")

        return ko_set

    def calculate_similarity(self, profile1: np.ndarray, profile2: np.ndarray) -> float:
        """Calculate similarity between two KO profiles.

        Args:
            profile1: First KO profile
            profile2: Second KO profile

        Returns:
            float: Similarity score between 0 and 1
        """
        # Calculate Jaccard similarity
        intersection = np.minimum(profile1, profile2).sum()
        union = np.maximum(profile1, profile2).sum()

        if union == 0:
            return 0.0

        return float(intersection / union)

    def weighted_jaccard_similarity(self, ko_set1: Set[str], ko_set2: Set[str], ko_weights: Optional[Dict[str, float]] = None) -> float:
        """Calculate weighted Jaccard similarity between two KO sets.

        This method uses KO importance weights to calculate a weighted version
        of the Jaccard similarity index, giving more importance to essential
        and functionally significant KO terms.

        Args:
            ko_set1: First set of KO IDs
            ko_set2: Second set of KO IDs
            ko_weights: Optional dictionary mapping KO IDs to weights
                        If not provided, internal weights will be used

        Returns:
            float: Weighted Jaccard similarity (0-1)
        """
        if not ko_set1 or not ko_set2:
            return 0.0

        # Calculate intersection and union
        intersection = ko_set1.intersection(ko_set2)
        union = ko_set1.union(ko_set2)

        if not union:
            return 0.0

        # If not using weighted similarity, use standard Jaccard
        if not self.use_weighted_similarity:
            return len(intersection) / len(union)

        # Use provided weights if available, otherwise use internal weights via _get_ko_weight
        if ko_weights is not None:
            intersection_weight = sum(ko_weights.get(ko, 1.0) for ko in intersection)
            union_weight = sum(ko_weights.get(ko, 1.0) for ko in union)
        else:
            intersection_weight = sum(self._get_ko_weight(ko) for ko in intersection)
            union_weight = sum(self._get_ko_weight(ko) for ko in union)

        if union_weight == 0:
            return 0.0

        return intersection_weight / union_weight

    def jaccard_similarity(self, ko_set1: Set[str], ko_set2: Set[str]) -> float:
        """Calculate standard Jaccard similarity between two KO sets.

        Args:
            ko_set1: First set of KO IDs
            ko_set2: Second set of KO IDs

        Returns:
            float: Jaccard similarity (0-1)
        """
        if not ko_set1 or not ko_set2:
            return 0.0

        intersection = len(ko_set1.intersection(ko_set2))
        union = len(ko_set1.union(ko_set2))

        if union == 0:
            return 0.0

        return intersection / union

    def pairwise_similarity_matrix(self, ko_sets: List[Set[str]], ko_weights: Optional[Dict[str, float]] = None) -> np.ndarray:
        """Calculate pairwise similarity matrix between KO sets.

        Args:
            ko_sets: List of KO ID sets for each genome
            ko_weights: Optional dictionary mapping KO IDs to weights
                        If not provided, internal weights will be used

        Returns:
            np.ndarray: N x N similarity matrix where N is the number of genomes
        """
        n = len(ko_sets)
        sim_matrix = np.zeros((n, n))

        for i in range(n):
            # Diagonal is 1.0 (self-similarity)
            sim_matrix[i, i] = 1.0

            # Calculate similarity with other sets
            for j in range(i+1, n):
                if self.use_weighted_similarity:
                    sim = self.weighted_jaccard_similarity(ko_sets[i], ko_sets[j], ko_weights)
                else:
                    sim = self.jaccard_similarity(ko_sets[i], ko_sets[j])

                sim_matrix[i, j] = sim
                sim_matrix[j, i] = sim  # Matrix is symmetric

        return sim_matrix

    def to_sparse_matrix(self, ko_sets: List[Set[str]]) -> csr_matrix:
        """Convert KO sets to a sparse matrix representation.

        Args:
            ko_sets: List of KO ID sets for each genome

        Returns:
            csr_matrix: Sparse matrix where rows are genomes and columns are KO terms
        """
        # Get all unique KO IDs
        all_kos = set()
        for ko_set in ko_sets:
            all_kos.update(ko_set)

        # Create mapping from KO ID to column index
        ko_to_col = {ko: i for i, ko in enumerate(sorted(all_kos))}

        # Create sparse matrix
        rows = []
        cols = []
        data = []

        for i, ko_set in enumerate(ko_sets):
            for ko in ko_set:
                rows.append(i)
                cols.append(ko_to_col[ko])
                data.append(1)  # Binary presence/absence

        return csr_matrix((data, (rows, cols)), shape=(len(ko_sets), len(ko_to_col)))

    def get_ko_ids(self) -> Set[str]:
        """Get the set of known KO IDs.

        Returns:
            Set[str]: Set of KO IDs
        """
        return set(self.ko_to_idx.keys())

    def get_vocab_size(self) -> int:
        """Get the current vocabulary size.

        Returns:
            int: Number of unique KO IDs seen
        """
        return self.next_idx

    def select_important_features(self, ko_sets: List[Set[str]], targets: np.ndarray, k: int = 100) -> List[str]:
        """Select the most important KO features based on correlation with targets.

        Args:
            ko_sets: List of KO ID sets for each genome
            targets: Target values (e.g., growth rates)
            k: Number of features to select

        Returns:
            List[str]: List of the k most important KO IDs
        """
        try:
            from sklearn.feature_selection import SelectKBest, f_regression

            # Convert KO sets to sparse matrix
            X = self.to_sparse_matrix(ko_sets).toarray()

            # Apply feature selection
            selector = SelectKBest(f_regression, k=min(k, X.shape[1]))
            selector.fit(X, targets)

            # Get selected feature indices
            selected_indices = selector.get_support(indices=True)

            # Map back to KO IDs
            all_kos = sorted(set().union(*ko_sets))
            selected_kos = [all_kos[i] for i in selected_indices]

            # Update KO weights based on feature importance
            scores = selector.scores_
            if scores is not None:
                max_score = np.max(scores) if scores.size > 0 else 1.0
                for i, ko in enumerate(all_kos):
                    if i < len(scores):
                        # Normalize score to range [1.0, 2.0]
                        normalized_score = 1.0 + (scores[i] / max_score) if max_score > 0 else 1.0
                        self.ko_weights[ko] = normalized_score

            return selected_kos

        except Exception as e:
            import logging
            logging.getLogger(__name__).warning(f"Error in feature selection: {e}")
            return list(set().union(*ko_sets))[:k] if ko_sets else []
            
    # Properties to maintain compatibility with both interfaces
    @property
    def ko_vocab_size(self) -> Optional[int]:
        """Get the KO vocabulary size."""
        return self._ko_vocab_size
        
    @ko_vocab_size.setter
    def ko_vocab_size(self, value: Optional[int]):
        """Set the KO vocabulary size."""
        self._ko_vocab_size = value
