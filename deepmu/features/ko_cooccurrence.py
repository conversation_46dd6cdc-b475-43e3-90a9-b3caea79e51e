"""
Co-occurrence-based KO grouping for DeepMu.

This module provides functions for grouping KO terms based on their co-occurrence
patterns across genomes, creating more robust KO-derived features.
"""

import numpy as np
import logging
from typing import Dict, List, Tuple
from pathlib import Path
from collections import defaultdict
from sklearn.cluster import AgglomerativeClustering
from sklearn.metrics import pairwise_distances

# Setup logging
logger = logging.getLogger(__name__)

class KOCooccurrenceCalculator:
    """
    Calculator for co-occurrence-based KO grouping.

    This class analyzes the co-occurrence patterns of KO terms across genomes
    and groups them into functionally related clusters.
    """

    def __init__(
        self,
        ko_dir: str,
        n_clusters: int = 50,
        min_occurrence: int = 5,
        distance_metric: str = 'jaccard'
    ):
        """
        Initialize the KO co-occurrence calculator.

        Args:
            ko_dir: Directory containing KO annotation files
            n_clusters: Number of KO clusters to create
            min_occurrence: Minimum number of genomes a KO must appear in to be included
            distance_metric: Distance metric for clustering ('jaccard', 'cosine', etc.)
        """
        self.ko_dir = Path(ko_dir)
        self.n_clusters = n_clusters
        self.min_occurrence = min_occurrence
        self.distance_metric = distance_metric

        # Initialize data structures
        self.ko_presence = defaultdict(set)  # KO -> set of genomes
        self.genome_kos = defaultdict(set)   # Genome -> set of KOs
        self.ko_clusters = {}                # KO -> cluster ID
        self.cluster_kos = defaultdict(set)  # Cluster ID -> set of KOs

        # Load KO data
        self._load_ko_data()

        # Filter KOs by occurrence
        self._filter_kos()

        # Create KO co-occurrence matrix
        self.ko_matrix, self.ko_list = self._create_ko_matrix()

        # Cluster KOs
        self._cluster_kos()

        logger.info(f"Initialized KOCooccurrenceCalculator with {len(self.ko_list)} KOs and {len(self.cluster_kos)} clusters")

    def _load_ko_data(self) -> None:
        """
        Load KO data from annotation files.
        """
        ko_files = list(self.ko_dir.glob("*.tsv"))
        logger.info(f"Loading KO data from {len(ko_files)} files in {self.ko_dir}")

        for ko_file in ko_files:
            genome_id = ko_file.stem.split('_ko')[0]

            try:
                # Read KO file
                with open(ko_file, 'r') as f:
                    for line in f:
                        parts = line.strip().split('\t')
                        if len(parts) >= 2:
                            ko_id = parts[1]

                            # Add KO to genome and genome to KO
                            self.ko_presence[ko_id].add(genome_id)
                            self.genome_kos[genome_id].add(ko_id)
            except Exception as e:
                logger.warning(f"Error reading KO file {ko_file}: {e}")

        logger.info(f"Loaded {len(self.ko_presence)} KOs across {len(self.genome_kos)} genomes")

    def _filter_kos(self) -> None:
        """
        Filter KOs by occurrence.
        """
        # Count KO occurrences
        ko_counts = {ko: len(genomes) for ko, genomes in self.ko_presence.items()}

        # Filter KOs by occurrence
        filtered_kos = {ko for ko, count in ko_counts.items() if count >= self.min_occurrence}

        # Update KO presence
        self.ko_presence = {ko: genomes for ko, genomes in self.ko_presence.items() if ko in filtered_kos}

        logger.info(f"Filtered KOs by occurrence: {len(filtered_kos)} KOs remain (min occurrence: {self.min_occurrence})")

    def _create_ko_matrix(self) -> Tuple[np.ndarray, List[str]]:
        """
        Create KO co-occurrence matrix.

        Returns:
            Tuple of (KO matrix, KO list)
        """
        # Create list of KOs
        ko_list = sorted(self.ko_presence.keys())

        # Create genome list
        genome_list = sorted(self.genome_kos.keys())

        # Create KO matrix (KOs x genomes)
        ko_matrix = np.zeros((len(ko_list), len(genome_list)), dtype=np.int8)

        # Fill KO matrix
        for i, ko in enumerate(ko_list):
            for j, genome in enumerate(genome_list):
                if ko in self.genome_kos[genome]:
                    ko_matrix[i, j] = 1

        logger.info(f"Created KO matrix of shape {ko_matrix.shape}")

        return ko_matrix, ko_list

    def _cluster_kos(self) -> None:
        """
        Cluster KOs based on co-occurrence patterns.
        """
        try:
            # Try using newer scikit-learn API
            # Calculate distance matrix
            distance_matrix = pairwise_distances(self.ko_matrix, metric=self.distance_metric)

            # Perform hierarchical clustering
            clustering = AgglomerativeClustering(
                n_clusters=min(self.n_clusters, len(self.ko_list)),
                metric='precomputed',  # Use 'metric' instead of 'affinity'
                linkage='average'
            )

            # Fit clustering
            cluster_labels = clustering.fit_predict(distance_matrix)

        except (TypeError, ValueError) as e:
            logger.warning(f"Error with precomputed distance matrix: {e}")
            logger.info("Falling back to direct clustering with Euclidean distance")

            # Fall back to direct clustering with Euclidean distance
            clustering = AgglomerativeClustering(
                n_clusters=min(self.n_clusters, len(self.ko_list)),
                linkage='average'
            )

            # Fit clustering
            cluster_labels = clustering.fit_predict(self.ko_matrix)

        # Map KOs to clusters
        for i, ko in enumerate(self.ko_list):
            cluster_id = cluster_labels[i]
            self.ko_clusters[ko] = cluster_id
            self.cluster_kos[cluster_id].add(ko)

        logger.info(f"Clustered KOs into {len(self.cluster_kos)} clusters")

        # Log cluster sizes
        cluster_sizes = [len(kos) for kos in self.cluster_kos.values()]
        logger.info(f"Cluster sizes: min={min(cluster_sizes)}, max={max(cluster_sizes)}, avg={np.mean(cluster_sizes):.2f}")

    def calculate_ko_group_features(self, ko_terms: List[str]) -> Dict[str, float]:
        """
        Calculate KO group features for a set of KO terms.

        Args:
            ko_terms: List of KO terms annotated in a genome

        Returns:
            Dictionary mapping cluster names to completeness scores
        """
        # Convert KO terms to a set for faster lookups
        ko_set = set(ko_terms)

        # Calculate completeness for each cluster
        cluster_features = {}

        for cluster_id, cluster_kos in self.cluster_kos.items():
            if cluster_kos:
                # Calculate completeness as proportion of KO terms present
                completeness = len(ko_set.intersection(cluster_kos)) / len(cluster_kos)
                cluster_features[f"ko_cluster_{cluster_id}"] = completeness

        return cluster_features

    def calculate_ko_group_features_from_file(self, ko_file_path: str) -> Dict[str, float]:
        """
        Calculate KO group features from a file containing KO terms.

        Args:
            ko_file_path: Path to file containing KO terms (one per line or in tabular format)

        Returns:
            Dictionary mapping cluster names to completeness scores
        """
        ko_terms = []
        try:
            with open(ko_file_path, 'r') as f:
                for line in f:
                    # Handle different file formats
                    parts = line.strip().split('\t')
                    if len(parts) >= 2:
                        ko_terms.append(parts[1])
        except Exception as e:
            logger.error(f"Failed to read KO file: {e}")
            return {}

        return self.calculate_ko_group_features(ko_terms)

    def generate_ko_group_features_for_dataset(
        self,
        ko_dir: str,
        output_dir: str
    ) -> None:
        """
        Generate KO group features for all genomes in a dataset.

        Args:
            ko_dir: Directory containing KO annotation files
            output_dir: Directory to save feature files
        """
        ko_dir_path = Path(ko_dir)
        output_dir_path = Path(output_dir)
        output_dir_path.mkdir(exist_ok=True, parents=True)

        # Process each KO file
        ko_files = list(ko_dir_path.glob("*.tsv"))
        logger.info(f"Processing {len(ko_files)} KO files from {ko_dir}")

        # Get all possible cluster features
        all_cluster_features = {f"ko_cluster_{cluster_id}": 0.0 for cluster_id in self.cluster_kos.keys()}

        for ko_file in ko_files:
            genome_id = ko_file.stem.split('_ko')[0]
            logger.info(f"Processing {genome_id}...")

            # Calculate cluster features
            cluster_features = self.calculate_ko_group_features_from_file(ko_file)

            # Ensure all clusters are included
            for cluster_name in all_cluster_features.keys():
                if cluster_name not in cluster_features:
                    cluster_features[cluster_name] = 0.0

            # Load existing features if available
            feature_file = output_dir_path / f"{genome_id}_cds_features.npz"
            if feature_file.exists():
                existing_features = dict(np.load(feature_file))

                # Add cluster features
                existing_features.update(cluster_features)

                # Save updated features
                np.savez(feature_file, **existing_features)
                logger.info(f"Added {len(cluster_features)} KO cluster features to {feature_file}")
            else:
                logger.warning(f"Feature file not found: {feature_file}")

        logger.info("KO cluster feature generation complete!")


def generate_ko_cooccurrence_features(
    ko_dir: str,
    output_dir: str,
    n_clusters: int = 50,
    min_occurrence: int = 5,
    distance_metric: str = 'jaccard'
) -> None:
    """
    Generate co-occurrence-based KO group features for all genomes in a dataset.

    Args:
        ko_dir: Directory containing KO annotation files
        output_dir: Directory to save feature files
        n_clusters: Number of KO clusters to create
        min_occurrence: Minimum number of genomes a KO must appear in to be included
        distance_metric: Distance metric for clustering ('jaccard', 'cosine', etc.)
    """
    # Initialize KO co-occurrence calculator
    calculator = KOCooccurrenceCalculator(
        ko_dir=ko_dir,
        n_clusters=n_clusters,
        min_occurrence=min_occurrence,
        distance_metric=distance_metric
    )

    # Generate KO group features
    calculator.generate_ko_group_features_for_dataset(
        ko_dir=ko_dir,
        output_dir=output_dir
    )

    logger.info(f"Generated {len(calculator.cluster_kos)} KO cluster features")


def enhance_codon_adaptation_features(
    feature_dir: str,
    output_dir: str = None
) -> None:
    """
    Enhance the representation of codon adaptation features.

    Args:
        feature_dir: Directory containing feature files
        output_dir: Directory to save enhanced feature files (if None, uses feature_dir)
    """
    # Set output directory
    if output_dir is None:
        output_dir = feature_dir

    output_dir_path = Path(output_dir)
    output_dir_path.mkdir(exist_ok=True, parents=True)

    # Get feature files
    feature_files = list(Path(feature_dir).glob("*_cds_features.npz"))
    logger.info(f"Processing {len(feature_files)} feature files")

    # Process each feature file
    for feature_file in feature_files:
        # Load features
        features = dict(np.load(feature_file))

        # Create enhanced codon adaptation features
        enhanced_features = {}

        # Use basic codon features that are available in all genomes
        # These are more reliable than the specialized features that might be missing

        # 1. Use HEG_ratio as a base feature (this is available)
        if 'HEG_ratio' in features:
            enhanced_features['HEG_ratio'] = features['HEG_ratio']

            # Create derived features based on HEG_ratio
            enhanced_features['HEG_ratio_squared'] = features['HEG_ratio'] ** 2
            enhanced_features['HEG_ratio_log'] = np.log1p(features['HEG_ratio'])

        # 2. Use CUB (Codon Usage Bias) if available
        if 'CUB' in features:
            enhanced_features['CUB'] = features['CUB']

            # Create interaction features
            if 'HEG_ratio' in features:
                enhanced_features['HEG_CUB_interaction'] = features['HEG_ratio'] * features['CUB']

        # 3. Use CPB (Codon Pair Bias) if available
        if 'CPB' in features:
            enhanced_features['CPB'] = features['CPB']

            # Create interaction features
            if 'HEG_ratio' in features:
                enhanced_features['HEG_CPB_interaction'] = features['HEG_ratio'] * features['CPB']

        # 4. Use Consistency if available
        if 'Consistency' in features:
            enhanced_features['Consistency'] = features['Consistency']

            # Create interaction features
            if 'HEG_ratio' in features:
                enhanced_features['HEG_Consistency_interaction'] = features['HEG_ratio'] * features['Consistency']

        # 5. Use RemoteInteraction if available
        if 'RemoteInteraction' in features:
            enhanced_features['RemoteInteraction'] = features['RemoteInteraction']

            # Create interaction features
            if 'HEG_ratio' in features:
                enhanced_features['HEG_RemoteInteraction'] = features['HEG_ratio'] * features['RemoteInteraction']

        # 6. Use GC content features if available
        if 'GC_content' in features:
            enhanced_features['GC_content'] = features['GC_content']

            # Create interaction features
            if 'HEG_ratio' in features:
                enhanced_features['HEG_GC_interaction'] = features['HEG_ratio'] * features['GC_content']

        # 7. Use GC3 (GC content at third codon position) if available
        if 'GC3' in features:
            enhanced_features['GC3'] = features['GC3']

            # Create interaction features
            if 'HEG_ratio' in features:
                enhanced_features['HEG_GC3_interaction'] = features['HEG_ratio'] * features['GC3']

        # 8. Create composite features from available metrics
        composite_features = []
        composite_weights = []

        if 'CUB' in features:
            composite_features.append(features['CUB'])
            composite_weights.append(0.4)

        if 'CPB' in features:
            composite_features.append(features['CPB'])
            composite_weights.append(0.3)

        if 'Consistency' in features:
            composite_features.append(features['Consistency'])
            composite_weights.append(0.2)

        if 'RemoteInteraction' in features:
            composite_features.append(features['RemoteInteraction'])
            composite_weights.append(0.1)

        if composite_features:
            # Normalize weights
            weights_sum = sum(composite_weights)
            normalized_weights = [w / weights_sum for w in composite_weights]

            # Calculate weighted composite
            composite_value = sum(f * w for f, w in zip(composite_features, normalized_weights))
            enhanced_features['composite_codon_metric'] = composite_value

            # Create interaction with HEG_ratio
            if 'HEG_ratio' in features:
                enhanced_features['HEG_composite_interaction'] = features['HEG_ratio'] * composite_value

        # 9. Create dinucleotide-based features
        dinucleotide_keys = [k for k in features.keys() if k.startswith('dinucleotide_')]
        if dinucleotide_keys:
            # Calculate average dinucleotide bias
            dinucleotide_values = [features[k] for k in dinucleotide_keys]
            enhanced_features['dinucleotide_avg'] = sum(dinucleotide_values) / len(dinucleotide_values)

            # Calculate dinucleotide skew (max - min) / avg
            max_dinucleotide = max(dinucleotide_values)
            min_dinucleotide = min(dinucleotide_values)
            avg_dinucleotide = sum(dinucleotide_values) / len(dinucleotide_values)

            if avg_dinucleotide > 0:
                enhanced_features['dinucleotide_skew'] = (max_dinucleotide - min_dinucleotide) / avg_dinucleotide

        # Add prefix to enhanced features
        enhanced_features = {f"enhanced_{k}": v for k, v in enhanced_features.items()}

        # Update features
        features.update(enhanced_features)

        # Save updated features
        output_file = output_dir_path / feature_file.name
        np.savez(output_file, **features)

        logger.info(f"Added {len(enhanced_features)} enhanced codon adaptation features to {output_file}")

    logger.info("Enhanced codon adaptation feature generation complete!")
