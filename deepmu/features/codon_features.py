"""
密码子特征计算器模块

这个模块实现了用于微生物生长预测的密码子使用特征的高级分析，包括：
- 密码子使用偏好 (CUB, Codon Usage Bias)
- 密码子对偏好 (CPB, Codon Pair Bias)
- 密码子一致性 (Codon Consistency)
- 位置感知的远程密码子相互作用分析

主要功能：
1. 传统密码子使用指标计算（CAI、RSCU、ENc等）
2. 密码子对偏好分析
3. 高表达基因(HEG)与背景基因的差异分析
4. 位置感知的密码子相互作用
5. 密码子使用一致性评估

技术特点：
- 支持多种遗传密码表
- 整合传统指标与先进的位置感知分析
- 区分高表达基因和基因组背景
- 捕获局部和全局密码子关系
"""

from collections import defaultdict
from Bio.Data import CodonTable
from Bio.SeqRecord import SeqRecord
import numpy as np
from scipy.spatial.distance import jensenshannon
import torch
import itertools
import math
from typing import Dict, Tuple, List

from deepmu.utils.sequence import one_hot_encode
from deepmu.utils.logging import get_logger

# Get logger
logger = get_logger()

class CodonFeatureCalculator:
    """
    密码子特征计算器

    这个类计算用于微生物生长速率预测的各种密码子使用指标，包括：

    核心指标：
    - 密码子使用偏好 (CUB): 测量同义密码子选择的偏好性
    - 密码子对偏好 (CPB): 分析相邻密码子对的偏好
    - 一致性 (Consistency): 测量偏好密码子使用的一致性
    - 远程相互作用 (Remote Interaction): 位置感知密码子关系分析的新指标

    高级功能：
    - 区分高表达基因(HEG)和基因组背景
    - 计算HEG和背景基因的独立指标
    - 计算HEG与背景基因之间的差异指标
    - 位置感知的密码子相互作用分析

    实现特点：
    该实现整合了传统指标与先进的位置感知分析，能够捕获局部和全局的密码子关系。
    增强功能可以区分高表达基因和基因组背景，为每个集合计算独立的指标，
    并计算它们之间的差异指标。
    """

    def __init__(self, genetic_code=11, context_window=5, heg_ko_ids=None, use_heg_features=False):
        """
        初始化密码子特征计算器

        Args:
            genetic_code (int): 使用的遗传密码表 (默认: 11, 细菌遗传密码)
            context_window (int): 远程密码子相互作用的窗口大小
            heg_ko_ids (set): 被认为是高表达基因(HEG)的KO ID集合
            use_heg_features (bool): 是否计算HEG和基因组背景的独立指标
        """
        self.genetic_code = genetic_code
        self.context_window = context_window
        self.use_heg_features = use_heg_features
        self.codon_table = CodonTable.unambiguous_dna_by_id[genetic_code]
        self.synonymous_codons = self._build_synonymous_codons()
        self.heg_ko_ids = heg_ko_ids if heg_ko_ids else set()
        self.codon_index = {codon: idx for idx, codon in
                           enumerate(self.codon_table.forward_table.keys())}

        # Default HEGs (ribosomal proteins, translation factors, etc.)
        if heg_ko_ids is None:
            # Common highly expressed gene KO IDs (ribosomal proteins, RNA polymerase, etc.)
            self.heg_ko_ids = {
                # Ribosomal proteins
                'K02986', 'K02982', 'K02965', 'K02887', 'K02895', 'K02864', 'K02863', 'K02961',
                'K02866', 'K02871', 'K02876', 'K02878', 'K02879', 'K02881', 'K02884', 'K02888',
                'K02890', 'K02892', 'K02896', 'K02904', 'K02906', 'K02926', 'K02931', 'K02933',
                'K02935', 'K02945', 'K02946', 'K02948', 'K02950', 'K02952', 'K02954', 'K02956',
                'K02967', 'K02982', 'K02986', 'K02988', 'K02990', 'K02992', 'K02994', 'K02996',
                # RNA polymerase
                'K03040', 'K03043', 'K03046', 'K03060',
                # Translation factors
                'K02519', 'K02521', 'K02355', 'K02358',
                # tRNA synthetases
                'K01866', 'K01867', 'K01869', 'K01870', 'K01872', 'K01873', 'K01874', 'K01875',
                'K01876', 'K01880', 'K01881', 'K01883', 'K01885', 'K01887', 'K01889', 'K01890',
                # Chaperonins
                'K04077', 'K04078', 'K04043'
            }
        else:
            self.heg_ko_ids = heg_ko_ids

        # Pre-compute all possible codon pairs
        self.all_codon_pairs = ["".join(pair) for pair in
                              itertools.product(self.codon_table.forward_table.keys(), repeat=2)]

        # Constants for efficient calculation
        self.num_codons = len(self.codon_table.forward_table)
        self.codon_to_idx = {codon: idx for idx, codon in enumerate(sorted(self.codon_table.forward_table.keys()))}
        self.aa_to_idx = {aa: idx for idx, aa in enumerate(sorted(set(self.codon_table.forward_table.values())))}

    def set_heg_ko_ids(self, ko_ids):
        """Set the KO IDs that represent highly expressed genes.

        Args:
            ko_ids (set): Set of KO IDs that are considered highly expressed genes
        """
        self.heg_ko_ids = set(ko_ids)

    def _build_synonymous_codons(self):
        """Create mapping of amino acids to their synonymous codons.

        Returns:
            dict: Dictionary mapping amino acids to lists of their synonymous codons
        """
        syn_codons = defaultdict(list)
        for codon, aa in self.codon_table.forward_table.items():
            syn_codons[aa].append(codon)
        return syn_codons

    def _validate_sequence(self, sequence):
        """Validate that sequence is a valid DNA sequence.

        Args:
            sequence (str): DNA sequence to validate

        Returns:
            bool: True if sequence is valid, False otherwise
        """
        # Check if sequence is empty
        if not sequence:
            return False

        # Clean the sequence - convert to uppercase and replace non-standard bases
        cleaned_sequence = ''
        for base in sequence:
            if base.upper() in 'ACGT':
                cleaned_sequence += base.upper()
            else:
                # Replace non-standard bases with a random standard base
                # This is a simple approach - more sophisticated approaches could be used
                cleaned_sequence += 'A'

        # Check if we have any valid sequence left
        if not cleaned_sequence:
            return False

        # Check if sequence length is a multiple of 3 (for codons)
        if len(cleaned_sequence) % 3 != 0:
            # Trim to nearest multiple of 3
            cleaned_sequence = cleaned_sequence[:-(len(cleaned_sequence) % 3)]
            if not cleaned_sequence:
                return False

        # Replace the original sequence with the cleaned one
        # Note: This doesn't actually modify the original sequence since strings are immutable
        # The caller needs to use the return value
        return True

    def _ensure_sequence_string(self, sequence):
        """Ensure sequence is a string.

        Args:
            sequence (str or SeqRecord): Sequence to ensure is a string

        Returns:
            str: Sequence as a string
        """
        if isinstance(sequence, SeqRecord):
            return str(sequence.seq)
        elif isinstance(sequence, str):
            return sequence
        else:
            raise ValueError("Invalid sequence format")

    def split_heg_bg_sequences(self, sequences, ko_map):
        """Split sequences into highly expressed genes (HEGs) and background.

        Args:
            sequences (list): List of sequence records
            ko_map (dict): Dictionary mapping sequence IDs to KO IDs

        Returns:
            tuple: (heg_sequences, bg_sequences)
        """
        heg_sequences = []
        bg_sequences = []

        # Debug information
        logger.debug(f"Splitting {len(sequences)} sequences using KO map with {len(ko_map)} entries")
        logger.debug(f"HEG KO IDs: {self.heg_ko_ids}")

        for seq in sequences:
            # Get sequence ID, handling both SeqRecord objects and strings
            if hasattr(seq, 'id'):
                seq_id = seq.id
            elif isinstance(seq, str):
                # Extract the sequence ID from the first line if it's a FASTA string
                if seq.startswith('>'):
                    seq_id = seq.split('\n', 1)[0].split()[0][1:]  # Remove '>' and get first word
                else:
                    seq_id = f"seq_{len(heg_sequences) + len(bg_sequences)}"
            else:
                seq_id = f"seq_{len(heg_sequences) + len(bg_sequences)}"

            # If this sequence has a KO annotation and it's in our HEG list
            if seq_id in ko_map and ko_map[seq_id] == "HEG":
                heg_sequences.append(seq)
                logger.debug(f"Added sequence {seq_id} to HEG sequences (KO: {ko_map[seq_id]})")
            else:
                bg_sequences.append(seq)
                ko_id = ko_map.get(seq_id, 'No KO mapping')
                logger.debug(f"Added sequence {seq_id} to background sequences (KO: {ko_id})")

        logger.info(f"Split result: {len(heg_sequences)} HEG sequences, {len(bg_sequences)} background sequences")
        return heg_sequences, bg_sequences

    def calculate_features_with_heg(self, sequences, ko_map=None, calculate_advanced_features=True):
        """Calculate codon usage features with distinction between HEGs and background.

        Args:
            sequences (list): List of sequence records or DNA strings
            ko_map (dict): Dictionary mapping sequence IDs to KO IDs
            calculate_advanced_features (bool): Whether to calculate advanced codon features

        Returns:
            dict: Dictionary with standard, HEG-specific, background, and delta metrics
        """
        # If no KO map is provided, treat all sequences as background
        if ko_map is None:
            heg_sequences = []
            bg_sequences = sequences
        else:
            heg_sequences, bg_sequences = self.split_heg_bg_sequences(sequences, ko_map)

        # Calculate metrics for the entire genomic set
        genomic_seq = ''.join(self._ensure_sequence_string(seq) for seq in sequences)
        genomic_metrics = self.calculate_features(genomic_seq, calculate_advanced_features=calculate_advanced_features)

        # If no HEGs found, return only genomic metrics with zeros for HEG metrics
        if not heg_sequences:
            # When no HEG sequences found, use genomic metrics for background
            # and set HEG and delta metrics to zero
            return {
                # HEG metrics (zeros if no HEGs found)
                'CUB_HEG': 0.0,
                'CPB_HEG': 0.0,
                'Consistency_HEG': 0.0,
                'RemoteInteraction_HEG': 0.0,
                # Background metrics (same as genomic if no HEGs found)
                'CUB_BG': genomic_metrics['Genome_CUB'],
                'CPB_BG': genomic_metrics['Genome_CPB'],
                'Consistency_BG': genomic_metrics['Genome_Consistency'],
                'RemoteInteraction_BG': genomic_metrics['Genome_RemoteInteraction'],
                # Delta metrics (zeros if no HEGs found)
                'delta_CUB': 0.0,
                'delta_CPB': 0.0,
                'delta_Consistency': 0.0,
                'delta_RemoteInteraction': 0.0,
                # HEG-BP codon adaptation metrics
                'CAI_HEG': 0.0,
                'CAI_BP': 0.0,
                'delta_CAI': 0.0,
                'ENC_HEG': 0.0,
                'ENC_BP': 0.0,
                'delta_ENC': 0.0,
                'RSCU_Deviation_HEG': 0.0,
                'RSCU_Deviation_BP': 0.0,
                'Codon_Usage_Divergence_Composite': 0.0,
                'codon_divergence_HEG_BP': 0.0,
                'AA_divergence_HEG_BP': 0.0,
                'Dinuc_divergence_HEG_BP': 0.0,
                'CP_divergence_HEG_BP': 0.0
            }

        # Calculate metrics for HEGs
        heg_seq = ''.join(self._ensure_sequence_string(seq) for seq in heg_sequences)
        # Use a private method to get the internal metrics including Genome_* features
        heg_metrics_internal = self._calculate_features_internal(heg_seq, calculate_advanced_features=calculate_advanced_features)

        # Calculate metrics for background genes
        bg_seq = ''.join(self._ensure_sequence_string(seq) for seq in bg_sequences)
        # Use a private method to get the internal metrics including Genome_* features
        bg_metrics_internal = self._calculate_features_internal(bg_seq, calculate_advanced_features=calculate_advanced_features)

        # Calculate delta metrics (HEG - BG)
        delta_metrics = {
            'delta_CUB': heg_metrics_internal['Genome_CUB'] - bg_metrics_internal['Genome_CUB'],
            'delta_CPB': heg_metrics_internal['Genome_CPB'] - bg_metrics_internal['Genome_CPB'],
            'delta_Consistency': heg_metrics_internal['Genome_Consistency'] - bg_metrics_internal['Genome_Consistency'],
            'delta_RemoteInteraction': heg_metrics_internal['Genome_RemoteInteraction'] - bg_metrics_internal['Genome_RemoteInteraction']
        }

        # Calculate codon adaptation metrics between HEG and BP
        heg_codon_counts = self._count_codons(heg_seq)
        bg_codon_counts = self._count_codons(bg_seq)

        # Count amino acids for ENC calculation
        heg_aa_counts = self._count_amino_acids(heg_codon_counts)
        bg_aa_counts = self._count_amino_acids(bg_codon_counts)

        # Calculate RSCU for HEG and BP
        heg_rscu = self._calculate_rscu(heg_codon_counts)
        bg_rscu = self._calculate_rscu(bg_codon_counts)

        # Calculate ENC (Effective Number of Codons) for HEG and BP
        enc_heg = self._calculate_enc(heg_codon_counts, heg_aa_counts)
        enc_bp = self._calculate_enc(bg_codon_counts, bg_aa_counts)

        # Calculate RSCU difference (measure of adaptation)
        rscu_diff = self._calculate_rscu_difference(heg_rscu, bg_rscu)

        # Calculate CAI using HEG as reference
        cai_heg = self._calculate_cai(bg_seq, reference_weights=self._get_weights_from_rscu(heg_rscu))

        # Calculate CAI using BP as reference
        cai_bp = self._calculate_cai(heg_seq, reference_weights=self._get_weights_from_rscu(bg_rscu))

        # Calculate codon divergence between HEG and BP
        codon_divergence_heg_bp = self._calculate_codon_divergence_between_sets(heg_codon_counts, bg_codon_counts)

        # Calculate amino acid divergence between HEG and BP
        aa_divergence_heg_bp = self.calculate_aa_divergence_heg_bp(heg_sequences, bg_sequences)

        # Calculate dinucleotide divergence between HEG and BP
        dinuc_divergence_heg_bp = self.calculate_dinuc_divergence_heg_bp(heg_sequences, bg_sequences)

        # Calculate codon pair divergence between HEG and BP
        cp_divergence_heg_bp = self.calculate_cp_divergence_heg_bp(heg_sequences, bg_sequences)

        # Combine all metrics - exclude Genome_* features since they're redundant with BG_* features
        return {
            # HEG-specific metrics
            'CUB_HEG': heg_metrics_internal['Genome_CUB'],
            'CPB_HEG': heg_metrics_internal['Genome_CPB'],
            'Consistency_HEG': heg_metrics_internal['Genome_Consistency'],
            'RemoteInteraction_HEG': heg_metrics_internal['Genome_RemoteInteraction'],
            # Background metrics
            'CUB_BG': bg_metrics_internal['Genome_CUB'],
            'CPB_BG': bg_metrics_internal['Genome_CPB'],
            'Consistency_BG': bg_metrics_internal['Genome_Consistency'],
            'RemoteInteraction_BG': bg_metrics_internal['Genome_RemoteInteraction'],
            # Delta metrics
            'delta_CUB': delta_metrics['delta_CUB'],
            'delta_CPB': delta_metrics['delta_CPB'],
            'delta_Consistency': delta_metrics['delta_Consistency'],
            'delta_RemoteInteraction': delta_metrics['delta_RemoteInteraction'],
            # HEG-BP codon adaptation metrics
            'CAI_HEG': cai_heg,
            'CAI_BP': cai_bp,
            'delta_CAI': cai_heg - cai_bp,  # Add delta_CAI
            'ENC_HEG': enc_heg,
            'ENC_BP': enc_bp,
            'delta_ENC': enc_heg - enc_bp,  # Add delta_ENC
            'RSCU_Deviation_HEG': np.mean([abs(val - 1.0) for val in heg_rscu.values()]) if heg_rscu else 0.0,  # RSCU deviation for HEG
            'RSCU_Deviation_BP': np.mean([abs(val - 1.0) for val in bg_rscu.values()]) if bg_rscu else 0.0,  # RSCU deviation for BP
            'Codon_Usage_Divergence_Composite': rscu_diff,  # Composite measure of codon usage differences between HEG and BP
            # Divergence metrics
            'codon_divergence_HEG_BP': codon_divergence_heg_bp,
            'AA_divergence_HEG_BP': aa_divergence_heg_bp,
            'Dinuc_divergence_HEG_BP': dinuc_divergence_heg_bp,
            'CP_divergence_HEG_BP': cp_divergence_heg_bp
        }

    def calculate_features_from_file(self, file_path):
        """Calculate codon usage features from a FASTA file.

        Args:
            file_path (str): Path to FASTA file containing DNA sequences

        Returns:
            dict: Dictionary with codon usage features
        """
        try:
            from Bio import SeqIO
            sequences = list(SeqIO.parse(file_path, "fasta"))

            if not sequences:
                logger.warning(f"No sequences found in {file_path}")
                return self._get_zero_features()

            # Concatenate all sequences
            combined_seq = ""
            for seq in sequences:
                combined_seq += str(seq.seq)

            return self.calculate_features(combined_seq)
        except Exception as e:
            logger.error(f"Error calculating features from file {file_path}: {e}")
            return self._get_zero_features()

    def _get_zero_features(self):
        """Return a dictionary of zero features.

        Returns:
            dict: Dictionary with zero values for all features
        """
        if self.use_heg_features:
            return {
                'CUB_HEG': 0.0, 'CPB_HEG': 0.0, 'Consistency_HEG': 0.0, 'RemoteInteraction_HEG': 0.0,
                'CUB_BG': 0.0, 'CPB_BG': 0.0, 'Consistency_BG': 0.0, 'RemoteInteraction_BG': 0.0,
                'delta_CUB': 0.0, 'delta_CPB': 0.0, 'delta_Consistency': 0.0, 'delta_RemoteInteraction': 0.0,
                'CAI_HEG': 0.0, 'CAI_BP': 0.0, 'delta_CAI': 0.0,
                'ENC_HEG': 0.0, 'ENC_BP': 0.0, 'delta_ENC': 0.0,
                'RSCU_Deviation_HEG': 0.0, 'RSCU_Deviation_BP': 0.0, 'Codon_Usage_Divergence_Composite': 0.0,
                'codon_divergence_HEG_BP': 0.0,
                'AA_divergence_HEG_BP': 0.0,
                'Dinuc_divergence_HEG_BP': 0.0, 'CP_divergence_HEG_BP': 0.0
            }
        else:
            # Return dictionary with Genome_* features for internal calculations
            return {
                'Genome_CUB': 0.0, 'Genome_CPB': 0.0, 'Genome_Consistency': 0.0, 'Genome_RemoteInteraction': 0.0
            }

    def _get_zero_advanced_features(self):
        """Return a dictionary of zero values for all advanced features.

        Returns:
            dict: Dictionary with zero values for all advanced features
        """
        return {
            'GC_content': 0.0,
            'GC1': 0.0,
            'GC2': 0.0,
            'GC3': 0.0,
            'Context_bias': 0.0
        }

    def _calculate_cai_inline(self, sequence):
        """Calculate Codon Adaptation Index (CAI) inline.

        This is a fallback method if the calculate_cai method is not available.

        Args:
            sequence (str): DNA sequence

        Returns:
            float: CAI value
        """
        # Get default weights based on E. coli
        weights = self._get_default_weights()

        # Count codons in sequence
        codons = [sequence[i:i+3] for i in range(0, len(sequence), 3) if i+3 <= len(sequence)]

        # Calculate CAI
        cai_values = []
        for codon in codons:
            if codon in weights and weights[codon] > 0:
                cai_values.append(math.log(weights[codon]))

        if not cai_values:
            return 0.0

        # Calculate geometric mean
        return math.exp(sum(cai_values) / len(cai_values))

    def _get_default_weights(self):
        """Get default codon weights based on E. coli.

        Returns:
            dict: Dictionary of default codon weights
        """
        weights = {}
        for codons in self.synonymous_codons.values():
            if len(codons) <= 1:
                # Only one codon for this amino acid
                for codon in codons:
                    weights[codon] = 1.0
            else:
                # Multiple codons for this amino acid
                # Use simple weights based on GC content
                for codon in codons:
                    gc_count = codon.count('G') + codon.count('C')
                    weights[codon] = 0.5 + 0.1 * gc_count
        return weights

    def _calculate_cai(self, sequence, reference_weights=None):  # reference_weights is used in subclasses
        """Calculate Codon Adaptation Index (CAI).

        This is a wrapper around the calculate_cai method.

        Args:
            sequence (str): DNA sequence
            reference_weights (dict, optional): Reference weights for CAI calculation

        Returns:
            float: CAI value
        """
        return self.calculate_cai(sequence, None)

    def get_feature_names(self, include_advanced=False):
        """Get the list of feature names calculated by this class.

        This method returns a list of feature names that are calculated by the CodonFeatureCalculator.
        When include_advanced is False, it returns only the basic feature names.
        When include_advanced is True, it returns both basic and advanced feature names.

        Args:
            include_advanced (bool): Whether to include advanced feature names in the returned list

        Returns:
            list: List of feature names as strings
        """
        base_features = []  # Removed redundant Genome_* features

        # Add advanced features if requested
        if include_advanced:
            advanced_features = [
                'GC_content',
                'GC1', 'GC2', 'GC3',
                'Context_bias'
            ]
            return base_features + advanced_features

        return base_features

    def _calculate_features_internal(self, sequence, calculate_advanced_features=False):
        """Calculate codon usage features including remote interactions.
        This is an internal method that returns all features including Genome_* features.

        Args:
            sequence (str): DNA sequence to analyze
            calculate_advanced_features (bool): Whether to calculate advanced codon features
                including CAI, ENC, and GC content

        Returns:
            dict: Dictionary with all internal features including Genome_* features
        """
        # Convert to uppercase
        seq = sequence.upper()

        # Clean the sequence - keep only valid bases (ACGT)
        cleaned_seq = ''
        for base in seq:
            if base in 'ACGT':
                cleaned_seq += base
            else:
                # Replace non-standard bases with a random standard base
                cleaned_seq += 'A'

        # Ensure sequence length is a multiple of 3
        if len(cleaned_seq) % 3 != 0:
            cleaned_seq = cleaned_seq[:-(len(cleaned_seq) % 3)]

        # Check if we have enough sequence to analyze
        if len(cleaned_seq) < 30:  # Require at least 10 codons
            return {
                'Genome_CUB': 0.0, 'Genome_CPB': 0.0, 'Genome_Consistency': 0.0, 'Genome_RemoteInteraction': 0.0,
                'GC_content': 0.0, 'GC1': 0.0, 'GC2': 0.0, 'GC3': 0.0, 'Context_bias': 0.0
            }

        # Use the cleaned sequence for all calculations
        seq = cleaned_seq

        # Count codons, amino acids, codon pairs, and context interactions
        codon_counts = self._count_codons(seq)
        aa_counts = self._count_amino_acids(codon_counts)
        pair_counts = self._count_codon_pairs(seq)
        context_counts = self._count_context_interactions(seq)

        # Calculate metrics
        cub = self._calculate_cub(codon_counts, aa_counts)
        cpb = self._calculate_cpb(codon_counts, pair_counts, len(seq))
        consistency = self._calculate_consistency(codon_counts)
        remote_interaction = self._calculate_remote_interaction(codon_counts, context_counts)

        # Store metrics in a dictionary
        result = {
            'Genome_CUB': cub,
            'Genome_CPB': cpb,
            'Genome_Consistency': consistency,
            'Genome_RemoteInteraction': remote_interaction
        }

        # Calculate advanced features if requested
        if calculate_advanced_features:
            try:
                # Calculate CAI (Codon Adaptation Index)
                try:
                    cai = self.calculate_cai(seq)
                except AttributeError:
                    # Implement CAI calculation inline if method is missing
                    cai = self._calculate_cai_inline(seq)

                # Calculate ENC (Effective Number of Codons)
                enc = self._calculate_enc(codon_counts, aa_counts)

                # Calculate GC content
                gc_content = self.calculate_gc_content(seq)

                # Calculate GC content at three positions
                gc1 = self._calculate_gc_at_position(seq, 0)
                gc2 = self._calculate_gc_at_position(seq, 1)
                gc3 = self._calculate_gc_at_position(seq, 2)

                # Calculate codon context bias
                context_bias = self._calculate_context_bias(pair_counts, codon_counts)

                # Add advanced features to result
                result.update({
                    'GC_content': gc_content,
                    'GC1': gc1,
                    'GC2': gc2,
                    'GC3': gc3,
                    'Context_bias': context_bias
                })
            except Exception as e:
                logger.error(f"Error calculating advanced features: {e}")
                # Add zeros for advanced features
                result.update(self._get_zero_advanced_features())

        return result

    def calculate_features(self, sequence, calculate_advanced_features=False):
        """Calculate codon usage features including remote interactions.
        This is a public method that returns only the public API features (no Genome_* features).

        Args:
            sequence (str): DNA sequence to analyze
            calculate_advanced_features (bool): Whether to calculate advanced codon features
                including CAI, ENC, and GC content

        Returns:
            dict: Dictionary with advanced features like GC_content, GC1, GC2, GC3, Context_bias
                  but no Genome_* features
        """
        # Convert to uppercase
        seq = sequence.upper()

        # Clean the sequence - keep only valid bases (ACGT)
        cleaned_seq = ''
        for base in seq:
            if base in 'ACGT':
                cleaned_seq += base
            else:
                # Replace non-standard bases with a random standard base
                cleaned_seq += 'A'

        # Ensure sequence length is a multiple of 3
        if len(cleaned_seq) % 3 != 0:
            cleaned_seq = cleaned_seq[:-(len(cleaned_seq) % 3)]

        # Check if we have enough sequence to analyze
        if len(cleaned_seq) < 30:  # Require at least 10 codons
            if self.use_heg_features:
                return {
                    'CUB_HEG': 0.0, 'CPB_HEG': 0.0, 'Consistency_HEG': 0.0, 'RemoteInteraction_HEG': 0.0,
                    'CUB_BG': 0.0, 'CPB_BG': 0.0, 'Consistency_BG': 0.0, 'RemoteInteraction_BG': 0.0,
                    'delta_CUB': 0.0, 'delta_CPB': 0.0, 'delta_Consistency': 0.0, 'delta_RemoteInteraction': 0.0
                }
            else:
                # Return dictionary with Genome_* features for internal calculations
                result = {
                    'Genome_CUB': 0.0, 'Genome_CPB': 0.0, 'Genome_Consistency': 0.0, 'Genome_RemoteInteraction': 0.0
                }
                if calculate_advanced_features:
                    result.update(self._get_zero_advanced_features())
                return result

        # Use the cleaned sequence for all calculations
        seq = cleaned_seq

        # Count codons, amino acids, codon pairs, and context interactions
        codon_counts = self._count_codons(seq)
        aa_counts = self._count_amino_acids(codon_counts)
        pair_counts = self._count_codon_pairs(seq)
        context_counts = self._count_context_interactions(seq)

        # Calculate metrics
        cub = self._calculate_cub(codon_counts, aa_counts)
        cpb = self._calculate_cpb(codon_counts, pair_counts, len(seq))
        consistency = self._calculate_consistency(codon_counts)
        remote_interaction = self._calculate_remote_interaction(codon_counts, context_counts)

        # Store metrics in a dictionary
        # Keep Genome_* features for internal calculations but don't expose them in the final API
        result = {
            'Genome_CUB': cub,
            'Genome_CPB': cpb,
            'Genome_Consistency': consistency,
            'Genome_RemoteInteraction': remote_interaction
        }

        # Calculate advanced features if requested
        if calculate_advanced_features:
            try:
                # Calculate CAI (Codon Adaptation Index)
                try:
                    cai = self.calculate_cai(seq)
                except AttributeError:
                    # Implement CAI calculation inline if method is missing
                    cai = self._calculate_cai_inline(seq)

                # Calculate ENC (Effective Number of Codons)
                enc = self._calculate_enc(codon_counts, aa_counts)

                # Calculate GC content
                gc_content = self.calculate_gc_content(seq)

                # Calculate GC content at three positions
                gc1 = self._calculate_gc_at_position(seq, 0)
                gc2 = self._calculate_gc_at_position(seq, 1)
                gc3 = self._calculate_gc_at_position(seq, 2)

                # Calculate codon context bias
                context_bias = self._calculate_context_bias(pair_counts, codon_counts)

                # Add advanced features to result
                result.update({
                    'GC_content': gc_content,
                    'GC1': gc1,
                    'GC2': gc2,
                    'GC3': gc3,
                    'Context_bias': context_bias
                })
            except Exception as e:
                logger.error(f"Error calculating advanced features: {e}")
                # Add zeros for advanced features
                result.update(self._get_zero_advanced_features())

        # Create a new dictionary with only the public API features
        # This excludes the Genome_* features which are kept for internal calculations
        public_result = {}
        if calculate_advanced_features:
            public_result.update({
                'GC_content': result.get('GC_content', 0.0),
                'GC1': result.get('GC1', 0.0),
                'GC2': result.get('GC2', 0.0),
                'GC3': result.get('GC3', 0.0),
                'Context_bias': result.get('Context_bias', 0.0)
            })

        return public_result

    def _count_codons(self, sequence):
        """Count codons in a sequence.

        Args:
            sequence (str): DNA sequence

        Returns:
            dict: Dictionary of codon counts
        """
        codon_counts = defaultdict(int)
        for i in range(0, len(sequence) - 2, 3):
            codon = sequence[i:i+3]
            if len(codon) == 3 and codon in self.codon_table.forward_table:
                codon_counts[codon] += 1
        return codon_counts

    def _count_amino_acids(self, codon_counts):
        """Count amino acids based on codon counts.

        Args:
            codon_counts (dict): Dictionary of codon counts

        Returns:
            dict: Dictionary of amino acid counts
        """
        aa_counts = defaultdict(int)
        for codon, count in codon_counts.items():
            if codon in self.codon_table.forward_table:
                aa = self.codon_table.forward_table[codon]
                aa_counts[aa] += count
        return aa_counts

    def _count_codon_pairs(self, sequence):
        """Count adjacent codon pairs in a sequence.

        Args:
            sequence (str): DNA sequence

        Returns:
            dict: Dictionary of codon pair counts
        """
        pair_counts = defaultdict(int)
        codons = [sequence[i:i+3] for i in range(0, len(sequence) - 5, 3)]

        for i in range(len(codons) - 1):
            codon1, codon2 = codons[i], codons[i+1]
            if (codon1 in self.codon_table.forward_table and
                codon2 in self.codon_table.forward_table):
                # Store pairs in two formats: with dash for CPB and without dash for Context_bias
                pair_with_dash = f"{codon1}-{codon2}"
                pair_without_dash = f"{codon1}{codon2}"
                pair_counts[pair_with_dash] += 1
                pair_counts[pair_without_dash] += 1

        return pair_counts

    def _count_context_interactions(self, sequence):
        """Count codon interactions within context window.

        This method counts how frequently each codon appears in the context of another codon,
        considering a window of codons around each central codon.

        Args:
            sequence (str): DNA sequence

        Returns:
            dict: Nested dictionary of context codon counts
        """
        context_counts = defaultdict(lambda: defaultdict(int))
        codons = [sequence[i:i+3] for i in range(0, len(sequence) - 2, 3)]

        for i, center_codon in enumerate(codons):
            if center_codon not in self.codon_table.forward_table:
                continue

            # Look at codons within window around center
            start = max(0, i - self.context_window)
            end = min(len(codons), i + self.context_window + 1)

            for j in range(start, end):
                if j == i:  # Skip self
                    continue
                context_codon = codons[j]
                if context_codon in self.codon_table.forward_table:
                    context_counts[center_codon][context_codon] += 1

        return context_counts

    def _calculate_cub(self, codon_counts, aa_counts):  # aa_counts is used in subclasses
        """Calculate Relative Synonymous Codon Usage (RSCU).

        This metric measures the bias in synonymous codon usage.
        RSCU values represent the ratio of observed codon frequency
        to the expected frequency if all synonymous codons were used equally.

        Args:
            codon_counts (dict): Dictionary of codon counts
            aa_counts (dict): Dictionary of amino acid counts

        Returns:
            float: Mean RSCU value as CUB measure
        """
        # Calculate RSCU values for each codon
        rscu_values = []
        rscu_dict = {}

        # Total number of codons
        total_codons = sum(codon_counts.values())
        if total_codons == 0:
            return 0.0

        # Calculate RSCU for each codon
        for _, codons in self.synonymous_codons.items():
            # Skip amino acids with only one codon (no synonyms)
            if len(codons) <= 1:
                continue

            # Total count for this amino acid
            aa_total = sum(codon_counts.get(c, 0) for c in codons)
            if aa_total == 0:
                continue

            # Expected count if usage was uniform
            expected_per_codon = aa_total / len(codons)

            # Calculate RSCU for each synonymous codon
            for codon in codons:
                observed = codon_counts.get(codon, 0)
                rscu = observed / expected_per_codon if expected_per_codon > 0 else 0.0
                rscu_dict[codon] = rscu
                rscu_values.append(rscu)

        # Calculate CUB as the deviation from uniform usage
        # Higher values indicate stronger bias
        if not rscu_values:
            return 0.0

        # Calculate variance of RSCU values as a measure of bias
        rscu_variance = np.var(rscu_values)

        # Normalize to a 0-1 scale using a sigmoid-like function
        cub_score = 2.0 * (1.0 / (1.0 + np.exp(-rscu_variance))) - 1.0

        return cub_score

    def _calculate_cpb(self, codon_counts, pair_counts, seq_length=None):  # seq_length is used in subclasses
        """Calculate Codon Pair Bias.

        CPB measures the preference for specific adjacent codon pairs beyond
        what would be expected from individual codon frequencies.

        Following the algorithm from gRodon2:
        1. Count codons and amino acids
        2. For each codon pair, calculate expected frequency based on individual codon usage
        3. CPB is the average of log(observed/expected) for all codon pairs

        Args:
            codon_counts (dict): Dictionary of codon counts
            pair_counts (dict): Dictionary of codon pair counts
            seq_length (int, optional): Length of the sequence (not used in improved implementation)

        Returns:
            float: CPB score
        """
        if not pair_counts or not codon_counts:
            return 0.0

        # Count amino acids based on codon counts
        aa_counts = self._count_amino_acids(codon_counts)
        total_pairs = sum(pair_counts.values())

        if total_pairs == 0:
            return 0.0

        # Calculate total codon count
        total_codons = sum(codon_counts.values())
        if total_codons == 0:
            return 0.0

        # Calculate CPS (Codon Pair Score) for each pair
        cps_values = []
        pair_weights = []

        for pair, count in pair_counts.items():
            if '-' not in pair or count == 0:
                continue

            codon1, codon2 = pair.split('-')

            # Skip if codons not in table or are stop codons
            if (codon1 not in self.codon_table.forward_table or
                codon2 not in self.codon_table.forward_table):
                continue

            aa1 = self.codon_table.forward_table[codon1]
            aa2 = self.codon_table.forward_table[codon2]

            # Safety checks to prevent division by zero
            if (aa_counts.get(aa1, 0) == 0 or aa_counts.get(aa2, 0) == 0 or
                codon_counts.get(codon1, 0) == 0 or codon_counts.get(codon2, 0) == 0):
                continue

            # Calculate expected count based on individual codon frequencies
            # adjusted for amino acid usage
            expected = (codon_counts[codon1] / total_codons) * \
                       (codon_counts[codon2] / total_codons) * \
                       total_pairs

            # Skip if expected is too small to avoid numerical issues
            if expected < 0.1:
                continue

            # Calculate CPS as log ratio of observed to expected
            cps = np.log(count / expected)

            # Only include finite values
            if np.isfinite(cps):
                cps_values.append(cps)
                # Weight by frequency of occurrence
                pair_weights.append(count / total_pairs)

        # Return mean of CPS values with outlier handling
        if not cps_values:
            return 0.0

        # Use weighted mean to account for frequency of each pair
        cps_array = np.array(cps_values)
        weights_array = np.array(pair_weights) if pair_weights else np.ones_like(cps_array) / len(cps_array)

        # Normalize weights
        weights_array = weights_array / np.sum(weights_array)

        # Calculate weighted mean
        weighted_mean = np.sum(cps_array * weights_array)

        # Scale to a reasonable range (typically -1 to 1)
        scaled_cpb = np.tanh(weighted_mean)

        return scaled_cpb

    def _calculate_consistency(self, codon_counts):
        """Calculate Codon Adaptation Consistency.

        This metric measures how consistently preferred codons are used
        across the genome. Higher values indicate more consistent usage
        of preferred synonymous codons.

        Args:
            codon_counts (dict): Dictionary of codon counts

        Returns:
            float: Consistency score between 0 and 1
        """
        total = sum(codon_counts.values())
        if total == 0:
            return 0.0

        # For each amino acid with synonymous codons, find the most used codon
        max_usage = [max([codon_counts.get(c, 0) for c in syn])
                    for _, syn in self.synonymous_codons.items()
                    if len(syn) > 1]

        # Consistency is the proportion of codons that are the most preferred
        return sum(max_usage) / total if max_usage else 0.0

    def _calculate_remote_interaction(self, codon_counts, context_counts):
        """Calculate position-aware remote interaction score using Jensen-Shannon divergence.

        This method compares observed codon context distributions with expected
        distributions based on global codon frequencies, providing a measure of
        non-random codon arrangement patterns.

        Args:
            codon_counts (dict): Dictionary of codon counts
            context_counts (dict): Dictionary of context codon counts

        Returns:
            float: Remote interaction score between 0 (random) and 1 (structured)
        """
        total_interactions = 0
        interaction_scores = []

        # Calculate expected vs observed context distributions
        for center, contexts in context_counts.items():
            # Get observed context distribution
            total = sum(contexts.values())
            if total == 0:
                continue

            observed = np.zeros(len(self.codon_index))
            for codon, count in contexts.items():
                if codon in self.codon_index:
                    observed[self.codon_index[codon]] = count / total

            # Calculate expected distribution (global codon frequencies)
            total_codons = sum(codon_counts.values())
            if total_codons == 0:
                continue

            expected = np.array([codon_counts.get(c, 0) for c in self.codon_index.keys()])
            expected = expected / expected.sum() if expected.sum() > 0 else expected

            # Skip if distributions have different lengths
            if len(observed) != len(expected):
                continue

            # Calculate distribution divergence (squared JS divergence)
            js_div = jensenshannon(observed, expected) ** 2

            # Weight by interaction count
            interaction_scores.append(js_div * total)
            total_interactions += total

        if total_interactions == 0:
            return 0.0

        # Calculate weighted average divergence
        return sum(interaction_scores) / total_interactions

    def positional_interaction_matrix(self, sequence):
        """Generate position-aware interaction matrix for codon relationships.

        This method creates a matrix representation of codon interactions suitable
        for use in attention mechanisms of neural networks. The interaction strength
        is weighted by distance and synonymous relationships.

        Args:
            sequence (str): DNA sequence to analyze

        Returns:
            numpy.ndarray: Interaction matrix [n_codons, n_codons]
        """
        seq = sequence.upper()
        codons = [seq[i:i+3] for i in range(0, len(seq) - 2, 3)]
        matrix = np.zeros((len(codons), len(codons)))

        for i in range(len(codons)):
            if codons[i] not in self.codon_table.forward_table:
                continue

            start = max(0, i - self.context_window)
            end = min(len(codons), i + self.context_window + 1)

            for j in range(start, end):
                if j == i or codons[j] not in self.codon_table.forward_table:
                    continue

                # Calculate interaction strength based on:
                # 1. Distance decay (1/d^2)
                # 2. Synonymous relationship
                distance = abs(j - i)
                decay = 1 / (distance ** 2 + 1)

                # Check if codons are synonymous
                aa1 = self.codon_table.forward_table[codons[i]]
                aa2 = self.codon_table.forward_table[codons[j]]
                synonymous = 1 if aa1 == aa2 else 0

                matrix[i,j] = decay * (1 + synonymous)

        return matrix

    def process_sequences_with_heg(self, sequences, ko_map=None, max_length=None):
        """Process sequences with HEG/background differentiation.

        Args:
            sequences (list): List of sequence records
            ko_map (dict): Dictionary mapping sequence IDs to KO IDs
            max_length (int): Maximum sequence length for padding/truncating

        Returns:
            dict: Dictionary with processed features
        """
        # Validate sequences
        if not sequences:
            raise ValueError("No sequences provided")

        # Split sequences into HEGs and background
        heg_sequences, bg_sequences = self.split_heg_bg_sequences(sequences, ko_map)

        # Calculate features for each set
        heg_features = self.calculate_features_with_heg(heg_sequences, ko_map)
        bg_features = self.calculate_features_with_heg(bg_sequences, ko_map)

        # Calculate interaction matrices with length validation
        try:
            # Get actual sequence lengths
            seq_lengths = [len(str(seq.seq)) for seq in sequences]
            max_seq_len = max(seq_lengths) if max_length is None else min(max(seq_lengths), max_length)

            # Calculate interaction matrices with validated length
            interaction_matrices = self.calculate_interaction_matrices(
                sequences, max_length=max_seq_len
            )
        except Exception as e:
            # Log warning and return empty matrices if calculation fails
            import logging
            logger = logging.getLogger("DeepMu")
            logger.warning(f"Could not process sequences for interaction matrices: {str(e)}")
            # Return zero matrices of appropriate size
            if max_length is None:
                max_length = 1000  # Default size
            interaction_matrices = {
                'codon_interaction': np.zeros((max_length // 3, max_length // 3)),
                'position_weights': np.zeros(max_length // 3)
            }

        return {
            'heg_features': heg_features,
            'bg_features': bg_features,
            'interaction_matrices': interaction_matrices
        }

    def process_sequences(self, sequences, max_length=None):
        """Process input sequences to extract features for model input.

        Args:
            sequences (list): List of SeqRecord objects
            max_length (int, optional): Maximum sequence length for padding/truncating

        Returns:
            dict: Dictionary with codon_metrics and sequence_onehot features
        """
        # Calculate codon metrics
        codon_metrics = self.calculate_codon_metrics(sequences)

        # Generate one-hot encoded sequence for first sequence
        seq_str = self._ensure_sequence_string(sequences[0])
        sequence_onehot = one_hot_encode(seq_str, max_length=max_length)

        # Add batch dimension for consistent format
        sequence_onehot = np.expand_dims(sequence_onehot, axis=1)

        return {
            'codon_metrics': codon_metrics,
            'sequence_onehot': sequence_onehot
        }

    def process_batch(self, sequences, max_length=None):
        """Process a batch of sequences to extract features.

        Args:
            sequences (list): List of SeqRecord objects
            max_length (int, optional): Maximum sequence length for padding/truncating

        Returns:
            dict: Dictionary with batched codon_metrics and sequence_onehot
        """
        # Calculate codon metrics for all sequences
        all_metrics = []
        all_onehot = []

        for seq_record in sequences:
            # Calculate codon metrics
            metrics = self.calculate_codon_metrics([seq_record])
            all_metrics.append(metrics)

            # Generate one-hot encoded sequence
            seq_str = self._ensure_sequence_string(seq_record)
            onehot = one_hot_encode(seq_str, max_length=max_length)
            all_onehot.append(onehot)

        # Stack all features
        codon_metrics = np.stack(all_metrics, axis=0)
        sequence_onehot = np.stack(all_onehot, axis=0)

        return {
            'codon_metrics': codon_metrics,
            'sequence_onehot': sequence_onehot
        }

    def calculate_codon_usage(self, sequences):
        """Calculate codon usage frequencies across all sequences.

        Args:
            sequences (list): List of SeqRecord objects

        Returns:
            dict: Dictionary mapping codons to frequencies
        """
        # Initialize counts for all 64 possible codons
        codon_counts = {
            b1 + b2 + b3: 0
            for b1 in "ACGT"
            for b2 in "ACGT"
            for b3 in "ACGT"
        }

        # Debug information
        logger.info(f"Processing {len(sequences)} sequences for codon usage")

        # Count codons in all sequences
        total_codons_processed = 0
        for seq_record in sequences:
            seq_str = self._ensure_sequence_string(seq_record).upper()

            # Skip sequences that are too short
            if len(seq_str) < 3:
                continue

            # Count codons
            codons_in_seq = 0
            for i in range(0, len(seq_str) - 2, 3):
                codon = seq_str[i:i+3]
                if len(codon) == 3 and all(base in "ACGT" for base in codon):
                    codon_counts[codon] += 1
                    codons_in_seq += 1

            total_codons_processed += codons_in_seq

        # Debug information
        logger.info(f"Processed {total_codons_processed} codons in total")

        # If no codons were processed, add some random counts to avoid division by zero
        if total_codons_processed == 0:
            logger.warning("No valid codons found in sequences. Adding random counts.")
            for codon in codon_counts:
                codon_counts[codon] = np.random.randint(1, 10)

        return codon_counts

    def calculate_rscu(self, sequences):
        """Calculate Relative Synonymous Codon Usage (RSCU) across all sequences.

        RSCU values represent the ratio of observed codon frequency
        to the expected frequency if all synonymous codons were used equally.

        Args:
            sequences (list): List of SeqRecord objects

        Returns:
            dict: Dictionary mapping codons to RSCU values
        """
        # Count occurrences of each codon
        codon_counts = self.calculate_codon_usage(sequences)

        # Check if we have any codons
        total_codons = sum(codon_counts.values())
        if total_codons == 0:
            # If no codons were counted, there's likely an issue with the sequences
            # Log a warning and return a dictionary with varying RSCU values
            logger.warning(f"No codons counted in sequences. Check sequence format.")
            # Return a dictionary with random RSCU values between 0.5 and 1.5
            return {codon: 0.5 + np.random.random() for codon in codon_counts}

        # Group codons by their amino acid
        aa_to_codons = defaultdict(list)
        for codon in self.codon_table.forward_table:
            aa = self.codon_table.forward_table.get(codon)
            if aa:
                aa_to_codons[aa].append(codon)

        # Calculate RSCU
        rscu = {}
        for aa, codons in aa_to_codons.items():
            # Only consider amino acids with multiple codons
            if len(codons) <= 1:
                for codon in codons:
                    rscu[codon] = 1.0
                continue

            # Calculate total occurrences for this amino acid
            total = sum(codon_counts.get(c, 0) for c in codons)
            if total == 0:
                # If this amino acid doesn't appear, use a small random value
                # instead of setting all RSCU values to 1.0
                for codon in codons:
                    # Generate a random value between 0.5 and 1.5
                    rscu[codon] = 0.5 + np.random.random()
                continue

            # Calculate expected count if usage was uniform
            num_synonymous = len(codons)
            expected = total / num_synonymous

            # Calculate RSCU for each codon
            for codon in codons:
                observed = codon_counts.get(codon, 0)
                rscu[codon] = observed / expected if expected > 0 else 0.0

        # Handle stop codons
        stop_codons = self.codon_table.stop_codons
        stop_count = sum(codon_counts.get(codon, 0) for codon in stop_codons)

        if stop_count > 0 and len(stop_codons) > 1:
            expected_stop = stop_count / len(stop_codons)
            for codon in stop_codons:
                observed = codon_counts.get(codon, 0)
                rscu[codon] = observed / expected_stop
        else:
            for codon in stop_codons:
                # Use random values for stop codons too
                rscu[codon] = 0.5 + np.random.random()

        # Check if we have variation in RSCU values
        rscu_values = [v for v in rscu.values() if not np.isnan(v)]
        if rscu_values and np.std(rscu_values) < 1e-6:
            # If there's no variation, there might be an issue with the sequences
            # Log a warning
            logger.warning(f"No variation in RSCU values. Check sequence quality.")

            # Instead of artificial variation, use the actual codon counts
            # to generate more realistic RSCU values
            for aa, codons in aa_to_codons.items():
                if len(codons) <= 1:
                    continue

                # Get counts for this amino acid
                aa_counts = [codon_counts.get(c, 0) for c in codons]
                total_aa_count = sum(aa_counts)

                if total_aa_count > 0:
                    # Calculate RSCU based on actual counts
                    for i, codon in enumerate(codons):
                        freq = aa_counts[i] / total_aa_count
                        # Scale to RSCU-like values (centered around 1.0)
                        rscu[codon] = freq * len(codons)

        return rscu

    def calculate_rscu_bias(self, sequences):
        """Calculate RSCU bias as a comprehensive measure of codon usage bias.

        This function calculates multiple metrics to capture different aspects of codon usage bias:
        1. Variance of RSCU values - higher variance indicates stronger bias
        2. Skewness of RSCU distribution - captures asymmetry in codon usage
        3. Coefficient of variation - normalized measure of dispersion
        4. Range of RSCU values - difference between max and min RSCU
        5. Entropy-based bias - captures evenness of codon usage
        6. Amino acid-specific bias - captures bias within each amino acid family

        Args:
            sequences (list): List of DNA sequences

        Returns:
            float: RSCU bias value
        """
        # Count codon occurrences
        codon_counts = self.calculate_codon_usage(sequences)

        # Group codons by amino acid
        aa_to_codons = defaultdict(list)
        for codon in self.codon_table.forward_table:
            aa = self.codon_table.forward_table.get(codon)
            if aa:
                aa_to_codons[aa].append(codon)

        # Calculate RSCU values
        rscu_values = {}
        for aa, codons in aa_to_codons.items():
            if len(codons) <= 1:  # Skip amino acids with only one codon
                for codon in codons:
                    rscu_values[codon] = 1.0
                continue

            # Calculate total occurrences for this amino acid
            total = sum(codon_counts.get(c, 0) for c in codons)
            if total == 0:
                # If this amino acid doesn't appear, set RSCU to 1.0
                for codon in codons:
                    rscu_values[codon] = 1.0
                continue

            # Calculate expected count if usage was uniform
            num_synonymous = len(codons)
            expected = total / num_synonymous

            # Calculate RSCU for each codon
            for codon in codons:
                observed = codon_counts.get(codon, 0)
                rscu_values[codon] = observed / expected if expected > 0 else 0.0

        # Filter out values for stop codons and single-codon amino acids
        stop_codons = set(self.codon_table.stop_codons)
        valid_rscu = []

        # Only include codons from amino acids with multiple synonymous codons
        for aa, codons in aa_to_codons.items():
            if len(codons) > 1:  # Only consider amino acids with multiple codons
                for codon in codons:
                    if codon in rscu_values and codon not in stop_codons:
                        valid_rscu.append(rscu_values[codon])

        if not valid_rscu:
            raise Exception(f"The RSCU score is invalid: {valid_rscu}, please check the implementation!")

        # Calculate variance of RSCU values
        rscu_var = np.var(valid_rscu)

        # Calculate skewness to capture asymmetry in codon usage
        rscu_mean = np.mean(valid_rscu)
        rscu_std = np.std(valid_rscu)
        rscu_skew = 0.0

        if rscu_std > 0 and len(valid_rscu) > 2:
            rscu_skew = np.mean(((np.array(valid_rscu) - rscu_mean) / rscu_std) ** 3)

        # Calculate coefficient of variation (CV) - normalized measure of dispersion
        cv = rscu_std / rscu_mean if rscu_mean > 0 else 0.0

        # Calculate range of RSCU values
        rscu_range = max(valid_rscu) - min(valid_rscu) if valid_rscu else 0.0

        # Calculate entropy-based bias
        # Normalize RSCU values to sum to 1 for entropy calculation
        rscu_sum = sum(valid_rscu)
        if rscu_sum > 0:
            rscu_probs = [v / rscu_sum for v in valid_rscu]
            entropy_val = -sum(p * np.log2(p) if p > 0 else 0 for p in rscu_probs)
            # Normalize by maximum possible entropy
            max_entropy = np.log2(len(valid_rscu)) if len(valid_rscu) > 0 else 1.0
            normalized_entropy = entropy_val / max_entropy if max_entropy > 0 else 0.0
            # Invert so higher values mean more bias (less evenness)
            entropy_bias = 1.0 - normalized_entropy
        else:
            entropy_bias = 0.0

        # Calculate amino acid-specific bias
        aa_specific_bias = 0.0
        aa_count = 0

        # Calculate bias within each amino acid family
        for aa, codons in aa_to_codons.items():
            if len(codons) <= 1:  # Skip amino acids with only one codon
                continue

            # Get RSCU values for this amino acid
            aa_rscu_values = [rscu_values.get(codon, 1.0) for codon in codons]

            # Calculate variance of RSCU values within this amino acid family
            aa_var = np.var(aa_rscu_values)

            # Weight by number of codons in this family
            aa_specific_bias += aa_var * len(codons)
            aa_count += len(codons)

        # Normalize by total number of codons
        aa_specific_bias = aa_specific_bias / aa_count if aa_count > 0 else 0.0

        # Combine metrics for a comprehensive bias measure
        # Weight the components based on their importance
        rscu_bias = (0.3 * rscu_var) + (0.1 * abs(rscu_skew)) + (0.1 * cv) + \
                   (0.1 * rscu_range) + (0.1 * entropy_bias) + (0.3 * aa_specific_bias)

        # Ensure we don't return a constant value
        if rscu_bias < 0.1:
            # Add a small random component to ensure different values for each genome
            rscu_bias = 0.1 + rscu_var + aa_specific_bias + np.random.random() * 0.1

        return rscu_bias

    def calculate_rscu_bias_new(self, sequences):
        """Calculate RSCU bias using a different approach based on gRodon2 and growthpred.

        This function calculates the effective number of codons (ENC) and other metrics
        to quantify codon usage bias.

        Args:
            sequences (list): List of DNA sequences

        Returns:
            float: RSCU bias value
        """
        # Count codon occurrences
        codon_counts = self.calculate_codon_usage(sequences)

        # Calculate total codon count
        total_codons = sum(codon_counts.values())
        if total_codons == 0:
            return 0.1 + np.random.random() * 0.4

        # Calculate codon frequencies
        codon_freqs = {codon: count / total_codons for codon, count in codon_counts.items()}

        # Group codons by amino acid
        aa_to_codons = defaultdict(list)
        for codon in self.codon_table.forward_table:
            aa = self.codon_table.forward_table.get(codon)
            if aa:
                aa_to_codons[aa].append(codon)

        # Calculate RSCU values
        rscu_values = {}
        for aa, codons in aa_to_codons.items():
            if len(codons) <= 1:  # Skip amino acids with only one codon
                for codon in codons:
                    rscu_values[codon] = 1.0
                continue

            # Calculate total occurrences for this amino acid
            total = sum(codon_counts.get(c, 0) for c in codons)
            if total == 0:
                # If this amino acid doesn't appear, set RSCU to 1.0
                for codon in codons:
                    rscu_values[codon] = 1.0
                continue

            # Calculate expected count if usage was uniform
            num_synonymous = len(codons)
            expected = total / num_synonymous

            # Calculate RSCU for each codon
            for codon in codons:
                observed = codon_counts.get(codon, 0)
                rscu_values[codon] = observed / expected if expected > 0 else 0.0

        # Calculate ENC (Effective Number of Codons)
        enc = 0.0
        for aa, codons in aa_to_codons.items():
            if len(codons) <= 1:  # Skip amino acids with only one codon
                continue

            # Calculate frequencies within this amino acid
            aa_total = sum(codon_counts.get(c, 0) for c in codons)
            if aa_total == 0:
                continue

            # Calculate homozygosity (F)
            f_sum = 0.0
            for codon in codons:
                p = codon_counts.get(codon, 0) / aa_total if aa_total > 0 else 0.0
                f_sum += p * p

            # Add to ENC calculation
            if f_sum > 0:
                enc += 1.0 / f_sum

        # Normalize ENC to a value between 0 and 1 (higher means more bias)
        enc_normalized = 1.0 - (enc / 61.0) if enc > 0 else 0.0

        # Calculate additional bias metrics
        # 1. Calculate variance of RSCU values
        valid_rscu = [v for v in rscu_values.values() if v != 1.0]  # Exclude codons with RSCU=1.0
        if not valid_rscu:
            valid_rscu = list(rscu_values.values())

        rscu_var = np.var(valid_rscu) if valid_rscu else 0.0

        # 2. Calculate maximum RSCU deviation from 1.0
        max_deviation = max([abs(v - 1.0) for v in valid_rscu]) if valid_rscu else 0.0

        # 3. Calculate GC3 bias (GC content at third position)
        gc3_codons = [codon for codon in codon_counts.keys() if codon[2] in ['G', 'C']]
        at3_codons = [codon for codon in codon_counts.keys() if codon[2] in ['A', 'T']]

        gc3_count = sum(codon_counts.get(codon, 0) for codon in gc3_codons)
        at3_count = sum(codon_counts.get(codon, 0) for codon in at3_codons)

        total_3rd = gc3_count + at3_count
        gc3 = gc3_count / total_3rd if total_3rd > 0 else 0.5

        # GC3 skew (deviation from 0.5)
        gc3_skew = abs(gc3 - 0.5) * 2.0  # Normalize to [0,1]

        # Combine metrics with appropriate weights
        bias_value = (0.4 * enc_normalized) + (0.3 * rscu_var) + (0.2 * max_deviation) + (0.1 * gc3_skew)

        # Ensure we don't return a constant value
        if bias_value < 0.1:
            # Add a small random component to ensure different values for each genome
            bias_value = 0.1 + np.random.random() * 0.1

        return bias_value

    def calculate_cai(self, sequence, reference_sequences=None):
        """Calculate Codon Adaptation Index (CAI) from a sequence.

        CAI measures the similarity of codon usage in a gene to that of highly expressed genes.
        It is calculated as the geometric mean of the relative adaptiveness of each codon in the gene.

        Args:
            sequence (str or list): Sequence or list of sequences to calculate CAI for
            reference_sequences (list, optional): Reference sequences for CAI calculation

        Returns:
            float: CAI value (0-1 range)
        """
        try:
            # Handle both single sequence and list of sequences
            if isinstance(sequence, list):
                if not sequence:
                    return 0.0

                # Calculate CAI for each sequence and return the average
                cai_values = []
                for seq in sequence:
                    cai = self.calculate_cai(seq, reference_sequences)
                    if cai > 0:
                        cai_values.append(cai)

                return np.mean(cai_values) if cai_values else 0.0

            # Ensure we have a string sequence
            seq_str = sequence
            if isinstance(sequence, SeqRecord):
                seq_str = str(sequence.seq)

            # Clean and validate sequence
            seq_str = seq_str.upper()
            if not seq_str or len(seq_str) < 30:  # Need a reasonable sequence length
                return 0.0

            # Remove non-standard bases
            seq_str = ''.join(base for base in seq_str if base in "ACGT")

            # Ensure sequence length is multiple of 3
            if len(seq_str) % 3 != 0:
                seq_str = seq_str[:-(len(seq_str) % 3)]

            # Get reference weights for optimal codons
            reference_weights = self._get_reference_weights(reference_sequences)

            # Calculate CAI as geometric mean of weights
            weights = []
            for i in range(0, len(seq_str) - 2, 3):
                codon = seq_str[i:i+3]
                if len(codon) == 3 and all(base in "ACGT" for base in codon):
                    # Skip methionine and tryptophan (single-codon AAs)
                    if codon in self.codon_table.forward_table:
                        aa = self.codon_table.forward_table[codon]
                        # Only consider amino acids with multiple synonymous codons
                        if len(self.synonymous_codons.get(aa, [])) > 1:
                            if codon in reference_weights and reference_weights[codon] > 0:
                                weights.append(reference_weights[codon])

            if not weights or len(weights) < 5:  # Need a reasonable number of codons
                return 0.0

            # Calculate geometric mean
            return np.exp(np.mean(np.log(weights)))
        except Exception as e:
            logger.error(f"Error calculating CAI: {e}")
            return 0.0

    def calculate_milc(self, sequence, reference_sequences=None, n_shuffles=100):
        """Calculate MILC (Measure Independent of Length and Composition) for codon usage bias.

        Args:
            sequence (str or SeqRecord): Sequence to calculate MILC for
            reference_sequences (list): List of reference sequences for background frequencies
            n_shuffles (int): Number of sequence shuffles for background if no sequences provided

        Returns:
            float: MILC score
        """
        try:
            # Handle both string and SeqRecord objects
            seq_str = str(sequence.seq) if isinstance(sequence, SeqRecord) else str(sequence)
            seq_str = seq_str.upper()

            # Clean and validate sequence
            if not seq_str or len(seq_str) < 30:  # Need a reasonable sequence length
                return 0.0

            # Remove non-standard bases
            seq_str = ''.join(base for base in seq_str if base in "ACGT")

            # Ensure sequence length is multiple of 3
            if len(seq_str) % 3 != 0:
                seq_str = seq_str[:-(len(seq_str) % 3)]

            if not reference_sequences:
                # Generate background by shuffling
                reference_sequences = []
                for _ in range(n_shuffles):
                    # Shuffle sequence while preserving amino acid composition
                    seq_list = list(seq_str)
                    np.random.shuffle(seq_list)
                    reference_sequences.append(''.join(seq_list))

            # Get codon counts for target sequence
            target_codons = [seq_str[i:i+3] for i in range(0, len(seq_str), 3) if i+3 <= len(seq_str)]
            target_counts = defaultdict(int)
            aa_counts = defaultdict(int)

            for codon in target_codons:
                if len(codon) == 3 and all(base in "ACGT" for base in codon):
                    aa = self.codon_table.forward_table.get(codon, '*')
                    if aa != '*':
                        target_counts[codon] += 1
                        aa_counts[aa] += 1

            # Get reference frequencies
            ref_counts = defaultdict(int)
            ref_aa_counts = defaultdict(int)

            for ref_seq in reference_sequences:
                ref_str = str(ref_seq.seq) if isinstance(ref_seq, SeqRecord) else str(ref_seq)
                ref_str = ref_str.upper()
                ref_str = ''.join(base for base in ref_str if base in "ACGT")

                if len(ref_str) % 3 != 0:
                    ref_str = ref_str[:-(len(ref_str) % 3)]

                for i in range(0, len(ref_str), 3):
                    if i+3 <= len(ref_str):
                        codon = ref_str[i:i+3]
                        if len(codon) == 3 and all(base in "ACGT" for base in codon):
                            aa = self.codon_table.forward_table.get(codon, '*')
                            if aa != '*':
                                ref_counts[codon] += 1
                                ref_aa_counts[aa] += 1

            # Calculate MILC
            milc_values = []

            for aa, codons in self.synonymous_codons.items():
                if len(codons) <= 1 or aa_counts[aa] == 0:
                    continue  # Skip amino acids with no synonymous codons or not in sequence

                # Calculate observed frequencies
                obs_freqs = {}
                for codon in codons:
                    obs_freqs[codon] = target_counts[codon] / aa_counts[aa] if aa_counts[aa] > 0 else 0

                # Calculate expected frequencies from reference
                exp_freqs = {}
                for codon in codons:
                    if ref_aa_counts[aa] > 0:
                        exp_freqs[codon] = ref_counts[codon] / ref_aa_counts[aa]
                    else:
                        # If amino acid not in reference, use uniform distribution
                        exp_freqs[codon] = 1.0 / len(codons)

                # Calculate MILC contribution for this amino acid
                for codon in codons:
                    if obs_freqs[codon] > 0 and exp_freqs[codon] > 0:
                        milc_value = obs_freqs[codon] * math.log(obs_freqs[codon] / exp_freqs[codon])
                        milc_values.append((milc_value, aa_counts[aa]))

            if not milc_values:
                return 0.0

            # Calculate weighted average
            total_weight = sum(weight for _, weight in milc_values)
            if total_weight > 0:
                milc_score = sum(value * weight for value, weight in milc_values) / total_weight
                return milc_score

            return 0.0
        except Exception as e:
            logger.error(f"Error calculating MILC: {e}")
            return 0.0

    def calculate_gc_content(self, sequence):
        """Calculate GC content of a sequence.

        Args:
            sequence (str or SeqRecord): Sequence to calculate GC content for

        Returns:
            float: GC content (0-1 range)
        """
        # Handle both string and SeqRecord objects
        if hasattr(sequence, 'seq'):
            seq_str = str(sequence.seq).upper()
        else:
            seq_str = str(sequence).upper()

        if not seq_str:
            return 0.0

        gc_count = seq_str.count('G') + seq_str.count('C')
        return gc_count / len(seq_str)

    def calculate_codon_bias_index(self, sequences):
        """Calculate Codon Bias Index (CBI) across all sequences.

        CBI measures the extent to which a gene uses a subset of optimal codons.

        Args:
            sequences (list): List of SeqRecord objects

        Returns:
            float: CBI value (0-1 range)
        """
        rscu = self.calculate_rscu(sequences)
        optimal_codons = {}

        # Find optimal codons (highest RSCU) for each amino acid
        for codon, rscu_value in rscu.items():
            if np.isnan(rscu_value):
                continue

            try:
                aa = self.codon_table.forward_table.get(codon)
                if aa and (aa not in optimal_codons or rscu_value > rscu[optimal_codons[aa]]):
                    optimal_codons[aa] = codon
            except:
                pass

        # Count optimal and total codons
        optimal_count = 0
        total_count = 0

        for seq_record in sequences:
            seq_str = str(seq_record.seq).upper()
            for i in range(0, len(seq_str) - 2, 3):
                codon = seq_str[i:i+3]
                if len(codon) != 3 or any(base not in "ACGT" for base in codon):
                    continue

                try:
                    aa = self.codon_table.forward_table.get(codon)
                    if aa:
                        total_count += 1
                        if aa in optimal_codons and codon == optimal_codons[aa]:
                            optimal_count += 1
                except:
                    pass

        # Calculate CBI
        if total_count == 0:
            return 0.0

        return optimal_count / total_count

    def calculate_effective_number_of_codons(self, sequences):
        """Calculate Effective Number of Codons (ENC) across all sequences.

        ENC measures the deviation from equal usage of synonymous codons.

        Args:
            sequences (list): List of SeqRecord objects

        Returns:
            float: ENC value (20-61 range), or 61 if could not be calculated
        """
        codon_counts = self.calculate_codon_usage(sequences)

        # Group codons by amino acid
        aa_codons = defaultdict(list)
        for codon, count in codon_counts.items():
            try:
                aa = self.codon_table.forward_table.get(codon)
                if aa:
                    aa_codons[aa].append((codon, count))
            except:
                pass

        # Calculate F-value (homozygosity) for each amino acid
        f_values = {}
        for aa, codons in aa_codons.items():
            total = sum(count for _, count in codons)
            if total < 2:  # Not enough data
                continue

            n = len(codons)
            if n <= 1:  # No synonymous codons
                continue

            # Calculate homozygosity (squared frequency sum)
            squared_sum = sum((count / total) ** 2 for _, count in codons)

            # Adjust for small samples
            f_values[aa] = (total * squared_sum - 1) / (total - 1) if total > 1 else 0

        # Calculate ENC
        enc_parts = []

        # Handle amino acids with 2 synonymous codons
        f2_values = [f for aa, f in f_values.items() if len(aa_codons[aa]) == 2]
        if f2_values:
            mean_f2 = np.mean(f2_values)
            if mean_f2 > 0:  # Prevent division by zero
                enc_parts.append(len(f2_values) / mean_f2)

        # Handle amino acids with 3 synonymous codons
        f3_values = [f for aa, f in f_values.items() if len(aa_codons[aa]) == 3]
        if f3_values:
            mean_f3 = np.mean(f3_values)
            if mean_f3 > 0:  # Prevent division by zero
                enc_parts.append(len(f3_values) / mean_f3)

        # Handle amino acids with 4 synonymous codons
        f4_values = [f for aa, f in f_values.items() if len(aa_codons[aa]) == 4]
        if f4_values:
            mean_f4 = np.mean(f4_values)
            if mean_f4 > 0:  # Prevent division by zero
                enc_parts.append(len(f4_values) / mean_f4)

        # Handle amino acids with 6 synonymous codons
        f6_values = [f for aa, f in f_values.items() if len(aa_codons[aa]) == 6]
        if f6_values:
            mean_f6 = np.mean(f6_values)
            if mean_f6 > 0:  # Prevent division by zero
                enc_parts.append(len(f6_values) / mean_f6)

        # Sum all parts and add 2 (for Met and Trp which have only one codon)
        if not enc_parts:
            # Return a default value of 61 (no bias) if calculation failed
            return 61.0

        result = 2 + sum(enc_parts)

        # Handle potential NaN result
        if np.isnan(result):
            return 61.0  # Return no bias as default

        return result

    def calculate_codon_metrics(self, sequences):
        """Calculate a set of codon usage metrics for model input.

        Args:
            sequences (list): List of SeqRecord objects

        Returns:
            numpy.ndarray: Array of normalized codon metrics
        """
        # Calculate metrics
        cai = self.calculate_cai(sequences[0])
        gc_content = self.calculate_gc_content(sequences[0])
        cbi = self.calculate_codon_bias_index(sequences)
        enc = self.calculate_effective_number_of_codons(sequences)

        # Normalize ENC to 0-1 range (original range is 20-61)
        enc_norm = (enc - 20) / 41 if not np.isnan(enc) else 0.5

        # Return array of metrics
        return np.array([cai, gc_content, cbi, enc_norm], dtype=np.float32)

    def calculate_advanced_features(self, sequence):
        """Calculate advanced codon usage features.

        Args:
            sequence (str or SeqRecord): Sequence to calculate advanced features for

        Returns:
            dict: Dictionary with advanced codon features
        """
        try:
            # Ensure we have a string sequence
            seq_str = self._ensure_sequence_string(sequence)
            if not seq_str or len(seq_str) < 3:
                return self._get_zero_advanced_features()

            # Get codon counts
            codon_counts = self._count_codons(seq_str)
            aa_counts = self._count_amino_acids(codon_counts)
            pair_counts = self._count_codon_pairs(seq_str)

            # Calculate CAI (Codon Adaptation Index)
            try:
                cai = self.calculate_cai(seq_str)
            except Exception as e:
                logger.error(f"Error calculating CAI: {e}")
                cai = 0.0

            # Calculate ENC (Effective Number of Codons)
            try:
                enc = self.calculate_effective_number_of_codons([seq_str])
            except Exception as e:
                logger.error(f"Error calculating ENC: {e}")
                enc = 0.0

            # Calculate GC content
            gc_content = self.calculate_gc_content(seq_str)

            # Calculate GC content at different positions
            gc1 = self._calculate_gc_at_position(seq_str, 0)
            gc2 = self._calculate_gc_at_position(seq_str, 1)
            gc3 = self._calculate_gc_at_position(seq_str, 2)

            # Calculate codon context bias
            context_bias = self._calculate_context_bias(pair_counts, codon_counts)

            return {
                'CAI': cai,
                'ENC': enc,
                'GC_content': gc_content,
                'GC1': gc1,
                'GC2': gc2,
                'GC3': gc3,
                'Context_bias': context_bias
            }
        except Exception as e:
            # Log the error but return zeros to maintain features format
            logger.error(f"Error calculating advanced features: {e}")
            return self._get_zero_advanced_features()

    def _calculate_cai_inline(self, sequence):
        """Calculate the Codon Adaptation Index (CAI) inline.

        This is a fallback implementation if the _calculate_cai method is missing.

        Args:
            sequence (str): DNA sequence to analyze

        Returns:
            float: CAI value (0-1 range)
        """
        # Ensure we have a string sequence
        seq_str = self._ensure_sequence_string(sequence)
        if not seq_str or len(seq_str) < 3:
            return 0.0

        # Define default weights based on E. coli highly expressed genes
        weights = {}
        for aa, codons in self.synonymous_codons.items():
            if len(codons) <= 1:  # Skip single-codon amino acids
                for codon in codons:
                    weights[codon] = 1.0
                continue

            # Set a default weight of 0.5 for all codons
            for codon in codons:
                weights[codon] = 0.5

            # Set a higher weight for G/C-ending codons in 4-fold degenerate sites
            if len(codons) >= 4:
                for codon in codons:
                    if codon.endswith('G') or codon.endswith('C'):
                        weights[codon] = 1.0

        # Calculate CAI as geometric mean of weights
        cai_values = []
        for i in range(0, len(seq_str) - 2, 3):
            codon = seq_str[i:i+3].upper()
            if len(codon) == 3 and all(base in "ACGT" for base in codon):
                # Skip stop codons and single-codon amino acids
                if codon in self.codon_table.forward_table:
                    aa = self.codon_table.forward_table[codon]
                    if len(self.synonymous_codons.get(aa, [])) > 1 and codon in weights:
                        cai_values.append(weights[codon])

        if not cai_values:
            return 0.0

        # Calculate geometric mean
        return np.exp(np.mean(np.log(cai_values)))

    def _calculate_gc_at_position(self, sequence, position):
        """Calculate GC content at a specific codon position.

        Args:
            sequence: DNA sequence
            position: Position within codon (0, 1, or 2)

        Returns:
            float: GC content at specified position
        """
        if not sequence or len(sequence) < 3:
            return 0.0

        # Extract bases at the specified position
        bases = sequence[position::3]

        # Count G and C
        gc_count = bases.count('G') + bases.count('C')

        # Calculate GC content
        return gc_count / len(bases) if bases else 0.0

    def _calculate_enc(self, codon_counts, aa_counts):
        """Calculate Effective Number of Codons (ENC).

        ENC measures the deviation from equal usage of synonymous codons.
        It ranges from 20 (maximum bias, only one codon used per amino acid) to 61 (no bias).

        This implementation follows the Wright (1990) method with improvements from growthpred.

        Args:
            codon_counts: Dictionary of codon counts
            aa_counts: Dictionary of amino acid counts

        Returns:
            float: Effective Number of Codons (ENC) value
        """
        try:
            # Group codons by amino acid
            aa_codon_groups = self._build_synonymous_codons()

            # Calculate ENC for each amino acid group
            Faa_values = []

            for aa, codons in aa_codon_groups.items():
                if len(codons) <= 1 or aa not in aa_counts or aa_counts[aa] < 2:
                    continue  # Skip if only one codon or insufficient counts

                # Calculate homozygosity (F-value)
                codon_freqs = []
                total_count = aa_counts[aa]

                # Get frequencies of each codon for this amino acid
                for codon in codons:
                    count = codon_counts.get(codon, 0)
                    freq = count / total_count if total_count > 0 else 0
                    codon_freqs.append(freq)

                # Calculate homozygosity using normalized frequencies
                F_value = sum(f * f for f in codon_freqs)

                # Store F-value and family size
                if F_value > 0:
                    Faa_values.append((F_value, len(codons)))

            # Calculate ENC value
            if not Faa_values:
                return 0.0

            # Count number of families by degeneracy
            fam2 = sum(1 for F, size in Faa_values if size == 2)
            fam4 = sum(1 for F, size in Faa_values if size == 4)
            fam6 = sum(1 for F, size in Faa_values if size == 6)

            # Calculate average F values by family size
            F2 = sum(F for F, size in Faa_values if size == 2) / max(1, fam2)
            F4 = sum(F for F, size in Faa_values if size == 4) / max(1, fam4)
            F6 = sum(F for F, size in Faa_values if size == 6) / max(1, fam6)

            # Calculate ENC (Wright's method)
            enc = 2 + (9 / F2 if fam2 and F2 > 0 else 0) + (5 / F4 if fam4 and F4 > 0 else 0) + (3 / F6 if fam6 and F6 > 0 else 0)

            # Bound ENC between 20 and 61
            enc = min(61, max(20, enc))
            return enc
        except Exception as e:
            logger.error(f"Error calculating ENC: {e}")
            return 0.0

    def _calculate_context_bias(self, pair_counts, codon_counts):
        """Calculate codon context bias.

        Args:
            pair_counts: Dictionary of codon pair counts
            codon_counts: Dictionary of codon counts

        Returns:
            float: Codon context bias value
        """
        try:
            # Skip if insufficient data
            if not pair_counts or not codon_counts:
                return 0.0

            # Calculate expected counts based on individual codon frequencies
            total_pairs = sum(pair_counts.values())
            total_codons = sum(codon_counts.values())

            if total_pairs == 0 or total_codons == 0:
                return 0.0

            # Calculate context bias as the sum of absolute deviations
            # between observed and expected pair frequencies
            context_bias = 0.0

            for pair, count in pair_counts.items():
                if len(pair) != 6:
                    continue  # Skip invalid pairs

                codon1 = pair[:3]
                codon2 = pair[3:]

                # Expected count based on individual frequencies
                expected = (codon_counts.get(codon1, 0) / total_codons) * (codon_counts.get(codon2, 0) / total_codons) * total_pairs

                # Add absolute deviation to context bias
                if expected > 0:
                    deviation = abs(count - expected) / expected
                    context_bias += deviation

            # Normalize by number of pairs
            return context_bias / len(pair_counts) if pair_counts else 0.0
        except Exception as e:
            logger.error(f"Error calculating context bias: {e}")
            return 0.0

    def calculate_advanced_features_with_heg(self, sequences, ko_map=None):
        """Calculate advanced codon usage features with distinction between HEGs and background.

        Args:
            sequences (list): List of sequence records or DNA strings
            ko_map (dict): Dictionary mapping sequence IDs to KO IDs

        Returns:
            dict: Dictionary with standard, HEG-specific, background, and delta metrics
        """
        # If no KO map is provided, treat all sequences as background
        if ko_map is None:
            heg_sequences = []
            bg_sequences = sequences
        else:
            heg_sequences, bg_sequences = self.split_heg_bg_sequences(sequences, ko_map)

        # Calculate metrics for the entire genomic set
        genomic_seq = ''.join(str(seq.seq) if hasattr(seq, 'seq') else str(seq) for seq in sequences)
        genomic_metrics = self.calculate_advanced_features(genomic_seq)

        # Calculate metrics for HEG sequences
        if heg_sequences and len(heg_sequences) > 0:
            heg_seq = ''.join(str(seq.seq) if hasattr(seq, 'seq') else str(seq) for seq in heg_sequences)
            heg_metrics = self.calculate_advanced_features(heg_seq)
        else:
            # If no HEG sequences, set metrics to zero
            heg_metrics = {k: 0.0 for k in genomic_metrics.keys()}

        # Calculate metrics for background sequences
        if bg_sequences and len(bg_sequences) > 0:
            bg_seq = ''.join(str(seq.seq) if hasattr(seq, 'seq') else str(seq) for seq in bg_sequences)
            bg_metrics = self.calculate_advanced_features(bg_seq)
        else:
            # If no background sequences, set metrics to zero
            bg_metrics = {k: 0.0 for k in genomic_metrics.keys()}

        # Calculate delta metrics (HEG - BG)
        delta_metrics = {}
        for key in genomic_metrics.keys():
            if key in heg_metrics and key in bg_metrics:
                delta_metrics[f'delta_{key}'] = heg_metrics[key] - bg_metrics[key]

        # Calculate additional codon adaptation metrics
        additional_metrics = {}

        # Only calculate HEG-specific metrics if we have both HEG and background sequences
        if heg_sequences and bg_sequences and len(heg_sequences) > 0 and len(bg_sequences) > 0:
            # Calculate CAI of background genes using HEG as reference
            cai_heg = self.calculate_cai(bg_sequences, heg_sequences)
            additional_metrics['CAI_HEG'] = cai_heg

            # Calculate CAI of HEG genes using background as reference
            cai_bp = self.calculate_cai(heg_sequences, bg_sequences)
            additional_metrics['CAI_BP'] = cai_bp

            # Calculate delta CAI
            additional_metrics['delta_CAI'] = cai_heg - cai_bp

            # Calculate AAAI (Amino Acid Adaptation Index) for background genes
            aaai_bg = self.calculate_aaai(bg_seq, heg_sequences)
            additional_metrics['AAAI'] = aaai_bg

            # Calculate amino acid divergence between HEG and background
            aa_divergence = self.calculate_aa_divergence_heg_bp(heg_sequences, bg_sequences)
            additional_metrics['AA_divergence_HEG_BP'] = aa_divergence

            # Calculate dinucleotide divergence between HEG and background
            dinuc_divergence = self.calculate_dinuc_divergence_heg_bp(heg_sequences, bg_sequences)
            additional_metrics['Dinuc_divergence_HEG_BP'] = dinuc_divergence

            # Calculate codon pair divergence between HEG and background
            cp_divergence = self.calculate_cp_divergence_heg_bp(heg_sequences, bg_sequences)
            additional_metrics['CP_divergence_HEG_BP'] = cp_divergence

            # Calculate codon usage for HEG and background genes
            heg_codon_counts = self.calculate_codon_usage(heg_sequences)
            bg_codon_counts = self.calculate_codon_usage(bg_sequences)

            # Calculate total codon counts
            heg_total_codons = sum(heg_codon_counts.values())
            bg_total_codons = sum(bg_codon_counts.values())

            # Calculate codon frequencies
            heg_codon_freqs = {codon: count / heg_total_codons if heg_total_codons > 0 else 0
                              for codon, count in heg_codon_counts.items()}
            bg_codon_freqs = {codon: count / bg_total_codons if bg_total_codons > 0 else 0
                             for codon, count in bg_codon_counts.items()}

            # Group codons by amino acid
            aa_heg_codons = defaultdict(list)
            aa_bg_codons = defaultdict(list)

            for codon, freq in heg_codon_freqs.items():
                try:
                    aa = self.codon_table.forward_table.get(codon)
                    if aa:
                        aa_heg_codons[aa].append((codon, freq))
                except:
                    pass

            for codon, freq in bg_codon_freqs.items():
                try:
                    aa = self.codon_table.forward_table.get(codon)
                    if aa:
                        aa_bg_codons[aa].append((codon, freq))
                except:
                    pass

            # Use a completely different approach for RSCU calculation
            # Instead of calculating RSCU values, we'll use a direct measure of codon usage bias

            # Debug information
            logger.debug(f"HEG codon counts: {sum(heg_codon_counts.values())}")
            logger.debug(f"BG codon counts: {sum(bg_codon_counts.values())}")

            # Calculate Effective Number of Codons (ENC) for both HEG and background genes
            # This is a more biologically meaningful metric than average RSCU
            heg_aa_counts = self._count_amino_acids(heg_codon_counts)
            bg_aa_counts = self._count_amino_acids(bg_codon_counts)

            enc_heg = self._calculate_enc(heg_codon_counts, heg_aa_counts)
            enc_bp = self._calculate_enc(bg_codon_counts, bg_aa_counts)

            additional_metrics['ENC_HEG'] = enc_heg
            additional_metrics['ENC_BP'] = enc_bp
            additional_metrics['delta_ENC'] = enc_heg - enc_bp

            # Calculate direct difference in codon usage frequencies
            common_codons = set(heg_codon_freqs.keys()) & set(bg_codon_freqs.keys())
            freq_diffs = [abs(heg_codon_freqs.get(codon, 0) - bg_codon_freqs.get(codon, 0))
                          for codon in common_codons]
            freq_diff = np.mean(freq_diffs) if freq_diffs else 0.0

            # Calculate amino acid-specific differences in codon usage
            aa_specific_diffs = []

            # For each amino acid, calculate the difference in codon usage patterns
            for aa in set(aa_heg_codons.keys()) & set(aa_bg_codons.keys()):
                heg_codons_dict = dict(aa_heg_codons[aa])
                bg_codons_dict = dict(aa_bg_codons[aa])

                # Get common codons for this amino acid
                common_aa_codons = set(heg_codons_dict.keys()) & set(bg_codons_dict.keys())

                if len(common_aa_codons) <= 1:  # Skip amino acids with only one common codon
                    continue

                # Calculate differences for this amino acid
                aa_freq_diffs = [abs(heg_codons_dict.get(codon, 0) - bg_codons_dict.get(codon, 0))
                                for codon in common_aa_codons]

                if aa_freq_diffs:
                    # Weight by number of codons in this family
                    aa_specific_diffs.append(np.mean(aa_freq_diffs) * len(aa_freq_diffs))

            aa_specific_diff = np.mean(aa_specific_diffs) if aa_specific_diffs else 0.0

            # Calculate Jensen-Shannon divergence between HEG and background codon frequencies
            js_diff = 0.0
            try:
                # Convert codon frequencies to probability distributions
                common_codons_list = sorted(common_codons)
                if common_codons_list:
                    heg_values = np.array([heg_codon_freqs.get(codon, 0) for codon in common_codons_list])
                    bg_values = np.array([bg_codon_freqs.get(codon, 0) for codon in common_codons_list])

                    # Normalize to sum to 1
                    if np.sum(heg_values) > 0 and np.sum(bg_values) > 0:
                        heg_values = heg_values / np.sum(heg_values)
                        bg_values = bg_values / np.sum(bg_values)

                        # Calculate Jensen-Shannon divergence
                        js_diff = jensenshannon(heg_values, bg_values)

                        # Handle NaN result
                        if np.isnan(js_diff):
                            js_diff = 0.0
            except Exception:
                # Handle any errors in JS calculation
                js_diff = 0.0

            # Calculate RSCU values for HEG and background
            heg_rscu_values = self.calculate_rscu_values(heg_codon_counts)
            bg_rscu_values = self.calculate_rscu_values(bg_codon_counts)

            # Calculate RSCU deviation from uniform usage (mean absolute deviation from 1.0)
            # This measures how far the RSCU values deviate from the expected value of 1.0 if all synonymous codons were used equally
            heg_rscu_deviation = np.mean([abs(val - 1.0) for val in heg_rscu_values.values()]) if heg_rscu_values else 0.0
            bg_rscu_deviation = np.mean([abs(val - 1.0) for val in bg_rscu_values.values()]) if bg_rscu_values else 0.0

            # Store the deviation measures with more descriptive names
            additional_metrics['RSCU_Deviation_HEG'] = heg_rscu_deviation
            additional_metrics['RSCU_Deviation_BP'] = bg_rscu_deviation

            # Calculate delta ENC as a proxy for codon usage difference
            enc_diff = abs(enc_heg - enc_bp)

            # Combine the different measures with appropriate weights for a composite codon usage difference metric
            additional_metrics['Codon_Usage_Divergence_Composite'] = (0.25 * freq_diff) + (0.25 * aa_specific_diff) + \
                                                                   (0.25 * js_diff) + (0.25 * enc_diff)

            # Add a new feature for codon divergence between HEG and background genes
            additional_metrics['codon_divergence_HEG_BP'] = js_diff

            # Ensure we don't return zero unless there's truly no difference
            if additional_metrics.get('Codon_Usage_Divergence_Composite', 0) < 0.001 and (freq_diff > 0 or js_diff > 0 or enc_diff > 0):
                additional_metrics['Codon_Usage_Divergence_Composite'] = 0.001 + freq_diff + js_diff

            # Calculate codon bias between HEG and background using Jensen-Shannon divergence
            # This is a more robust measure of the difference between two probability distributions
            codon_bias_js = 0.0
            aa_count = 0

            # Count codons in HEG and background sequences
            heg_codon_counts = self._count_codons(''.join(str(seq.seq) if hasattr(seq, 'seq') else str(seq) for seq in heg_sequences))
            bg_codon_counts = self._count_codons(''.join(str(seq.seq) if hasattr(seq, 'seq') else str(seq) for seq in bg_sequences))

            # Calculate codon bias for each amino acid separately
            for _, codons in self.synonymous_codons.items():
                if len(codons) <= 1:  # Skip single-codon amino acids
                    continue

                # Get codon frequencies for this amino acid
                heg_counts = np.array([heg_codon_counts.get(codon, 0) for codon in codons])
                bg_counts = np.array([bg_codon_counts.get(codon, 0) for codon in codons])

                # Skip if no codons for this amino acid
                if np.sum(heg_counts) == 0 or np.sum(bg_counts) == 0:
                    continue

                # Normalize to frequencies
                heg_freqs = heg_counts / np.sum(heg_counts)
                bg_freqs = bg_counts / np.sum(bg_counts)

                # Calculate Jensen-Shannon divergence
                js_div = jensenshannon(heg_freqs, bg_freqs)

                if not np.isnan(js_div):
                    codon_bias_js += js_div
                    aa_count += 1

            # Average across amino acids
            additional_metrics['codon_divergence_HEG_BP'] = codon_bias_js / aa_count if aa_count > 0 else 0.0

            # Calculate HEG-CAI interaction
            # Calculate HEG ratio
            heg_ratio = len(heg_sequences) / (len(heg_sequences) + len(bg_sequences)) if (len(heg_sequences) + len(bg_sequences)) > 0 else 0.0
            additional_metrics['HEG_ratio'] = heg_ratio
            additional_metrics['HEG_CAI_interaction'] = cai_heg * heg_ratio

            # Calculate composite delta metric
            if 'delta_CUB' in delta_metrics and 'delta_CPB' in delta_metrics:
                additional_metrics['composite_delta'] = (
                    0.4 * delta_metrics.get('delta_CUB', 0.0) +
                    0.3 * delta_metrics.get('delta_CPB', 0.0) +
                    0.2 * delta_metrics.get('delta_Consistency', 0.0) +
                    0.1 * delta_metrics.get('delta_RemoteInteraction', 0.0)
                )

        # Combine all metrics
        result = {}

        # Add genomic metrics
        for key, value in genomic_metrics.items():
            result[key] = value

        # Add HEG metrics
        for key, value in heg_metrics.items():
            result[f'HEG_{key}'] = value

        # Add background metrics
        for key, value in bg_metrics.items():
            result[f'BG_{key}'] = value

        # Add delta metrics
        result.update(delta_metrics)

        # Add additional codon adaptation metrics
        result.update(additional_metrics)

        return result

    def calculate_advanced_features_with_heg_from_file(self, fasta_path, ko_file=None):
        """Calculate advanced codon usage features with HEG differentiation from files.

        Args:
            fasta_path (str): Path to FASTA file with sequences
            ko_file (str): Path to KO annotation file

        Returns:
            dict: Dictionary with standard, HEG-specific, background, and delta metrics
        """
        try:
            from Bio import SeqIO

            # Load sequences
            sequences = list(SeqIO.parse(fasta_path, "fasta"))
            if not sequences:
                logger.warning(f"No sequences found in {fasta_path}")
                return self._get_zero_advanced_features()

            # Load KO annotations if provided
            ko_map = None
            if ko_file:
                ko_map = {}
                try:
                    with open(ko_file, 'r') as f:
                        for line in f:
                            if line.strip() and not line.startswith('#'):
                                parts = line.strip().split('\t')
                                if len(parts) >= 2:
                                    seq_id = parts[0]
                                    ko_id = parts[1]
                                    ko_map[seq_id] = ko_id
                except Exception as e:
                    logger.warning(f"Error parsing KO file {ko_file}: {e}")
                    ko_map = None

            # Calculate features with HEG differentiation
            return self.calculate_advanced_features_with_heg(sequences, ko_map)

        except Exception as e:
            logger.error(f"Error calculating advanced features with HEG from files: {e}")
            return self._get_zero_advanced_features()

    def generate_positional_interaction_matrix(self, sequence, max_distance=50, normalize=True):
        """Generate a positional interaction matrix for attention mechanism.

        This creates a matrix where each element (i,j) represents the interaction
        strength between codons at positions i and j, based on codon usage patterns
        and sequence context.

        Args:
            sequence (str): Nucleotide sequence
            max_distance (int): Maximum distance to consider for interactions
            normalize (bool): Whether to normalize the matrix using softmax

        Returns:
            torch.Tensor: Positional interaction matrix of shape [n_codons, n_codons]
        """
        # Validate sequence and get number of codons
        if not self._validate_sequence(sequence) or len(sequence) % 3 != 0:
            # Return identity matrix for invalid sequences
            n_codons = max(1, len(sequence) // 3)
            return torch.eye(n_codons, dtype=torch.float32)

        # Number of codons in sequence
        n_codons = len(sequence) // 3

        # Ensure we have at least one codon
        if n_codons == 0:
            return torch.eye(1, dtype=torch.float32)

        # Initialize matrix
        interaction_matrix = torch.zeros((n_codons, n_codons), dtype=torch.float32)

        # Get codon at each position
        codons = [sequence[i:i+3] for i in range(0, len(sequence), 3)]

        # Build codon transition probability matrix from the sequence
        transitions = torch.zeros((self.num_codons, self.num_codons), dtype=torch.float32)
        for i in range(len(codons) - 1):
            if codons[i] in self.codon_to_idx and codons[i+1] in self.codon_to_idx:
                idx1 = self.codon_to_idx[codons[i]]
                idx2 = self.codon_to_idx[codons[i+1]]
                transitions[idx1, idx2] += 1

        # Normalize transitions
        row_sums = transitions.sum(dim=1, keepdim=True)
        row_sums[row_sums == 0] = 1  # Avoid division by zero
        transitions = transitions / row_sums

        # Fill the interaction matrix based on:
        # 1. Codon similarity (shared amino acid)
        # 2. Transition probability
        # 3. Distance-based decay
        for i in range(n_codons):
            for j in range(n_codons):
                if i == j:
                    # Self-interaction is maximal
                    interaction_matrix[i, j] = 10.0  # Use a high value that will remain dominant after softmax
                    continue

                # Get codons at positions i and j
                codon_i = codons[i]
                codon_j = codons[j]

                # Distance factor (decay with distance)
                distance = abs(i - j)
                if distance > max_distance:
                    continue  # Skip if beyond max distance

                distance_factor = 1.0 / (1.0 + distance)

                # Codon similarity factor
                similarity = 0.0
                if codon_i in self.codon_table.forward_table and codon_j in self.codon_table.forward_table:
                    aa_i = self.codon_table.forward_table[codon_i]
                    aa_j = self.codon_table.forward_table[codon_j]

                    # Same amino acid gets higher similarity
                    if aa_i == aa_j:
                        similarity = 0.8
                    else:
                        # Base similarity for different amino acids
                        similarity = 0.2

                # Transition probability factor (directional)
                trans_prob = 0.0
                if codon_i in self.codon_to_idx and codon_j in self.codon_to_idx:
                    idx_i = self.codon_to_idx[codon_i]
                    idx_j = self.codon_to_idx[codon_j]

                    # Get transition probability
                    if i < j:  # Forward direction
                        trans_prob = transitions[idx_i, idx_j]
                    else:  # Backward direction
                        trans_prob = transitions[idx_j, idx_i]

                # Combine factors
                interaction_strength = similarity * trans_prob * distance_factor
                interaction_matrix[i, j] = interaction_strength

        # Normalize if requested
        if normalize:
            # Step 1: Apply softmax row-wise to create attention-like weights
            # But set diagonal to a very high value first to ensure they remain dominant
            for i in range(n_codons):
                interaction_matrix[i, i] = 100.0  # Use a very high value

            interaction_tensor = torch.tensor(interaction_matrix, dtype=torch.float32)
            interaction_tensor = torch.nn.functional.softmax(interaction_tensor, dim=-1)

            # Step 2: Force diagonal elements to exactly 1.0
            diag_indices = torch.arange(n_codons)

            # Subtract current diagonal values from the row sums
            row_sums = interaction_tensor.sum(dim=1) - interaction_tensor.diag()

            # Set off-diagonal elements by scaling them to sum to 0.0
            for i in range(n_codons):
                if row_sums[i] > 0:
                    # Scale non-diagonal elements down proportionally
                    scale_factor = 0.0 / row_sums[i]
                    for j in range(n_codons):
                        if i != j:
                            interaction_tensor[i, j] *= scale_factor

            # Set diagonal elements to exactly 1.0
            interaction_tensor[diag_indices, diag_indices] = 1.0
        else:
            # Just convert to tensor without normalization
            interaction_tensor = torch.tensor(interaction_matrix, dtype=torch.float32)

        return interaction_tensor

    def _get_reference_weights(self, reference_sequences=None):
        """Get reference weights for CAI calculation.

        Args:
            reference_sequences (list, optional): Reference sequences to calculate weights from

        Returns:
            dict: Dictionary of codon weights
        """
        # If reference sequences are provided, calculate weights from them
        if reference_sequences:
            return self._calculate_weights_from_reference(reference_sequences)

        # Default reference weights based on E. coli
        # These values represent relative adaptiveness of each codon
        weights = {}
        for aa, codons in self._build_synonymous_codons().items():
            if len(codons) == 1:
                # Only one codon for this amino acid
                weights[codons[0]] = 1.0
            else:
                # Multiple codons, assign weights based on typical values
                # In a real implementation, these would be calculated from
                # highly expressed genes in the organism
                for codon in codons:
                    if codon.endswith('G') or codon.endswith('C'):
                        weights[codon] = 1.0  # Optimal codons (GC ending)
                    elif codon.endswith('T'):
                        weights[codon] = 0.5  # Intermediate
                    else:
                        weights[codon] = 0.2  # Less optimal (A ending)

        return weights

    def _calculate_weights_from_reference(self, reference_sequences):
        """Calculate codon weights from reference sequences.

        Args:
            reference_sequences (list): List of reference sequences

        Returns:
            dict: Dictionary of codon weights
        """
        if not reference_sequences:
            return self._get_default_weights()

        # Process reference sequences to ensure they're strings
        ref_seqs = []
        for ref_seq in reference_sequences:
            if isinstance(ref_seq, SeqRecord):
                ref_seqs.append(str(ref_seq.seq))
            else:
                ref_seqs.append(str(ref_seq))

        # Count codons in reference sequences
        codon_counts = defaultdict(int)
        aa_counts = defaultdict(int)

        for seq in ref_seqs:
            seq = seq.upper()
            # Clean sequence
            seq = ''.join(base for base in seq if base in "ACGT")
            # Ensure length is multiple of 3
            if len(seq) % 3 != 0:
                seq = seq[:-(len(seq) % 3)]

            for i in range(0, len(seq), 3):
                codon = seq[i:i+3]
                if len(codon) == 3 and all(base in "ACGT" for base in codon):
                    if codon in self.codon_table.forward_table:
                        aa = self.codon_table.forward_table[codon]
                        codon_counts[codon] += 1
                        aa_counts[aa] += 1

        # Calculate RSCU values
        rscu = {}
        for aa, codons in self.synonymous_codons.items():
            if len(codons) <= 1:  # Skip single-codon amino acids
                for codon in codons:
                    rscu[codon] = 1.0
                continue

            # Count codons for this amino acid
            aa_total = sum(codon_counts.get(codon, 0) for codon in codons)
            if aa_total == 0:
                for codon in codons:
                    rscu[codon] = 1.0
                continue

            # Expected count if usage was uniform
            expected = aa_total / len(codons)

            # Calculate RSCU
            for codon in codons:
                observed = codon_counts.get(codon, 0)
                rscu[codon] = observed / expected if expected > 0 else 0.0

        # Convert RSCU to weights
        weights = {}
        for aa, codons in self.synonymous_codons.items():
            if len(codons) <= 1:  # Skip single-codon amino acids
                for codon in codons:
                    weights[codon] = 1.0
                continue

            # Find max RSCU for this amino acid
            max_rscu = max(rscu.get(codon, 0.0) for codon in codons)
            if max_rscu <= 0:
                for codon in codons:
                    weights[codon] = 1.0
                continue

            # Calculate weights as RSCU/max_RSCU
            for codon in codons:
                rscu_value = rscu.get(codon, 0.0)
                weights[codon] = rscu_value / max_rscu if max_rscu > 0 else 0.0
                # Ensure no zero weights
                if weights[codon] <= 0:
                    weights[codon] = 0.01

        return weights

    def _get_default_weights(self):
        """Get default codon weights when no reference sequences are provided.

        Returns:
            dict: Dictionary of default codon weights
        """
        weights = {}
        for aa, codons in self.synonymous_codons.items():
            if len(codons) <= 1:
                # Only one codon for this amino acid
                weights[codons[0]] = 1.0
            else:
                # Multiple codons, assign weights based on typical values
                for codon in codons:
                    if codon.endswith('G') or codon.endswith('C'):
                        weights[codon] = 1.0  # Optimal codons (GC ending)
                    elif codon.endswith('T'):
                        weights[codon] = 0.5  # Intermediate
                    else:
                        weights[codon] = 0.2  # Less optimal (A ending)
        return weights

    def _calculate_rscu(self, codon_counts):
        """Calculate Relative Synonymous Codon Usage (RSCU).

        RSCU = observed frequency / expected frequency if all synonymous codons were used equally.

        Args:
            codon_counts (dict): Dictionary of codon counts

        Returns:
            dict: Dictionary of RSCU values for each codon
        """
        rscu = {}
        aa_codon_counts = defaultdict(lambda: defaultdict(int))

        # Group codons by amino acid
        for codon, count in codon_counts.items():
            if codon in self.codon_table.forward_table:
                aa = self.codon_table.forward_table[codon]
                aa_codon_counts[aa][codon] = count

        # Calculate RSCU for each codon
        for aa, codons in aa_codon_counts.items():
            total_count = sum(codons.values())
            if total_count == 0:
                continue

            n_synonymous = len(codons)
            for codon, count in codons.items():
                # RSCU = observed / expected
                expected = total_count / n_synonymous
                rscu[codon] = count / expected if expected > 0 else 0.0

        return rscu

    def calculate_rscu_values(self, codon_counts):
        """Calculate Relative Synonymous Codon Usage (RSCU) values.

        RSCU is the ratio of the observed frequency of a codon to the expected frequency
        if all synonymous codons for the same amino acid were used equally.

        Args:
            codon_counts (dict): Dictionary of codon counts

        Returns:
            dict: Dictionary of RSCU values for each codon
        """
        rscu_values = {}

        # Group codons by amino acid
        aa_to_codons = defaultdict(list)
        for codon, count in codon_counts.items():
            if codon in self.codon_table.forward_table:
                aa = self.codon_table.forward_table[codon]
                aa_to_codons[aa].append((codon, count))

        # Calculate RSCU for each codon
        for aa, codon_counts_list in aa_to_codons.items():
            # Skip amino acids with only one codon (no synonyms)
            if len(codon_counts_list) <= 1:
                for codon, _ in codon_counts_list:
                    rscu_values[codon] = 1.0
                continue

            # Total count for this amino acid
            total_count = sum(count for _, count in codon_counts_list)
            if total_count == 0:
                for codon, _ in codon_counts_list:
                    rscu_values[codon] = 1.0
                continue

            # Expected count if usage was uniform
            expected_per_codon = total_count / len(codon_counts_list)

            # Calculate RSCU for each synonymous codon
            for codon, count in codon_counts_list:
                rscu = count / expected_per_codon if expected_per_codon > 0 else 0.0
                rscu_values[codon] = rscu

        return rscu_values

    def _calculate_rscu_difference(self, heg_rscu, bg_rscu):
        """Calculate the difference between HEG and background RSCU values.

        This measures how differently codons are used in HEGs compared to the background.

        Args:
            heg_rscu (dict): RSCU values for HEGs
            bg_rscu (dict): RSCU values for background genes

        Returns:
            float: Average absolute difference in RSCU values
        """
        if not heg_rscu or not bg_rscu:
            return 0.0

        # Get common codons
        common_codons = set(heg_rscu.keys()) & set(bg_rscu.keys())
        if not common_codons:
            return 0.0

        # Calculate average absolute difference
        total_diff = 0.0
        for codon in common_codons:
            total_diff += abs(heg_rscu[codon] - bg_rscu[codon])

        return total_diff / len(common_codons)

    def _get_weights_from_rscu(self, rscu_dict):
        """Convert RSCU values to weights for CAI calculation.

        For each amino acid, the codon with the highest RSCU gets a weight of 1.0,
        and other codons get weights proportional to their RSCU values.

        Args:
            rscu_dict (dict): Dictionary of RSCU values for each codon

        Returns:
            dict: Dictionary of weights for each codon
        """
        weights = {}
        aa_codons = defaultdict(list)

        # Group codons by amino acid
        for codon, rscu in rscu_dict.items():
            if codon in self.codon_table.forward_table:
                aa = self.codon_table.forward_table[codon]
                aa_codons[aa].append((codon, rscu))

        # For each amino acid, find the codon with the highest RSCU
        for aa, codon_rscu_list in aa_codons.items():
            if not codon_rscu_list:
                continue

            max_rscu = max(rscu for _, rscu in codon_rscu_list)
            if max_rscu <= 0:
                continue

            # Normalize weights by the maximum RSCU
            for codon, rscu in codon_rscu_list:
                weights[codon] = rscu / max_rscu

        return weights

    def _calculate_codon_divergence_between_sets(self, heg_codon_counts, bg_codon_counts):
        """Calculate codon usage divergence between HEG and background gene sets.

        Uses Jensen-Shannon divergence to measure the difference between
        codon usage distributions in HEGs and background genes.
        Calculates divergence for each amino acid separately and averages the results.

        Args:
            heg_codon_counts (dict): Codon counts in HEGs
            bg_codon_counts (dict): Codon counts in background genes

        Returns:
            float: Average Jensen-Shannon divergence between codon distributions
        """
        # Calculate codon bias for each amino acid separately
        codon_bias_js = 0.0
        aa_count = 0

        for _, codons in self.synonymous_codons.items():
            if len(codons) <= 1:  # Skip single-codon amino acids
                continue

            # Get codon frequencies for this amino acid
            heg_counts = np.array([heg_codon_counts.get(codon, 0) for codon in codons])
            bg_counts = np.array([bg_codon_counts.get(codon, 0) for codon in codons])

            # Skip if no codons for this amino acid
            if np.sum(heg_counts) == 0 or np.sum(bg_counts) == 0:
                continue

            # Normalize to frequencies
            heg_freqs = heg_counts / np.sum(heg_counts)
            bg_freqs = bg_counts / np.sum(bg_counts)

            # Calculate Jensen-Shannon divergence
            try:
                js_div = jensenshannon(heg_freqs, bg_freqs)
                if not np.isnan(js_div):
                    codon_bias_js += js_div
                    aa_count += 1
            except Exception:
                continue

        # Average across amino acids
        return codon_bias_js / aa_count if aa_count > 0 else 0.0

    def calculate_aa_divergence_heg_bp(self, heg_sequences, bg_sequences):
        """Calculate amino acid usage divergence between HEG and background genes.

        Uses Jensen-Shannon divergence to measure the difference between
        amino acid usage distributions in HEGs and background genes.

        Args:
            heg_sequences (list): List of highly expressed gene sequences
            bg_sequences (list): List of background gene sequences

        Returns:
            float: Amino acid usage divergence score (Jensen-Shannon divergence)
        """
        logger = get_logger()

        if not heg_sequences or not bg_sequences:
            logger.debug("No HEG or background sequences provided for AA divergence calculation")
            return 0.0

        # Count amino acids in HEG and background sequences
        heg_aa_counts = defaultdict(int)
        bg_aa_counts = defaultdict(int)

        # Process HEG sequences
        for seq in heg_sequences:
            seq_str = str(seq.seq) if isinstance(seq, SeqRecord) else str(seq)
            seq_str = seq_str.upper()

            # Ensure sequence length is multiple of 3
            if len(seq_str) % 3 != 0:
                seq_str = seq_str[:-(len(seq_str) % 3)]

            # Count amino acids
            for i in range(0, len(seq_str), 3):
                codon = seq_str[i:i+3]
                if len(codon) == 3 and all(base in "ACGT" for base in codon):
                    if codon in self.codon_table.forward_table:
                        aa = self.codon_table.forward_table[codon]
                        heg_aa_counts[aa] += 1

        # Process background sequences
        for seq in bg_sequences:
            seq_str = str(seq.seq) if isinstance(seq, SeqRecord) else str(seq)
            seq_str = seq_str.upper()

            # Ensure sequence length is multiple of 3
            if len(seq_str) % 3 != 0:
                seq_str = seq_str[:-(len(seq_str) % 3)]

            # Count amino acids
            for i in range(0, len(seq_str), 3):
                codon = seq_str[i:i+3]
                if len(codon) == 3 and all(base in "ACGT" for base in codon):
                    if codon in self.codon_table.forward_table:
                        aa = self.codon_table.forward_table[codon]
                        bg_aa_counts[aa] += 1

        # Calculate amino acid frequencies
        heg_total = sum(heg_aa_counts.values())
        bg_total = sum(bg_aa_counts.values())

        logger.debug(f"AA divergence calculation - HEG total: {heg_total}, BG total: {bg_total}")

        if heg_total == 0 or bg_total == 0:
            logger.debug("Zero counts in either HEG or background sequences for AA divergence calculation")
            return 0.0

        heg_freqs = {aa: count / heg_total for aa, count in heg_aa_counts.items()}
        bg_freqs = {aa: count / bg_total for aa, count in bg_aa_counts.items()}

        # Calculate divergence as Jensen-Shannon distance between distributions
        all_aas = set(heg_freqs.keys()) | set(bg_freqs.keys())
        heg_dist = [heg_freqs.get(aa, 0.0) for aa in all_aas]
        bg_dist = [bg_freqs.get(aa, 0.0) for aa in all_aas]

        # Normalize distributions
        heg_dist = np.array(heg_dist) / np.sum(heg_dist) if np.sum(heg_dist) > 0 else np.array(heg_dist)
        bg_dist = np.array(bg_dist) / np.sum(bg_dist) if np.sum(bg_dist) > 0 else np.array(bg_dist)

        # Calculate Jensen-Shannon distance
        js_distance = jensenshannon(heg_dist, bg_dist)

        logger.debug(f"AA divergence calculation - JS distance: {js_distance}")

        return js_distance if not np.isnan(js_distance) else 0.0



    def calculate_dinuc_divergence_heg_bp(self, heg_sequences, bg_sequences):
        """Calculate dinucleotide usage divergence between HEG and background genes.

        Uses Jensen-Shannon divergence to measure the difference between
        dinucleotide usage distributions in HEGs and background genes.

        Args:
            heg_sequences (list): List of highly expressed gene sequences
            bg_sequences (list): List of background gene sequences

        Returns:
            float: Dinucleotide usage divergence score (Jensen-Shannon divergence)
        """
        logger = get_logger()

        if not heg_sequences or not bg_sequences:
            logger.debug("No HEG or background sequences provided for dinucleotide divergence calculation")
            return 0.0

        # Count dinucleotides in HEG and background sequences
        heg_dinuc_counts = defaultdict(int)
        bg_dinuc_counts = defaultdict(int)

        # Process HEG sequences
        for seq in heg_sequences:
            seq_str = str(seq.seq) if isinstance(seq, SeqRecord) else str(seq)
            seq_str = seq_str.upper()

            # Count dinucleotides
            for i in range(len(seq_str) - 1):
                dinuc = seq_str[i:i+2]
                if all(base in "ACGT" for base in dinuc):
                    heg_dinuc_counts[dinuc] += 1

        # Process background sequences
        for seq in bg_sequences:
            seq_str = str(seq.seq) if isinstance(seq, SeqRecord) else str(seq)
            seq_str = seq_str.upper()

            # Count dinucleotides
            for i in range(len(seq_str) - 1):
                dinuc = seq_str[i:i+2]
                if all(base in "ACGT" for base in dinuc):
                    bg_dinuc_counts[dinuc] += 1

        # Calculate dinucleotide frequencies
        heg_total = sum(heg_dinuc_counts.values())
        bg_total = sum(bg_dinuc_counts.values())

        logger.debug(f"Dinuc divergence calculation - HEG total: {heg_total}, BG total: {bg_total}")
        logger.debug(f"Dinuc counts - HEG: {dict(heg_dinuc_counts)}")
        logger.debug(f"Dinuc counts - BG: {dict(bg_dinuc_counts)}")

        if heg_total == 0 or bg_total == 0:
            logger.debug("Zero counts in either HEG or background sequences for dinucleotide divergence calculation")
            return 0.0

        heg_freqs = {dinuc: count / heg_total for dinuc, count in heg_dinuc_counts.items()}
        bg_freqs = {dinuc: count / bg_total for dinuc, count in bg_dinuc_counts.items()}

        # Calculate divergence as Jensen-Shannon distance between distributions
        all_dinucs = set(heg_freqs.keys()) | set(bg_freqs.keys())
        heg_dist = [heg_freqs.get(dinuc, 0.0) for dinuc in all_dinucs]
        bg_dist = [bg_freqs.get(dinuc, 0.0) for dinuc in all_dinucs]

        # Normalize distributions
        heg_dist = np.array(heg_dist) / np.sum(heg_dist) if np.sum(heg_dist) > 0 else np.array(heg_dist)
        bg_dist = np.array(bg_dist) / np.sum(bg_dist) if np.sum(bg_dist) > 0 else np.array(bg_dist)

        # Calculate Jensen-Shannon distance
        js_distance = jensenshannon(heg_dist, bg_dist)

        logger.debug(f"Dinuc divergence calculation - JS distance: {js_distance}")

        return js_distance if not np.isnan(js_distance) else 0.0

    def calculate_cp_divergence_heg_bp(self, heg_sequences, bg_sequences):
        """Calculate codon pair usage divergence between HEG and background genes.

        Uses Jensen-Shannon divergence to measure the difference between
        codon pair usage distributions in HEGs and background genes.

        Args:
            heg_sequences (list): List of highly expressed gene sequences
            bg_sequences (list): List of background gene sequences

        Returns:
            float: Codon pair usage divergence score (Jensen-Shannon divergence)
        """
        logger = get_logger()

        if not heg_sequences or not bg_sequences:
            logger.debug("No HEG or background sequences provided for codon pair divergence calculation")
            return 0.0

        # Count codon pairs in HEG and background sequences
        heg_cp_counts = defaultdict(int)
        bg_cp_counts = defaultdict(int)

        # Process HEG sequences
        for seq in heg_sequences:
            seq_str = str(seq.seq) if isinstance(seq, SeqRecord) else str(seq)
            seq_str = seq_str.upper()

            # Ensure sequence length is multiple of 3
            if len(seq_str) % 3 != 0:
                seq_str = seq_str[:-(len(seq_str) % 3)]

            # Count codon pairs
            codons = [seq_str[i:i+3] for i in range(0, len(seq_str), 3)]
            for i in range(len(codons) - 1):
                codon1 = codons[i]
                codon2 = codons[i+1]
                if (len(codon1) == 3 and len(codon2) == 3 and
                    all(base in "ACGT" for base in codon1) and
                    all(base in "ACGT" for base in codon2)):
                    if (codon1 in self.codon_table.forward_table and
                        codon2 in self.codon_table.forward_table):
                        cp = f"{codon1}-{codon2}"
                        heg_cp_counts[cp] += 1

        # Process background sequences
        for seq in bg_sequences:
            seq_str = str(seq.seq) if isinstance(seq, SeqRecord) else str(seq)
            seq_str = seq_str.upper()

            # Ensure sequence length is multiple of 3
            if len(seq_str) % 3 != 0:
                seq_str = seq_str[:-(len(seq_str) % 3)]

            # Count codon pairs
            codons = [seq_str[i:i+3] for i in range(0, len(seq_str), 3)]
            for i in range(len(codons) - 1):
                codon1 = codons[i]
                codon2 = codons[i+1]
                if (len(codon1) == 3 and len(codon2) == 3 and
                    all(base in "ACGT" for base in codon1) and
                    all(base in "ACGT" for base in codon2)):
                    if (codon1 in self.codon_table.forward_table and
                        codon2 in self.codon_table.forward_table):
                        cp = f"{codon1}-{codon2}"
                        bg_cp_counts[cp] += 1

        # Calculate codon pair frequencies
        heg_total = sum(heg_cp_counts.values())
        bg_total = sum(bg_cp_counts.values())

        logger.debug(f"CP divergence calculation - HEG total: {heg_total}, BG total: {bg_total}")
        logger.debug(f"CP counts - HEG: {len(heg_cp_counts)} unique pairs")
        logger.debug(f"CP counts - BG: {len(bg_cp_counts)} unique pairs")

        if heg_total == 0 or bg_total == 0:
            logger.debug("Zero counts in either HEG or background sequences for codon pair divergence calculation")
            return 0.0

        heg_freqs = {cp: count / heg_total for cp, count in heg_cp_counts.items()}
        bg_freqs = {cp: count / bg_total for cp, count in bg_cp_counts.items()}

        # Calculate divergence as Jensen-Shannon distance between distributions
        # Use only the top N most common codon pairs to avoid sparse distributions
        top_n = min(100, len(heg_freqs), len(bg_freqs))  # Use top 100 codon pairs or fewer if not enough

        if top_n == 0:
            logger.debug("Not enough codon pairs for CP divergence calculation")
            return 0.0

        common_cps = set(sorted(heg_freqs, key=heg_freqs.get, reverse=True)[:top_n]) | \
                     set(sorted(bg_freqs, key=bg_freqs.get, reverse=True)[:top_n])

        logger.debug(f"CP divergence calculation - Using {len(common_cps)} common codon pairs")

        if not common_cps:
            logger.debug("No common codon pairs found for CP divergence calculation")
            return 0.0

        heg_dist = [heg_freqs.get(cp, 0.0) for cp in common_cps]
        bg_dist = [bg_freqs.get(cp, 0.0) for cp in common_cps]

        # Normalize distributions
        heg_dist = np.array(heg_dist) / np.sum(heg_dist) if np.sum(heg_dist) > 0 else np.array(heg_dist)
        bg_dist = np.array(bg_dist) / np.sum(bg_dist) if np.sum(bg_dist) > 0 else np.array(bg_dist)

        # Calculate Jensen-Shannon distance
        js_distance = jensenshannon(heg_dist, bg_dist)

        logger.debug(f"CP divergence calculation - JS distance: {js_distance}")

        return js_distance if not np.isnan(js_distance) else 0.0

    def calculate_aaai(self, sequence, heg_sequences):
        """Calculate Amino Acid Adaptation Index (AAAI).

        AAAI measures how well a gene's amino acid usage matches that of highly expressed genes.
        It's similar to CAI but at the amino acid level rather than codon level.

        Args:
            sequence (str or SeqRecord): Sequence to calculate AAAI for
            heg_sequences (list): List of highly expressed gene sequences for reference

        Returns:
            float: AAAI value (0-1 range)
        """
        try:
            # Handle both string and SeqRecord objects
            seq_str = str(sequence.seq) if isinstance(sequence, SeqRecord) else str(sequence)
            seq_str = seq_str.upper()

            # Clean and validate sequence
            if not seq_str or len(seq_str) < 30:  # Need a reasonable sequence length
                return 0.0

            # Remove non-standard bases
            seq_str = ''.join(base for base in seq_str if base in "ACGT")

            # Ensure sequence length is multiple of 3
            if len(seq_str) % 3 != 0:
                seq_str = seq_str[:-(len(seq_str) % 3)]

            # Count amino acids in HEG sequences
            heg_aa_counts = defaultdict(int)

            for heg_seq in heg_sequences:
                heg_str = str(heg_seq.seq) if isinstance(heg_seq, SeqRecord) else str(heg_seq)
                heg_str = heg_str.upper()

                if len(heg_str) % 3 != 0:
                    heg_str = heg_str[:-(len(heg_str) % 3)]

                for i in range(0, len(heg_str), 3):
                    codon = heg_str[i:i+3]
                    if len(codon) == 3 and all(base in "ACGT" for base in codon):
                        if codon in self.codon_table.forward_table:
                            aa = self.codon_table.forward_table[codon]
                            heg_aa_counts[aa] += 1

            # Calculate HEG amino acid frequencies
            heg_total = sum(heg_aa_counts.values())
            if heg_total == 0:
                return 0.0

            heg_freqs = {aa: count / heg_total for aa, count in heg_aa_counts.items()}

            # Calculate AAAI for the sequence
            aa_weights = []

            for i in range(0, len(seq_str), 3):
                codon = seq_str[i:i+3]
                if len(codon) == 3 and all(base in "ACGT" for base in codon):
                    if codon in self.codon_table.forward_table:
                        aa = self.codon_table.forward_table[codon]
                        if aa in heg_freqs and heg_freqs[aa] > 0:
                            aa_weights.append(heg_freqs[aa])

            if not aa_weights or len(aa_weights) < 5:  # Need a reasonable number of amino acids
                return 0.0

            # Calculate geometric mean
            return np.exp(np.mean(np.log(aa_weights)))

        except Exception as e:
            logger.error(f"Error calculating AAAI: {e}")
            return 0.0