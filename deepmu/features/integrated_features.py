"""综合特征提取模块

本模块将基因组特征与密码子特征整合，用于全面的微生物生长速率和最适温度预测。
"""

import os
import logging
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Optional, Union, Tuple
import multiprocessing as mp
from tqdm import tqdm

from deepmu.features.codon_features import CodonFeatureCalculator
from deepmu.features.genomic_features import GenomicFeatureCalculator
from deepmu.features.sequence_features import SequenceFeatureCalculator
from deepmu.features.pathway_features import PathwayFeatureCalculator, PathwayDatabase
from deepmu.features.pathway_clustering import PathwayClusterCalculator
from deepmu.taxonomy.taxonomy_utils import TaxonomyUtils

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def extract_integrated_features(
    genome_id: str,
    genome_file: str,
    cds_file: str,
    ko_file: Optional[str] = None,
    taxonomy_file: Optional[str] = None,
    metadata_file: Optional[str] = None,
    heg_ko_list: Optional[str] = None,
    kegg_path: Optional[str] = None,
    use_weighted_similarity: bool = True,
    select_important_features: bool = False,
    advanced_codon_features: bool = True,
    include_pathway_features: bool = True,
    taxid: Optional[str] = None
) -> Dict[str, np.ndarray]:
    """为单个基因组提取综合特征。

    参数:
        genome_id: 基因组ID
        genome_file: 基因组FASTA文件路径
        cds_file: CDS的FASTA文件路径
        ko_file: 可选，KO文件路径
        taxonomy_file: 可选，分类文件路径
        metadata_file: 可选，元数据文件路径
        heg_ko_list: 可选，HEG KO列表路径
        kegg_path: 可选，KEGG通路映射文件路径
        use_weighted_similarity: 是否使用加权相似性
        select_important_features: 是否选择重要特征
        advanced_codon_features: 是否计算高级密码子特征
        include_pathway_features: 是否包含通路完整性特征
        taxid: 可选，分类ID

    返回:
        综合特征的字典
    """
    try:
        logger.info(f"正在为 {genome_id} 提取综合特征")

        # 初始化特征计算器
        genomic_calculator = GenomicFeatureCalculator()
        codon_calculator = CodonFeatureCalculator(use_heg_features=True)

        # 提取基因组特征
        genomic_features = genomic_calculator.calculate_features(genome_file)
        if not genomic_features:
            logger.warning(f"{genome_id} 未提取到基因组特征")
            return {}

        # 提取密码子特征
        from deepmu.features.feature_extraction import extract_features_for_genome
        codon_features = extract_features_for_genome(
            genome_id=genome_id,
            cds_file=cds_file,
            ko_file=ko_file,
            taxonomy_file=taxonomy_file,
            metadata_file=metadata_file,
            heg_ko_list=heg_ko_list,
            use_weighted_similarity=use_weighted_similarity,
            select_important_features=select_important_features,
            advanced_codon_features=advanced_codon_features,
            taxid=taxid
        )
        if not codon_features:
            logger.warning(f"{genome_id} 未提取到密码子特征")
            return {}

        # 合并特征
        integrated_features = {**genomic_features, **codon_features}

        # 如需且KO文件可用，提取通路特征
        if include_pathway_features and ko_file and kegg_path and os.path.exists(ko_file) and os.path.exists(kegg_path):
            try:
                # 从KO文件加载KO条目
                ko_terms = []
                with open(ko_file, 'r') as f:
                    for line in f:
                        parts = line.strip().split('\t')
                        if len(parts) >= 2:
                            ko_terms.append(parts[1])

                if ko_terms:
                    # 初始化通路特征计算器
                    pathway_calculator = PathwayFeatureCalculator(kegg_path=kegg_path)

                    # 计算通路完整性特征
                    pathway_features = {}
                    pathway_vector = pathway_calculator.pathway_db.map_to_pathways(ko_terms)

                    # 向量转字典
                    for i, pathway_id in enumerate(pathway_calculator.pathway_db._get_feature_names()):
                        if i < len(pathway_vector):
                            pathway_features[f"pathway_{pathway_id}"] = pathway_vector[i]

                    # 添加通路特征
                    integrated_features.update(pathway_features)

                    # 添加通路特征类型指示
                    for key in pathway_features:
                        integrated_features[f"is_pathway_{key}"] = 1.0

                    logger.info(f"{genome_id} 添加了 {len(pathway_features)} 个通路特征")

                    # 计算通路聚类特征
                    try:
                        pathway_cluster_calculator = PathwayClusterCalculator(kegg_path=kegg_path)
                        cluster_features = pathway_cluster_calculator.calculate_cluster_features(ko_terms)

                        # 添加聚类特征
                        integrated_features.update(cluster_features)

                        # 添加聚类特征类型指示
                        for key in cluster_features:
                            integrated_features[f"is_cluster_{key}"] = 1.0

                        logger.info(f"{genome_id} 添加了 {len(cluster_features)} 个通路聚类特征")
                    except Exception as e:
                        logger.warning(f"计算通路聚类特征出错: {e}")
            except Exception as e:
                logger.warning(f"提取通路特征出错: {e}")

        # 添加特征类型指示，便于分析
        for key in genomic_features:
            integrated_features[f"is_genomic_{key}"] = 1.0

        for key in codon_features:
            integrated_features[f"is_codon_{key}"] = 1.0

        logger.info(f"{genome_id} 提取了 {len(integrated_features)} 个综合特征")
        return integrated_features

    except Exception as e:
        logger.error(f"{genome_id} 提取综合特征出错: {e}")
        return {}

def extract_integrated_features_batch(
    genome_dir: str,
    cds_dir: str,
    ko_dir: Optional[str] = None,
    taxonomy_dir: Optional[str] = None,
    metadata_file: Optional[str] = None,
    heg_ko_list: Optional[str] = None,
    kegg_path: Optional[str] = None,
    output_dir: str = "integrated_features",
    num_processes: int = mp.cpu_count(),
    use_weighted_similarity: bool = True,
    select_important_features: bool = False,
    advanced_codon_features: bool = True,
    include_pathway_features: bool = True,
    genome_ids: Optional[List[str]] = None
) -> None:
    """为多个基因组批量提取综合特征。

    参数:
        genome_dir: 基因组文件目录
        cds_dir: CDS文件目录
        ko_dir: 可选，KO文件目录
        taxonomy_dir: 可选，分类文件目录
        metadata_file: 可选，元数据文件路径
        heg_ko_list: 可选，HEG KO列表路径
        output_dir: 特征文件保存目录
        num_processes: 使用的进程数
        use_weighted_similarity: 是否使用加权相似性
        select_important_features: 是否选择重要特征
        advanced_codon_features: 是否计算高级密码子特征
        genome_ids: 可选，需处理的基因组ID列表
    """
    logger.info(f"正在为 {genome_dir} 中的基因组提取综合特征")

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 获取基因组文件
    genome_files = []
    if genome_ids:
        for genome_id in genome_ids:
            genome_file = os.path.join(genome_dir, f"{genome_id}.fna")
            # 尝试不同的CDS文件命名
            cds_file = os.path.join(cds_dir, f"{genome_id}_cds.fna")
            if not os.path.exists(cds_file):
                cds_file = os.path.join(cds_dir, f"{genome_id}.ffn")
            if not os.path.exists(cds_file):
                cds_file = os.path.join(cds_dir, f"{genome_id}_cds.ffn")

            if not os.path.exists(genome_file):
                logger.warning(f"{genome_id} 未找到基因组文件")
                continue

            if not os.path.exists(cds_file):
                logger.warning(f"{genome_id} 未找到CDS文件")
                continue

            ko_file = os.path.join(ko_dir, f"{genome_id}_ko.tsv") if ko_dir else None
            taxonomy_file = os.path.join(taxonomy_dir, f"{genome_id}_taxonomy.tsv") if taxonomy_dir else None

            genome_files.append((genome_id, genome_file, cds_file, ko_file, taxonomy_file))
    else:
        for filename in os.listdir(genome_dir):
            if filename.endswith(".fna"):
                genome_id = filename.split(".fna")[0]
                genome_file = os.path.join(genome_dir, filename)
                # 尝试不同的CDS文件命名
                cds_file = os.path.join(cds_dir, f"{genome_id}_cds.fna")
                if not os.path.exists(cds_file):
                    cds_file = os.path.join(cds_dir, f"{genome_id}.ffn")
                if not os.path.exists(cds_file):
                    cds_file = os.path.join(cds_dir, f"{genome_id}_cds.ffn")

                if not os.path.exists(cds_file):
                    logger.warning(f"{genome_id} 未找到CDS文件")
                    continue

                ko_file = os.path.join(ko_dir, f"{genome_id}_ko.tsv") if ko_dir else None
                taxonomy_file = os.path.join(taxonomy_dir, f"{genome_id}_taxonomy.tsv") if taxonomy_dir else None

                genome_files.append((genome_id, genome_file, cds_file, ko_file, taxonomy_file))

    # 如有元数据，获取taxid映射
    taxid_map = {}
    if metadata_file and os.path.exists(metadata_file):
        try:
            metadata_df = pd.read_csv(metadata_file, sep='\t')
            if 'genome_id' in metadata_df.columns and 'taxid' in metadata_df.columns:
                taxid_map = dict(zip(metadata_df['genome_id'], metadata_df['taxid']))
        except Exception as e:
            logger.error(f"读取元数据文件出错: {e}")

    # 处理基因组
    if num_processes > 1:
        # 并行处理
        with mp.Pool(processes=num_processes) as pool:
            args = [
                (
                    genome_id,
                    genome_file,
                    cds_file,
                    ko_file,
                    taxonomy_file,
                    metadata_file,
                    heg_ko_list,
                    kegg_path,
                    use_weighted_similarity,
                    select_important_features,
                    advanced_codon_features,
                    include_pathway_features,
                    output_dir,
                    taxid_map.get(genome_id)
                )
                for genome_id, genome_file, cds_file, ko_file, taxonomy_file in genome_files
            ]

            for _ in tqdm(pool.imap_unordered(process_genome_wrapper, args), total=len(args)):
                pass
    else:
        # Sequential processing
        for genome_id, genome_file, cds_file, ko_file, taxonomy_file in tqdm(genome_files):
            process_genome(
                genome_id,
                genome_file,
                cds_file,
                ko_file,
                taxonomy_file,
                metadata_file,
                heg_ko_list,
                kegg_path,
                use_weighted_similarity,
                select_important_features,
                advanced_codon_features,
                include_pathway_features,
                output_dir,
                taxid_map.get(genome_id)
            )

    logger.info(f"Extracted integrated features for {len(genome_files)} genomes")

def process_genome(
    genome_id: str,
    genome_file: str,
    cds_file: str,
    ko_file: Optional[str],
    taxonomy_file: Optional[str],
    metadata_file: Optional[str],
    heg_ko_list: Optional[str],
    kegg_path: Optional[str],
    use_weighted_similarity: bool,
    select_important_features: bool,
    advanced_codon_features: bool,
    include_pathway_features: bool,
    output_dir: str,
    taxid: Optional[str] = None
) -> None:
    """Process a genome and save integrated features.

    Args:
        genome_id: Genome ID
        genome_file: Path to genome file
        cds_file: Path to CDS file
        ko_file: Optional path to KO file
        taxonomy_file: Optional path to taxonomy file
        metadata_file: Optional path to metadata file
        heg_ko_list: Optional path to HEG KO list
        use_weighted_similarity: Whether to use weighted similarity
        select_important_features: Whether to select important features
        advanced_codon_features: Whether to calculate advanced codon features
        output_dir: Directory to save feature files
        taxid: Optional taxonomy ID
    """
    # Create output file path
    output_file = os.path.join(output_dir, f"{genome_id}_integrated_features.npz")

    # Skip if output file already exists
    if os.path.exists(output_file):
        logger.info(f"Skipping {genome_id}, integrated features already exist")
        return

    # Extract integrated features
    features = extract_integrated_features(
        genome_id=genome_id,
        genome_file=genome_file,
        cds_file=cds_file,
        ko_file=ko_file,
        taxonomy_file=taxonomy_file,
        metadata_file=metadata_file,
        heg_ko_list=heg_ko_list,
        kegg_path=kegg_path,
        use_weighted_similarity=use_weighted_similarity,
        select_important_features=select_important_features,
        advanced_codon_features=advanced_codon_features,
        include_pathway_features=include_pathway_features,
        taxid=taxid
    )

    # Save features
    if features:
        np.savez(output_file, **features)
        logger.info(f"Saved integrated features for {genome_id} to {output_file}")
    else:
        logger.warning(f"No integrated features to save for {genome_id}")

def process_genome_wrapper(args: Tuple) -> None:
    """Wrapper function for parallel processing."""
    process_genome(*args)

def combine_feature_files(
    feature_dir: str,
    output_file: str,
    metadata_file: Optional[str] = None
) -> None:
    """Combine feature files into a single TSV file.

    Args:
        feature_dir: Directory containing feature files
        output_file: Path to output TSV file
        metadata_file: Optional path to metadata file
    """
    logger.info(f"Combining feature files from {feature_dir}")

    # Get feature files
    feature_files = [f for f in os.listdir(feature_dir) if f.endswith("_integrated_features.npz")]

    if not feature_files:
        logger.warning(f"No feature files found in {feature_dir}")
        return

    # Load metadata if available
    metadata_df = None
    if metadata_file and os.path.exists(metadata_file):
        try:
            metadata_df = pd.read_csv(metadata_file, sep='\t')
            logger.info(f"Loaded metadata with {len(metadata_df)} rows")
        except Exception as e:
            logger.error(f"Error loading metadata: {e}")

    # Load and combine features
    all_features = []
    for feature_file in feature_files:
        try:
            # Extract genome ID from filename
            genome_id = feature_file.split("_integrated_features.npz")[0]

            # Load features
            features = dict(np.load(os.path.join(feature_dir, feature_file)))

            # Add genome ID
            features['genome_id'] = genome_id

            # Add to list
            all_features.append(features)
        except Exception as e:
            logger.error(f"Error loading features from {feature_file}: {e}")

    if not all_features:
        logger.warning("No features loaded")
        return

    # Convert to DataFrame
    features_df = pd.DataFrame(all_features)

    # Merge with metadata if available
    if metadata_df is not None:
        features_df = pd.merge(features_df, metadata_df, on='genome_id', how='inner')
        logger.info(f"Merged features with metadata, resulting in {len(features_df)} rows")

    # Save to TSV
    features_df.to_csv(output_file, sep='\t', index=False)
    logger.info(f"Saved combined features to {output_file}")

def main():
    """Main function for command-line usage."""
    import argparse

    parser = argparse.ArgumentParser(description="Extract integrated genomic and codon features")
    parser.add_argument("--genome-dir", type=str, required=True, help="Directory containing genome files")
    parser.add_argument("--cds-dir", type=str, required=True, help="Directory containing CDS files")
    parser.add_argument("--ko-dir", type=str, help="Directory containing KO files")
    parser.add_argument("--taxonomy-dir", type=str, help="Directory containing taxonomy files")
    parser.add_argument("--metadata", type=str, help="Path to metadata file")
    parser.add_argument("--heg-ko-list", type=str, help="Path to HEG KO list")
    parser.add_argument("--kegg-path", type=str, help="Path to KEGG pathway mapping file")
    parser.add_argument("--output-dir", type=str, default="integrated_features", help="Directory to save feature files")
    parser.add_argument("--combined-output", type=str, help="Path to combined output TSV file")
    parser.add_argument("--num-processes", type=int, default=mp.cpu_count(), help="Number of processes to use")
    parser.add_argument("--use-weighted-similarity", action="store_true", help="Use weighted similarity")
    parser.add_argument("--select-important-features", action="store_true", help="Select important features")
    parser.add_argument("--advanced-codon-features", action="store_true", help="Calculate advanced codon features")
    parser.add_argument("--include-pathway-features", action="store_true", help="Include pathway completeness features")
    parser.add_argument("--genome-list", type=str, help="File containing list of genome IDs to process")

    args = parser.parse_args()

    # Get genome IDs if provided
    genome_ids = None
    if args.genome_list:
        with open(args.genome_list, 'r') as f:
            genome_ids = [line.strip() for line in f if line.strip()]

    # Extract integrated features
    extract_integrated_features_batch(
        genome_dir=args.genome_dir,
        cds_dir=args.cds_dir,
        ko_dir=args.ko_dir,
        taxonomy_dir=args.taxonomy_dir,
        metadata_file=args.metadata,
        heg_ko_list=args.heg_ko_list,
        kegg_path=args.kegg_path,
        output_dir=args.output_dir,
        num_processes=args.num_processes,
        use_weighted_similarity=args.use_weighted_similarity,
        select_important_features=args.select_important_features,
        advanced_codon_features=args.advanced_codon_features,
        include_pathway_features=args.include_pathway_features,
        genome_ids=genome_ids
    )

    # Combine feature files if requested
    if args.combined_output:
        combine_feature_files(
            feature_dir=args.output_dir,
            output_file=args.combined_output,
            metadata_file=args.metadata
        )

if __name__ == "__main__":
    main()
