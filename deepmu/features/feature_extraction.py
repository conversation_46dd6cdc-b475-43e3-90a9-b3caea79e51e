"""
模块化特征提取模块

这个模块为 DeepMu 提供了模块化的特征提取方法，包含不同类型特征的独立模块：

特征类型：
- 密码子特征 (来自DNA序列) - 密码子使用偏好、密码子适应指数等
- 氨基酸特征 (来自蛋白质序列) - 氨基酸组成、理化性质等
- 基因组特征 (来自基因组序列) - GC含量、基因密度等
- 分类学特征 (来自NCBI分类学ID) - 系统发育信息、分类学距离等
- 代谢途径特征 (来自KO注释) - KEGG途径完整性、酶功能分布等

主要功能：
1. 单个基因组特征提取 - 从单个基因组文件提取所有类型的特征
2. 批量基因组特征提取 - 并行处理多个基因组的特征提取
3. 特征整合 - 将不同类型的特征整合为统一格式
4. 特征标准化 - 对提取的特征进行标准化处理
5. 高表达基因分析 - 区分高表达基因和背景基因的特征

使用场景：
- 训练数据准备：为模型训练准备特征数据
- 预测数据处理：为新基因组生成预测所需的特征
- 特征分析：分析不同特征对预测结果的贡献

该模块可用于单个基因组或批量基因组的特征提取。
"""

import os
import logging
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple, Union
import multiprocessing as mp
from tqdm import tqdm
from Bio import SeqIO

# Import feature calculators
from deepmu.features.rna_features import extract_rna_features

# Import feature calculators
from deepmu.features.codon_features import CodonFeatureCalculator
from deepmu.features.sequence_features import SequenceFeatureCalculator
from deepmu.features.genomic_features import GenomicFeatureCalculator
from deepmu.features.amino_acid_features import AminoAcidFeatureCalculator
from deepmu.features.taxonomy_features import TaxonomyFeatureCalculator
from deepmu.features.pathway_features import PathwayDatabase, PathwayFeatureCalculator
from deepmu.features.protein_pi_features import extract_pi_features_for_genome
from deepmu.features.heg_analysis import load_heg_ko_list, load_ko_file, create_heg_ko_map

# Configure logging
logger = logging.getLogger(__name__)

def load_heg_genes(ko_file: str, heg_ko_list: str) -> Tuple[set, set]:
    """Load highly expressed genes from KO file.

    Args:
        ko_file: Path to KO file
        heg_ko_list: Path to HEG KO list file

    Returns:
        Tuple of (heg_genes, bg_genes)
    """
    # Load HEG KO list using the heg_analysis module
    heg_kos = load_heg_ko_list(heg_ko_list)

    # If the file is empty or not found, use the default list
    if not heg_kos:
        logger.warning(f"No HEG KOs found in {heg_ko_list}. Using default list.")
        heg_kos = set([
            'K01869', 'K01870', 'K01873', 'K01872', 'K01985', 'K01992', 'K01876',
            'K01874', 'K01875', 'K01887', 'K01889', 'K01890', 'K01886', 'K01892',
            'K01893', 'K02357', 'K02358', 'K02967', 'K02982', 'K02986', 'K02988',
            'K02990', 'K02992', 'K02994', 'K02996', 'K02946', 'K02931', 'K02933',
            'K02935', 'K02906', 'K02904', 'K02902', 'K02909', 'K02911', 'K02913',
            'K02914', 'K02919', 'K02876', 'K02878', 'K02879', 'K02881', 'K02884',
            'K02925', 'K02926', 'K02965', 'K02871', 'K02863', 'K02886', 'K02889',
            'K02890', 'K02892', 'K02895', 'K02952', 'K02954', 'K02956', 'K02961',
            'K02959', 'K02950', 'K02930', 'K02907', 'K02899', 'K02866', 'K02868',
            'K02882', 'K02874', 'K02969', 'K02970', 'K02948', 'K02864', 'K02939',
            'K02941', 'K02944', 'K02937', 'K02977', 'K02979', 'K02984', 'K02872',
            'K02891', 'K02887', 'K02921', 'K02923', 'K02927', 'K02929', 'K02932',
            'K02934', 'K02936', 'K02938', 'K02940', 'K02942', 'K02943', 'K02945',
            'K02947', 'K02949', 'K02951', 'K02953', 'K02955', 'K02957', 'K02958',
            'K02960', 'K02962', 'K02963', 'K02964', 'K02966', 'K02968', 'K02971',
            'K02973', 'K02974', 'K02975', 'K02976', 'K02978', 'K02980', 'K02981',
            'K02983', 'K02985', 'K02987', 'K02989', 'K02991', 'K02993', 'K02995',
            'K02997', 'K02998', 'K02999', 'K03000', 'K03001', 'K03002', 'K03003',
            'K03004', 'K03005', 'K03006', 'K03007', 'K03008', 'K03009', 'K03010',
            'K03011', 'K03012', 'K03013', 'K03014', 'K03015', 'K03016', 'K03017',
            'K03018', 'K03019', 'K03020', 'K03021', 'K03022', 'K03023', 'K03024',
            'K03025', 'K03026', 'K03027', 'K03028', 'K03029', 'K03030', 'K03031',
            'K03032', 'K03033', 'K03034', 'K03035', 'K03036', 'K03037', 'K03038',
            'K03039', 'K03040', 'K03041', 'K03042', 'K03043', 'K03044', 'K03045',
            'K03046', 'K03047', 'K03048', 'K03049', 'K03050', 'K03051', 'K03052',
            'K03053', 'K03054', 'K03055', 'K03056', 'K03057', 'K03058', 'K03059',
            'K03060', 'K03061', 'K03062', 'K03063', 'K03064', 'K03065', 'K03066',
            'K03067', 'K03068', 'K03069', 'K03070', 'K03071', 'K03072', 'K03073',
            'K03074', 'K03075', 'K03076', 'K03077', 'K03078', 'K03079', 'K03080',
            'K03081', 'K03082', 'K03083', 'K03084', 'K03085', 'K03086', 'K03087',
            'K03088', 'K03089', 'K03090', 'K03091', 'K03092', 'K03093', 'K03094',
            'K03095', 'K03096', 'K03097', 'K03098', 'K03099', 'K03100', 'K03101',
            'K03102', 'K03103', 'K03104', 'K03105', 'K03106', 'K03107', 'K03108',
            'K03109', 'K03110', 'K03111', 'K03112', 'K03113', 'K03114', 'K03115',
            'K03116', 'K03117', 'K03118', 'K03119', 'K03120', 'K03121', 'K03122',
            'K03123', 'K03124'
        ])

    # Load KO file using the heg_analysis module
    ko_genes = load_ko_file(ko_file)

    # Identify HEG and background genes
    heg_genes = set()
    bg_genes = set()
    for gene_id, ko_id in ko_genes.items():
        if ko_id in heg_kos:
            heg_genes.add(gene_id)
        else:
            bg_genes.add(gene_id)

    logger.info(f"Identified {len(heg_genes)} HEG genes and {len(bg_genes)} background genes from {ko_file}")
    return heg_genes, bg_genes


def calculate_heg_features(sequences: List, heg_genes: Set[str], bg_genes: Set[str], advanced_codon_features: bool, genetic_code: int = 11) -> Dict[str, float]:
    """Calculate features related to Highly Expressed Genes (HEGs).

    Args:
        sequences: List of sequences (either SeqRecord objects or strings)
        heg_genes: Set of HEG gene IDs
        bg_genes: Set of background gene IDs
        advanced_codon_features: Whether to calculate advanced codon features
        genetic_code: Genetic code to use for codon translation (default: 11)

    Returns:
        Dictionary of HEG-related features
    """
    codon_calculator = CodonFeatureCalculator(use_heg_features=True, genetic_code=genetic_code)

    # Calculate HEG ratio
    heg_ratio = len(heg_genes) / (len(sequences) + 1e-10)

    # If no HEG genes found, return default values
    if not heg_genes:
        logger.warning("No HEG genes found in the genome")
        heg_features = {f'HEG_{k}': 0.0 for k in ['CUB', 'CPB', 'Consistency', 'RemoteInteraction']}
        bg_features = {f'BG_{k}': 0.0 for k in ['CUB', 'CPB', 'Consistency', 'RemoteInteraction']}
        delta_features = {f'delta_{k}': 0.0 for k in ['CUB', 'CPB', 'Consistency', 'RemoteInteraction']}

        return {
            'HEG_ratio': heg_ratio,
            **heg_features,
            **bg_features,
            **delta_features,
            'CAI_HEG': 0.0,
            'CAI_BP': 0.0,
            'delta_CAI': 0.0,
            'ENC_HEG': 0.0,
            'ENC_BP': 0.0,
            'delta_ENC': 0.0,
            'RSCU_Deviation_HEG': 0.0,
            'RSCU_Deviation_BP': 0.0,
            'Codon_Usage_Divergence_Composite': 0.0,
            'codon_divergence_HEG_BP': 0.0,
            'AA_divergence_HEG_BP': 0.0,
            'Dinuc_divergence_HEG_BP': 0.0,
            'CP_divergence_HEG_BP': 0.0
        }

    # Create a mapping from sequence IDs to HEG status
    ko_map = {}
    for seq in sequences:
        # Get the sequence ID
        if hasattr(seq, 'id'):
            seq_id = seq.id
        elif isinstance(seq, str) and seq.startswith('>'):
            # Extract ID from FASTA header
            seq_id = seq.split('\n', 1)[0].split()[0][1:]
        else:
            # Skip sequences without IDs
            continue

        # Check if this sequence is a HEG
        if seq_id in heg_genes:
            ko_map[seq_id] = "HEG"

    logger.info(f"Created ko_map with {len(ko_map)} HEG sequences out of {len(sequences)} total sequences")

    # Use the calculate_features_with_heg method to get all metrics including HEG-BP comparisons
    combined_features = codon_calculator.calculate_features_with_heg(sequences, ko_map, calculate_advanced_features=advanced_codon_features)

    # Log HEG features for debugging
    logger.info(f"Calculated {len(combined_features)} HEG-specific features")
    for key, value in sorted(combined_features.items()):
        if any(pattern in key for pattern in ['_HEG', '_BG', 'delta_', 'CAI_', 'RSCU_', 'codon_divergence_']):
            logger.debug(f"HEG feature {key}: {value}")

    # Extract and organize features
    heg_features = {f'{k}_HEG': combined_features[f'{k}_HEG'] for k in ['CUB', 'CPB', 'Consistency', 'RemoteInteraction']}
    bg_features = {f'{k}_BG': combined_features[f'{k}_BG'] for k in ['CUB', 'CPB', 'Consistency', 'RemoteInteraction']}
    delta_features = {f'delta_{k}': combined_features[f'delta_{k}'] for k in ['CUB', 'CPB', 'Consistency', 'RemoteInteraction']}

    # Extract HEG-BP codon adaptation metrics
    heg_bp_features = {
        'CAI_HEG': combined_features.get('CAI_HEG', 0.0),
        'CAI_BP': combined_features.get('CAI_BP', 0.0),
        'delta_CAI': combined_features.get('delta_CAI', 0.0),
        'ENC_HEG': combined_features.get('ENC_HEG', 0.0),
        'ENC_BP': combined_features.get('ENC_BP', 0.0),
        'delta_ENC': combined_features.get('delta_ENC', 0.0),
        'RSCU_Deviation_HEG': combined_features.get('RSCU_Deviation_HEG', 0.0),
        'RSCU_Deviation_BP': combined_features.get('RSCU_Deviation_BP', 0.0),
        'Codon_Usage_Divergence_Composite': combined_features.get('Codon_Usage_Divergence_Composite', 0.0),
        'codon_divergence_HEG_BP': combined_features.get('codon_divergence_HEG_BP', 0.0),
        'AA_divergence_HEG_BP': combined_features.get('AA_divergence_HEG_BP', 0.0),
        'Dinuc_divergence_HEG_BP': combined_features.get('Dinuc_divergence_HEG_BP', 0.0),
        'CP_divergence_HEG_BP': combined_features.get('CP_divergence_HEG_BP', 0.0)
    }

    # Combine all features
    result = {
        'HEG_ratio': heg_ratio,
        **heg_features,
        **bg_features,
        **delta_features,
        **heg_bp_features
    }

    return result


def extract_codon_features(
    cds_file: str,
    advanced_features: bool = True,
    heg_ko_list: Optional[str] = None,
    ko_file: Optional[str] = None,
    genetic_code: int = 11
) -> Dict[str, float]:
    """Extract codon usage features from a CDS file.

    Args:
        cds_file: Path to CDS file in FASTA format
        advanced_features: Whether to calculate advanced codon features
        heg_ko_list: Path to HEG KO list file (optional)
        ko_file: Path to KO file (optional)
        genetic_code: Genetic code to use for codon translation (default: 11)

    Returns:
        Dictionary of codon features
    """
    try:
        # Initialize feature calculator
        codon_calculator = CodonFeatureCalculator(genetic_code=genetic_code, use_heg_features=True)

        # Read sequences from file
        sequences = list(SeqIO.parse(cds_file, "fasta"))
        # Keep the original SeqRecord objects for HEG analysis
        sequence_strings = [str(record.seq) for record in sequences]

        if not sequences:
            logger.warning(f"No sequences found in {cds_file}")
            return {}

        # Create a combined sequence for basic features
        combined_seq = ''.join(sequence_strings)

        # Calculate basic codon features
        try:
            codon_features = codon_calculator.calculate_features(
                combined_seq,
                calculate_advanced_features=advanced_features
            )
        except AttributeError as e:
            logger.warning(f"Error calculating advanced features: {e}")
            # Fall back to basic features
            codon_features = codon_calculator.calculate_features(
                combined_seq,
                calculate_advanced_features=False
            )

        # Calculate HEG features if KO file and HEG KO list are provided
        if ko_file and os.path.exists(ko_file) and heg_ko_list and os.path.exists(heg_ko_list):
            # Load HEG and background genes
            heg_genes, bg_genes = load_heg_genes(ko_file, heg_ko_list)
            logger.info(f"Found {len(heg_genes)} HEG genes and {len(bg_genes)} background genes")

            # Calculate HEG-specific features
            if heg_genes:
                heg_features = calculate_heg_features(
                    sequences,  # Pass the original SeqRecord objects
                    heg_genes,
                    bg_genes,
                    advanced_features,
                    genetic_code
                )
                codon_features.update(heg_features)

        return codon_features

    except Exception as e:
        logger.error(f"Error extracting codon features from {cds_file}: {e}")
        return {}

def extract_amino_acid_features(
    faa_file: str,
    heg_ko_list: Optional[str] = None,
    ko_file: Optional[str] = None,
    genetic_code: int = 11
) -> Dict[str, float]:
    """Extract amino acid features from a protein file.

    Args:
        faa_file: Path to protein file in FASTA format
        heg_ko_list: Path to HEG KO list file (optional)
        ko_file: Path to KO file (optional)
        genetic_code: Genetic code to use for translation (default: 11 for bacterial)

    Returns:
        Dictionary of amino acid features
    """
    try:
        # Load HEG KO list if provided
        heg_kos = set()
        if heg_ko_list and os.path.exists(heg_ko_list):
            with open(heg_ko_list, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        heg_kos.add(line)
            logger.info(f"Loaded {len(heg_kos)} HEG KOs from {heg_ko_list}")

        # Initialize feature calculator with the correct genetic code
        aa_calculator = AminoAcidFeatureCalculator(use_heg_features=True, heg_ko_ids=heg_kos, genetic_code=genetic_code)

        # Calculate features
        return aa_calculator.calculate_features_from_file(faa_file, ko_file=ko_file)

    except Exception as e:
        logger.error(f"Error extracting amino acid features from {faa_file}: {e}")
        return {}

def extract_genomic_features(genome_file: str) -> Dict[str, float]:
    """Extract genomic features from a genome file.

    Args:
        genome_file: Path to genome file in FASTA format

    Returns:
        Dictionary of genomic features
    """
    try:
        # Initialize feature calculator
        genomic_calculator = GenomicFeatureCalculator()

        # Calculate features
        return genomic_calculator.calculate_features(genome_file)

    except Exception as e:
        logger.error(f"Error extracting genomic features from {genome_file}: {e}")
        return {}

def extract_taxonomy_features(taxid: Union[str, int]) -> Dict[str, float]:
    """Extract taxonomy features from a taxonomy ID.

    Args:
        taxid: NCBI taxonomy ID

    Returns:
        Dictionary of taxonomy features
    """
    try:
        # Initialize feature calculator
        taxonomy_calculator = TaxonomyFeatureCalculator()

        # Check if NCBI taxonomy database is available
        if not hasattr(taxonomy_calculator, 'has_ete3') or not taxonomy_calculator.has_ete3:
            logger.warning("NCBI taxonomy database not available. Run prepare_ncbi_taxonomy.py first.")
            return {}

        # Calculate features
        return taxonomy_calculator.calculate_features(taxid)

    except Exception as e:
        logger.error(f"Error extracting taxonomy features from taxid {taxid}: {e}")
        return {}

def extract_pathway_features(ko_file: str, kegg_map_file: Optional[str] = None) -> Dict[str, float]:
    """Extract pathway features from a KO file.

    Args:
        ko_file: Path to KO file
        kegg_map_file: Path to KEGG mapping file (optional)

    Returns:
        Dictionary of pathway features including pathway completeness
    """
    try:
        # Initialize pathway database and feature calculator
        pathway_db = PathwayDatabase(kegg_map_file)
        pathway_calculator = PathwayFeatureCalculator(kegg_path=kegg_map_file)

        # Read KO terms from file
        ko_terms = []
        with open(ko_file, 'r') as f:
            for line in f:
                parts = line.strip().split('\t')
                if len(parts) >= 2:
                    ko = parts[1]
                    ko_terms.append(ko)

        # Map KO terms to pathways (basic pathway features)
        pathway_features = pathway_db.map_to_pathways(ko_terms)

        # Calculate pathway completeness features
        if ko_terms:
            try:
                # Convert pathway features to numpy array for metabolism scores calculation
                pathway_values = np.array(list(pathway_features.values()))

                # Calculate metabolism scores
                metabolism_scores = pathway_calculator.calculate_metabolism_scores(pathway_values)

                # Add metabolism scores as features
                for category, score in metabolism_scores.items():
                    if category != 'functional_profile':  # Skip the detailed profile
                        pathway_features[f"metabolism_{category}"] = score

                # Add functional profile scores
                for category, score in metabolism_scores.get('functional_profile', {}).items():
                    pathway_features[f"functional_{category}"] = score

                # Analyze pathway completeness
                completeness_info = pathway_calculator.analyze_pathway_completeness(ko_terms)

                # Add pathway completeness features
                for pathway_id, info in completeness_info.items():
                    # Ensure completeness is a float
                    try:
                        completeness = float(info['completeness'])
                        pathway_features[f"pathway_completeness_{pathway_id}"] = completeness
                    except (ValueError, TypeError):
                        logger.warning(f"Invalid completeness value for pathway {pathway_id}: {info['completeness']}")

                logger.info(f"Added {len(completeness_info)} pathway completeness features")

            except Exception as e:
                logger.warning(f"Error calculating pathway completeness features: {e}")
                import traceback
                logger.warning(f"Traceback: {traceback.format_exc()}")

        return pathway_features

    except Exception as e:
        logger.error(f"Error extracting pathway features from {ko_file}: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return {}

def extract_features_for_genome(
    genome_id: str,
    genome_file: Optional[str] = None,
    cds_file: Optional[str] = None,
    faa_file: Optional[str] = None,
    ko_file: Optional[str] = None,
    taxid: Optional[Union[str, int]] = None,
    kegg_map_file: Optional[str] = None,
    heg_ko_list: Optional[str] = None,
    advanced_codon_features: bool = True,
    genetic_code: int = 11,
    trna_file: Optional[str] = None,
    rrna_file: Optional[str] = None
) -> Dict[str, float]:
    """
    为单个基因组提取所有特征

    这个函数是特征提取的主要接口，能够从基因组数据中提取多种类型的特征，
    包括密码子特征、氨基酸特征、基因组特征、分类学特征和代谢途径特征。

    Args:
        genome_id (str): 基因组标识符
        genome_file (str, optional): 基因组FASTA文件路径
        cds_file (str, optional): 编码序列(CDS) FASTA文件路径
        faa_file (str, optional): 蛋白质序列FASTA文件路径
        ko_file (str, optional): KO注释文件路径
        taxid (str/int, optional): NCBI分类学ID
        kegg_map_file (str, optional): KEGG映射文件路径
        heg_ko_list (str, optional): 高表达基因KO列表文件路径
        advanced_codon_features (bool): 是否计算高级密码子特征 (默认: True)
        genetic_code (int): 用于密码子翻译的遗传密码表 (默认: 11, 细菌)
        trna_file (str, optional): tRNAscan-SE输出的tRNA文件路径
        rrna_file (str, optional): Barrnap输出的rRNA文件路径

    Returns:
        Dict[str, float]: 包含所有提取特征的字典

    特征类型包括：
        - 密码子特征: 密码子使用偏好、密码子适应指数等
        - 氨基酸特征: 氨基酸组成、理化性质等
        - 基因组特征: GC含量、基因密度等
        - 分类学特征: 系统发育信息
        - 代谢途径特征: KEGG途径完整性
        - RNA特征: tRNA和rRNA相关特征
        - 蛋白质特征: 等电点、分子量等
    """
    features = {'genome_id': genome_id}

    # Extract codon features if CDS file is provided
    if cds_file and os.path.exists(cds_file):
        codon_features = extract_codon_features(
            cds_file,
            advanced_features=advanced_codon_features,
            heg_ko_list=heg_ko_list,
            ko_file=ko_file,
            genetic_code=genetic_code
        )
        features.update(codon_features)

    # Extract amino acid features if protein file is provided
    if faa_file and os.path.exists(faa_file):
        aa_features = extract_amino_acid_features(
            faa_file,
            heg_ko_list=heg_ko_list,
            ko_file=ko_file,
            genetic_code=genetic_code
        )
        features.update(aa_features)

    # Extract genomic features if genome file is provided
    if genome_file and os.path.exists(genome_file):
        genomic_features = extract_genomic_features(genome_file)
        features.update(genomic_features)

    # Extract taxonomy features if taxid is provided
    if taxid:
        taxonomy_features = extract_taxonomy_features(taxid)
        features.update(taxonomy_features)

    # Extract pathway features if KO file is provided
    if ko_file and os.path.exists(ko_file):
        pathway_features = extract_pathway_features(ko_file, kegg_map_file)
        features.update(pathway_features)

    # Extract RNA features if tRNA or rRNA files are provided
    if (trna_file and os.path.exists(trna_file)) or (rrna_file and os.path.exists(rrna_file)):
        rna_features = extract_rna_features(
            genome_id=genome_id,
            trna_file=trna_file,
            rrna_file=rrna_file,
            cds_file=cds_file,
            genetic_code=genetic_code
        )
        features.update(rna_features)

    # Extract protein isoelectric point (pI) features if protein file is provided
    if faa_file and os.path.exists(faa_file):
        pi_features = extract_pi_features_for_genome(genome_id, faa_file)
        features.update(pi_features)

    # Analyze HEG sequences if CDS file, KO file, and HEG KO list are provided
    if cds_file and os.path.exists(cds_file) and ko_file and os.path.exists(ko_file) and heg_ko_list and os.path.exists(heg_ko_list):
        from deepmu.features.heg_analysis import analyze_heg_sequences
        heg_analysis = analyze_heg_sequences(cds_file, ko_file, heg_ko_list)
        # Add HEG analysis results as features
        heg_features = {
            'heg_total_sequences': heg_analysis['total_sequences'],
            'heg_count': heg_analysis['heg_sequences'],
            'bg_count': heg_analysis['bg_sequences'],
            'heg_ratio': heg_analysis['heg_ratio']
        }
        features.update(heg_features)
        logger.info(f"Added HEG analysis features: {len(heg_features)} features")

    return features

def extract_features_batch(
    metadata_file: str,
    output_dir: str = "features",
    genome_dir: Optional[str] = None,
    cds_dir: Optional[str] = None,
    faa_dir: Optional[str] = None,
    ko_dir: Optional[str] = None,
    kegg_map_file: Optional[str] = None,
    heg_ko_list: Optional[str] = None,
    advanced_codon_features: bool = True,
    num_processes: int = mp.cpu_count(),
    trna_dir: Optional[str] = None,
    rrna_dir: Optional[str] = None
) -> None:
    """
    批量提取元数据文件中所有基因组的特征

    这个函数实现了大规模基因组特征提取的并行处理，能够高效地处理大量基因组数据。
    支持多进程并行计算，显著提高处理速度。

    Args:
        metadata_file (str): 元数据文件路径，包含基因组信息的TSV文件
        output_dir (str): 保存特征文件的目录 (默认: "features")
        genome_dir (str, optional): 包含基因组文件的目录
        cds_dir (str, optional): 包含CDS文件的目录
        faa_dir (str, optional): 包含蛋白质文件的目录
        ko_dir (str, optional): 包含KO注释文件的目录
        kegg_map_file (str, optional): KEGG映射文件路径
        heg_ko_list (str, optional): 高表达基因KO列表文件路径
        advanced_codon_features (bool): 是否计算高级密码子特征 (默认: True)
        num_processes (int): 使用的进程数 (默认: CPU核心数)
        trna_dir (str, optional): 包含tRNAscan-SE输出tRNA文件的目录
        rrna_dir (str, optional): 包含Barrnap输出rRNA文件的目录

    输出：
        在output_dir中为每个基因组生成独立的特征文件（JSON格式）

    处理流程：
        1. 读取元数据文件，获取基因组列表
        2. 为每个基因组构建文件路径
        3. 使用多进程并行提取特征
        4. 保存每个基因组的特征到独立文件
        5. 生成处理报告和统计信息
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Load metadata
    metadata = pd.read_csv(metadata_file, sep='\t')

    # Create process arguments
    process_args = []
    for _, row in metadata.iterrows():
        genome_id = row['genome_id']

        # Define file paths
        genome_file = None
        if genome_dir:
            genome_file_path = os.path.join(genome_dir, f"{genome_id}.fna")
            if os.path.exists(genome_file_path):
                genome_file = genome_file_path

        # Check both naming conventions for CDS files
        cds_file = None
        if cds_dir:
            cds_file_1 = os.path.join(cds_dir, f"{genome_id}_cds.ffn")
            cds_file_2 = os.path.join(cds_dir, f"{genome_id}.ffn")
            if os.path.exists(cds_file_1):
                cds_file = cds_file_1
            elif os.path.exists(cds_file_2):
                cds_file = cds_file_2

        # Check both naming conventions for protein files
        faa_file = None
        if faa_dir:
            faa_file_1 = os.path.join(faa_dir, f"{genome_id}_cds.faa")
            faa_file_2 = os.path.join(faa_dir, f"{genome_id}.faa")
            if os.path.exists(faa_file_1):
                faa_file = faa_file_1
            elif os.path.exists(faa_file_2):
                faa_file = faa_file_2

        # Check both naming conventions for KO files
        ko_file = None
        if ko_dir:
            ko_file_1 = os.path.join(ko_dir, f"{genome_id}_ko.tsv")
            ko_file_2 = os.path.join(ko_dir, f"{genome_id}.tsv")
            if os.path.exists(ko_file_1):
                ko_file = ko_file_1
            elif os.path.exists(ko_file_2):
                ko_file = ko_file_2

        # Check both naming conventions for tRNA files
        trna_file = None
        if trna_dir:
            trna_file_1 = os.path.join(trna_dir, f"{genome_id}_tRNA.tsv")
            trna_file_2 = os.path.join(trna_dir, f"{genome_id}.tRNA")
            if os.path.exists(trna_file_1):
                trna_file = trna_file_1
            elif os.path.exists(trna_file_2):
                trna_file = trna_file_2

        # Check both naming conventions for rRNA files
        rrna_file = None
        if rrna_dir:
            rrna_file_1 = os.path.join(rrna_dir, f"{genome_id}_rrna.gff")
            rrna_file_2 = os.path.join(rrna_dir, f"{genome_id}.gff")
            if os.path.exists(rrna_file_1):
                rrna_file = rrna_file_1
            elif os.path.exists(rrna_file_2):
                rrna_file = rrna_file_2

        # Define output file
        output_file = os.path.join(output_dir, f"{genome_id}_features.npz")

        # Skip if output file already exists
        if os.path.exists(output_file):
            logger.info(f"Skipping {genome_id}, features already exist")
            continue

        # Get taxid and genetic code from metadata
        taxid = row.get('taxid', None)
        genetic_code = int(row.get('genetic_code', 11))

        # Add to process arguments
        process_args.append((
            genome_id,
            genome_file,
            cds_file,
            faa_file,
            ko_file,
            taxid,
            kegg_map_file,
            heg_ko_list,
            advanced_codon_features,
            genetic_code,
            trna_file,
            rrna_file,
            output_file
        ))

    # Process genomes sequentially or in parallel
    if num_processes > 1:
        # Process genomes in parallel
        with mp.Pool(processes=num_processes) as pool:
            list(tqdm(
                pool.starmap(_process_genome_wrapper, process_args),
                total=len(process_args),
                desc="Extracting features"
            ))
    else:
        # Process genomes sequentially
        for args in tqdm(process_args, desc="Extracting features"):
            _process_genome_wrapper(*args)

    logger.info(f"Feature extraction complete. Features saved to {output_dir}")

def _process_genome_wrapper(
    genome_id,
    genome_file,
    cds_file,
    faa_file,
    ko_file,
    taxid,
    kegg_map_file,
    heg_ko_list,
    advanced_codon_features,
    genetic_code,
    trna_file,
    rrna_file,
    output_file
):
    """Wrapper function for processing a single genome.

    Args:
        genome_id: Genome ID
        genome_file: Path to genome file
        cds_file: Path to CDS file
        faa_file: Path to protein file
        ko_file: Path to KO file
        taxid: NCBI taxonomy ID
        kegg_map_file: Path to KEGG mapping file
        heg_ko_list: Path to HEG KO list file
        advanced_codon_features: Whether to calculate advanced codon features
        genetic_code: Genetic code to use for codon translation
        trna_file: Path to tRNA file from tRNAscan-SE
        rrna_file: Path to rRNA file from Barrnap
        output_file: Path to output file
    """
    try:
        # Extract features
        features = extract_features_for_genome(
            genome_id,
            genome_file,
            cds_file,
            faa_file,
            ko_file,
            taxid,
            kegg_map_file,
            heg_ko_list,
            advanced_codon_features,
            genetic_code,
            trna_file,
            rrna_file
        )

        # Save features
        if features:
            np.savez(output_file, **features)
            logger.info(f"Extracted features for {genome_id} and saved to {output_file}")
        else:
            logger.warning(f"No features extracted for {genome_id}")
    except Exception as e:
        logger.error(f"Error processing {genome_id}: {e}")

def combine_feature_files(feature_dir: str, output_file: str) -> None:
    """Combine feature files into a single TSV file.

    Args:
        feature_dir: Directory containing feature files
        output_file: Path to output TSV file
    """
    # Get list of feature files
    feature_files = list(Path(feature_dir).glob('*_features.npz'))

    # Load features
    all_features = []
    for file in feature_files:
        try:
            features = dict(np.load(file))
            all_features.append(features)
        except Exception as e:
            logger.error(f"Error loading features from {file}: {e}")

    # Convert to DataFrame
    df = pd.DataFrame(all_features)

    # Save to TSV
    df.to_csv(output_file, sep='\t', index=False)
    logger.info(f"Combined features saved to {output_file}")

if __name__ == "__main__":
    import argparse

    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # Parse arguments
    parser = argparse.ArgumentParser(description="Extract features for DeepMu")
    parser.add_argument("--metadata", required=True, help="Path to metadata file")
    parser.add_argument("--output-dir", default="features", help="Directory to save feature files")
    parser.add_argument("--genome-dir", help="Directory containing genome files")
    parser.add_argument("--cds-dir", help="Directory containing CDS files")
    parser.add_argument("--faa-dir", help="Directory containing protein files")
    parser.add_argument("--ko-dir", help="Directory containing KO files")
    parser.add_argument("--trna-dir", help="Directory containing tRNA files from tRNAscan-SE")
    parser.add_argument("--rrna-dir", help="Directory containing rRNA files from Barrnap")
    parser.add_argument("--kegg-map", help="Path to KEGG mapping file")
    parser.add_argument("--heg-ko-list", help="Path to HEG KO list file")
    parser.add_argument("--advanced-codon-features", action="store_true", help="Calculate advanced codon features")
    parser.add_argument("--num-processes", type=int, default=mp.cpu_count(), help="Number of processes to use")
    parser.add_argument("--combine", action="store_true", help="Combine feature files into a single TSV file")
    parser.add_argument("--output-tsv", help="Path to output TSV file (if --combine is specified)")

    args = parser.parse_args()

    # Extract features
    extract_features_batch(
        args.metadata,
        args.output_dir,
        args.genome_dir,
        args.cds_dir,
        args.faa_dir,
        args.ko_dir,
        args.kegg_map,
        args.heg_ko_list,
        args.advanced_codon_features,
        args.num_processes,
        args.trna_dir,
        args.rrna_dir
    )

    # Combine feature files if requested
    if args.combine:
        if not args.output_tsv:
            args.output_tsv = os.path.join(args.output_dir, "combined_features.tsv")
        combine_feature_files(args.output_dir, args.output_tsv)