"""
DeepMu 特征提取和处理模块

这个包提供了从微生物基因组中提取和处理各种类型特征的工具，包括：

特征类型：
- 密码子使用特征 - 密码子使用偏好、密码子适应指数、RSCU等
- 基因组特征 - GC含量、基因密度、基因组大小等
- 序列特征 - 序列长度分布、重复序列、序列复杂度等
- HEG特异性特征 - 高表达基因与背景基因的差异特征
- KO谱特征 - KEGG直系同源群功能注释特征
- 分类学特征 - 系统发育信息、分类学距离等
- 代谢途径特征 - 代谢途径完整性、酶功能分布等
- 集成特征 - 多种特征类型的整合和组合

主要功能：
1. 单个基因组特征提取
2. 批量基因组特征提取
3. 特征标准化和预处理
4. 特征选择和降维
5. 特征重要性分析
6. 特征可视化

使用示例：
```python
from deepmu.features import extract_features_for_genome, CodonFeatureCalculator

# 提取单个基因组的所有特征
features = extract_features_for_genome(
    genome_id="example_genome",
    cds_file="genome_cds.fna",
    faa_file="genome_proteins.faa",
    ko_file="genome_ko.tsv"
)

# 使用密码子特征计算器
codon_calc = CodonFeatureCalculator()
codon_features = codon_calc.calculate_features(sequences)
```
"""

from deepmu.features.feature_extraction import (
    extract_features_for_genome,
    calculate_heg_features,
    load_heg_genes,
    extract_features_batch as extract_features
)

from deepmu.features.rna_features import (
    extract_rna_features,
    RNAFeatureCalculator
)

from deepmu.features.codon_features import CodonFeatureCalculator
from deepmu.features.genomic_features import GenomicFeatureCalculator
from deepmu.features.sequence_features import SequenceFeatureCalculator
from deepmu.features.pathway_features import PathwayFeatureCalculator, PathwayDatabase
from deepmu.features.ko_similarity import KOSimilarityCalculator
from deepmu.features.ko_cooccurrence import KOCooccurrenceCalculator
from deepmu.features.pathway_clustering import PathwayClusterCalculator
from deepmu.features.integrated_features import (
    extract_integrated_features,
    extract_integrated_features_batch,
    combine_feature_files
)

__all__ = [
    # Feature extraction
    'extract_features_for_genome',
    'extract_features',
    'calculate_heg_features',
    'load_heg_genes',
    'extract_rna_features',
    'RNAFeatureCalculator',

    # Feature calculators
    'CodonFeatureCalculator',
    'GenomicFeatureCalculator',
    'SequenceFeatureCalculator',
    'PathwayFeatureCalculator',
    'PathwayDatabase',
    'KOSimilarityCalculator',
    'KOCooccurrenceCalculator',
    'PathwayClusterCalculator',

    # Integrated features
    'extract_integrated_features',
    'extract_integrated_features_batch',
    'combine_feature_files'
]
