"""Taxonomy Feature Calculator Module

This module provides functionality for calculating taxonomy-based features
from NCBI taxonomy IDs, including hierarchical taxonomy information and
one-hot encoded features for different taxonomic levels.
"""

import numpy as np
from typing import Dict, List, Optional, Set, Union
import logging
import warnings

# Configure logging
logger = logging.getLogger(__name__)

class TaxonomyFeatureCalculator:
    """Calculator for taxonomy-based features."""

    def __init__(self):
        """Initialize the taxonomy feature calculator."""
        self.taxonomy_levels = ["phylum", "class", "order", "family", "genus"]

        # Try to import ETE3 for taxonomy lookups
        try:
            from ete3 import NCBITaxa
            try:
                self.ncbi = NCBITaxa()
                self.has_ete3 = True
            except Exception as e:
                self.has_ete3 = False
                warnings.warn(f"Failed to initialize NCBI taxonomy database: {e}. "
                              f"Run prepare_ncbi_taxonomy.py first to download the database.")
        except ImportError:
            self.has_ete3 = False
            warnings.warn("ETE3 package not available. Some taxonomy features will be limited.")

    def calculate_features(self, taxid: Union[str, int]) -> Dict[str, Union[int, float]]:
        """Calculate taxonomy features from a taxonomy ID.

        Args:
            taxid: NCBI taxonomy ID

        Returns:
            Dictionary of taxonomy features
        """
        if not taxid:
            return {}

        # Convert taxid to int if it's a string
        if isinstance(taxid, str):
            try:
                taxid = int(taxid)
            except ValueError:
                logger.warning(f"Invalid taxonomy ID: {taxid}")
                return {}

        # Get taxonomy lineage
        lineage = self._get_lineage(taxid)
        if not lineage:
            return {}

        # Create features dictionary
        features = {}

        # Add taxid values for each taxonomic level
        # This is more appropriate than one-hot encoding since there are many more genera than phyla
        for level in self.taxonomy_levels:
            if level in lineage and lineage[level] > 0:
                # Add the raw taxid value
                features[f'taxid_{level}'] = lineage[level]

                # Add a normalized value (using log scale to handle large taxid values)
                # This can be useful for machine learning models
                features[f'taxid_{level}_norm'] = np.log1p(lineage[level]) / 20.0  # log(10^9) ≈ 20
            else:
                features[f'taxid_{level}'] = 0
                features[f'taxid_{level}_norm'] = 0.0

        return features

    def _get_lineage(self, taxid: int) -> Dict[str, int]:
        """Get the taxonomic lineage for a taxonomy ID.

        Args:
            taxid: NCBI taxonomy ID

        Returns:
            Dictionary mapping taxonomy levels to taxonomy IDs
        """
        if not self.has_ete3:
            # Return a simple placeholder if ETE3 is not available
            return {level: taxid % (100 * (i + 1)) for i, level in enumerate(self.taxonomy_levels)}

        try:
            # Get the lineage
            lineage = self.ncbi.get_lineage(taxid)

            # Get the ranks
            ranks2levels = self.ncbi.get_rank(lineage)
            levels2ranks = {ranks2levels[t]: t for t in lineage}

            # Extract the taxids for the taxonomy levels we're interested in
            result = {}
            for level in self.taxonomy_levels:
                if level in levels2ranks:
                    result[level] = levels2ranks[level]

            return result
        except Exception as e:
            logger.error(f"Error retrieving taxonomy lineage for taxid {taxid}: {e}")
            return {}

    def get_taxonomy_string(self, taxid: int) -> str:
        """Get a pipe-separated taxonomy string for a taxonomy ID.

        Args:
            taxid: NCBI taxonomy ID

        Returns:
            Pipe-separated taxonomy string
        """
        if not self.has_ete3:
            return f"{taxid % 100}|{taxid % 200}|{taxid % 300}|{taxid % 400}|{taxid % 500}"

        try:
            # Get the lineage
            lineage = self.ncbi.get_lineage(taxid)

            # Get the ranks
            ranks2levels = self.ncbi.get_rank(lineage)
            levels2ranks = {ranks2levels[t]: t for t in lineage}

            # Extract the taxids for the taxonomy levels we're interested in
            tax_ids = []
            for level in self.taxonomy_levels:
                if level in levels2ranks:
                    tax_ids.append(str(levels2ranks[level]))
                else:
                    tax_ids.append("0")  # Use 0 for missing ranks

            # Return pipe-separated string
            return "|".join(tax_ids)
        except Exception as e:
            logger.error(f"Error retrieving taxonomy string for taxid {taxid}: {e}")
            return ""
