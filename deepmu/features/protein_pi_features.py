#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Module for calculating protein isoelectric point (pI) features.

This module provides functions to calculate the isoelectric point (pI)
of proteins in a genome and extract features based on the pI distribution.
"""

import os
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union
from Bio import SeqIO
from Bio.SeqUtils.ProtParam import ProteinAnalysis
from Bio.SeqUtils import IsoelectricPoint

# Set up logging
logger = logging.getLogger(__name__)

class ProteinPICalculator:
    """
    Class for calculating protein isoelectric point (pI) features.
    """

    def __init__(self):
        """Initialize the ProteinPICalculator."""
        # Amino acid validation set
        self.valid_aa = set('ACDEFGHIKLMNPQRSTVWY')

    def calculate_protein_pi(self, sequence: str) -> Optional[float]:
        """
        Calculate the isoelectric point (pI) of a protein sequence.

        Args:
            sequence: Protein sequence

        Returns:
            Isoelectric point (pI) or None if calculation fails
        """
        try:
            # Validate sequence
            sequence = sequence.upper()

            # Remove stop codons (*) if present
            sequence = sequence.replace('*', '')

            # Check if sequence is empty after removing stop codons
            if not sequence:
                logger.warning("Empty sequence after removing stop codons")
                return None

            # Check for invalid amino acids
            if not set(sequence).issubset(self.valid_aa):
                invalid_chars = set(sequence) - self.valid_aa
                logger.warning(f"Invalid amino acids {invalid_chars} - skipping")
                return None

            # Calculate pI
            prot_analysis = ProteinAnalysis(sequence)
            pi = prot_analysis.isoelectric_point()
            return pi

        except Exception as e:
            logger.error(f"Error calculating pI: {str(e)}")
            return None

    def calculate_proteome_pi(self, faa_file: str) -> Dict[str, float]:
        """
        Calculate isoelectric points for all proteins in a FASTA file.

        Args:
            faa_file: Path to input FASTA file

        Returns:
            Dictionary mapping protein IDs to pI values
        """
        # Check if file exists
        if not os.path.exists(faa_file):
            logger.error(f"File not found: {faa_file}")
            return {}

        # Storage for results
        results = {}

        # Process each protein sequence
        for record in SeqIO.parse(faa_file, 'fasta'):
            try:
                # Get protein sequence
                seq = str(record.seq).upper()
                protein_id = record.id

                # Skip empty sequences
                if len(seq) == 0:
                    logger.warning(f"Empty sequence for {protein_id}")
                    continue

                # Calculate pI
                pi = self.calculate_protein_pi(seq)
                if pi is not None:
                    results[protein_id] = pi

            except Exception as e:
                logger.error(f"Error processing {record.id}: {str(e)}")
                continue

        return results

    def extract_pi_features(self, pi_values: Dict[str, float]) -> Dict[str, float]:
        """
        Extract features from the pI distribution.

        Args:
            pi_values: Dictionary mapping protein IDs to pI values

        Returns:
            Dictionary of pI features
        """
        # Check if there are any pI values
        if not pi_values:
            logger.warning("No pI values to extract features from")
            return {
                'pi_mean': 0.0,
                'pi_median': 0.0,
                'pi_std': 0.0,
                'pi_min': 0.0,
                'pi_max': 0.0,
                'pi_range': 0.0,
                'pi_acidic_fraction': 0.0,
                'pi_basic_fraction': 0.0,
                'pi_neutral_fraction': 0.0,
                'pi_bimodality': 0.0
            }

        # Convert to numpy array
        values = np.array(list(pi_values.values()))

        # Calculate basic statistics
        pi_mean = np.mean(values)
        pi_median = np.median(values)
        pi_std = np.std(values)
        pi_min = np.min(values)
        pi_max = np.max(values)
        pi_range = pi_max - pi_min

        # Calculate fractions
        acidic_count = np.sum(values < 7.0)
        basic_count = np.sum(values > 7.0)
        neutral_count = np.sum((values >= 6.5) & (values <= 7.5))

        pi_acidic_fraction = acidic_count / len(values)
        pi_basic_fraction = basic_count / len(values)
        pi_neutral_fraction = neutral_count / len(values)

        # Calculate bimodality (difference between acidic and basic peaks)
        try:
            # Create histogram
            hist, bin_edges = np.histogram(values, bins=20, range=(3, 13))
            bin_centers = (bin_edges[:-1] + bin_edges[1:]) / 2

            # Simple peak finding algorithm
            # A point is a peak if it's higher than its neighbors
            peaks = []
            for i in range(1, len(hist)-1):
                if hist[i] > hist[i-1] and hist[i] > hist[i+1]:
                    peaks.append(i)

            if len(peaks) >= 2:
                # Sort peaks by height
                peak_heights = [hist[i] for i in peaks]
                sorted_peaks = [x for _, x in sorted(zip(peak_heights, peaks), reverse=True)]
                top_peaks = sorted_peaks[:2]

                # Calculate bimodality as distance between top two peaks
                pi_bimodality = abs(bin_centers[top_peaks[0]] - bin_centers[top_peaks[1]])
            else:
                pi_bimodality = 0.0

        except Exception as e:
            logger.warning(f"Error calculating bimodality: {str(e)}")
            pi_bimodality = 0.0

        # Return features
        return {
            'pi_mean': pi_mean,
            'pi_median': pi_median,
            'pi_std': pi_std,
            'pi_min': pi_min,
            'pi_max': pi_max,
            'pi_range': pi_range,
            'pi_acidic_fraction': pi_acidic_fraction,
            'pi_basic_fraction': pi_basic_fraction,
            'pi_neutral_fraction': pi_neutral_fraction,
            'pi_bimodality': pi_bimodality
        }

def calculate_pi_features(genome_id: str, faa_file: str) -> Dict[str, float]:
    """
    Calculate protein isoelectric point (pI) features for a genome.

    Args:
        genome_id: Genome ID
        faa_file: Path to protein FASTA file

    Returns:
        Dictionary of pI features
    """
    logger.info(f"Calculating pI features for {genome_id}")

    # Initialize calculator
    calculator = ProteinPICalculator()

    # Calculate pI values
    pi_values = calculator.calculate_proteome_pi(faa_file)
    logger.info(f"Calculated pI for {len(pi_values)} proteins in {genome_id}")

    # Extract features
    features = calculator.extract_pi_features(pi_values)

    # Add genome ID prefix to feature names
    return {f"protein_{key}": value for key, value in features.items()}

def extract_pi_features_for_genome(genome_id: str, faa_file: Optional[str] = None) -> Dict[str, float]:
    """
    Extract protein isoelectric point (pI) features for a genome.

    Args:
        genome_id: Genome ID
        faa_file: Path to protein FASTA file

    Returns:
        Dictionary of pI features
    """
    if faa_file is None or not os.path.exists(faa_file):
        logger.warning(f"Protein FASTA file not found for {genome_id}")
        return {}

    return calculate_pi_features(genome_id, faa_file)

if __name__ == "__main__":
    # Set up logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    # Example usage
    import argparse

    parser = argparse.ArgumentParser(description='Calculate protein isoelectric point (pI) features')
    parser.add_argument('--faa', required=True, help='Path to protein FASTA file')
    parser.add_argument('--genome-id', required=True, help='Genome ID')

    args = parser.parse_args()

    # Calculate features
    features = extract_pi_features_for_genome(args.genome_id, args.faa)

    # Print features
    for key, value in features.items():
        print(f"{key}: {value}")
