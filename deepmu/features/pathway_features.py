"""
代谢途径特征模块

这个模块提供了分析微生物基因组中基因功能和代谢途径的功能，
包括KEGG途径映射和特征提取。

主要功能：
1. KEGG途径映射 - 将KO注释映射到代谢途径
2. 途径完整性分析 - 计算代谢途径的完整程度
3. 功能分类 - 基于KEGG功能分类的特征提取
4. 模块分析 - KEGG模块水平的功能分析
5. 层次化途径组织 - 支持多层次的途径分类

特征类型：
- 途径存在性特征 - 二进制特征表示途径是否存在
- 途径完整性特征 - 连续值表示途径的完整程度
- 功能类别特征 - 基于KEGG功能分类的统计特征
- 酶功能特征 - 特定酶功能的存在和丰度
- 代谢网络特征 - 代谢网络连通性和复杂度

应用价值：
代谢途径特征能够反映微生物的代谢能力和环境适应性，
对于预测生长速率和最适温度具有重要意义。
"""

import os
import numpy as np
from collections import defaultdict
from typing import Dict, List, Tuple, Any
import logging

# Set up logger
logger = logging.getLogger(__name__)

class PathwayDatabase:
    """
    增强的KEGG直系同源群(KO)术语和途径之间映射的数据库类

    这个类提供了全面的功能来加载KEGG映射并将注释的KO术语转换为
    用于机器学习模型的途径特征。它包括层次化途径组织、途径描述
    和功能分类，以提高可解释性。

    核心功能：
    - KEGG映射加载和解析
    - KO到途径的映射转换
    - 层次化途径组织管理
    - 途径描述和功能分类
    - 特征名称生成和管理

    数据结构：
    - kegg_map: KO到途径的映射字典
    - pathway_db: 途径信息数据库
    - pathway_categories: 途径功能分类
    - module_db: KEGG模块数据库
    - feature_names: 特征名称列表
    """

    def __init__(self, kegg_path: str = None, module_path: str = None):
        """
        初始化增强的途径数据库

        Args:
            kegg_path (str): KEGG映射文件路径 (ko00001.keg格式)
            module_path (str): KEGG模块映射文件路径 (可选)
        """
        self.kegg_map = {}
        self.pathway_lengths = defaultdict(int)
        self.pathway_db = {}
        self.module_db = {}
        self.pathway_categories = {}
        self.feature_names = []

        if kegg_path is not None and os.path.exists(kegg_path):
            self.kegg_map, self.pathway_db, self.pathway_categories = self._load_kegg_mappings(kegg_path)
            self.feature_names = self._get_feature_names()
        else:
            # Initialize with empty data if no valid kegg_path is provided
            self.kegg_map = {}
            self.pathway_db = {}
            self.pathway_categories = {}
            self.feature_names = []

        if module_path is not None and os.path.exists(module_path):
            self.module_db = self._load_module_mappings(module_path)
        else:
            self.module_db = {}

    @property
    def num_features(self) -> int:
        """Return the number of pathway features."""
        return len(self.feature_names)

    def _load_kegg_mappings(self, kegg_path: str) -> Tuple[Dict[str, List[str]], Dict[str, Dict], Dict[str, str]]:
        """
        Load enhanced KEGG mappings from a KO hierarchy file.

        Args:
            kegg_path: Path to KEGG mapping file

        Returns:
            Tuple of:
            - Dictionary mapping KO terms to lists of pathway IDs
            - Dictionary with pathway metadata
            - Dictionary mapping pathways to their categories
        """
        ko_to_pathways = defaultdict(list)
        pathway_to_kos = defaultdict(list)
        pathway_db = {}
        pathway_categories = {}

        try:
            # Parse the KEGG hierarchy file
            with open(kegg_path, 'r') as f:
                current_category = None
                current_pathway = None
                current_pathway_name = None

                for line in f:
                    line = line.strip()
                    if not line or line.startswith('#'):
                        # Skip empty lines and comments
                        continue

                    # Check for category headers [Category]
                    if line.startswith('[') and line.endswith(']'):
                        current_category = line[1:-1].strip()
                        continue

                    # Check for pathway entries (ko00010\tGlycolysis / Gluconeogenesis)
                    if line.startswith('ko') and '\t' in line:
                        parts = line.split('\t', 1)
                        if len(parts) == 2:
                            pathway_id = parts[0].strip()
                            pathway_name = parts[1].strip()
                            current_pathway = pathway_id
                            current_pathway_name = pathway_name

                            # Store pathway metadata
                            pathway_db[pathway_id] = {
                                'name': pathway_name,
                                'category': current_category,
                                'kos': []
                            }
                            pathway_categories[pathway_id] = current_category
                        continue

                    # Check for KO entries (K00001\tenzyme [EC:*******])
                    if line.startswith('K') and '\t' in line and current_pathway:
                        parts = line.split('\t', 1)
                        if len(parts) == 2:
                            ko_id = parts[0].strip()
                            ko_name = parts[1].strip()

                            ko_to_pathways[ko_id].append(current_pathway)
                            pathway_to_kos[current_pathway].append(ko_id)

                            # Add KO to pathway metadata
                            pathway_db[current_pathway]['kos'].append({
                                'id': ko_id,
                                'name': ko_name
                            })

            # Calculate pathway lengths (number of KOs)
            for pathway, kos in pathway_to_kos.items():
                self.pathway_lengths[pathway] = len(kos)
                if pathway in pathway_db:
                    pathway_db[pathway]['ko_count'] = len(kos)

            # Print debug information
            print(f"Loaded {len(pathway_db)} pathways and {len(ko_to_pathways)} KO terms from {kegg_path}")
            if len(pathway_db) > 0:
                print(f"Example pathway: {list(pathway_db.keys())[0]} with {len(pathway_to_kos.get(list(pathway_db.keys())[0], []))} KOs")
            if len(ko_to_pathways) > 0:
                print(f"Example KO: {list(ko_to_pathways.keys())[0]} maps to {len(ko_to_pathways.get(list(ko_to_pathways.keys())[0], []))} pathways")

            return dict(ko_to_pathways), pathway_db, pathway_categories

        except Exception as e:
            print(f"Error loading KEGG mappings: {e}")
            return {}, {}, {}

    def _load_module_mappings(self, module_path: str) -> Dict[str, Dict]:
        """
        Load KEGG module mappings for more detailed pathway analysis.

        Args:
            module_path: Path to KEGG module mapping file

        Returns:
            Dictionary with module metadata
        """
        module_db = {}

        try:
            # Parse the module file (simplified example)
            with open(module_path, 'r') as f:
                current_module = None
                for line in f:
                    line = line.strip()
                    if line.startswith('ENTRY'):
                        parts = line.split(maxsplit=2)
                        if len(parts) > 1:
                            module_id = parts[1]
                            current_module = module_id
                            module_db[module_id] = {'kos': []}
                    elif line.startswith('NAME') and current_module:
                        parts = line.split(maxsplit=1)
                        if len(parts) > 1:
                            module_db[current_module]['name'] = parts[1]
                    elif line.startswith('DEFINITION') and current_module:
                        parts = line.split(maxsplit=1)
                        if len(parts) > 1:
                            module_db[current_module]['definition'] = parts[1]
                    elif line.startswith('ORTHOLOGY') and current_module:
                        parts = line.split(maxsplit=2)
                        if len(parts) > 2 and parts[1].startswith('K'):
                            module_db[current_module]['kos'].append({
                                'id': parts[1],
                                'name': parts[2]
                            })
            return module_db
        except Exception as e:
            print(f"Error loading module mappings: {e}")
            return {}

    def _get_feature_names(self) -> List[str]:
        """
        Get ordered list of pathway IDs for consistent feature ordering.

        Returns:
            List of pathway IDs
        """
        # Get unique pathway IDs
        pathways = set()
        for ko, pathway_list in self.kegg_map.items():
            pathways.update(pathway_list)

        # Return sorted list for consistent ordering
        return sorted(list(pathways))

    def map_to_pathways(self, ko_list: List[str]) -> Dict[str, float]:
        """
        Convert KO terms to pathway presence vector with enhanced normalization.

        Args:
            ko_list: List of KO terms annotated in a genome

        Returns:
            Dictionary mapping pathway IDs to coverage values
        """
        pathway_counts = defaultdict(int)
        ko_set = set(ko_list)  # For faster lookups

        # Count KOs per pathway
        for ko in ko_set:
            for pathway in self.kegg_map.get(ko, []):
                pathway_counts[pathway] += 1

        # Create feature dictionary with normalized pathway coverage
        features_dict = {}
        for i, pathway in enumerate(self.feature_names):
            count = pathway_counts.get(pathway, 0)
            max_genes = self.pathway_lengths[pathway]

            # Enhanced normalization with sigmoid scaling for better signal
            if max_genes > 0:
                # Calculate raw coverage
                raw_coverage = count / max_genes

                # Apply sigmoid scaling to emphasize differences in mid-range
                # This helps distinguish between partial and complete pathways
                if raw_coverage > 0:
                    # Adjust scaling to emphasize differences around 50% coverage
                    scaled_coverage = 1.0 / (1.0 + np.exp(-10 * (raw_coverage - 0.5)))
                    features_dict[pathway] = scaled_coverage
                else:
                    features_dict[pathway] = 0.0
            else:
                features_dict[pathway] = 0.0

        return features_dict

    def get_pathway_info(self, pathway_id: str) -> Dict:
        """
        Get detailed information about a pathway.

        Args:
            pathway_id: KEGG pathway ID (e.g., 'ko00010')

        Returns:
            Dictionary with pathway information
        """
        if pathway_id in self.pathway_db:
            return self.pathway_db[pathway_id]
        return {'name': pathway_id, 'category': 'Unknown', 'kos': []}

    def get_pathway_category(self, pathway_id: str) -> str:
        """
        Get the functional category of a pathway.

        Args:
            pathway_id: KEGG pathway ID

        Returns:
            Category name as string
        """
        return self.pathway_categories.get(pathway_id, 'Unknown')


class PathwayFeatureCalculator:
    """
    Enhanced pathway feature calculator with comprehensive analysis capabilities.

    This class provides advanced functionality to calculate pathway features from
    pre-annotated KO terms, with support for pathway enrichment analysis,
    functional categorization, and metabolic capacity scoring.
    """

    def __init__(self, kegg_path: str = None, module_path: str = None):
        """
        Initialize the enhanced pathway feature calculator.

        Args:
            kegg_path: Path to KEGG mapping file
            module_path: Path to KEGG module mapping file (optional)
        """
        self.pathway_db = PathwayDatabase(kegg_path, module_path)

        # Define pathway categories for functional analysis
        self.pathway_categories = {
            'energy': [
                'ko00010', 'ko00020', 'ko00030', 'ko00190', 'ko00195', 'ko00710', 'ko00720',
                'ko00680', 'ko00910', 'ko00920'  # Additional energy pathways
            ],
            'carbohydrate': [
                'ko00040', 'ko00051', 'ko00052', 'ko00500', 'ko00520', 'ko00620', 'ko00630',
                'ko00640', 'ko00650'  # Carbohydrate metabolism
            ],
            'lipid': [
                'ko00061', 'ko00071', 'ko00072', 'ko00073', 'ko00100', 'ko00120', 'ko00140',
                'ko00561', 'ko00564', 'ko00565', 'ko00600', 'ko00590'  # Lipid metabolism
            ],
            'nucleotide': [
                'ko00230', 'ko00240', 'ko00760', 'ko00770'  # Nucleotide metabolism
            ],
            'amino_acid': [
                'ko00250', 'ko00260', 'ko00270', 'ko00280', 'ko00290', 'ko00300', 'ko00310',
                'ko00330', 'ko00340', 'ko00350', 'ko00360', 'ko00380', 'ko00400'  # Amino acid metabolism
            ],
            'glycan': [
                'ko00510', 'ko00511', 'ko00512', 'ko00513', 'ko00514', 'ko00515', 'ko00531',
                'ko00532', 'ko00533', 'ko00534', 'ko00540', 'ko00550', 'ko00563'  # Glycan metabolism
            ],
            'cofactor': [
                'ko00730', 'ko00740', 'ko00750', 'ko00760', 'ko00770', 'ko00780', 'ko00785',
                'ko00790', 'ko00830', 'ko00860', 'ko00130'  # Cofactor and vitamin metabolism
            ],
            'xenobiotics': [
                'ko00362', 'ko00627', 'ko00980', 'ko00982', 'ko00983'  # Xenobiotics biodegradation
            ],
            'terpenoid': [
                'ko00900', 'ko00902', 'ko00903', 'ko00904', 'ko00906', 'ko00908', 'ko00909',
                'ko00981'  # Terpenoid and polyketide metabolism
            ],
            'secondary_metabolites': [
                'ko00940', 'ko00941', 'ko00942', 'ko00943', 'ko00944', 'ko00945', 'ko00950',
                'ko00960', 'ko00965', 'ko00966'  # Secondary metabolite biosynthesis
            ]
        }

    def calculate_features(self, ko_terms: List[str]) -> Dict[str, float]:
        """
        Calculate pathway features from pre-annotated KO terms.

        Args:
            ko_terms: List of pre-annotated KO terms

        Returns:
            Dictionary mapping pathway IDs to coverage values
        """
        if not ko_terms:
            logger.warning("No valid KO terms provided")
            return {}

        return self.pathway_db.map_to_pathways(ko_terms)

    def calculate_features_from_file(self, ko_file: str) -> Dict[str, float]:
        """
        Calculate pathway features from a file containing KO terms.

        Args:
            ko_file: Path to file containing KO terms

        Returns:
            Dictionary mapping pathway IDs to coverage values
        """
        ko_terms = []
        try:
            with open(ko_file, 'r') as f:
                for line in f:
                    parts = line.strip().split('\t')
                    if len(parts) >= 2:
                        ko = parts[1]
                        ko_terms.append(ko)
        except Exception as e:
            logger.error(f"Error reading KO file {ko_file}: {e}")
            raise ValueError("No valid KO terms found in file")

        return self.calculate_features(ko_terms=ko_terms)

    def get_enriched_pathways(self, pathway_features: np.ndarray, threshold: float = 0.5) -> List[Dict]:
        """
        Get a list of enriched pathways with detailed information.

        Args:
            pathway_features: Numpy array of pathway features
            threshold: Minimum coverage threshold for considering a pathway enriched

        Returns:
            List of dictionaries with pathway information for enriched pathways
        """
        enriched = []

        for i, score in enumerate(pathway_features):
            # Ensure score is a float
            try:
                score_float = float(score)
                if score_float >= threshold:
                    pathway_id = self.pathway_db.feature_names[i]
                    # Get detailed pathway information
                    pathway_info = self.pathway_db.get_pathway_info(pathway_id)
                    # Add score to the info
                    pathway_info['score'] = score_float
                    pathway_info['id'] = pathway_id
                    enriched.append(pathway_info)
            except (ValueError, TypeError):
                # Skip invalid scores
                continue

        # Sort by score descending
        return sorted(enriched, key=lambda x: x['score'], reverse=True)

    def calculate_functional_profile(self, pathway_features: np.ndarray) -> Dict[str, float]:
        """
        Calculate a comprehensive functional profile from pathway features.

        Args:
            pathway_features: Numpy array of pathway features

        Returns:
            Dictionary mapping functional categories to scores
        """
        profile = {}

        # Calculate scores for each functional category
        for category, pathway_list in self.pathway_categories.items():
            # Find indices of pathways in this category
            indices = [i for i, pathway in enumerate(self.pathway_db.feature_names)
                      if pathway in pathway_list]

            if indices:
                # Calculate mean score for this category
                category_score = float(np.mean(pathway_features[indices]))
                # Apply sigmoid scaling for better differentiation
                scaled_score = 1.0 / (1.0 + np.exp(-5 * (category_score - 0.5)))
                profile[category] = scaled_score
            else:
                profile[category] = 0.0

        return profile

    def calculate_metabolism_scores(self, pathway_features: np.ndarray) -> Dict[str, float]:
        """
        Calculate enhanced metabolism scores from pathway features.

        Args:
            pathway_features: Numpy array of pathway features

        Returns:
            Dictionary of metabolism scores with detailed breakdown
        """
        # Ensure all values are floats
        try:
            pathway_features = np.array([float(x) for x in pathway_features])
        except (ValueError, TypeError) as e:
            logger.warning(f"Error converting pathway features to float: {e}")
            # Create a safe version with zeros for non-numeric values
            safe_features = []
            for x in pathway_features:
                try:
                    safe_features.append(float(x))
                except (ValueError, TypeError):
                    safe_features.append(0.0)
            pathway_features = np.array(safe_features)

        # Get functional profile
        functional_profile = self.calculate_functional_profile(pathway_features)

        # Calculate higher-level scores
        scores = {}

        # Energy metabolism score (weighted average of energy pathways)
        scores['energy_metabolism'] = functional_profile.get('energy', 0.0)

        # Biosynthesis capacity (weighted average of biosynthesis-related categories)
        biosynthesis_score = (
            0.3 * functional_profile.get('amino_acid', 0.0) +
            0.2 * functional_profile.get('nucleotide', 0.0) +
            0.2 * functional_profile.get('lipid', 0.0) +
            0.15 * functional_profile.get('carbohydrate', 0.0) +
            0.15 * functional_profile.get('cofactor', 0.0)
        )
        scores['biosynthesis_capacity'] = biosynthesis_score

        # Specialized metabolism (secondary metabolites and xenobiotics)
        specialized_score = (
            0.5 * functional_profile.get('secondary_metabolites', 0.0) +
            0.3 * functional_profile.get('terpenoid', 0.0) +
            0.2 * functional_profile.get('xenobiotics', 0.0)
        )
        scores['specialized_metabolism'] = specialized_score

        # Overall metabolic capacity (weighted average of all scores)
        scores['metabolic_capacity'] = (
            0.4 * scores['energy_metabolism'] +
            0.4 * scores['biosynthesis_capacity'] +
            0.2 * scores['specialized_metabolism']
        )

        # Add raw functional profile for detailed analysis
        scores['functional_profile'] = functional_profile

        return scores

    def analyze_pathway_completeness(self, ko_terms: List[str]) -> Dict[str, Dict]:
        """
        Analyze the completeness of all metabolic pathways.

        Args:
            ko_terms: List of KO terms annotated in a genome

        Returns:
            Dictionary mapping pathway IDs to completeness information
        """
        # Convert KO terms to a set for faster lookups
        ko_set = set(ko_terms)

        # Calculate pathway features
        pathway_features = self.pathway_db.map_to_pathways(ko_terms)

        # Analyze completeness of each pathway
        completeness_info = {}

        for pathway_id, score in pathway_features.items():
            # Get detailed pathway information
            pathway_data = self.pathway_db.get_pathway_info(pathway_id)

            # Calculate which KOs are present/missing
            present_kos = []
            missing_kos = []

            for ko_info in pathway_data.get('kos', []):
                ko_id = ko_info['id']
                if ko_id in ko_set:
                    present_kos.append(ko_info)
                else:
                    missing_kos.append(ko_info)

            # Calculate completeness metrics
            total_kos = len(present_kos) + len(missing_kos)

            if total_kos > 0:
                completeness = float(len(present_kos)) / float(total_kos)

                # Store completeness information
                completeness_info[pathway_id] = {
                    'name': pathway_data.get('name', pathway_id),
                    'category': pathway_data.get('category', 'Unknown'),
                    'completeness': completeness,
                    'score': float(score),
                    'present_kos': present_kos,
                    'missing_kos': missing_kos,
                    'total_kos': total_kos
                }

        return completeness_info

    def _calculate_gini(self, array: np.ndarray) -> float:
        """
        Calculate the Gini coefficient of an array.

        The Gini coefficient measures inequality in a distribution.
        A value of 0 indicates perfect equality, while 1 indicates maximum inequality.

        Args:
            array: Numpy array of values

        Returns:
            Gini coefficient as a float between 0 and 1
        """
        # Sort the array
        sorted_array = np.sort(array)
        n = len(sorted_array)
        if n == 0:
            return 0.0

        # Calculate the Gini coefficient
        index = np.arange(1, n + 1)
        return (np.sum((2 * index - n - 1) * sorted_array)) / (n * np.sum(sorted_array))
