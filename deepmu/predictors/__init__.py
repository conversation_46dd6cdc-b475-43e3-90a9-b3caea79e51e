"""
DeepMu 预测器模块

这个模块提供了微生物生长速率和最适生长温度的预测器。

主要功能：
1. 单个微生物预测 - 预测单个基因组的生长参数
2. 多任务预测 - 同时预测生长速率和最适温度
3. 批量预测 - 高效处理多个基因组
4. 群落预测 - 基于宏基因组数据的群落水平预测
5. 特征重要性分析 - 分析影响预测的关键特征
6. 不确定性量化 - 提供预测结果的置信区间

核心类：
- MicrobialGrowthPredictor: 主要的预测器类，支持所有预测功能

使用示例：
```python
from deepmu.predictors import MicrobialGrowthPredictor

# 初始化预测器（多任务模式）
predictor = MicrobialGrowthPredictor(multi_task=True)

# 多任务预测
result = predictor.predict_multi_task(
    cds_fasta_path="genome_cds.fna",
    ko_file="genome_ko.tsv"
)

print(f"生长速率: {result['growth_rate']:.4f} h⁻¹")
print(f"最适温度: {result['optimal_temperature']:.2f} °C")
```

技术特点：
- 支持GPU加速推理
- 内存优化的批量处理
- 多种输入格式支持
- 详细的错误处理和日志
- 可配置的模型参数
"""

from .predictor import MicrobialGrowthPredictor

__all__ = ['MicrobialGrowthPredictor']
