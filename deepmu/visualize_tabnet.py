#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import argparse
import numpy as np
import pandas as pd
import torch
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import StandardScaler

from deepmu.models.tabnet_model import TabNetRegressor
from deepmu.utils.logging import get_logger


def parse_args():
    parser = argparse.ArgumentParser(description='Visualize TabNet feature importance')
    
    parser.add_argument('--data_path', type=str, default='data/', help='Path to data directory')
    parser.add_argument('--model_path', type=str, default='models/tabnet/best_tabnet_model.pt', help='Path to saved model')
    parser.add_argument('--output_path', type=str, default='results/tabnet/', help='Path to save visualizations')
    parser.add_argument('--n_examples', type=int, default=10, help='Number of examples to visualize')
    
    # TabNet parameters (must match the trained model)
    parser.add_argument('--n_d', type=int, default=64, help='Dimension of feature transform')
    parser.add_argument('--n_a', type=int, default=64, help='Dimension of attentive transform')
    parser.add_argument('--n_steps', type=int, default=3, help='Number of steps in TabNet')
    parser.add_argument('--gamma', type=float, default=1.3, help='Scale for feature reusage')
    parser.add_argument('--n_shared', type=int, default=2, help='Number of shared decision steps')
    parser.add_argument('--n_independent', type=int, default=2, help='Number of independent decision steps')
    parser.add_argument('--virtual_batch_size', type=int, default=128, help='Virtual batch size for batch norm')
    parser.add_argument('--mask_type', type=str, default='sparsemax', help='Type of mask to use')
    
    return parser.parse_args()


def load_data(data_path):
    """Load and prepare data for visualization."""
    # Load data
    df = pd.read_csv(os.path.join(data_path, 'train.csv'))
    
    # Get feature names
    feature_names = df.drop('target', axis=1).columns.tolist()
    
    # Split features and target
    X = df.drop('target', axis=1).values
    y = df['target'].values.reshape(-1, 1)
    
    # Standardize features
    scaler = StandardScaler()
    X = scaler.fit_transform(X)
    
    return X, y, feature_names


def load_model(args, input_dim):
    """Load the trained TabNet model."""
    # Create model with the same architecture
    model = TabNetRegressor(
        input_dim=input_dim,
        output_dim=1,
        n_d=args.n_d,
        n_a=args.n_a,
        n_steps=args.n_steps,
        gamma=args.gamma,
        n_independent=args.n_independent,
        n_shared=args.n_shared,
        virtual_batch_size=args.virtual_batch_size,
        mask_type=args.mask_type,
    )
    
    # Load the trained parameters
    model.load_state_dict(torch.load(args.model_path, map_location=torch.device('cpu')))
    model.eval()
    
    return model


def visualize_feature_importance(model, X, feature_names, args):
    """
    Visualize feature importance based on TabNet masks.
    
    Args:
        model: Trained TabNet model
        X: Input features
        feature_names: List of feature names
        args: Command line arguments
    """
    # Create output directory
    os.makedirs(args.output_path, exist_ok=True)
    
    # Choose random samples to visualize
    n_examples = min(args.n_examples, X.shape[0])
    indices = np.random.choice(X.shape[0], size=n_examples, replace=False)
    
    # Convert to tensor
    X_tensor = torch.FloatTensor(X[indices])
    
    # Get predictions and masks
    with torch.no_grad():
        preds, masks = model.forward_masks(X_tensor)
    
    # Compute global feature importance
    if masks:
        # Aggregate masks from all steps
        global_masks = torch.zeros(X_tensor.shape[1])
        
        for mask in masks:
            # Average mask values across samples
            avg_mask = mask.mean(dim=0)
            global_masks += avg_mask.cpu()
        
        # Normalize
        global_masks /= len(masks)
        
        # Sort features by importance
        feature_importance = global_masks.numpy()
        sorted_idx = np.argsort(feature_importance)[::-1]
        sorted_features = [feature_names[i] for i in sorted_idx]
        sorted_importance = feature_importance[sorted_idx]
        
        # Plot global feature importance
        plt.figure(figsize=(12, 8))
        plt.barh(range(len(sorted_features[:20])), sorted_importance[:20], align='center')
        plt.yticks(range(len(sorted_features[:20])), sorted_features[:20])
        plt.xlabel('Feature Importance')
        plt.title('TabNet Global Feature Importance (Top 20)')
        plt.tight_layout()
        plt.savefig(os.path.join(args.output_path, 'global_feature_importance.png'), dpi=300)
        plt.close()
        
        # Save feature importance to CSV
        importance_df = pd.DataFrame({
            'Feature': sorted_features,
            'Importance': sorted_importance
        })
        importance_df.to_csv(os.path.join(args.output_path, 'feature_importance.csv'), index=False)
        
        # Visualize individual examples
        for i in range(n_examples):
            plt.figure(figsize=(15, 10))
            
            # For each step
            n_steps = len(masks)
            for step in range(n_steps):
                plt.subplot(1, n_steps, step + 1)
                
                # Get mask for this example
                mask_values = masks[step][i].cpu().numpy()
                
                # Sort by importance
                sorted_idx = np.argsort(mask_values)[::-1]
                top_features = [feature_names[j] for j in sorted_idx[:15]]
                top_values = mask_values[sorted_idx[:15]]
                
                # Plot
                plt.barh(range(len(top_features)), top_values, align='center')
                plt.yticks(range(len(top_features)), top_features)
                plt.title(f'Step {step+1} Feature Importance')
                
            plt.tight_layout()
            plt.savefig(os.path.join(args.output_path, f'example_{i+1}_feature_importance.png'), dpi=300)
            plt.close()
            
        # Create heatmap of feature importance across steps
        if masks:
            # Get top 20 features
            top_indices = sorted_idx[:20]
            top_features = [feature_names[i] for i in top_indices]
            
            # Collect mask values for each step
            step_importances = []
            for mask in masks:
                # Average mask values across samples
                avg_mask = mask.mean(dim=0)
                step_importances.append(avg_mask.cpu().numpy()[top_indices])
            
            # Create heatmap
            plt.figure(figsize=(10, 12))
            sns.heatmap(np.array(step_importances), 
                        cmap='viridis', 
                        yticklabels=[f'Step {i+1}' for i in range(len(masks))],
                        xticklabels=top_features)
            plt.title('Feature Importance Across Steps (Top 20 Features)')
            plt.tight_layout()
            plt.savefig(os.path.join(args.output_path, 'feature_importance_heatmap.png'), dpi=300)
            plt.close()
    else:
        print("No masks were computed during the forward pass.")


def main():
    """Main function."""
    args = parse_args()
    logger = get_logger('TabNetVisualizer')
    
    # Load data
    logger.info("Loading data...")
    X, y, feature_names = load_data(args.data_path)
    
    # Load model
    logger.info("Loading model...")
    model = load_model(args, X.shape[1])
    
    # Visualize feature importance
    logger.info("Visualizing feature importance...")
    visualize_feature_importance(model, X, feature_names, args)
    
    logger.info(f"Visualizations saved to {args.output_path}")


if __name__ == "__main__":
    main() 