"""
DeepMu 数据集模块。

此模块提供用于加载预计算特征的数据集类，
用于微生物生长速率和最适温度预测。
"""

import torch
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Callable
from torch.utils.data import Dataset
from pathlib import Path
import logging

# 设置日志
logger = logging.getLogger(__name__)

class DeepMuDataset(Dataset):
    """
    用于加载 DeepMu 预计算特征的数据集。

    此数据集从 NPZ 文件加载预计算特征，从 TSV 文件加载元数据，
    为训练模型提供便捷的接口。
    """

    def __init__(
        self,
        metadata_file: str,
        feature_dir: str,
        target_columns: List[str] = ['growth_rate', 'optimal_temperature'],
        feature_transform: Optional[Callable] = None,
        target_transform: Optional[Callable] = None
    ):
        """
        初始化 DeepMu 数据集。

        参数:
            metadata_file: 元数据 TSV 文件路径
            feature_dir: 包含特征 NPZ 文件的目录
            target_columns: 元数据中目标列名称列表
            feature_transform: 应用于特征的可选变换
            target_transform: 应用于目标的可选变换
        """
        self.metadata_file = metadata_file
        self.feature_dir = Path(feature_dir)
        self.target_columns = target_columns
        self.feature_transform = feature_transform
        self.target_transform = target_transform

        # Load metadata
        self.metadata = pd.read_csv(metadata_file, sep='\t')

        # Filter to genomes with feature files
        self.genome_ids = []
        for genome_id in self.metadata['genome_id']:
            # Try both naming patterns
            feature_file = self.feature_dir / f"{genome_id}_features.npz"
            cds_feature_file = self.feature_dir / f"{genome_id}_cds_features.npz"

            if feature_file.exists():
                self.genome_ids.append(genome_id)
            elif cds_feature_file.exists():
                self.genome_ids.append(genome_id)

        logger.info(f"Loaded metadata for {len(self.metadata)} genomes")
        logger.info(f"Found feature files for {len(self.genome_ids)} genomes")

    def __len__(self) -> int:
        """Get the number of samples in the dataset."""
        return len(self.genome_ids)

    def __getitem__(self, idx: int) -> Tuple[Dict[str, torch.Tensor], Dict[str, torch.Tensor]]:
        """
        Get a sample from the dataset.

        Args:
            idx: Index of the sample

        Returns:
            Tuple of (features, targets)
        """
        # Get genome ID
        genome_id = self.genome_ids[idx]

        # Load features
        feature_file = self.feature_dir / f"{genome_id}_features.npz"
        cds_feature_file = self.feature_dir / f"{genome_id}_cds_features.npz"

        if feature_file.exists():
            features_dict = dict(np.load(feature_file))
        elif cds_feature_file.exists():
            features_dict = dict(np.load(cds_feature_file))
        else:
            raise FileNotFoundError(f"Feature file not found for genome {genome_id}")

        # Convert features to tensors
        features = {}
        for key, value in features_dict.items():
            if isinstance(value, (int, float, np.number)):
                features[key] = torch.tensor(float(value))
            elif isinstance(value, np.ndarray):
                features[key] = torch.tensor(value)

        # Apply feature transform if provided
        if self.feature_transform is not None:
            features = self.feature_transform(features)

        # Get targets
        targets = {}
        metadata_row = self.metadata[self.metadata['genome_id'] == genome_id].iloc[0]

        for column in self.target_columns:
            if column in metadata_row and not pd.isna(metadata_row[column]):
                targets[column] = torch.tensor(float(metadata_row[column]))

        # Apply target transform if provided
        if self.target_transform is not None:
            targets = self.target_transform(targets)

        return features, targets

    def get_feature_names(self) -> List[str]:
        """
        Get the names of all features in the dataset.

        Returns:
            List of feature names
        """
        # Load features for the first genome to get feature names
        if len(self.genome_ids) == 0:
            return []

        feature_file = self.feature_dir / f"{self.genome_ids[0]}_features.npz"
        cds_feature_file = self.feature_dir / f"{self.genome_ids[0]}_cds_features.npz"

        if feature_file.exists():
            features_dict = dict(np.load(feature_file))
        elif cds_feature_file.exists():
            features_dict = dict(np.load(cds_feature_file))
        else:
            return []

        return list(features_dict.keys())

    def get_metadata(self) -> pd.DataFrame:
        """
        Get the metadata for the dataset.

        Returns:
            Metadata DataFrame
        """
        return self.metadata[self.metadata['genome_id'].isin(self.genome_ids)]
