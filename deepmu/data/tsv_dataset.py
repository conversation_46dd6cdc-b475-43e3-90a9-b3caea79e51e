"""
用于加载 DeepMu TSV 特征文件的数据集模块。

此模块提供用于从组合 TSV 文件加载预计算特征的数据集类，
用于微生物生长速率和最适温度预测。
"""

import torch
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Callable
from torch.utils.data import Dataset
import logging

# 设置日志
logger = logging.getLogger(__name__)

class DeepMuTsvDataset(Dataset):
    """
    用于从组合 TSV 文件加载 DeepMu 预计算特征的数据集。

    此数据集从组合 TSV 文件加载预计算特征，从 TSV 文件加载元数据，
    为训练模型提供便捷的接口。
    """

    def __init__(
        self,
        metadata_file: str,
        feature_file: str,
        target_columns: List[str] = ['growth_rate', 'optimal_temperature'],
        feature_transform: Optional[Callable] = None,
        target_transform: Optional[Callable] = None
    ):
        """
        初始化 DeepMu TSV 数据集。

        参数:
            metadata_file: 元数据 TSV 文件路径
            feature_file: 组合特征 TSV 文件路径
            target_columns: 元数据中目标列名称列表
            feature_transform: 应用于特征的可选变换
            target_transform: 应用于目标的可选变换
        """
        self.metadata_file = metadata_file
        self.feature_file = feature_file
        self.target_columns = target_columns
        self.feature_transform = feature_transform
        self.target_transform = target_transform

        # 加载元数据
        self.metadata = pd.read_csv(metadata_file, sep='\t')
        
        # Load features
        self.features = pd.read_csv(feature_file, sep='\t')
        
        # Merge metadata and features on genome_id
        self.data = pd.merge(self.metadata, self.features, on='genome_id', how='inner')
        
        # Get list of genome IDs that have both metadata and features
        self.genome_ids = self.data['genome_id'].tolist()
        
        # Get feature names (all columns except genome_id and target columns)
        self.feature_names = [col for col in self.features.columns if col != 'genome_id']

        logger.info(f"Loaded metadata for {len(self.metadata)} genomes")
        logger.info(f"Found feature files for {len(self.genome_ids)} genomes")
        logger.info(f"Using {len(self.feature_names)} features")

    def __len__(self) -> int:
        """Get the number of samples in the dataset."""
        return len(self.genome_ids)

    def __getitem__(self, idx: int) -> Tuple[Dict[str, torch.Tensor], Dict[str, torch.Tensor]]:
        """
        Get a sample from the dataset.

        Args:
            idx: Index of the sample

        Returns:
            Tuple of (features, targets)
        """
        # Get genome ID
        genome_id = self.genome_ids[idx]
        
        # Get row for this genome
        row = self.data[self.data['genome_id'] == genome_id].iloc[0]
        
        # Extract features
        features = {}
        for feature_name in self.feature_names:
            value = row[feature_name]
            # Convert to tensor and handle NaN values
            if pd.isna(value):
                features[feature_name] = torch.tensor(0.0)
            else:
                try:
                    features[feature_name] = torch.tensor(float(value))
                except (ValueError, TypeError):
                    # If conversion to float fails, use 0.0
                    features[feature_name] = torch.tensor(0.0)

        # Apply feature transform if provided
        if self.feature_transform is not None:
            features = self.feature_transform(features)

        # Get targets
        targets = {}
        for column in self.target_columns:
            if column in row and not pd.isna(row[column]):
                targets[column] = torch.tensor(float(row[column]))

        # Apply target transform if provided
        if self.target_transform is not None:
            targets = self.target_transform(targets)

        return features, targets

    def get_feature_names(self) -> List[str]:
        """
        Get the names of all features in the dataset.

        Returns:
            List of feature names
        """
        return self.feature_names

    def get_metadata(self) -> pd.DataFrame:
        """
        Get the metadata for the dataset.

        Returns:
            Metadata DataFrame
        """
        return self.metadata[self.metadata['genome_id'].isin(self.genome_ids)]
