"""
DeepMu: 增强型微生物生长速率和温度预测器

这个包提供了使用多分支神经网络方法预测微生物生长速率和最适生长温度的工具，
该方法整合了密码子使用模式、系统发育信息和具有位置感知注意力机制的序列特征。

主要组件：
- 具有位置感知分析的密码子特征计算
- 具有注意力机制的多分支神经架构
- 基于丰度加权的群落水平预测
- 生长速率和温度的多任务学习
- 用于增强系统发育建模的分层分类学和KO谱相似性
- 通过Prodigal和KofamScan集成基因预测和KO注释

核心功能：
1. 单个微生物预测 - 预测单个基因组的生长参数
2. 群落预测 - 基于宏基因组数据的群落水平预测
3. 特征提取 - 从基因组数据提取多维特征
4. 预处理 - 基因预测和功能注释
5. 多任务学习 - 同时预测生长速率和最适温度

基本使用方法：
```python
from deepmu import MicrobialGrowthPredictor

# 初始化预测器
predictor = MicrobialGrowthPredictor()

# 单个微生物预测
result = predictor.predict_single("genome.fna", temperature=37.0)
print(f"生长速率: {result['growth_rate']}")
print(f"最适温度: {result['optimal_temperature']}")

# 群落预测
community_result = predictor.predict_community(
    "metagenome.fna",
    coverage_path="coverage.tsv",
    temperature=37.0
)

# 直接处理基因组（自动基因预测和KO注释）
genome_result = predictor.predict_single(
    "genome.fna",
    temperature=37.0,
    is_genome=True
)
```

模块结构：
- deepmu.features: 特征提取和计算模块
- deepmu.models: 神经网络模型定义
- deepmu.predictors: 预测器接口
- deepmu.utils: 工具函数和预处理
- deepmu.data: 数据处理和加载
- deepmu.cli: 命令行接口
- deepmu.taxonomy: 分类学相关功能

版本: 1.1.1
作者: DeepMu 开发团队
许可证: MIT License
"""

import os
import sys

# Add module directory to path for relative imports
module_dir = os.path.dirname(os.path.abspath(__file__))
if module_dir not in sys.path:
    sys.path.append(module_dir)

# 仅导入特征相关类以避免循环导入
from .features import CodonFeatureCalculator
from .features import PathwayFeatureCalculator, PathwayDatabase

# 按需导入其他类以避免循环导入
def _import_predictor():
    """
    按需导入预测器类

    Returns:
        MicrobialGrowthPredictor: 微生物生长预测器类
    """
    from .predictors import MicrobialGrowthPredictor
    return MicrobialGrowthPredictor

def _import_models():
    """
    按需导入模型类

    Returns:
        tuple: 包含各种模型类的元组
    """
    from .models import EnhancedPhyloGrowthModel, EnhancedCommunityGrowthModel
    from .models.phylo import EnhancedPhyloBranch, TaxonomicEmbedder, KOSimilarityCalculator, HierarchicalGNN
    return EnhancedPhyloGrowthModel, EnhancedCommunityGrowthModel, EnhancedPhyloBranch, TaxonomicEmbedder, KOSimilarityCalculator, HierarchicalGNN

def _import_data():
    """
    按需导入数据处理类

    Returns:
        tuple: 包含数据集类的元组
    """
    from .data import GrowthRateDataset, CommunityGrowthDataset
    return GrowthRateDataset, CommunityGrowthDataset

def _import_utils():
    """
    按需导入工具类

    Returns:
        tuple: 包含工具类的元组
    """
    from .utils.gene_annotation import GenomePreprocessor
    from .taxonomy.taxonomy_utils import TaxonomyUtils
    return GenomePreprocessor, TaxonomyUtils

# 便捷导入函数
def get_predictor(*args, **kwargs):
    """
    获取预测器实例的便捷函数

    Args:
        *args: 传递给预测器构造函数的位置参数
        **kwargs: 传递给预测器构造函数的关键字参数

    Returns:
        MicrobialGrowthPredictor: 预测器实例
    """
    MicrobialGrowthPredictor = _import_predictor()
    return MicrobialGrowthPredictor(*args, **kwargs)

# 设置版本号
__version__ = '1.1.1'

# 导出的主要类和函数
__all__ = [
    'CodonFeatureCalculator',           # 密码子特征计算器
    'PathwayFeatureCalculator',         # 代谢途径特征计算器
    'PathwayDatabase',                  # 代谢途径数据库
    '_import_predictor',                # 预测器导入函数
    '_import_models',                   # 模型导入函数
    '_import_data',                     # 数据导入函数
    '_import_utils',                    # 工具导入函数
    'get_predictor',                    # 获取预测器便捷函数
    '__version__',                      # 版本号
]

# 包的元数据
__author__ = 'DeepMu 开发团队'
__email__ = '<EMAIL>'
__license__ = 'MIT'
__description__ = '基于深度学习的微生物生长速率和温度预测工具'
