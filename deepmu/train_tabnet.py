#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import argparse
import yaml
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, r2_score

from deepmu.models.tabnet_model import TabNetRegressor
from deepmu.utils.logging import get_logger

def parse_args():
    parser = argparse.ArgumentParser(description='Train TabNet model')
    
    # Config file
    parser.add_argument('--config', type=str, default=None, help='Path to config file')
    
    # Data parameters
    parser.add_argument('--data_path', type=str, default='data/', help='Path to data directory')
    parser.add_argument('--model_path', type=str, default='models/tabnet/', help='Path to save model')
    parser.add_argument('--log_path', type=str, default='logs/', help='Path to save logs')
    
    # Training parameters
    parser.add_argument('--batch_size', type=int, default=1024, help='Batch size')
    parser.add_argument('--n_epochs', type=int, default=100, help='Number of epochs')
    parser.add_argument('--learning_rate', type=float, default=0.02, help='Learning rate')
    parser.add_argument('--weight_decay', type=float, default=1e-5, help='Weight decay')
    parser.add_argument('--patience', type=int, default=10, help='Patience for early stopping')
    
    # TabNet parameters
    parser.add_argument('--n_steps', type=int, default=3, help='Number of steps in the feature transformer')
    parser.add_argument('--n_d', type=int, default=64, help='Dimension of feature transform')
    parser.add_argument('--n_a', type=int, default=64, help='Dimension of attentive transform')
    parser.add_argument('--gamma', type=float, default=1.3, help='Scale for feature reusage')
    parser.add_argument('--n_shared', type=int, default=2, help='Number of shared decision steps')
    parser.add_argument('--n_independent', type=int, default=2, help='Number of independent decision steps')
    parser.add_argument('--virtual_batch_size', type=int, default=128, help='Virtual batch size for batch norm')
    parser.add_argument('--mask_type', type=str, default='sparsemax', choices=['sparsemax', 'entmax'], 
                        help='Type of mask to use: sparsemax or entmax')
    parser.add_argument('--lambda_sparse', type=float, default=1e-4, help='Sparsity regularization weight')
    
    # Other parameters
    parser.add_argument('--seed', type=int, default=42, help='Random seed')
    
    args = parser.parse_args()
    
    # Load config file if provided
    if args.config is not None:
        with open(args.config, 'r') as f:
            config = yaml.safe_load(f)
        
        # Update args with config values, ensuring proper type conversion
        for key, value in config.items():
            if hasattr(args, key):
                # Get the expected type from argparse
                arg_type = type(getattr(args, key))
                try:
                    # Convert the value to the expected type
                    if arg_type == float:
                        value = float(value)
                    elif arg_type == int:
                        value = int(value)
                    elif arg_type == bool:
                        value = bool(value)
                    # No conversion needed for strings
                    setattr(args, key, value)
                except (ValueError, TypeError):
                    print(f"Warning: Could not convert {key}={value} to {arg_type}, using default.")
            else:
                print(f"Warning: Unknown config parameter: {key}")
    
    return args

def set_seed(seed):
    """Set random seed for reproducibility."""
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
        torch.backends.cudnn.deterministic = True

def prepare_data(data_path):
    """Load and prepare data for training."""
    # Load data
    train_df = pd.read_csv(os.path.join(data_path, 'train.csv'))
    
    # Split features and target
    X = train_df.drop('target', axis=1).values
    y = train_df['target'].values.reshape(-1, 1)
    
    # Split data into train and validation
    X_train, X_val, y_train, y_val = train_test_split(X, y, test_size=0.2, random_state=42)
    
    # Standardize features
    scaler_X = StandardScaler()
    X_train = scaler_X.fit_transform(X_train)
    X_val = scaler_X.transform(X_val)
    
    # Convert to tensors
    X_train_tensor = torch.FloatTensor(X_train)
    y_train_tensor = torch.FloatTensor(y_train)
    X_val_tensor = torch.FloatTensor(X_val)
    y_val_tensor = torch.FloatTensor(y_val)
    
    # Create datasets and dataloaders
    train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
    val_dataset = TensorDataset(X_val_tensor, y_val_tensor)
    
    return train_dataset, val_dataset, X_train.shape[1]

def train_and_evaluate(args, train_dataset, val_dataset, input_dim):
    """Train and evaluate TabNet model."""
    logger = get_logger('TabNetTrainer')
    
    # Create dataloaders - reduce batch size for small dataset
    batch_size = min(args.batch_size, len(train_dataset) // 2)  # Ensure at least 2 batches
    logger.info(f"Using batch size: {batch_size}")
    
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size)
    
    # Set device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"Using device: {device}")
    
    # Log dimensions for debugging
    logger.info(f"Input dimension: {input_dim}")
    
    # For small datasets, use even smaller model dimensions
    n_d = min(args.n_d, input_dim * 2)  # Scale based on input dim
    n_a = min(args.n_a, input_dim * 2)  # Scale based on input dim
    virtual_batch_size = min(args.virtual_batch_size, batch_size)
    
    logger.info(f"Using reduced dimensions - n_d={n_d}, n_a={n_a}, virtual_batch_size={virtual_batch_size}")
    
    # Create model with smaller dimensions
    model = TabNetRegressor(
        input_dim=input_dim,
        output_dim=1,
        n_d=n_d,
        n_a=n_a,
        n_steps=args.n_steps,
        gamma=args.gamma,
        n_independent=args.n_independent,
        n_shared=args.n_shared,
        virtual_batch_size=virtual_batch_size,
        mask_type=args.mask_type,
    ).to(device)
    
    # Loss function and optimizer
    criterion = nn.MSELoss()
    optimizer = optim.Adam(model.parameters(), lr=args.learning_rate, weight_decay=args.weight_decay)
    
    # Early stopping
    best_val_loss = float('inf')
    early_stop_counter = 0
    
    # Training loop
    for epoch in range(args.n_epochs):
        # Training
        model.train()
        train_loss = 0.0
        
        for X_batch, y_batch in train_loader:
            X_batch, y_batch = X_batch.to(device), y_batch.to(device)
            
            # Log the shapes for the first batch in the first epoch
            if epoch == 0 and train_loss == 0.0:
                logger.info(f"Batch shapes - X: {X_batch.shape}, y: {y_batch.shape}")
            
            optimizer.zero_grad()
            y_pred, M_loss = model(X_batch)
            
            loss = criterion(y_pred, y_batch) + args.lambda_sparse * M_loss
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item() * X_batch.size(0)
        
        train_loss /= len(train_loader.dataset)
        
        # Validation
        model.eval()
        val_loss = 0.0
        val_preds = []
        val_targets = []
        
        with torch.no_grad():
            for X_batch, y_batch in val_loader:
                X_batch, y_batch = X_batch.to(device), y_batch.to(device)
                
                y_pred, _ = model(X_batch)
                val_loss += criterion(y_pred, y_batch).item() * X_batch.size(0)
                
                val_preds.append(y_pred.cpu().numpy())
                val_targets.append(y_batch.cpu().numpy())
        
        val_loss /= len(val_loader.dataset)
        
        # Calculate metrics
        val_preds = np.vstack(val_preds)
        val_targets = np.vstack(val_targets)
        val_rmse = np.sqrt(mean_squared_error(val_targets, val_preds))
        val_r2 = r2_score(val_targets, val_preds)
        
        logger.info(f"Epoch {epoch+1}/{args.n_epochs} - Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}, "
                    f"Val RMSE: {val_rmse:.4f}, Val R²: {val_r2:.4f}")
        
        # Early stopping
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            early_stop_counter = 0
            
            # Save model
            os.makedirs(args.model_path, exist_ok=True)
            torch.save(model.state_dict(), os.path.join(args.model_path, 'best_tabnet_model.pt'))
            logger.info(f"Model saved at epoch {epoch+1}")
        else:
            early_stop_counter += 1
            if early_stop_counter >= args.patience:
                logger.info(f"Early stopping at epoch {epoch+1}")
                break
    
    # Load best model
    model.load_state_dict(torch.load(os.path.join(args.model_path, 'best_tabnet_model.pt')))
    
    return model

def main():
    """Main function."""
    args = parse_args()
    
    # Set up logging
    os.makedirs(args.log_path, exist_ok=True)
    logger = get_logger('TabNetTrainer')
    
    # Set random seed
    set_seed(args.seed)
    
    # Prepare data
    logger.info("Preparing data...")
    train_dataset, val_dataset, input_dim = prepare_data(args.data_path)
    
    # Train and evaluate model
    logger.info("Training model...")
    model = train_and_evaluate(args, train_dataset, val_dataset, input_dim)
    
    logger.info("Training completed!")

if __name__ == "__main__":
    main() 