#!/usr/bin/env python3
import json
import glob
import os

# 尝试导入可选依赖
try:
    import numpy as np
    HAS_NUMPY = True
except ImportError:
    HAS_NUMPY = False
    print("警告: numpy未安装，将使用基本统计功能")

try:
    import matplotlib.pyplot as plt
    HAS_MATPLOTLIB = True
except ImportError:
    HAS_MATPLOTLIB = False

def analyze_results():
    """分析多任务预测结果"""

    # 查找所有结果文件（在多个位置）
    search_patterns = [
        "*.json",                    # 当前目录
        "results/*.json",            # results目录
        "../results/*.json",         # 上级results目录
        "multi_task_examples/*.json" # 示例目录
    ]

    result_files = []
    for pattern in search_patterns:
        files = glob.glob(pattern)
        result_files.extend(files)

    # 去重
    result_files = list(set(result_files))

    if not result_files:
        print("未找到结果文件")
        print("搜索路径:")
        for pattern in search_patterns:
            print(f"  - {pattern}")
        print("\n请先运行预测生成结果文件:")
        print("  python deepmu_cli.py predict features.tsv --output results.json")
        return
    
    print(f"找到 {len(result_files)} 个结果文件")
    print("="*50)
    
    growth_rates = []
    temperatures = []
    
    for file in result_files:
        try:
            with open(file, 'r') as f:
                result = json.load(f)

            print(f"\n文件: {file}")

            # 显示基本信息
            if 'growth_rate' in result:
                growth_rate = result['growth_rate']
                print(f"  生长速率: {growth_rate:.4f} h⁻¹")
                growth_rates.append(growth_rate)
            else:
                print(f"  生长速率: N/A")

            # 处理温度预测（可能为null）
            if 'optimal_temperature' in result:
                temp = result['optimal_temperature']
                if temp is not None:
                    print(f"  最适温度: {temp:.2f} °C")
                    temperatures.append(temp)
                else:
                    print(f"  最适温度: 不可用 (遗留模型)")

            # 显示模型信息
            if 'model_type' in result:
                print(f"  模型类型: {result['model_type']}")

            # 显示多任务信息
            if 'multi_task_requested' in result:
                print(f"  多任务模式: {result['multi_task_requested']}")

            if 'temperature_prediction_available' in result:
                print(f"  温度预测可用: {result['temperature_prediction_available']}")

            # 显示特征信息
            if 'features_shape' in result:
                print(f"  特征形状: {result['features_shape']}")

        except Exception as e:
            print(f"读取文件 {file} 时出错: {e}")
    
    # 统计分析
    print(f"\n" + "="*50)
    print("统计分析:")
    print("="*50)

    if growth_rates:
        print(f"生长速率统计 ({len(growth_rates)} 个样本):")
        print(f"  范围: {min(growth_rates):.4f} - {max(growth_rates):.4f} h⁻¹")

        # 计算平均值
        mean_growth = sum(growth_rates) / len(growth_rates)
        print(f"  平均值: {mean_growth:.4f} h⁻¹")

        # 计算中位数
        sorted_growth = sorted(growth_rates)
        n = len(sorted_growth)
        if n % 2 == 0:
            median_growth = (sorted_growth[n//2-1] + sorted_growth[n//2]) / 2
        else:
            median_growth = sorted_growth[n//2]
        print(f"  中位数: {median_growth:.4f} h⁻¹")

        # 计算标准差（如果有numpy）
        if HAS_NUMPY:
            print(f"  标准差: {np.std(growth_rates):.4f} h⁻¹")
        else:
            # 简单的标准差计算
            variance = sum((x - mean_growth) ** 2 for x in growth_rates) / len(growth_rates)
            std_growth = variance ** 0.5
            print(f"  标准差: {std_growth:.4f} h⁻¹")
    else:
        print("未找到有效的生长速率数据")

    if temperatures:
        print(f"\n温度预测统计 ({len(temperatures)} 个样本):")
        print(f"  范围: {min(temperatures):.2f} - {max(temperatures):.2f} °C")

        # 计算平均值
        mean_temp = sum(temperatures) / len(temperatures)
        print(f"  平均值: {mean_temp:.2f} °C")

        # 计算中位数
        sorted_temp = sorted(temperatures)
        n = len(sorted_temp)
        if n % 2 == 0:
            median_temp = (sorted_temp[n//2-1] + sorted_temp[n//2]) / 2
        else:
            median_temp = sorted_temp[n//2]
        print(f"  中位数: {median_temp:.2f} °C")

        # 计算标准差（如果有numpy）
        if HAS_NUMPY:
            print(f"  标准差: {np.std(temperatures):.2f} °C")
        else:
            # 简单的标准差计算
            variance = sum((x - mean_temp) ** 2 for x in temperatures) / len(temperatures)
            std_temp = variance ** 0.5
            print(f"  标准差: {std_temp:.2f} °C")
    else:
        print("\n温度预测统计:")
        print("  无可用的温度预测数据 (可能使用了遗留模型)")

    # 模型类型统计
    print(f"\n处理的文件总数: {len(result_files)}")
    print(f"有效生长速率预测: {len(growth_rates)}")
    print(f"有效温度预测: {len(temperatures)}")

    # 生成简单的可视化（如果有matplotlib）
    if HAS_MATPLOTLIB:
        try:
            if growth_rates and len(growth_rates) > 1:
                plt.figure(figsize=(12, 4))

                plt.subplot(1, 2, 1)
                plt.hist(growth_rates, bins=min(20, len(growth_rates)), alpha=0.7, color='blue')
                plt.xlabel('生长速率 (h⁻¹)')
                plt.ylabel('频次')
                plt.title('生长速率分布')
                plt.grid(True, alpha=0.3)

                if temperatures and len(temperatures) > 1:
                    plt.subplot(1, 2, 2)
                    plt.hist(temperatures, bins=min(20, len(temperatures)), alpha=0.7, color='red')
                    plt.xlabel('最适温度 (°C)')
                    plt.ylabel('频次')
                    plt.title('最适温度分布')
                    plt.grid(True, alpha=0.3)

                plt.tight_layout()
                plt.savefig('prediction_analysis.png', dpi=300, bbox_inches='tight')
                print(f"\n📊 可视化图表已保存为: prediction_analysis.png")

        except Exception as e:
            print(f"\n📊 生成可视化时出错: {e}")
    else:
        print(f"\n📊 matplotlib未安装，跳过可视化")
        print(f"  温度平均值: {np.mean(temperatures):.2f} °C")
        print(f"  温度标准差: {np.std(temperatures):.2f} °C")

if __name__ == "__main__":
    analyze_results()
