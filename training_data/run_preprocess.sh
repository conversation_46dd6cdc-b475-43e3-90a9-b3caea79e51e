#!/bin/bash

Metadata="metadata.tsv"
KofamScan_DB="../DeepMu-v1.1.1/db/KofamScan"
CDS_KO_DIR="cds_ko_files"


i=0
for f in `ls genomes/*.fna`; do
    filename=`basename $f`
    genomeID="${filename%.fna}"
    echo $f
    code=`grep ${genomeID} ${Metadata} | cut -d$'\t' -f 5 `
    kingdom=`grep ${genomeID} ${Metadata} | cut -d$'\t' -f 6 `
    ko_file=${CDS_KO_DIR}/${genomeID}_ko.tsv

    # preprocess
    if [ ! -f ${ko_file} ]; then
        echo "Preprocessing $f with genetic code $code"
        DeepMu preprocess $f --genetic-code $code --kingdom ${kingdom} --output-dir ${CDS_KO_DIR} --cpu 10 --kofamscan-db ${KofamScan_DB} --identify-trnas --identify-rrnas --skip-existing --log-level DEBUG
    else
        echo "$genomeID has been done!"
        continue
    fi

    ((i++))
done
