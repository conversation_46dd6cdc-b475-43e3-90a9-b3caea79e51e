#!/bin/bash

# source folders
SRC_CDS_KO_DIR="/home/<USER>/GitHub/DeepMu/training_data/cds_ko_files"
SRC_GENOME_DIR="/home/<USER>/GitHub/DeepMu/training_data/genomes"

# target folders
CDS_FFN="cds_ffn"
CDS_FAA="cds_faa"
GENOMES="genomes"
RNA_FILES="rna_files"
KO_FILES="kegg/ko_files"
mkdir -p ${CDS_FFN} ${CDS_FAA} ${GENOMES} ${RNA_FILES} ${KO_FILES}

# copy files
META_DATA="metadata.tsv"
while IFS= read -r line; do
    #echo "Line: $line"
    GENOME_ID=$(echo "$line" | cut -d$'\t' -f 1)
    echo "Copying files of the genome: ${GENOME_ID} ..."

    genome_file=${SRC_GENOME_DIR}/${GENOME_ID}.fna
    cds_ffn_file=${SRC_CDS_KO_DIR}/${GENOME_ID}_cds.ffn    
    cds_faa_file=${SRC_CDS_KO_DIR}/${GENOME_ID}_cds.faa
    ko_file=${SRC_CDS_KO_DIR}/${GENOME_ID}_ko.tsv
    rrna_fa=${SRC_CDS_KO_DIR}/${GENOME_ID}_rrna.fasta
    rrna_gff=${SRC_CDS_KO_DIR}/${GENOME_ID}_rrna.gff
    trna_fa=${SRC_CDS_KO_DIR}/${GENOME_ID}_tRNA.fasta
    trna_tsv=${SRC_CDS_KO_DIR}/${GENOME_ID}_tRNA.tsv

    cp ${genome_file} ${GENOMES}
    cp ${cds_ffn_file} ${CDS_FFN}
    cp ${cds_faa_file} ${CDS_FAA}
    cp ${ko_file} ${KO_FILES}
    cp ${rrna_fa} ${rrna_gff} ${trna_fa} ${trna_tsv} ${RNA_FILES}  


done < "$META_DATA"

