import torch
import torch.nn as nn
import torch.nn.functional as F

class CNNDNNTemperatureModel(nn.Module):
    """CNN+DNN model for temperature prediction.
    
    Architecture:
    1. 1D CNN layers to extract feature patterns
    2. Dense DNN layers for prediction
    """
    
    def __init__(self, input_dim, hidden_dims=(256, 128, 64), cnn_filters=(64, 32, 16), 
                 kernel_sizes=(3, 5, 7), dropout_rate=0.2):
        """Initialize the CNN+DNN temperature model.
        
        Args:
            input_dim: Dimension of input features
            hidden_dims: Tuple of hidden dimensions for DNN layers
            cnn_filters: Tuple of CNN filter numbers
            kernel_sizes: Tuple of kernel sizes for CNN layers
            dropout_rate: Dropout rate for regularization
        """
        super(CNNDNNTemperatureModel, self).__init__()
        
        # Reshape the input for CNN (batch_size, 1, input_dim)
        self.input_dim = input_dim
        
        # CNN layers
        self.cnn_layers = nn.ModuleList()
        
        # Create 1D CNN layers
        in_channels = 1
        conv_output_dim = input_dim
        
        for i, (filters, kernel_size) in enumerate(zip(cnn_filters, kernel_sizes)):
            padding = kernel_size // 2  # Same padding
            self.cnn_layers.append(
                nn.Conv1d(in_channels, filters, kernel_size, padding=padding)
            )
            in_channels = filters
            # Update the feature dimension after convolution
            # With "same" padding, the dimension remains the same
        
        # Flatten CNN output
        cnn_output_dim = conv_output_dim * cnn_filters[-1]
        
        # DNN layers
        self.dnn_layers = nn.ModuleList()
        
        # First DNN layer after CNN
        self.dnn_layers.append(nn.Linear(cnn_output_dim, hidden_dims[0]))
        
        # Hidden DNN layers
        for i in range(len(hidden_dims) - 1):
            self.dnn_layers.append(nn.Linear(hidden_dims[i], hidden_dims[i+1]))
        
        # Output layer
        self.output_layer = nn.Linear(hidden_dims[-1], 1)
        
        # Dropout for regularization
        self.dropout = nn.Dropout(dropout_rate)
        
        # Batch normalization layers
        self.bn_layers = nn.ModuleList()
        for dim in hidden_dims:
            self.bn_layers.append(nn.BatchNorm1d(dim))
            
    def forward(self, x):
        """Forward pass through the model.
        
        Args:
            x: Input tensor of shape (batch_size, input_dim)
            
        Returns:
            Output tensor of shape (batch_size, 1)
        """
        batch_size = x.size(0)
        
        # Reshape for CNN (batch_size, channels, features)
        x = x.view(batch_size, 1, self.input_dim)
        
        # Forward through CNN layers
        for i, cnn_layer in enumerate(self.cnn_layers):
            x = F.relu(cnn_layer(x))
        
        # Flatten CNN output
        x = x.view(batch_size, -1)
        
        # Forward through DNN layers
        for i, (dnn_layer, bn_layer) in enumerate(zip(self.dnn_layers, self.bn_layers)):
            x = dnn_layer(x)
            x = bn_layer(x)
            x = F.relu(x)
            x = self.dropout(x)
        
        # Output layer
        x = self.output_layer(x)
        
        return x 