#!/bin/bash

# Train the DeepMu hybrid model with CNN+DNN for temperature prediction
# Uses the same DNN for growth rate as the final model

echo "=========================================================="
echo "   Training DeepMu hybrid model with CNN+DNN for temperature   "
echo "   - DNN for growth rate (log2 transform)                 "
echo "   - CNN+DNN for temperature (no transform)               "
echo "=========================================================="

# Set output directory
OUTPUT_DIR="models/deepmu_cnn_dnn_temp_model"

# Run the model training script
python cnn_dnn_hybrid_model.py \
    --feature_file="./training_data/combined_features.tsv" \
    --metadata_file="./training_data/metadata.tsv" \
    --output_dir=$OUTPUT_DIR \
    --n_growth_features=250 \
    --n_temp_features=500 \
    --growth_transform=log2 \
    --temp_transform=none \
    --batch_size=64 \
    --epochs=300 \
    --patience=30 \
    --growth_hidden_dims=128,64,32 \
    --temp_hidden_dims=256,128,64 \
    --cnn_filters=64,32,16 \
    --kernel_sizes=3,5,7

echo -e "\n========================================================"
echo "   Training completed successfully!                       "
echo "=========================================================="
echo -e "\nModel saved to: $OUTPUT_DIR"
echo -e "\nThis model uses:"
echo -e "  - DNN for growth rate prediction (with log2 transformation)"
echo -e "  - CNN+DNN for temperature prediction (with NO transformation)"
echo -e "\nDone." 