import os
import logging
import json
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from sklearn.ensemble import RandomForestRegressor
import joblib
import argparse
from typing import Dict, List, Tuple, Optional, Union
import warnings

# Import the CNN+DNN model for temperature prediction
from cnn_dnn_temp_model import CNNDNNTemperatureModel

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

class GrowthRateModel(nn.Module):
    """Deep Neural Network model for growth rate prediction."""
    
    def __init__(self, input_dim: int, hidden_dims: Tuple[int, ...] = (128, 64, 32)):
        """Initialize the growth rate model.
        
        Args:
            input_dim: Dimension of input features
            hidden_dims: Tuple of hidden dimensions
        """
        super(GrowthRateModel, self).__init__()
        
        # DNN layers
        self.fc_layers = nn.ModuleList()
        
        # First layer
        self.fc_layers.append(nn.Linear(input_dim, hidden_dims[0]))
        
        # Hidden layers
        for i in range(len(hidden_dims) - 1):
            self.fc_layers.append(nn.Linear(hidden_dims[i], hidden_dims[i+1]))
        
        # Output layer
        self.output_layer = nn.Linear(hidden_dims[-1], 1)
        
        # Dropout for regularization
        self.dropout = nn.Dropout(0.2)
        
        # Batch normalization
        self.bn_layers = nn.ModuleList()
        for dim in hidden_dims:
            self.bn_layers.append(nn.BatchNorm1d(dim))
            
    def forward(self, x):
        """Forward pass through the model.
        
        Args:
            x: Input tensor of shape (batch_size, input_dim)
            
        Returns:
            Output tensor of shape (batch_size, 1)
        """
        # Forward through hidden layers
        for i, (fc_layer, bn_layer) in enumerate(zip(self.fc_layers, self.bn_layers)):
            x = fc_layer(x)
            x = bn_layer(x)
            x = torch.relu(x)
            x = self.dropout(x)
        
        # Output layer
        x = self.output_layer(x)
        
        return x

class HybridModel:
    """Hybrid model combining DNN for growth rate and CNN+DNN for temperature prediction."""
    
    def __init__(self, logger=None):
        """Initialize the hybrid model.
        
        Args:
            logger: Logger instance
        """
        self.growth_model = None
        self.temp_model = None
        self.logger = logger or logging.getLogger(__name__)
        
        # For feature preprocessing
        self.growth_scaler = None
        self.temp_scaler = None
        self.growth_feature_indices = None
        self.temp_feature_indices = None
        
        # For data transformation
        self.growth_transform = None
        self.temp_transform = None
        
        # For output directory
        self.output_dir = None
        
    def _initialize_models(self, growth_input_dim: int, temp_input_dim: int, 
                           growth_hidden_dims: Tuple[int, ...] = (128, 64, 32),
                           temp_hidden_dims: Tuple[int, ...] = (256, 128, 64),
                           cnn_filters: Tuple[int, ...] = (64, 32, 16),
                           kernel_sizes: Tuple[int, ...] = (3, 5, 7),
                           device: str = 'cpu'):
        """Initialize the growth rate and temperature models.
        
        Args:
            growth_input_dim: Dimension of growth rate input features
            temp_input_dim: Dimension of temperature input features
            growth_hidden_dims: Tuple of hidden dimensions for growth rate model
            temp_hidden_dims: Tuple of hidden dimensions for temperature model
            cnn_filters: Tuple of CNN filter numbers for temperature model
            kernel_sizes: Tuple of kernel sizes for CNN layers
            device: Device to run the models on
        """
        # Initialize growth rate model
        self.growth_model = GrowthRateModel(growth_input_dim, growth_hidden_dims).to(device)
        
        # Initialize temperature model
        self.temp_model = CNNDNNTemperatureModel(
            temp_input_dim, 
            hidden_dims=temp_hidden_dims,
            cnn_filters=cnn_filters,
            kernel_sizes=kernel_sizes
        ).to(device)
        
    def fit(self, features: np.ndarray, growth_rates: np.ndarray, temperatures: np.ndarray,
            test_size: float = 0.2, val_size: float = 0.2, n_growth_features: int = 250,
            n_temp_features: int = 500, batch_size: int = 32, epochs: int = 300,
            growth_transform: str = 'log2', temp_transform: str = 'none',
            device: str = 'cpu', early_stopping_patience: int = 20,
            growth_hidden_dims: Tuple[int, ...] = (128, 64, 32),
            temp_hidden_dims: Tuple[int, ...] = (256, 128, 64),
            cnn_filters: Tuple[int, ...] = (64, 32, 16),
            kernel_sizes: Tuple[int, ...] = (3, 5, 7),
            output_dir: str = None):
        """Fit the hybrid model on the given data.
        
        Args:
            features: Feature matrix
            growth_rates: Growth rate array
            temperatures: Temperature array
            test_size: Fraction of data to use for testing
            val_size: Fraction of training data to use for validation
            n_growth_features: Number of features to use for growth rate prediction
            n_temp_features: Number of features to use for temperature prediction
            batch_size: Batch size for training
            epochs: Number of epochs for training
            growth_transform: Transformation to apply to growth rates ('none', 'log', 'log2', 'log10')
            temp_transform: Transformation to apply to temperatures ('none', 'log', 'log2', 'log10')
            device: Device to run the models on
            early_stopping_patience: Number of epochs to wait for improvement before stopping
            growth_hidden_dims: Tuple of hidden dimensions for growth rate model
            temp_hidden_dims: Tuple of hidden dimensions for temperature model
            cnn_filters: Tuple of CNN filter numbers for temperature model
            kernel_sizes: Tuple of kernel sizes for CNN layers
            output_dir: Directory to save model and results
        """
        self.growth_transform = growth_transform
        self.temp_transform = temp_transform
        self.output_dir = output_dir
        
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)
        
        # Split data into train, validation, and test sets
        X_train_val, X_test, y_growth_train_val, y_growth_test, y_temp_train_val, y_temp_test = train_test_split(
            features, growth_rates, temperatures, test_size=test_size, random_state=42
        )
        
        X_train, X_val, y_growth_train, y_growth_val, y_temp_train, y_temp_val = train_test_split(
            X_train_val, y_growth_train_val, y_temp_train_val, test_size=val_size, random_state=42
        )
        
        # Apply transformations to target variables
        y_growth_train_transformed = self._transform(y_growth_train, growth_transform)
        y_growth_val_transformed = self._transform(y_growth_val, growth_transform)
        y_growth_test_transformed = self._transform(y_growth_test, growth_transform)
        
        y_temp_train_transformed = self._transform(y_temp_train, temp_transform)
        y_temp_val_transformed = self._transform(y_temp_val, temp_transform)
        y_temp_test_transformed = self._transform(y_temp_test, temp_transform)
        
        # Select top features for growth rate and temperature prediction
        self.growth_feature_indices = self._select_features(X_train, y_growth_train_transformed, n_growth_features)
        self.temp_feature_indices = self._select_features(X_train, y_temp_train_transformed, n_temp_features)
        
        X_train_growth = X_train[:, self.growth_feature_indices]
        X_val_growth = X_val[:, self.growth_feature_indices]
        X_test_growth = X_test[:, self.growth_feature_indices]
        
        X_train_temp = X_train[:, self.temp_feature_indices]
        X_val_temp = X_val[:, self.temp_feature_indices]
        X_test_temp = X_test[:, self.temp_feature_indices]
        
        # Standardize features
        self.growth_scaler = StandardScaler().fit(X_train_growth)
        self.temp_scaler = StandardScaler().fit(X_train_temp)
        
        X_train_growth_scaled = self.growth_scaler.transform(X_train_growth)
        X_val_growth_scaled = self.growth_scaler.transform(X_val_growth)
        X_test_growth_scaled = self.growth_scaler.transform(X_test_growth)
        
        X_train_temp_scaled = self.temp_scaler.transform(X_train_temp)
        X_val_temp_scaled = self.temp_scaler.transform(X_val_temp)
        X_test_temp_scaled = self.temp_scaler.transform(X_test_temp)
        
        # Create datasets
        train_growth_dataset = TensorDataset(
            torch.FloatTensor(X_train_growth_scaled),
            torch.FloatTensor(y_growth_train_transformed.reshape(-1, 1) if not hasattr(y_growth_train_transformed, 'iloc') else y_growth_train_transformed.values.reshape(-1, 1))
        )
        
        val_growth_dataset = TensorDataset(
            torch.FloatTensor(X_val_growth_scaled),
            torch.FloatTensor(y_growth_val_transformed.reshape(-1, 1) if not hasattr(y_growth_val_transformed, 'iloc') else y_growth_val_transformed.values.reshape(-1, 1))
        )
        
        train_temp_dataset = TensorDataset(
            torch.FloatTensor(X_train_temp_scaled),
            torch.FloatTensor(y_temp_train_transformed.reshape(-1, 1) if not hasattr(y_temp_train_transformed, 'iloc') else y_temp_train_transformed.values.reshape(-1, 1))
        )
        
        val_temp_dataset = TensorDataset(
            torch.FloatTensor(X_val_temp_scaled),
            torch.FloatTensor(y_temp_val_transformed.reshape(-1, 1) if not hasattr(y_temp_val_transformed, 'iloc') else y_temp_val_transformed.values.reshape(-1, 1))
        )
        
        # Create data loaders
        train_growth_loader = DataLoader(train_growth_dataset, batch_size=batch_size, shuffle=True)
        val_growth_loader = DataLoader(val_growth_dataset, batch_size=batch_size)
        
        train_temp_loader = DataLoader(train_temp_dataset, batch_size=batch_size, shuffle=True)
        val_temp_loader = DataLoader(val_temp_dataset, batch_size=batch_size)
        
        # Initialize models
        self._initialize_models(
            growth_input_dim=X_train_growth_scaled.shape[1],
            temp_input_dim=X_train_temp_scaled.shape[1],
            growth_hidden_dims=growth_hidden_dims,
            temp_hidden_dims=temp_hidden_dims,
            cnn_filters=cnn_filters,
            kernel_sizes=kernel_sizes,
            device=device
        )
        
        # Set up optimizers and schedulers
        growth_optimizer = optim.Adam(self.growth_model.parameters(), lr=0.001, weight_decay=1e-4)
        temp_optimizer = optim.Adam(self.temp_model.parameters(), lr=0.001, weight_decay=1e-4)
        
        # Use MSE loss
        criterion = nn.MSELoss()
        
        # Training variables
        best_growth_val_loss = float('inf')
        best_temp_val_loss = float('inf')
        best_growth_model_state = None
        best_temp_model_state = None
        growth_patience_counter = 0
        temp_patience_counter = 0
        
        # Train the model
        for epoch in range(epochs):
            # Training mode
            self.growth_model.train()
            self.temp_model.train()
            
            train_growth_loss = 0
            train_temp_loss = 0
            
            # Train growth rate model
            for X_batch, y_batch in train_growth_loader:
                X_batch = X_batch.to(device)
                y_batch = y_batch.to(device)
                
                # Zero the gradients
                growth_optimizer.zero_grad()
                
                # Forward pass
                outputs = self.growth_model(X_batch)
                
                # Calculate loss
                loss = criterion(outputs, y_batch)
                
                # Backward pass and optimize
                loss.backward()
                # Apply gradient clipping
                torch.nn.utils.clip_grad_norm_(self.growth_model.parameters(), max_norm=1.0)
                growth_optimizer.step()
                
                train_growth_loss += loss.item()
            
            # Average training loss
            train_growth_loss /= len(train_growth_loader)
            
            # Train temperature model
            for X_batch, y_batch in train_temp_loader:
                X_batch = X_batch.to(device)
                y_batch = y_batch.to(device)
                
                # Zero the gradients
                temp_optimizer.zero_grad()
                
                # Forward pass
                outputs = self.temp_model(X_batch)
                
                # Calculate loss
                loss = criterion(outputs, y_batch)
                
                # Backward pass and optimize
                loss.backward()
                # Apply gradient clipping
                torch.nn.utils.clip_grad_norm_(self.temp_model.parameters(), max_norm=1.0)
                temp_optimizer.step()
                
                train_temp_loss += loss.item()
            
            # Average training loss
            train_temp_loss /= len(train_temp_loader)
            
            # Validation
            self.growth_model.eval()
            self.temp_model.eval()
            
            val_growth_loss = 0
            val_temp_loss = 0
            
            growth_preds = []
            temp_preds = []
            growth_targets = []
            temp_targets = []
            
            with torch.no_grad():
                # Validate growth rate model
                for X_batch, y_batch in val_growth_loader:
                    X_batch = X_batch.to(device)
                    y_batch = y_batch.to(device)
                    
                    # Forward pass
                    outputs = self.growth_model(X_batch)
                    
                    # Calculate loss
                    loss = criterion(outputs, y_batch)
                    val_growth_loss += loss.item()
                    
                    # Store predictions and targets for metrics
                    growth_preds.append(outputs.cpu().numpy())
                    growth_targets.append(y_batch.cpu().numpy())
                
                # Validate temperature model
                for X_batch, y_batch in val_temp_loader:
                    X_batch = X_batch.to(device)
                    y_batch = y_batch.to(device)
                    
                    # Forward pass
                    outputs = self.temp_model(X_batch)
                    
                    # Calculate loss
                    loss = criterion(outputs, y_batch)
                    val_temp_loss += loss.item()
                    
                    # Store predictions and targets for metrics
                    temp_preds.append(outputs.cpu().numpy())
                    temp_targets.append(y_batch.cpu().numpy())
            
            # Average validation loss
            val_growth_loss /= len(val_growth_loader)
            val_temp_loss /= len(val_temp_loader)
            
            # Concatenate predictions and targets
            growth_preds = np.concatenate(growth_preds).flatten()
            growth_targets = np.concatenate(growth_targets).flatten()
            temp_preds = np.concatenate(temp_preds).flatten()
            temp_targets = np.concatenate(temp_targets).flatten()
            
            # Calculate metrics
            growth_r2 = r2_score(growth_targets, growth_preds)
            growth_rmse = np.sqrt(mean_squared_error(growth_targets, growth_preds))
            growth_mae = mean_absolute_error(growth_targets, growth_preds)
            
            temp_r2 = r2_score(temp_targets, temp_preds)
            temp_rmse = np.sqrt(mean_squared_error(temp_targets, temp_preds))
            temp_mae = mean_absolute_error(temp_targets, temp_preds)
            
            # Check for early stopping
            if val_growth_loss < best_growth_val_loss:
                best_growth_val_loss = val_growth_loss
                best_growth_model_state = self.growth_model.state_dict().copy()
                growth_patience_counter = 0
            else:
                growth_patience_counter += 1
            
            if val_temp_loss < best_temp_val_loss:
                best_temp_val_loss = val_temp_loss
                best_temp_model_state = self.temp_model.state_dict().copy()
                temp_patience_counter = 0
            else:
                temp_patience_counter += 1
            
            # Log progress every 10 epochs
            if (epoch + 1) % 10 == 0:
                self.logger.info("="*60)
                self.logger.info(f"Epoch {epoch + 1}/{epochs}")
                
                self.logger.info("Growth Rate Model:")
                self.logger.info(f"Train - Loss: {train_growth_loss:.4f}")
                self.logger.info(f"Val   - Loss: {val_growth_loss:.4f}, R2: {growth_r2:.6f}, RMSE: {growth_rmse:.4f}, MAE: {growth_mae:.4f}")
                
                self.logger.info("Temperature Model (CNN+DNN):")
                self.logger.info(f"Train - Loss: {train_temp_loss:.4f}")
                self.logger.info(f"Val   - Loss: {val_temp_loss:.4f}, R2: {temp_r2:.6f}, RMSE: {temp_rmse:.4f}, MAE: {temp_mae:.4f}")
            
            # Early stopping
            if growth_patience_counter >= early_stopping_patience and temp_patience_counter >= early_stopping_patience:
                self.logger.info(f"Early stopping triggered at epoch {epoch + 1}")
                break
        
        # Load the best models
        if best_growth_model_state is not None:
            self.growth_model.load_state_dict(best_growth_model_state)
        
        if best_temp_model_state is not None:
            self.temp_model.load_state_dict(best_temp_model_state)
        
        # Final evaluation on test set
        test_metrics = self.evaluate(
            X_test, 
            y_growth_test_transformed, 
            y_temp_test_transformed
        )
        
        self.logger.info("\n============================================================")
        self.logger.info("Test Set Evaluation:")
        self.logger.info(f"Growth Rate - R²: {test_metrics['growth_r2']:.4f}, RMSE: {test_metrics['growth_rmse']:.4f}, MAE: {test_metrics['growth_mae']:.4f}")
        self.logger.info(f"Temperature - R²: {test_metrics['temp_r2']:.4f}, RMSE: {test_metrics['temp_rmse']:.4f}, MAE: {test_metrics['temp_mae']:.4f}")
        
        # Save test metrics to file if output directory is provided
        if output_dir:
            metrics_file = os.path.join(output_dir, 'test_metrics.csv')
            pd.DataFrame([test_metrics]).to_csv(metrics_file, index=False)
            self.logger.info(f"Saved test metrics to {metrics_file}")
        
        # Save the model
        if output_dir:
            self.save(output_dir)
            self.logger.info(f"Model saved to {output_dir}")
    
    def evaluate(self, X, y_growth, y_temp):
        """Evaluate the model on given data.
        
        Args:
            X: Feature matrix
            y_growth: Growth rate array
            y_temp: Temperature array
            
        Returns:
            Dictionary of evaluation metrics
        """
        # Convert to numpy if needed
        X_np = X.values if hasattr(X, 'values') else X
        
        # Apply feature selection
        X_growth = X_np[:, self.growth_feature_indices]
        X_temp = X_np[:, self.temp_feature_indices]
        
        # Standardize features
        X_growth_scaled = self.growth_scaler.transform(X_growth)
        X_temp_scaled = self.temp_scaler.transform(X_temp)
        
        # Convert to tensors
        X_growth_tensor = torch.FloatTensor(X_growth_scaled)
        X_temp_tensor = torch.FloatTensor(X_temp_scaled)
        
        # Make predictions
        self.growth_model.eval()
        self.temp_model.eval()
        
        with torch.no_grad():
            growth_preds = self.growth_model(X_growth_tensor).numpy().flatten()
            temp_preds = self.temp_model(X_temp_tensor).numpy().flatten()
        
        # Calculate metrics
        growth_r2 = r2_score(y_growth, growth_preds)
        growth_rmse = np.sqrt(mean_squared_error(y_growth, growth_preds))
        growth_mae = mean_absolute_error(y_growth, growth_preds)
        
        temp_r2 = r2_score(y_temp, temp_preds)
        temp_rmse = np.sqrt(mean_squared_error(y_temp, temp_preds))
        temp_mae = mean_absolute_error(y_temp, temp_preds)
        
        # Apply inverse transformations for reporting
        y_growth_orig = self._inverse_transform(y_growth, self.growth_transform)
        growth_preds_orig = self._inverse_transform(growth_preds, self.growth_transform)
        
        y_temp_orig = self._inverse_transform(y_temp, self.temp_transform)
        temp_preds_orig = self._inverse_transform(temp_preds, self.temp_transform)
        
        # Calculate metrics in original scale
        growth_r2_orig = r2_score(y_growth_orig, growth_preds_orig)
        growth_rmse_orig = np.sqrt(mean_squared_error(y_growth_orig, growth_preds_orig))
        growth_mae_orig = mean_absolute_error(y_growth_orig, growth_preds_orig)
        
        temp_r2_orig = r2_score(y_temp_orig, temp_preds_orig)
        temp_rmse_orig = np.sqrt(mean_squared_error(y_temp_orig, temp_preds_orig))
        temp_mae_orig = mean_absolute_error(y_temp_orig, temp_preds_orig)
        
        # Return metrics
        return {
            'growth_r2': growth_r2,
            'growth_rmse': growth_rmse,
            'growth_mae': growth_mae,
            'temp_r2': temp_r2,
            'temp_rmse': temp_rmse,
            'temp_mae': temp_mae,
            'growth_r2_orig': growth_r2_orig,
            'growth_rmse_orig': growth_rmse_orig,
            'growth_mae_orig': growth_mae_orig,
            'temp_r2_orig': temp_r2_orig,
            'temp_rmse_orig': temp_rmse_orig,
            'temp_mae_orig': temp_mae_orig
        }
    
    def predict(self, X):
        """Make predictions.
        
        Args:
            X: Input features
            
        Returns:
            Tuple of (predicted growth rates, predicted temperatures)
        """
        # Convert to numpy if needed
        X_np = X.values if hasattr(X, 'values') else X
        
        # Apply feature selection
        X_growth = X_np[:, self.growth_feature_indices]
        X_temp = X_np[:, self.temp_feature_indices]
        
        # Standardize features
        X_growth_scaled = self.growth_scaler.transform(X_growth)
        X_temp_scaled = self.temp_scaler.transform(X_temp)
        
        # Convert to tensors
        X_growth_tensor = torch.FloatTensor(X_growth_scaled)
        X_temp_tensor = torch.FloatTensor(X_temp_scaled)
        
        # Make predictions
        self.growth_model.eval()
        self.temp_model.eval()
        
        with torch.no_grad():
            growth_preds = self.growth_model(X_growth_tensor).numpy().flatten()
            temp_preds = self.temp_model(X_temp_tensor).numpy().flatten()
        
        # Inverse transform predictions
        growth_preds_orig = self._inverse_transform(growth_preds, self.growth_transform)
        temp_preds_orig = self._inverse_transform(temp_preds, self.temp_transform)
        
        return growth_preds_orig, temp_preds_orig
    
    def save(self, output_dir):
        """Save the model.
        
        Args:
            output_dir: Directory to save the model
        """
        os.makedirs(output_dir, exist_ok=True)
        
        # Save model weights
        torch.save(self.growth_model.state_dict(), os.path.join(output_dir, 'growth_model.pt'))
        torch.save(self.temp_model.state_dict(), os.path.join(output_dir, 'temp_model.pt'))
        
        # Save scalers
        joblib.dump(self.growth_scaler, os.path.join(output_dir, 'growth_scaler.pkl'))
        joblib.dump(self.temp_scaler, os.path.join(output_dir, 'temp_scaler.pkl'))
        
        # Save feature indices
        joblib.dump(self.growth_feature_indices, os.path.join(output_dir, 'growth_feature_indices.pkl'))
        joblib.dump(self.temp_feature_indices, os.path.join(output_dir, 'temp_feature_indices.pkl'))
        
        # Save transformations
        with open(os.path.join(output_dir, 'transforms.json'), 'w') as f:
            json.dump({
                'growth_transform': self.growth_transform,
                'temp_transform': self.temp_transform
            }, f)
        
        # Save model architecture parameters
        model_params = {
            'growth_input_dim': self.growth_model.fc_layers[0].in_features,
            'growth_hidden_dims': [layer.out_features for layer in self.growth_model.fc_layers],
            'temp_input_dim': self.temp_model.input_dim,
            'temp_hidden_dims': [layer.out_features for layer in self.temp_model.dnn_layers],
            'cnn_filters': [layer.out_channels for layer in self.temp_model.cnn_layers],
            'kernel_sizes': [layer.kernel_size[0] for layer in self.temp_model.cnn_layers]
        }
        
        with open(os.path.join(output_dir, 'model_params.json'), 'w') as f:
            json.dump(model_params, f)
    
    @classmethod
    def load(cls, model_dir):
        """Load the model from directory.
        
        Args:
            model_dir: Directory containing saved model
            
        Returns:
            Loaded model
        """
        # Initialize model
        model = cls()
        
        # Load model parameters
        with open(os.path.join(model_dir, 'model_params.json'), 'r') as f:
            model_params = json.load(f)
        
        # Load transformations
        with open(os.path.join(model_dir, 'transforms.json'), 'r') as f:
            transforms = json.load(f)
        
        model.growth_transform = transforms['growth_transform']
        model.temp_transform = transforms['temp_transform']
        
        # Load feature indices
        model.growth_feature_indices = joblib.load(os.path.join(model_dir, 'growth_feature_indices.pkl'))
        model.temp_feature_indices = joblib.load(os.path.join(model_dir, 'temp_feature_indices.pkl'))
        
        # Load scalers
        model.growth_scaler = joblib.load(os.path.join(model_dir, 'growth_scaler.pkl'))
        model.temp_scaler = joblib.load(os.path.join(model_dir, 'temp_scaler.pkl'))
        
        # Initialize models
        model._initialize_models(
            growth_input_dim=model_params['growth_input_dim'],
            temp_input_dim=model_params['temp_input_dim'],
            growth_hidden_dims=tuple(model_params['growth_hidden_dims']),
            temp_hidden_dims=tuple(model_params['temp_hidden_dims']),
            cnn_filters=tuple(model_params['cnn_filters']),
            kernel_sizes=tuple(model_params['kernel_sizes'])
        )
        
        # Load model weights
        model.growth_model.load_state_dict(torch.load(os.path.join(model_dir, 'growth_model.pt'), map_location='cpu'))
        model.temp_model.load_state_dict(torch.load(os.path.join(model_dir, 'temp_model.pt'), map_location='cpu'))
        
        # Set models to eval mode
        model.growth_model.eval()
        model.temp_model.eval()
        
        model.output_dir = model_dir
        
        return model
    
    def _select_features(self, X, y, n_features):
        """Select top features based on correlation with target.
        
        Args:
            X: Feature matrix
            y: Target array
            n_features: Number of features to select
            
        Returns:
            Indices of selected features
        """
        # Calculate correlation between each feature and the target
        correlations = []
        for i in range(X.shape[1]):
            corr = np.abs(np.corrcoef(X[:, i], y)[0, 1])
            if np.isnan(corr):
                corr = 0
            correlations.append((i, corr))
        
        # Sort by correlation and select top n_features
        correlations.sort(key=lambda x: x[1], reverse=True)
        selected_indices = [idx for idx, _ in correlations[:n_features]]
        
        return selected_indices
    
    def _transform(self, y, transform_type):
        """Apply transformation to the target variable.
        
        Args:
            y: Target array
            transform_type: Type of transformation ('none', 'log', 'log2', 'log10')
            
        Returns:
            Transformed array
        """
        if transform_type == 'none':
            return y
        elif transform_type == 'log':
            return np.log(y)
        elif transform_type == 'log2':
            return np.log2(y)
        elif transform_type == 'log10':
            return np.log10(y)
        else:
            raise ValueError(f"Unknown transformation: {transform_type}")
    
    def _inverse_transform(self, y, transform_type):
        """Apply inverse transformation to the target variable.
        
        Args:
            y: Transformed array
            transform_type: Type of transformation ('none', 'log', 'log2', 'log10')
            
        Returns:
            Original array
        """
        if transform_type == 'none':
            return y
        elif transform_type == 'log':
            return np.exp(y)
        elif transform_type == 'log2':
            return np.power(2, y)
        elif transform_type == 'log10':
            return np.power(10, y)
        else:
            raise ValueError(f"Unknown transformation: {transform_type}")

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Train CNN+DNN hybrid model')
    
    # Data arguments
    parser.add_argument('--feature_file', type=str, required=True, help='Path to feature file')
    parser.add_argument('--metadata_file', type=str, required=True, help='Path to metadata file')
    parser.add_argument('--output_dir', type=str, required=True, help='Directory to save model and results')
    
    # Feature selection arguments
    parser.add_argument('--n_growth_features', type=int, default=250, help='Number of features for growth rate prediction')
    parser.add_argument('--n_temp_features', type=int, default=500, help='Number of features for temperature prediction')
    
    # Training arguments
    parser.add_argument('--batch_size', type=int, default=32, help='Batch size for training')
    parser.add_argument('--epochs', type=int, default=300, help='Number of epochs for training')
    parser.add_argument('--patience', type=int, default=20, help='Patience for early stopping')
    
    # Transformation arguments
    parser.add_argument('--growth_transform', type=str, default='log2', choices=['none', 'log', 'log2', 'log10'], help='Transformation for growth rates')
    parser.add_argument('--temp_transform', type=str, default='none', choices=['none', 'log', 'log2', 'log10'], help='Transformation for temperatures')
    
    # Model architecture arguments
    parser.add_argument('--growth_hidden_dims', type=str, default='128,64,32', help='Hidden dimensions for growth model (comma-separated)')
    parser.add_argument('--temp_hidden_dims', type=str, default='256,128,64', help='Hidden dimensions for temperature model (comma-separated)')
    parser.add_argument('--cnn_filters', type=str, default='64,32,16', help='CNN filters for temperature model (comma-separated)')
    parser.add_argument('--kernel_sizes', type=str, default='3,5,7', help='Kernel sizes for CNN layers (comma-separated)')
    
    return parser.parse_args()

def main():
    """Main function."""
    args = parse_args()
    
    # Set up logging
    logger = logging.getLogger(__name__)
    
    # Parse comma-separated arguments
    growth_hidden_dims = tuple(map(int, args.growth_hidden_dims.split(',')))
    temp_hidden_dims = tuple(map(int, args.temp_hidden_dims.split(',')))
    cnn_filters = tuple(map(int, args.cnn_filters.split(',')))
    kernel_sizes = tuple(map(int, args.kernel_sizes.split(',')))
    
    # Load data
    features = pd.read_csv(args.feature_file, sep='\t', index_col=0)
    metadata = pd.read_csv(args.metadata_file, sep='\t', index_col=0)
    
    # Align indices
    common_indices = features.index.intersection(metadata.index)
    features = features.loc[common_indices]
    metadata = metadata.loc[common_indices]
    
    # Initialize model
    model = HybridModel(logger=logger)
    
    # Determine device
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    logger.info(f"Using device: {device}")
    
    # Train model
    model.fit(
        features.values, 
        metadata['growth_rate'].values, 
        metadata['optimal_temperature'].values,
        n_growth_features=args.n_growth_features,
        n_temp_features=args.n_temp_features,
        batch_size=args.batch_size,
        epochs=args.epochs,
        growth_transform=args.growth_transform,
        temp_transform=args.temp_transform,
        device=device,
        early_stopping_patience=args.patience,
        growth_hidden_dims=growth_hidden_dims,
        temp_hidden_dims=temp_hidden_dims,
        cnn_filters=cnn_filters,
        kernel_sizes=kernel_sizes,
        output_dir=args.output_dir
    )

if __name__ == '__main__':
    main() 