#!/bin/bash

# DeepMu Final Training Script
# Uses the hybrid approach with:
# - DNN for growth rate prediction (with log2 transformation)
# - Random Forest for temperature prediction (with NO transformation)

echo "=========================================================="
echo "   DeepMu Final Model Training                            "
echo "   Hybrid Model:                                          "
echo "   - DNN for growth rate (log2 transform)                 "
echo "   - Random Forest for temperature (no transform)         "
echo "=========================================================="

# Set output directory
OUTPUT_DIR="models/deepmu_final_model"

# Run the model training script
python rf_temp_hybrid_model.py \
    --feature_file="./training_data/combined_features.tsv" \
    --metadata_file="./training_data/metadata.tsv" \
    --output_dir=$OUTPUT_DIR \
    --n_growth_features=250 \
    --n_temp_features=500 \
    --use_rf_temp_model \
    --rf_n_estimators=300 \
    --rf_max_depth=20 \
    --rf_min_samples_leaf=2 \
    --temp_transform=none \
    --growth_transform=log2 \
    --batch_size=128 \
    --epochs=300 \
    --patience=20

echo -e "\n========================================================"
echo "   Training completed successfully!                       "
echo "=========================================================="
echo -e "\nFinal DeepMu model saved to: $OUTPUT_DIR"
echo -e "\nThis model uses:"
echo -e "  - DNN for growth rate prediction (with log2 transformation)"
echo -e "  - Random Forest for temperature prediction (with NO transformation)"
echo -e "\nDone." 