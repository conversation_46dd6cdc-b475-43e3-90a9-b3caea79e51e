#!/bin/bash

# DeepMu: Training Script with Improved Temperature Model
# This script uses the existing final_hybrid_model.py but with improved parameters
# for temperature prediction.

# Set a clean output directory
OUTPUT_DIR="models/deepmu_improved_temp_model"

# Create output directory
mkdir -p "$OUTPUT_DIR"

# Define color codes for output formatting
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Display initial message
echo -e "${BLUE}${BOLD}========================================================${NC}"
echo -e "${BLUE}${BOLD}   DeepMu - Improved Temperature Model   ${NC}"
echo -e "${BLUE}${BOLD}========================================================${NC}"
echo -e "${GREEN}This script uses improved parameters for temperature prediction:${NC}"
echo -e "  1. ${YELLOW}Deeper neural network architecture${NC}"
echo -e "  2. ${YELLOW}Residual connections and attention mechanisms${NC}"
echo -e "  3. ${YELLOW}Leaky ReLU activation function${NC}"
echo -e "  4. ${YELLOW}More temperature-specific features${NC}"
echo -e ""
echo -e "${YELLOW}Output will be saved to:${NC} ${OUTPUT_DIR}"
echo ""

# Check if prerequisites are available
echo -e "${YELLOW}Checking prerequisites...${NC}"
if [ ! -f "./training_data/combined_features.tsv" ]; then
    echo -e "${RED}Error: Feature file not found. Please ensure './training_data/combined_features.tsv' exists.${NC}"
    exit 1
fi

if [ ! -f "./training_data/metadata.tsv" ]; then
    echo -e "${RED}Error: Metadata file not found. Please ensure './training_data/metadata.tsv' exists.${NC}"
    exit 1
fi

echo -e "${GREEN}All prerequisites found.${NC}"
echo ""

# Step 1: Filter the dataset to remove unreliable temperature data
echo -e "${YELLOW}Step 1: Filtering dataset to remove unreliable temperature data...${NC}"
python improved_temp_data_filter.py \
    --feature-file "./training_data/combined_features.tsv" \
    --metadata-file "./training_data/metadata.tsv" \
    --output-dir "$OUTPUT_DIR/filtered_data" \
    --reliability-threshold 0.6 \
    --prediction-error-threshold 2.0

# Check if filtering was successful
if [ $? -eq 0 ]; then
    echo -e "${GREEN}Dataset filtering completed. Using filtered datasets for training.${NC}"
    FEATURES_FILE="${OUTPUT_DIR}/filtered_data/filtered_features.tsv"
    METADATA_FILE="${OUTPUT_DIR}/filtered_data/filtered_metadata.tsv"
else
    echo -e "${RED}Error: Dataset filtering failed. Exiting.${NC}"
    exit 1
fi

# Set parameters for training
echo -e "${YELLOW}Setting parameters for training...${NC}"

# Parameters for enhanced temperature model
HIDDEN_DIMS="768,768,512,512,384,256,128"  # Deeper architecture
DROPOUT_RATES="0.2,0.25,0.3,0.35,0.4,0.45,0.5"  # Progressive dropout
MAX_EPOCHS=800  # More epochs for complete training
PATIENCE=80  # Increased patience for more stable convergence
BATCH_SIZE=32  # Smaller batch size for better generalization
N_GROWTH_FEATURES=150  # Fewer features for growth rate
N_TEMP_FEATURES=1000  # More features for temperature
L1_LAMBDA="1e-6"  # Reduced L1 regularization
WEIGHT_DECAY="1e-6"  # Reduced weight decay
LEARNING_RATE="0.0005"  # Lower learning rate for better convergence

# Train with fixed transformations: none for temperature, log2 for growth rate
echo -e "\n${BLUE}${BOLD}========================================================${NC}"
echo -e "${BLUE}${BOLD}   Training Final Model with Improved Temperature Model   ${NC}"
echo -e "${BLUE}${BOLD}   Temperature: none, Growth Rate: log2   ${NC}"
echo -e "${BLUE}${BOLD}========================================================${NC}"

# Set environment variables for deterministic behavior
export PYTHONHASHSEED=42
export TORCH_CUDNN_DETERMINISTIC=1
export TORCH_DETERMINISTIC=1
export CUBLAS_WORKSPACE_CONFIG=:4096:8

# Run the Python script with optimized parameters
python final_hybrid_model.py \
    --feature_file "$FEATURES_FILE" \
    --metadata_file "$METADATA_FILE" \
    --output_dir "$OUTPUT_DIR" \
    --n_growth_features $N_GROWTH_FEATURES \
    --n_temp_features $N_TEMP_FEATURES \
    --seed 42 \
    --lr $LEARNING_RATE \
    --batch_size $BATCH_SIZE \
    --patience $PATIENCE \
    --epochs $MAX_EPOCHS \
    --l1_lambda $L1_LAMBDA \
    --weight_decay $WEIGHT_DECAY \
    --mse_l1_ratio 0.95 \
    --hidden_dims "$HIDDEN_DIMS" \
    --dropout_rates "$DROPOUT_RATES" \
    --activation "leaky_relu" \
    --use_batch_norm \
    --use_residual \
    --use_attention \
    --use_one_cycle_lr \
    --save_checkpoints \
    --missing_threshold 0.5 \
    --temp_transform "none" \
    --growth_transform "log2" \
    --verbose 2>&1 | tee "${OUTPUT_DIR}/training.log"

# Check if training was successful
if [ $? -eq 0 ]; then
    echo ""
    echo -e "${GREEN}${BOLD}========================================================${NC}"
    echo -e "${GREEN}${BOLD}   Training completed successfully!   ${NC}"
    echo -e "${GREEN}${BOLD}========================================================${NC}"
    echo ""
    echo -e "${GREEN}Model saved to:${NC} ${OUTPUT_DIR}"
    
else
    echo ""
    echo -e "${RED}${BOLD}========================================================${NC}"
    echo -e "${RED}${BOLD}   Training failed!   ${NC}"
    echo -e "${RED}${BOLD}========================================================${NC}"
    echo ""
    echo -e "${RED}Please check the error messages above for details.${NC}"
fi

echo ""
echo -e "${GREEN}Done.${NC}"
