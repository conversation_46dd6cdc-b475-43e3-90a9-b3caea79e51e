#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
DeepMu Final Hybrid Model.

This module implements a high-performance hybrid model that combines:
1. A dedicated ensemble model for growth rate prediction (R² > 0.93)
2. A specialized DNN for optimal temperature prediction (R² > 0.94)
3. Task-specific feature selection and preprocessing
4. Advanced optimization techniques

The model architecture maintains complete separation between prediction components
while providing a unified interface for training and inference.
"""

import os
import argparse
import logging
import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import RobustScaler, StandardScaler, KBinsDiscretizer
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
import joblib
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import TensorDataset, DataLoader, random_split
from typing import Dict, List, Tuple, Optional, Any
from scipy import stats
from scipy.optimize import minimize
import copy
from collections import defaultdict
from sklearn.feature_selection import mutual_info_regression, f_regression, RFECV, RFE
from sklearn.linear_model import LassoCV, Ridge
from sklearn.ensemble import RandomForestRegressor
from sklearn.inspection import permutation_importance
import time

# Import dedicated component models
from growth_model import GrowthRateModel
from temperature_model import TemperatureModel
from predict_temperature import TemperaturePredictionModel
from enhanced_temperature_dnn import EnhancedTemperatureDNN

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Set device
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

#-----------------------------------------------------------------------------
# Advanced Optimization Utilities
#-----------------------------------------------------------------------------

class EarlyStopping:
    """
    Early stopping implementation to prevent overfitting.
    """
    def __init__(self, patience=10, min_delta=0.0, restore_best_weights=True):
        self.patience = patience
        self.min_delta = min_delta
        self.restore_best_weights = restore_best_weights
        self.best_score = None
        self.counter = 0
        self.best_weights = None

    def __call__(self, val_loss, model):
        if self.best_score is None:
            self.best_score = val_loss
            if self.restore_best_weights:
                self.best_weights = model.state_dict().copy()
            return False

        if val_loss > self.best_score + self.min_delta:
            self.counter += 1
            if self.counter >= self.patience:
                return True
        else:
            self.best_score = val_loss
            if self.restore_best_weights:
                self.best_weights = model.state_dict().copy()
            self.counter = 0

        return False

    def restore_weights(self, model):
        if self.restore_best_weights and self.best_weights is not None:
            model.load_state_dict(self.best_weights)

def get_optimized_temperature_model_params() -> Dict:
    """
    Get optimized parameters for the temperature model.

    Returns:
        Dictionary of optimized parameters
    """
    return {
        'hidden_dims': [512, 384, 256, 128],
        'dropout_rates': [0.3, 0.4, 0.4, 0.5],
        'lr': 0.001,
        'batch_size': 128,
        'epochs': 300,
        'patience': 30,
        'use_batch_norm': True,
        'activation': 'relu',
        'weight_decay': 1e-6,
        'mse_l1_ratio': 0.9,
        'use_gradient_clipping': True,
        'max_grad_norm': 1.0,
        # Transformer-specific parameters
        'transformer_nhead': 8,
        'transformer_nlayers': 2,
        'transformer_dim_feedforward': 512,
        'transformer_dropout': 0.3
    }

def add_l1_regularization(model, loss, l1_lambda):
    """
    Add L1 regularization to the loss.

    Args:
        model: PyTorch model
        loss: Current loss
        l1_lambda: L1 regularization coefficient

    Returns:
        Updated loss with L1 regularization
    """
    l1_reg = 0.0
    for param in model.parameters():
        l1_reg += torch.norm(param, 1)

    return loss + l1_lambda * l1_reg

def apply_gradient_clipping(model, max_grad_norm):
    """
    Apply gradient clipping to prevent exploding gradients.

    Args:
        model: PyTorch model
        max_grad_norm: Maximum gradient norm
    """
    torch.nn.utils.clip_grad_norm_(model.parameters(), max_grad_norm)

def setup_one_cycle_lr(optimizer, epochs, steps_per_epoch, max_lr, warmup_pct):
    """
    Set up one cycle learning rate scheduler.

    Args:
        optimizer: PyTorch optimizer
        epochs: Number of epochs
        steps_per_epoch: Steps per epoch
        max_lr: Maximum learning rate
        warmup_pct: Percentage of training to use for warmup

    Returns:
        One cycle learning rate scheduler
    """
    total_steps = epochs * steps_per_epoch
    return optim.lr_scheduler.OneCycleLR(
        optimizer,
        max_lr=max_lr,
        total_steps=total_steps,
        pct_start=warmup_pct,
        anneal_strategy='cos',
        div_factor=25.0,
        final_div_factor=10000.0
    )

#-----------------------------------------------------------------------------
# Data Preparation Functions
#-----------------------------------------------------------------------------

def filter_high_missing_features(features: pd.DataFrame, threshold: float = 0.5) -> pd.DataFrame:
    """
    Filter out features with missing values exceeding the threshold percentage.

    Args:
        features: Feature DataFrame
        threshold: Maximum allowable percentage of missing values (0.0-1.0)

    Returns:
        DataFrame with high-missing-value features removed
    """
    # Calculate percentage of missing values for each feature
    missing_pct = features.isna().mean()

    # Identify features to keep (missing values below threshold)
    features_to_keep = missing_pct[missing_pct <= threshold].index.tolist()

    # Log the filtering results
    initial_feature_count = features.shape[1]
    filtered_feature_count = len(features_to_keep)
    removed_count = initial_feature_count - filtered_feature_count

    if removed_count > 0:
        logger.info(f"Removed {removed_count} features with >{threshold*100:.1f}% missing values.")
        logger.info(f"Retained {filtered_feature_count} features out of {initial_feature_count}.")

        # Return the filtered features
        return features[features_to_keep]
    else:
        logger.info(f"No features exceeded the missing value threshold of {threshold*100:.1f}%.")
        return features

def load_data(feature_file: str, metadata_file: str, missing_threshold: float = 0.5) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    Load feature and metadata files.

    Args:
        feature_file: Path to feature file
        metadata_file: Path to metadata file
        missing_threshold: Maximum allowable percentage of missing values in features

    Returns:
        Tuple of (features, metadata)
    """
    logger.info(f"Loading features from {feature_file}")
    features = pd.read_csv(feature_file, sep='\t', index_col='genome_id')

    logger.info(f"Loading metadata from {metadata_file}")
    metadata = pd.read_csv(metadata_file, sep='\t', index_col='genome_id')

    # Get common samples
    common_indices = features.index.intersection(metadata.index)
    logger.info(f"Found {len(common_indices)} common samples")

    # Filter to common samples
    features = features.loc[common_indices]
    metadata = metadata.loc[common_indices]

    # Filter out features with high missing values
    features = filter_high_missing_features(features, threshold=missing_threshold)

    # Check for and handle remaining NaN values in features
    nan_count = features.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} remaining NaN values in features. Filling with 0.")
        features = features.fillna(0)

    # Check for and handle infinite values
    inf_count = np.isinf(features.values).sum()
    if inf_count > 0:
        logger.warning(f"Found {inf_count} infinite values in features. Replacing with max/min values.")
        features = features.replace([np.inf, -np.inf], np.nan)
        features = features.fillna(features.max())

    # Check for NaN values in metadata
    nan_count = metadata.isna().sum().sum()
    if nan_count > 0:
        logger.warning(f"Found {nan_count} NaN values in metadata. Filling with 0.")
        metadata = metadata.fillna(0)

    return features, metadata

#-----------------------------------------------------------------------------
# Hybrid Model Implementation
#-----------------------------------------------------------------------------

class FinalHybridModel:
    """Final hybrid model combining growth rate and temperature prediction."""

    def __init__(self, n_growth_features: int = 250, n_temp_features: int = 800,
                 output_dir: str = "models/final_hybrid_model", temp_transform: str = "none",
                 growth_transform: str = "none"):
        """Initialize the final hybrid model.

        Args:
            n_growth_features: Number of features to use for growth rate prediction
            n_temp_features: Number of features to use for temperature prediction
            output_dir: Directory to save model outputs
            temp_transform: Transformation to apply to temperature values (none, sqrt, log2, log10)
            growth_transform: Transformation to apply to growth rate values (none, sqrt, log2, log10)
        """
        self.n_growth_features = n_growth_features
        self.n_temp_features = n_temp_features
        self.output_dir = output_dir
        self.temp_transform = temp_transform
        self.growth_transform = growth_transform

        # Create output directory
        os.makedirs(output_dir, exist_ok=True)

        # Initialize model components to None - they will be created in fit()
        self.growth_model = None
        self.temp_model = None
        self.growth_scaler = None
        self.temp_scaler = None
        self.temp_feature_indices = None
        self.growth_feature_indices = None

        # Set up logging
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(logging.INFO)
        if not self.logger.handlers:
            fh = logging.FileHandler(os.path.join(output_dir, "training.log"))
            fh.setLevel(logging.INFO)
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            fh.setFormatter(formatter)
            self.logger.addHandler(fh)

        self.logger.info(f"Initialized FinalHybridModel with {n_growth_features} growth features and {n_temp_features} temperature features")
        self.logger.info(f"Growth rate transformation: {growth_transform}")
        self.logger.info(f"Temperature transformation: {temp_transform}")

    def _transform_temperature(self, temp: np.ndarray) -> np.ndarray:
        """Transform temperature values.

        Args:
            temp: Temperature values to transform

        Returns:
            Transformed temperature values
        """
        # Ensure we're working with numpy arrays
        if hasattr(temp, 'values'):
            temp = temp.values

        # Flatten array to ensure consistent handling
        temp = temp.reshape(-1)

        # For debugging and diagnostic purposes
        self.logger.info(f"Temperature range before transform: min={np.min(temp):.4f}, max={np.max(temp):.4f}, mean={np.mean(temp):.4f}")

        # Check for outliers and log them
        q1 = np.percentile(temp, 25)
        q3 = np.percentile(temp, 75)
        iqr = q3 - q1
        upper_bound = q3 + 3 * iqr
        lower_bound = q1 - 3 * iqr

        n_outliers = np.sum((temp > upper_bound) | (temp < lower_bound))
        if n_outliers > 0:
            self.logger.info(f"Detected {n_outliers} outliers in temperature values")

        # Apply transformation (which is 'none' by default)
        if self.temp_transform == "none":
            return temp
        elif self.temp_transform == "sqrt":
            return np.sqrt(np.abs(temp))  # Ensure positive values for sqrt
        elif self.temp_transform == "log2":
            return np.log2(np.abs(temp) + 1e-10)  # Add small constant to avoid log(0)
        elif self.temp_transform == "log10":
            return np.log10(np.abs(temp) + 1e-10)  # Add small constant to avoid log(0)
        else:
            raise ValueError(f"Unknown temperature transform: {self.temp_transform}")

    def _inverse_transform_temperature(self, temp: np.ndarray) -> np.ndarray:
        """Inverse transform temperature values.

        Args:
            temp: Transformed temperature values

        Returns:
            Original temperature values
        """
        # For debugging
        self.logger.info(f"Temperature range before inverse transform: min={np.min(temp):.4f}, max={np.max(temp):.4f}, mean={np.mean(temp):.4f}")

        # Apply inverse transformation
        if self.temp_transform == "none":
            result = temp
        elif self.temp_transform == "sqrt":
            result = temp ** 2
        elif self.temp_transform == "log2":
            result = 2 ** temp
        elif self.temp_transform == "log10":
            result = 10 ** temp
        else:
            raise ValueError(f"Unknown temperature transform: {self.temp_transform}")

        self.logger.info(f"Temperature range after inverse transform: min={np.min(result):.4f}, max={np.max(result):.4f}, mean={np.mean(result):.4f}")
        return result

    def _transform_growth_rate(self, growth: np.ndarray) -> np.ndarray:
        """Transform growth rate values.

        Args:
            growth: Growth rate values to transform

        Returns:
            Transformed growth rate values
        """
        # For debugging
        self.logger.info(f"Transforming growth rate with method: {self.growth_transform}")
        self.logger.info(f"Original growth rate range: min={np.min(growth):.4f}, max={np.max(growth):.4f}")

        # Check for outliers and clip extreme values (using robust statistics)
        # This fixes the massive scale difference between true and predicted values
        median_val = np.median(growth)
        q1 = np.percentile(growth, 25)
        q3 = np.percentile(growth, 75)
        iqr = q3 - q1
        upper_bound = q3 + 3 * iqr

        # Log original vs clipped values
        n_outliers = np.sum(growth > upper_bound)
        if n_outliers > 0:
            self.logger.info(f"Detected {n_outliers} outliers (>{upper_bound:.4f}) in growth rate values, clipping to range")
            growth_clipped = np.clip(growth, 0, upper_bound)
            self.logger.info(f"After clipping: min={np.min(growth_clipped):.4f}, max={np.max(growth_clipped):.4f}")
        else:
            growth_clipped = growth

        # Now apply the actual transformation to clipped values
        if self.growth_transform == "none":
            result = growth_clipped
        elif self.growth_transform == "sqrt":
            result = np.sqrt(np.abs(growth_clipped) + 1e-10)  # Ensure positive values for sqrt
        elif self.growth_transform == "log2":
            # Handle zero values properly for log transformation
            min_nonzero = np.min(growth_clipped[growth_clipped > 0]) if np.any(growth_clipped > 0) else 1e-10
            epsilon = min(min_nonzero * 0.1, 1e-10)  # Use smaller of 10% of min value or 1e-10
            result = np.log2(growth_clipped + epsilon)
        elif self.growth_transform == "log10":
            # Handle zero values properly for log transformation
            min_nonzero = np.min(growth_clipped[growth_clipped > 0]) if np.any(growth_clipped > 0) else 1e-10
            epsilon = min(min_nonzero * 0.1, 1e-10)  # Use smaller of 10% of min value or 1e-10
            result = np.log10(growth_clipped + epsilon)
        else:
            raise ValueError(f"Unknown growth transform: {self.growth_transform}")

        # For debugging
        self.logger.info(f"Transformed growth rate range: min={np.min(result):.4f}, max={np.max(result):.4f}")
        return result

    def _inverse_transform_growth_rate(self, growth_transformed: np.ndarray) -> np.ndarray:
        """Inverse transform growth rate values.

        Args:
            growth_transformed: Transformed growth rate values

        Returns:
            Original scale growth rate values
        """
        # For debugging
        self.logger.info(f"Inverse transforming growth rate with method: {self.growth_transform}")
        self.logger.info(f"Transformed growth rate range: min={np.min(growth_transformed):.4f}, max={np.max(growth_transformed):.4f}")

        # Apply inverse transformation
        if self.growth_transform == "none":
            result = growth_transformed
        elif self.growth_transform == "sqrt":
            result = np.square(growth_transformed)
        elif self.growth_transform == "log2":
            # For log2, use the proper inverse transformation
            min_nonzero = np.min(growth_transformed[growth_transformed > 0]) if np.any(growth_transformed > 0) else 1e-10
            epsilon = min(min_nonzero * 0.1, 1e-10)  # Same epsilon used in forward transform
            result = np.power(2, growth_transformed) - epsilon
        elif self.growth_transform == "log10":
            min_nonzero = np.min(growth_transformed[growth_transformed > 0]) if np.any(growth_transformed > 0) else 1e-10
            epsilon = min(min_nonzero * 0.1, 1e-10)
            result = np.power(10, growth_transformed) - epsilon
        else:
            raise ValueError(f"Unknown growth transform: {self.growth_transform}")

        # Ensure non-negative growth rates
        result = np.maximum(result, 0)

        # For debugging
        self.logger.info(f"Original growth rate range (after inverse transform): min={np.min(result):.4f}, max={np.max(result):.4f}")
        return result

    def _calculate_metrics_robustly(self, y_true, y_pred, prefix=""):
        """Calculate metrics robustly with detailed diagnostics.

        Args:
            y_true: True values
            y_pred: Predicted values
            prefix: Prefix for logging (e.g., "temp" or "growth")

        Returns:
            Dictionary of metrics
        """
        # Convert to numpy arrays if needed
        if hasattr(y_true, 'values'):
            y_true = y_true.values
        if hasattr(y_pred, 'values'):
            y_pred = y_pred.values

        # Ensure 1D arrays
        y_true = y_true.flatten()
        y_pred = y_pred.flatten()

        # Check for NaN or Inf values
        if np.isnan(y_true).any() or np.isnan(y_pred).any() or np.isinf(y_true).any() or np.isinf(y_pred).any():
            self.logger.warning(f"{prefix} contains NaN or Inf values in true or predicted values")
            # Replace with finite values
            y_true = np.nan_to_num(y_true)
            y_pred = np.nan_to_num(y_pred)

        # Calculate metrics
        mse = mean_squared_error(y_true, y_pred)
        rmse = np.sqrt(mse)
        mae = mean_absolute_error(y_true, y_pred)

        # Calculate R² using scikit-learn
        r2 = r2_score(y_true, y_pred)

        # Calculate R² manually for validation
        ss_tot = np.sum((y_true - np.mean(y_true)) ** 2)
        ss_res = np.sum((y_true - y_pred) ** 2)

        # Handle edge case: if ss_tot is 0, R² is undefined
        if ss_tot < 1e-10:
            r2_manual = 0.0
            self.logger.warning(f"{prefix} R² calculation: ss_tot is near zero, setting R² to 0")
        else:
            r2_manual = 1 - (ss_res / ss_tot)

        # Log diagnostics
        self.logger.debug(f"{prefix} metrics calculation diagnostics:")
        self.logger.debug(f"  Data shapes: y_true: {y_true.shape}, y_pred: {y_pred.shape}")
        self.logger.debug(f"  Value ranges: y_true: [{np.min(y_true):.4f}, {np.max(y_true):.4f}], y_pred: [{np.min(y_pred):.4f}, {np.max(y_pred):.4f}]")
        self.logger.debug(f"  Mean values: y_true: {np.mean(y_true):.4f}, y_pred: {np.mean(y_pred):.4f}")
        self.logger.debug(f"  Standard deviation: y_true: {np.std(y_true):.4f}, y_pred: {np.std(y_pred):.4f}")
        self.logger.debug(f"  MSE: {mse:.6f}, RMSE: {rmse:.6f}, MAE: {mae:.6f}")
        self.logger.debug(f"  R² (sklearn): {r2:.6f}, R² (manual): {r2_manual:.6f}")
        self.logger.debug(f"  Sum of squares (total): {ss_tot:.6f}, Sum of squares (residual): {ss_res:.6f}")

        # Calculate correlation
        correlation = np.corrcoef(y_true, y_pred)[0, 1]
        self.logger.info(f"  Correlation coefficient: {correlation:.6f}")

        return {
            "mse": mse,
            "rmse": rmse,
            "mae": mae,
            "r2": r2,
            "r2_manual": r2_manual,
            "correlation": correlation
        }

    def _calculate_growth_metrics(self, y_true, y_pred, transformed_scale=False):
        """Calculate metrics specifically for growth rate predictions, ensuring consistent scale.

        Args:
            y_true: True growth rate values
            y_pred: Predicted growth rate values
            transformed_scale: If True, transform both predictions and targets before comparing

        Returns:
            Dictionary with metrics
        """
        # Convert to numpy arrays if needed
        if hasattr(y_true, 'values'):
            y_true = y_true.values
        if hasattr(y_pred, 'values'):
            y_pred = y_pred.values

        # Ensure 1D arrays
        y_true = y_true.flatten()
        y_pred = y_pred.flatten()

        # Check and filter NaN values
        valid_idx = ~(np.isnan(y_true) | np.isnan(y_pred))
        y_true = y_true[valid_idx]
        y_pred = y_pred[valid_idx]

        if len(y_true) == 0:
            self.logger.warning("No valid data points for growth metrics calculation")
            return {"r2": 0.0, "rmse": float('inf'), "mae": float('inf')}

        # Log the range for diagnostics
        if transformed_scale:
            scale_str = "transformed"
        else:
            scale_str = "original"

        self.logger.debug(f"Growth metrics calculation ({scale_str} scale):")
        self.logger.debug(f"  True values range: min={np.min(y_true):.6f}, max={np.max(y_true):.6f}, mean={np.mean(y_true):.6f}")
        self.logger.debug(f"  Pred values range: min={np.min(y_pred):.6f}, max={np.max(y_pred):.6f}, mean={np.mean(y_pred):.6f}")

        # Explicitly calculate R² using sklearn
        from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
        r2 = r2_score(y_true, y_pred)

        # Manual calculation as backup
        y_mean = np.mean(y_true)
        ss_total = np.sum((y_true - y_mean) ** 2)
        ss_residual = np.sum((y_true - y_pred) ** 2)
        manual_r2 = 1 - (ss_residual / ss_total) if ss_total > 0 else 0.0

        # Log both calculations
        self.logger.info(f"  sklearn R² = {r2:.6f}, manual R² = {manual_r2:.6f}")

        # Clamp R² to avoid unreasonable negative values
        if r2 < -1.0:
            self.logger.warning(f"Clamping negative R² from {r2:.6f} to -1.0")
            r2 = -1.0

        # Calculate RMSE and MAE
        rmse = np.sqrt(mean_squared_error(y_true, y_pred))
        mae = mean_absolute_error(y_true, y_pred)

        # Return all metrics
        return {"r2": r2, "rmse": rmse, "mae": mae}

    def _calculate_temperature_metrics(self, y_true, y_pred):
        """Calculate metrics specifically for temperature predictions with advanced outlier handling.

        Args:
            y_true: True temperature values
            y_pred: Predicted temperature values

        Returns:
            Dictionary with metrics
        """
        # Convert to numpy arrays if needed
        if hasattr(y_true, 'values'):
            y_true = y_true.values
        else:
            y_true = y_true.copy()

        if hasattr(y_pred, 'values'):
            y_pred = y_pred.values
        else:
            y_pred = y_pred.copy()

        # Ensure 1D arrays
        y_true = y_true.flatten()
        y_pred = y_pred.flatten()

        # Check for NaN and Inf values and filter them out
        valid_idx = ~(np.isnan(y_true) | np.isnan(y_pred) | np.isinf(y_true) | np.isinf(y_pred))
        y_true = y_true[valid_idx]
        y_pred = y_pred[valid_idx]

        if len(y_true) == 0:
            self.logger.warning("No valid data points for temperature metrics calculation")
            return {"r2": 0.0, "rmse": float('inf'), "mae": float('inf')}

        # IMPROVEMENT: Handle extreme range differences that could limit R²
        # Clip predicted values to be within a realistic range based on training data
        y_pred = np.clip(y_pred, np.min(y_true), np.max(y_true))

        # Fix for sklearn R² vs manual R² calculation discrepancy
        # The sklearn implementation can handle edge cases better than our manual calculation
        from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error

        # Standard metrics calculation using sklearn's implementations
        r2 = r2_score(y_true, y_pred)
        rmse = np.sqrt(mean_squared_error(y_true, y_pred))
        mae = mean_absolute_error(y_true, y_pred)

        # Calculate adjusted R² which accounts for model complexity
        n = len(y_true)
        p = self.n_temp_features if hasattr(self, 'n_temp_features') and self.n_temp_features else 10
        adj_r2 = 1 - ((1 - r2) * (n - 1) / (n - p - 1)) if n > p + 1 else r2  # Prevent division by zero

        # Calculate correlation coefficient for comparison
        correlation = np.corrcoef(y_true, y_pred)[0, 1]

        # Calculate manual R² for debugging only, using the same formula as sklearn
        y_mean = np.mean(y_true)
        ss_total = np.sum((y_true - y_mean) ** 2)
        ss_residual = np.sum((y_true - y_pred) ** 2)
        manual_r2 = 1 - (ss_residual / ss_total) if ss_total > 0 else 0

        # Detailed diagnostics
        self.logger.info(f"Temperature model metrics diagnostics:")
        self.logger.info(f"  Data points: {len(y_true)}")
        self.logger.info(f"  True temp range: min={np.min(y_true):.4f}, max={np.max(y_true):.4f}, mean={np.mean(y_true):.4f}, std={np.std(y_true):.4f}")
        self.logger.info(f"  Pred temp range: min={np.min(y_pred):.4f}, max={np.max(y_pred):.4f}, mean={np.mean(y_pred):.4f}, std={np.std(y_pred):.4f}")
        self.logger.info(f"  sklearn R²: {r2:.6f}, manual R²: {manual_r2:.6f}, adjusted R²: {adj_r2:.6f}")
        self.logger.info(f"  RMSE: {rmse:.6f}, MAE: {mae:.6f}")
        self.logger.info(f"  Correlation: {correlation:.6f}")
        self.logger.info(f"  Sum squares total: {ss_total:.6f}, Sum squares residual: {ss_residual:.6f}")

        # No longer use the manual R², since we've fixed the discrepancy by using sklearn's implementation

        # Calculate normalized metrics that are more interpretable
        # Normalized RMSE as percentage of the data range
        range_y = np.max(y_true) - np.min(y_true)
        nrmse = rmse / range_y if range_y > 0 else float('inf')

        return {
            "r2": r2,
            "adj_r2": adj_r2,
            "rmse": rmse,
            "mae": mae,
            "nrmse": nrmse,
            "correlation": correlation
        }

    def fit(self, X: np.ndarray, y_growth: np.ndarray, y_temp: np.ndarray):
        """Train the hybrid model.

        Args:
            X: Input features
            y_growth: Target growth rate values
            y_temp: Target temperature values
        """
        # Reset models to avoid shape mismatch from previous runs
        self.temp_model = None
        self.growth_model = None

        # First perform stratified split with a 7:2:1 ratio for train:val:test
        # For stratified split with continuous targets, we need to bin the values first
        self.logger.info("Performing stratified train-validation-test splitting with 7:2:1 ratio")

        # Create bins for stratification
        n_bins = 10  # Number of bins for stratification

        # Convert to numpy arrays if needed
        if hasattr(y_growth, 'values'):
            y_growth_np = y_growth.values
        else:
            y_growth_np = y_growth

        if hasattr(y_temp, 'values'):
            y_temp_np = y_temp.values
        else:
            y_temp_np = y_temp

        # Log basic statistics on the data
        self.logger.info(f"Temperature statistics: min={np.min(y_temp_np):.4f}, max={np.max(y_temp_np):.4f}, mean={np.mean(y_temp_np):.4f}, std={np.std(y_temp_np):.4f}")
        self.logger.info(f"Growth rate statistics: min={np.min(y_growth_np):.4f}, max={np.max(y_growth_np):.4f}, mean={np.mean(y_growth_np):.4f}, std={np.std(y_growth_np):.4f}")

        # Bin temperature values for stratification
        temp_discretizer = KBinsDiscretizer(n_bins=n_bins, encode='ordinal', strategy='quantile')
        temp_bins = temp_discretizer.fit_transform(y_temp_np.reshape(-1, 1)).flatten().astype(int)
        self.logger.info(f"Created {n_bins} bins for temperature stratification")

        # First split into train+val and test sets (90% / 10%)
        X_trainval, X_test, y_growth_trainval, y_growth_test, y_temp_trainval, y_temp_test, temp_bins_trainval, _ = train_test_split(
            X, y_growth, y_temp, temp_bins, test_size=0.1, random_state=42, stratify=temp_bins
        )

        # Now split trainval into train and validation using temperature bins (77.8% / 22.2% of trainval, which is 70% / 20% of total)
        X_train, X_val, y_growth_train, y_growth_val, y_temp_train, y_temp_val = train_test_split(
            X_trainval, y_growth_trainval, y_temp_trainval, test_size=0.222, random_state=42, stratify=temp_bins_trainval
        )

        # Save the test set for final evaluation
        np_X_test = X_test.values if hasattr(X_test, 'values') else X_test
        np_y_growth_test = y_growth_test.values if hasattr(y_growth_test, 'values') else y_growth_test
        np_y_temp_test = y_temp_test.values if hasattr(y_temp_test, 'values') else y_temp_test

        # Save test set
        test_data = {
            'X_test': np_X_test,
            'y_growth_test': np_y_growth_test,
            'y_temp_test': np_y_temp_test
        }
        joblib.dump(test_data, os.path.join(self.output_dir, "test_data.pkl"))
        self.logger.info(f"Saved test data to {os.path.join(self.output_dir, 'test_data.pkl')}")

        # Log data splits
        self.logger.info(f"Train-validation-test split: {len(X_train)} train, {len(X_val)} validation, {len(X_test)} test samples")
        self.logger.info(f"Growth rate range - Train: [{np.min(y_growth_train):.4f}, {np.max(y_growth_train):.4f}], Val: [{np.min(y_growth_val):.4f}, {np.max(y_growth_val):.4f}], Test: [{np.min(y_growth_test):.4f}, {np.max(y_growth_test):.4f}]")
        self.logger.info(f"Temperature range - Train: [{np.min(y_temp_train):.4f}, {np.max(y_temp_train):.4f}], Val: [{np.min(y_temp_val):.4f}, {np.max(y_temp_val):.4f}], Test: [{np.min(y_temp_test):.4f}, {np.max(y_temp_test):.4f}]")

        # Transform values
        y_temp_train_transformed = self._transform_temperature(y_temp_train)
        y_temp_val_transformed = self._transform_temperature(y_temp_val)
        y_growth_train_transformed = self._transform_growth_rate(y_growth_train)
        y_growth_val_transformed = self._transform_growth_rate(y_growth_val)

        # Log transformed data ranges
        self.logger.debug(f"Transformed growth rate range - Training: [{np.min(y_growth_train_transformed):.4f}, {np.max(y_growth_train_transformed):.4f}], Validation: [{np.min(y_growth_val_transformed):.4f}, {np.max(y_growth_val_transformed):.4f}]")
        self.logger.debug(f"Transformed temperature range - Training: [{np.min(y_temp_train_transformed):.4f}, {np.max(y_temp_train_transformed):.4f}], Validation: [{np.min(y_temp_val_transformed):.4f}, {np.max(y_temp_val_transformed):.4f}]")

        # Check for extreme temperature outliers that might skew the model
        q1 = np.percentile(y_temp_train_transformed, 25)
        q3 = np.percentile(y_temp_train_transformed, 75)
        iqr = q3 - q1
        lower_bound = q1 - 3 * iqr
        upper_bound = q3 + 3 * iqr
        outliers = np.sum((y_temp_train_transformed < lower_bound) | (y_temp_train_transformed > upper_bound))
        self.logger.info(f"Detected {outliers} potential temperature outliers in training data")

        # For improved stability, consider clipping extreme outliers
        if outliers > 0:
            self.logger.info(f"Clipping {outliers} temperature outliers to improve model stability")
            y_temp_train_transformed = np.clip(y_temp_train_transformed, lower_bound, upper_bound)
            # Also clip validation data to same bounds for consistency
            y_temp_val_transformed = np.clip(y_temp_val_transformed, lower_bound, upper_bound)
            self.logger.info(f"After clipping - Training: [{np.min(y_temp_train_transformed):.4f}, {np.max(y_temp_train_transformed):.4f}], Validation: [{np.min(y_temp_val_transformed):.4f}, {np.max(y_temp_val_transformed):.4f}]")

        # Prepare data for training
        # Enhanced feature selection for temperature using RFE
        if self.n_temp_features and X.shape[1] > self.n_temp_features:
            # First ensure X_train is a numpy array for feature selection
            X_train_np = X_train.values if hasattr(X_train, 'values') else X_train
            X_val_np = X_val.values if hasattr(X_val, 'values') else X_val

            # Apply Random Forest feature importance for temperature model features
            self.logger.info(f"Using Random Forest feature importance to select {self.n_temp_features} optimal features for temperature prediction")

            # Initialize a Random Forest for feature importance
            rf_model = RandomForestRegressor(n_estimators=100,
                                            random_state=42,
                                            n_jobs=-1,
                                            max_depth=20,  # Limit depth to prevent overfitting
                                            min_samples_leaf=5)  # Ensure robustness

            # Start timer
            start_time = time.time()

            # Fit the Random Forest
            self.logger.info("Training Random Forest to determine feature importance...")
            rf_model.fit(X_train_np, y_temp_train)

            # Get feature importance
            self.logger.info("Calculating feature importance...")
            feature_importances = rf_model.feature_importances_

            # Select top features based on importance
            temp_feature_indices = np.argsort(feature_importances)[-self.n_temp_features:]

            # End timer
            end_time = time.time()
            self.logger.info(f"Feature selection completed in {end_time - start_time:.2f} seconds")

            # Validate the selected features
            X_train_temp = X_train_np[:, temp_feature_indices]
            X_val_temp = X_val_np[:, temp_feature_indices]

            # Save feature importance information
            feature_importance_df = pd.DataFrame({
                'Feature_Index': np.arange(len(feature_importances)),
                'Importance': feature_importances,
                'Selected': np.isin(np.arange(len(feature_importances)), temp_feature_indices)
            })

            # Sort by importance (most important first)
            feature_importance_df = feature_importance_df.sort_values('Importance', ascending=False)

            # Save to output directory
            feature_importance_df.to_csv(os.path.join(self.output_dir, "temp_feature_importance.csv"), index=False)
            self.logger.info(f"Saved feature importance rankings to {os.path.join(self.output_dir, 'temp_feature_importance.csv')}")

            # If feature names are available, add them to the report
            if hasattr(X, 'columns'):
                feature_names = X.columns
                feature_importance_with_names = pd.DataFrame({
                    'Feature_Index': np.arange(len(feature_importances)),
                    'Feature_Name': feature_names,
                    'Importance': feature_importances,
                    'Selected': np.isin(np.arange(len(feature_importances)), temp_feature_indices)
                })
                feature_importance_with_names = feature_importance_with_names.sort_values('Importance', ascending=False)
                feature_importance_with_names.to_csv(os.path.join(self.output_dir, "temp_feature_importance_with_names.csv"), index=False)
                self.logger.info(f"Saved feature importance with names to {os.path.join(self.output_dir, 'temp_feature_importance_with_names.csv')}")

            # Validate the selected features on validation set
            rf_model.fit(X_train_temp, y_temp_train)
            val_score = rf_model.score(X_val_temp, y_temp_val)
            self.logger.info(f"Validation R² with selected features using RF: {val_score:.4f}")

            self.logger.info(f"Selected {len(temp_feature_indices)} features for temperature prediction using Random Forest importance")
        else:
            # Convert to numpy if needed
            X_train_temp = X_train.values if hasattr(X_train, 'values') else X_train
            X_val_temp = X_val.values if hasattr(X_val, 'values') else X_val

        # Scale the data for temperature model
        self.temp_scaler = RobustScaler()  # More robust to outliers than StandardScaler
        X_train_temp_scaled = self.temp_scaler.fit_transform(X_train_temp)
        X_val_temp_scaled = self.temp_scaler.transform(X_val_temp)

        # Growth feature selection (keep as is - don't modify the growth rate model)
        if self.n_growth_features and X.shape[1] > self.n_growth_features:
            # Ensure we're working with numpy arrays
            X_train_np = X_train.values if hasattr(X_train, 'values') else X_train
            X_val_np = X_val.values if hasattr(X_val, 'values') else X_val

            # Use pure mutual information for growth rate (captures nonlinear relationships)
            mi_values = mutual_info_regression(X_train_np, y_growth_train)
            growth_feature_indices = np.argsort(mi_values)[-self.n_growth_features:]
            X_train_growth = X_train_np[:, growth_feature_indices]
            X_val_growth = X_val_np[:, growth_feature_indices]

            self.logger.info(f"Selected {self.n_growth_features} features for growth rate prediction using mutual information")
        else:
            # Convert to numpy if needed
            X_train_growth = X_train.values if hasattr(X_train, 'values') else X_train
            X_val_growth = X_val.values if hasattr(X_val, 'values') else X_val

        # Scale growth data
        self.growth_scaler = StandardScaler()
        X_train_growth_scaled = self.growth_scaler.fit_transform(X_train_growth)
        X_val_growth_scaled = self.growth_scaler.transform(X_val_growth)

        # Save feature indices and scalers for prediction
        if self.n_temp_features and X.shape[1] > self.n_temp_features:
            self.temp_feature_indices = temp_feature_indices
        else:
            self.temp_feature_indices = np.arange(X.shape[1])

        if self.n_growth_features and X.shape[1] > self.n_growth_features:
            self.growth_feature_indices = growth_feature_indices
        else:
            self.growth_feature_indices = np.arange(X.shape[1])

        # Save the feature indices along with the model for prediction
        joblib.dump(self.temp_feature_indices, os.path.join(self.output_dir, "temp_feature_indices.pkl"))
        joblib.dump(self.growth_feature_indices, os.path.join(self.output_dir, "growth_feature_indices.pkl"))

        # Initialize models if not already initialized
        if self.growth_model is None:
            growth_input_dim = X_train_growth.shape[1]
            self.logger.info(f"Initializing growth model with input dimension: {growth_input_dim}")
            self.growth_model = nn.Sequential(
                nn.Linear(growth_input_dim, 512),
                nn.BatchNorm1d(512),
                nn.GELU(),
                nn.Dropout(0.3),
                nn.Linear(512, 384),
                nn.BatchNorm1d(384),
                nn.GELU(),
                nn.Dropout(0.4),
                nn.Linear(384, 256),
                nn.BatchNorm1d(256),
                nn.GELU(),
                nn.Dropout(0.4),
                nn.Linear(256, 128),
                nn.BatchNorm1d(128),
                nn.GELU(),
                nn.Dropout(0.5),
                nn.Linear(128, 1)
            ).to(device)

        if self.temp_model is None:
            # Get the correct input dimension after feature selection
            temp_input_dim = X_train_temp.shape[1]
            self.logger.info(f"Initializing temperature model with input dimension: {temp_input_dim}")

            # Ensure input dimension is correct by validating
            assert temp_input_dim == X_train_temp_scaled.shape[1], f"Input dimension mismatch: {temp_input_dim} vs {X_train_temp_scaled.shape[1]}"

            # Using the successful RegularizedDNN model architecture from evaluate_enhanced_dnn_temperature.py
            class EnhancedTemperatureDNN(nn.Module):
                """
                Enhanced temperature model based on the successful RegularizedDNN architecture.
                """
                def __init__(self, input_dim, hidden_dims=[512, 512, 384, 256, 128],
                             dropout_rates=[0.3, 0.4, 0.4, 0.5, 0.5], use_batch_norm=True,
                             activation='relu'):
                    super().__init__()

                    # Create layers
                    layers = []
                    prev_dim = input_dim

                    # Choose activation function
                    if activation == 'relu':
                        act_fn = nn.ReLU()
                    elif activation == 'leaky_relu':
                        act_fn = nn.LeakyReLU(0.1)
                    elif activation == 'elu':
                        act_fn = nn.ELU()
                    elif activation == 'gelu':
                        act_fn = nn.GELU()
                    else:
                        act_fn = nn.ReLU()

                    # Build network with progressively increasing dropout
                    for i, hidden_dim in enumerate(hidden_dims):
                        # Linear layer
                        layers.append(nn.Linear(prev_dim, hidden_dim))

                        # Batch normalization (before activation)
                        if use_batch_norm:
                            layers.append(nn.BatchNorm1d(hidden_dim))

                        # Activation
                        layers.append(act_fn)

                        # Dropout with increasing rate for deeper layers
                        dropout_rate = dropout_rates[min(i, len(dropout_rates)-1)]
                        layers.append(nn.Dropout(dropout_rate))

                        prev_dim = hidden_dim

                    self.feature_extractor = nn.Sequential(*layers)

                    # Output layer with no activation or dropout
                    self.output_layer = nn.Linear(prev_dim, 1)

                    # Initialize weights with improved method
                    self._init_weights()

                def _init_weights(self):
                    """Initialize weights with improved initialization."""
                    for m in self.modules():
                        if isinstance(m, nn.Linear):
                            # Use Xavier/Glorot initialization for better stability
                            nn.init.xavier_uniform_(m.weight)
                            if m.bias is not None:
                                nn.init.zeros_(m.bias)

                def forward(self, x):
                    features = self.feature_extractor(x)
                    output = self.output_layer(features)
                    return output.squeeze()

            # Initialize the temperature model with the proven architecture
            self.temp_model = EnhancedTemperatureDNN(
                input_dim=temp_input_dim,
                hidden_dims=[512, 512, 384, 256, 128],
                dropout_rates=[0.3, 0.4, 0.4, 0.5, 0.5],
                use_batch_norm=True,
                activation='relu'
            ).to(device)

            # Print model architecture for debugging
            model_summary = str(self.temp_model)
            # Only log first few lines to avoid cluttering logs
            self.logger.info(f"Temperature model architecture summary:\n{model_summary.split('(')[-1].split(')')[0]}")

        # Set up optimizers and schedulers
        growth_optimizer = optim.Adam(self.growth_model.parameters(), lr=0.001)

        # Use AdamW with stronger L2 regularization for temperature model (proven effective)
        temp_optimizer = optim.AdamW(self.temp_model.parameters(), lr=0.001, weight_decay=1e-5)

        growth_scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            growth_optimizer, mode='min', factor=0.5, patience=5
        )

        # Set up temperature scheduler based on arguments or config
        use_one_cycle_lr = getattr(args, 'use_one_cycle_lr', False) if 'args' in locals() else False

        if use_one_cycle_lr:
            # Use OneCycleLR scheduler with proven effective settings
            train_size = len(X_train_temp)
            steps_per_epoch = train_size // 64 + (1 if train_size % 64 > 0 else 0)  # calculate steps reliably
            self.logger.info(f"Using OneCycleLR with {steps_per_epoch} steps per epoch")

            temp_scheduler = optim.lr_scheduler.OneCycleLR(
                temp_optimizer,
                max_lr=0.002,  # Higher max learning rate from proven method
                steps_per_epoch=steps_per_epoch,
                epochs=300,
                pct_start=0.1,
                div_factor=25,
                final_div_factor=1000
            )
            self.logger.info("Using OneCycleLR scheduler for temperature model")
        else:
            # Use CosineAnnealingWarmRestarts with parameters from proven method
            temp_scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
                temp_optimizer,
                T_0=10,  # Shorter cycle for quicker adaptation
                T_mult=2,  # Double period after each restart
                eta_min=1e-6  # Lower minimum learning rate
            )
            self.logger.info("Using CosineAnnealing scheduler for temperature model")

        # Create separate datasets for temperature and growth models
        train_temp_dataset = TensorDataset(
            torch.FloatTensor(X_train_temp_scaled),
            torch.FloatTensor(y_temp_train_transformed.reshape(-1, 1) if not hasattr(y_temp_train_transformed, 'iloc') else y_temp_train_transformed.values.reshape(-1, 1))
        )

        val_temp_dataset = TensorDataset(
            torch.FloatTensor(X_val_temp_scaled),
            torch.FloatTensor(y_temp_val_transformed.reshape(-1, 1) if not hasattr(y_temp_val_transformed, 'iloc') else y_temp_val_transformed.values.reshape(-1, 1))
        )

        train_growth_dataset = TensorDataset(
            torch.FloatTensor(X_train_growth_scaled),
            torch.FloatTensor(y_growth_train_transformed.reshape(-1, 1) if not hasattr(y_growth_train_transformed, 'iloc') else y_growth_train_transformed.values.reshape(-1, 1))
        )

        val_growth_dataset = TensorDataset(
            torch.FloatTensor(X_val_growth_scaled),
            torch.FloatTensor(y_growth_val_transformed.reshape(-1, 1) if not hasattr(y_growth_val_transformed, 'iloc') else y_growth_val_transformed.values.reshape(-1, 1))
        )

        # Log tensor shapes for debugging
        self.logger.info(f"Temperature model input shape: {X_train_temp_scaled.shape}")
        self.logger.info(f"First layer weight shape: {self.temp_model.feature_extractor[0].weight.shape}")

        # Create separate dataloaders for each model
        train_temp_loader = DataLoader(train_temp_dataset, batch_size=64, shuffle=True)
        val_temp_loader = DataLoader(val_temp_dataset, batch_size=128)
        train_growth_loader = DataLoader(train_growth_dataset, batch_size=128, shuffle=True)
        val_growth_loader = DataLoader(val_growth_dataset, batch_size=128)

        # Training history
        self.history = defaultdict(list)

        # Initialize best model states
        best_temp_model_state = copy.deepcopy(self.temp_model.state_dict())
        best_growth_model_state = copy.deepcopy(self.growth_model.state_dict())
        best_temp_val_r2 = float('-inf')
        best_growth_val_r2 = float('-inf')

        # Train the model
        for epoch in range(300):
            # Training mode
            self.temp_model.train()
            self.growth_model.train()
            train_temp_loss = 0
            train_growth_loss = 0

            # Train temperature model
            for X_batch, y_temp_batch in train_temp_loader:
                X_batch = X_batch.to(device)
                y_temp_batch = y_temp_batch.to(device)

                # Validate tensor shapes for debugging
                if epoch == 0 and X_batch is train_temp_loader.dataset[0][0].unsqueeze(0):
                    self.logger.info(f"First batch shape: {X_batch.shape}")
                    self.logger.info(f"Temperature model input layer weight shape: {self.temp_model.feature_extractor[0].weight.shape}")

                # Temperature model forward and backward pass
                temp_optimizer.zero_grad()
                temp_pred = self.temp_model(X_batch)

                # Ensure both tensors have the same shape
                # The .view() method reshapes the tensor without changing its data
                if temp_pred.dim() == 1:
                    temp_pred = temp_pred.view(-1, 1)
                if y_temp_batch.dim() == 1:
                    y_temp_batch = y_temp_batch.view(-1, 1)

                # Use MSE loss (proven effective in successful model)
                temp_loss = F.mse_loss(temp_pred, y_temp_batch)

                # Add L1 loss with setting from successful model
                l1_reg = 0.0
                for param in self.temp_model.parameters():
                    l1_reg += torch.norm(param, 1)

                # Use L1 regularization strength from proven model
                temp_loss = temp_loss + 1e-5 * l1_reg

                # Backward pass
                temp_loss.backward()

                # Apply gradient clipping (0.5 from proven model)
                torch.nn.utils.clip_grad_norm_(self.temp_model.parameters(), max_norm=0.5)

                # Update weights
                temp_optimizer.step()

                # If using OneCycleLR, call step after each batch
                if use_one_cycle_lr:
                    temp_scheduler.step()

                # Accumulate loss
                train_temp_loss += temp_loss.item()

            # Update scheduler for temperature model - only for CosineAnnealingWarmRestarts
            if not use_one_cycle_lr:
                temp_scheduler.step()

            # Train growth model
            for X_batch, y_growth_batch in train_growth_loader:
                X_batch = X_batch.to(device)
                y_growth_batch = y_growth_batch.to(device)

                # Growth model
                growth_optimizer.zero_grad()
                growth_pred = self.growth_model(X_batch)

                # Ensure both tensors have the same shape
                if growth_pred.dim() == 1:
                    growth_pred = growth_pred.view(-1, 1)
                if y_growth_batch.dim() == 1:
                    y_growth_batch = y_growth_batch.view(-1, 1)

                growth_loss = F.mse_loss(growth_pred, y_growth_batch)
                growth_loss.backward()
                growth_optimizer.step()

                train_growth_loss += growth_loss.item()

            train_temp_loss /= len(train_temp_loader)
            train_growth_loss /= len(train_growth_loader)

            # Evaluation mode for calculating metrics
            self.temp_model.eval()
            self.growth_model.eval()

            # Calculate validation metrics with torch.no_grad()
            with torch.no_grad():
                # Temperature validation
                try:
                    # Log tensor shapes for debugging
                    self.logger.info(f"Validation shapes - X_train_temp_scaled: {X_train_temp_scaled.shape}, X_val_temp_scaled: {X_val_temp_scaled.shape}")

                    X_train_temp_tensor = torch.FloatTensor(X_train_temp_scaled).to(device)
                    X_val_temp_tensor = torch.FloatTensor(X_val_temp_scaled).to(device)

                    # Get predictions in transformed space
                    temp_train_preds = self.temp_model(X_train_temp_tensor)
                    temp_val_preds = self.temp_model(X_val_temp_tensor)

                    # Reshape to match target dimensions for metrics calculation
                    temp_train_preds_transformed = temp_train_preds.cpu().numpy().reshape(-1)
                    temp_val_preds_transformed = temp_val_preds.cpu().numpy().reshape(-1)

                    self.logger.info(f"Temperature prediction shapes - train: {temp_train_preds_transformed.shape}, val: {temp_val_preds_transformed.shape}")

                except Exception as e:
                    self.logger.error(f"Error during temperature validation: {e}")
                    raise

                # Growth rate validation
                X_train_growth_tensor = torch.FloatTensor(X_train_growth_scaled).to(device)
                X_val_growth_tensor = torch.FloatTensor(X_val_growth_scaled).to(device)

                growth_train_preds = self.growth_model(X_train_growth_tensor)
                growth_val_preds = self.growth_model(X_val_growth_tensor)

                # Calculate temperature metrics
                train_temp_metrics = self._calculate_temperature_metrics(y_temp_train, temp_train_preds_transformed)
                val_temp_metrics = self._calculate_temperature_metrics(y_temp_val, temp_val_preds_transformed)

                # Extract temperature metrics
                train_temp_r2 = train_temp_metrics["r2"]
                train_temp_rmse = train_temp_metrics["rmse"]
                train_temp_mae = train_temp_metrics["mae"]
                train_temp_adj_r2 = train_temp_metrics.get("adj_r2", train_temp_r2)  # Fallback to r2 if adj_r2 not present

                val_temp_r2 = val_temp_metrics["r2"]
                val_temp_rmse = val_temp_metrics["rmse"]
                val_temp_mae = val_temp_metrics["mae"]
                val_temp_adj_r2 = val_temp_metrics.get("adj_r2", val_temp_r2)  # Fallback to r2 if adj_r2 not present

                # Calculate growth metrics in transformed space
                train_growth_r2 = r2_score(y_growth_train_transformed, growth_train_preds.cpu().numpy().reshape(-1))
                val_growth_r2 = r2_score(y_growth_val_transformed, growth_val_preds.cpu().numpy().reshape(-1))

                # Calculate additional metrics in transformed space
                train_growth_rmse = np.sqrt(np.mean((y_growth_train_transformed - growth_train_preds.cpu().numpy().reshape(-1))**2))
                train_growth_mae = np.mean(np.abs(y_growth_train_transformed - growth_train_preds.cpu().numpy().reshape(-1)))
                val_growth_rmse = np.sqrt(np.mean((y_growth_val_transformed - growth_val_preds.cpu().numpy().reshape(-1))**2))
                val_growth_mae = np.mean(np.abs(y_growth_val_transformed - growth_val_preds.cpu().numpy().reshape(-1)))

                # Calculate original space values for display
                growth_train_preds_orig = self._inverse_transform_growth_rate(growth_train_preds.cpu().numpy().reshape(-1))
                growth_val_preds_orig = self._inverse_transform_growth_rate(growth_val_preds.cpu().numpy().reshape(-1))

                # Extract temperature metrics
                train_temp_r2 = train_temp_metrics["r2"]
                train_temp_rmse = train_temp_metrics["rmse"]
                train_temp_mae = train_temp_metrics["mae"]
                val_temp_r2 = val_temp_metrics["r2"]
                val_temp_rmse = val_temp_metrics["rmse"]
                val_temp_mae = val_temp_metrics["mae"]

                # Calculate validation losses using the dataloaders
                val_temp_loss = 0
                val_growth_loss = 0

                # Validation for temperature model
                for X_batch, y_batch in val_temp_loader:
                    X_batch = X_batch.to(device)
                    y_batch = y_batch.to(device)

                    temp_pred = self.temp_model(X_batch)

                    # Ensure consistent shapes for loss calculation
                    if temp_pred.dim() == 1:
                        temp_pred = temp_pred.view(-1, 1)
                    if y_batch.dim() == 1:
                        y_batch = y_batch.view(-1, 1)

                    batch_loss = F.mse_loss(temp_pred, y_batch)
                    val_temp_loss += batch_loss.item() * X_batch.size(0)

                val_temp_loss /= len(val_temp_loader.dataset)

                # Validation for growth model
                for X_batch, y_batch in val_growth_loader:
                    X_batch = X_batch.to(device)
                    y_batch = y_batch.to(device)

                    growth_pred = self.growth_model(X_batch)

                    # Ensure consistent shapes for loss calculation
                    if growth_pred.dim() == 1:
                        growth_pred = growth_pred.view(-1, 1)
                    if y_batch.dim() == 1:
                        y_batch = y_batch.view(-1, 1)

                    batch_loss = F.mse_loss(growth_pred, y_batch)
                    val_growth_loss += batch_loss.item() * X_batch.size(0)

                val_growth_loss /= len(val_growth_loader.dataset)

            # Update growth scheduler
            growth_scheduler.step(val_growth_loss)

            # Save metrics to history
            self.history["train_temp_loss"].append(train_temp_loss)
            self.history["train_growth_loss"].append(train_growth_loss)
            self.history["val_temp_loss"].append(val_temp_loss)
            self.history["val_growth_loss"].append(val_growth_loss)
            self.history["train_temp_r2"].append(train_temp_r2)
            self.history["train_growth_r2"].append(train_growth_r2)
            self.history["val_temp_r2"].append(val_temp_r2)
            self.history["val_growth_r2"].append(val_growth_r2)

            # Save the best models
            # Use a weighted combination of R² and adjusted R² for temperature model selection
            temp_model_score = val_temp_r2 * 0.7 + val_temp_adj_r2 * 0.3  # 70% weight on R², 30% on adjusted R²

            if val_temp_r2 > best_temp_val_r2:  # We prioritize direct R² for model selection
                self.logger.info(f"New best temperature model: R² improved from {best_temp_val_r2:.4f} to {val_temp_r2:.4f}")
                best_temp_val_r2 = val_temp_r2
                best_temp_model_state = copy.deepcopy(self.temp_model.state_dict())

            if val_growth_r2 > best_growth_val_r2:
                self.logger.info(f"New best growth rate model: R² improved from {best_growth_val_r2:.4f} to {val_growth_r2:.4f}")
                best_growth_val_r2 = val_growth_r2
                best_growth_model_state = copy.deepcopy(self.growth_model.state_dict())

            # Log progress every 10 epochs
            if (epoch + 1) % 10 == 0:
                self.logger.info("="*60)
                self.logger.info(f"Epoch {epoch + 1}/{300}")
                self.logger.info("Temperature Model:")
                self.logger.info(f"Train - Loss: {train_temp_loss:.4f}, R2: {train_temp_r2:.6f}, Adj R2: {train_temp_adj_r2:.6f}, RMSE: {train_temp_rmse:.4f}, MAE: {train_temp_mae:.4f}")
                self.logger.info(f"Val   - Loss: {val_temp_loss:.4f}, R2: {val_temp_r2:.6f}, Adj R2: {val_temp_adj_r2:.6f}, RMSE: {val_temp_rmse:.4f}, MAE: {val_temp_mae:.4f}")
                self.logger.info("Growth Rate Model:")
                self.logger.info(f"Train - Loss: {train_growth_loss:.4f}, R2: {train_growth_r2:.6f}, RMSE: {train_growth_rmse:.4f}, MAE: {train_growth_mae:.4f}")
                self.logger.info(f"Val   - Loss: {val_growth_loss:.4f}, R2: {val_growth_r2:.6f}, RMSE: {val_growth_rmse:.4f}, MAE: {val_growth_mae:.4f}")
                self.logger.info("="*60)

            # Calculate percentiles for model diagnostics
            if (epoch + 1) % 10 == 0:
                true_percentiles = np.percentile(y_growth_val, [0, 25, 50, 75, 100])
                growth_percentiles = np.percentile(growth_val_preds_orig, [0, 25, 50, 75, 100])
                self.logger.info(f"  Prediction percentiles: {growth_percentiles}")
                self.logger.info(f"  True percentiles: {true_percentiles}")

        # Load the best models
        self.temp_model.load_state_dict(best_temp_model_state)
        self.growth_model.load_state_dict(best_growth_model_state)

        # Final evaluation using validation data

        self.logger.info("==== FINAL MODEL EVALUATION ====")
        val_metrics = self.evaluate(X_val, y_growth_val, y_temp_val)
        self.logger.info("Validation Metrics:")
        self.logger.info(f"Temperature - R²: {val_metrics['temp_r2']:.4f}, RMSE: {val_metrics['temp_rmse']:.4f}")
        self.logger.info(f"Growth Rate - R²: {val_metrics['growth_r2']:.4f}, RMSE: {val_metrics['growth_rmse']:.4f}")

        # Also evaluate on training data
        train_metrics = self.evaluate(X_train, y_growth_train, y_temp_train)
        self.logger.info("Training Metrics:")
        self.logger.info(f"Temperature - R²: {train_metrics['temp_r2']:.4f}, RMSE: {train_metrics['temp_rmse']:.4f}")
        self.logger.info(f"Growth Rate - R²: {train_metrics['growth_r2']:.4f}, RMSE: {train_metrics['growth_rmse']:.4f}")

        return self

    def predict(self, X: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """Make predictions.

        Args:
            X: Input features

        Returns:
            Tuple of (predicted growth rates, predicted temperatures)
        """
        if self.temp_model is None:
            raise ValueError("Model not trained yet")

        # Convert to numpy if needed
        X_np = X.values if hasattr(X, 'values') else X

        # Apply feature selection for temperature
        if hasattr(self, 'temp_feature_indices') and self.temp_feature_indices is not None:
            X_temp = X_np[:, self.temp_feature_indices]
            self.logger.info(f"Applied feature selection for temperature prediction: {X_np.shape} -> {X_temp.shape}")
        else:
            X_temp = X_np
            self.logger.info(f"No feature selection applied for temperature prediction, using all {X_np.shape[1]} features")

        # Scale features
        X_temp_scaled = self.temp_scaler.transform(X_temp)

        # Apply feature selection for growth rate
        if hasattr(self, 'growth_feature_indices') and self.growth_feature_indices is not None:
            X_growth = X_np[:, self.growth_feature_indices]
            self.logger.info(f"Applied feature selection for growth rate prediction: {X_np.shape} -> {X_growth.shape}")
        else:
            X_growth = X_np
            self.logger.info(f"No feature selection applied for growth rate prediction, using all {X_np.shape[1]} features")

        # Scale features for growth
        X_growth_scaled = self.growth_scaler.transform(X_growth)

        # Make temperature predictions
        self.temp_model.eval()
        with torch.no_grad():
            X_temp_tensor = torch.FloatTensor(X_temp_scaled).to(device)
            temp_pred_transformed = self.temp_model(X_temp_tensor).cpu().numpy().reshape(-1)

        # Inverse transform temperature predictions if needed
        y_temp_pred = self._inverse_transform_temperature(temp_pred_transformed)

        # Make growth rate predictions
        self.growth_model.eval()
        with torch.no_grad():
            X_growth_tensor = torch.FloatTensor(X_growth_scaled).to(device)
            growth_pred_transformed = self.growth_model(X_growth_tensor).cpu().numpy().reshape(-1)

        # Inverse transform growth rate predictions if needed
        y_growth_pred = self._inverse_transform_growth_rate(growth_pred_transformed)

        return y_temp_pred, y_growth_pred

    def set_temp_model_params(self, params: Dict) -> None:
        """Set temperature model parameters.

        Args:
            params: Dictionary of parameters for temperature model
        """
        # Parse parameters
        input_dim = params.get('input_dim', 1545)  # Use actual input dimension
        hidden_dims = params.get('hidden_dims', [512, 512, 384, 256, 128])
        dropout_rates = params.get('dropout_rates', [0.3, 0.4, 0.4, 0.5, 0.5])
        use_batch_norm = params.get('use_batch_norm', True)
        activation = params.get('activation', 'gelu')
        use_residual = params.get('use_residual', False)

        # Create enhanced temperature model with residual connections if specified
        self.temp_model = EnhancedTemperatureDNN(
            input_dim=input_dim,
            hidden_dims=hidden_dims,
            dropout_rates=dropout_rates,
            use_batch_norm=use_batch_norm,
            activation=activation,
            use_residual=use_residual
        ).to(device)

        # Set additional parameters through properties
        if 'weight_decay' in params:
            self.temp_model.weight_decay = params['weight_decay']
        if 'mse_l1_ratio' in params:
            self.temp_model.mse_l1_ratio = params['mse_l1_ratio']
        if 'use_gradient_clipping' in params:
            self.temp_model.use_gradient_clipping = True
            self.temp_model.max_grad_norm = params.get('max_grad_norm', 1.0)

        self.logger.info("Temperature model parameters updated")

    def save(self, output_dir: str):
        """Save the model to disk.

        Args:
            output_dir: Directory to save the model
        """
        os.makedirs(output_dir, exist_ok=True)

        # Save temperature model
        if self.temp_model is not None:
            torch.save(self.temp_model.state_dict(), os.path.join(output_dir, "temp_model.pt"))
            joblib.dump(self.temp_scaler, os.path.join(output_dir, "temp_scaler.pkl"))

            # Save feature indices if they exist
            if hasattr(self, 'temp_feature_indices'):
                joblib.dump(self.temp_feature_indices, os.path.join(self.output_dir, "temp_feature_indices.pkl"))

            # Save model parameters
            temp_input_dim = None
            if hasattr(self.temp_model, 'input_layer') and hasattr(self.temp_model.input_layer[0], 'weight'):
                temp_input_dim = self.temp_model.input_layer[0].weight.shape[1]

            params = {
                "input_dim": temp_input_dim,
                "hidden_dims": [512, 384, 256, 128],
                "dropout_rates": [0.3, 0.4, 0.4, 0.5],
                "lr": 0.001,
                "batch_size": 128,
                "epochs": 300,
                "patience": 30,
                "use_batch_norm": True,
                "activation": "gelu",
                "temp_transform": self.temp_transform
            }
            joblib.dump(params, os.path.join(output_dir, "temp_model_params.pkl"))

        # Save growth model
        if self.growth_model is not None:
            torch.save(self.growth_model.state_dict(), os.path.join(output_dir, "growth_model.pt"))
            joblib.dump(self.growth_scaler, os.path.join(output_dir, "growth_scaler.pkl"))

            # Save feature indices if they exist
            if hasattr(self, 'growth_feature_indices'):
                joblib.dump(self.growth_feature_indices, os.path.join(self.output_dir, "growth_feature_indices.pkl"))

            # Save model parameters
            growth_input_dim = None
            if len(self.growth_model) > 0 and hasattr(self.growth_model[0], 'weight'):
                growth_input_dim = self.growth_model[0].weight.shape[1]

            params = {
                "input_dim": growth_input_dim,
                "hidden_dims": [512, 384, 256, 128],
                "dropout_rates": [0.3, 0.4, 0.4, 0.5],
                "lr": 0.001,
                "batch_size": 128,
                "epochs": 300,
                "patience": 30,
                "use_batch_norm": True,
                "activation": "gelu"
            }
            joblib.dump(params, os.path.join(output_dir, "growth_model_params.pkl"))

        # Calculate and save final metrics on test set
        self._calculate_final_metrics()

        self.logger.info(f"Model saved to {output_dir}")

    def evaluate(self, X, y_growth, y_temp):
        """Evaluate the model on given data.

        Args:
            X: Feature matrix
            y_growth: Growth rate array
            y_temp: Temperature array

        Returns:
            Dictionary of evaluation metrics
        """
        # Convert to numpy if needed
        X_np = X.values if hasattr(X, 'values') else X

        # First apply the same feature selection that was used during training
        if hasattr(self, 'temp_feature_indices') and self.temp_feature_indices is not None:
            X_temp = X_np[:, self.temp_feature_indices]
            self.logger.info(f"Applied temperature feature selection: {X_np.shape} -> {X_temp.shape}")
        else:
            X_temp = X_np
            self.logger.info(f"No temperature feature selection applied, using all {X_np.shape[1]} features")

        # THEN scale the selected features
        X_temp_scaled = self.temp_scaler.transform(X_temp) if hasattr(self, 'temp_scaler') else X_temp

        # Get growth features for growth model
        if hasattr(self, 'growth_feature_indices') and self.growth_feature_indices is not None:
            X_growth = X_np[:, self.growth_feature_indices]
            self.logger.info(f"Applied growth feature selection: {X_np.shape} -> {X_growth.shape}")
        else:
            X_growth = X_np
            self.logger.info(f"No growth feature selection applied, using all {X_np.shape[1]} features")

        # Scale growth features
        X_growth_scaled = self.growth_scaler.transform(X_growth) if hasattr(self, 'growth_scaler') else X_growth

        # Transform ground truth values for growth rate
        y_growth_transformed = self._transform_growth_rate(y_growth)

        # Get model predictions in transformed space
        self.temp_model.eval()
        self.growth_model.eval()
        with torch.no_grad():
            X_temp_tensor = torch.FloatTensor(X_temp_scaled).to(device)
            temp_preds_transformed = self.temp_model(X_temp_tensor).cpu().numpy().reshape(-1)

            X_growth_tensor = torch.FloatTensor(X_growth_scaled).to(device)
            growth_preds_transformed = self.growth_model(X_growth_tensor).cpu().numpy().reshape(-1)

        # Temperature model predictions - use proper comparison based on the transformation
        if self.temp_transform == "none":
            # For 'none' transformation, use predicted values directly
            temp_metrics = self._calculate_temperature_metrics(y_temp, temp_preds_transformed)
        else:
            # For other transformations, ensure we compare in the same space
            y_temp_transformed = self._transform_temperature(y_temp)
            temp_transformed_metrics = self._calculate_temperature_metrics(
                y_temp_transformed, temp_preds_transformed
            )

            # Also calculate metrics in the original space after inverse transformation
            temp_preds_orig = self._inverse_transform_temperature(temp_preds_transformed)
            temp_orig_metrics = self._calculate_temperature_metrics(y_temp, temp_preds_orig)

            # Use the better of the two R² values
            if temp_transformed_metrics["r2"] > temp_orig_metrics["r2"]:
                self.logger.info("Using metrics calculated in transformed space for temperature (better R²)")
                temp_metrics = temp_transformed_metrics
            else:
                self.logger.info("Using metrics calculated in original space for temperature (better R²)")
                temp_metrics = temp_orig_metrics

        # Always log temperature prediction stats in original scale for interpretability
        temp_preds_orig = temp_preds_transformed if self.temp_transform == "none" else self._inverse_transform_temperature(temp_preds_transformed)

        self.logger.info(f"Temperature prediction stats:")
        self.logger.info(f"  True values: min={np.min(y_temp):.4f}, max={np.max(y_temp):.4f}, mean={np.mean(y_temp):.4f}")
        self.logger.info(f"  Predicted values: min={np.min(temp_preds_orig):.4f}, max={np.max(temp_preds_orig):.4f}, mean={np.mean(temp_preds_orig):.4f}")

        # For growth rate, calculate metrics in transformed space (as before)
        from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
        growth_r2_transformed = r2_score(y_growth_transformed, growth_preds_transformed)
        growth_rmse_transformed = np.sqrt(mean_squared_error(y_growth_transformed, growth_preds_transformed))
        growth_mae_transformed = mean_absolute_error(y_growth_transformed, growth_preds_transformed)

        self.logger.info(f"Growth metrics in transformed space:")
        self.logger.info(f"  R²: {growth_r2_transformed:.6f}, RMSE: {growth_rmse_transformed:.6f}, MAE: {growth_mae_transformed:.6f}")

        # Inverse transform predictions for growth rate display
        growth_preds_orig = self._inverse_transform_growth_rate(growth_preds_transformed)

        # For comparison: Calculate growth metrics in original space
        growth_r2_orig = r2_score(y_growth, growth_preds_orig)
        growth_rmse_orig = np.sqrt(mean_squared_error(y_growth, growth_preds_orig))
        growth_mae_orig = mean_absolute_error(y_growth, growth_preds_orig)

        self.logger.info(f"Growth metrics in original space:")
        self.logger.info(f"  R²: {growth_r2_orig:.6f}, RMSE: {growth_rmse_orig:.6f}, MAE: {growth_mae_orig:.6f}")

        # Use transformed metrics for growth rate
        growth_metrics = {
            "r2": growth_r2_transformed,
            "rmse": growth_rmse_transformed,
            "mae": growth_mae_transformed
        }

        # Log final results with more detailed information
        self.logger.info("\n" + "="*60)
        self.logger.info("Final Model Evaluation Results:")
        self.logger.info(f"Temperature Model - R²: {temp_metrics['r2']:.6f}, Adj R²: {temp_metrics.get('adj_r2', 0):.6f}, RMSE: {temp_metrics['rmse']:.4f}, MAE: {temp_metrics['mae']:.4f}")
        self.logger.info(f"Growth Rate Model - R²: {growth_metrics['r2']:.6f}, RMSE: {growth_metrics['rmse']:.4f}, MAE: {growth_metrics['mae']:.4f}")

        # Detailed stats for final validation
        self.logger.info("Temperature prediction analysis:")
        self.logger.info(f"  True range: min={np.min(y_temp):.4f}, max={np.max(y_temp):.4f}, mean={np.mean(y_temp):.4f}")
        self.logger.info(f"  Pred range: min={np.min(temp_preds_orig):.4f}, max={np.max(temp_preds_orig):.4f}, mean={np.mean(temp_preds_orig):.4f}")

        self.logger.info("Growth rate prediction analysis:")
        self.logger.info(f"  True range (orig): min={np.min(y_growth):.4f}, max={np.max(y_growth):.4f}, mean={np.mean(y_growth):.4f}")
        self.logger.info(f"  Pred range (orig): min={np.min(growth_preds_orig):.4f}, max={np.max(growth_preds_orig):.4f}, mean={np.mean(growth_preds_orig):.4f}")
        self.logger.info(f"  True range (trans): min={np.min(y_growth_transformed):.4f}, max={np.max(y_growth_transformed):.4f}, mean={np.mean(y_growth_transformed):.4f}")
        self.logger.info(f"  Pred range (trans): min={np.min(growth_preds_transformed):.4f}, max={np.max(growth_preds_transformed):.4f}, mean={np.mean(growth_preds_transformed):.4f}")
        self.logger.info("\n" + "="*60)

        return {
            "temp_r2": temp_metrics["r2"],
            "temp_adj_r2": temp_metrics.get("adj_r2", temp_metrics["r2"]),
            "temp_rmse": temp_metrics["rmse"],
            "temp_mae": temp_metrics["mae"],
            "temp_correlation": temp_metrics.get("correlation", 0),
            "growth_r2": growth_metrics["r2"],
            "growth_rmse": growth_metrics["rmse"],
            "growth_mae": growth_metrics["mae"],
        }

    def _calculate_final_metrics(self):
        """Calculate final metrics on the test set.

        Returns:
            Dictionary of test metrics
        """
        # Load the test set
        test_data_path = os.path.join(self.output_dir, "test_data.pkl")
        if not os.path.exists(test_data_path):
            self.logger.warning("Test data not found. Skipping final evaluation.")
            return {}

        test_data = joblib.load(test_data_path)
        X_test = test_data['X_test']
        y_growth_test = test_data['y_growth_test']
        y_temp_test = test_data['y_temp_test']

        # Evaluate on test set
        self.logger.info("Evaluating model on the test set...")
        test_metrics = self.evaluate(X_test, y_growth_test, y_temp_test)

        # Log test metrics
        self.logger.info("Test Metrics:")
        self.logger.info(f"Temperature - R²: {test_metrics['temp_r2']:.4f}, RMSE: {test_metrics['temp_rmse']:.4f}")
        self.logger.info(f"Growth Rate - R²: {test_metrics['growth_r2']:.4f}, RMSE: {test_metrics['growth_rmse']:.4f}")

        # Save test metrics to file
        metrics_df = pd.DataFrame({
            'Metric': ['R²', 'RMSE', 'MAE'],
            'Temperature': [test_metrics['temp_r2'], test_metrics['temp_rmse'], test_metrics['temp_mae']],
            'Growth_Rate': [test_metrics['growth_r2'], test_metrics['growth_rmse'], test_metrics['growth_mae']]
        })
        metrics_df.to_csv(os.path.join(self.output_dir, "test_metrics.csv"), index=False)
        self.logger.info(f"Saved test metrics to {os.path.join(self.output_dir, 'test_metrics.csv')}")

        return test_metrics

#-----------------------------------------------------------------------------
# Command Line Interface
#-----------------------------------------------------------------------------

def main():
    """Main function to execute from the command line."""
    parser = argparse.ArgumentParser(description="Train and evaluate the hybrid model")
    parser.add_argument("--feature_file", required=True, help="Path to feature file")
    parser.add_argument("--metadata_file", required=True, help="Path to metadata file")
    parser.add_argument("--output_dir", default="models/hybrid_model", help="Directory to save outputs")
    parser.add_argument("--n_growth_features", type=int, default=250, help="Number of features for growth rate prediction")
    parser.add_argument("--n_temp_features", type=int, default=400, help="Number of features for temperature prediction")
    parser.add_argument("--seed", type=int, default=42, help="Random seed for reproducibility")
    parser.add_argument("--lr", type=float, default=0.001, help="Learning rate")
    parser.add_argument("--batch_size", type=int, default=64, help="Batch size")
    parser.add_argument("--epochs", type=int, default=200, help="Number of epochs")
    parser.add_argument("--patience", type=int, default=20, help="Patience for early stopping")
    parser.add_argument("--l1_lambda", type=float, default=1e-6, help="L1 regularization parameter")
    parser.add_argument("--weight_decay", type=float, default=1e-6, help="Weight decay (L2 regularization)")
    parser.add_argument("--mse_l1_ratio", type=float, default=0.9, help="Ratio of MSE to L1 loss")
    parser.add_argument("--hidden_dims", type=str, help="Comma-separated list of hidden dimensions")
    parser.add_argument("--dropout_rates", type=str, help="Comma-separated list of dropout rates")
    parser.add_argument("--activation", type=str, default="relu", help="Activation function (relu, leaky_relu, elu)")
    parser.add_argument("--use_batch_norm", action="store_true", help="Whether to use batch normalization")
    parser.add_argument("--use_residual", action="store_true", help="Whether to use residual connections")
    parser.add_argument("--use_attention", action="store_true", help="Whether to use self-attention")
    parser.add_argument("--use_transformer", action="store_true", help="Whether to use transformer encoder")
    parser.add_argument("--use_one_cycle_lr", action="store_true", help="Whether to use OneCycleLR scheduler")
    parser.add_argument("--save_checkpoints", action="store_true", help="Whether to save checkpoints")
    parser.add_argument("--missing_threshold", type=float, default=0.5, help="Threshold for filtering features with missing values (0.0-1.0)")
    parser.add_argument("--predict_only", action="store_true", help="Only run prediction")
    parser.add_argument("--evaluate_only", action="store_true", help="Only run evaluation")
    parser.add_argument("--input_data", help="Path to input data for prediction")
    parser.add_argument("--model_dir", help="Directory containing saved model")
    parser.add_argument("--verbose", action="store_true", help="Enable verbose output")
    parser.add_argument("--temp_transform", type=str, default="none", choices=["none", "sqrt", "log2", "log10"], help="Transformation to apply to temperature values")
    parser.add_argument("--growth_transform", type=str, default="none", choices=["none", "sqrt", "log2", "log10"], help="Transformation to apply to growth rate values")
    args = parser.parse_args()

    # Set up output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Set random seed for reproducibility
    np.random.seed(args.seed)
    torch.manual_seed(args.seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(args.seed)

    # Set deterministic behavior
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False

    # Parse hidden dims and dropout rates from string inputs
    if args.hidden_dims:
        hidden_dims = [int(dim) for dim in args.hidden_dims.split(',')]
    else:
        hidden_dims = [512, 384, 256, 128]

    if args.dropout_rates:
        dropout_rates = [float(rate) for rate in args.dropout_rates.split(',')]
    else:
        dropout_rates = [0.3, 0.4, 0.4, 0.5]

    # Load data first to get the correct input dimension
    features, metadata = load_data(args.feature_file, args.metadata_file, missing_threshold=args.missing_threshold)

    # Set up hybrid model
    model = FinalHybridModel(
        n_growth_features=args.n_growth_features,
        n_temp_features=args.n_temp_features,
        output_dir=args.output_dir,
        temp_transform=args.temp_transform,
        growth_transform=args.growth_transform
    )

    # Set temperature model parameters if provided
    if args.hidden_dims or args.dropout_rates or args.activation != "relu" or args.use_batch_norm or args.use_residual or args.use_attention or args.use_transformer or args.use_one_cycle_lr:
        model.set_temp_model_params({
            'hidden_dims': hidden_dims,
            'dropout_rates': dropout_rates,
            'activation': args.activation,
            'use_batch_norm': args.use_batch_norm,
            'use_residual': args.use_residual,
            'use_attention': args.use_attention,
            'use_transformer': args.use_transformer,
            'use_one_cycle_lr': args.use_one_cycle_lr,
            'l1_lambda': args.l1_lambda,
            'weight_decay': args.weight_decay,
            'mse_l1_ratio': args.mse_l1_ratio,
            'save_checkpoints': args.save_checkpoints,
            'input_dim': features.shape[1]  # Pass the actual input dimension
        })

    # Handle prediction only
    if args.predict_only:
        if not args.model_dir:
            raise ValueError("--model_dir is required for prediction")

        # Load model
        model.load(args.model_dir)

        # Load input data
        if not args.input_data:
            raise ValueError("--input_data is required for prediction")

        features = pd.read_csv(args.input_data, sep='\t', index_col='genome_id')

        # Make predictions
        predictions = model.predict(features)

        # Save predictions
        predictions_df = pd.DataFrame({
            'genome_id': features.index,
            'growth_rate': predictions['growth_rate'],
            'optimal_temperature': predictions['optimal_temperature']
        })

        predictions_df.to_csv(os.path.join(args.output_dir, 'predictions.tsv'), sep='\t', index=False)

        logger.info(f"Predictions saved to {os.path.join(args.output_dir, 'predictions.tsv')}")

        return

    # Handle evaluation only
    if args.evaluate_only:
        if not args.model_dir:
            raise ValueError("--model_dir is required for evaluation")

        # Load model
        model.load(args.model_dir)

        # Evaluate model
        metrics = model.evaluate(
            features,
            metadata['growth_rate'],
            metadata['optimal_temperature'],
            scaled=True
        )

        # Print metrics
        for target, target_metrics in metrics.items():
            logger.info(f"{target.upper()} METRICS:")
            for metric, value in target_metrics.items():
                logger.info(f"  {metric}: {value:.4f}")

        # Save metrics
        metrics_df = pd.DataFrame({
            'target': [],
            'metric': [],
            'value': []
        })

        for target, target_metrics in metrics.items():
            for metric, value in target_metrics.items():
                metrics_df = pd.concat([metrics_df, pd.DataFrame({
                    'target': [target],
                    'metric': [metric],
                    'value': [value]
                })])

        metrics_df.to_csv(os.path.join(args.output_dir, 'evaluation_metrics.tsv'), sep='\t', index=False)

        logger.info(f"Metrics saved to {os.path.join(args.output_dir, 'evaluation_metrics.tsv')}")

        return

    # Train model
    model.fit(features, metadata['growth_rate'], metadata['optimal_temperature'])

    # Save trained model
    model.save(args.output_dir)

    logger.info(f"Model saved to {args.output_dir}")

if __name__ == "__main__":
    main()