#!/bin/bash

# DeepMu: Training Script with Balanced Regularization
# This script uses the existing final_hybrid_model.py but with balanced regularization
# to prevent overfitting without sacrificing performance.

# Set a clean output directory
OUTPUT_DIR="models/deepmu_balanced_regularization"

# Create output directory
mkdir -p "$OUTPUT_DIR"

# Define color codes for output formatting
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Display initial message
echo -e "${BLUE}${BOLD}========================================================${NC}"
echo -e "${BLUE}${BOLD}   DeepMu - Balanced Regularization Model   ${NC}"
echo -e "${BLUE}${BOLD}========================================================${NC}"
echo -e "${GREEN}This script uses balanced regularization techniques:${NC}"
echo -e "  1. ${YELLOW}Moderate dropout rates (0.2-0.4)${NC}"
echo -e "  2. ${YELLOW}Optimal model complexity${NC}"
echo -e "  3. ${YELLOW}Moderate L2 regularization${NC}"
echo -e "  4. ${YELLOW}One-cycle learning rate scheduling${NC}"
echo -e "  5. ${YELLOW}Batch normalization${NC}"
echo -e ""
echo -e "${YELLOW}Output will be saved to:${NC} ${OUTPUT_DIR}"
echo ""

# Check if prerequisites are available
echo -e "${YELLOW}Checking prerequisites...${NC}"
if [ ! -f "./training_data/combined_features.tsv" ]; then
    echo -e "${RED}Error: Feature file not found. Please ensure './training_data/combined_features.tsv' exists.${NC}"
    exit 1
fi

if [ ! -f "./training_data/metadata.tsv" ]; then
    echo -e "${RED}Error: Metadata file not found. Please ensure './training_data/metadata.tsv' exists.${NC}"
    exit 1
fi

echo -e "${GREEN}All prerequisites found.${NC}"
echo ""

# Step 1: Filter the dataset to remove unreliable temperature data
echo -e "${YELLOW}Step 1: Filtering dataset to remove unreliable temperature data...${NC}"
python improved_temp_data_filter.py \
    --feature-file "./training_data/combined_features.tsv" \
    --metadata-file "./training_data/metadata.tsv" \
    --output-dir "$OUTPUT_DIR/filtered_data" \
    --reliability-threshold 0.6 \
    --prediction-error-threshold 2.0

# Check if filtering was successful
if [ $? -eq 0 ]; then
    echo -e "${GREEN}Dataset filtering completed. Using filtered datasets for training.${NC}"
    FEATURES_FILE="${OUTPUT_DIR}/filtered_data/filtered_features.tsv"
    METADATA_FILE="${OUTPUT_DIR}/filtered_data/filtered_metadata.tsv"
else
    echo -e "${RED}Error: Dataset filtering failed. Exiting.${NC}"
    exit 1
fi

# Set parameters for training with balanced regularization
# Optimal model complexity - not too deep, not too shallow
HIDDEN_DIMS="640,512,384,256,192"  # Balanced architecture
DROPOUT_RATES="0.2,0.25,0.3,0.35,0.4"  # Moderate dropout rates
MAX_EPOCHS=600  # Sufficient epochs for convergence
PATIENCE=60  # Moderate patience for early stopping
BATCH_SIZE=48  # Balanced batch size
N_GROWTH_FEATURES=150  # Optimal features for growth rate
N_TEMP_FEATURES=1000  # Optimal features for temperature
L1_LAMBDA="2e-6"  # Mild L1 regularization
WEIGHT_DECAY="2e-5"  # Moderate weight decay (L2 regularization)
LEARNING_RATE="0.0008"  # Balanced learning rate
# Gradient clipping is enabled with --use_gradient_clipping flag

# Train with fixed transformations: none for temperature, log2 for growth rate
echo -e "\n${BLUE}${BOLD}========================================================${NC}"
echo -e "${BLUE}${BOLD}   Training Final Model with Balanced Regularization   ${NC}"
echo -e "${BLUE}${BOLD}   Temperature: none, Growth Rate: log2   ${NC}"
echo -e "${BLUE}${BOLD}========================================================${NC}"

# Set environment variables for deterministic behavior
export PYTHONHASHSEED=42
export TORCH_CUDNN_DETERMINISTIC=1
export TORCH_DETERMINISTIC=1
export CUBLAS_WORKSPACE_CONFIG=:4096:8

# Run the Python script with optimized parameters
python final_hybrid_model.py \
    --feature_file "$FEATURES_FILE" \
    --metadata_file "$METADATA_FILE" \
    --output_dir "$OUTPUT_DIR" \
    --n_growth_features $N_GROWTH_FEATURES \
    --n_temp_features $N_TEMP_FEATURES \
    --seed 42 \
    --lr $LEARNING_RATE \
    --batch_size $BATCH_SIZE \
    --patience $PATIENCE \
    --epochs $MAX_EPOCHS \
    --l1_lambda $L1_LAMBDA \
    --weight_decay $WEIGHT_DECAY \
    --mse_l1_ratio 0.95 \
    --hidden_dims "$HIDDEN_DIMS" \
    --dropout_rates "$DROPOUT_RATES" \
    --activation "leaky_relu" \
    --use_batch_norm \
    --use_residual \
    --use_attention \
    --use_one_cycle_lr \
    --save_checkpoints \
    --missing_threshold 0.5 \
    --temp_transform "none" \
    --growth_transform "log2" \
    --verbose 2>&1 | tee "${OUTPUT_DIR}/training.log"

# Check if training was successful
if [ $? -eq 0 ]; then
    echo ""
    echo -e "${GREEN}${BOLD}========================================================${NC}"
    echo -e "${GREEN}${BOLD}   Training completed successfully!   ${NC}"
    echo -e "${GREEN}${BOLD}========================================================${NC}"
    echo ""
    echo -e "${GREEN}Model saved to:${NC} ${OUTPUT_DIR}"

else
    echo ""
    echo -e "${RED}${BOLD}========================================================${NC}"
    echo -e "${RED}${BOLD}   Training failed!   ${NC}"
    echo -e "${RED}${BOLD}========================================================${NC}"
    echo ""
    echo -e "${RED}Please check the error messages above for details.${NC}"
fi

# Step 3: Evaluate the model on the test set
echo -e "${YELLOW}Step 3: Evaluating model on the test set...${NC}"

# Extract test metrics from the log file
echo -e "${GREEN}Test Metrics:${NC}"
grep "Test Metrics:" -A 3 "${OUTPUT_DIR}/training.log"

echo ""
echo -e "${GREEN}Done.${NC}"
