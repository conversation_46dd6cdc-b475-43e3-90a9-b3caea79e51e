#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
DeepMu 命令行接口

这个模块提供了 DeepMu 的命令行接口，允许用户从基因组数据预测微生物生长速率和最适生长温度。

主要功能：
1. 基因组预处理 - 基因预测和功能注释
2. 特征提取 - 从基因组数据提取多种类型的特征
3. 单个基因组预测 - 预测单个微生物的生长参数
4. 批量预测 - 批量处理多个基因组
5. 群落预测 - 基于丰度数据的群落水平预测

使用示例：
    # 预处理基因组
    python deepmu_cli.py preprocess genome.fna -o output_dir

    # 提取特征
    python deepmu_cli.py feature genome_dir -o features_dir

    # 单个预测
    python deepmu_cli.py predict features.tsv -o results.json

    # 群落预测
    python deepmu_cli.py community MAGs_dir --abundance abundance.tsv

作者：DeepMu 开发团队
版本：1.1.1
"""

import argparse
import sys
import os
import logging
from pathlib import Path
from typing import Optional

# 仅导入特征提取命令所需的模块
from deepmu.cli.feature import setup_feature_parser

# 其他模块按需导入以避免循环导入
def import_preprocess():
    """按需导入预处理模块"""
    from deepmu.utils.preprocess import preprocess_genome
    return preprocess_genome

def get_predictor():
    """按需导入预测器模块"""
    from deepmu.predictors import MicrobialGrowthPredictor
    return MicrobialGrowthPredictor

def setup_logging(log_level: str = 'INFO'):
    """
    设置日志配置

    Args:
        log_level (str): 日志级别 ('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL')
    """
    level = getattr(logging, log_level.upper())
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

def single_genome_command(args):
    """
    处理单个微生物预测命令（使用预处理的特征数据）

    这个函数处理单个基因组的生长速率和最适温度预测。它接受预处理的特征文件，
    加载训练好的模型，并输出预测结果。

    Args:
        args: 命令行参数对象，包含以下属性：
            - feature_file: 特征文件路径
            - output_dir: 输出目录路径
            - verbose: 是否启用详细日志
            - model_path: 模型文件路径（可选）
            - temperature: 预测温度（可选）

    输出：
        在指定目录生成预测结果文件（JSON格式）
    """
    # 设置日志级别
    setup_logging('DEBUG' if args.verbose else 'INFO')
    logger = logging.getLogger(__name__)

    logger.info("开始单个基因组预测任务")

    # 创建输出目录（如果不存在）
    output_dir = Path(args.output_dir) if args.output_dir else Path.cwd() / 'results'
    output_dir.mkdir(parents=True, exist_ok=True)
    logger.info(f"输出目录设置为: {output_dir}")

    try:
        # 验证输入文件
        if not os.path.exists(args.feature_file):
            raise FileNotFoundError(f"特征文件未找到: {args.feature_file}")

        logger.info(f"加载特征文件: {args.feature_file}")

        # 使用模型路径初始化预测器（启用多任务学习）
        model_path = args.model_path if args.model_path else os.path.join('models', 'deepmu_final_model', 'growth_model.pt')
        MicrobialGrowthPredictor = get_predictor()

        # 检查是否启用多任务预测
        multi_task_mode = hasattr(args, 'multi_task') and args.multi_task

        predictor = MicrobialGrowthPredictor(
            model_path=model_path,
            multi_task=multi_task_mode or not args.no_temp_predict  # 如果需要温度预测则启用多任务
        )

        # 执行预测
        logger.info("开始执行预测...")

        # 检查是否使用遗留模型
        if hasattr(predictor, 'use_legacy_model') and predictor.use_legacy_model:
            logger.info("检测到遗留模型，使用特征预测模式")

            # 读取特征文件
            import pandas as pd
            import numpy as np
            try:
                if args.feature_file.endswith('.csv'):
                    features_df = pd.read_csv(args.feature_file)
                else:
                    features_df = pd.read_csv(args.feature_file, sep='\t')

                # 获取特征数组
                features = features_df.values
                logger.info(f"加载特征数组，形状: {features.shape}")

                # 检查数据类型
                if features.dtype == object:
                    logger.info("检测到非数值数据，进行数据清理...")
                    # 转换为数值类型
                    for col in range(features.shape[1]):
                        features_df.iloc[:, col] = pd.to_numeric(features_df.iloc[:, col], errors='coerce')

                    # 填充NaN值
                    features_df = features_df.fillna(0.0)
                    features = features_df.values.astype(np.float32)
                    logger.info(f"数据清理后形状: {features.shape}, 类型: {features.dtype}")

                # 使用遗留模型进行预测
                results = predictor.predict_from_features(features)

                # 为兼容性添加额外信息
                results['temperature'] = args.temp
                results['model_type'] = 'legacy'
                results['multi_task_requested'] = multi_task_mode

                if multi_task_mode:
                    logger.warning("遗留模型不支持温度预测，仅提供生长速率预测")
                    results['optimal_temperature'] = None
                    results['temperature_prediction_available'] = False
                else:
                    results['temperature_prediction_available'] = False

            except Exception as e:
                logger.error(f"特征文件处理失败: {e}")
                raise
        else:
            # 使用新模型的预测方法
            if multi_task_mode:
                logger.info("使用多任务预测模式（同时预测生长速率和温度）")
                results = predictor.predict_multi_task(
                    cds_fasta_path=None,  # 使用特征文件时不需要
                    ko_file=None,         # 使用特征文件时不需要
                    temperature=args.temp,
                    analyze_features=hasattr(args, 'analyze_features') and args.analyze_features
                )
            else:
                results = predictor.predict_from_features(
                    feature_file=args.feature_file,
                temperature=args.temp,
                predict_temp=not args.no_temp_predict
            )

        # 显示预测结果
        print("\n" + "="*50)
        print("DeepMu 预测结果")
        print("="*50)
        print(f"生长速率: {results['growth_rate']:.4f} h⁻¹")

        if not args.no_temp_predict and 'optimal_temperature' in results:
            if results['optimal_temperature'] is not None:
                print(f"最适生长温度: {results['optimal_temperature']:.2f}°C")
            else:
                print("最适生长温度: 不可用 (遗留模型不支持温度预测)")

            # 如果是多任务预测，显示额外信息
            if multi_task_mode and 'prediction_confidence' in results:
                conf = results['prediction_confidence']
                print(f"生长速率置信区间: [{conf['growth_rate_range'][0]:.4f}, {conf['growth_rate_range'][1]:.4f}] h⁻¹")
                print(f"温度置信区间: [{conf['temperature_range'][0]:.2f}, {conf['temperature_range'][1]:.2f}]°C")

        # 显示模型信息
        if 'model_info' in results:
            model_info = results['model_info']
            print(f"\n模型版本: {model_info.get('model_version', 'Unknown')}")
            print(f"预测模式: {model_info.get('prediction_mode', 'Unknown')}")

        print("="*50)

        # Save results to output file if specified
        if args.output:
            import json
            with open(args.output, 'w') as f:
                json.dump(results, f, indent=2)
            print(f"\nResults saved to: {args.output}")

    except Exception as e:
        logging.error(f"Error: {str(e)}")
        sys.exit(1)

def community_command(args):
    """Handle community-level prediction from MAGs with abundance filtering."""
    # Set up logging
    setup_logging('DEBUG' if args.verbose else 'INFO')

    # Create output directory if it doesn't exist
    output_dir = Path(args.output_dir) if args.output_dir else Path.cwd() / 'results'
    output_dir.mkdir(parents=True, exist_ok=True)

    try:
        # Validate input files
        input_dir = Path(args.input_dir)
        if not input_dir.exists():
            raise FileNotFoundError(f"Input directory not found: {input_dir}")
        
        if not os.path.exists(args.abundance):
            raise FileNotFoundError(f"Abundance file not found: {args.abundance}")

        # Load abundance data
        import pandas as pd
        abundance_df = pd.read_csv(args.abundance, sep='\t', header=None, names=['mag_id', 'abundance'])
        abundance_df['abundance'] = abundance_df['abundance'] / abundance_df['abundance'].sum()  # Normalize to relative abundances
        
        # Filter MAGs by abundance threshold
        filtered_mags = abundance_df[abundance_df['abundance'] >= args.min_abundance]
        logging.info(f"Found {len(filtered_mags)} MAGs above abundance threshold {args.min_abundance}")
        
        if len(filtered_mags) == 0:
            raise ValueError(f"No MAGs found above abundance threshold {args.min_abundance}")

        # Initialize predictor
        model_path = args.model_path if args.model_path else os.path.join('models', 'deepmu_final_model', 'growth_model.pt')
        MicrobialGrowthPredictor = get_predictor()
        predictor = MicrobialGrowthPredictor(
            model_path=model_path
        )

        # Process each MAG
        results = {
            'community_growth_rate': 0.0,
            'organism_growth_rates': {},
            'community_composition': {},
            'filtered_mags': []
        }

        total_abundance = 0.0
        for _, row in filtered_mags.iterrows():
            mag_id = row['mag_id']
            abundance = row['abundance']
            
            # Find corresponding MAG file
            mag_file = None
            for ext in ['.fa', '.fna', '.fasta']:
                potential_file = input_dir / f"{mag_id}{ext}"
                if potential_file.exists():
                    mag_file = potential_file
                    break
            
            if mag_file is None:
                logging.warning(f"No FASTA file found for MAG {mag_id}, skipping...")
                continue

            try:
                # Extract features for the MAG
                from deepmu.feature_extraction import extract_features
                features = extract_features(
                    cds_file=str(mag_file),
                    genome_id=mag_id
                )

                # Make prediction
                mag_prediction = predictor.predict_from_features(features)
                
                # Update results
                results['organism_growth_rates'][mag_id] = mag_prediction['growth_rate']
                results['community_composition'][mag_id] = abundance
                results['community_growth_rate'] += mag_prediction['growth_rate'] * abundance
                total_abundance += abundance
                
                if 'optimal_temperature' in mag_prediction:
                    if 'optimal_temperature' not in results:
                        results['optimal_temperature'] = 0.0
                    results['optimal_temperature'] += mag_prediction['optimal_temperature'] * abundance

            except Exception as e:
                logging.error(f"Error processing MAG {mag_id}: {str(e)}")
                continue

        # Normalize community growth rate by total abundance
        if total_abundance > 0:
            results['community_growth_rate'] /= total_abundance
            if 'optimal_temperature' in results:
                results['optimal_temperature'] /= total_abundance

        # Print results
        print("\nCommunity Prediction Results:")
        print(f"Total MAGs analyzed: {len(results['organism_growth_rates'])}")
        print(f"Community Growth Rate: {results['community_growth_rate']:.2f} h⁻¹")
        if 'optimal_temperature' in results:
            print(f"Community Optimal Temperature: {results['optimal_temperature']:.1f}°C")

        print("\nMAG-specific Growth Rates:")
        for mag_id, growth_rate in results['organism_growth_rates'].items():
            abundance = results['community_composition'][mag_id]
            print(f"{mag_id}: {growth_rate:.2f} h⁻¹ (abundance: {abundance:.3f})")

        # Save results
        if args.output:
            output_file = args.output
        else:
            output_file = output_dir / "community_predictions.json"
        
        import json
        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2)
        print(f"\nDetailed results saved to: {output_file}")

    except Exception as e:
        logging.error(f"Error: {str(e)}")
        sys.exit(1)

def main():
    """Main entry point for the DeepMu CLI."""
    parser = argparse.ArgumentParser(description="DeepMu - Microbial Growth Rate and Temperature Prediction")
    subparsers = parser.add_subparsers(dest='command', help='Command to run')

    # Create predict subcommand - 创建预测子命令
    predict_parser = subparsers.add_parser(
        'predict',
        help='使用提取的特征预测生长速率和最适生长温度'
    )
    predict_parser.add_argument(
        'feature_file',
        help='包含提取特征的TSV文件路径（由feature命令生成）'
    )
    predict_parser.add_argument(
        '-o', '--output-dir',
        help='输出文件存储目录（默认: ./results）'
    )
    predict_parser.add_argument(
        '--output',
        help='输出JSON文件路径'
    )
    predict_parser.add_argument(
        '--model-path',
        help='预训练模型文件路径（默认: 使用内置模型）'
    )
# 注意：feature-config 参数已移除，因为当前版本的预测器不支持外部特征配置文件
    # predict_parser.add_argument(
    #     '--feature-config',
    #     help='特征配置文件路径（默认: 使用内置配置）'
    # )
    predict_parser.add_argument(
        '--temp',
        type=float,
        default=37.0,
        help='参考温度（摄氏度，默认: 37.0）'
    )
    predict_parser.add_argument(
        '--no-temp-predict',
        action='store_true',
        help='禁用温度预测（仅预测生长速率）'
    )
    predict_parser.add_argument(
        '--multi-task',
        action='store_true',
        help='启用多任务预测模式（同时预测生长速率和温度，提供更详细的结果）'
    )
    predict_parser.add_argument(
        '--analyze-features',
        action='store_true',
        help='进行特征重要性分析'
    )
    predict_parser.add_argument(
        '--include-uncertainty',
        action='store_true',
        help='包含预测不确定性估计'
    )
    predict_parser.add_argument(
        '-v', '--verbose',
        action='store_true',
        help='启用详细输出'
    )
    predict_parser.set_defaults(func=single_genome_command)

    # Community prediction command
    community_parser = subparsers.add_parser(
        'community',
        help='Predict community-level growth rates from metagenome-assembled genomes (MAGs)'
    )
    community_parser.add_argument(
        'input_dir',
        help='Directory containing MAG FASTA files'
    )
    community_parser.add_argument(
        '--abundance',
        required=True,
        help='Path to abundance file (TSV format) containing MAG abundances. Format: MAG_ID<tab>abundance'
    )
    community_parser.add_argument(
        '--min-abundance',
        type=float,
        default=0.01,
        help='Minimum relative abundance threshold for including MAGs (default: 0.01)'
    )
    community_parser.add_argument(
        '-o', '--output-dir',
        help='Directory to store output files (default: ./results)'
    )
    community_parser.add_argument(
        '-k', '--kofamscan-db',
        help='Path to KofamScan database'
    )
    community_parser.add_argument(
        '--model-path',
        help='Path to pretrained model file (default: uses built-in model)'
    )
# 注意：feature-config 参数已移除，因为当前版本的预测器不支持外部特征配置文件
    # community_parser.add_argument(
    #     '--feature-config',
    #     help='Path to feature configuration file (default: uses built-in configuration)'
    # )
    community_parser.add_argument(
        '--output',
        help='Path to output JSON file'
    )
    community_parser.add_argument(
        '-v', '--verbose',
        action='store_true',
        help='Enable verbose output'
    )
    community_parser.set_defaults(func=community_command)

    # Preprocess command
    preprocess_parser = subparsers.add_parser(
        'preprocess',
        help='Preprocess genome sequences with gene prediction and KO annotation'
    )
    preprocess_parser.add_argument(
        'input',
        help='Path to input genome FASTA file or directory containing multiple genomes'
    )
    preprocess_parser.add_argument(
        '-o', '--output-dir',
        help='Directory to store output files (default: ./results)'
    )
    preprocess_parser.add_argument(
        '-k', '--kofamscan-db',
        help='Path to KofamScan database'
    )
    preprocess_parser.add_argument(
        '-g', '--genetic-code',
        type=int,
        default=11,
        help='NCBI genetic code ID (default: 11 for bacterial)'
    )
    preprocess_parser.add_argument(
        '-m', '--meta-mode',
        action='store_true',
        help='Use metagenome mode for gene prediction'
    )
    preprocess_parser.add_argument(
        '-c', '--cpu',
        type=int,
        default=1,
        help='Number of CPU cores to use for KofamScan'
    )
    preprocess_parser.add_argument(
        '--identify-trnas',
        action='store_true',
        help='Identify tRNAs using tRNAscan-SE'
    )
    preprocess_parser.add_argument(
        '--trnascan-path',
        help='Path to tRNAscan-SE executable (default: uses system PATH)'
    )
    preprocess_parser.add_argument(
        '--identify-rrnas',
        action='store_true',
        help='Identify rRNAs using Barrnap'
    )
    preprocess_parser.add_argument(
        '--barrnap-path',
        help='Path to Barrnap executable (default: uses system PATH)'
    )
    preprocess_parser.add_argument(
        '--kingdom',
        choices=['bac', 'arc', 'euk', 'mito'],
        default='bac',
        help='Kingdom for tRNA and rRNA prediction (bac=bacteria, arc=archaea, euk=eukaryote, mito=mitochondrion, default: bac)'
    )
    preprocess_parser.add_argument(
        '--skip-existing',
        action='store_true',
        help='Skip processing if output files already exist'
    )
    preprocess_parser.add_argument(
        '--metadata',
        help='Path to metadata.tsv file containing kingdom information'
    )
    preprocess_parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
        default='INFO',
        help='Set the logging level'
    )

    # Add feature extraction command
    feature_parser = subparsers.add_parser(
        'feature',
        help='Extract features from genome sequences for growth rate prediction'
    )
    feature_parser.add_argument(
        'input_dir',
        help='Directory containing input genome files (for single genome, create a directory with just one file)'
    )
    feature_parser.add_argument(
        '-o', '--output-dir',
        help='Directory to store output files (default: ./results)'
    )
    feature_parser.add_argument(
        '-k', '--kofamscan-db',
        help='Path to KofamScan database'
    )
    feature_parser.add_argument(
        '-m', '--meta-mode',
        action='store_true',
        help='Use metagenome mode for gene prediction'
    )
    feature_parser.add_argument(
        '-c', '--cpu',
        type=int,
        default=1,
        help='Number of CPU cores to use for KofamScan'
    )
    feature_parser.add_argument(
        '--identify-trnas',
        action='store_true',
        help='Identify tRNAs using tRNAscan-SE'
    )
    feature_parser.add_argument(
        '--trnascan-path',
        help='Path to tRNAscan-SE executable (default: uses system PATH)'
    )
    feature_parser.add_argument(
        '--identify-rrnas',
        action='store_true',
        help='Identify rRNAs using Barrnap'
    )
    feature_parser.add_argument(
        '--barrnap-path',
        help='Path to Barrnap executable (default: uses system PATH)'
    )
    feature_parser.add_argument(
        '--kingdom',
        choices=['bac', 'arc', 'euk', 'mito'],
        default='bac',
        help='Kingdom for tRNA and rRNA prediction (bac=bacteria, arc=archaea, euk=eukaryote, mito=mitochondrion, default: bac)'
    )
    feature_parser.add_argument(
        '--skip-existing',
        action='store_true',
        help='Skip processing if output files already exist'
    )
    feature_parser.add_argument(
        '--metadata',
        help='Path to metadata.tsv file containing kingdom information'
    )
    feature_parser.add_argument(
        '--output-format',
        choices=['tsv', 'json'],
        default='tsv',
        help='Output format for feature file (default: tsv)'
    )
    feature_parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
        default='INFO',
        help='Set the logging level'
    )
    feature_parser.add_argument(
        '-v', '--verbose',
        action='store_true',
        help='Enable verbose output'
    )
    feature_parser.set_defaults(func=feature_command)

    # Parse arguments
    args = parser.parse_args()

    # Run the appropriate command
    if hasattr(args, 'func'):
        args.func(args)
    else:
        parser.print_help()
        sys.exit(1)

def predict_command(args):
    """Handle prediction command using preprocessed feature data."""
    # Set up logging
    setup_logging('DEBUG' if args.verbose else 'INFO')

    # Create output directory if it doesn't exist
    output_dir = Path(args.output_dir) if args.output_dir else Path.cwd() / 'results'
    output_dir.mkdir(parents=True, exist_ok=True)

    try:
        # Validate input file
        if not os.path.exists(args.feature_file):
            raise FileNotFoundError(f"Feature file not found: {args.feature_file}")

        # Initialize predictor with model path
        model_path = args.model_path if args.model_path else os.path.join('models', 'deepmu_final_model', 'growth_model.pt')
        MicrobialGrowthPredictor = get_predictor()
        predictor = MicrobialGrowthPredictor(
            model_path=model_path
        )

        # Make predictions
        logging.info("Making predictions...")
        results = predictor.predict_from_features(
            feature_file=args.feature_file
        )

        # Print results
        if isinstance(results, dict) and 'genome_predictions' in results:
            # Batch prediction results
            print("\nPrediction Results:")
            print(f"Total genomes processed: {len(results['genome_predictions'])}")
            print("\nSummary Statistics:")
            growth_rates = [pred['growth_rate'] for pred in results['genome_predictions'].values()]
            print(f"Average Growth Rate: {sum(growth_rates)/len(growth_rates):.2f} h⁻¹")
            print(f"Min Growth Rate: {min(growth_rates):.2f} h⁻¹")
            print(f"Max Growth Rate: {max(growth_rates):.2f} h⁻¹")

            opt_temps = [pred['optimal_temperature'] for pred in results['genome_predictions'].values() 
                        if 'optimal_temperature' in pred]
            if opt_temps:
                print(f"\nAverage Optimal Temperature: {sum(opt_temps)/len(opt_temps):.1f}°C")
                print(f"Min Optimal Temperature: {min(opt_temps):.1f}°C")
                print(f"Max Optimal Temperature: {max(opt_temps):.1f}°C")
        else:
            # Single prediction results
            print("\nPrediction Results:")
            print(f"Growth Rate: {results['growth_rate']:.2f} h⁻¹")
            if 'optimal_temperature' in results:
                print(f"Optimal Growth Temperature: {results['optimal_temperature']:.1f}°C")

        # Save results to output file if specified
        if args.output:
            output_file = args.output
        else:
            output_file = output_dir / "predictions.json"
        
        import json
        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2)
        print(f"\nDetailed results saved to: {output_file}")

    except Exception as e:
        logging.error(f"Error: {str(e)}")
        sys.exit(1)

def feature_command(args):
    """Handle feature extraction command for both single and batch processing."""
    # Set up logging
    setup_logging('DEBUG' if args.verbose else args.log_level)

    # Create output directory if it doesn't exist
    output_dir = Path(args.output_dir) if args.output_dir else Path.cwd() / 'results'
    output_dir.mkdir(parents=True, exist_ok=True)

    try:
        # Get all genome files in the input directory
        input_dir = Path(args.input_dir)
        genome_files = list(input_dir.glob('*.fa*')) + list(input_dir.glob('*.fna')) + list(input_dir.glob('*.ffn'))
        if not genome_files:
            logging.error(f"No genome files found in {input_dir}")
            sys.exit(1)

        logging.info(f"Found {len(genome_files)} genome files")

        # Process each genome
        results = {}
        for genome_file in genome_files:
            logging.info(f"Processing {genome_file.name}")
            try:
                # Preprocess genome
                preprocess_genome = import_preprocess()
                cds_file, ko_file = preprocess_genome(
                    genome_file=str(genome_file),
                    output_dir=str(output_dir / genome_file.stem),
                    kofamscan_db_path=args.kofamscan_db,
                    meta_mode=args.meta_mode,
                    identify_trnas=args.identify_trnas,
                    trnascan_path=args.trnascan_path,
                    identify_rrnas=args.identify_rrnas,
                    barrnap_path=args.barrnap_path,
                    kingdom=args.kingdom,
                    skip_existing=args.skip_existing,
                    metadata=args.metadata
                )

                # Extract features
                from deepmu.feature_extraction import extract_features
                features = extract_features(
                    cds_file=cds_file,
                    ko_file=ko_file,
                    genome_id=genome_file.stem
                )
                results[genome_file.stem] = features

            except Exception as e:
                logging.error(f"Error processing {genome_file.name}: {str(e)}")
                continue

        # Save results
        if args.output_format == 'tsv':
            output_file = output_dir / "features.tsv"
            from deepmu.utils import save_features_to_tsv
            save_features_to_tsv(results, output_file)
        else:
            output_file = output_dir / "features.json"
            import json
            with open(output_file, 'w') as f:
                json.dump(results, f, indent=2)

        logging.info(f"Features saved to {output_file}")

    except Exception as e:
        logging.error(f"Error: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main()
