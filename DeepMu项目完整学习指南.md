# DeepMu 项目完整学习指南
## 从生物学基础到深度学习实现 - 零基础到独立构建项目

---

## 🎯 学习目标

通过本指南，您将学会：
1. **生物学基础**：理解微生物、基因组、生长等核心概念
2. **数据科学技能**：掌握生物数据处理和特征工程
3. **深度学习应用**：设计和训练生物预测模型
4. **项目架构**：理解完整项目的组织结构
5. **独立开发能力**：具备从零构建类似项目的能力

## 📁 项目文件结构总览

```
DeepMu/
├── 🧬 核心预测系统
│   ├── deepmu_cli.py                    # 命令行接口 - 项目入口
│   ├── deepmu/                          # 核心代码包
│   │   ├── __init__.py                  # 包初始化
│   │   ├── predictors/predictor.py      # 核心预测器 - 主要预测逻辑
│   │   ├── models/networks.py           # 神经网络模型定义
│   │   ├── features/                    # 特征提取模块
│   │   └── utils/                       # 工具函数
│
├── 🚀 训练系统 (新增强化版)
│   ├── train_multi_task_model.py        # 多任务训练 - 同时预测温度和生长率
│   ├── train_growth_rate_model.py       # 生长率专用训练
│   ├── train_temperature_model.py       # 温度专用训练
│   ├── train_*.sh                       # 对应的Shell脚本
│
├── 🎨 可视化分析系统
│   ├── visualization/training/          # 训练过程可视化
│   ├── visualization/prediction/        # 预测结果可视化
│   ├── visualization/interpretability/  # 可解释性分析
│
├── 📊 数据和模型
│   ├── training_data/                   # 训练数据
│   ├── models/                          # 训练好的模型
│   └── features/                        # 特征文件
│
└── 📚 配置和文档
    ├── configs/                         # 配置文件
    ├── requirements.txt                 # 依赖包
    └── *.md                            # 文档
```

## 📚 目录

1. [生物学基础知识](#1-生物学基础知识)
2. [微生物生长原理](#2-微生物生长原理)
3. [基因组学基础](#3-基因组学基础)
4. [机器学习在生物学中的应用](#4-机器学习在生物学中的应用)
5. [DeepMu项目架构详解](#5-deepmu项目架构详解)
6. [数据处理与特征工程](#6-数据处理与特征工程)
7. [深度学习模型设计](#7-深度学习模型设计)
8. [训练策略与优化](#8-训练策略与优化)
9. [模型评估与可解释性](#9-模型评估与可解释性)
10. [实际应用与案例](#10-实际应用与案例)
11. [从零构建项目指南](#11-从零构建项目指南)

---

## 1. 生物学基础知识

### 1.1 什么是微生物？

**微生物**是肉眼看不见的微小生物，包括：
- **细菌** (Bacteria): 单细胞原核生物
- **古菌** (Archaea): 极端环境中的原核生物
- **真菌** (Fungi): 包括酵母、霉菌等
- **病毒** (Viruses): 需要宿主细胞才能复制

```python
# 在DeepMu中，我们主要研究细菌和古菌
# 这些微生物的基因组信息存储在FASTA文件中

def load_genome_sequence(fasta_file):
    """
    加载基因组序列

    基因组序列由四种碱基组成：A(腺嘌呤)、T(胸腺嘧啶)、G(鸟嘌呤)、C(胞嘧啶)
    """
    from Bio import SeqIO

    sequences = []
    for record in SeqIO.parse(fasta_file, "fasta"):
        # record.seq 包含DNA序列，如 "ATCGATCGATCG..."
        sequences.append({
            'id': record.id,           # 基因ID
            'sequence': str(record.seq), # DNA序列
            'length': len(record.seq)    # 序列长度
        })

    return sequences

# 示例：基因组序列片段
example_sequence = "ATGAAACGCATTAGCACCACCATTACCACCACCATCACCATTACCACAGGTAACGGTGCGGGCTGA"
print(f"序列长度: {len(example_sequence)} 个碱基对")
print(f"GC含量: {(example_sequence.count('G') + example_sequence.count('C')) / len(example_sequence) * 100:.1f}%")
```

### 1.2 微生物的重要性

微生物在自然界和人类生活中扮演重要角色：

1. **环境作用**：
   - 分解有机物，维持生态平衡
   - 固氮作用，为植物提供氮素
   - 污染物降解，环境修复

2. **工业应用**：
   - 发酵工业（酒类、面包、酸奶）
   - 抗生素生产
   - 生物燃料制造

3. **医学意义**：
   - 肠道菌群影响健康
   - 病原菌引起疾病
   - 益生菌治疗

```python
# DeepMu项目的应用场景示例
applications = {
    "环境修复": {
        "目标": "预测微生物在不同温度下的污染物降解能力",
        "重要性": "选择最适合的微生物进行环境治理"
    },
    "工业发酵": {
        "目标": "预测微生物的最适生长温度和生长速率",
        "重要性": "优化发酵条件，提高产量"
    },
    "食品安全": {
        "目标": "预测病原菌在不同温度下的生长情况",
        "重要性": "制定食品储存和加工标准"
    }
}

for app, details in applications.items():
    print(f"\n{app}:")
    print(f"  目标: {details['目标']}")
    print(f"  重要性: {details['重要性']}")
```

---

## 2. 微生物生长原理

### 2.1 微生物生长的基本概念

**生长速率** (Growth Rate): 微生物数量随时间的增长速度

```python
import numpy as np
import matplotlib.pyplot as plt

def exponential_growth(initial_population, growth_rate, time):
    """
    指数增长模型：N(t) = N0 * e^(μt)

    参数:
    - initial_population (N0): 初始菌群数量
    - growth_rate (μ): 生长速率 (h⁻¹)
    - time (t): 时间 (小时)
    """
    return initial_population * np.exp(growth_rate * time)

# 示例：不同生长速率的微生物生长曲线
time_points = np.linspace(0, 10, 100)  # 0-10小时
growth_rates = [0.1, 0.3, 0.5, 0.7]   # 不同的生长速率

plt.figure(figsize=(10, 6))
for rate in growth_rates:
    population = exponential_growth(1, rate, time_points)
    plt.plot(time_points, population, label=f'μ = {rate} h⁻¹')

plt.xlabel('时间 (小时)')
plt.ylabel('菌群数量 (相对值)')
plt.title('不同生长速率下的微生物生长曲线')
plt.legend()
plt.yscale('log')  # 对数坐标，更好显示指数增长
plt.grid(True, alpha=0.3)
plt.show()

# 在DeepMu中，我们要预测的就是这个生长速率μ
print("生长速率的意义：")
print("- μ = 0.1 h⁻¹: 生长缓慢，10小时增长2.7倍")
print("- μ = 0.3 h⁻¹: 中等生长，10小时增长20倍")
print("- μ = 0.5 h⁻¹: 快速生长，10小时增长148倍")
print("- μ = 0.7 h⁻¹: 极快生长，10小时增长1096倍")
```

### 2.2 温度对微生物生长的影响

温度是影响微生物生长最重要的环境因素之一：

```python
def temperature_growth_relationship(temperature, optimal_temp, max_temp, min_temp):
    """
    温度-生长速率关系模型（简化的Cardinal温度模型）

    参数:
    - temperature: 当前温度 (°C)
    - optimal_temp: 最适温度 (°C)
    - max_temp: 最高生长温度 (°C)
    - min_temp: 最低生长温度 (°C)
    """
    if temperature <= min_temp or temperature >= max_temp:
        return 0  # 超出生长范围，无法生长

    # 简化的二次函数模型
    if temperature <= optimal_temp:
        # 低温侧
        growth_rate = ((temperature - min_temp) / (optimal_temp - min_temp)) ** 2
    else:
        # 高温侧
        growth_rate = ((max_temp - temperature) / (max_temp - optimal_temp)) ** 2

    return growth_rate

# 示例：不同类型微生物的温度适应性
microbe_types = {
    "嗜冷菌": {"min": -5, "opt": 15, "max": 25},
    "中温菌": {"min": 10, "opt": 37, "max": 45},
    "嗜热菌": {"min": 45, "opt": 70, "max": 85},
    "超嗜热菌": {"min": 60, "opt": 90, "max": 110}
}

temperatures = np.linspace(-10, 120, 200)

plt.figure(figsize=(12, 8))
for microbe, temps in microbe_types.items():
    growth_rates = [temperature_growth_relationship(t, temps["opt"], temps["max"], temps["min"])
                   for t in temperatures]
    plt.plot(temperatures, growth_rates, label=f'{microbe} (最适温度: {temps["opt"]}°C)', linewidth=2)

plt.xlabel('温度 (°C)')
plt.ylabel('相对生长速率')
plt.title('不同类型微生物的温度-生长速率关系')
plt.legend()
plt.grid(True, alpha=0.3)
plt.xlim(-10, 120)
plt.ylim(0, 1.1)

# 添加重要温度点的标注
plt.axvline(x=0, color='blue', linestyle='--', alpha=0.5, label='冰点')
plt.axvline(x=37, color='red', linestyle='--', alpha=0.5, label='人体温度')
plt.axvline(x=100, color='orange', linestyle='--', alpha=0.5, label='沸点')

plt.show()

print("DeepMu项目的目标：")
print("给定微生物的基因组信息，预测其最适生长温度和生长速率")
```

### 2.3 影响微生物生长的其他因素

```python
# 微生物生长的影响因素
growth_factors = {
    "物理因素": {
        "温度": "影响酶活性和细胞膜流动性",
        "pH值": "影响酶活性和离子平衡",
        "渗透压": "影响细胞水分平衡",
        "氧气": "好氧菌需要氧气，厌氧菌不需要"
    },
    "化学因素": {
        "营养物质": "碳源、氮源、磷源等",
        "微量元素": "铁、锌、铜等金属离子",
        "维生素": "某些微生物需要特定维生素",
        "抑制剂": "抗生素、重金属等"
    },
    "生物因素": {
        "种间竞争": "不同微生物竞争资源",
        "共生关系": "某些微生物互利共生",
        "捕食关系": "噬菌体感染细菌"
    }
}

# 在DeepMu中，我们主要关注温度和生长速率
# 但基因组信息包含了微生物对其他因素的适应性信息
def analyze_genome_for_growth_factors(genome_features):
    """
    从基因组特征分析微生物的生长特性
    """
    growth_characteristics = {}

    # 温度适应性相关基因
    heat_shock_genes = sum(1 for feature in genome_features if 'heat_shock' in feature.lower())
    cold_shock_genes = sum(1 for feature in genome_features if 'cold_shock' in feature.lower())

    growth_characteristics['temperature_adaptation'] = {
        'heat_tolerance': heat_shock_genes,
        'cold_tolerance': cold_shock_genes
    }

    # 代谢相关基因
    metabolic_genes = sum(1 for feature in genome_features if any(keyword in feature.lower()
                         for keyword in ['metabolism', 'enzyme', 'pathway']))

    growth_characteristics['metabolic_capacity'] = metabolic_genes

    return growth_characteristics

# 示例基因组特征（简化）
example_features = [
    'heat_shock_protein_70', 'cold_shock_protein_A', 'glucose_metabolism_enzyme',
    'amino_acid_biosynthesis', 'DNA_repair_enzyme', 'cell_wall_synthesis'
]

characteristics = analyze_genome_for_growth_factors(example_features)
print("基因组分析结果：")
for category, values in characteristics.items():
    print(f"  {category}: {values}")
```

---

## 3. 基因组学基础

### 3.1 什么是基因组？

**基因组** (Genome) 是生物体全部遗传信息的总和，包含了生物体生长、发育、代谢等所有生命活动的"指令书"。

```python
# 文件位置: deepmu/features/genome_processor.py

import pandas as pd
from Bio import SeqIO
from collections import Counter

class GenomeAnalyzer:
    """
    基因组分析器 - 理解基因组数据的结构和含义

    这是DeepMu项目的核心组件，负责将生物序列转换为机器学习特征
    """

    def __init__(self, genome_file):
        self.genome_file = genome_file
        self.sequences = self.load_genome()

    def load_genome(self):
        """
        加载基因组序列

        基因组文件格式：
        - FASTA格式：包含DNA序列
        - 每个序列代表一个基因或基因组片段

        示例FASTA文件内容：
        >gene1 glucose metabolism enzyme
        ATGAAACGCATTAGCACCACCATTACCACCACCATCACCATTACCACAGGTAACGGTGCGGGCTGA
        >gene2 heat shock protein
        ATGCGTAAAGGAGAAGAACTTTTCACTGGAGTTGTCCCAATTCTTGTTGAATTAGATGGTGATGTT
        """
        sequences = []
        try:
            for record in SeqIO.parse(self.genome_file, "fasta"):
                sequences.append({
                    'id': record.id,                    # 基因标识符
                    'description': record.description,  # 基因功能描述
                    'sequence': str(record.seq),        # DNA序列
                    'length': len(record.seq)           # 序列长度
                })
        except Exception as e:
            print(f"加载基因组文件失败: {e}")

        return sequences

    def calculate_gc_content(self, sequence):
        """
        计算GC含量 - 重要的基因组特征

        GC含量 = (G + C) / (A + T + G + C) × 100%

        生物学意义：
        - 高GC含量 (>60%): DNA更稳定，适应高温环境
        - 中等GC含量 (40-60%): 大多数中温菌的特征
        - 低GC含量 (<40%): DNA复制更容易，生长更快

        在DeepMu中，GC含量是预测温度适应性的重要特征
        """
        sequence = sequence.upper()
        gc_count = sequence.count('G') + sequence.count('C')
        total_count = len(sequence)

        if total_count == 0:
            return 0

        return (gc_count / total_count) * 100

    def analyze_codon_usage(self, sequence):
        """
        分析密码子使用偏好

        密码子：三个碱基组成一个密码子，编码一个氨基酸
        例如：ATG编码甲硫氨酸，TAA是终止密码子

        不同微生物有不同的密码子使用偏好：
        - 快速生长的微生物偏好使用高效的密码子
        - 极端环境微生物使用特殊的密码子模式

        这影响蛋白质合成效率，进而影响生长速率
        """
        codons = []
        sequence = sequence.upper()

        # 提取所有密码子（每3个碱基）
        for i in range(0, len(sequence) - 2, 3):
            codon = sequence[i:i+3]
            if len(codon) == 3 and all(base in 'ATGC' for base in codon):
                codons.append(codon)

        return Counter(codons)

# 演示基因组分析过程
def demonstrate_genome_analysis():
    """
    演示基因组分析过程 - 理解从DNA序列到特征的转换
    """
    print("=== 基因组分析演示 ===\n")

    # 模拟真实的基因组序列
    sample_sequences = [
        {
            'id': 'gene1',
            'description': 'glucose metabolism enzyme',
            'sequence': 'ATGAAACGCATTAGCACCACCATTACCACCACCATCACCATTACCACAGGTAACGGTGCGGGCTGA'
        },
        {
            'id': 'gene2',
            'description': 'heat shock protein 70',
            'sequence': 'ATGCGTAAAGGAGAAGAACTTTTCACTGGAGTTGTCCCAATTCTTGTTGAATTAGATGGTGATGTT'
        }
    ]

    print("基因组序列分析：")

    for seq_info in sample_sequences:
        sequence = seq_info['sequence']
        print(f"\n基因 {seq_info['id']} ({seq_info['description']}):")
        print(f"  序列长度: {len(sequence)} bp (碱基对)")

        # 计算GC含量
        gc_content = (sequence.count('G') + sequence.count('C')) / len(sequence) * 100
        print(f"  GC含量: {gc_content:.1f}%")

        # 分析密码子
        codons = []
        for i in range(0, len(sequence) - 2, 3):
            codon = sequence[i:i+3]
            if len(codon) == 3:
                codons.append(codon)

        print(f"  密码子数量: {len(codons)}")
        print(f"  序列片段: {sequence[:30]}...")

        # 预测功能类型
        if 'metabolism' in seq_info['description']:
            print(f"  功能预测: 代谢相关 → 可能影响生长速率")
        elif 'heat' in seq_info['description']:
            print(f"  功能预测: 温度适应 → 可能影响温度耐受性")

demonstrate_genome_analysis()
```

### 3.2 从基因组到特征的转换

这是DeepMu项目的核心技术 - 将生物序列转换为机器学习可以理解的数值特征：

```python
# 文件位置: deepmu/features/feature_extractor.py

import numpy as np
from sklearn.preprocessing import StandardScaler
from collections import Counter

class GenomeFeatureExtractor:
    """
    基因组特征提取器 - DeepMu项目的核心组件

    功能：将生物序列转换为机器学习特征
    输入：基因组序列和注释信息
    输出：数值特征向量
    """

    def __init__(self):
        self.feature_names = []
        self.scaler = StandardScaler()

    def extract_composition_features(self, genome_sequence):
        """
        提取序列组成特征

        这些特征反映了基因组的基本组成，与微生物的生长特性密切相关：

        1. 单核苷酸频率 (A, T, G, C) - 基础组成
        2. 双核苷酸频率 (AA, AT, AG, ...) - 局部结构
        3. 三核苷酸频率 (密码子频率) - 蛋白质编码偏好
        """
        features = {}
        sequence = genome_sequence.upper()
        length = len(sequence)

        if length == 0:
            return features

        # 1. 单核苷酸频率 - 反映基本组成
        for base in ['A', 'T', 'G', 'C']:
            features[f'mono_{base}'] = sequence.count(base) / length

        # 2. 双核苷酸频率 - 反映局部序列特征
        dinucleotides = [
            'AA', 'AT', 'AG', 'AC', 'TA', 'TT', 'TG', 'TC',
            'GA', 'GT', 'GG', 'GC', 'CA', 'CT', 'CG', 'CC'
        ]

        for dinuc in dinucleotides:
            count = 0
            for i in range(length - 1):
                if sequence[i:i+2] == dinuc:
                    count += 1
            features[f'di_{dinuc}'] = count / (length - 1) if length > 1 else 0

        # 3. 三核苷酸频率 (密码子) - 反映蛋白质编码偏好
        # 只统计最常见的密码子，避免特征过多
        common_codons = [
            'ATG', 'TTG', 'CTG', 'GTG',  # 起始密码子和亮氨酸
            'TAA', 'TAG', 'TGA',         # 终止密码子
            'GCT', 'GCC', 'GCA', 'GCG'   # 丙氨酸密码子
        ]

        for codon in common_codons:
            count = 0
            for i in range(0, length - 2, 3):
                if sequence[i:i+3] == codon:
                    count += 1
            features[f'codon_{codon}'] = count / ((length // 3) + 1)

        return features

    def extract_structural_features(self, genome_sequence):
        """
        提取结构特征

        这些特征反映DNA的物理化学性质，与温度适应性相关：

        1. GC含量 - 影响DNA稳定性
        2. GC偏斜 - 反映复制方向
        3. AT偏斜 - 反映碱基组成偏好
        4. 复杂度 - 序列的复杂程度
        """
        features = {}
        sequence = genome_sequence.upper()

        # 1. GC含量 - 最重要的温度适应性指标
        gc_count = sequence.count('G') + sequence.count('C')
        features['gc_content'] = gc_count / len(sequence) if len(sequence) > 0 else 0

        # 2. GC偏斜：(G-C)/(G+C) - 反映复制链偏好
        g_count = sequence.count('G')
        c_count = sequence.count('C')
        if (g_count + c_count) > 0:
            features['gc_skew'] = (g_count - c_count) / (g_count + c_count)
        else:
            features['gc_skew'] = 0

        # 3. AT偏斜：(A-T)/(A+T) - 反映碱基组成偏好
        a_count = sequence.count('A')
        t_count = sequence.count('T')
        if (a_count + t_count) > 0:
            features['at_skew'] = (a_count - t_count) / (a_count + t_count)
        else:
            features['at_skew'] = 0

        # 4. 序列复杂度 - 使用Shannon熵
        base_counts = Counter(sequence)
        total = len(sequence)
        entropy = 0
        for count in base_counts.values():
            if count > 0:
                p = count / total
                entropy -= p * np.log2(p)
        features['sequence_complexity'] = entropy

        return features

# 演示特征提取过程
def demonstrate_feature_extraction():
    """
    演示从基因组到机器学习特征的完整过程
    """
    print("=== 基因组特征提取演示 ===\n")

    # 模拟不同类型微生物的基因组片段
    microbe_examples = {
        "嗜热菌": {
            "sequence": "GCGCGCGCATGCGCGCGCGCATGCGCGCGCGCATGCGCGCGCGC" * 5,  # 高GC含量
            "description": "高GC含量，适应高温"
        },
        "中温菌": {
            "sequence": "ATGCATGCATGCATGCATGCATGCATGCATGCATGCATGCATGC" * 5,  # 中等GC含量
            "description": "平衡的碱基组成"
        },
        "快速生长菌": {
            "sequence": "ATATATATGCGCGCGCATATATATGCGCGCGCATATATATGCGC" * 5,  # 特殊模式
            "description": "优化的密码子使用"
        }
    }

    extractor = GenomeFeatureExtractor()

    print("不同微生物的基因组特征对比：\n")

    for microbe_type, data in microbe_examples.items():
        sequence = data["sequence"]

        # 提取特征
        comp_features = extractor.extract_composition_features(sequence)
        struct_features = extractor.extract_structural_features(sequence)

        print(f"{microbe_type} ({data['description']}):")
        print(f"  序列长度: {len(sequence)} bp")
        print(f"  GC含量: {struct_features['gc_content']:.3f}")
        print(f"  GC偏斜: {struct_features['gc_skew']:.3f}")
        print(f"  序列复杂度: {struct_features['sequence_complexity']:.3f}")
        print(f"  A频率: {comp_features['mono_A']:.3f}")
        print(f"  T频率: {comp_features['mono_T']:.3f}")
        print(f"  G频率: {comp_features['mono_G']:.3f}")
        print(f"  C频率: {comp_features['mono_C']:.3f}")
        print()

    print("这些特征将输入神经网络，预测微生物的生长特性")

demonstrate_feature_extraction()
```

### 3.3 功能注释与基因分类

基因的功能注释告诉我们每个基因的作用，这对预测微生物特性至关重要：

```python
# 文件位置: deepmu/features/functional_analyzer.py

class FunctionalAnnotationAnalyzer:
    """
    功能注释分析器 - 从基因功能预测微生物特性

    基因功能分类：
    1. 代谢相关 - 影响生长速率
    2. 应激响应 - 影响环境适应性
    3. 膜蛋白 - 影响物质交换
    4. DNA修复 - 影响稳定性
    """

    def __init__(self):
        # 定义功能分类关键词
        self.functional_categories = {
            'metabolism': {
                'keywords': ['metabolism', 'enzyme', 'kinase', 'synthase', 'dehydrogenase',
                           'transferase', 'hydrolase', 'lyase', 'isomerase', 'ligase'],
                'description': '代谢相关基因，影响生长速率和营养利用'
            },
            'stress_response': {
                'keywords': ['stress', 'heat_shock', 'cold_shock', 'chaperone', 'groEL',
                           'dnaK', 'response', 'adaptation'],
                'description': '应激响应基因，影响环境适应性'
            },
            'membrane_transport': {
                'keywords': ['membrane', 'transport', 'channel', 'pump', 'permease',
                           'transporter', 'efflux', 'influx'],
                'description': '膜转运基因，影响物质交换效率'
            },
            'dna_replication': {
                'keywords': ['replication', 'DNA', 'polymerase', 'helicase', 'primase',
                           'ligase', 'repair', 'recombination'],
                'description': 'DNA复制修复基因，影响遗传稳定性'
            },
            'transcription_regulation': {
                'keywords': ['transcription', 'RNA', 'promoter', 'regulator', 'sigma',
                           'activator', 'repressor', 'operon'],
                'description': '转录调控基因，影响基因表达调控'
            },
            'cell_wall': {
                'keywords': ['cell_wall', 'peptidoglycan', 'murein', 'wall', 'envelope'],
                'description': '细胞壁相关基因，影响细胞结构稳定性'
            }
        }

    def classify_gene_function(self, gene_annotation):
        """
        根据基因注释分类基因功能

        输入：基因功能描述，如 "glucose metabolism enzyme"
        输出：功能分类列表
        """
        annotation_lower = gene_annotation.lower()
        classifications = []

        for category, info in self.functional_categories.items():
            for keyword in info['keywords']:
                if keyword in annotation_lower:
                    classifications.append(category)
                    break

        return classifications

    def extract_functional_features(self, gene_annotations):
        """
        从基因注释列表提取功能特征

        输入：基因注释列表
        输出：功能特征字典
        """
        features = {}

        # 初始化计数器
        for category in self.functional_categories.keys():
            features[f'func_{category}_count'] = 0
            features[f'func_{category}_ratio'] = 0

        total_genes = len(gene_annotations)
        if total_genes == 0:
            return features

        # 统计各功能类别的基因数量
        for annotation in gene_annotations:
            classifications = self.classify_gene_function(annotation)
            for category in classifications:
                features[f'func_{category}_count'] += 1

        # 计算比例
        for category in self.functional_categories.keys():
            count = features[f'func_{category}_count']
            features[f'func_{category}_ratio'] = count / total_genes

        return features

    def predict_growth_characteristics(self, functional_features):
        """
        基于功能特征预测生长特性

        这是一个简化的规则基预测，展示功能与表型的关系
        """
        predictions = {}

        # 预测生长速率潜力
        metabolism_ratio = functional_features.get('func_metabolism_ratio', 0)
        if metabolism_ratio > 0.15:
            predictions['growth_potential'] = 'high'
        elif metabolism_ratio > 0.10:
            predictions['growth_potential'] = 'medium'
        else:
            predictions['growth_potential'] = 'low'

        # 预测温度适应性
        stress_ratio = functional_features.get('func_stress_response_ratio', 0)
        if stress_ratio > 0.05:
            predictions['temperature_adaptation'] = 'broad'
        else:
            predictions['temperature_adaptation'] = 'narrow'

        # 预测环境适应性
        membrane_ratio = functional_features.get('func_membrane_transport_ratio', 0)
        if membrane_ratio > 0.08:
            predictions['environmental_adaptation'] = 'versatile'
        else:
            predictions['environmental_adaptation'] = 'specialized'

        return predictions

# 演示功能注释分析
def demonstrate_functional_analysis():
    """
    演示功能注释分析过程
    """
    print("=== 功能注释分析演示 ===\n")

    # 模拟不同微生物的基因注释
    microbe_annotations = {
        "快速生长菌": [
            "glucose metabolism enzyme",
            "amino acid biosynthesis enzyme",
            "ATP synthase subunit",
            "ribosomal protein L1",
            "DNA polymerase III",
            "membrane transport protein",
            "cell division protein"
        ],
        "极端环境菌": [
            "heat shock protein 70",
            "cold shock protein A",
            "DNA repair enzyme",
            "stress response regulator",
            "membrane stabilization protein",
            "oxidative stress response",
            "chaperone protein"
        ],
        "专性寄生菌": [
            "host cell invasion protein",
            "virulence factor",
            "secretion system component",
            "antibiotic resistance gene",
            "toxin production enzyme"
        ]
    }

    analyzer = FunctionalAnnotationAnalyzer()

    print("不同微生物的功能特征分析：\n")

    for microbe_type, annotations in microbe_annotations.items():
        print(f"{microbe_type}:")
        print(f"  基因数量: {len(annotations)}")

        # 提取功能特征
        func_features = analyzer.extract_functional_features(annotations)

        # 显示主要功能类别
        for category, info in analyzer.functional_categories.items():
            ratio = func_features[f'func_{category}_ratio']
            if ratio > 0:
                print(f"  {category}: {ratio:.3f} ({info['description']})")

        # 预测生长特性
        predictions = analyzer.predict_growth_characteristics(func_features)
        print(f"  预测特性:")
        for trait, value in predictions.items():
            print(f"    {trait}: {value}")
        print()

demonstrate_functional_analysis()
```

---

## 4. 机器学习在生物学中的应用

### 4.1 为什么用机器学习预测微生物特性？

传统的微生物研究方法需要大量实验，而机器学习可以：

```python
# 文件位置: deepmu/utils/ml_concepts.py

class BioinformaticsMLConcepts:
    """
    生物信息学中的机器学习概念
    """

    @staticmethod
    def traditional_vs_ml_approach():
        """
        对比传统方法与机器学习方法
        """
        comparison = {
            "传统实验方法": {
                "优点": [
                    "结果准确可靠",
                    "能获得详细的生物学机制",
                    "实验条件可控"
                ],
                "缺点": [
                    "耗时长（数周到数月）",
                    "成本高（培养基、设备、人力）",
                    "通量低（一次只能测试少数菌株）",
                    "受实验条件限制"
                ],
                "适用场景": "深入研究特定微生物的详细特性"
            },
            "机器学习方法": {
                "优点": [
                    "速度快（几分钟到几小时）",
                    "成本低（只需计算资源）",
                    "高通量（可同时预测大量菌株）",
                    "可预测未培养微生物"
                ],
                "缺点": [
                    "需要大量训练数据",
                    "预测准确性依赖模型质量",
                    "难以解释生物学机制"
                ],
                "适用场景": "大规模筛选和初步预测"
            }
        }

        return comparison

    @staticmethod
    def ml_workflow_in_biology():
        """
        生物学中机器学习的工作流程
        """
        workflow = {
            "1. 数据收集": {
                "基因组数据": "DNA序列、基因注释",
                "表型数据": "生长速率、最适温度、pH耐受性",
                "环境数据": "分离环境、生态位信息"
            },
            "2. 特征工程": {
                "序列特征": "GC含量、密码子使用、k-mer频率",
                "功能特征": "基因功能分类、代谢通路",
                "结构特征": "蛋白质结构预测、RNA二级结构"
            },
            "3. 模型选择": {
                "回归问题": "预测连续值（生长速率、温度）",
                "分类问题": "预测类别（嗜热/中温/嗜冷）",
                "多任务学习": "同时预测多个相关特性"
            },
            "4. 模型训练": {
                "数据分割": "训练集、验证集、测试集",
                "超参数优化": "学习率、网络结构、正则化",
                "交叉验证": "评估模型泛化能力"
            },
            "5. 模型评估": {
                "性能指标": "R²、RMSE、MAE",
                "生物学验证": "与已知生物学知识对比",
                "实验验证": "选择性实验验证预测结果"
            }
        }

        return workflow

# 演示机器学习在生物学中的应用
def demonstrate_ml_in_biology():
    """
    演示机器学习在生物学研究中的应用价值
    """
    print("=== 机器学习在微生物学中的应用 ===\n")

    concepts = BioinformaticsMLConcepts()

    # 对比传统方法与ML方法
    comparison = concepts.traditional_vs_ml_approach()

    print("方法对比：")
    for method, details in comparison.items():
        print(f"\n{method}:")
        print(f"  优点: {', '.join(details['优点'])}")
        print(f"  缺点: {', '.join(details['缺点'])}")
        print(f"  适用场景: {details['适用场景']}")

    # 实际应用案例
    print("\n\n实际应用案例：")

    cases = {
        "药物发现": {
            "问题": "筛选能产生新抗生素的微生物",
            "传统方法": "培养数千种微生物，逐一测试抗菌活性",
            "ML方法": "分析基因组，预测抗生素合成基因簇",
            "优势": "从数万种微生物中快速筛选出候选者"
        },
        "环境修复": {
            "问题": "寻找能降解特定污染物的微生物",
            "传统方法": "从污染环境分离微生物，测试降解能力",
            "ML方法": "基于基因组预测代谢能力和环境适应性",
            "优势": "预测未培养微生物的降解潜力"
        },
        "工业发酵": {
            "问题": "优化发酵条件，提高产量",
            "传统方法": "试验不同温度、pH、营养条件",
            "ML方法": "预测最适生长条件，指导实验设计",
            "优势": "减少实验次数，快速找到最优条件"
        }
    }

    for case_name, details in cases.items():
        print(f"\n{case_name}:")
        for key, value in details.items():
            print(f"  {key}: {value}")

demonstrate_ml_in_biology()
```

### 4.2 DeepMu的机器学习策略

```python
# 文件位置: deepmu/models/ml_strategy.py

import torch
import torch.nn as nn
import numpy as np

class DeepMuMLStrategy:
    """
    DeepMu的机器学习策略解析
    """

    @staticmethod
    def problem_formulation():
        """
        问题定义：从基因组序列预测微生物生长特性
        """
        problem = {
            "输入": {
                "基因组序列": "ATCGATCG... (DNA序列)",
                "基因注释": "['enzyme', 'transport protein', ...]",
                "特征维度": "通常1000-5000维"
            },
            "输出": {
                "生长速率": "连续值，单位 h⁻¹",
                "最适温度": "连续值，单位 °C",
                "预测范围": "生长速率: 0-2 h⁻¹, 温度: 0-100°C"
            },
            "挑战": {
                "高维稀疏": "基因组特征维度高，样本相对较少",
                "非线性关系": "基因型与表型的复杂映射关系",
                "多任务学习": "同时预测多个相关特性",
                "可解释性": "需要理解哪些基因影响预测结果"
            }
        }

        return problem

    @staticmethod
    def model_architecture_rationale():
        """
        模型架构设计理念
        """
        rationale = {
            "深度神经网络": {
                "原因": "能学习复杂的非线性映射关系",
                "结构": "多层全连接网络",
                "激活函数": "GELU - 平滑的非线性变换"
            },
            "多任务学习": {
                "原因": "生长速率和温度相关，共享特征表示",
                "结构": "共享编码器 + 任务特定头",
                "优势": "提高数据利用效率，增强泛化能力"
            },
            "正则化技术": {
                "Dropout": "防止过拟合，提高泛化能力",
                "BatchNorm": "稳定训练，加速收敛",
                "权重衰减": "控制模型复杂度"
            },
            "损失函数设计": {
                "MSE": "适用于回归问题",
                "MAE": "对异常值更鲁棒",
                "多任务损失": "加权组合不同任务的损失"
            }
        }

        return rationale

# 简化的DeepMu模型示例
class SimpleDeepMuModel(nn.Module):
    """
    简化的DeepMu模型 - 展示核心思想
    """

    def __init__(self, input_dim, hidden_dims, output_dim):
        super().__init__()

        # 构建网络层
        layers = []
        prev_dim = input_dim

        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.BatchNorm1d(hidden_dim),
                nn.GELU(),
                nn.Dropout(0.3)
            ])
            prev_dim = hidden_dim

        # 输出层
        layers.append(nn.Linear(prev_dim, output_dim))

        self.network = nn.Sequential(*layers)

    def forward(self, x):
        return self.network(x)

# 演示模型工作原理
def demonstrate_model_workflow():
    """
    演示DeepMu模型的工作流程
    """
    print("=== DeepMu模型工作流程演示 ===\n")

    # 模拟输入数据
    batch_size = 32
    input_dim = 1516  # 特征维度（与实际DeepMu一致）

    # 创建模型
    model = SimpleDeepMuModel(
        input_dim=input_dim,
        hidden_dims=[512, 256, 128],
        output_dim=2  # 生长速率 + 温度
    )

    # 模拟基因组特征
    genome_features = torch.randn(batch_size, input_dim)

    print(f"输入特征形状: {genome_features.shape}")
    print(f"特征示例 (前5个): {genome_features[0, :5].tolist()}")

    # 前向传播
    with torch.no_grad():
        predictions = model(genome_features)

    print(f"\n预测结果形状: {predictions.shape}")
    print(f"预测示例:")
    for i in range(min(5, batch_size)):
        growth_rate = predictions[i, 0].item()
        temperature = predictions[i, 1].item()
        print(f"  样本{i+1}: 生长速率={growth_rate:.3f} h⁻¹, 温度={temperature:.1f}°C")

    # 模型参数统计
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)

    print(f"\n模型统计:")
    print(f"  总参数数量: {total_params:,}")
    print(f"  可训练参数: {trainable_params:,}")
    print(f"  模型大小: ~{total_params * 4 / 1024 / 1024:.1f} MB")

demonstrate_model_workflow()
```

---

## 5. DeepMu项目架构详解

### 5.1 项目整体架构

DeepMu项目采用模块化设计，每个组件都有明确的职责：

```python
# 文件位置: deepmu/__init__.py

"""
DeepMu: 微生物生长预测深度学习框架

项目架构：
1. 核心预测系统 - 提供预测接口
2. 训练系统 - 模型训练和优化
3. 可视化系统 - 结果分析和解释
4. 数据处理系统 - 特征提取和预处理
"""

__version__ = "2.0.0"
__author__ = "DeepMu Development Team"

# 核心组件导入
from .predictors.predictor import DeepMuPredictor
from .models.networks import MultiTaskModel, GrowthRateModel, TemperatureModel
from .features.feature_extractor import GenomeFeatureExtractor

# 项目架构概览
PROJECT_ARCHITECTURE = {
    "核心预测系统": {
        "入口": "deepmu_cli.py",
        "核心类": "DeepMuPredictor",
        "功能": "提供命令行和API预测接口"
    },
    "训练系统": {
        "多任务训练": "train_multi_task_model.py",
        "单任务训练": ["train_growth_rate_model.py", "train_temperature_model.py"],
        "功能": "模型训练、验证、保存"
    },
    "可视化系统": {
        "训练可视化": "visualization/training/",
        "预测可视化": "visualization/prediction/",
        "可解释性": "visualization/interpretability/",
        "功能": "结果分析、模型解释、报告生成"
    },
    "数据处理": {
        "特征提取": "deepmu/features/",
        "数据加载": "deepmu/utils/",
        "功能": "基因组数据处理、特征工程"
    }
}

def show_architecture():
    """显示项目架构"""
    print("=== DeepMu 项目架构 ===\n")
    for system, details in PROJECT_ARCHITECTURE.items():
        print(f"{system}:")
        for key, value in details.items():
            if isinstance(value, list):
                print(f"  {key}: {', '.join(value)}")
            else:
                print(f"  {key}: {value}")
        print()

if __name__ == "__main__":
    show_architecture()
```

### 5.2 核心预测系统详解

#### 5.2.1 命令行接口 (deepmu_cli.py)

这是用户与DeepMu交互的主要入口：

```python
# 文件位置: deepmu_cli.py

#!/usr/bin/env python3
"""
DeepMu 命令行接口

这是DeepMu项目的主要入口点，提供以下功能：
1. 单个基因组预测
2. 批量基因组预测
3. 模型信息查看
4. 结果格式化输出

使用示例：
    python deepmu_cli.py predict genome.fasta
    python deepmu_cli.py batch-predict genomes/ --output results.json
"""

import argparse
import sys
import json
from pathlib import Path

# 导入DeepMu核心组件
from deepmu.predictors.predictor import DeepMuPredictor
from deepmu.utils.file_utils import validate_input_file, load_genome_file

class DeepMuCLI:
    """
    DeepMu命令行接口类

    负责：
    1. 解析命令行参数
    2. 调用相应的预测功能
    3. 格式化输出结果
    4. 错误处理和用户提示
    """

    def __init__(self):
        self.predictor = None
        self.setup_predictor()

    def setup_predictor(self):
        """初始化预测器"""
        try:
            self.predictor = DeepMuPredictor()
            print("✅ DeepMu预测器初始化成功")
        except Exception as e:
            print(f"❌ 预测器初始化失败: {e}")
            sys.exit(1)

    def predict_single_genome(self, genome_file, output_format='json'):
        """
        预测单个基因组

        参数:
            genome_file: 基因组文件路径 (.fasta格式)
            output_format: 输出格式 ('json', 'table', 'simple')
        """
        print(f"🧬 分析基因组文件: {genome_file}")

        # 验证输入文件
        if not validate_input_file(genome_file):
            print(f"❌ 无效的输入文件: {genome_file}")
            return None

        try:
            # 执行预测
            result = self.predictor.predict_from_file(genome_file)

            # 格式化输出
            if output_format == 'json':
                return json.dumps(result, indent=2, ensure_ascii=False)
            elif output_format == 'table':
                return self.format_table_output(result)
            elif output_format == 'simple':
                return self.format_simple_output(result)

        except Exception as e:
            print(f"❌ 预测过程出错: {e}")
            return None

    def format_simple_output(self, result):
        """格式化简单输出"""
        if 'growth_rate' in result and 'optimal_temperature' in result:
            return (f"生长速率: {result['growth_rate']:.4f} h⁻¹\n"
                   f"最适温度: {result['optimal_temperature']:.1f} °C")
        elif 'growth_rate' in result:
            return f"生长速率: {result['growth_rate']:.4f} h⁻¹"
        elif 'optimal_temperature' in result:
            return f"最适温度: {result['optimal_temperature']:.1f} °C"
        else:
            return "预测结果不可用"

    def format_table_output(self, result):
        """格式化表格输出"""
        lines = ["=" * 50]
        lines.append("DeepMu 预测结果")
        lines.append("=" * 50)

        if 'growth_rate' in result:
            lines.append(f"生长速率:     {result['growth_rate']:.4f} h⁻¹")

            # 添加生长速率解释
            gr = result['growth_rate']
            if gr > 0.5:
                interpretation = "快速生长"
            elif gr > 0.2:
                interpretation = "中等生长"
            else:
                interpretation = "缓慢生长"
            lines.append(f"生长特性:     {interpretation}")

        if 'optimal_temperature' in result:
            lines.append(f"最适温度:     {result['optimal_temperature']:.1f} °C")

            # 添加温度分类
            temp = result['optimal_temperature']
            if temp < 20:
                temp_class = "嗜冷菌"
            elif temp < 45:
                temp_class = "中温菌"
            elif temp < 80:
                temp_class = "嗜热菌"
            else:
                temp_class = "超嗜热菌"
            lines.append(f"温度分类:     {temp_class}")

        if 'confidence' in result:
            lines.append(f"预测置信度:   {result['confidence']:.3f}")

        lines.append("=" * 50)
        return "\n".join(lines)

def create_argument_parser():
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description="DeepMu: 微生物生长预测工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 预测单个基因组
  python deepmu_cli.py predict genome.fasta

  # 批量预测并保存结果
  python deepmu_cli.py batch-predict genomes/ --output results.json

  # 查看模型信息
  python deepmu_cli.py info

  # 获取帮助
  python deepmu_cli.py --help
        """
    )

    subparsers = parser.add_subparsers(dest='command', help='可用命令')

    # 单个预测命令
    predict_parser = subparsers.add_parser('predict', help='预测单个基因组')
    predict_parser.add_argument('genome_file', help='基因组文件路径 (.fasta)')
    predict_parser.add_argument('--output-format', choices=['json', 'table', 'simple'],
                               default='table', help='输出格式')
    predict_parser.add_argument('--output', help='输出文件路径')

    # 批量预测命令
    batch_parser = subparsers.add_parser('batch-predict', help='批量预测基因组')
    batch_parser.add_argument('input_dir', help='包含基因组文件的目录')
    batch_parser.add_argument('--output', required=True, help='输出文件路径')
    batch_parser.add_argument('--format', choices=['json', 'csv'], default='json', help='输出格式')

    # 模型信息命令
    info_parser = subparsers.add_parser('info', help='显示模型信息')

    return parser

def main():
    """主函数"""
    parser = create_argument_parser()
    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        return

    # 创建CLI实例
    cli = DeepMuCLI()

    if args.command == 'predict':
        # 单个预测
        result = cli.predict_single_genome(args.genome_file, args.output_format)

        if result:
            if args.output:
                with open(args.output, 'w', encoding='utf-8') as f:
                    f.write(result)
                print(f"✅ 结果已保存到: {args.output}")
            else:
                print(result)

    elif args.command == 'batch-predict':
        # 批量预测
        print("🚀 开始批量预测...")
        # 这里会调用批量预测功能
        print("批量预测功能开发中...")

    elif args.command == 'info':
        # 显示模型信息
        print("📊 DeepMu模型信息:")
        print("  版本: 2.0.0")
        print("  模型类型: 多任务深度神经网络")
        print("  预测目标: 生长速率、最适温度")
        print("  输入格式: FASTA基因组文件")

if __name__ == "__main__":
    main()
```

#### 5.2.2 核心预测器 (deepmu/predictors/predictor.py)

这是DeepMu的核心预测逻辑：

```python
# 文件位置: deepmu/predictors/predictor.py

import torch
import numpy as np
import pandas as pd
from pathlib import Path
import pickle
import logging

# 导入DeepMu组件
from ..models.networks import MultiTaskModel, GrowthRateModel, TemperatureModel
from ..features.feature_extractor import GenomeFeatureExtractor
from ..utils.data_utils import preprocess_features

class DeepMuPredictor:
    """
    DeepMu核心预测器

    功能：
    1. 加载训练好的模型
    2. 处理输入基因组数据
    3. 执行预测
    4. 返回格式化结果

    支持的模型类型：
    - 多任务模型：同时预测生长速率和温度
    - 单任务模型：只预测生长速率或温度
    """

    def __init__(self, model_dir="models/deepmu_final_model"):
        """
        初始化预测器

        参数:
            model_dir: 模型目录路径
        """
        self.model_dir = Path(model_dir)
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

        # 初始化组件
        self.feature_extractor = GenomeFeatureExtractor()
        self.model = None
        self.scaler = None
        self.model_type = None

        # 加载模型
        self.load_model()

        # 设置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

    def load_model(self):
        """
        加载训练好的模型和预处理器
        """
        try:
            # 检测模型类型
            if (self.model_dir / "multi_task_model.pt").exists():
                self.model_type = "multi_task"
                model_file = self.model_dir / "multi_task_model.pt"
            elif (self.model_dir / "growth_rate_model.pt").exists():
                self.model_type = "growth_rate"
                model_file = self.model_dir / "growth_rate_model.pt"
            elif (self.model_dir / "temperature_model.pt").exists():
                self.model_type = "temperature"
                model_file = self.model_dir / "temperature_model.pt"
            else:
                # 兼容旧版本
                model_file = self.model_dir / "growth_model.pt"
                self.model_type = "legacy"

            # 加载模型
            self.model = torch.load(model_file, map_location=self.device)
            self.model.eval()

            # 加载数据预处理器
            scaler_file = self.model_dir / "scalers.pt"
            if scaler_file.exists():
                self.scaler = torch.load(scaler_file, map_location=self.device)

            self.logger.info(f"✅ 成功加载{self.model_type}模型")

        except Exception as e:
            self.logger.error(f"❌ 模型加载失败: {e}")
            raise

    def extract_features_from_file(self, genome_file):
        """
        从基因组文件提取特征

        参数:
            genome_file: 基因组文件路径 (.fasta格式)

        返回:
            features: 特征向量 (numpy array)
        """
        try:
            # 使用特征提取器处理基因组文件
            features = self.feature_extractor.extract_from_file(genome_file)

            # 转换为numpy数组
            if isinstance(features, dict):
                # 如果返回的是字典，转换为向量
                feature_vector = np.array(list(features.values()))
            else:
                feature_vector = np.array(features)

            return feature_vector.reshape(1, -1)  # 添加batch维度

        except Exception as e:
            self.logger.error(f"特征提取失败: {e}")
            raise

    def predict_from_features(self, features):
        """
        从特征向量进行预测

        参数:
            features: 特征向量 (numpy array)

        返回:
            predictions: 预测结果字典
        """
        try:
            # 预处理特征
            if self.scaler:
                features = self.scaler['feature_scaler'].transform(features)

            # 转换为PyTorch张量
            features_tensor = torch.FloatTensor(features).to(self.device)

            # 执行预测
            with torch.no_grad():
                outputs = self.model(features_tensor)

            # 解析预测结果
            predictions = self.parse_model_output(outputs)

            return predictions

        except Exception as e:
            self.logger.error(f"预测执行失败: {e}")
            raise

    def parse_model_output(self, outputs):
        """
        解析模型输出

        参数:
            outputs: 模型原始输出

        返回:
            predictions: 格式化的预测结果
        """
        predictions = {}

        if self.model_type == "multi_task":
            # 多任务模型输出两个值
            if isinstance(outputs, tuple):
                growth_output, temp_output = outputs
            else:
                # 假设输出是[growth_rate, temperature]
                growth_output = outputs[:, 0:1]
                temp_output = outputs[:, 1:2]

            # 反标准化
            if self.scaler and 'growth_scaler' in self.scaler:
                growth_rate = self.scaler['growth_scaler'].inverse_transform(
                    growth_output.cpu().numpy()
                )[0, 0]
            else:
                growth_rate = growth_output.cpu().numpy()[0, 0]

            if self.scaler and 'temp_scaler' in self.scaler:
                temperature = self.scaler['temp_scaler'].inverse_transform(
                    temp_output.cpu().numpy()
                )[0, 0]
            else:
                temperature = temp_output.cpu().numpy()[0, 0]

            predictions['growth_rate'] = float(growth_rate)
            predictions['optimal_temperature'] = float(temperature)

        elif self.model_type == "growth_rate":
            # 只预测生长速率
            if self.scaler and 'target_scaler' in self.scaler:
                growth_rate = self.scaler['target_scaler'].inverse_transform(
                    outputs.cpu().numpy()
                )[0, 0]
            else:
                growth_rate = outputs.cpu().numpy()[0, 0]

            predictions['growth_rate'] = float(growth_rate)

        elif self.model_type == "temperature":
            # 只预测温度
            if self.scaler and 'target_scaler' in self.scaler:
                temperature = self.scaler['target_scaler'].inverse_transform(
                    outputs.cpu().numpy()
                )[0, 0]
            else:
                temperature = outputs.cpu().numpy()[0, 0]

            predictions['optimal_temperature'] = float(temperature)

        # 添加置信度估计（简化版）
        predictions['confidence'] = self.estimate_confidence(outputs)

        return predictions

    def estimate_confidence(self, outputs):
        """
        估计预测置信度（简化版）

        在实际应用中，可以使用更复杂的不确定性估计方法
        """
        # 简单的置信度估计：基于输出值的稳定性
        output_std = torch.std(outputs).item()
        confidence = max(0.5, 1.0 - output_std)
        return confidence

    def predict_from_file(self, genome_file):
        """
        从基因组文件进行完整预测

        这是主要的预测接口
        """
        self.logger.info(f"开始预测基因组: {genome_file}")

        # 提取特征
        features = self.extract_features_from_file(genome_file)

        # 执行预测
        predictions = self.predict_from_features(features)

        # 添加元信息
        predictions['input_file'] = str(genome_file)
        predictions['model_type'] = self.model_type
        predictions['prediction_time'] = pd.Timestamp.now().isoformat()

        self.logger.info("预测完成")

        return predictions

# 使用示例
def demonstrate_predictor():
    """
    演示预测器的使用方法
    """
    print("=== DeepMu预测器使用演示 ===\n")

    try:
        # 创建预测器实例
        predictor = DeepMuPredictor()

        print(f"模型类型: {predictor.model_type}")
        print(f"设备: {predictor.device}")

        # 模拟特征向量（实际使用中从基因组文件提取）
        dummy_features = np.random.randn(1, 1516)  # 1516是实际特征维度

        # 执行预测
        predictions = predictor.predict_from_features(dummy_features)

        print("\n预测结果:")
        for key, value in predictions.items():
            if isinstance(value, float):
                print(f"  {key}: {value:.4f}")
            else:
                print(f"  {key}: {value}")

    except Exception as e:
        print(f"演示失败: {e}")

if __name__ == "__main__":
    demonstrate_predictor()
```

### 5.3 训练系统详解

DeepMu提供了三种训练模式，每种都有其特定的应用场景：

#### 5.3.1 多任务训练 (train_multi_task_model.py)

多任务学习是DeepMu的核心创新，同时预测生长速率和温度：

```python
# 文件位置: train_multi_task_model.py

"""
DeepMu 多任务训练脚本

核心思想：
1. 生长速率和温度相关 - 共享底层特征表示
2. 联合训练提高数据利用效率
3. 正则化效应提高泛化能力

网络架构：
输入特征 → 共享编码器 → 分支到两个任务头
                      ├── 生长速率预测头
                      └── 温度预测头
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import RobustScaler, MinMaxScaler
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error

class MultiTaskModel(nn.Module):
    """
    多任务神经网络模型

    架构设计理念：
    1. 共享特征提取器 - 学习通用的基因组表示
    2. 任务特定头 - 针对不同任务的专门处理
    3. 适当的正则化 - 防止过拟合
    """

    def __init__(self, input_dim, shared_dims, growth_dims, temp_dims, dropout_rate=0.3):
        super(MultiTaskModel, self).__init__()

        # 共享特征提取器
        self.shared_layers = self._build_shared_encoder(input_dim, shared_dims, dropout_rate)

        # 生长速率预测头
        self.growth_head = self._build_task_head(shared_dims[-1], growth_dims, dropout_rate)

        # 温度预测头（带约束）
        self.temp_head = self._build_task_head(shared_dims[-1], temp_dims, dropout_rate)

        # 温度输出约束层
        self.temp_constraint = nn.Sequential(
            nn.Sigmoid(),  # 输出到[0,1]
            # 然后线性变换到温度范围[0, 100]°C
        )

    def _build_shared_encoder(self, input_dim, hidden_dims, dropout_rate):
        """构建共享编码器"""
        layers = []
        prev_dim = input_dim

        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.BatchNorm1d(hidden_dim),
                nn.GELU(),  # 平滑的激活函数
                nn.Dropout(dropout_rate)
            ])
            prev_dim = hidden_dim

        return nn.Sequential(*layers)

    def _build_task_head(self, input_dim, hidden_dims, dropout_rate):
        """构建任务特定头"""
        layers = []
        prev_dim = input_dim

        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.BatchNorm1d(hidden_dim),
                nn.GELU(),
                nn.Dropout(dropout_rate)
            ])
            prev_dim = hidden_dim

        # 输出层
        layers.append(nn.Linear(prev_dim, 1))

        return nn.Sequential(*layers)

    def forward(self, x):
        """前向传播"""
        # 共享特征提取
        shared_features = self.shared_layers(x)

        # 任务特定预测
        growth_output = self.growth_head(shared_features)
        temp_output = self.temp_head(shared_features)

        # 温度约束到合理范围
        temp_output = self.temp_constraint(temp_output) * 100  # [0, 100]°C

        return growth_output, temp_output

class MultiTaskTrainer:
    """
    多任务训练器

    负责：
    1. 数据加载和预处理
    2. 模型训练和验证
    3. 损失函数设计
    4. 性能评估
    """

    def __init__(self, config):
        self.config = config
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

        # 初始化组件
        self.model = None
        self.optimizer = None
        self.scheduler = None
        self.scalers = {}

        # 训练历史
        self.training_history = {
            'train_total_loss': [],
            'train_growth_loss': [],
            'train_temp_loss': [],
            'val_total_loss': [],
            'val_growth_loss': [],
            'val_temp_loss': [],
            'train_growth_r2': [],
            'train_temp_r2': [],
            'val_growth_r2': [],
            'val_temp_r2': [],
            'learning_rate': []
        }

    def load_data(self):
        """
        加载和预处理数据

        数据格式：
        - features: 基因组特征矩阵 (n_samples, n_features)
        - targets: 目标变量 (growth_rate, optimal_temperature)
        """
        print("📊 加载训练数据...")

        # 加载特征数据
        features_df = pd.read_csv(self.config['data']['feature_file'],
                                 sep='\t', index_col=0)

        # 加载目标变量
        metadata_df = pd.read_csv(self.config['data']['metadata_file'],
                                 sep='\t', index_col=0)

        # 获取共同样本
        common_samples = features_df.index.intersection(metadata_df.index)

        X = features_df.loc[common_samples].fillna(0).values
        y_growth = metadata_df.loc[common_samples, 'growth_rate'].values
        y_temp = metadata_df.loc[common_samples, 'optimal_temperature'].values

        # 数据清理
        valid_mask = (~np.isnan(y_growth)) & (~np.isnan(y_temp))
        X = X[valid_mask]
        y_growth = y_growth[valid_mask]
        y_temp = y_temp[valid_mask]

        print(f"  有效样本数: {len(X)}")
        print(f"  特征维度: {X.shape[1]}")
        print(f"  生长速率范围: {y_growth.min():.4f} - {y_growth.max():.4f}")
        print(f"  温度范围: {y_temp.min():.1f} - {y_temp.max():.1f}°C")

        return X, y_growth, y_temp

    def preprocess_data(self, X, y_growth, y_temp):
        """数据预处理"""
        print("🔧 数据预处理...")

        # 特征标准化
        feature_scaler = RobustScaler()
        X_scaled = feature_scaler.fit_transform(X)

        # 目标变量标准化
        growth_scaler = RobustScaler()
        temp_scaler = MinMaxScaler(feature_range=(0, 1))  # 温度用MinMax，配合Sigmoid

        y_growth_scaled = growth_scaler.fit_transform(y_growth.reshape(-1, 1)).flatten()
        y_temp_scaled = temp_scaler.fit_transform(y_temp.reshape(-1, 1)).flatten()

        # 保存标准化器
        self.scalers = {
            'feature_scaler': feature_scaler,
            'growth_scaler': growth_scaler,
            'temp_scaler': temp_scaler
        }

        return X_scaled, y_growth_scaled, y_temp_scaled

    def create_model(self, input_dim):
        """创建模型"""
        model_config = self.config['model']

        self.model = MultiTaskModel(
            input_dim=input_dim,
            shared_dims=model_config['shared_dims'],
            growth_dims=model_config['growth_dims'],
            temp_dims=model_config['temp_dims'],
            dropout_rate=model_config['dropout_rate']
        ).to(self.device)

        # 优化器
        self.optimizer = optim.Adam(
            self.model.parameters(),
            lr=self.config['training']['learning_rate'],
            weight_decay=self.config['training']['weight_decay']
        )

        # 学习率调度器
        self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
            self.optimizer,
            T_max=self.config['training']['epochs']
        )

        print(f"📱 模型已创建，参数数量: {sum(p.numel() for p in self.model.parameters()):,}")

    def compute_loss(self, growth_pred, temp_pred, growth_true, temp_true):
        """
        计算多任务损失

        损失函数设计：
        1. 每个任务使用适合的损失函数
        2. 加权组合不同任务的损失
        3. 考虑任务难度平衡
        """
        # 生长速率损失 (MSE)
        growth_loss = nn.MSELoss()(growth_pred.squeeze(), growth_true)

        # 温度损失 (MAE，对异常值更鲁棒)
        temp_loss = nn.L1Loss()(temp_pred.squeeze(), temp_true * 100)  # 反标准化

        # 任务权重
        growth_weight = self.config['task_weights']['growth']
        temp_weight = self.config['task_weights']['temperature']

        # 总损失
        total_loss = growth_weight * growth_loss + temp_weight * temp_loss

        return total_loss, growth_loss, temp_loss

    def train_epoch(self, train_loader):
        """训练一个epoch"""
        self.model.train()

        total_loss = 0
        growth_loss_sum = 0
        temp_loss_sum = 0

        all_growth_pred = []
        all_growth_true = []
        all_temp_pred = []
        all_temp_true = []

        for batch_X, batch_y_growth, batch_y_temp in train_loader:
            batch_X = batch_X.to(self.device)
            batch_y_growth = batch_y_growth.to(self.device)
            batch_y_temp = batch_y_temp.to(self.device)

            # 前向传播
            growth_pred, temp_pred = self.model(batch_X)

            # 计算损失
            loss, growth_loss, temp_loss = self.compute_loss(
                growth_pred, temp_pred, batch_y_growth, batch_y_temp
            )

            # 反向传播
            self.optimizer.zero_grad()
            loss.backward()

            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(
                self.model.parameters(),
                self.config['training']['gradient_clip']
            )

            self.optimizer.step()

            # 累计损失
            total_loss += loss.item()
            growth_loss_sum += growth_loss.item()
            temp_loss_sum += temp_loss.item()

            # 收集预测结果用于计算R²
            all_growth_pred.extend(growth_pred.detach().cpu().numpy().flatten())
            all_growth_true.extend(batch_y_growth.detach().cpu().numpy())
            all_temp_pred.extend(temp_pred.detach().cpu().numpy().flatten())
            all_temp_true.extend((batch_y_temp * 100).detach().cpu().numpy())

        # 计算R²
        growth_r2 = r2_score(all_growth_true, all_growth_pred)
        temp_r2 = r2_score(all_temp_true, all_temp_pred)

        return (total_loss / len(train_loader),
                growth_loss_sum / len(train_loader),
                temp_loss_sum / len(train_loader),
                growth_r2, temp_r2)

# 使用示例
def demonstrate_multi_task_training():
    """
    演示多任务训练过程
    """
    print("=== 多任务训练演示 ===\n")

    # 配置示例
    config = {
        'data': {
            'feature_file': 'training_data/combined_features.tsv',
            'metadata_file': 'training_data/metadata.tsv'
        },
        'model': {
            'shared_dims': [512, 256],
            'growth_dims': [128, 64],
            'temp_dims': [128, 64],
            'dropout_rate': 0.3
        },
        'training': {
            'learning_rate': 0.001,
            'weight_decay': 1e-4,
            'gradient_clip': 1.0,
            'epochs': 300
        },
        'task_weights': {
            'growth': 1.0,
            'temperature': 1.0
        }
    }

    print("多任务学习的优势：")
    print("1. 数据效率：两个任务共享特征表示，提高数据利用率")
    print("2. 正则化：任务间的约束减少过拟合")
    print("3. 特征学习：学习更通用的基因组表示")
    print("4. 计算效率：一个模型解决两个问题")

    print(f"\n模型配置：")
    print(f"  共享层: {config['model']['shared_dims']}")
    print(f"  生长速率头: {config['model']['growth_dims']}")
    print(f"  温度预测头: {config['model']['temp_dims']}")
    print(f"  任务权重: 生长速率={config['task_weights']['growth']}, 温度={config['task_weights']['temperature']}")

demonstrate_multi_task_training()
```

### 5.4 可视化系统详解

DeepMu的可视化系统提供了全面的分析和解释功能：

#### 5.4.1 训练过程可视化

```python
# 文件位置: visualization/training/training_visualizer.py

"""
训练过程可视化器

功能：
1. 训练曲线可视化 - 损失函数、性能指标变化
2. 多任务分析 - 不同任务的性能对比
3. 交叉验证结果 - 模型稳定性分析
4. 自动报告生成 - 详细的训练分析报告
"""

import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import json
from pathlib import Path

class TrainingVisualizer:
    """训练过程可视化器"""

    def __init__(self, model_dir, output_dir=None):
        self.model_dir = Path(model_dir)
        self.output_dir = Path(output_dir) if output_dir else self.model_dir / "visualizations"
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # 设置中文字体和样式
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        sns.set_style("whitegrid")

        # 加载训练历史
        self.history = self._load_training_history()
        self.model_type = self._detect_model_type()

    def create_training_curves(self):
        """
        创建训练曲线图

        根据模型类型生成不同的可视化：
        - 多任务模型：显示总损失、任务特定损失、性能指标
        - 单任务模型：显示交叉验证结果
        """
        if self.model_type == 'multi_task':
            self._plot_multi_task_curves()
        elif self.model_type in ['growth_rate', 'temperature']:
            self._plot_single_task_curves()

    def _plot_multi_task_curves(self):
        """绘制多任务训练曲线"""
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('DeepMu 多任务训练过程可视化', fontsize=16, fontweight='bold')

        epochs = range(1, len(self.history['train_total_loss']) + 1)

        # 1. 总损失变化
        axes[0, 0].plot(epochs, self.history['train_total_loss'],
                       label='训练总损失', linewidth=2, color='blue')
        axes[0, 0].plot(epochs, self.history['val_total_loss'],
                       label='验证总损失', linewidth=2, color='red')
        axes[0, 0].set_title('总损失变化')
        axes[0, 0].set_xlabel('轮次')
        axes[0, 0].set_ylabel('损失')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)

        # 2. 生长速率损失
        axes[0, 1].plot(epochs, self.history['train_growth_loss'],
                       label='训练生长损失', linewidth=2, color='green')
        axes[0, 1].plot(epochs, self.history['val_growth_loss'],
                       label='验证生长损失', linewidth=2, color='orange')
        axes[0, 1].set_title('生长速率损失')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)

        # 3. 温度损失
        axes[0, 2].plot(epochs, self.history['train_temp_loss'],
                       label='训练温度损失', linewidth=2, color='purple')
        axes[0, 2].plot(epochs, self.history['val_temp_loss'],
                       label='验证温度损失', linewidth=2, color='brown')
        axes[0, 2].set_title('温度预测损失')
        axes[0, 2].legend()
        axes[0, 2].grid(True, alpha=0.3)

        # 4. 生长速率R²
        axes[1, 0].plot(epochs, self.history['train_growth_r2'],
                       label='训练生长R²', linewidth=2, color='green')
        axes[1, 0].plot(epochs, self.history['val_growth_r2'],
                       label='验证生长R²', linewidth=2, color='orange')
        axes[1, 0].set_title('生长速率R²')
        axes[1, 0].set_ylabel('R²')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)

        # 5. 温度R²
        axes[1, 1].plot(epochs, self.history['train_temp_r2'],
                       label='训练温度R²', linewidth=2, color='purple')
        axes[1, 1].plot(epochs, self.history['val_temp_r2'],
                       label='验证温度R²', linewidth=2, color='brown')
        axes[1, 1].set_title('温度预测R²')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)

        # 6. 学习率变化
        axes[1, 2].plot(epochs, self.history['learning_rate'],
                       linewidth=2, color='red')
        axes[1, 2].set_title('学习率变化')
        axes[1, 2].set_ylabel('学习率')
        axes[1, 2].set_yscale('log')
        axes[1, 2].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(self.output_dir / 'multi_task_training_curves.png',
                   dpi=300, bbox_inches='tight')
        plt.close()

        print("✅ 多任务训练曲线已保存")

# 使用示例
def demonstrate_visualization():
    """演示可视化系统的使用"""
    print("=== 可视化系统演示 ===\n")

    print("可视化系统功能：")
    print("1. 训练过程可视化")
    print("   - 损失函数变化曲线")
    print("   - 性能指标变化曲线")
    print("   - 学习率调度可视化")
    print("   - 早停分析")

    print("\n2. 预测结果可视化")
    print("   - 预测值分布分析")
    print("   - 预测准确性散点图")
    print("   - 误差分析图")
    print("   - 置信区间分析")

    print("\n3. 可解释性分析")
    print("   - 特征重要性排序")
    print("   - 特征相关性热图")
    print("   - 功能分类分析")
    print("   - 生物学解释指导")

    print("\n使用方法：")
    print("# 训练过程可视化")
    print("./visualization/run_visualization.sh training models/deepmu_multi_task")
    print("\n# 可解释性分析")
    print("./visualization/run_visualization.sh interpretability \\")
    print("    models/deepmu_multi_task training_data/combined_features.tsv")

demonstrate_visualization()
```

---

## 6. 数据处理与特征工程

### 6.1 数据流水线设计

```python
# 文件位置: deepmu/utils/data_pipeline.py

"""
DeepMu数据处理流水线

数据流程：
原始基因组文件 → 序列解析 → 特征提取 → 数据清理 → 标准化 → 模型输入

关键步骤：
1. 基因组序列解析
2. 多层次特征提取
3. 数据质量控制
4. 特征选择和降维
5. 数据标准化
"""

import pandas as pd
import numpy as np
from sklearn.preprocessing import RobustScaler, StandardScaler
from sklearn.feature_selection import SelectKBest, f_regression
from sklearn.decomposition import PCA
import logging

class DataPipeline:
    """
    数据处理流水线

    设计原则：
    1. 模块化 - 每个步骤独立可测试
    2. 可配置 - 支持不同的处理策略
    3. 可重现 - 保存所有预处理参数
    4. 鲁棒性 - 处理各种数据质量问题
    """

    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger(__name__)

        # 预处理组件
        self.feature_scaler = None
        self.target_scaler = None
        self.feature_selector = None
        self.dimensionality_reducer = None

        # 数据统计
        self.data_stats = {}

    def load_raw_data(self):
        """
        加载原始数据

        数据来源：
        1. 基因组特征文件 - 从FASTA文件提取的特征
        2. 元数据文件 - 包含生长速率和温度的实验数据
        """
        self.logger.info("📊 加载原始数据...")

        # 加载特征数据
        features_df = pd.read_csv(
            self.config['feature_file'],
            sep='\t',
            index_col=0
        )

        # 加载目标变量
        metadata_df = pd.read_csv(
            self.config['metadata_file'],
            sep='\t',
            index_col=0
        )

        # 数据对齐
        common_samples = features_df.index.intersection(metadata_df.index)

        self.logger.info(f"  特征文件样本数: {len(features_df)}")
        self.logger.info(f"  元数据文件样本数: {len(metadata_df)}")
        self.logger.info(f"  共同样本数: {len(common_samples)}")

        return features_df.loc[common_samples], metadata_df.loc[common_samples]

    def quality_control(self, features_df, metadata_df):
        """
        数据质量控制

        检查项目：
        1. 缺失值处理
        2. 异常值检测
        3. 数据类型验证
        4. 范围合理性检查
        """
        self.logger.info("🔍 数据质量控制...")

        # 1. 缺失值统计
        feature_missing = features_df.isnull().sum()
        target_missing = metadata_df.isnull().sum()

        self.logger.info(f"  特征缺失值: {feature_missing.sum()} / {features_df.size}")
        self.logger.info(f"  目标缺失值: {target_missing.sum()} / {metadata_df.size}")

        # 2. 异常值检测（使用IQR方法）
        def detect_outliers(series, factor=1.5):
            Q1 = series.quantile(0.25)
            Q3 = series.quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - factor * IQR
            upper_bound = Q3 + factor * IQR
            return (series < lower_bound) | (series > upper_bound)

        outlier_stats = {}
        for col in ['growth_rate', 'optimal_temperature']:
            if col in metadata_df.columns:
                outliers = detect_outliers(metadata_df[col].dropna())
                outlier_stats[col] = outliers.sum()
                self.logger.info(f"  {col} 异常值: {outliers.sum()}")

        # 3. 数据范围检查
        if 'growth_rate' in metadata_df.columns:
            gr_range = metadata_df['growth_rate'].describe()
            self.logger.info(f"  生长速率范围: {gr_range['min']:.4f} - {gr_range['max']:.4f}")

        if 'optimal_temperature' in metadata_df.columns:
            temp_range = metadata_df['optimal_temperature'].describe()
            self.logger.info(f"  温度范围: {temp_range['min']:.1f} - {temp_range['max']:.1f}°C")

        # 保存质量统计
        self.data_stats['quality'] = {
            'feature_missing': feature_missing.to_dict(),
            'target_missing': target_missing.to_dict(),
            'outliers': outlier_stats
        }

        return features_df, metadata_df

    def feature_engineering(self, features_df):
        """
        特征工程

        步骤：
        1. 特征清理和填充
        2. 特征变换
        3. 特征选择
        4. 降维（可选）
        """
        self.logger.info("🔧 特征工程...")

        # 1. 缺失值处理
        features_clean = features_df.fillna(0)  # 基因组特征缺失通常表示不存在

        # 2. 移除低方差特征
        feature_var = features_clean.var()
        low_var_features = feature_var[feature_var < 1e-6].index
        if len(low_var_features) > 0:
            features_clean = features_clean.drop(columns=low_var_features)
            self.logger.info(f"  移除低方差特征: {len(low_var_features)}")

        # 3. 特征选择（可选）
        if self.config.get('feature_selection', {}).get('enabled', False):
            n_features = self.config['feature_selection']['n_features']
            self.feature_selector = SelectKBest(f_regression, k=n_features)

            # 需要目标变量进行特征选择，这里简化处理
            self.logger.info(f"  特征选择: 保留前{n_features}个特征")

        # 4. 降维（可选）
        if self.config.get('dimensionality_reduction', {}).get('enabled', False):
            n_components = self.config['dimensionality_reduction']['n_components']
            self.dimensionality_reducer = PCA(n_components=n_components)

            features_reduced = self.dimensionality_reducer.fit_transform(features_clean)
            features_clean = pd.DataFrame(
                features_reduced,
                index=features_clean.index,
                columns=[f'PC{i+1}' for i in range(n_components)]
            )
            self.logger.info(f"  PCA降维: {features_df.shape[1]} → {n_components}")

        self.logger.info(f"  最终特征维度: {features_clean.shape}")

        return features_clean

    def normalize_data(self, features_df, targets_df):
        """
        数据标准化

        策略：
        1. 特征标准化 - RobustScaler（对异常值鲁棒）
        2. 目标标准化 - 根据任务选择合适的标准化方法
        """
        self.logger.info("📏 数据标准化...")

        # 特征标准化
        self.feature_scaler = RobustScaler()
        features_scaled = self.feature_scaler.fit_transform(features_df)
        features_scaled_df = pd.DataFrame(
            features_scaled,
            index=features_df.index,
            columns=features_df.columns
        )

        # 目标变量标准化
        targets_scaled_df = targets_df.copy()

        if 'growth_rate' in targets_df.columns:
            growth_scaler = RobustScaler()
            targets_scaled_df['growth_rate'] = growth_scaler.fit_transform(
                targets_df[['growth_rate']]
            ).flatten()

            if not hasattr(self, 'target_scalers'):
                self.target_scalers = {}
            self.target_scalers['growth_rate'] = growth_scaler

        if 'optimal_temperature' in targets_df.columns:
            # 温度使用MinMaxScaler，配合模型的Sigmoid输出
            from sklearn.preprocessing import MinMaxScaler
            temp_scaler = MinMaxScaler(feature_range=(0, 1))
            targets_scaled_df['optimal_temperature'] = temp_scaler.fit_transform(
                targets_df[['optimal_temperature']]
            ).flatten()

            if not hasattr(self, 'target_scalers'):
                self.target_scalers = {}
            self.target_scalers['optimal_temperature'] = temp_scaler

        self.logger.info("  标准化完成")

        return features_scaled_df, targets_scaled_df

    def process(self):
        """
        执行完整的数据处理流水线
        """
        self.logger.info("🚀 开始数据处理流水线...")

        # 1. 加载数据
        features_df, metadata_df = self.load_raw_data()

        # 2. 质量控制
        features_df, metadata_df = self.quality_control(features_df, metadata_df)

        # 3. 特征工程
        features_processed = self.feature_engineering(features_df)

        # 4. 数据标准化
        features_final, targets_final = self.normalize_data(features_processed, metadata_df)

        self.logger.info("✅ 数据处理流水线完成")

        return features_final, targets_final

# 使用示例
def demonstrate_data_pipeline():
    """演示数据处理流水线"""
    print("=== 数据处理流水线演示 ===\n")

    config = {
        'feature_file': 'training_data/combined_features.tsv',
        'metadata_file': 'training_data/metadata.tsv',
        'feature_selection': {
            'enabled': False,
            'n_features': 1000
        },
        'dimensionality_reduction': {
            'enabled': False,
            'n_components': 100
        }
    }

    print("数据处理流程：")
    print("1. 原始数据加载")
    print("   - 基因组特征文件 (TSV格式)")
    print("   - 元数据文件 (包含生长速率和温度)")
    print("   - 样本对齐和匹配")

    print("\n2. 数据质量控制")
    print("   - 缺失值统计和处理")
    print("   - 异常值检测 (IQR方法)")
    print("   - 数据范围合理性检查")

    print("\n3. 特征工程")
    print("   - 低方差特征移除")
    print("   - 特征选择 (可选)")
    print("   - 主成分分析降维 (可选)")

    print("\n4. 数据标准化")
    print("   - 特征: RobustScaler (对异常值鲁棒)")
    print("   - 生长速率: RobustScaler")
    print("   - 温度: MinMaxScaler (配合Sigmoid输出)")

    print(f"\n配置示例:")
    for key, value in config.items():
        print(f"  {key}: {value}")

demonstrate_data_pipeline()
```

---

## 11. 从零构建项目指南

### 11.1 项目规划和设计

当您理解了DeepMu的所有组件后，可以按照以下步骤构建自己的生物预测项目：

```python
# 第一步：项目规划文档

"""
生物预测项目规划模板

1. 项目定义
   - 预测目标：要预测什么生物特性？
   - 输入数据：有什么类型的生物数据？
   - 应用场景：解决什么实际问题？

2. 数据需求
   - 数据来源：从哪里获取数据？
   - 数据质量：数据的完整性和准确性如何？
   - 数据规模：有多少样本和特征？

3. 技术选型
   - 模型类型：回归、分类、多任务？
   - 深度学习框架：PyTorch、TensorFlow？
   - 评估指标：如何衡量模型性能？

4. 项目结构
   - 代码组织：如何组织代码模块？
   - 数据管理：如何存储和版本控制数据？
   - 实验管理：如何记录和比较实验？
"""

class ProjectPlanner:
    """项目规划助手"""

    def __init__(self, project_name):
        self.project_name = project_name
        self.requirements = {}
        self.architecture = {}

    def define_problem(self, prediction_targets, input_data_types, applications):
        """定义问题"""
        self.requirements['problem'] = {
            'targets': prediction_targets,
            'inputs': input_data_types,
            'applications': applications
        }

        print(f"=== {self.project_name} 项目定义 ===")
        print(f"预测目标: {', '.join(prediction_targets)}")
        print(f"输入数据: {', '.join(input_data_types)}")
        print(f"应用场景: {', '.join(applications)}")

    def plan_data_pipeline(self, data_sources, processing_steps):
        """规划数据流水线"""
        self.requirements['data'] = {
            'sources': data_sources,
            'processing': processing_steps
        }

        print(f"\n=== 数据流水线规划 ===")
        print("数据来源:")
        for source in data_sources:
            print(f"  - {source}")

        print("处理步骤:")
        for step in processing_steps:
            print(f"  - {step}")

    def design_architecture(self, model_type, components):
        """设计系统架构"""
        self.architecture = {
            'model_type': model_type,
            'components': components
        }

        print(f"\n=== 系统架构设计 ===")
        print(f"模型类型: {model_type}")
        print("系统组件:")
        for component, description in components.items():
            print(f"  - {component}: {description}")

# 示例：规划一个新的微生物预测项目
def plan_new_project():
    """规划新项目示例"""
    planner = ProjectPlanner("MicroPredict")

    # 定义问题
    planner.define_problem(
        prediction_targets=['抗生素抗性', '病原性', '代谢产物产量'],
        input_data_types=['基因组序列', '转录组数据', '蛋白质组数据'],
        applications=['药物发现', '临床诊断', '工业生产']
    )

    # 规划数据流水线
    planner.plan_data_pipeline(
        data_sources=[
            'NCBI基因组数据库',
            '实验室测序数据',
            '文献数据挖掘',
            '公共数据库整合'
        ],
        processing_steps=[
            '序列质量控制',
            '基因注释',
            '功能分类',
            '特征提取',
            '数据标准化'
        ]
    )

    # 设计架构
    planner.design_architecture(
        model_type='多分类深度学习模型',
        components={
            '数据处理模块': '基因组数据预处理和特征提取',
            '模型训练模块': '深度学习模型训练和优化',
            '预测服务模块': 'API服务和批量预测',
            '可视化模块': '结果分析和模型解释',
            '评估模块': '性能评估和验证'
        }
    )

plan_new_project()
```

### 11.2 项目搭建步骤

#### 步骤1：创建项目结构

```bash
# 文件位置: setup_project.sh

#!/bin/bash
# 项目结构创建脚本

PROJECT_NAME="MyBioPredict"

echo "🚀 创建项目: $PROJECT_NAME"

# 创建主目录结构
mkdir -p $PROJECT_NAME/{
    src/{models,features,utils,predictors},
    data/{raw,processed,external},
    models/{trained,checkpoints},
    notebooks,
    tests,
    configs,
    scripts,
    docs,
    results/{predictions,visualizations,reports}
}

# 创建核心文件
cat > $PROJECT_NAME/src/__init__.py << 'EOF'
"""
MyBioPredict: 生物特性预测框架

基于DeepMu架构设计的生物预测系统
"""

__version__ = "1.0.0"
__author__ = "Your Name"

from .predictors.predictor import BioPredictor
from .models.networks import PredictionModel
from .features.extractor import FeatureExtractor

__all__ = ['BioPredictor', 'PredictionModel', 'FeatureExtractor']
EOF

# 创建配置文件模板
cat > $PROJECT_NAME/configs/config_template.json << 'EOF'
{
    "data": {
        "raw_data_dir": "data/raw",
        "processed_data_dir": "data/processed",
        "feature_file": "data/processed/features.tsv",
        "target_file": "data/processed/targets.tsv"
    },
    "model": {
        "type": "multi_task",
        "hidden_dims": [512, 256, 128],
        "dropout_rate": 0.3,
        "activation": "gelu"
    },
    "training": {
        "batch_size": 128,
        "learning_rate": 0.001,
        "epochs": 300,
        "patience": 30,
        "validation_split": 0.2
    },
    "evaluation": {
        "metrics": ["r2_score", "rmse", "mae"],
        "cross_validation": {
            "enabled": true,
            "folds": 5
        }
    }
}
EOF

# 创建requirements.txt
cat > $PROJECT_NAME/requirements.txt << 'EOF'
# 核心依赖
torch>=1.7.0
numpy>=1.19.0
pandas>=1.2.0
scikit-learn>=0.24.0

# 生物信息学
biopython>=1.78

# 可视化
matplotlib>=3.3.0
seaborn>=0.11.0
plotly>=5.0.0

# 工具库
tqdm>=4.60.0
pyyaml>=5.4.0
click>=7.1.0

# 开发工具
pytest>=6.2.0
black>=21.0.0
flake8>=3.8.0
jupyter>=1.0.0
EOF

# 创建README模板
cat > $PROJECT_NAME/README.md << 'EOF'
# MyBioPredict

基于深度学习的生物特性预测框架

## 功能特性

- 🧬 多种生物数据类型支持
- 🤖 深度学习模型训练
- 📊 可视化分析
- 🔍 模型可解释性
- 🚀 高性能预测服务

## 快速开始

```bash
# 安装依赖
pip install -r requirements.txt

# 训练模型
python scripts/train_model.py --config configs/config.json

# 进行预测
python scripts/predict.py --input data/test.fasta --output results/predictions.json
```

## 项目结构

```
MyBioPredict/
├── src/                    # 源代码
├── data/                   # 数据文件
├── models/                 # 训练好的模型
├── configs/                # 配置文件
├── scripts/                # 脚本文件
├── tests/                  # 测试文件
└── docs/                   # 文档
```
EOF

echo "✅ 项目结构创建完成: $PROJECT_NAME"
echo "📁 项目目录: $(pwd)/$PROJECT_NAME"
```

#### 步骤2：实现核心组件

```python
# 文件位置: src/models/networks.py

"""
神经网络模型定义

基于DeepMu的设计经验，实现可扩展的模型架构
"""

import torch
import torch.nn as nn
import torch.nn.functional as F

class PredictionModel(nn.Module):
    """
    通用生物预测模型

    设计特点：
    1. 模块化设计 - 易于扩展和修改
    2. 配置驱动 - 通过配置文件控制架构
    3. 多任务支持 - 可同时预测多个目标
    4. 正则化 - 内置多种正则化技术
    """

    def __init__(self, config):
        super(PredictionModel, self).__init__()

        self.config = config
        self.model_type = config['type']

        # 构建网络
        if self.model_type == 'single_task':
            self.network = self._build_single_task_network()
        elif self.model_type == 'multi_task':
            self.shared_encoder = self._build_shared_encoder()
            self.task_heads = self._build_task_heads()
        else:
            raise ValueError(f"不支持的模型类型: {self.model_type}")

    def _build_single_task_network(self):
        """构建单任务网络"""
        layers = []
        input_dim = self.config['input_dim']
        hidden_dims = self.config['hidden_dims']
        dropout_rate = self.config.get('dropout_rate', 0.3)
        activation = self.config.get('activation', 'relu')

        # 隐藏层
        prev_dim = input_dim
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.BatchNorm1d(hidden_dim),
                self._get_activation(activation),
                nn.Dropout(dropout_rate)
            ])
            prev_dim = hidden_dim

        # 输出层
        layers.append(nn.Linear(prev_dim, self.config['output_dim']))

        return nn.Sequential(*layers)

    def _build_shared_encoder(self):
        """构建共享编码器"""
        layers = []
        input_dim = self.config['input_dim']
        shared_dims = self.config['shared_dims']
        dropout_rate = self.config.get('dropout_rate', 0.3)
        activation = self.config.get('activation', 'relu')

        prev_dim = input_dim
        for hidden_dim in shared_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.BatchNorm1d(hidden_dim),
                self._get_activation(activation),
                nn.Dropout(dropout_rate)
            ])
            prev_dim = hidden_dim

        return nn.Sequential(*layers)

    def _build_task_heads(self):
        """构建任务特定头"""
        heads = nn.ModuleDict()
        shared_dim = self.config['shared_dims'][-1]

        for task_name, task_config in self.config['tasks'].items():
            head_layers = []
            prev_dim = shared_dim

            for hidden_dim in task_config['hidden_dims']:
                head_layers.extend([
                    nn.Linear(prev_dim, hidden_dim),
                    nn.BatchNorm1d(hidden_dim),
                    self._get_activation(task_config.get('activation', 'relu')),
                    nn.Dropout(task_config.get('dropout_rate', 0.3))
                ])
                prev_dim = hidden_dim

            # 输出层
            head_layers.append(nn.Linear(prev_dim, task_config['output_dim']))

            # 任务特定的输出约束
            if task_config.get('output_constraint'):
                constraint_type = task_config['output_constraint']['type']
                if constraint_type == 'sigmoid_range':
                    min_val = task_config['output_constraint']['min']
                    max_val = task_config['output_constraint']['max']
                    head_layers.extend([
                        nn.Sigmoid(),
                        ScaleLayer(min_val, max_val)
                    ])

            heads[task_name] = nn.Sequential(*head_layers)

        return heads

    def _get_activation(self, activation_name):
        """获取激活函数"""
        activations = {
            'relu': nn.ReLU(),
            'gelu': nn.GELU(),
            'leaky_relu': nn.LeakyReLU(),
            'elu': nn.ELU(),
            'swish': nn.SiLU()
        }
        return activations.get(activation_name, nn.ReLU())

    def forward(self, x):
        """前向传播"""
        if self.model_type == 'single_task':
            return self.network(x)

        elif self.model_type == 'multi_task':
            shared_features = self.shared_encoder(x)
            outputs = {}

            for task_name, head in self.task_heads.items():
                outputs[task_name] = head(shared_features)

            return outputs

class ScaleLayer(nn.Module):
    """输出缩放层"""

    def __init__(self, min_val, max_val):
        super(ScaleLayer, self).__init__()
        self.min_val = min_val
        self.max_val = max_val

    def forward(self, x):
        return x * (self.max_val - self.min_val) + self.min_val

# 模型配置示例
def create_model_config_examples():
    """创建模型配置示例"""

    # 单任务配置
    single_task_config = {
        'type': 'single_task',
        'input_dim': 1000,
        'hidden_dims': [512, 256, 128],
        'output_dim': 1,
        'dropout_rate': 0.3,
        'activation': 'gelu'
    }

    # 多任务配置
    multi_task_config = {
        'type': 'multi_task',
        'input_dim': 1000,
        'shared_dims': [512, 256],
        'tasks': {
            'growth_rate': {
                'hidden_dims': [128, 64],
                'output_dim': 1,
                'dropout_rate': 0.4,
                'activation': 'gelu'
            },
            'temperature': {
                'hidden_dims': [128, 64],
                'output_dim': 1,
                'dropout_rate': 0.4,
                'activation': 'gelu',
                'output_constraint': {
                    'type': 'sigmoid_range',
                    'min': 0.0,
                    'max': 100.0
                }
            }
        }
    }

    return single_task_config, multi_task_config

# 使用示例
def demonstrate_model_creation():
    """演示模型创建过程"""
    print("=== 模型创建演示 ===\n")

    single_config, multi_config = create_model_config_examples()

    # 创建单任务模型
    print("1. 单任务模型:")
    single_model = PredictionModel(single_config)
    total_params = sum(p.numel() for p in single_model.parameters())
    print(f"   参数数量: {total_params:,}")

    # 创建多任务模型
    print("\n2. 多任务模型:")
    multi_model = PredictionModel(multi_config)
    total_params = sum(p.numel() for p in multi_model.parameters())
    print(f"   参数数量: {total_params:,}")

    # 测试前向传播
    batch_size = 32
    input_dim = 1000
    test_input = torch.randn(batch_size, input_dim)

    print("\n3. 前向传播测试:")
    with torch.no_grad():
        single_output = single_model(test_input)
        multi_output = multi_model(test_input)

        print(f"   单任务输出形状: {single_output.shape}")
        print(f"   多任务输出: {list(multi_output.keys())}")
        for task, output in multi_output.items():
            print(f"     {task}: {output.shape}")

demonstrate_model_creation()
```

### 11.3 完整开发流程

```python
# 文件位置: development_guide.py

"""
完整开发流程指南

这个指南展示了如何从零开始构建一个完整的生物预测项目
"""

class DevelopmentGuide:
    """开发指南"""

    def __init__(self):
        self.phases = {
            '1. 项目初始化': self.phase1_initialization,
            '2. 数据收集与处理': self.phase2_data_processing,
            '3. 模型设计与实现': self.phase3_model_development,
            '4. 训练与优化': self.phase4_training,
            '5. 评估与验证': self.phase5_evaluation,
            '6. 部署与维护': self.phase6_deployment
        }

    def phase1_initialization(self):
        """阶段1：项目初始化"""
        steps = [
            "1.1 需求分析",
            "  - 明确预测目标和应用场景",
            "  - 分析数据可用性和质量",
            "  - 评估技术可行性",
            "",
            "1.2 技术选型",
            "  - 选择深度学习框架 (PyTorch/TensorFlow)",
            "  - 确定开发环境和工具链",
            "  - 设计系统架构",
            "",
            "1.3 项目搭建",
            "  - 创建项目结构",
            "  - 设置版本控制 (Git)",
            "  - 配置开发环境",
            "  - 编写项目文档"
        ]
        return steps

    def phase2_data_processing(self):
        """阶段2：数据收集与处理"""
        steps = [
            "2.1 数据收集",
            "  - 从公共数据库下载数据",
            "  - 整合多个数据源",
            "  - 数据格式标准化",
            "",
            "2.2 数据清理",
            "  - 缺失值处理",
            "  - 异常值检测和处理",
            "  - 数据质量评估",
            "",
            "2.3 特征工程",
            "  - 生物序列特征提取",
            "  - 功能注释特征化",
            "  - 特征选择和降维",
            "",
            "2.4 数据预处理",
            "  - 数据标准化",
            "  - 训练/验证/测试集划分",
            "  - 数据增强 (如果适用)"
        ]
        return steps

    def phase3_model_development(self):
        """阶段3：模型设计与实现"""
        steps = [
            "3.1 模型架构设计",
            "  - 选择网络结构 (全连接/CNN/RNN)",
            "  - 设计损失函数",
            "  - 选择优化算法",
            "",
            "3.2 模型实现",
            "  - 编写模型类",
            "  - 实现前向传播",
            "  - 添加正则化技术",
            "",
            "3.3 训练流程实现",
            "  - 数据加载器",
            "  - 训练循环",
            "  - 验证流程",
            "",
            "3.4 单元测试",
            "  - 模型组件测试",
            "  - 数据流测试",
            "  - 端到端测试"
        ]
        return steps

    def phase4_training(self):
        """阶段4：训练与优化"""
        steps = [
            "4.1 基线模型训练",
            "  - 简单模型快速验证",
            "  - 确定数据流水线正确性",
            "  - 建立评估基准",
            "",
            "4.2 超参数优化",
            "  - 网格搜索/随机搜索",
            "  - 贝叶斯优化",
            "  - 学习率调度",
            "",
            "4.3 模型改进",
            "  - 架构优化",
            "  - 正则化调整",
            "  - 数据增强",
            "",
            "4.4 训练监控",
            "  - 损失曲线可视化",
            "  - 性能指标追踪",
            "  - 早停策略"
        ]
        return steps

    def phase5_evaluation(self):
        """阶段5：评估与验证"""
        steps = [
            "5.1 性能评估",
            "  - 多种评估指标",
            "  - 交叉验证",
            "  - 统计显著性测试",
            "",
            "5.2 模型解释",
            "  - 特征重要性分析",
            "  - 可视化解释",
            "  - 生物学意义验证",
            "",
            "5.3 鲁棒性测试",
            "  - 不同数据集测试",
            "  - 噪声鲁棒性",
            "  - 边界情况测试",
            "",
            "5.4 比较分析",
            "  - 与现有方法对比",
            "  - 消融实验",
            "  - 计算效率分析"
        ]
        return steps

    def phase6_deployment(self):
        """阶段6：部署与维护"""
        steps = [
            "6.1 模型部署",
            "  - API服务开发",
            "  - 容器化部署",
            "  - 性能优化",
            "",
            "6.2 用户界面",
            "  - Web界面开发",
            "  - 命令行工具",
            "  - 文档和教程",
            "",
            "6.3 监控与维护",
            "  - 性能监控",
            "  - 错误日志",
            "  - 模型更新策略",
            "",
            "6.4 社区建设",
            "  - 开源发布",
            "  - 用户反馈收集",
            "  - 持续改进"
        ]
        return steps

    def show_complete_guide(self):
        """显示完整开发指南"""
        print("=== 生物预测项目完整开发指南 ===\n")

        for phase_name, phase_func in self.phases.items():
            print(f"{phase_name}")
            print("=" * len(phase_name))

            steps = phase_func()
            for step in steps:
                print(step)
            print()

    def create_checklist(self):
        """创建开发检查清单"""
        checklist = {
            "项目初始化": [
                "□ 需求文档编写",
                "□ 技术方案设计",
                "□ 项目结构创建",
                "□ 开发环境配置"
            ],
            "数据处理": [
                "□ 数据收集完成",
                "□ 数据清理验证",
                "□ 特征工程实现",
                "□ 数据预处理管道"
            ],
            "模型开发": [
                "□ 模型架构设计",
                "□ 代码实现完成",
                "□ 单元测试通过",
                "□ 集成测试验证"
            ],
            "训练优化": [
                "□ 基线模型训练",
                "□ 超参数优化",
                "□ 性能监控设置",
                "□ 模型收敛验证"
            ],
            "评估验证": [
                "□ 性能评估完成",
                "□ 可解释性分析",
                "□ 鲁棒性测试",
                "□ 对比实验"
            ],
            "部署维护": [
                "□ 服务部署",
                "□ 用户界面",
                "□ 文档完善",
                "□ 监控系统"
            ]
        }

        print("=== 开发检查清单 ===\n")
        for category, items in checklist.items():
            print(f"{category}:")
            for item in items:
                print(f"  {item}")
            print()

# 使用指南
def main():
    """主函数"""
    guide = DevelopmentGuide()

    print("🚀 欢迎使用生物预测项目开发指南！\n")
    print("这个指南将帮助您从零开始构建一个完整的生物预测系统。")
    print("基于DeepMu项目的经验和最佳实践。\n")

    # 显示完整指南
    guide.show_complete_guide()

    # 显示检查清单
    guide.create_checklist()

    print("💡 建议：")
    print("1. 按阶段逐步推进，不要跳跃式开发")
    print("2. 每个阶段都要有明确的交付物和验收标准")
    print("3. 重视数据质量，它比复杂的模型更重要")
    print("4. 保持代码的可读性和可维护性")
    print("5. 及时记录实验结果和经验教训")
    print("6. 与领域专家保持密切合作")

if __name__ == "__main__":
    main()
```

---

## 总结

通过这个完整的学习指南，您已经掌握了：

### 🧬 生物学基础
- 微生物学基本概念
- 基因组学原理
- 生长特性与环境因子的关系

### 💻 技术实现
- 深度学习在生物学中的应用
- 特征工程和数据处理
- 多任务学习架构设计
- 模型训练和优化策略

### 🔧 项目实践
- 完整的项目架构设计
- 代码组织和模块化
- 可视化和可解释性分析
- 从零构建项目的完整流程

### 🚀 独立开发能力
现在您具备了独立构建类似项目的能力：
1. **问题分析**: 能够分析生物预测问题的特点和需求
2. **技术选型**: 能够选择合适的技术栈和架构
3. **系统设计**: 能够设计完整的系统架构
4. **代码实现**: 能够实现各个功能模块
5. **项目管理**: 能够规划和管理整个开发流程

### 📚 持续学习建议
1. **深入生物学**: 学习更多生物学领域知识
2. **技术进阶**: 关注最新的深度学习技术
3. **实践项目**: 尝试解决不同的生物预测问题
4. **社区参与**: 参与开源项目和学术交流
5. **跨学科合作**: 与生物学家和其他领域专家合作

祝您在生物信息学和深度学习的道路上取得成功！🎉
```