#!/bin/bash

# DeepMu 温度预测训练脚本 (Shell版本)
# 
# 这个脚本用于训练专门的微生物最适温度预测模型
# 
# 作者: DeepMu 开发团队
# 版本: 2.0.0
# 日期: 2025-06-29

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_debug() {
    if [[ "$VERBOSE" == "true" ]]; then
        echo -e "${BLUE}[DEBUG]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
    fi
}

# 显示帮助信息
show_help() {
    cat << EOF
DeepMu 温度预测训练脚本

用法: $0 [选项]

选项:
    -h, --help              显示此帮助信息
    -c, --config FILE       配置文件路径 (默认: configs/temperature_config.json)
    -o, --output-dir DIR    输出目录 (默认: models/deepmu_temperature)
    -g, --gpu ID            GPU设备ID (默认: 0)
    -v, --verbose           详细输出
    -e, --epochs NUM        训练轮数 (默认: 300)
    -b, --batch-size NUM    批次大小 (默认: 128)
    -l, --learning-rate NUM 学习率 (默认: 0.001)
    -k, --k-folds NUM       交叉验证折数 (默认: 5)
    --activation STR        激活函数 (默认: gelu)
    --temp-min NUM          最低温度 (默认: 0.0)
    --temp-max NUM          最高温度 (默认: 100.0)
    --use-mae-loss          使用MAE损失函数
    --dry-run              仅显示配置，不执行训练

示例:
    $0 --config my_config.json --output-dir my_models --verbose
    $0 --epochs 500 --batch-size 256 --learning-rate 0.0005
    $0 --k-folds 10 --temp-min 5.0 --temp-max 80.0 --use-mae-loss

EOF
}

# 默认参数
CONFIG_FILE="configs/temperature_config.json"
OUTPUT_DIR="models/deepmu_temperature"
GPU_ID=0
VERBOSE=false
EPOCHS=300
BATCH_SIZE=128
LEARNING_RATE=0.001
K_FOLDS=5
ACTIVATION="gelu"
TEMP_MIN=0.0
TEMP_MAX=100.0
USE_MAE_LOSS=true
DRY_RUN=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -c|--config)
            CONFIG_FILE="$2"
            shift 2
            ;;
        -o|--output-dir)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        -g|--gpu)
            GPU_ID="$2"
            shift 2
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -e|--epochs)
            EPOCHS="$2"
            shift 2
            ;;
        -b|--batch-size)
            BATCH_SIZE="$2"
            shift 2
            ;;
        -l|--learning-rate)
            LEARNING_RATE="$2"
            shift 2
            ;;
        -k|--k-folds)
            K_FOLDS="$2"
            shift 2
            ;;
        --activation)
            ACTIVATION="$2"
            shift 2
            ;;
        --temp-min)
            TEMP_MIN="$2"
            shift 2
            ;;
        --temp-max)
            TEMP_MAX="$2"
            shift 2
            ;;
        --use-mae-loss)
            USE_MAE_LOSS=true
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查Python环境
check_python_env() {
    log_info "检查Python环境..."
    
    if ! command -v python3 &> /dev/null; then
        log_error "Python3 未找到，请安装Python3"
        exit 1
    fi
    
    # 检查必要的Python包
    local required_packages=("torch" "numpy" "pandas" "sklearn" "matplotlib" "seaborn")
    for package in "${required_packages[@]}"; do
        if ! python3 -c "import $package" &> /dev/null; then
            log_error "Python包 '$package' 未安装"
            if [[ "$package" == "sklearn" ]]; then
                log_info "请运行: pip install scikit-learn"
            else
                log_info "请运行: pip install $package"
            fi
            exit 1
        fi
    done
    
    log_info "Python环境检查通过"
}

# 检查数据文件
check_data_files() {
    log_info "检查数据文件..."
    
    local feature_file="training_data/combined_features.tsv"
    local metadata_file="training_data/metadata.tsv"
    
    if [[ ! -f "$feature_file" ]]; then
        log_error "特征文件不存在: $feature_file"
        exit 1
    fi
    
    if [[ ! -f "$metadata_file" ]]; then
        log_error "元数据文件不存在: $metadata_file"
        exit 1
    fi
    
    # 检查元数据中是否包含温度列
    if ! head -1 "$metadata_file" | grep -q "optimal_temperature"; then
        log_error "元数据文件中未找到 'optimal_temperature' 列"
        exit 1
    fi
    
    log_info "数据文件检查通过"
}

# 创建配置文件
create_config() {
    log_info "创建配置文件..."
    
    # 创建配置目录
    mkdir -p "$(dirname "$CONFIG_FILE")"
    
    # 生成配置文件
    cat > "$CONFIG_FILE" << EOF
{
    "data": {
        "feature_file": "training_data/combined_features.tsv",
        "metadata_file": "training_data/metadata.tsv",
        "normalize_target": true,
        "random_state": 42
    },
    "model": {
        "hidden_dims": [512, 256, 128, 64],
        "dropout_rates": [0.3, 0.4, 0.4, 0.5],
        "use_batch_norm": true,
        "activation": "$ACTIVATION",
        "temp_range": [$TEMP_MIN, $TEMP_MAX]
    },
    "training": {
        "batch_size": $BATCH_SIZE,
        "learning_rate": $LEARNING_RATE,
        "epochs": $EPOCHS,
        "patience": 30,
        "weight_decay": 1e-4,
        "gradient_clip": 1.0,
        "use_mae_loss": $USE_MAE_LOSS
    },
    "validation": {
        "k_folds": $K_FOLDS
    }
}
EOF
    
    log_info "配置文件已创建: $CONFIG_FILE"
}

# 显示配置信息
show_config() {
    log_info "训练配置:"
    echo "  配置文件: $CONFIG_FILE"
    echo "  输出目录: $OUTPUT_DIR"
    echo "  GPU设备: $GPU_ID"
    echo "  训练轮数: $EPOCHS"
    echo "  批次大小: $BATCH_SIZE"
    echo "  学习率: $LEARNING_RATE"
    echo "  交叉验证折数: $K_FOLDS"
    echo "  激活函数: $ACTIVATION"
    echo "  温度范围: [$TEMP_MIN, $TEMP_MAX]°C"
    echo "  使用MAE损失: $USE_MAE_LOSS"
    echo "  详细输出: $VERBOSE"
}

# 检查GPU可用性
check_gpu() {
    log_info "检查GPU可用性..."
    
    if python3 -c "import torch; print('CUDA available:', torch.cuda.is_available())" | grep -q "True"; then
        local gpu_count=$(python3 -c "import torch; print(torch.cuda.device_count())")
        log_info "检测到 $gpu_count 个GPU设备"
        
        if [[ $GPU_ID -ge $gpu_count ]]; then
            log_warn "指定的GPU ID ($GPU_ID) 超出范围，使用GPU 0"
            GPU_ID=0
        fi
        
        local gpu_name=$(python3 -c "import torch; print(torch.cuda.get_device_name($GPU_ID))" 2>/dev/null || echo "Unknown")
        log_info "使用GPU $GPU_ID: $gpu_name"
    else
        log_warn "CUDA不可用，将使用CPU训练"
        GPU_ID=-1
    fi
}

# 创建输出目录
setup_output_dir() {
    log_info "设置输出目录..."
    
    mkdir -p "$OUTPUT_DIR"
    mkdir -p "$OUTPUT_DIR/plots"
    mkdir -p "$OUTPUT_DIR/logs"
    
    # 创建训练信息文件
    cat > "$OUTPUT_DIR/training_info.txt" << EOF
DeepMu 温度预测训练信息
====================

开始时间: $(date)
配置文件: $CONFIG_FILE
输出目录: $OUTPUT_DIR
GPU设备: $GPU_ID
训练轮数: $EPOCHS
批次大小: $BATCH_SIZE
学习率: $LEARNING_RATE
交叉验证折数: $K_FOLDS
激活函数: $ACTIVATION
温度范围: [$TEMP_MIN, $TEMP_MAX]°C
使用MAE损失: $USE_MAE_LOSS

EOF
    
    log_info "输出目录已设置: $OUTPUT_DIR"
}

# 运行训练
run_training() {
    log_info "开始温度预测模型训练..."
    
    # 构建Python命令
    local python_cmd="python3 train_temperature_model.py"
    python_cmd="$python_cmd --config $CONFIG_FILE"
    python_cmd="$python_cmd --output-dir $OUTPUT_DIR"
    python_cmd="$python_cmd --gpu $GPU_ID"
    
    if [[ "$VERBOSE" == "true" ]]; then
        python_cmd="$python_cmd --verbose"
    fi
    
    log_debug "执行命令: $python_cmd"
    
    # 执行训练
    if eval "$python_cmd"; then
        log_info "温度预测模型训练完成！"
        
        # 显示结果摘要
        if [[ -f "$OUTPUT_DIR/training_history.json" ]]; then
            log_info "训练结果摘要:"
            python3 -c "
import json
with open('$OUTPUT_DIR/training_history.json', 'r') as f:
    history = json.load(f)
if 'cv_metrics' in history:
    r2_mean = history['cv_metrics']['r2_mean']
    r2_std = history['cv_metrics']['r2_std']
    mae_mean = history['cv_metrics']['mae_mean']
    mae_std = history['cv_metrics']['mae_std']
    print(f'  交叉验证 R²: {r2_mean:.4f} ± {r2_std:.4f}')
    print(f'  交叉验证 MAE: {mae_mean:.2f} ± {mae_std:.2f}°C')
    print(f'  最佳折: {history[\"best_fold\"]}')
"
        fi
        
        # 更新训练信息
        echo "结束时间: $(date)" >> "$OUTPUT_DIR/training_info.txt"
        echo "训练状态: 成功完成" >> "$OUTPUT_DIR/training_info.txt"
        
    else
        log_error "训练失败！"
        echo "结束时间: $(date)" >> "$OUTPUT_DIR/training_info.txt"
        echo "训练状态: 失败" >> "$OUTPUT_DIR/training_info.txt"
        exit 1
    fi
}

# 生成训练报告
generate_report() {
    log_info "生成训练报告..."
    
    local report_file="$OUTPUT_DIR/training_report.md"
    
    cat > "$report_file" << EOF
# DeepMu 温度预测模型训练报告

## 训练信息

- **开始时间**: $(date)
- **模型类型**: 最适温度预测
- **交叉验证**: ${K_FOLDS}折
- **训练轮数**: ${EPOCHS}
- **批次大小**: ${BATCH_SIZE}
- **学习率**: ${LEARNING_RATE}
- **激活函数**: ${ACTIVATION}
- **温度范围**: [${TEMP_MIN}, ${TEMP_MAX}]°C

## 模型架构

- **隐藏层**: [512, 256, 128, 64]
- **Dropout率**: [0.3, 0.4, 0.4, 0.5]
- **批量归一化**: 是
- **温度约束**: 是
- **损失函数**: $(if [[ "$USE_MAE_LOSS" == "true" ]]; then echo "MAE"; else echo "MSE"; fi)

## 数据处理

- **特征标准化**: RobustScaler
- **目标标准化**: MinMaxScaler
- **异常值检测**: IsolationForest
- **温度范围过滤**: 是

## 评估指标

- **R²**: 决定系数
- **MAE**: 平均绝对误差 (°C)
- **RMSE**: 均方根误差 (°C)
- **1°C内准确率**: 预测误差在1°C内的样本比例
- **2°C内准确率**: 预测误差在2°C内的样本比例
- **5°C内准确率**: 预测误差在5°C内的样本比例

## 使用方法

\`\`\`python
import torch
from train_temperature_model import TemperatureModel

# 加载模型
model = TemperatureModel(...)
model.load_state_dict(torch.load('temperature_model.pt'))
model.eval()

# 加载标准化器
scalers = torch.load('scalers.pt')
feature_scaler = scalers['feature_scaler']
target_scaler = scalers['target_scaler']

# 进行预测
features_scaled = feature_scaler.transform(features)
with torch.no_grad():
    predictions = model(torch.FloatTensor(features_scaled))
    predictions_orig = target_scaler.inverse_transform(predictions.numpy())
\`\`\`

## 温度约束

模型使用sigmoid函数将输出约束到[0,1]范围，然后线性变换到指定的温度范围，
确保预测的温度值在物理合理的范围内。

EOF
    
    log_info "训练报告已生成: $report_file"
}

# 主函数
main() {
    log_info "开始DeepMu温度预测训练流程"
    
    # 显示配置
    show_config
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "干运行模式，仅显示配置"
        exit 0
    fi
    
    # 执行检查和准备
    check_python_env
    check_data_files
    check_gpu
    setup_output_dir
    
    # 创建配置文件（如果不存在）
    if [[ ! -f "$CONFIG_FILE" ]]; then
        create_config
    else
        log_info "使用现有配置文件: $CONFIG_FILE"
    fi
    
    # 运行训练
    run_training
    
    # 生成报告
    generate_report
    
    log_info "温度预测训练流程完成！"
    log_info "模型文件: $OUTPUT_DIR/temperature_model.pt"
    log_info "训练日志: $OUTPUT_DIR/training.log"
    log_info "配置文件: $OUTPUT_DIR/config.json"
    log_info "训练报告: $OUTPUT_DIR/training_report.md"
}

# 捕获中断信号
trap 'log_error "训练被中断"; exit 1' INT TERM

# 执行主函数
main "$@"
