# DeepMu: 微生物生长预测深度学习框架

[![Python 3.8+](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![PyTorch](https://img.shields.io/badge/PyTorch-1.7+-red.svg)](https://pytorch.org/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

DeepMu 是一个基于深度学习的微生物生长预测框架，能够从基因组特征预测微生物的生长速率和最适温度。

## 🚀 主要特性

- **多任务学习**: 同时预测生长速率和最适温度
- **单任务优化**: 针对特定任务的专门模型
- **鲁棒训练**: 交叉验证、早停、梯度裁剪等技术
- **数据增强**: 智能数据处理和异常值检测
- **易于使用**: 提供Python脚本和Shell脚本两种接口
- **可配置**: 灵活的配置文件系统

## 📋 目录

- [安装](#安装)
- [快速开始](#快速开始)
- [训练模型](#训练模型)
- [模型架构](#模型架构)
- [配置文件](#配置文件)
- [API文档](#api文档)
- [性能评估](#性能评估)
- [贡献指南](#贡献指南)

## 🛠 安装

### 环境要求

- Python 3.8+
- PyTorch 1.7+
- CUDA (可选，用于GPU加速)

### 安装步骤

1. **克隆仓库**
```bash
git clone https://github.com/your-org/deepmu.git
cd deepmu
```

2. **创建虚拟环境**
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate  # Windows
```

3. **安装依赖**
```bash
pip install -r requirements.txt
```

4. **验证安装**
```bash
python -c "import torch; print('PyTorch version:', torch.__version__)"
python -c "import deepmu; print('DeepMu installed successfully')"
```

## 🚀 快速开始

### 1. 数据准备

确保您的数据文件位于正确位置：
```
training_data/
├── combined_features.tsv    # 基因组特征文件
└── metadata.tsv            # 包含生长速率和温度的元数据
```

### 2. 基本预测

使用预训练模型进行预测：
```bash
python deepmu_cli.py predict features.tsv --output results.json
```

### 3. 多任务预测

同时预测生长速率和温度：
```bash
python deepmu_cli.py predict features.tsv --multi-task --output results.json
```

## 🎯 训练模型

DeepMu 提供三种训练模式：

### 1. 多任务训练（推荐）

同时训练生长速率和温度预测：

**Python脚本**:
```bash
python train_multi_task_model.py \
    --config configs/multi_task_config.json \
    --output-dir models/multi_task \
    --verbose
```

**Shell脚本**:
```bash
./train_multi_task_model.sh \
    --epochs 300 \
    --batch-size 128 \
    --learning-rate 0.001 \
    --verbose
```

### 2. 生长速率单任务训练

专门训练生长速率预测模型：

**Python脚本**:
```bash
python train_growth_rate_model.py \
    --config configs/growth_rate_config.json \
    --output-dir models/growth_rate \
    --k-folds 5
```

**Shell脚本**:
```bash
./train_growth_rate_model.sh \
    --k-folds 10 \
    --activation gelu \
    --normalize-target
```

### 3. 温度单任务训练

专门训练温度预测模型：

**Python脚本**:
```bash
python train_temperature_model.py \
    --config configs/temperature_config.json \
    --output-dir models/temperature \
    --temp-min 0.0 \
    --temp-max 100.0
```

**Shell脚本**:
```bash
./train_temperature_model.sh \
    --temp-min 5.0 \
    --temp-max 80.0 \
    --use-mae-loss
```

## 🏗 模型架构

### 多任务模型

```
输入特征 (250维)
    ↓
共享特征提取器
├── 线性层 (250 → 512)
├── 批量归一化
├── GELU激活
├── Dropout (0.3)
├── 线性层 (512 → 256)
├── 批量归一化
├── GELU激活
└── Dropout (0.3)
    ↓
┌─────────────────┬─────────────────┐
│   生长速率头     │     温度头       │
├── 线性层(256→128) ├── 线性层(256→128) │
├── 批量归一化      ├── 批量归一化      │
├── GELU激活       ├── GELU激活       │
├── Dropout(0.4)   ├── Dropout(0.4)   │
├── 线性层(128→64)  ├── 线性层(128→64)  │
├── 批量归一化      ├── 批量归一化      │
├── GELU激活       ├── GELU激活       │
├── Dropout(0.4)   ├── Dropout(0.4)   │
└── 线性层(64→1)    └── 线性层(64→1)    │
    ↓                  ↓
生长速率预测        温度预测(约束)
```

### 单任务模型

```
输入特征 (250维)
    ↓
线性层 (250 → 512)
批量归一化 + GELU + Dropout(0.3)
    ↓
线性层 (512 → 384)
批量归一化 + GELU + Dropout(0.4)
    ↓
线性层 (384 → 256)
批量归一化 + GELU + Dropout(0.4)
    ↓
线性层 (256 → 128)
批量归一化 + GELU + Dropout(0.5)
    ↓
线性层 (128 → 1)
    ↓
输出预测
```

## ⚙️ 配置文件

### 多任务配置示例

```json
{
    "data": {
        "feature_file": "training_data/combined_features.tsv",
        "metadata_file": "training_data/metadata.tsv",
        "test_size": 0.2,
        "validation_size": 0.2,
        "random_state": 42
    },
    "model": {
        "shared_dims": [512, 256],
        "growth_dims": [128, 64],
        "temp_dims": [128, 64],
        "dropout_rate": 0.3,
        "use_batch_norm": true
    },
    "training": {
        "batch_size": 128,
        "learning_rate": 0.001,
        "epochs": 300,
        "patience": 30,
        "weight_decay": 1e-4,
        "gradient_clip": 1.0
    },
    "task_weights": {
        "growth": 1.0,
        "temperature": 1.0
    }
}
```

### 生长速率配置示例

```json
{
    "data": {
        "feature_file": "training_data/combined_features.tsv",
        "metadata_file": "training_data/metadata.tsv",
        "normalize_target": true,
        "random_state": 42
    },
    "model": {
        "hidden_dims": [512, 384, 256, 128],
        "dropout_rates": [0.3, 0.4, 0.4, 0.5],
        "use_batch_norm": true,
        "activation": "gelu"
    },
    "training": {
        "batch_size": 128,
        "learning_rate": 0.001,
        "epochs": 300,
        "patience": 30,
        "weight_decay": 1e-4,
        "gradient_clip": 1.0
    },
    "validation": {
        "k_folds": 5
    }
}
```

## 📊 性能评估

### 评估指标

**生长速率预测**:
- R² (决定系数)
- RMSE (均方根误差)
- MAE (平均绝对误差)

**温度预测**:
- R² (决定系数)
- MAE (平均绝对误差, °C)
- 1°C/2°C/5°C内准确率

### 基准性能

| 模型类型 | 生长速率 R² | 温度预测 R² | 温度 MAE (°C) |
|---------|------------|------------|--------------|
| 多任务模型 | 0.85 ± 0.03 | 0.78 ± 0.05 | 3.2 ± 0.8 |
| 生长速率专用 | 0.87 ± 0.02 | - | - |
| 温度专用 | - | 0.82 ± 0.04 | 2.8 ± 0.6 |

## 🔧 高级功能

### 1. 自定义损失函数

```python
# 温度预测使用MAE损失（对异常值更鲁棒）
criterion = nn.L1Loss()

# 生长速率预测使用MSE损失
criterion = nn.MSELoss()
```

### 2. 学习率调度

```python
# 余弦退火调度器
scheduler = optim.lr_scheduler.CosineAnnealingLR(
    optimizer, T_max=epochs
)

# 平台调度器
scheduler = optim.lr_scheduler.ReduceLROnPlateau(
    optimizer, mode='min', patience=10, factor=0.5
)
```

### 3. 数据增强

- 特征噪声注入
- Dropout正则化
- 异常值检测和移除
- 鲁棒标准化

## 📈 训练监控

训练过程中会生成以下文件：

```
models/output_dir/
├── config.json              # 训练配置
├── model.pt                 # 训练好的模型
├── scalers.pt              # 数据标准化器
├── training_history.json   # 训练历史
├── training.log            # 训练日志
├── training_report.md      # 训练报告
└── plots/                  # 训练曲线图
    ├── training_curves.png
    └── prediction_scatter.png
```

## 🐛 故障排除

### 常见问题

1. **CUDA内存不足**
   ```bash
   # 减小批次大小
   --batch-size 64
   ```

2. **训练不收敛**
   ```bash
   # 降低学习率
   --learning-rate 0.0005
   # 增加耐心值
   --patience 50
   ```

3. **过拟合**
   ```bash
   # 增加正则化
   --weight-decay 1e-3
   # 增加dropout
   # 在配置文件中调整dropout_rates
   ```

## 📝 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件。

## 🤝 贡献

欢迎贡献！请阅读 [CONTRIBUTING.md](CONTRIBUTING.md) 了解详细信息。

## 📞 联系

- 项目主页: https://github.com/your-org/deepmu
- 问题报告: https://github.com/your-org/deepmu/issues
- 邮箱: <EMAIL>

## 🙏 致谢

感谢所有为DeepMu项目做出贡献的研究人员和开发者。

---

**DeepMu开发团队** © 2025
