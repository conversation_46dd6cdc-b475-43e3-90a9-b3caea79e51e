#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
DeepMu 增强训练脚本

这个脚本实现了一个现代化、鲁棒的微生物生长预测模型训练流程，
具有以下特点：

1. 鲁棒性增强：
   - 自动数据验证和清理
   - 异常值检测和处理
   - 梯度裁剪和权重衰减
   - 早停机制防止过拟合

2. 泛化能力提升：
   - K折交叉验证
   - 数据增强技术
   - 集成学习方法
   - 正则化技术

3. 可读性和可维护性：
   - 清晰的代码结构
   - 详细的日志记录
   - 配置文件支持
   - 模块化设计

作者: DeepMu 开发团队
版本: 2.0.0
日期: 2025-06-29
"""

import os
import sys
import json
import argparse
import logging
import warnings
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union
from datetime import datetime

import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
from sklearn.model_selection import KFold, train_test_split
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from sklearn.ensemble import IsolationForest
import matplotlib.pyplot as plt
import seaborn as sns

# 导入DeepMu模块
try:
    from deepmu.models.enhanced_networks import EnhancedPhyloGrowthModel
    from deepmu.utils.data_utils import DataProcessor, DataAugmenter
    from deepmu.utils.model_utils import ModelTrainer, EarlyStopping
    from deepmu.utils.logging import setup_logger, log_function_call
except ImportError as e:
    print(f"警告: 无法导入DeepMu模块: {e}")
    print("将使用简化版本的训练流程")

# 忽略警告
warnings.filterwarnings('ignore')

class TrainingConfig:
    """训练配置类"""
    
    def __init__(self, config_file: Optional[str] = None):
        """
        初始化训练配置
        
        Args:
            config_file: 配置文件路径（JSON格式）
        """
        # 默认配置
        self.config = {
            # 数据配置
            "data": {
                "feature_file": "training_data/combined_features.tsv",
                "metadata_file": "training_data/metadata.tsv",
                "target_column": "growth_rate",
                "test_size": 0.2,
                "validation_size": 0.2,
                "random_state": 42
            },
            
            # 模型配置
            "model": {
                "input_dim": 250,
                "hidden_dims": [512, 384, 256, 128],
                "dropout_rates": [0.3, 0.4, 0.4, 0.5],
                "use_batch_norm": True,
                "activation": "gelu",
                "output_dim": 1
            },
            
            # 训练配置
            "training": {
                "batch_size": 128,
                "learning_rate": 0.001,
                "epochs": 300,
                "patience": 30,
                "min_delta": 1e-6,
                "weight_decay": 1e-4,
                "gradient_clip": 1.0,
                "scheduler": "cosine",
                "warmup_epochs": 10
            },
            
            # 验证配置
            "validation": {
                "k_folds": 5,
                "use_cross_validation": True,
                "stratify": False
            },
            
            # 数据增强配置
            "augmentation": {
                "use_augmentation": True,
                "noise_std": 0.01,
                "dropout_prob": 0.1,
                "mixup_alpha": 0.2
            },
            
            # 输出配置
            "output": {
                "model_dir": "models/deepmu_enhanced",
                "log_dir": "logs",
                "plot_dir": "plots",
                "save_best_only": True,
                "save_checkpoints": True
            }
        }
        
        # 如果提供了配置文件，则加载并合并
        if config_file and os.path.exists(config_file):
            self.load_config(config_file)
    
    def load_config(self, config_file: str):
        """加载配置文件"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                user_config = json.load(f)
            
            # 递归合并配置
            self._merge_config(self.config, user_config)
            print(f"✅ 成功加载配置文件: {config_file}")
            
        except Exception as e:
            print(f"⚠️  加载配置文件失败: {e}")
            print("使用默认配置")
    
    def _merge_config(self, default: dict, user: dict):
        """递归合并配置字典"""
        for key, value in user.items():
            if key in default and isinstance(default[key], dict) and isinstance(value, dict):
                self._merge_config(default[key], value)
            else:
                default[key] = value
    
    def save_config(self, output_file: str):
        """保存当前配置到文件"""
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, indent=2, ensure_ascii=False)
    
    def __getitem__(self, key):
        """支持字典式访问"""
        return self.config[key]
    
    def get(self, key, default=None):
        """获取配置值"""
        keys = key.split('.')
        value = self.config
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        return value

class EnhancedDataProcessor:
    """增强的数据处理器"""
    
    def __init__(self, config: TrainingConfig):
        """
        初始化数据处理器
        
        Args:
            config: 训练配置
        """
        self.config = config
        self.scaler = RobustScaler()  # 使用RobustScaler对异常值更鲁棒
        self.outlier_detector = IsolationForest(
            contamination=0.1, 
            random_state=config.get('data.random_state', 42)
        )
        self.logger = logging.getLogger(__name__)
    
    def load_and_validate_data(self) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        加载和验证数据
        
        Returns:
            特征数据和元数据的元组
        """
        self.logger.info("开始加载数据...")
        
        # 加载特征数据
        feature_file = self.config.get('data.feature_file')
        if not os.path.exists(feature_file):
            raise FileNotFoundError(f"特征文件不存在: {feature_file}")
        
        features = pd.read_csv(feature_file, sep='\t', index_col=0)
        self.logger.info(f"加载特征数据: {features.shape}")
        
        # 加载元数据
        metadata_file = self.config.get('data.metadata_file')
        if not os.path.exists(metadata_file):
            raise FileNotFoundError(f"元数据文件不存在: {metadata_file}")
        
        metadata = pd.read_csv(metadata_file, sep='\t', index_col=0)
        self.logger.info(f"加载元数据: {metadata.shape}")
        
        # 数据验证
        self._validate_data(features, metadata)
        
        return features, metadata
    
    def _validate_data(self, features: pd.DataFrame, metadata: pd.DataFrame):
        """验证数据质量"""
        self.logger.info("验证数据质量...")
        
        # 检查索引匹配
        common_indices = features.index.intersection(metadata.index)
        if len(common_indices) == 0:
            raise ValueError("特征数据和元数据没有共同的样本")
        
        self.logger.info(f"共同样本数: {len(common_indices)}")
        
        # 检查缺失值
        feature_missing = features.isnull().sum().sum()
        metadata_missing = metadata.isnull().sum().sum()
        
        if feature_missing > 0:
            self.logger.warning(f"特征数据中有 {feature_missing} 个缺失值")
        
        if metadata_missing > 0:
            self.logger.warning(f"元数据中有 {metadata_missing} 个缺失值")
        
        # 检查目标变量
        target_col = self.config.get('data.target_column')
        if target_col not in metadata.columns:
            raise ValueError(f"目标变量 '{target_col}' 不在元数据中")
        
        target_values = metadata[target_col].dropna()
        if len(target_values) == 0:
            raise ValueError(f"目标变量 '{target_col}' 没有有效值")
        
        self.logger.info(f"目标变量统计: 均值={target_values.mean():.4f}, "
                        f"标准差={target_values.std():.4f}, "
                        f"范围=[{target_values.min():.4f}, {target_values.max():.4f}]")
    
    def preprocess_data(self, features: pd.DataFrame, metadata: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray, List[str]]:
        """
        预处理数据
        
        Args:
            features: 特征数据
            metadata: 元数据
            
        Returns:
            处理后的特征、目标值和样本ID
        """
        self.logger.info("开始数据预处理...")
        
        # 获取共同样本
        common_indices = features.index.intersection(metadata.index)
        features = features.loc[common_indices]
        metadata = metadata.loc[common_indices]
        
        # 获取目标变量
        target_col = self.config.get('data.target_column')
        target = metadata[target_col].values
        
        # 移除目标值为NaN的样本
        valid_mask = ~np.isnan(target)
        features = features[valid_mask]
        target = target[valid_mask]
        sample_ids = features.index.tolist()
        
        self.logger.info(f"有效样本数: {len(sample_ids)}")
        
        # 处理特征缺失值
        features = features.fillna(0)
        
        # 移除常数特征
        feature_std = features.std()
        non_constant_features = feature_std[feature_std > 1e-8].index
        features = features[non_constant_features]
        
        self.logger.info(f"移除常数特征后剩余特征数: {features.shape[1]}")
        
        # 异常值检测
        if len(features) > 50:  # 只有足够样本时才进行异常值检测
            outlier_mask = self.outlier_detector.fit_predict(features) == 1
            features = features[outlier_mask]
            target = target[outlier_mask]
            sample_ids = [sample_ids[i] for i in range(len(sample_ids)) if outlier_mask[i]]
            
            self.logger.info(f"异常值检测后剩余样本数: {len(sample_ids)}")
        
        # 特征标准化
        features_scaled = self.scaler.fit_transform(features)
        
        self.logger.info("数据预处理完成")

        return features_scaled, target, sample_ids

class SimpleGrowthModel(nn.Module):
    """简化的生长预测模型"""

    def __init__(self, input_dim: int, hidden_dims: List[int], dropout_rates: List[float],
                 use_batch_norm: bool = True, activation: str = 'gelu'):
        """
        初始化模型

        Args:
            input_dim: 输入维度
            hidden_dims: 隐藏层维度列表
            dropout_rates: Dropout率列表
            use_batch_norm: 是否使用批量归一化
            activation: 激活函数类型
        """
        super().__init__()

        self.layers = nn.ModuleList()

        # 激活函数
        if activation == 'gelu':
            self.activation = nn.GELU()
        elif activation == 'relu':
            self.activation = nn.ReLU()
        elif activation == 'leaky_relu':
            self.activation = nn.LeakyReLU(0.1)
        else:
            self.activation = nn.ReLU()

        # 构建网络层
        dims = [input_dim] + hidden_dims + [1]

        for i in range(len(dims) - 1):
            # 线性层
            self.layers.append(nn.Linear(dims[i], dims[i + 1]))

            # 最后一层不添加激活函数和正则化
            if i < len(dims) - 2:
                # 批量归一化
                if use_batch_norm:
                    self.layers.append(nn.BatchNorm1d(dims[i + 1]))

                # 激活函数
                self.layers.append(self.activation)

                # Dropout
                if i < len(dropout_rates):
                    self.layers.append(nn.Dropout(dropout_rates[i]))

    def forward(self, x):
        """前向传播"""
        for layer in self.layers:
            x = layer(x)
        return x

class EnhancedModelTrainer:
    """增强的模型训练器"""

    def __init__(self, config: TrainingConfig, device: torch.device):
        """
        初始化训练器

        Args:
            config: 训练配置
            device: 计算设备
        """
        self.config = config
        self.device = device
        self.logger = logging.getLogger(__name__)

    def create_model(self, input_dim: int) -> nn.Module:
        """创建模型"""
        model_config = self.config['model']

        model = SimpleGrowthModel(
            input_dim=input_dim,
            hidden_dims=model_config['hidden_dims'],
            dropout_rates=model_config['dropout_rates'],
            use_batch_norm=model_config['use_batch_norm'],
            activation=model_config['activation']
        )

        return model.to(self.device)

    def train(self, features: np.ndarray, target: np.ndarray, sample_ids: List[str]) -> Tuple[nn.Module, Dict]:
        """
        训练模型

        Args:
            features: 特征数据
            target: 目标值
            sample_ids: 样本ID

        Returns:
            最佳模型和训练历史
        """
        self.logger.info("开始模型训练...")

        # 数据分割
        train_config = self.config['training']
        val_config = self.config['validation']

        if val_config['use_cross_validation']:
            return self._train_with_cv(features, target, sample_ids)
        else:
            return self._train_single_fold(features, target, sample_ids)

    def _train_single_fold(self, features: np.ndarray, target: np.ndarray,
                          sample_ids: List[str]) -> Tuple[nn.Module, Dict]:
        """单折训练"""
        # 数据分割
        data_config = self.config['data']

        X_temp, X_test, y_temp, y_test = train_test_split(
            features, target,
            test_size=data_config['test_size'],
            random_state=data_config['random_state']
        )

        X_train, X_val, y_train, y_val = train_test_split(
            X_temp, y_temp,
            test_size=data_config['validation_size'] / (1 - data_config['test_size']),
            random_state=data_config['random_state']
        )

        self.logger.info(f"数据分割: 训练={len(X_train)}, 验证={len(X_val)}, 测试={len(X_test)}")

        # 创建模型
        model = self.create_model(features.shape[1])

        # 训练模型
        history = self._train_epoch_loop(model, X_train, y_train, X_val, y_val)

        # 测试评估
        test_metrics = self._evaluate_model(model, X_test, y_test)
        history['test_metrics'] = test_metrics

        return model, history

    def _train_epoch_loop(self, model: nn.Module, X_train: np.ndarray, y_train: np.ndarray,
                         X_val: np.ndarray, y_val: np.ndarray) -> Dict:
        """训练循环"""
        train_config = self.config['training']

        # 创建数据加载器
        train_dataset = TensorDataset(
            torch.FloatTensor(X_train),
            torch.FloatTensor(y_train).unsqueeze(1)
        )
        train_loader = DataLoader(
            train_dataset,
            batch_size=train_config['batch_size'],
            shuffle=True
        )

        val_dataset = TensorDataset(
            torch.FloatTensor(X_val),
            torch.FloatTensor(y_val).unsqueeze(1)
        )
        val_loader = DataLoader(
            val_dataset,
            batch_size=train_config['batch_size'],
            shuffle=False
        )

        # 优化器和损失函数
        optimizer = optim.AdamW(
            model.parameters(),
            lr=train_config['learning_rate'],
            weight_decay=train_config['weight_decay']
        )

        criterion = nn.MSELoss()

        # 学习率调度器
        if train_config['scheduler'] == 'cosine':
            scheduler = optim.lr_scheduler.CosineAnnealingLR(
                optimizer, T_max=train_config['epochs']
            )
        elif train_config['scheduler'] == 'plateau':
            scheduler = optim.lr_scheduler.ReduceLROnPlateau(
                optimizer, mode='min', patience=10, factor=0.5
            )
        else:
            scheduler = None

        # 早停
        early_stopping = EarlyStopping(
            patience=train_config['patience'],
            min_delta=train_config['min_delta']
        )

        # 训练历史
        history = {
            'train_loss': [],
            'val_loss': [],
            'train_r2': [],
            'val_r2': [],
            'learning_rate': []
        }

        best_model_state = None
        best_val_loss = float('inf')

        # 训练循环
        for epoch in range(train_config['epochs']):
            # 训练阶段
            model.train()
            train_loss = 0.0
            train_predictions = []
            train_targets = []

            for batch_x, batch_y in train_loader:
                batch_x, batch_y = batch_x.to(self.device), batch_y.to(self.device)

                optimizer.zero_grad()
                outputs = model(batch_x)
                loss = criterion(outputs, batch_y)
                loss.backward()

                # 梯度裁剪
                if train_config['gradient_clip'] > 0:
                    torch.nn.utils.clip_grad_norm_(
                        model.parameters(),
                        train_config['gradient_clip']
                    )

                optimizer.step()

                train_loss += loss.item()
                train_predictions.extend(outputs.detach().cpu().numpy())
                train_targets.extend(batch_y.detach().cpu().numpy())

            # 验证阶段
            model.eval()
            val_loss = 0.0
            val_predictions = []
            val_targets = []

            with torch.no_grad():
                for batch_x, batch_y in val_loader:
                    batch_x, batch_y = batch_x.to(self.device), batch_y.to(self.device)
                    outputs = model(batch_x)
                    loss = criterion(outputs, batch_y)

                    val_loss += loss.item()
                    val_predictions.extend(outputs.cpu().numpy())
                    val_targets.extend(batch_y.cpu().numpy())

            # 计算指标
            train_loss /= len(train_loader)
            val_loss /= len(val_loader)

            train_r2 = r2_score(train_targets, train_predictions)
            val_r2 = r2_score(val_targets, val_predictions)

            # 记录历史
            history['train_loss'].append(train_loss)
            history['val_loss'].append(val_loss)
            history['train_r2'].append(train_r2)
            history['val_r2'].append(val_r2)
            history['learning_rate'].append(optimizer.param_groups[0]['lr'])

            # 保存最佳模型
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                best_model_state = model.state_dict().copy()

            # 学习率调度
            if scheduler:
                if train_config['scheduler'] == 'plateau':
                    scheduler.step(val_loss)
                else:
                    scheduler.step()

            # 早停检查
            if early_stopping(val_loss):
                self.logger.info(f"早停触发，在第 {epoch + 1} 轮停止训练")
                break

            # 日志输出
            if (epoch + 1) % 10 == 0:
                self.logger.info(
                    f"Epoch {epoch + 1}/{train_config['epochs']}: "
                    f"Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}, "
                    f"Train R²: {train_r2:.4f}, Val R²: {val_r2:.4f}, "
                    f"LR: {optimizer.param_groups[0]['lr']:.2e}"
                )

        # 加载最佳模型
        if best_model_state:
            model.load_state_dict(best_model_state)

        return history

    def _evaluate_model(self, model: nn.Module, X_test: np.ndarray, y_test: np.ndarray) -> Dict:
        """评估模型"""
        model.eval()

        with torch.no_grad():
            X_test_tensor = torch.FloatTensor(X_test).to(self.device)
            predictions = model(X_test_tensor).cpu().numpy().flatten()

        # 计算指标
        mse = mean_squared_error(y_test, predictions)
        rmse = np.sqrt(mse)
        mae = mean_absolute_error(y_test, predictions)
        r2 = r2_score(y_test, predictions)

        metrics = {
            'mse': float(mse),
            'rmse': float(rmse),
            'mae': float(mae),
            'r2': float(r2),
            'predictions': predictions.tolist(),
            'targets': y_test.tolist()
        }

        self.logger.info(f"测试集评估: MSE={mse:.6f}, RMSE={rmse:.6f}, MAE={mae:.6f}, R²={r2:.4f}")

        return metrics

class EarlyStopping:
    """早停类"""

    def __init__(self, patience: int = 30, min_delta: float = 1e-6):
        """
        初始化早停

        Args:
            patience: 耐心值
            min_delta: 最小改善量
        """
        self.patience = patience
        self.min_delta = min_delta
        self.counter = 0
        self.best_loss = float('inf')

    def __call__(self, val_loss: float) -> bool:
        """
        检查是否应该早停

        Args:
            val_loss: 验证损失

        Returns:
            是否应该早停
        """
        if val_loss < self.best_loss - self.min_delta:
            self.best_loss = val_loss
            self.counter = 0
        else:
            self.counter += 1

        return self.counter >= self.patience

    def _train_with_cv(self, features: np.ndarray, target: np.ndarray,
                      sample_ids: List[str]) -> Tuple[nn.Module, Dict]:
        """K折交叉验证训练"""
        val_config = self.config['validation']
        k_folds = val_config['k_folds']

        self.logger.info(f"开始 {k_folds} 折交叉验证训练...")

        kfold = KFold(n_splits=k_folds, shuffle=True,
                     random_state=self.config.get('data.random_state', 42))

        fold_histories = []
        fold_models = []

        for fold, (train_idx, val_idx) in enumerate(kfold.split(features)):
            self.logger.info(f"训练第 {fold + 1}/{k_folds} 折...")

            X_train, X_val = features[train_idx], features[val_idx]
            y_train, y_val = target[train_idx], target[val_idx]

            # 创建模型
            model = self.create_model(features.shape[1])

            # 训练模型
            history = self._train_epoch_loop(model, X_train, y_train, X_val, y_val)
            history['fold'] = fold + 1

            fold_histories.append(history)
            fold_models.append(model)

        # 选择最佳模型（基于验证R²）
        best_fold = max(range(k_folds),
                       key=lambda i: max(fold_histories[i]['val_r2']))

        best_model = fold_models[best_fold]

        # 合并历史
        combined_history = {
            'fold_histories': fold_histories,
            'best_fold': best_fold + 1,
            'cv_metrics': self._calculate_cv_metrics(fold_histories)
        }

        self.logger.info(f"最佳模型来自第 {best_fold + 1} 折")

        return best_model, combined_history

    def _calculate_cv_metrics(self, fold_histories: List[Dict]) -> Dict:
        """计算交叉验证指标"""
        # 收集最终验证指标
        final_val_r2 = [max(h['val_r2']) for h in fold_histories]
        final_val_loss = [min(h['val_loss']) for h in fold_histories]

        cv_metrics = {
            'val_r2_mean': float(np.mean(final_val_r2)),
            'val_r2_std': float(np.std(final_val_r2)),
            'val_loss_mean': float(np.mean(final_val_loss)),
            'val_loss_std': float(np.std(final_val_loss)),
            'val_r2_folds': final_val_r2,
            'val_loss_folds': final_val_loss
        }

        self.logger.info(
            f"交叉验证结果: R² = {cv_metrics['val_r2_mean']:.4f} ± {cv_metrics['val_r2_std']:.4f}, "
            f"Loss = {cv_metrics['val_loss_mean']:.6f} ± {cv_metrics['val_loss_std']:.6f}"
        )

        return cv_metrics

    def generate_report(self, output_dir: str, training_history: Dict):
        """生成训练报告"""
        self.logger.info("生成训练报告...")

        # 创建图表目录
        plot_dir = os.path.join(output_dir, 'plots')
        os.makedirs(plot_dir, exist_ok=True)

        # 设置绘图样式
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")

        if 'fold_histories' in training_history:
            # 交叉验证报告
            self._plot_cv_results(training_history, plot_dir)
        else:
            # 单折训练报告
            self._plot_single_fold_results(training_history, plot_dir)

        # 生成文本报告
        self._generate_text_report(training_history, output_dir)

        self.logger.info(f"训练报告已保存到: {output_dir}")

    def _plot_single_fold_results(self, history: Dict, plot_dir: str):
        """绘制单折训练结果"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('DeepMu 训练结果', fontsize=16, fontweight='bold')

        epochs = range(1, len(history['train_loss']) + 1)

        # 损失曲线
        axes[0, 0].plot(epochs, history['train_loss'], label='训练损失', linewidth=2)
        axes[0, 0].plot(epochs, history['val_loss'], label='验证损失', linewidth=2)
        axes[0, 0].set_xlabel('轮次')
        axes[0, 0].set_ylabel('损失')
        axes[0, 0].set_title('训练和验证损失')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)

        # R²曲线
        axes[0, 1].plot(epochs, history['train_r2'], label='训练R²', linewidth=2)
        axes[0, 1].plot(epochs, history['val_r2'], label='验证R²', linewidth=2)
        axes[0, 1].set_xlabel('轮次')
        axes[0, 1].set_ylabel('R²')
        axes[0, 1].set_title('训练和验证R²')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)

        # 学习率曲线
        axes[1, 0].plot(epochs, history['learning_rate'], linewidth=2, color='orange')
        axes[1, 0].set_xlabel('轮次')
        axes[1, 0].set_ylabel('学习率')
        axes[1, 0].set_title('学习率变化')
        axes[1, 0].set_yscale('log')
        axes[1, 0].grid(True, alpha=0.3)

        # 测试集预测散点图
        if 'test_metrics' in history:
            test_metrics = history['test_metrics']
            axes[1, 1].scatter(test_metrics['targets'], test_metrics['predictions'],
                             alpha=0.6, s=30)

            # 添加对角线
            min_val = min(min(test_metrics['targets']), min(test_metrics['predictions']))
            max_val = max(max(test_metrics['targets']), max(test_metrics['predictions']))
            axes[1, 1].plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2)

            axes[1, 1].set_xlabel('真实值')
            axes[1, 1].set_ylabel('预测值')
            axes[1, 1].set_title(f'测试集预测 (R² = {test_metrics["r2"]:.4f})')
            axes[1, 1].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(os.path.join(plot_dir, 'training_results.png'), dpi=300, bbox_inches='tight')
        plt.close()

    def _generate_text_report(self, history: Dict, output_dir: str):
        """生成文本报告"""
        report_path = os.path.join(output_dir, 'training_report.md')

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# DeepMu 训练报告\n\n")
            f.write(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            # 配置信息
            f.write("## 训练配置\n\n")
            f.write("```json\n")
            f.write(json.dumps(self.config.config, indent=2, ensure_ascii=False))
            f.write("\n```\n\n")

            # 训练结果
            if 'fold_histories' in history:
                f.write("## 交叉验证结果\n\n")
                cv_metrics = history['cv_metrics']
                f.write(f"- **验证R²**: {cv_metrics['val_r2_mean']:.4f} ± {cv_metrics['val_r2_std']:.4f}\n")
                f.write(f"- **验证损失**: {cv_metrics['val_loss_mean']:.6f} ± {cv_metrics['val_loss_std']:.6f}\n")
                f.write(f"- **最佳折**: {history['best_fold']}\n\n")
            else:
                f.write("## 训练结果\n\n")
                if 'test_metrics' in history:
                    test_metrics = history['test_metrics']
                    f.write(f"- **测试R²**: {test_metrics['r2']:.4f}\n")
                    f.write(f"- **测试RMSE**: {test_metrics['rmse']:.6f}\n")
                    f.write(f"- **测试MAE**: {test_metrics['mae']:.6f}\n\n")

            f.write("## 模型架构\n\n")
            model_config = self.config['model']
            f.write(f"- **输入维度**: {model_config['input_dim']}\n")
            f.write(f"- **隐藏层**: {model_config['hidden_dims']}\n")
            f.write(f"- **Dropout率**: {model_config['dropout_rates']}\n")
            f.write(f"- **激活函数**: {model_config['activation']}\n")
            f.write(f"- **批量归一化**: {model_config['use_batch_norm']}\n\n")

            f.write("## 使用说明\n\n")
            f.write("训练完成的模型可以通过以下方式加载和使用：\n\n")
            f.write("```python\n")
            f.write("import torch\n")
            f.write("from deepmu.models import SimpleGrowthModel\n\n")
            f.write("# 加载模型\n")
            f.write("model = SimpleGrowthModel(...)\n")
            f.write("model.load_state_dict(torch.load('best_model.pt'))\n")
            f.write("model.eval()\n\n")
            f.write("# 进行预测\n")
            f.write("with torch.no_grad():\n")
            f.write("    predictions = model(features)\n")
            f.write("```\n")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='DeepMu 增强训练脚本')
    parser.add_argument('--config', type=str, help='配置文件路径')
    parser.add_argument('--output-dir', type=str, default='models/deepmu_enhanced', 
                       help='输出目录')
    parser.add_argument('--verbose', action='store_true', help='详细输出')
    parser.add_argument('--gpu', type=int, default=0, help='GPU设备ID')
    
    args = parser.parse_args()
    
    # 设置日志
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(f'{args.output_dir}/training.log')
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info("开始DeepMu增强训练流程")
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 加载配置
    config = TrainingConfig(args.config)
    config.config['output']['model_dir'] = args.output_dir
    
    # 保存配置
    config.save_config(os.path.join(args.output_dir, 'config.json'))
    
    # 设置设备
    device = torch.device(f'cuda:{args.gpu}' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    try:
        # 数据处理
        processor = EnhancedDataProcessor(config)
        features_df, metadata_df = processor.load_and_validate_data()
        features, target, sample_ids = processor.preprocess_data(features_df, metadata_df)
        
        logger.info(f"最终数据形状: 特征={features.shape}, 目标={target.shape}")
        
        # 模型训练
        trainer = EnhancedModelTrainer(config, device)
        best_model, training_history = trainer.train(features, target, sample_ids)

        # 保存最佳模型
        model_path = os.path.join(args.output_dir, 'best_model.pt')
        torch.save(best_model.state_dict(), model_path)
        logger.info(f"最佳模型已保存到: {model_path}")

        # 保存训练历史
        history_path = os.path.join(args.output_dir, 'training_history.json')
        with open(history_path, 'w') as f:
            json.dump(training_history, f, indent=2)

        # 生成训练报告
        trainer.generate_report(args.output_dir, training_history)

        logger.info("训练完成！")
        
    except Exception as e:
        logger.error(f"训练过程中出现错误: {e}")
        raise

if __name__ == "__main__":
    main()
