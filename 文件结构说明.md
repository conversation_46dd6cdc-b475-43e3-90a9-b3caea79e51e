# DeepMu 文件结构说明

## 📁 项目总体结构

```
DeepMu/
├── 🚀 训练脚本 (新增)
│   ├── train_multi_task_model.py      # 多任务训练 (Python)
│   ├── train_multi_task_model.sh      # 多任务训练 (Shell)
│   ├── train_growth_rate_model.py     # 生长速率训练 (Python)
│   ├── train_growth_rate_model.sh     # 生长速率训练 (Shell)
│   ├── train_temperature_model.py     # 温度预测训练 (Python)
│   └── train_temperature_model.sh     # 温度预测训练 (Shell)
│
├── 📚 文档
│   ├── README.md                      # 项目主文档 (新增)
│   ├── 文件结构说明.md                 # 本文件 (新增)
│   ├── README_修复状态.md              # 修复状态说明
│   └── 🎉最终成功报告.md               # 详细修复报告
│
├── 🧬 核心代码
│   ├── deepmu_cli.py                  # 命令行接口
│   ├── deepmu/                        # 主要代码包
│   │   ├── __init__.py
│   │   ├── predictors/                # 预测器模块
│   │   │   └── predictor.py           # 核心预测器
│   │   ├── models/                    # 模型定义
│   │   │   ├── networks.py            # 神经网络模型
│   │   │   └── legacy_compatibility.py # 遗留模型兼容
│   │   ├── features/                  # 特征提取
│   │   ├── utils/                     # 工具函数
│   │   └── cli/                       # 命令行模块
│   │
├── 🤖 模型和数据
│   ├── models/                        # 预训练模型
│   │   └── deepmu_final_model/
│   │       ├── growth_model.pt        # 生长速率模型
│   │       └── growth_model_params.pkl
│   ├── features/                      # 特征文件
│   │   └── combined_features.tsv      # 合并特征
│   └── training_data/                 # 训练数据
│       ├── metadata.tsv               # 元数据
│       ├── combined_features.tsv      # 训练特征
│       └── genomes/                   # 基因组文件
│
├── 🧪 测试和示例
│   ├── tests/                         # 测试文件
│   ├── multi_task_examples/           # 多任务示例
│   │   └── analyze_results.py         # 结果分析
│   └── results/                       # 预测结果
│
├── ⚙️ 配置和环境
│   ├── configs/                       # 配置文件目录 (建议创建)
│   │   ├── multi_task_config.json     # 多任务配置
│   │   ├── growth_rate_config.json    # 生长速率配置
│   │   └── temperature_config.json    # 温度预测配置
│   ├── requirements.txt               # Python依赖
│   ├── setup.py                       # 安装配置
│   └── venv/                          # 虚拟环境
│
└── 📚 历史和参考
    ├── legacy_code/                   # 历史代码
    ├── memory_bank/                   # 开发记录
    └── update_DeepMu/                 # 更新记录
```

## 🆕 新增训练脚本详解

### 1. 多任务训练脚本

#### `train_multi_task_model.py`
- **功能**: 同时训练生长速率和温度预测
- **架构**: 共享特征提取器 + 任务特定头
- **特点**: 
  - 任务权重平衡
  - 联合损失优化
  - 共享表示学习

#### `train_multi_task_model.sh`
- **功能**: Shell版本的多任务训练
- **特点**:
  - 完整的参数解析
  - 环境检查
  - GPU自动检测
  - 训练监控

### 2. 生长速率训练脚本

#### `train_growth_rate_model.py`
- **功能**: 专门的生长速率预测模型训练
- **特点**:
  - 交叉验证支持
  - 异常值检测
  - 鲁棒数据处理
  - 详细性能分析

#### `train_growth_rate_model.sh`
- **功能**: Shell版本的生长速率训练
- **特点**:
  - 灵活的配置选项
  - 自动报告生成
  - 错误处理

### 3. 温度预测训练脚本

#### `train_temperature_model.py`
- **功能**: 专门的温度预测模型训练
- **特点**:
  - 温度范围约束
  - MAE损失函数
  - 温度特定评估指标
  - 物理意义保持

#### `train_temperature_model.sh`
- **功能**: Shell版本的温度预测训练
- **特点**:
  - 温度范围配置
  - 损失函数选择
  - 专门的评估报告

## 📋 脚本使用指南

### Python脚本使用

```bash
# 多任务训练
python train_multi_task_model.py \
    --config configs/multi_task_config.json \
    --output-dir models/multi_task \
    --epochs 300 \
    --verbose

# 生长速率训练
python train_growth_rate_model.py \
    --config configs/growth_rate_config.json \
    --output-dir models/growth_rate \
    --k-folds 5

# 温度预测训练
python train_temperature_model.py \
    --config configs/temperature_config.json \
    --output-dir models/temperature \
    --temp-min 0.0 \
    --temp-max 100.0
```

### Shell脚本使用

```bash
# 多任务训练
./train_multi_task_model.sh \
    --epochs 500 \
    --batch-size 256 \
    --growth-weight 1.5 \
    --temp-weight 0.8

# 生长速率训练
./train_growth_rate_model.sh \
    --k-folds 10 \
    --activation relu \
    --normalize-target

# 温度预测训练
./train_temperature_model.sh \
    --temp-min 5.0 \
    --temp-max 80.0 \
    --use-mae-loss
```

## 🔧 配置文件系统

### 配置文件位置
建议在项目根目录创建 `configs/` 目录：

```bash
mkdir -p configs
```

### 配置文件类型

1. **多任务配置** (`configs/multi_task_config.json`)
   - 共享层配置
   - 任务特定层配置
   - 任务权重设置

2. **生长速率配置** (`configs/growth_rate_config.json`)
   - 单任务优化参数
   - 交叉验证设置
   - 数据处理选项

3. **温度预测配置** (`configs/temperature_config.json`)
   - 温度范围约束
   - 损失函数选择
   - 评估指标配置

## 📊 输出文件结构

训练完成后，每个输出目录包含：

```
models/output_dir/
├── config.json              # 使用的配置
├── {model_name}.pt          # 训练好的模型
├── scalers.pt              # 数据标准化器
├── training_history.json   # 训练历史
├── training.log            # 详细日志
├── training_report.md      # 训练报告
├── training_info.txt       # 训练信息摘要
└── plots/                  # 可视化图表
    ├── training_curves.png
    └── prediction_scatter.png
```

## 🎯 训练流程对比

| 特性 | 多任务训练 | 生长速率训练 | 温度预测训练 |
|------|-----------|-------------|-------------|
| **目标** | 同时预测两个任务 | 专门优化生长速率 | 专门优化温度预测 |
| **架构** | 共享+特定头 | 深度全连接 | 约束输出网络 |
| **损失函数** | 加权多任务损失 | MSE | MAE (可选) |
| **评估** | 双任务指标 | 生长速率指标 | 温度特定指标 |
| **优势** | 共享表示学习 | 单任务最优 | 物理约束 |
| **适用场景** | 平衡性能 | 最佳生长预测 | 最佳温度预测 |

## 🚀 性能优化建议

### 1. 硬件配置
- **GPU**: 推荐使用CUDA兼容GPU
- **内存**: 至少8GB RAM
- **存储**: SSD推荐，用于快速数据加载

### 2. 训练参数调优
- **批次大小**: 根据GPU内存调整 (64-256)
- **学习率**: 从0.001开始，根据收敛情况调整
- **正则化**: 根据过拟合情况调整dropout和weight_decay

### 3. 数据处理优化
- **特征选择**: 移除冗余特征
- **数据增强**: 适度使用，避免过度增强
- **异常值处理**: 平衡数据质量和数量

## 🔄 维护和更新

### 定期维护任务
1. **模型重训练**: 当有新数据时重新训练
2. **性能监控**: 定期评估模型性能
3. **配置优化**: 根据新需求调整配置
4. **代码更新**: 保持依赖包的更新

### 版本控制
- 使用语义化版本号
- 记录重要变更
- 保持向后兼容性

## 📞 技术支持

如果在使用过程中遇到问题：

1. **查看日志**: 检查 `training.log` 文件
2. **配置检查**: 验证配置文件格式
3. **环境检查**: 确认Python包版本
4. **数据验证**: 检查输入数据格式

---

**DeepMu开发团队** © 2025
